import {Redirect, Stack, usePathname} from "expo-router";
import React, {useCallback, useEffect, useRef} from "react";
import {useSession} from "@/hooks/useSession";
import {verifyEmailTitle} from "@/catalog";
import {useUser} from "@/hooks/useUser";
import {LinearGradient} from "expo-linear-gradient";
import {Platform, StatusBar, StyleSheet, View} from "react-native";
import {beautifulGradientColors, colors} from "@/styles";
import {useLingui} from "@lingui/react";
import * as Notifications from 'expo-notifications';
import {usePushNotificationHandler} from "@/hooks/usePushNotificationHandler";
import {api} from "@/api";
import {useScreenOrientation} from "@/hooks/useScreenOrientation";
import {useStoreLoginState} from "@/hooks/useStoreLoginState";
import {useSignOut} from "@/hooks/useSignOut";
import {ChatWrapper} from "@/components/ChatWrapper";
import {DateTime} from "luxon";
import {storage} from "@/hooks/usePersistentState";

export default function SignedInLayout() {
  const {isSignedIn} = useSession();
  const {isUserEmailVerified} = useUser();
  const pathname = usePathname();
  const {i18n} = useLingui();
  const {inactivityTimeoutMs} = useStoreLoginState();

  // Handle incoming notifications
  usePushNotificationHandler();

  const {isLandscape} = useScreenOrientation();


  useEffect(() => {
    if (Platform.OS !== 'android') {
      return;
    }

    StatusBar.setTranslucent(isLandscape);
    StatusBar.setHidden(isLandscape);

    return () => {
      StatusBar.setTranslucent(false);
      StatusBar.setHidden(false);
    };
  }, [Platform.OS, isLandscape]);

  const resetTimer = useCallback((inactivityTimeoutMs: number) => {
    const signOutDt = DateTime.now().plus({milliseconds: inactivityTimeoutMs});
    signOutTime.current = signOutDt;
    storage.set("Nation_signOutTime", signOutDt.toISO());
    // console.log("setting signOutTime to ", signOutTime.current.toLocaleString(DateTime.TIME_WITH_SECONDS));
  }, []);
  const clearTimer = useCallback(() => {
    signOutTime.current = null;
    storage.set("Nation_signOutTime", "");
    // console.log("clearing signOutTime");
  }, []);

  const signOut = useSignOut();
  const signOutTime = useRef<DateTime | null>(null);
  const onTouched = useCallback(() => {
    if (inactivityTimeoutMs && Number.isFinite(inactivityTimeoutMs)) {
      resetTimer(inactivityTimeoutMs);
    } else {
      clearTimer();
    }
  }, [inactivityTimeoutMs]);

  const signOutCheckInterval = useRef<NodeJS.Timeout | null>(null);

  // start inactivity timer on mount
  useEffect(() => {
    if (inactivityTimeoutMs && Number.isFinite(inactivityTimeoutMs)) {
      if (signOutCheckInterval.current) {
        clearInterval(signOutCheckInterval.current);
      }

      // if there is a persisted sign out time, use that. Otherwise, set fresh
      if (storage.getString("Nation_signOutTime")) {
        signOutTime.current = DateTime.fromISO(storage.getString("Nation_signOutTime")!);
        // console.log("using persisted signOutTime", signOutTime.current.toLocaleString(DateTime.TIME_WITH_SECONDS));
      } else {
        resetTimer(inactivityTimeoutMs);
      }

      if (signOutTime.current && signOutTime.current < DateTime.now()) {
        signOut();
      }

      signOutCheckInterval.current = setInterval(() => {
        if (signOutTime.current && signOutTime.current < DateTime.now()) {
          signOut();
        }
      }, 1000);
    } else {
      clearTimer();
    }
    return () => {
      clearTimer();
      if (signOutCheckInterval.current) {
        clearInterval(signOutCheckInterval.current);
      }
    };
  }, [inactivityTimeoutMs])


  // You can keep the splash screen open, or render a loading screen like we do here.
  // if (!isLoaded) {
  //   return <Text>Loading...</Text>;
  // }

  // Only require authentication within the (signedin) group's layout
  if (!isSignedIn) {
    return <Redirect href="/"/>;
  }

  if (!isUserEmailVerified) {
    if (pathname.search("verify-email") === -1) {
      return <Redirect href="/(signedin)/business/verify-email"/>;
    }
  }

  if (Platform.OS === 'android') {
    Notifications.setNotificationChannelAsync('default', {
      name: 'default',
      importance: Notifications.AndroidImportance.MAX,
    });
  }

  Notifications.setNotificationHandler({
    handleNotification: async () => ({
      shouldShowAlert: true,
      shouldPlaySound: true,
      shouldSetBadge: true,
    }),
  });

  return <ChatWrapper>
    <View style={{flex: 1}} onTouchStart={onTouched}
          collapsable={false}>
      <Stack>
        <Stack.Screen name="(drawer)" options={{headerShown: false}}/>
        <Stack.Screen name="schedule/(schedule-drawer)" options={{headerShown: false}}/>
        <Stack.Screen name="checklists/(checklists-drawer)" options={{headerShown: false}}/>
        <Stack.Screen name="checklists2/(checklists2-drawer)" options={{headerShown: false}}/>
        <Stack.Screen name="shift-lead/(shift-lead-drawer)" options={{headerShown: false}}/>
        <Stack.Screen name="insights/(insights-drawer)" options={{headerShown: false}}/>
        <Stack.Screen name="person/[storeId]/[personId]/approval" options={{
          headerShown: false
        }}/>
        <Stack.Screen name="store/[storeId]/devices" options={{
          headerBackTitle: "Back",
          headerBackground: () => <LinearGradient colors={beautifulGradientColors}
                                                  style={StyleSheet.absoluteFill}/>,
          headerTintColor: "#fff",
          headerTitle: "Store Devices",
        }}/>
        <Stack.Screen name="store/[storeId]/device/[clientId]" options={{
          headerBackTitle: "Back",
          headerBackground: () => <LinearGradient colors={beautifulGradientColors}
                                                  style={StyleSheet.absoluteFill}/>,
          headerTintColor: "#fff",
          headerTitle: "Store Device",
        }}/>
        <Stack.Screen name="store/[storeId]/add-device" options={{
          headerBackTitle: "Back",
          headerBackground: () => <LinearGradient colors={beautifulGradientColors}
                                                  style={StyleSheet.absoluteFill}/>,
          headerTintColor: "#fff",
          headerTitle: "Add Store Device",
        }}/>
        <Stack.Screen name="store/[storeId]/set-pin" options={{
          headerBackTitle: "Back",
          headerBackground: () => <LinearGradient colors={beautifulGradientColors}
                                                  style={StyleSheet.absoluteFill}/>,
          headerTintColor: "#fff",
          headerTitle: "Set PIN",
        }}/>
        <Stack.Screen name="store/education/education-resources" options={{
          headerBackTitle: i18n._({id: "Back", message: "Back"}),
          headerTitle: "Education",
          headerBackground: () => <LinearGradient colors={beautifulGradientColors}
                                                  style={StyleSheet.absoluteFill}/>,
          headerTintColor: "#fff",
          headerShadowVisible: true,
        }}/>
        <Stack.Screen name="store/education/[educationResourceId]/view-education-resource"
                      options={({route}) => ({
                        headerBackTitle: i18n._({id: "Back", message: "Back"}),
                        headerTitle: "",
                        headerBackground: () => <LinearGradient colors={beautifulGradientColors}
                                                                style={StyleSheet.absoluteFill}/>,
                        headerTintColor: "#fff",
                        headerShadowVisible: true,
                      })}/>
        <Stack.Screen name="person/[storeId]/[personId]/edit-details" options={{
          headerBackTitle: "Cancel",
          headerTitle: "Edit Details",
          headerTitleStyle: {
            fontSize: 20,
            fontWeight: "bold",
            color: colors.primary[700]
          },
          headerTintColor: colors.gray[500],
          headerShadowVisible: false,
        }}/>
        <Stack.Screen name="person/[storeId]/[personId]/edit-operator-details" options={{
          headerBackTitle: "Cancel",
          headerTitle: "Edit Details",
          headerTitleStyle: {
            fontSize: 20,
            fontWeight: "bold",
            color: colors.primary[700]
          },
          headerTintColor: colors.gray[500],
          headerShadowVisible: false,
        }}/>
        <Stack.Screen name="person/[storeId]/[personId]/notifications" options={{
          headerBackground: () => <LinearGradient colors={beautifulGradientColors}
                                                  style={StyleSheet.absoluteFill}/>,
          headerBackButtonDisplayMode: "minimal",
          headerTintColor: "#fff",
          headerTitle: "Notifications",
          headerTitleStyle: {
            fontSize: 20,
            fontWeight: "bold",
            color: "#fff"
          },
          headerShadowVisible: false,
        }}/>
        <Stack.Screen name="person/[storeId]/[personId]/notification-permission" options={{
          headerShown: false,
          gestureEnabled: false,
        }}/>
        <Stack.Screen name="person/[storeId]/[personId]/edit-notifications" options={{
          headerBackButtonDisplayMode: "default",
          headerBackTitle: "Back",
          headerTitle: "Notification Settings",
          headerTitleStyle: {
            fontSize: 20,
            fontWeight: "bold",
            color: colors.primary[700]
          },
          headerTintColor: colors.gray[500],
          headerShadowVisible: false,
        }}/>
        <Stack.Screen name="person/[storeId]/[personId]/edit-core-values" options={{
          headerShown: false,
          presentation: "modal",
        }}/>
        <Stack.Screen name="person/[storeId]/[personId]/edit-about" options={{
          headerShown: false,
          presentation: "modal",
        }}/>
        <Stack.Screen name="person/[storeId]/[personId]/edit-bio" options={{
          headerShown: false,
          presentation: "modal",
        }}/>
        <Stack.Screen name="person/[storeId]/[personId]/edit-operator-about" options={{
          headerShown: false,
          presentation: "modal",
        }}/>
        <Stack.Screen name="person/[storeId]/[personId]/index" options={{
          headerShown: false
        }}/>
        <Stack.Screen name="person/[storeId]/[personId]/[positionId]" options={{
          headerBackTitle: i18n._({id: "Back", message: "Back"}),
          headerTitle: "Position Details",
          headerBackground: () => <LinearGradient colors={beautifulGradientColors}
                                                  style={StyleSheet.absoluteFill}/>,
          headerTintColor: "#fff",
        }}/>
        <Stack.Screen name="person/[storeId]/[personId]/view-position-evaluation" options={{
          headerShown: false,
          presentation: "modal",
        }}/>
        <Stack.Screen name="person/[storeId]/[personId]/note/[noteId]" options={{
          headerBackTitle: i18n._({id: "Back", message: "Back"}),
          headerTitle: "General Note",
          headerTitleStyle: {
            fontSize: 20,
            fontWeight: "bold",
            color: colors.primary[700]
          },
          headerTintColor: colors.gray[500],
          headerShadowVisible: false,
        }}/>
        <Stack.Screen name="person/[storeId]/[personId]/note/edit/[noteId]" options={{
          headerBackTitle: i18n._({id: "Back", message: "Back"}),
          headerTitle: "General Note",
          headerTitleStyle: {
            fontSize: 20,
            fontWeight: "bold",
            color: colors.primary[700]
          },
          headerTintColor: colors.gray[500],
          headerShadowVisible: false,
        }}/>
        <Stack.Screen name="person/[storeId]/[personId]/note/history/[noteId]" options={{
          headerBackTitle: i18n._({id: "Back", message: "Back"}),
          headerTitle: "Version History",
          headerTitleStyle: {
            fontSize: 20,
            fontWeight: "bold",
            color: colors.primary[700]
          },
          headerTintColor: colors.gray[500],
          headerShadowVisible: false,
        }}/>
        <Stack.Screen name="person/[storeId]/[personId]/note/from-corrective-action/[correctiveActionId]"
                      options={{
                        headerBackTitle: i18n._({id: "Back", message: "Back"}),
                        headerTitle: "General Note",
                        headerTitleStyle: {
                          fontSize: 20,
                          fontWeight: "bold",
                          color: colors.primary[700]
                        },
                        headerTintColor: colors.gray[500],
                        headerShadowVisible: false,
                      }}/>
        <Stack.Screen name="store/select-job-title" options={{
          headerBackVisible: true,
          headerTitle: "Select Job",
          headerTitleStyle: {
            color: colors.gioaBlue
          },
          headerTintColor: colors.gray[500],
          headerShadowVisible: false,
        }}/>
        <Stack.Screen name={"store/edit-work-areas"} options={{
          headerBackVisible: true,
          headerBackTitle: i18n._({id: "Back", message: "Back"}),
          headerTitle: "Work Areas & Positions",
          headerTintColor: colors.gray[500],
          headerShadowVisible: false,
        }}/>
        <Stack.Screen name={"store/operator-add-store-delegate"} options={{
          headerBackVisible: true,
          headerBackTitle: i18n._({id: "Back", message: "Back"}),
          headerTitle: "Add Delegate",
          headerTitleStyle: {
            fontSize: 20,
            fontWeight: "bold",
            color: colors.primary[700]
          },
          headerTintColor: colors.gray[500],
          headerShadowVisible: false,
        }}/>
        <Stack.Screen name={"store/edit-store-info"} options={{
          headerBackVisible: true,
          headerBackTitle: i18n._({id: "Back", message: "Back"}),
          headerTitle: "Store Info",
          headerTintColor: colors.gray[500],
          headerShadowVisible: false,
        }}/>
        <Stack.Screen name={"store/edit-work-area-positions"} options={{
          headerBackVisible: true,
          headerBackTitle: i18n._({id: "Back", message: "Back"}),
          headerTitle: "Work Areas & Positions",
          headerTintColor: colors.gray[500],
          headerShadowVisible: false,
        }}/>
        <Stack.Screen name={"store/[storeId]/manage-corrective-action-custom-types"} options={{
          headerBackTitle: i18n._({id: "Back", message: "Back"}),
          headerTitle: "Custom Action Types",
          headerTitleStyle: {
            fontSize: 20,
            fontWeight: "bold",
            color: colors.primary[700]
          },
          headerTintColor: colors.gray[500],
          headerShadowVisible: false,
        }}/>
        <Stack.Screen name={"store/resources/[storeId]/store-links"} options={{
          headerBackTitle: i18n._({id: "Back", message: "Back"}),
          headerTitle: "Links",
          headerBackground: () => <LinearGradient colors={beautifulGradientColors}
                                                  style={StyleSheet.absoluteFill}/>,
          headerTintColor: "#fff",
          headerShadowVisible: true,
        }}/>
        <Stack.Screen name={"store/resources/[storeId]/[storeResourceId]/edit-store-links"} options={{
          headerBackTitle: i18n._({id: "Back", message: "Back"}),
          headerTitle: "",
          headerBackground: () => <LinearGradient colors={beautifulGradientColors}
                                                  style={StyleSheet.absoluteFill}/>,
          headerTintColor: "#fff",
          headerShadowVisible: true,
        }}/>
        <Stack.Screen name={"store/resources/[storeId]/store-files"} options={{
          headerBackTitle: i18n._({id: "Back", message: "Back"}),
          headerTitle: "Files",
          headerBackground: () => <LinearGradient colors={beautifulGradientColors}
                                                  style={StyleSheet.absoluteFill}/>,
          headerTintColor: "#fff",
          headerShadowVisible: true,
        }}/>
        <Stack.Screen name={"store/resources/[storeId]/[storeResourceId]/edit-store-files"} options={{
          headerBackTitle: i18n._({id: "Back", message: "Back"}),
          headerTitle: "",
          headerBackground: () => <LinearGradient colors={beautifulGradientColors}
                                                  style={StyleSheet.absoluteFill}/>,
          headerTintColor: "#fff",
          headerShadowVisible: true,
        }}/>
        <Stack.Screen name={"store/resources/[storeId]/store-vendors"} options={{
          headerBackTitle: i18n._({id: "Back", message: "Back"}),
          headerTitle: "Vendors",
          headerBackground: () => <LinearGradient colors={beautifulGradientColors}
                                                  style={StyleSheet.absoluteFill}/>,
          headerTintColor: "#fff",
          headerShadowVisible: true,
        }}/>
        <Stack.Screen name={"store/resources/[storeId]/[storeResourceId]/edit-store-vendors"} options={{
          headerBackTitle: i18n._({id: "Back", message: "Back"}),
          headerTitle: "",
          headerBackground: () => <LinearGradient colors={beautifulGradientColors}
                                                  style={StyleSheet.absoluteFill}/>,
          headerTintColor: "#fff",
          headerShadowVisible: true,
        }}/>
        <Stack.Screen name={"store/resources/[storeId]/[storeResourceId]/vendor-details"} options={{
          headerBackTitle: i18n._({id: "Back", message: "Back"}),
          headerTitle: "Vendor Details",
          headerBackground: () => <LinearGradient colors={beautifulGradientColors}
                                                  style={StyleSheet.absoluteFill}/>,
          headerTintColor: "#fff",
          headerShadowVisible: true,
        }}/>
        <Stack.Screen name={"job/[jobId]/index"} options={{
          headerBackVisible: true,
          headerTitle: "Edit Job",
          headerTitleStyle: {
            fontSize: 20,
            fontWeight: "bold",
            color: colors.primary[700]
          },
          headerTintColor: colors.gray[500],
          headerShadowVisible: false,
        }}/>
        <Stack.Screen name={"job/[jobId]/review"} options={{
          headerBackVisible: true,
          headerBackTitle: i18n._({id: "Back", message: "Back"}),
          headerTitle: "Review",
          headerTitleStyle: {
            fontSize: 20,
            fontWeight: "bold",
            color: colors.primary[700]
          },
          headerTintColor: colors.gray[500],
          headerShadowVisible: false,
        }}/>
        <Stack.Screen name={"business/edit-core-values"} options={{
          headerBackVisible: true,
          headerBackTitle: i18n._({id: "Back", message: "Back"}),
          headerTitle: "",
          headerTintColor: colors.gray[500],
          headerShadowVisible: false,
        }}/>
        <Stack.Screen name={"business/edit-job-titles"} options={{
          headerBackVisible: true,
          headerBackTitle: i18n._({id: "Back", message: "Back"}),
          headerTitle: "Job Titles",
          headerTitleStyle: {
            fontSize: 20,
            fontWeight: "bold",
            color: colors.primary[700]
          },
          headerTintColor: colors.gray[500],
          headerShadowVisible: false,
        }}/>

        <Stack.Screen name={"business/verify-email"} options={{
          title: verifyEmailTitle,
        }}/>
        <Stack.Screen name="business/send-bulk-import-email" options={{
          headerShown: false,
          presentation: "modal",
        }}/>
        <Stack.Screen name={"onboarding/intro"} options={{
          headerBackVisible: false,
          headerBackTitle: i18n._({id: "Back", message: "Back"}),
          headerTitle: "",
          headerTintColor: colors.gray[500],
          headerShadowVisible: false,
        }}/>
        <Stack.Screen name={"onboarding/headshot"} options={{
          headerBackTitle: i18n._({id: "Back", message: "Back"}),
          headerTitle: "",
          headerTintColor: colors.gray[500],
          headerShadowVisible: false,
        }}/>
        <Stack.Screen name={"onboarding/continue-or-messaging"} options={{
          headerBackTitle: i18n._({id: "Back", message: "Back"}),
          headerTitle: "",
          headerTintColor: colors.gray[500],
          headerShadowVisible: false,
        }}/>
        <Stack.Screen name={"onboarding/about"} options={{
          headerBackTitle: i18n._({id: "Back", message: "Back"}),
          headerTitle: "",
          headerTintColor: colors.gray[500],
          headerShadowVisible: false,
        }}/>
        <Stack.Screen name={"onboarding/store-setup-explainer"} options={{
          headerBackTitle: i18n._({id: "Back", message: "Back"}),
          headerTitle: "",
          headerTintColor: colors.gray[500],
          headerShadowVisible: false,
        }}/>
        <Stack.Screen name={"onboarding/[storeId]/store-areas-explainer"} options={{
          headerBackTitle: i18n._({id: "Back", message: "Back"}),
          headerTitle: "",
          headerTintColor: colors.gray[500],
          headerShadowVisible: false,
        }}/>
        <Stack.Screen name={"onboarding/[storeId]/area/[areaId]/positions"} options={{
          headerBackTitle: i18n._({id: "Back", message: "Back"}),
          headerTitle: "",
          headerTintColor: colors.gray[500],
          headerShadowVisible: false,
        }}/>
        <Stack.Screen name={"onboarding/[storeId]/store-areas"} options={{
          headerBackTitle: i18n._({id: "Back", message: "Back"}),
          headerTitle: "",
          headerTintColor: colors.gray[500],
          headerShadowVisible: false,
        }}/>
        <Stack.Screen name={"onboarding/[storeId]/store-delegate-choice"} options={{
          headerBackTitle: i18n._({id: "Back", message: "Back"}),
          headerTitle: "",
          headerTintColor: colors.gray[500],
          headerShadowVisible: false,
        }}/>
        <Stack.Screen name={"onboarding/core-values"} options={{
          headerBackTitle: i18n._({id: "Back", message: "Back"}),
          headerTitle: "",
          headerTintColor: colors.gray[500],
          headerTitleStyle: {
            fontSize: 20,
            fontWeight: "bold",
            color: colors.primary[700]
          },
          headerShadowVisible: false,
        }}/>
        <Stack.Screen name={"onboarding/core-value/[coreValueId]"} options={{
          headerShown: false,
          headerShadowVisible: false,
          presentation: "modal",
        }}/>
        <Stack.Screen name={"onboarding/[storeId]/store-info"} options={{
          headerBackTitle: i18n._({id: "Back", message: "Back"}),
          headerTitle: "",
          headerTintColor: colors.gray[500],
          headerTitleStyle: {
            fontSize: 20,
            fontWeight: "bold",
            color: colors.primary[700]
          },
          headerShadowVisible: false,
        }}/>
        <Stack.Screen name={"onboarding/[storeId]/store-photo"} options={{
          headerBackTitle: i18n._({id: "Back", message: "Back"}),
          headerTitle: "",
          headerTintColor: colors.gray[500],
          headerTitleStyle: {
            fontSize: 20,
            fontWeight: "bold",
            color: colors.primary[700]
          },
          headerShadowVisible: false,
        }}/>
        <Stack.Screen name={"onboarding/[storeId]/store-add-employee"} options={{
          headerShown: false,
          headerShadowVisible: false,
          presentation: "modal",
        }}/>
        <Stack.Screen name={"onboarding/[storeId]/store-positions"} options={{
          headerBackTitle: i18n._({id: "Back", message: "Back"}),
          headerTitle: "Store Positions",
          headerTintColor: colors.gray[500],
          headerTitleStyle: {
            fontSize: 20,
            fontWeight: "bold",
            color: colors.primary[700]
          },
          headerShadowVisible: false,
        }}/>
        <Stack.Screen name={"onboarding/[storeId]/area/[areaId]/edit-positions"} options={{
          headerShown: false,
          headerShadowVisible: false,
          presentation: "modal",
        }}/>
        <Stack.Screen name={"onboarding/[storeId]/area/[areaId]/position/[positionId]"} options={{
          headerShown: false,
          headerShadowVisible: false,
          presentation: "modal",
        }}/>
        <Stack.Screen name={"onboarding/[storeId]/area/[areaId]/index"} options={{
          headerShown: false,
          headerShadowVisible: false,
          presentation: "modal",
        }}/>
        <Stack.Screen name={"onboarding/[storeId]/store-employees-explainer"} options={{
          headerBackTitle: i18n._({id: "Back", message: "Back"}),
          headerTitle: "",
          headerTintColor: colors.gray[500],
          headerTitleStyle: {
            fontSize: 20,
            fontWeight: "bold",
            color: colors.primary[700]
          },
          headerShadowVisible: false,
        }}/>
        <Stack.Screen name={"onboarding/player/intro"} options={{
          headerBackVisible: false,
          headerBackTitle: i18n._({id: "Back", message: "Back"}),
          headerTitle: "",
          headerTintColor: colors.gray[500],
          headerShadowVisible: false,
        }}/>
        <Stack.Screen name={"onboarding/player/language"} options={{
          headerBackVisible: true,
          headerBackTitle: i18n._({id: "Back", message: "Back"}),
          headerTitle: "",
          headerTintColor: colors.gray[500],
          headerShadowVisible: false,
        }}/>
        <Stack.Screen name={"onboarding/player/job-info"} options={{
          headerBackVisible: true,
          headerBackTitle: i18n._({id: "Back", message: "Back"}),
          headerTitle: "",
          headerTintColor: colors.gray[500],
          headerShadowVisible: false,
        }}/>
        <Stack.Screen name={"onboarding/player/about-you"} options={{
          headerBackVisible: true,
          headerBackTitle: i18n._({id: "Back", message: "Back"}),
          headerTitle: "",
          headerTintColor: colors.gray[500],
          headerShadowVisible: false,
        }}/>
        <Stack.Screen name={"onboarding/player/[storeId]/skills"} options={{
          headerBackVisible: true,
          headerBackTitle: i18n._({id: "Back", message: "Back"}),
          headerTitle: "",
          headerTintColor: colors.gray[500],
          headerShadowVisible: false,
        }}/>
        <Stack.Screen name={"onboarding/player/[storeId]/availability"} options={{
          headerBackVisible: true,
          headerBackTitle: i18n._({id: "Back", message: "Back"}),
          headerTitle: "",
          headerTintColor: colors.gray[500],
          headerShadowVisible: false,
        }}/>
        <Stack.Screen name={"person/[storeId]/[personId]/onboarding-daily-availability"} options={{
          headerShown: false,
        }}/>
        <Stack.Screen name={"onboarding/player/[storeId]/availability-overview"} options={{
          headerBackVisible: true,
          headerBackTitle: i18n._({id: "Back", message: "Back"}),
          headerTitle: "",
          headerTintColor: colors.gray[500],
          headerShadowVisible: false,
        }}/>
        <Stack.Screen name={"onboarding/[storeId]/store-handoff"} options={{
          headerBackVisible: true,
          headerBackTitle: i18n._({id: "Back", message: "Back"}),
          headerTitle: "",
          headerTintColor: colors.gray[500],
          headerShadowVisible: false,
        }}/>
        <Stack.Screen name={"onboarding/[storeId]/store-handoff-complete"} options={{
          headerBackVisible: false,
          headerBackTitle: i18n._({id: "Back", message: "Back"}),
          headerTitle: "",
          headerTintColor: colors.gray[500],
          headerShadowVisible: false,
        }}/>
        <Stack.Screen name={"onboarding/onboarding-complete"} options={{
          headerBackVisible: false,
          headerBackTitle: i18n._({id: "Back", message: "Back"}),
          headerTitle: "",
          headerTintColor: colors.gray[500],
          headerShadowVisible: false,
        }}/>
        <Stack.Screen name={"onboarding/[storeId]/store-handoff-add-admin"} options={{
          headerBackVisible: true,
          headerBackTitle: i18n._({id: "Back", message: "Back"}),
          headerTitle: "Handoff",
          headerTitleStyle: {
            fontSize: 20,
            fontWeight: "bold",
            color: colors.primary[700]
          },
          headerTintColor: colors.gray[500],
          headerShadowVisible: false,
        }}/>
        <Stack.Screen name={"person/[storeId]/[personId]/create-actionable-item"} options={{
          headerBackVisible: true,
          headerBackTitle: i18n._({id: "Back", message: "Back"}),
          headerTitle: "Actionable Item",
          headerTitleStyle: {
            fontSize: 20,
            fontWeight: "bold",
            color: colors.primary[700]
          },
          headerTintColor: colors.gray[500],
          headerShadowVisible: false,
        }}/>
        <Stack.Screen name={"person/[storeId]/[personId]/approval-job-title"} options={{
          headerBackVisible: true,
          headerBackTitle: i18n._({id: "Back", message: "Back"}),
          headerTitle: "Job Title",
          headerTitleStyle: {
            fontSize: 20,
            fontWeight: "bold",
            color: colors.primary[700]
          },
          headerTintColor: colors.gray[500],
          headerShadowVisible: false,
        }}/>
        <Stack.Screen name={"person/[storeId]/[personId]/approval-permissions"} options={{
          headerBackVisible: true,
          headerBackTitle: i18n._({id: "Back", message: "Back"}),
          headerTitle: "Permissions",
          headerTitleStyle: {
            fontSize: 20,
            fontWeight: "bold",
            color: colors.primary[700]
          },
          headerTintColor: colors.gray[500],
          headerShadowVisible: false,
        }}/>
        <Stack.Screen name={"person/[storeId]/[personId]/approval-training"} options={{
          headerBackVisible: true,
          headerBackTitle: i18n._({id: "Back", message: "Back"}),
          headerTitle: "Training",
          headerTitleStyle: {
            fontSize: 20,
            fontWeight: "bold",
            color: colors.primary[700]
          },
          headerTintColor: colors.gray[500],
          headerShadowVisible: false,
        }}/>
        <Stack.Screen name={"person/[storeId]/[personId]/approval-availability"} options={{
          headerBackVisible: true,
          headerBackTitle: i18n._({id: "Back", message: "Back"}),
          headerTitle: "Availability",
          headerTitleStyle: {
            fontSize: 20,
            fontWeight: "bold",
            color: colors.primary[700]
          },
          headerTintColor: colors.gray[500],
          headerShadowVisible: false,
        }}/>
        <Stack.Screen name={"person/[storeId]/[personId]/approval-proficiency"} options={{
          headerBackVisible: true,
          headerBackTitle: i18n._({id: "Back", message: "Back"}),
          headerTitle: "Set Proficiency",
          headerTitleStyle: {
            fontSize: 20,
            fontWeight: "bold",
            color: colors.primary[700]
          },
          headerTintColor: colors.gray[500],
          headerShadowVisible: false,
        }}/>
        <Stack.Screen name={"person/[storeId]/[personId]/select-or-create"} options={{
          headerBackTitle: i18n._({id: "Back", message: "Back"}),
          headerTitle: "Select Policies in Action",
          headerTitleStyle: {
            fontSize: 20,
            fontWeight: "bold",
            color: colors.primary[700]
          },
          headerTintColor: colors.gray[500],
          headerShadowVisible: false,
        }}/>
        <Stack.Screen name={"person/[storeId]/[personId]/corrective-action/[correctiveActionId]/index"} options={{
          headerBackTitle: i18n._({id: "Back", message: "Back"}),
          headerTitle: "Action",
          headerTitleStyle: {
            fontSize: 20,
            fontWeight: "bold",
            color: colors.primary[700]
          },
          headerTintColor: colors.gray[500],
          headerShadowVisible: false,
        }}/>
        <Stack.Screen name={"person/[storeId]/[personId]/corrective-action/[correctiveActionId]/formalize"}
                      options={{
                        headerBackTitle: i18n._({id: "Back", message: "Back"}),
                        headerTitle: "Create Corrective Action",
                        headerTitleStyle: {
                          fontSize: 20,
                          fontWeight: "bold",
                          color: colors.primary[700]
                        },
                        headerTintColor: colors.gray[500],
                        headerShadowVisible: false,
                      }}/>
        <Stack.Screen name="person/[storeId]/[personId]/corrective-action/[correctiveActionId]/history" options={{
          headerBackTitle: i18n._({id: "Back", message: "Back"}),
          headerTitle: "Version History",
          headerTitleStyle: {
            fontSize: 20,
            fontWeight: "bold",
            color: colors.primary[700]
          },
          headerTintColor: colors.gray[500],
          headerShadowVisible: false,
        }}/>
        <Stack.Screen name={"person/[storeId]/[personId]/add-feedback"} options={{
          headerBackTitle: i18n._({id: "Back", message: "Back"}),
          headerTitle: "Add Feedback",
          headerTitleStyle: {
            fontSize: 20,
            fontWeight: "bold",
            color: colors.primary[700]
          },
          headerTintColor: colors.gray[500],
          headerShadowVisible: false,
        }}/>
        <Stack.Screen name={"person/[storeId]/[personId]/archive-person"} options={{
          headerBackTitle: i18n._({id: "Back", message: "Back"}),
          headerTitle: "Archive Team Member",
          headerBackground: () => <LinearGradient colors={beautifulGradientColors}
                                                  style={StyleSheet.absoluteFill}/>,
          headerTintColor: "#fff",
          headerShadowVisible: true,
        }}/>
        <Stack.Screen name={"person/edit-employment-permissions"} options={{
          headerBackTitle: i18n._({id: "Back", message: "Back"}),
          headerTitle: "Edit Permissions",
          headerTitleStyle: {
            fontSize: 20,
            fontWeight: "bold",
            color: colors.primary[700]
          },
          headerTintColor: colors.gray[500],
          headerShadowVisible: false,
        }}/>
        <Stack.Screen name="person/app-feedback" options={{
          headerBackVisible: true,
          headerBackTitle: i18n._({id: "Back", message: "Back"}),
          headerTitle: "",
          headerTintColor: colors.gray[500],
          headerShadowVisible: false,
        }}/>
        <Stack.Screen name={"person/[storeId]/[personId]/weekly-availability"} options={{
          headerBackVisible: true,
          headerBackTitle: i18n._({id: "Back", message: "Back"}),
          headerTitle: "My Availability",
          headerBackground: () => <LinearGradient colors={beautifulGradientColors}
                                                  style={StyleSheet.absoluteFill}/>,
          headerTintColor: "#fff",
          headerShadowVisible: true,
        }}/>
        <Stack.Screen name={"person/[storeId]/[personId]/daily-availability"} options={{
          headerShown: false,
        }}/>
        <Stack.Screen name={"person/[storeId]/[personId]/manage-shift-offers"} options={{
          headerShown: false,
        }}/>
        <Stack.Screen name={"person/[storeId]/[personId]/view-volunteered-shift"} options={{
          headerShown: false,
        }}/>
        <Stack.Screen name={"person/[storeId]/[personId]/view-shift-offer"} options={{
          headerBackVisible: true,
          headerBackTitle: i18n._({id: "Back", message: "Back"}),
          headerTitle: "Your Shift Offer",
          headerBackground: () => <LinearGradient colors={beautifulGradientColors}
                                                  style={StyleSheet.absoluteFill}/>,
          headerTintColor: "#fff",
          headerShadowVisible: true,
        }}/>
        <Stack.Screen name={"person/[storeId]/[personId]/manage-shift-swaps"} options={{
          headerBackVisible: true,
          headerBackTitle: i18n._({id: "Back", message: "Back"}),
          headerTitle: "Shift Swaps",
          headerBackground: () => <LinearGradient colors={beautifulGradientColors}
                                                  style={StyleSheet.absoluteFill}/>,
          headerTintColor: "#fff",
          headerShadowVisible: true,
        }}/>
        <Stack.Screen name={"person/[storeId]/[personId]/shift-swap/[shiftSwapId]/index"} options={{
          headerBackVisible: true,
          headerBackTitle: i18n._({id: "Back", message: "Back"}),
          headerTitle: "Swap Offer",
          headerBackground: () => <LinearGradient colors={beautifulGradientColors}
                                                  style={StyleSheet.absoluteFill}/>,
          headerTintColor: "#fff",
          headerShadowVisible: true,
        }}/>
        <Stack.Screen name={"person/[storeId]/[personId]/shift-swap/[shiftSwapId]/view-offer"} options={{
          headerBackVisible: true,
          headerBackTitle: i18n._({id: "Back", message: "Back"}),
          headerTitle: "Swap Details",
          headerBackground: () => <LinearGradient colors={beautifulGradientColors}
                                                  style={StyleSheet.absoluteFill}/>,
          headerTintColor: "#fff",
          headerShadowVisible: true,
        }}/>
        <Stack.Screen name={"person/[storeId]/[personId]/new-shift-swap"} options={{
          headerBackVisible: true,
          headerBackTitle: i18n._({id: "Back", message: "Back"}),
          headerTitle: "New Swap",
          headerBackground: () => <LinearGradient colors={beautifulGradientColors}
                                                  style={StyleSheet.absoluteFill}/>,
          headerTintColor: "#fff",
          headerShadowVisible: true,
        }}/>
        <Stack.Screen name={"person/[storeId]/[personId]/select-shift-to-offer"} options={{
          headerBackVisible: true,
          headerBackTitle: i18n._({id: "Back", message: "Back"}),
          headerTitle: "Shift Offers",
          headerBackground: () => <LinearGradient colors={beautifulGradientColors}
                                                  style={StyleSheet.absoluteFill}/>,
          headerTintColor: "#fff",
          headerShadowVisible: true,
        }}/>
        <Stack.Screen name={"person/[storeId]/[personId]/time-off-request"} options={{
          headerBackVisible: true,
          headerBackTitle: i18n._({id: "Back", message: "Back"}),
          headerTitle: i18n._({id: "create.time.off.title", message: "Create Time Off Request"}),
          headerBackground: () => <LinearGradient colors={beautifulGradientColors}
                                                  style={StyleSheet.absoluteFill}/>,
          headerTintColor: "#fff",
          headerShadowVisible: true,
        }}/>
        <Stack.Screen name={"person/[storeId]/[personId]/view-time-off-requests"} options={{
          headerBackVisible: true,
          headerBackTitle: i18n._({id: "Back", message: "Back"}),
          headerTitle: "Time Off",
          headerBackground: () => <LinearGradient colors={beautifulGradientColors}
                                                  style={StyleSheet.absoluteFill}/>,
          headerTintColor: "#fff",
          headerShadowVisible: true,
        }}/>
        <Stack.Screen name={"person/[storeId]/[personId]/time-off-details"} options={{
          headerBackVisible: true,
          headerBackTitle: i18n._({id: "Back", message: "Back"}),
          headerTitle: i18n._({id: "review.time.off.title", message: "Review Time Off Request"}),
          headerBackground: () => <LinearGradient colors={beautifulGradientColors}
                                                  style={StyleSheet.absoluteFill}/>,
          headerTintColor: "#fff",
          headerShadowVisible: true,
        }}/>
        <Stack.Screen name={"person/[storeId]/[personId]/availability-request-details"} options={{
          headerBackVisible: true,
          headerBackTitle: i18n._({id: "Back", message: "Back"}),
          headerTitle: i18n._({id: "review.availability.title", message: "Review Availability Request"}),
          headerBackground: () => <LinearGradient colors={beautifulGradientColors}
                                                  style={StyleSheet.absoluteFill}/>,
          headerTintColor: "#fff",
          headerShadowVisible: true,
        }}/>
        <Stack.Screen name={"person/[storeId]/[personId]/schedule-events"} options={{
          headerBackVisible: true,
          headerBackTitle: i18n._({id: "Back", message: "Back"}),
          headerTitle: "Your Events",
          headerBackground: () => <LinearGradient colors={beautifulGradientColors}
                                                  style={StyleSheet.absoluteFill}/>,
          headerTintColor: "#fff",
          headerShadowVisible: true,
        }}/>
        <Stack.Screen name={"person/[storeId]/[personId]/schedule-announcements"} options={{
          headerBackVisible: true,
          headerBackTitle: i18n._({id: "Back", message: "Back"}),
          headerTitle: "Your Announcements",
          headerBackground: () => <LinearGradient colors={beautifulGradientColors}
                                                  style={StyleSheet.absoluteFill}/>,
          headerTintColor: "#fff",
          headerShadowVisible: true,
        }}/>
        <Stack.Screen name={"person/[storeId]/[personId]/offer-shift"} options={{
          headerBackVisible: true,
          headerBackTitle: i18n._({id: "Back", message: "Back"}),
          headerTitle: "New Shift Offer",
          headerBackground: () => <LinearGradient colors={beautifulGradientColors}
                                                  style={StyleSheet.absoluteFill}/>,
          headerTintColor: "#fff",
          headerShadowVisible: true,
        }}/>
        <Stack.Screen name={"person/[storeId]/[personId]/offer-partial-shift"} options={{
          headerBackVisible: true,
          headerBackTitle: i18n._({id: "Back", message: "Back"}),
          headerTitle: "New Shift Offer",
          headerBackground: () => <LinearGradient colors={beautifulGradientColors}
                                                  style={StyleSheet.absoluteFill}/>,
          headerTintColor: "#fff",
          headerShadowVisible: true,
        }}/>
        <Stack.Screen name={"person/[storeId]/[personId]/offer-partial-shift-review"} options={{

          headerShown: false,
          presentation: "modal",
        }}/>
        <Stack.Screen name={"person/[storeId]/[personId]/offer-shift-type"} options={{
          headerBackVisible: true,
          headerBackTitle: i18n._({id: "Back", message: "Back"}),
          headerTitle: "New Shift Offer",
          headerBackground: () => <LinearGradient colors={beautifulGradientColors}
                                                  style={StyleSheet.absoluteFill}/>,
          headerTintColor: "#fff",
          headerShadowVisible: true,
        }}/>
        <Stack.Screen name={"person/[storeId]/[personId]/edit-availability"} options={{
          headerBackVisible: false,
          headerTitle: "Edit Availability",
          headerBackground: () => <LinearGradient colors={beautifulGradientColors}
                                                  style={StyleSheet.absoluteFill}/>,
          headerTintColor: "#fff",
          headerShadowVisible: true,
        }}/>
        <Stack.Screen name={"person/[storeId]/[personId]/availability-review"} options={{
          headerBackVisible: true,
          headerBackTitle: i18n._({id: "Back", message: "Back"}),
          headerTitle: "Availability Review",
          headerBackground: () => <LinearGradient colors={beautifulGradientColors}
                                                  style={StyleSheet.absoluteFill}/>,
          headerTintColor: "#fff",
          headerShadowVisible: true,
        }}/>
        <Stack.Screen name={"person/[storeId]/[personId]/availability-summary"} options={{
          headerShown: false,
        }}/>
        <Stack.Screen name={"person/[storeId]/[personId]/shift-details"} options={{
          headerBackVisible: true,
          headerBackTitle: i18n._({id: "Back", message: "Back"}),
          headerTitle: "Your Shift Details",
          headerBackground: () => <LinearGradient colors={beautifulGradientColors}
                                                  style={StyleSheet.absoluteFill}/>,
          headerTintColor: "#fff",
          headerShadowVisible: true,
        }}/>
        <Stack.Screen name={"business/review-ca"} options={{
          headerBackVisible: false,
          headerBackTitle: i18n._({id: "Back", message: "Back"}),
          headerTitle: "",
          headerTintColor: colors.gray[500],
          headerShadowVisible: false,
          gestureEnabled: false,
        }}/>
        <Stack.Screen name={"business/review-note"} options={{
          headerBackVisible: false,
          headerBackTitle: i18n._({id: "Back", message: "Back"}),
          headerTitle: "",
          headerTintColor: colors.gray[500],
          headerShadowVisible: false,
          gestureEnabled: false,
        }}/>
        <Stack.Screen name={"business/[storeId]/acknowledge-announcement"} options={{
          headerBackVisible: false,
          headerBackTitle: i18n._({id: "Back", message: "Back"}),
          headerTitle: "Announcement",
          headerBackground: () => <LinearGradient colors={beautifulGradientColors}
                                                  style={StyleSheet.absoluteFill}/>,
          headerTintColor: "#fff",
          headerShadowVisible: true,
          gestureEnabled: false,
        }}/>
        <Stack.Screen name={"business/[storeId]/acknowledge-reminder"} options={{
          headerBackVisible: false,
          headerBackTitle: i18n._({id: "Back", message: "Back"}),
          headerTitle: "Reminder",
          headerBackground: () => <LinearGradient colors={beautifulGradientColors}
                                                  style={StyleSheet.absoluteFill}/>,
          headerTintColor: "#fff",
          headerShadowVisible: true,
          gestureEnabled: false,
        }}/>
        <Stack.Screen name={"business/[storeId]/sign-document"} options={{
          headerBackVisible: false,
          headerBackTitle: i18n._({id: "Back", message: "Back"}),
          headerTitle: "Signature Required",
          headerBackground: () => <LinearGradient colors={beautifulGradientColors}
                                                  style={StyleSheet.absoluteFill}/>,
          headerTintColor: "#fff",
          headerShadowVisible: true,
          gestureEnabled: false,
        }}/>
        <Stack.Screen name={"schedule/[storeId]/schedule-day"} options={{
          headerBackVisible: true,
          headerBackTitle: i18n._({id: "Back", message: "Back"}),
          headerTitle: "Schedule",
          headerBackground: () => <LinearGradient colors={beautifulGradientColors}
                                                  style={StyleSheet.absoluteFill}/>,
          headerTintColor: "#fff",
          headerShadowVisible: true,
        }}/>
        <Stack.Screen name={"schedule/[storeId]/shift/[shiftId]/index"} options={{
          headerBackVisible: true,
          headerBackTitle: i18n._({id: "Back", message: "Back"}),
          headerTitle: "Shift Details",
          headerBackground: () => <LinearGradient colors={beautifulGradientColors}
                                                  style={StyleSheet.absoluteFill}/>,
          headerTintColor: "#fff",
          headerShadowVisible: true,
        }}/>
        <Stack.Screen name={"schedule/[storeId]/event/[eventId]/index"} options={{
          headerBackVisible: true,
          headerBackTitle: i18n._({id: "Back", message: "Back"}),
          headerTitle: "Event Details",
          headerBackground: () => <LinearGradient colors={beautifulGradientColors}
                                                  style={StyleSheet.absoluteFill}/>,
          headerTintColor: "#fff",
          headerShadowVisible: true,
        }}/>
        <Stack.Screen name={"schedule/[storeId]/event/[eventId]/edit"} options={{
          headerBackVisible: true,
          headerBackTitle: i18n._({id: "Back", message: "Back"}),
          headerTitle: "Edit Event",
          headerBackground: () => <LinearGradient colors={beautifulGradientColors}
                                                  style={StyleSheet.absoluteFill}/>,
          headerTintColor: "#fff",
          headerShadowVisible: true,
        }}/>
        <Stack.Screen name={"schedule/[storeId]/event/create"} options={{
          headerBackVisible: true,
          headerBackTitle: i18n._({id: "Back", message: "Back"}),
          headerTitle: "Create Event",
          headerBackground: () => <LinearGradient colors={beautifulGradientColors}
                                                  style={StyleSheet.absoluteFill}/>,
          headerTintColor: "#fff",
          headerShadowVisible: true,
        }}/>
        <Stack.Screen name={"schedule/[storeId]/announcement/[eventId]/index"} options={{
          headerBackVisible: true,
          headerBackTitle: i18n._({id: "Back", message: "Back"}),
          headerTitle: "Announcement Details",
          headerBackground: () => <LinearGradient colors={beautifulGradientColors}
                                                  style={StyleSheet.absoluteFill}/>,
          headerTintColor: "#fff",
          headerShadowVisible: true,
        }}/>
        <Stack.Screen name={"schedule/[storeId]/announcement/[eventId]/edit"} options={{
          headerBackVisible: true,
          headerBackTitle: i18n._({id: "Back", message: "Back"}),
          headerTitle: "Edit Announcement",
          headerBackground: () => <LinearGradient colors={beautifulGradientColors}
                                                  style={StyleSheet.absoluteFill}/>,
          headerTintColor: "#fff",
          headerShadowVisible: true,
        }}/>
        <Stack.Screen name={"schedule/[storeId]/announcement/create"} options={{
          headerBackVisible: true,
          headerBackTitle: i18n._({id: "Back", message: "Back"}),
          headerTitle: "New Announcement",
          headerBackground: () => <LinearGradient colors={beautifulGradientColors}
                                                  style={StyleSheet.absoluteFill}/>,
          headerTintColor: "#fff",
          headerShadowVisible: true,
        }}/>
        <Stack.Screen name={"schedule/[storeId]/requests/index"} options={{
          headerBackVisible: true,
          headerBackTitle: i18n._({id: "Back", message: "Back"}),
          headerTitle: "",
          headerBackground: () => <LinearGradient colors={beautifulGradientColors}
                                                  style={StyleSheet.absoluteFill}/>,
          headerTintColor: "#fff",
          headerShadowVisible: true,
        }}/>
        <Stack.Screen name={"schedule/[storeId]/requests/time-off"} options={{
          headerBackVisible: true,
          headerBackButtonDisplayMode: "minimal",
          headerTitle: "Time Off",
          headerBackground: () => <LinearGradient colors={beautifulGradientColors}
                                                  style={StyleSheet.absoluteFill}/>,
          headerTintColor: "#fff",
          headerShadowVisible: true,
        }}/>
        <Stack.Screen name={"schedule/[storeId]/requests/create-time-off"} options={{
          headerBackVisible: true,
          headerTitle: "Create Request",
          headerBackground: () => <LinearGradient colors={beautifulGradientColors}
                                                  style={StyleSheet.absoluteFill}/>,
          headerTintColor: "#fff",
          headerShadowVisible: true,
        }}/>
        <Stack.Screen name={"schedule/[storeId]/requests/time-off-details"} options={{
          headerShown: false,
          presentation: "modal",
        }}/>
        <Stack.Screen name={"schedule/[storeId]/requests/availability"} options={{
          headerBackVisible: true,
          headerBackButtonDisplayMode: "minimal",
          headerTitle: "Availability",
          headerBackground: () => <LinearGradient colors={beautifulGradientColors}
                                                  style={StyleSheet.absoluteFill}/>,
          headerTintColor: "#fff",
          headerShadowVisible: true,
        }}/>
        <Stack.Screen name={"schedule/[storeId]/requests/availability-details"} options={{
          headerBackVisible: true,
          headerTitle: "Availability Request",
          headerBackground: () => <LinearGradient colors={beautifulGradientColors}
                                                  style={StyleSheet.absoluteFill}/>,
          headerTintColor: "#fff",
          headerShadowVisible: true,
          headerBackTitle: "Back"
        }}/>
        <Stack.Screen name={"schedule/[storeId]/requests/create-availability"} options={{
          headerBackVisible: true,
          headerTitle: "Create Request",
          headerBackground: () => <LinearGradient colors={beautifulGradientColors}
                                                  style={StyleSheet.absoluteFill}/>,
          headerTintColor: "#fff",
          headerShadowVisible: true,
        }}/>
        <Stack.Screen name={"schedule/[storeId]/requests/create-availability-summary"} options={{
          headerBackVisible: true,
          headerTitle: "Summary",
          headerBackground: () => <LinearGradient colors={beautifulGradientColors}
                                                  style={StyleSheet.absoluteFill}/>,
          headerTintColor: "#fff",
          headerShadowVisible: true,
        }}/>
        <Stack.Screen name={"schedule/[storeId]/requests/create-availability-day"} options={{
          headerShown: false,
        }}/>
        <Stack.Screen name={"schedule/[storeId]/requests/shift-offer"} options={{
          headerBackVisible: true,
          headerBackButtonDisplayMode: "minimal",
          headerTitle: "Shift Offers",
          headerBackground: () => <LinearGradient colors={beautifulGradientColors}
                                                  style={StyleSheet.absoluteFill}/>,
          headerTintColor: "#fff",
          headerShadowVisible: true,
        }}/>
        <Stack.Screen name={"schedule/[storeId]/requests/shift-swap"} options={{
          headerBackVisible: true,
          headerBackButtonDisplayMode: "minimal",
          headerTitle: "Shift Swaps",
          headerBackground: () => <LinearGradient colors={beautifulGradientColors}
                                                  style={StyleSheet.absoluteFill}/>,
          headerTintColor: "#fff",
          headerShadowVisible: true,
        }}/>
        <Stack.Screen name={"schedule/[storeId]/requests/shift-offer-details"} options={{
          headerShown: false,
          presentation: "modal",
        }}/>
        <Stack.Screen name={"schedule/[storeId]/requests/shift-swap-details"} options={{
          headerShown: false,
          presentation: "modal",
        }}/>
        <Stack.Screen name={"schedule/[storeId]/schedule/day-view"} options={{
          headerBackVisible: true,
          headerBackButtonDisplayMode: "default",
          headerTitle: "Schedule",
          headerBackground: () => <LinearGradient colors={beautifulGradientColors}
                                                  style={StyleSheet.absoluteFill}/>,
          headerTintColor: "#fff",
          headerShadowVisible: true,
        }}/>
        <Stack.Screen name={"schedule/[storeId]/schedule/create-shift"} options={{
          headerBackVisible: true,
          headerBackButtonDisplayMode: "default",
          headerTitle: "Create Shift",
          headerBackground: () => <LinearGradient colors={beautifulGradientColors}
                                                  style={StyleSheet.absoluteFill}/>,
          headerTintColor: "#fff",
          headerShadowVisible: true,
        }}/>

        <Stack.Screen name={"checklist/[storeId]/new"} options={{
          headerBackVisible: true,
          headerBackTitle: "Back",
          headerTitle: "New Checklist",
          headerBackground: () => <LinearGradient colors={beautifulGradientColors}
                                                  style={StyleSheet.absoluteFill}/>,
          headerTintColor: "#fff",
          headerShadowVisible: true,
        }}/>

        <Stack.Screen name={"checklist/[storeId]/template/index"} options={{
          headerBackVisible: true,
          headerBackTitle: "Back",
          headerTitle: "Checklist Templates",
          headerBackground: () => <LinearGradient colors={beautifulGradientColors}
                                                  style={StyleSheet.absoluteFill}/>,
          headerTintColor: "#fff",
          headerShadowVisible: true,
        }}/>

        <Stack.Screen name={"checklist/[storeId]/template/[templateId]/index"} options={{
          headerBackVisible: true,
          headerBackTitle: "Back",
          headerTitle: "Checklist Template",
          headerBackground: () => <LinearGradient colors={beautifulGradientColors}
                                                  style={StyleSheet.absoluteFill}/>,
          headerTintColor: "#fff",
          headerShadowVisible: true,
        }}/>

        <Stack.Screen name={"checklist/[storeId]/template/[templateId]/assign"} options={{
          headerBackVisible: true,
          headerBackTitle: "Back",
          headerTitle: "Use Template",
          headerBackground: () => <LinearGradient colors={beautifulGradientColors}
                                                  style={StyleSheet.absoluteFill}/>,
          headerTintColor: "#fff",
          headerShadowVisible: true,
        }}/>

        <Stack.Screen name={"checklist/[storeId]/event/[checklistEventId]/index"} options={{
          headerBackVisible: true,
          headerBackTitle: "Back",
          headerTitle: "Checklist",
          headerBackground: () => <LinearGradient colors={beautifulGradientColors}
                                                  style={StyleSheet.absoluteFill}/>,
          headerTintColor: "#fff",
          headerShadowVisible: true,
        }}/>

        <Stack.Screen name={"checklist/[storeId]/item/[itemId]"} options={{
          headerBackVisible: true,
          headerTitle: "Checklist Item",
          headerBackground: () => <LinearGradient colors={beautifulGradientColors}
                                                  style={StyleSheet.absoluteFill}/>,
          headerTintColor: "#fff",
          headerShadowVisible: true,
        }}/>

        <Stack.Screen name={"checklist/[storeId]/history/index"} options={{
          headerBackVisible: true,
          headerBackTitle: "Back",
          headerTitle: "Recorded Checklists",
          headerBackground: () => <LinearGradient colors={beautifulGradientColors}
                                                  style={StyleSheet.absoluteFill}/>,
          headerTintColor: "#fff",
          headerShadowVisible: true,
        }}/>

        <Stack.Screen name={"checklist2/[storeId]/new"} options={{
          headerBackVisible: true,
          headerBackTitle: "Back",
          headerTitle: "New Checklist",
          headerBackground: () => <LinearGradient colors={beautifulGradientColors}
                                                  style={StyleSheet.absoluteFill}/>,
          headerTintColor: "#fff",
          headerShadowVisible: true,
        }}/>

        <Stack.Screen name={"checklist2/[storeId]/template/index"} options={{
          headerBackVisible: true,
          headerBackTitle: "Back",
          headerTitle: "Checklist Templates",
          headerBackground: () => <LinearGradient colors={beautifulGradientColors}
                                                  style={StyleSheet.absoluteFill}/>,
          headerTintColor: "#fff",
          headerShadowVisible: true,
        }}/>

        <Stack.Screen name={"checklist2/[storeId]/template/[templateId]/index"} options={{
          headerBackVisible: true,
          headerBackTitle: "Back",
          headerTitle: "Checklist Template",
          headerBackground: () => <LinearGradient colors={beautifulGradientColors}
                                                  style={StyleSheet.absoluteFill}/>,
          headerTintColor: "#fff",
          headerShadowVisible: true,
        }}/>

        <Stack.Screen name={"checklist2/[storeId]/template/[templateId]/use"} options={{
          headerBackVisible: true,
          headerBackTitle: "Back",
          headerTitle: "Use Template",
          headerBackground: () => <LinearGradient colors={beautifulGradientColors}
                                                  style={StyleSheet.absoluteFill}/>,
          headerTintColor: "#fff",
          headerShadowVisible: true,
        }}/>

        <Stack.Screen name={"checklist2/[storeId]/template/[templateId]/item/[itemId]"} options={{
          headerBackVisible: true,
          headerTitle: "Checklist Item",
          headerBackground: () => <LinearGradient colors={beautifulGradientColors}
                                                  style={StyleSheet.absoluteFill}/>,
          headerTintColor: "#fff",
          headerShadowVisible: true,
        }}/>

        <Stack.Screen name={"checklist2/[storeId]/checklist/[checklistId]/item/[itemId]"} options={{
          headerBackVisible: true,
          headerTitle: "Checklist Item",
          headerBackground: () => <LinearGradient colors={beautifulGradientColors}
                                                  style={StyleSheet.absoluteFill}/>,
          headerTintColor: "#fff",
          headerShadowVisible: true,
        }}/>

        <Stack.Screen name={"checklist2/[storeId]/checklist/[checklistId]/index"} options={{
          headerBackVisible: true,
          headerBackTitle: "Back",
          headerTitle: "Checklist",
          headerBackground: () => <LinearGradient colors={beautifulGradientColors}
                                                  style={StyleSheet.absoluteFill}/>,
          headerTintColor: "#fff",
          headerShadowVisible: true,
        }}/>

        <Stack.Screen name={"checklist2/[storeId]/checklist/[checklistId]/view/index"} options={{
          headerBackVisible: true,
          headerTitle: "Checklist",
          headerBackTitle: "Back",
          headerBackground: () => <LinearGradient colors={beautifulGradientColors}
                                                  style={StyleSheet.absoluteFill}/>,
          headerTintColor: "#fff",
          headerShadowVisible: true,
        }}/>

        <Stack.Screen name={"checklist2/[storeId]/checklist/[checklistId]/assign"} options={{
          headerBackVisible: true,
          headerBackTitle: "Cancel",
          headerTitle: "Assign Checklist",
          headerBackground: () => <LinearGradient colors={beautifulGradientColors}
                                                  style={StyleSheet.absoluteFill}/>,
          headerTintColor: "#fff",
          headerShadowVisible: true,
        }}/>
        <Stack.Screen name={"checklist2/[storeId]/checklist/[checklistId]/archive"} options={{
          headerBackVisible: true,
          headerBackTitle: "Cancel",
          headerTitle: "Archive Checklist",
          headerBackground: () => <LinearGradient colors={beautifulGradientColors}
                                                  style={StyleSheet.absoluteFill}/>,
          headerTintColor: "#fff",
          headerShadowVisible: true,
        }}/>

        <Stack.Screen name={"checklist2/[storeId]/history/index"} options={{
          headerBackVisible: true,
          headerBackTitle: "Back",
          headerTitle: "Recorded Checklists",
          headerBackground: () => <LinearGradient colors={beautifulGradientColors}
                                                  style={StyleSheet.absoluteFill}/>,
          headerTintColor: "#fff",
          headerShadowVisible: true,
        }}/>

        <Stack.Screen name={"recurrence/index"} options={{
          headerBackVisible: true,
          headerTitle: "Repeat",
          headerBackground: () => <LinearGradient colors={beautifulGradientColors}
                                                  style={StyleSheet.absoluteFill}/>,
          headerTintColor: "#fff",
          headerShadowVisible: true,
        }}/>

        <Stack.Screen name={"recurrence/custom"} options={{
          headerBackVisible: true,
          headerTitle: "Custom Repeat",
          headerBackground: () => <LinearGradient colors={beautifulGradientColors}
                                                  style={StyleSheet.absoluteFill}/>,
          headerTintColor: "#fff",
          headerShadowVisible: true,
        }}/>

        <Stack.Screen name={"checklist/[storeId]/team/[personId]/index"} options={{
          headerBackVisible: true,
          headerTitle: "Your Checklists",
          headerBackTitle: "Back",
          headerBackground: () => <LinearGradient colors={beautifulGradientColors}
                                                  style={StyleSheet.absoluteFill}/>,
          headerTintColor: "#fff",
          headerShadowVisible: true,
        }}/>

        <Stack.Screen name={"checklist/[storeId]/event/[checklistEventId]/view/index"} options={{
          headerBackVisible: true,
          headerTitle: "Checklist",
          headerBackTitle: "Back",
          headerBackground: () => <LinearGradient colors={beautifulGradientColors}
                                                  style={StyleSheet.absoluteFill}/>,
          headerTintColor: "#fff",
          headerShadowVisible: true,
        }}/>

        <Stack.Screen name={"checklist/[storeId]/event/[checklistEventId]/view/item/[itemId]"} options={{
          headerBackVisible: true,
          headerTitle: "Checklist Items",
          headerBackTitle: "Back",
          headerBackground: () => <LinearGradient colors={beautifulGradientColors}
                                                  style={StyleSheet.absoluteFill}/>,
          headerTintColor: "#fff",
          headerShadowVisible: true,
        }}/>

        {/****** SETUP SHEETS ******/}
        <Stack.Screen name={"shift-lead/[storeId]/setup-sheets-tablet/setup-wizard"} options={{
          headerBackButtonDisplayMode: "default",
          headerBackTitle: "Done",
          headerTitle: "Configure Store",
          headerBackground: () => <LinearGradient colors={beautifulGradientColors}
                                                  style={StyleSheet.absoluteFill}/>,
          headerTintColor: "#fff",
          headerShadowVisible: true,
        }}/>
        <Stack.Screen name={"shift-lead/[storeId]/setup-sheets-tablet/edit-timeframes"} options={{
          headerBackButtonDisplayMode: "default",
          headerBackTitle: "Cancel",
          headerTitle: "Edit Time Frames",
          headerBackground: () => <LinearGradient colors={beautifulGradientColors}
                                                  style={StyleSheet.absoluteFill}/>,
          headerTintColor: "#fff",
          headerShadowVisible: true,
        }}/>
        <Stack.Screen name={"shift-lead/[storeId]/setup-sheets-tablet/edit-setup-positions"} options={{
          headerBackButtonDisplayMode: "default",
          headerBackTitle: "Cancel",
          headerTitle: "Edit Setup Positions",
          headerBackground: () => <LinearGradient colors={beautifulGradientColors}
                                                  style={StyleSheet.absoluteFill}/>,
          headerTintColor: "#fff",
          headerShadowVisible: true,
        }}/>
        <Stack.Screen name={"shift-lead/[storeId]/setup-sheets-tablet/manage-tags"} options={{
          headerBackButtonDisplayMode: "default",
          headerBackTitle: "Back",
          headerTitle: "Configure Setup Tags",
          headerBackground: () => <LinearGradient colors={beautifulGradientColors}
                                                  style={StyleSheet.absoluteFill}/>,
          headerTintColor: "#fff",
          headerShadowVisible: true,
        }}/>
        <Stack.Screen name={"shift-lead/[storeId]/setup-sheets-tablet/[setupDayId]/agenda"} options={{
          headerShown: false,
          gestureEnabled: false,
        }}/>
        <Stack.Screen name={"shift-lead/[storeId]/setup-sheets-tablet/[setupDayId]/timeFrames/[timeFrameId]"}
                      options={{
                        headerBackButtonDisplayMode: "default",
                        headerBackTitle: "Done",
                        headerTitle: "Edit Layout",
                        headerBackground: () => <LinearGradient colors={beautifulGradientColors}
                                                                style={StyleSheet.absoluteFill}/>,
                        headerTintColor: "#fff",
                        headerShadowVisible: true,
                      }}/>
        <Stack.Screen name={"shift-lead/[storeId]/setup-sheets-tablet/[setupDayId]/setup-day"} options={{
          headerShown: false,
          gestureEnabled: false,
        }}/>
        <Stack.Screen name={"shift-lead/[storeId]/setup-sheets-tablet/layouts/[layoutId]/index"} options={{
          headerBackButtonDisplayMode: "default",
          headerBackTitle: "Done",
          headerTitle: "Store Layout",
          headerBackground: () => <LinearGradient colors={beautifulGradientColors}
                                                  style={StyleSheet.absoluteFill}/>,
          headerTintColor: "#fff",
          headerShadowVisible: true,
        }}/>
        <Stack.Screen name={"shift-lead/[storeId]/setup-sheets/new-select-type"} options={{
          headerBackButtonDisplayMode: "minimal",
          headerTitle: "New Setup Sheet",
          headerBackground: () => <LinearGradient colors={beautifulGradientColors}
                                                  style={StyleSheet.absoluteFill}/>,
          headerTintColor: "#fff",
          headerShadowVisible: true,
        }}/>
        <Stack.Screen name={"shift-lead/[storeId]/setup-sheets/create-new"} options={{
          headerBackButtonDisplayMode: "minimal",
          headerTitle: "Create New Setup Sheet",
          headerBackground: () => <LinearGradient colors={beautifulGradientColors}
                                                  style={StyleSheet.absoluteFill}/>,
          headerTintColor: "#fff",
          headerShadowVisible: true,
        }}/>
        <Stack.Screen name={"shift-lead/[storeId]/setup-sheets/edit-positions"} options={{
          headerBackButtonDisplayMode: "minimal",
          headerTitle: "Create New",
          headerBackground: () => <LinearGradient colors={beautifulGradientColors}
                                                  style={StyleSheet.absoluteFill}/>,
          headerTintColor: "#fff",
          headerShadowVisible: true,
        }}/>
        <Stack.Screen name={"shift-lead/[storeId]/setup-sheets/select-template"} options={{
          headerBackButtonDisplayMode: "minimal",
          headerTitle: "Setup Sheet Templates",
          headerBackground: () => <LinearGradient colors={beautifulGradientColors}
                                                  style={StyleSheet.absoluteFill}/>,
          headerTintColor: "#fff",
          headerShadowVisible: true,
        }}/>
        <Stack.Screen name={"shift-lead/[storeId]/setup-sheets/configure-template"} options={{
          headerBackButtonDisplayMode: "minimal",
          headerTitle: "Templates",
          headerBackground: () => <LinearGradient colors={beautifulGradientColors}
                                                  style={StyleSheet.absoluteFill}/>,
          headerTintColor: "#fff",
          headerShadowVisible: true,
        }}/>
        <Stack.Screen name={"shift-lead/[storeId]/setup-sheets/active-setup-sheets"} options={{
          headerBackButtonDisplayMode: "minimal",
          headerTitle: "Active Setup Sheets",
          headerBackground: () => <LinearGradient colors={beautifulGradientColors}
                                                  style={StyleSheet.absoluteFill}/>,
          headerTintColor: "#fff",
          headerShadowVisible: true,
        }}/>
        <Stack.Screen name={"shift-lead/[storeId]/setup-sheets/[setupSheetId]/edit-setup-sheet"} options={{
          headerBackButtonDisplayMode: "minimal",
          headerTitle: "Edit Setup Sheet", // to be overridden in file
          headerBackground: () => <LinearGradient colors={beautifulGradientColors}
                                                  style={StyleSheet.absoluteFill}/>,
          headerTintColor: "#fff",
          headerShadowVisible: true,
        }}/>
        <Stack.Screen name={"shift-lead/[storeId]/setup-sheets/position/[setupSheetPositionId]/agenda"} options={{
          headerBackButtonDisplayMode: "minimal",
          headerTitle: "Position", // should be replaced dynamically w/ position name
          headerBackground: () => <LinearGradient colors={beautifulGradientColors}
                                                  style={StyleSheet.absoluteFill}/>,
          headerTintColor: "#fff",
          headerShadowVisible: true,
        }}/>
        <Stack.Screen name={"shift-lead/[storeId]/setup-sheets/person/[personId]/agenda"} options={{
          headerBackButtonDisplayMode: "minimal",
          headerTitle: "Person",
          headerBackground: () => <LinearGradient colors={beautifulGradientColors}
                                                  style={StyleSheet.absoluteFill}/>,
          headerTintColor: "#fff",
          headerShadowVisible: true,
        }}/>
        <Stack.Screen name={"shift-lead/[storeId]/shift-notes/[scheduleId]/index"} options={{
          headerShown: false,
          presentation: "modal",
          // gestureEnabled: false,
          headerTitle: "Shift Notes",
        }}/>
        <Stack.Screen name={"shift-lead/[storeId]/shift-notes/[scheduleId]/readonly"} options={{
          headerShown: false,
          presentation: "modal",
          // gestureEnabled: false,
          headerTitle: "Shift Notes",
        }}/>
        <Stack.Screen name={"insights/schedule/[scheduleId]/notes/index"} options={{
          headerShown: false,
          presentation: "modal",
          // gestureEnabled: false,
          headerTitle: "Schedule Notes",
        }}/>
        <Stack.Screen name={"shift-lead/[storeId]/scores/index"} options={{
          headerBackVisible: true,
          headerTitle: "Position Scores",
          headerBackTitle: "Back",
          headerBackground: () => <LinearGradient colors={beautifulGradientColors}
                                                  style={StyleSheet.absoluteFill}/>,
          headerTintColor: "#fff",
          headerShadowVisible: true,
        }}/>
        <Stack.Screen name={"shift-lead/[storeId]/scores/[personId]/score"} options={{
          headerBackVisible: true,
          headerTitle: "Position Scores",
          headerBackground: () => <LinearGradient colors={beautifulGradientColors}
                                                  style={StyleSheet.absoluteFill}/>,
          headerTintColor: "#fff",
          headerShadowVisible: true,
        }}/>
        <Stack.Screen name={"checklist/[storeId]/event/[checklistEventId]/assign"} options={{
          headerBackVisible: true,
          headerBackTitle: "Cancel",
          headerTitle: "Assign Checklist",
          headerBackground: () => <LinearGradient colors={beautifulGradientColors}
                                                  style={StyleSheet.absoluteFill}/>,
          headerTintColor: "#fff",
          headerShadowVisible: true,
        }}/>
        <Stack.Screen name={"checklist/[storeId]/event/[checklistEventId]/archive"} options={{
          headerBackVisible: true,
          headerBackTitle: "Cancel",
          headerTitle: "Archive Checklist",
          headerBackground: () => <LinearGradient colors={beautifulGradientColors}
                                                  style={StyleSheet.absoluteFill}/>,
          headerTintColor: "#fff",
          headerShadowVisible: true,
        }}/>
        <Stack.Screen name={"hr/[storeId]/correctiveActions/attentionList"} options={{
          headerBackVisible: true,
          headerBackTitle: "Back",
          headerTitle: "Actionable Items",
          headerBackground: () => <LinearGradient colors={beautifulGradientColors}
                                                  style={StyleSheet.absoluteFill}/>,
          headerTintColor: "#fff",
          headerShadowVisible: true,
        }}/>

        <Stack.Screen
                name={"person/[storeId]/[personId]/metric/position/[positionId]/datapoint/[dataPointId]/view"}
                options={{
                  headerBackVisible: true,
                  headerBackTitle: "Back",
                  headerTitle: "Position Score",
                  headerBackground: () => <LinearGradient colors={beautifulGradientColors}
                                                          style={StyleSheet.absoluteFill}/>,
                  headerTintColor: "#fff",
                  headerShadowVisible: true,
                }}/>

        <Stack.Screen name={"insights/store/[storeId]/shiftLead/[dataPointId]/view"} options={{
          headerBackVisible: true,
          headerBackTitle: "Back",
          headerTitle: "Shift Leader Rating",
          headerBackground: () => <LinearGradient colors={beautifulGradientColors}
                                                  style={StyleSheet.absoluteFill}/>,
          headerTintColor: "#fff",
          headerShadowVisible: true,
        }}/>

        <Stack.Screen name={"insights/store/[storeId]/notes/shiftAndScheduling"} options={{
          headerBackVisible: true,
          headerBackTitle: "Back",
          headerTitle: "Notes",
          headerBackground: () => <LinearGradient colors={beautifulGradientColors}
                                                  style={StyleSheet.absoluteFill}/>,
          headerTintColor: "#fff",
          headerShadowVisible: true,
        }}/>

        <Stack.Screen name={"store/[storeId]/chat/[cid]/index"} options={{
          headerShown: false,
        }}/>

        <Stack.Screen name={"store/[storeId]/chat/[cid]/settings"} options={{
          headerShown: false,
        }}/>

        <Stack.Screen name={"store/[storeId]/chat/[cid]/edit"} options={{
          headerShown: false,
          presentation: "modal",
        }}/>

        <Stack.Screen name={"store/[storeId]/chat/[cid]/all-members"} options={{
          headerShown: false,
          presentation: "modal",
        }}/>

        <Stack.Screen name={"store/[storeId]/chat/[cid]/search"} options={{
          headerShown: false,
        }}/>

        <Stack.Screen name={"store/[storeId]/chat/[cid]/read-receipts"} options={{
          headerShown: false,
          presentation: "modal",
        }}/>

        <Stack.Screen name={"store/[storeId]/chat/[cid]/add-members"} options={{
          headerBackTitle: i18n._({id: "Back", message: "Back"}),
          headerTitle: "Add Members",
          headerTintColor: colors.gray[800],
        }}/>

        <Stack.Screen name={"store/[storeId]/chat/[cid]/remove-members"} options={{
          headerBackTitle: i18n._({id: "Back", message: "Back"}),
          headerTitle: "Remove Members",
          headerTintColor: colors.gray[800],
        }}/>

        <Stack.Screen name={"store/[storeId]/chat/[cid]/edit-jobs"} options={{
          headerBackTitle: i18n._({id: "Back", message: "Back"}),
          headerTitle: "Edit Jobs",
          headerTintColor: colors.gray[800]
        }}/>

        <Stack.Screen name={"store/[storeId]/chat/[cid]/edit-filters"} options={{
          headerBackTitle: i18n._({id: "Back", message: "Back"}),
          headerTitle: "Edit Membership",
          headerTintColor: colors.gray[800]
        }}/>

        <Stack.Screen name={"store/[storeId]/chat/[cid]/thread/[parentMessageId]/index"} options={{
          headerBackButtonDisplayMode: "default",
          headerBackTitle: "Back",
          headerTitle: "Thread",
          headerBackground: () => <LinearGradient colors={beautifulGradientColors} style={StyleSheet.absoluteFill}/>,
          headerTintColor: "#fff",
          headerShadowVisible: true,
        }}/>

        <Stack.Screen name={"store/[storeId]/groups/create-group"} options={{
          headerBackButtonDisplayMode: "default",
          headerBackTitle: "Cancel",
          headerTitle: "Create Group",
          headerBackground: () => <LinearGradient colors={beautifulGradientColors} style={StyleSheet.absoluteFill}/>,
          headerTintColor: "#fff",
          headerShadowVisible: true,
        }}/>

        <Stack.Screen name={"store/[storeId]/groups/create-group-visibility"} options={{
          headerBackButtonDisplayMode: "default",
          headerBackTitle: "Back",
          headerTitle: "Group Visibility",
          headerBackground: () => <LinearGradient colors={beautifulGradientColors} style={StyleSheet.absoluteFill}/>,
          headerTintColor: "#fff",
          headerShadowVisible: true,
        }}/>

        <Stack.Screen name={"store/[storeId]/groups/create-group-members"} options={{
          headerBackVisible: false,
          headerTitle: "Group Members",
          headerBackground: () => <LinearGradient colors={beautifulGradientColors} style={StyleSheet.absoluteFill}/>,
          headerTintColor: "#fff",
          headerShadowVisible: true,
        }}/>

        <Stack.Screen name={"store/[storeId]/groups/create-direct-message"} options={{
          headerBackButtonDisplayMode: "default",
          headerBackTitle: "Cancel",
          headerTitle: "Direct Message",
          headerBackground: () => <LinearGradient colors={beautifulGradientColors} style={StyleSheet.absoluteFill}/>,
          headerTintColor: "#fff",
          headerShadowVisible: true,
        }}/>

        {/* GROUPS 2 */}

        <Stack.Screen name={"store/[storeId]/groups2/create-group"} options={{
          headerBackButtonDisplayMode: "default",
          headerBackTitle: "Cancel",
          headerTitle: "Create Group",
          headerBackground: () => <LinearGradient colors={beautifulGradientColors} style={StyleSheet.absoluteFill}/>,
          headerTintColor: "#fff",
          headerShadowVisible: true,
        }}/>

        <Stack.Screen name={"store/[storeId]/groups2/create-group-visibility"} options={{
          headerBackButtonDisplayMode: "default",
          headerBackTitle: "Back",
          headerTitle: "Create Group",
          headerBackground: () => <LinearGradient colors={beautifulGradientColors} style={StyleSheet.absoluteFill}/>,
          headerTintColor: "#fff",
          headerShadowVisible: true,
        }}/>

        <Stack.Screen name={"store/[storeId]/groups2/create-group-filters"} options={{
          headerBackButtonDisplayMode: "default",
          headerBackTitle: "Back",
          headerTitle: "Create Group",
          headerBackground: () => <LinearGradient colors={beautifulGradientColors} style={StyleSheet.absoluteFill}/>,
          headerTintColor: "#fff",
          headerShadowVisible: true,
        }}/>

        {/*OPERATOR GROUPS*/}

        <Stack.Screen name={"operator/groups/index"} options={{
          headerBackButtonDisplayMode: "minimal",
          headerBackTitle: "Back",
          headerTitle: "Operator Groups",
          headerBackground: () => <LinearGradient colors={beautifulGradientColors} style={StyleSheet.absoluteFill}/>,
          headerTintColor: "#fff",
          headerShadowVisible: true,
        }}/>

        <Stack.Screen name={"operator/groups/chat/[cid]/index"} options={{
          headerShown: false,
        }}/>

        <Stack.Screen name={"operator/groups/chat/[cid]/settings"} options={{
          headerShown: false,
        }}/>

        <Stack.Screen name={"operator/groups/chat/[cid]/edit"} options={{
          headerShown: false,
          presentation: "modal",
        }}/>

        <Stack.Screen name={"operator/groups/chat/[cid]/all-members"} options={{
          headerShown: false,
          presentation: "modal",
        }}/>

        <Stack.Screen name={"operator/groups/chat/[cid]/search"} options={{
          headerShown: false,
        }}/>

        <Stack.Screen name={"operator/groups/chat/[cid]/add-members"} options={{
          headerBackTitle: i18n._({id: "Back", message: "Back"}),
          headerTitle: "Add Members",
          headerTintColor: colors.gray[800],
        }}/>

        <Stack.Screen name={"operator/groups/chat/[cid]/remove-members"} options={{
          headerBackTitle: i18n._({id: "Back", message: "Back"}),
          headerTitle: "Remove Members",
          headerTintColor: colors.gray[800]
        }}/>

        <Stack.Screen name={"operator/groups/chat/[cid]/thread/[parentMessageId]/index"} options={{
          headerBackButtonDisplayMode: "default",
          headerBackTitle: "Back",
          headerTitle: "Thread",
          headerBackground: () => <LinearGradient colors={beautifulGradientColors} style={StyleSheet.absoluteFill}/>,
          headerTintColor: "#fff",
          headerShadowVisible: true,
        }}/>

        <Stack.Screen name={"operator/groups/create-group"} options={{
          headerBackButtonDisplayMode: "default",
          headerBackTitle: "Cancel",
          headerTitle: "Create Group",
          headerBackground: () => <LinearGradient colors={beautifulGradientColors} style={StyleSheet.absoluteFill}/>,
          headerTintColor: "#fff",
          headerShadowVisible: true,
        }}/>

        <Stack.Screen name={"operator/groups/create-group-visibility"} options={{
          headerBackButtonDisplayMode: "default",
          headerBackTitle: "Back",
          headerTitle: "Group Visibility",
          headerBackground: () => <LinearGradient colors={beautifulGradientColors} style={StyleSheet.absoluteFill}/>,
          headerTintColor: "#fff",
          headerShadowVisible: true,
        }}/>

        <Stack.Screen name={"operator/groups/create-group-members"} options={{
          headerBackVisible: false,
          headerTitle: "Group Members",
          headerBackground: () => <LinearGradient colors={beautifulGradientColors} style={StyleSheet.absoluteFill}/>,
          headerTintColor: "#fff",
          headerShadowVisible: true,
        }}/>

        <Stack.Screen name={"operator/groups/create-direct-message"} options={{
          headerBackButtonDisplayMode: "default",
          headerBackTitle: "Cancel",
          headerTitle: "Direct Message",
          headerBackground: () => <LinearGradient colors={beautifulGradientColors} style={StyleSheet.absoluteFill}/>,
          headerTintColor: "#fff",
          headerShadowVisible: true,
        }}/>
      </Stack>
    </View>
  </ChatWrapper>;
}
