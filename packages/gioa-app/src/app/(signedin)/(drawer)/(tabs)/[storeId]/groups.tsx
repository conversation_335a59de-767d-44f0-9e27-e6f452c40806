import {Pressable, ScrollView, View} from "react-native";
import {cn} from "@/style.util";
import React, {Suspense, useCallback, useContext, useLayoutEffect, useMemo} from "react";
import {ScreenLoadingIndicator} from "@/components/ScreenLoadingIndicator";
import {ScreenErrorBoundary} from "@/components/ScreenErrorBoundary";
import {useSafeAreaInsets} from "react-native-safe-area-context";
import {ChatContext, ChatContextValue} from "stream-chat-expo";
import type {ChannelSort} from 'stream-chat';
import {ChannelFilters} from "stream-chat";
import {router, useLocalSearchParams} from "expo-router";
import {useNavigation} from "@react-navigation/native";
import {Ionicons} from "@expo/vector-icons";
import {api} from "@/api";
import {StatusBar} from "expo-status-bar";
import {useNavigationPadding} from "@/hooks/useNavigationPadding";
import {
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuItemTitle,
  DropdownMenuRoot,
  DropdownMenuTrigger
} from "@/components/PopoverMenu";
import {CollapsibleChannelList} from "@/components/chat/CollapsibleChannelList";
import {colors} from "@/styles";
import {Text} from "@/components/Text";
import {OperatorPurgatoryMessage} from "@/components/OperatorPurgatoryMessage";
import {useStoreOnboardingStatus} from "@/hooks/useIsOperatorInPurgatory";
import {StoreSwitcherHeaderTitle} from "@/components/StoreSwitcherHeaderTitle";
import {SecureBusinessId, SecureStoreId} from "../../../../../../../api/src/database.types";

export type ChannelListEventListenerOptions = {
  filters?: ChannelFilters;
  sort?: ChannelSort;
};

const groupsOptions = {
  state: true,
  watch: true,
  limit: 100
};

const directMessagesOptions = {
  state: true,
  watch: true,
  limit: 20,
};

export function GroupsScreenComp() {
  const {storeId} = useLocalSearchParams<{ storeId: string }>();
  const safeArea = useSafeAreaInsets();
  const [userProfile] = api.user.getUserProfile.useSuspenseQuery(undefined, {
    staleTime: 1000 * 60 * 60 // 1 hour
  });
  const userStoreIds = userProfile.person.storeIds as SecureStoreId[];
  const [store] = api.user.getStore.useSuspenseQuery({storeId: storeId!}, {
    staleTime: 1000 * 60 * 60 // 1 hour
  });
  const currentPersonId = userProfile.person.id!;
  const {isOperatorInPurgatory} = useStoreOnboardingStatus({storeId})

  const onAddGroup = useCallback(async () => {
    router.navigate({
      pathname: "/(signedin)/store/[storeId]/groups2/create-group",
      params: {storeId}
    });
  }, [storeId])

  const onAddDirectMessage = useCallback(async () => {
    router.navigate({
      pathname: "/(signedin)/store/[storeId]/groups/create-direct-message",
      params: {storeId}
    });
  }, [storeId])

  const navigation = useNavigation();
  useLayoutEffect(() => {

    if (store.isPersonUnapproved) {
      navigation.setOptions({
        headerTitle: "Groups",
        headerRight: undefined
      });
      return;
    }

    navigation.setOptions({
      headerTitle: "Groups",
      headerRight: () => {
        // only chat admins can create groups
        if (userProfile.hasChatAdminPermission) {
          return <DropdownMenuRoot>
            <DropdownMenuTrigger>
              <Pressable style={cn("pr-3")}>
                <Ionicons name={"add"} size={24} color={"white"}/>
              </Pressable>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              <DropdownMenuItem key="create-direct-message" onSelect={onAddDirectMessage}>
                <DropdownMenuItemTitle>Direct Message</DropdownMenuItemTitle>
              </DropdownMenuItem>
              <DropdownMenuItem key="create-group" onSelect={onAddGroup}>
                <DropdownMenuItemTitle>Create Group</DropdownMenuItemTitle>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenuRoot>;
        } else {
          return <Pressable style={cn("pr-3")} onPress={onAddDirectMessage}>
            <Ionicons name={"add"} size={24} color={"white"}/>
          </Pressable>;
        }
      }
    })
  }, [onAddGroup, onAddDirectMessage, store.isPersonUnapproved, userProfile.hasChatAdminPermission]);

  // Filter for groups (channels with IDs or public team channels)
  const groupsFilters = useMemo(() => ({
    $or: [
      // public team channels (TODO "team" TYPE CHANNELS ARE DEPRECATED -- All "groups" use "messaging" type now)
      {type: "team", team: {$in: userStoreIds}},
      // private messaging channels with IDs that the user is a member of
      {type: "messaging", members: {$in: [currentPersonId]}, team: {$in: [...userStoreIds, userProfile.businessId as SecureBusinessId]}}
    ]
  } satisfies ChannelFilters), [currentPersonId, userStoreIds, userProfile.businessId]);

  // Filter for direct messages (distinct channels without IDs)
  const directMessagesFilters = useMemo(() => ({
    type: "direct",
    archived: false,
    team: {$in: [...userStoreIds, userProfile.businessId as SecureBusinessId]},
    members: {$in: [currentPersonId]},
  } satisfies ChannelFilters), [currentPersonId, userStoreIds, userProfile.businessId]);

  const navPadding = useNavigationPadding();

  if (store.isPersonUnapproved) {
    return <View style={cn("flex-1 bg-gray-100 border-t border-gray-300 items-center justify-center")}>
      <View
              style={cn("flex flex-col items-center justify-center bg-white rounded-lg px-4 py-6 border border-gray-300 gap-3 mx-6")}>
        <Ionicons name={"people"} size={60} color={colors.gray[400]}/>
        <Text center style={cn("text-2xl")} bold colorScheme="dark">
          Groups
        </Text>
        <Text center>
          You will see chat groups at your store here once your profile has been approved. Check back later!
        </Text>
      </View>
    </View>
  }

  if (isOperatorInPurgatory) {
    return <OperatorPurgatoryMessage storeId={storeId}/>
  }

  return <ScrollView contentContainerStyle={{paddingBottom: Math.max(safeArea.bottom, navPadding + 24)}}
                     style={[cn("flex-1 bg-white")]}>
    <CollapsibleChannelList title={"Groups"} filters={groupsFilters} options={groupsOptions}
                            storeId={storeId} channelTypes={["team", "messaging"]}
                            onAddDirectMessage={onAddDirectMessage}
                            onAddGroup={onAddGroup}/>
    <CollapsibleChannelList title={"Direct Messages"} filters={directMessagesFilters} options={directMessagesOptions}
                            storeId={storeId} channelTypes={["direct"]}
                            onAddDirectMessage={onAddDirectMessage}
                            onAddGroup={onAddGroup}/>
  </ScrollView>
}

export default function GroupsScreen() {
  const contextValue = useContext(ChatContext) as unknown as ChatContextValue;
  // if the chat client is not initialized yet, show loading screen
  if (!contextValue.client) {
    return <ScreenLoadingIndicator/>
  }

  return <Suspense fallback={<ScreenLoadingIndicator/>}>
    <StatusBar style={"light"}/>
    <GroupsScreenComp/>
  </Suspense>
}

export const ErrorBoundary = ScreenErrorBoundary;


