import {Pressable, View} from "react-native";
import {cn} from "@/style.util";
import React, {Suspense, useCallback, useContext, useLayoutEffect, useMemo} from "react";
import {ScreenLoadingIndicator} from "@/components/ScreenLoadingIndicator";
import {ScreenErrorBoundary} from "@/components/ScreenErrorBoundary";
import {useSafeAreaInsets} from "react-native-safe-area-context";
import {ChatContext, ChatContextValue} from "stream-chat-expo";
import {StatusBar} from "expo-status-bar";
import {api} from "@/api";
import {
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuItemTitle,
  DropdownMenuRoot,
  DropdownMenuTrigger
} from "@/components/PopoverMenu";
import {useNavigation} from "@react-navigation/native";
import {Ionicons} from "@expo/vector-icons";
import {Channel, ChannelFilters, Event as GetStreamEvent} from "stream-chat";
import {router, useLocalSearchParams} from "expo-router";
import {useChatState} from "@/hooks/useChatState";
import {CollapsibleChannelList} from "@/components/chat/CollapsibleChannelList";
import {IconButton} from "@/components/IconButton";

const channelQueryOptions = {
  state: true,
  watch: true,
  limit: 200
};

const sort = {last_updated: -1} as const;


export function OperatorGroupsScreenComp() {
  const {backToStoreId} = useLocalSearchParams<{ backToStoreId: string }>();
  const safeArea = useSafeAreaInsets();
  const [userProfile] = api.user.getUserProfile.useSuspenseQuery(undefined, {
    staleTime: 1000 * 60 * 60 // 1 hour
  });
  const currentPersonId = userProfile.person.id!;

  const onAddGroup = useCallback(async () => {
    router.navigate({
      pathname: "/(signedin)/operator/groups/create-group",
    });
  }, [])

  const onAddDirectMessage = useCallback(async () => {
    router.navigate({
      pathname: "/(signedin)/operator/groups/create-direct-message",
    });
  }, [])

  const navigation = useNavigation();
  useLayoutEffect(() => {
    navigation.setOptions({
      headerLeft: () => <IconButton variant={"link"} colorScheme={"white"}
                                    accessibilityLabel={"Back"}
                                    icon={s => <Ionicons name={"chevron-back-outline"} size={24} color={"white"}/>}
                                    onPress={() => router.dismissTo({
                                      pathname: "/(signedin)/(drawer)/(tabs)/[storeId]",
                                      params: {
                                        storeId: backToStoreId
                                      }
                                    })} hitSlop={4}
                                    style={cn("pl-0")}/>,
      headerRight: () => {
        return <DropdownMenuRoot>
          <DropdownMenuTrigger>
            <Pressable hitSlop={8}>
              <Ionicons name={"add"} size={24} color={"white"}/>
            </Pressable>
          </DropdownMenuTrigger>
          <DropdownMenuContent>
            <DropdownMenuItem key="create-direct-message" onSelect={onAddDirectMessage}>
              <DropdownMenuItemTitle>Direct Message</DropdownMenuItemTitle>
            </DropdownMenuItem>
            <DropdownMenuItem key="create-group" onSelect={onAddGroup}>
              <DropdownMenuItemTitle>Create Group</DropdownMenuItemTitle>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenuRoot>;
      }
    })
  }, [onAddGroup, onAddDirectMessage]);

  const filters = useMemo(() => ({
    $or: [
      // Operator group channels
      {
        type: "operator",
        members: {$in: [currentPersonId]},
        team: "operators",
        archived: false
      },
      // Direct messages between operators
      {
        type: "direct",
        members: {$in: [currentPersonId]},
        team: "operators",
        archived: false
      }
    ]
  } satisfies ChannelFilters), [currentPersonId]);

  const setChatChannel = useChatState(s => s.setChannel);
  const goToChannel = useCallback((channel: Channel) => {
    setChatChannel(channel);

    router.navigate({
      pathname: "/(signedin)/operator/groups/chat/[cid]",
      params: {
        cid: channel.cid,
        ...(backToStoreId && { backToStoreId })
      }
    })
  }, [setChatChannel, backToStoreId]);

  const preventDefaultIfNotForThisChannel = useCallback((event: GetStreamEvent) => {
    if (event.channel_type === "operator" || event.channel_type === "direct") {
      return false;
    }
    return true;
  }, []);

  return <View style={[cn("flex-1 bg-white"), {paddingBottom: Math.max(safeArea.bottom, 16)}]}>
    <CollapsibleChannelList
            title="Operator Channels"
            filters={filters}
            options={channelQueryOptions}
            channelTypes={["operator", "direct"]}
            onAddGroup={onAddGroup}
            onAddDirectMessage={onAddDirectMessage}
            collapsible={false}
            showHeader={false}
            onSelect={goToChannel}
            additionalFlatListProps={{}} // Enable scrolling (default behavior)
    />
  </View>
}

export default function OperatorGroupsScreen() {
  const contextValue = useContext(ChatContext) as unknown as ChatContextValue;
  // if the chat client is not initialized yet, show loading screen
  if (!contextValue.client) {
    return <ScreenLoadingIndicator/>
  }

  return <Suspense fallback={<ScreenLoadingIndicator/>}>
    <StatusBar style={"light"}/>
    <OperatorGroupsScreenComp/>
  </Suspense>
}

export const ErrorBoundary = ScreenErrorBoundary;
