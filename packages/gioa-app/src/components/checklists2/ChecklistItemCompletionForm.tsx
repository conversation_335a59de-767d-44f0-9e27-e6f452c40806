import React, {useCallback, useLayoutEffect, useState} from 'react';
import {api} from "@/api";
import {useChecklistItemCompletion} from "@/hooks/useChecklistItemCompletion";
import {compact, find, flatMap, groupBy, isEmpty, map, mapValues, reduce, trim} from "lodash";
import {useFormAttachmentUpload} from "@/hooks/useFormAttachmentUpload";
import {AttachmentMediaType, FileAttachmentDto} from "../../../../api/src/fileAttachment.dto";
import {isCompletion, isItemComplete} from "@/components/checklists2/checklistInteractionUtil";
import {
  RequirementCompletionDto,
  RequirementCompletionsDto
} from "../../../../api/src/checklists/checklist/checklistDto";
import {getHumanReadableErrorMessage} from "@/components/ErrorAlert";
import {useAvoidKeyboard} from "@/hooks/useAvoidKeyboard";
import {getAttachmentIconAndLabel} from "@/fileAttachment.util";
import {cn} from "@/style.util";
import {colors} from "@/styles";
import {ExternalLink} from "@/components/ExternalLink";
import {imageUrl} from "@/images";
import {Text} from "@/components/Text";
import {Avatar} from "@/components/Avatar";
import {DateTime} from "luxon";
import {FormChecklistRequirementInput} from "@/components/checklists2/FormChecklistRequirementInput";
import {Button} from "@/components/Button";
import {useNavigation} from 'expo-router';
import {AttachmentValue} from "@/components/EditableAttachmentDropdown";
import {ItemRequirementDto} from "../../../../api/src/checklists/checklistTemplate/checklistTemplateDtos";
import {assertUnreachable} from "../../../../api/src/util";
import {SelectedMedia} from "@/onPickMediaFromLibrary";
import {SelectedDocument} from "@/onPickDocumentFromLibrary";
import {useForm} from "@tanstack/react-form";
import {zodValidator} from '@tanstack/zod-form-adapter'
import {View} from 'react-native';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import {Ionicons} from "@expo/vector-icons";


export interface ChecklistItemCompletionFormProps {
  itemId: string;
  storeId: string;
  checklistId: string;
  recurrenceId: Date | undefined;
  onCompleted: () => void;
  onUncompleted: () => void;
  onCompletionFailed: () => void;
  onUncompletionFailed: () => void;
}


interface FormRequirement {
  type: string;
  requirementId: string;
  attachment?: AttachmentValue;
  text?: string;
  numberInput?: string;

  booleanInput?: boolean;
  // for boolean inputs, yes or no branches may have requirements
  thenRequirements?: {
    [reqId: string]: FormRequirement
  };
}

function toFormRequirement(req: ItemRequirementDto, completions: RequirementCompletionsDto | undefined): FormRequirement[] {
  const getCompletionForRequirement = (reqId: string) => {
    if (!completions) return undefined;
    return completions[reqId];
  }
  const completion = getCompletionForRequirement(req.id);

  switch (req.type) {
    case "addImage":
      return [{
        type: req.type,
        requirementId: req.id,
        attachment: completion?.type === "addImage" ? completion.attachment : undefined
      }]
    case "writeComment":
      return [{
        type: req.type,
        requirementId: req.id,
        text: completion?.type === "writeComment" ? completion.text : undefined
      }]
    case "inputNumber":
      return [{
        type: req.type,
        requirementId: req.id,
        numberInput: completion?.type === "inputNumber" ? completion.numberInput?.toString() : undefined
      }]
    case "inputBoolean":
      const flattenedConditionalReqs = flatMap(req.conditionals, cond => {
        return flatMap(cond.thenRequirements, req => toFormRequirement(req, completions))
      });
      return [{
        type: req.type,
        requirementId: req.id,
        booleanInput: completion?.type === "inputBoolean" ? completion.booleanInput : undefined,
      },
        ...flattenedConditionalReqs
      ]
    default:
      assertUnreachable(req)
  }
}

async function fromFormRequirement(req: FormRequirement, uploadAttachment: (value: SelectedMedia | SelectedDocument | FileAttachmentDto) => Promise<FileAttachmentDto | undefined>): Promise<RequirementCompletionDto | undefined> {
  // console.log("fromFormRequirement", req);
  switch (req.type) {
    case "addImage": {
      const attachment = req.attachment;
      if (!attachment) return;

      // if attachment is FileAttachmentDto, then nothing has changed
      if ("id" in attachment) {
        return {
          type: req.type,
          requirementId: req.requirementId,
          attachment: attachment
        };
      }

      // If attachment is SelectedMedia | SelectedDocument, then convert it to FileAttachmentDto
      const fileAttachmentDto = await uploadAttachment(attachment);
      if (!fileAttachmentDto) throw new Error("Failed to upload attachment");

      return {
        type: req.type,
        requirementId: req.requirementId,
        attachment: fileAttachmentDto
      }
    }
    case "writeComment":
      if (req.text === undefined) return;

      return {
        type: req.type,
        requirementId: req.requirementId,
        text: req.text
      }
    case "inputNumber":
      const parsedNumberInput = req.numberInput !== undefined ? parseFloat(trim(req.numberInput, " $%")) : undefined;
      if (parsedNumberInput === undefined) return;

      return {
        type: req.type,
        requirementId: req.requirementId,
        numberInput: parsedNumberInput
      }
    case "inputBoolean":
      if (req.booleanInput === undefined) return;

      return {
        type: req.type,
        requirementId: req.requirementId,
        booleanInput: req.booleanInput,
      }
    default:
      throw new Error("Unknown requirement type");
  }
}

export const ChecklistItemCompletionForm: React.FC<ChecklistItemCompletionFormProps> = ({
                                                                                          itemId,
                                                                                          storeId,
                                                                                          onCompleted,
                                                                                          onCompletionFailed,
                                                                                          onUncompleted,
                                                                                          onUncompletionFailed,
                                                                                          checklistId,
                                                                                          recurrenceId
                                                                                        }) => {
  const [{checklist, timezone}] = api.checklist2.getChecklist2.useSuspenseQuery({
    storeId: storeId,
    checklistId: checklistId,
    recurrenceId
  });
  const [store] = api.user.getStore.useSuspenseQuery({storeId}, {staleTime: 1000 * 60 * 60});
  const people = store.employees;
  const [user] = api.user.getUserProfile.useSuspenseQuery(undefined, {staleTime: 1000 * 60 * 60});

  const {completeChecklistItem, isPending} = useChecklistItemCompletion({
    onCompleted,
    onCompletionFailed,
    onUncompleted,
    onUncompletionFailed
  });
  const item = find(checklist.items, i => i.id === itemId)!;
  const navigation = useNavigation();
  const getPresignedPost = api.checklist2.getPresignedPostForChecklistItem.useMutation();
  const {toDto: uploadAttachment, status: uploadAttachmentStatus} = useFormAttachmentUpload(({
                                                                                               contentType,
                                                                                               mediaType
                                                                                             }) => {
    return getPresignedPost.mutateAsync({
      storeId: storeId,
      contentType: contentType,
      mediaType: mediaType as AttachmentMediaType
    });
  });

  const completion = item.latestInteraction && isCompletion(item.latestInteraction) ? item.latestInteraction : undefined;
  const completedBy = completion ? find(people, p => p.id === completion.personId) : undefined;
  const formRequirements = reduce(item.requirements, (acc, req) => {
    const formReqs = toFormRequirement(req, completion?.requirementCompletions);
    return {
      ...acc,
      ...mapValues(groupBy(formReqs, "requirementId"), reqs => reqs[0])
    }
  }, {} as { [requirementId: string]: FormRequirement });

  const form = useForm({
    defaultValues: {
      requirements: formRequirements
    },
    validatorAdapter: zodValidator(),
    onSubmit: async (event) => {
      const save = async () => {
        // upload the attachments for any requirements that have attachments
        let reqsWithAttachments: (RequirementCompletionDto | undefined)[] = [];
        try {
          reqsWithAttachments = await Promise.all(map(event.value.requirements, async (req, reqId) => {
            if (!req) {
              return undefined;
            }

            return fromFormRequirement(req, uploadAttachment)
          }));
        } catch (e) {
          alert("Error completing item: " + getHumanReadableErrorMessage(e));
          return;
        }

        const requirements = reduce(compact(reqsWithAttachments), (acc, req) => {
          return {
            ...acc,
            [req.requirementId]: req
          }
        }, {} as RequirementCompletionsDto);

        completeChecklistItem({
          checklistId: checklistId,
          recurrenceId: recurrenceId,
          item: item,
          requirements: requirements,
          checklistVersion: checklist.version
        })
      }

      save();
    }
  })

  useLayoutEffect(() => {
    navigation.setOptions({
      headerTitle: checklist.title,
    })
  }, [])

  const safeArea = useSafeAreaInsets();
  useAvoidKeyboard();

  const apiUtil = api.useUtils();
  const [refreshing, setRefreshing] = useState(false);

  const onRefresh = useCallback(() => {
    setRefreshing(true);
    apiUtil.checklist2.getChecklist2.invalidate({
      storeId: storeId,
    }).then(() => {
      setRefreshing(false);
    });
  }, [storeId]);

  const renderAttachment = (attachment: FileAttachmentDto) => {
    const {icon, label} = getAttachmentIconAndLabel(attachment);
    return <View style={cn("flex flex-row items-center gap-3")} key={attachment.id}>
      <View>
        {typeof icon === "string" ?
          <Ionicons name={icon as any} size={20} color={colors.gray[600]}/>
          : icon}
      </View>
      {attachment.url ?
        <ExternalLink href={attachment.mediaType === "image" ? imageUrl(attachment.url, {width: 1280}) : attachment.url}
                      style={cn("flex-1")}>
          {attachment.filename}
        </ExternalLink> : null}
    </View>;
  };

  const isItemCompleted = isItemComplete(item);

  return <View>
    {!isEmpty(item.attachments) ?
      <View style={cn("bg-white py-4")}>
        <View style={cn("mt-4")}>
          {map(item.attachments, attachment => {
            return renderAttachment(attachment);
          })}
        </View>
      </View> : null}

    {map(item.requirements, req => {
      // @ts-ignore "Type instantiation is excessively deep and possibly infinite."
      return <FormChecklistRequirementInput key={req.id} requirement={req}
                                            form={form} isItemCompleted={isItemCompleted}/>
    })}

    {!isEmpty(item.instructions) ?
      <View style={cn("bg-white py-4 mb-2")}>
        <Text bold style={cn("mb-1")}>
          Instructions
        </Text>
        {map(item.instructions, instruction => {
          return <View key={instruction.id} style={cn("mb-3")}>
            {instruction.text ? <Text>{instruction.text}</Text> : null}
            {instruction.attachment?.url ? renderAttachment(instruction.attachment) : null}
          </View>
        })}
      </View> : null}
    {item.canComplete ?
      <View
        style={[cn("bg-white px-4 pb-6 pt-6")]}>
        <Button onPress={form.handleSubmit}
                isLoading={isPending || getPresignedPost.isPending || uploadAttachmentStatus.isLoading}
                leftIcon={(s) => <Ionicons name={"checkmark-outline"} size={24} color={"white"}/>}>
          Mark Complete
        </Button>
      </View> : null}

    {isItemCompleted && completion ?
      <View style={cn("bg-white py-4")}>
        <Text bold style={cn("mb-1")}>
          Completed by
        </Text>
        <View style={cn("flex flex-row items-center gap-3")}>
          <Avatar src={completedBy?.profileImageUrl}/>
          <View>
            <Text>
              {completedBy?.firstName} {completedBy?.lastName}
            </Text>
            <Text muted size={"sm"}>
              On {completion.interactedAt ? DateTime.fromJSDate(completion.interactedAt).toLocaleString(DateTime.DATETIME_FULL) : null}
            </Text>
          </View>
        </View>
      </View> : null}
  </View>
}
