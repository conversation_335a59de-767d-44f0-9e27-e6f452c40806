import React, {Suspense, useEffect, useState} from 'react';
import {cn} from "@/style.util";
import {colors} from "@/styles";
import {Text} from "@/components/Text";
import {Pressable, View} from 'react-native';
import {FontAwesome6, Ionicons} from "@expo/vector-icons";
import {filter, isEmpty, map, some} from "lodash";
import {LiveChecklistItemDto} from "../../../../api/src/checklists/checklist/checklistDto";
import {useChecklistItemCompletion} from "@/hooks/useChecklistItemCompletion";
import {DomainEventId} from "../../../../api/src/eventSourcing/domainEvent";
import {Divider} from "@/components/Divider";
import {ChecklistItemCompletionForm} from "@/components/checklists2/ChecklistItemCompletionForm";
import {ScreenLoadingIndicator} from "@/components/ScreenLoadingIndicator";
import {CollapsibleHeaderIcon} from "@/components/CollapsibleHeaderIcon";
import * as <PERSON><PERSON> from 'moti';
import Toast from 'react-native-root-toast';
import {isCompleteNonCompliant} from "@/components/checklists2/checklistInteractionUtil";


export interface ChecklistItemAccordionContentProps {
  storeId: string;
  checklistId: string;
  recurrenceId: Date | undefined;
  checklistVersion: DomainEventId;
  item: LiveChecklistItemDto;
  isExpanded: boolean;
  onToggleExpand: (itemId: string) => void;
  onCollapse: (itemId: string) => void;
}

export const ChecklistItemAccordionItem: React.FC<ChecklistItemAccordionContentProps> = ({
                                                                                           storeId,
                                                                                           item, onCollapse,
                                                                                           checklistId,
                                                                                           checklistVersion,
                                                                                           recurrenceId,
                                                                                           isExpanded,
                                                                                           onToggleExpand
                                                                                         }) => {

  const completionCallbacks = {
    onCompleted: () => {
      setIsComplete(true);
      Toast.show("Checklist item completed", {
        position: Toast.positions.TOP
      });
      onCollapse(item.id);
    },
    onUncompleted: () => {
      setIsComplete(false);
      Toast.show("Checklist item un-completed", {
        position: Toast.positions.TOP
      });
    },
    onCompletionFailed: () => {
      setIsComplete(false);
    },
    onUncompletionFailed: () => {
      setIsComplete(true);
    },
    onCompletedOptimistic: () => {
      setIsComplete(true);
      onCollapse(item.id);
    },
    onUncompletedOptimistic: () => {
      setIsComplete(false);
    }
  };
  const {toggleChecklistItem} = useChecklistItemCompletion(completionCallbacks);

  const onItemPress = () => {
    onToggleExpand(item.id);
  }

  const [isComplete, setIsComplete] = useState<boolean>(item.isComplete);
  useEffect(() => {
    setIsComplete(item.isComplete);
  }, [item.isComplete])

  const [_isCompleteNonCompliant, setIsCompleteNonCompliant] = useState<boolean>(false);
  useEffect(() => {
    setIsCompleteNonCompliant(isCompleteNonCompliant(item))
  }, [item.isComplete, item.requirements])

  const completeIfPossible = () => {
    // If the item has no requriements, we can complete it.
    if (isEmpty(item.requirements) || isComplete) {
      toggleChecklistItem({
        checklistId: checklistId,
        recurrenceId: recurrenceId,
        item: item,
        requirements: {},
        checklistVersion: checklistVersion
      })
    } else {
      // else expand the item
      onToggleExpand(item.id);
    }
  }

  return <View style={cn("mx-4")}>
    <View style={cn("flex flex-row items-stretch bg-white", {
      "rounded-lg": !isExpanded,
      "rounded-t-lg": isExpanded
    })}>
      <Pressable style={[cn("pl-4 pr-4 py-4 flex items-center justify-center")]}
                 onPress={completeIfPossible}>
        <FontAwesome6 name={isComplete ? "check-circle" : "circle"} size={24}
                      color={isComplete ? _isCompleteNonCompliant ? colors.red[500] : colors.green[500] : colors.gray[500]}/>
      </Pressable>
      <Pressable onPress={onItemPress}
                 style={cn("flex flex-row items-center gap-3 flex-1 pr-3 py-2")}>
        <View style={cn("flex-1")}>
          <Text semibold>
            {item.title}
          </Text>
          {item.description ?
            <Text size={"sm"}>
              {item.description}
            </Text> : null}
        </View>

        <CollapsibleHeaderIcon icon={<Ionicons name={"chevron-down-outline"} size={24} color={colors.gray[500]}/>}
                               isCollapsed={!isExpanded}/>
      </Pressable>
    </View>
    <Moti.AnimatePresence>
      {isExpanded ? <Moti.View style={cn("bg-white rounded-b-lg px-4")}
                               from={{
                                 opacity: 0,
                               }}
                               animate={{
                                 opacity: 1,
                               }} exit={{
        opacity: 0,
      }} exitTransition={{
        type: 'timing',
        duration: 0,
      }}>
        <Divider size={"xs"} style={cn("mb-4")}/>
        <Suspense fallback={<ScreenLoadingIndicator/>}>
          <ChecklistItemCompletionForm itemId={item.id} storeId={storeId}
                                       {...completionCallbacks}
                                       checklistId={checklistId} recurrenceId={recurrenceId}/>
        </Suspense>
      </Moti.View> : null}
    </Moti.AnimatePresence>
  </View>
}
