import React, {useCallback, useRef, useEffect} from 'react';
import {cn} from "@/style.util";
import {Text} from "@/components/Text";
import {CollapsibleHeaderIcon} from "@/components/CollapsibleHeaderIcon";
import {colors} from "@/styles";
import {ChannelList} from "@/components/chat/ChannelList";
import {includes} from "lodash";
import {Pressable, View} from "react-native";
import {Ionicons} from "@expo/vector-icons";
import MaterialCommunityIcons from '@expo/vector-icons/MaterialCommunityIcons';
import {
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuItemTitle,
  DropdownMenuRoot,
  DropdownMenuTrigger
} from "@/components/PopoverMenu";
import {Channel, ChannelFilters, ChannelOptions, Event as GetStreamEvent} from "stream-chat";
import {useDisclosure} from "@/hooks/useDisclosure";
import {Button} from "@/components/Button";
import {useChatState} from "@/hooks/useChatState";
import {router} from 'expo-router';
import {api} from "@/api";
import {ChannelPreviewMessenger} from 'stream-chat-react-native-core';

export interface CollapsibleChannelListProps {
  title: string,
  filters: ChannelFilters,
  channelTypes: string[]
  storeId?: string; // Optional - only needed for default navigation, not for pinning
  onAddGroup: () => void;
  onAddDirectMessage: () => void;
  options: ChannelOptions;
  collapsible?: boolean; // Optional prop to control collapsible behavior
  showHeader?: boolean; // Optional prop to show/hide the header
  onSelect?: (channel: Channel) => void; // Optional custom channel selection handler
  additionalFlatListProps?: object; // Optional FlatList props
}

const sort = {last_updated: -1} as const;


export const CollapsibleChannelList: React.FC<CollapsibleChannelListProps> = ({
                                                                                title,
                                                                                filters,
                                                                                channelTypes, storeId,
                                                                                onAddGroup, options,
                                                                                onAddDirectMessage,
                                                                                collapsible: isCollapsible = true,
                                                                                showHeader = true,
                                                                                onSelect,
                                                                                additionalFlatListProps,
                                                                              }) => {
  const collapsible = useDisclosure(isCollapsible);
  const chatState = useChatState();

  // Fetch pinned channels for this user (across all stores)
  const {data: pinnedChannelsData, isLoading, isFetching, refetch: refetchPinnedChannels} = api.chat.getPinnedChannels.useQuery({}, {
    staleTime: 1000 * 30, // Keep data fresh for 30 seconds (shorter for better refresh behavior)
    gcTime: 1000 * 60 * 10, // Keep in cache for 10 minutes
    refetchOnWindowFocus: true, // Refetch when window regains focus
    refetchOnMount: true, // Refetch when component mounts
  });
  const pinnedChannelIds = pinnedChannelsData?.pinnedChannelIds ?? [];

  // Use a ref to always get the latest pinnedChannelIds
  const pinnedChannelIdsRef = useRef(pinnedChannelIds);
  pinnedChannelIdsRef.current = pinnedChannelIds;

  const goToChannel = (channel: Channel) => {
    if (onSelect) {
      // Use custom onSelect handler if provided
      onSelect(channel);
    } else if (storeId) {
      // Default behavior for store groups (only if storeId is provided)
      chatState.setChannel(channel);

      router.navigate({
        pathname: "/(signedin)/store/[storeId]/chat/[cid]",
        params: {
          storeId: storeId,
          cid: channel.cid
        }
      })
    }
  }

  // Filter function to sort pinned channels first, then alphabetically
  const channelRenderFilterFn = useCallback((channels: Channel[]) => {
    return channels.sort((a, b) => {
      const aIsPinned = includes(pinnedChannelIds, a.id);
      const bIsPinned = includes(pinnedChannelIds, b.id);

      // Primary sort: pinned status
      if (aIsPinned !== bIsPinned) {
        return aIsPinned ? -1 : 1; // Pinned channels come first
      }

      // Secondary sort: alphabetical by channel name
      const aName = a.data?.name || '';
      const bName = b.data?.name || '';
      return aName.localeCompare(bName, undefined, { sensitivity: 'base' });
    });
  }, [pinnedChannelIds]);

  // Pin/unpin mutations
  const utils = api.useUtils();
  const pinChannelMutation = api.chat.pinChannel.useMutation({
    onSuccess: async () => {
      await utils.chat.getPinnedChannels.invalidate({});
    }
  });
  const unpinChannelMutation = api.chat.unpinChannel.useMutation({
    onSuccess: async () => {
      await utils.chat.getPinnedChannels.invalidate({});
    }
  });

  // Custom refresh handler that also refetches pinned channels
  const handleRefresh = useCallback(async () => {
    await refetchPinnedChannels();
  }, [refetchPinnedChannels]);

  const handlePinToggle = useCallback(async (channel: Channel, isPinned: boolean) => {
    try {
      if (isPinned) {
        await unpinChannelMutation.mutateAsync({channelId: channel.id!});
      } else {
        await pinChannelMutation.mutateAsync({channelId: channel.id!});
      }
    } catch (error) {
      console.error(`[FRONTEND] Failed to toggle pin for channel ${channel.id}:`, error);
    }
  }, [pinChannelMutation, unpinChannelMutation]);

  // Custom Preview component with pin functionality
  const CustomPreview = useCallback((props: any) => {
    const {channel} = props;

    // Safety check
    if (!channel || !channel.id) {
      return <ChannelPreviewMessenger {...props} />;
    }

    const currentPinnedChannelIds = pinnedChannelIdsRef.current;
    const isPinned = includes(currentPinnedChannelIds, channel.id);

    const handlePinPress = (e?: any) => {
      e?.stopPropagation();
      handlePinToggle(channel, isPinned);
    };

    return (
      <View style={cn('relative')}>
        <DropdownMenuRoot>
          <DropdownMenuTrigger {...({action: "longPress"} as any)}>
            <ChannelPreviewMessenger {...props} />
          </DropdownMenuTrigger>
          <DropdownMenuContent>
            <DropdownMenuItem key={isPinned ? 'unpin' : 'pin'} onSelect={handlePinPress}>
              <DropdownMenuItemTitle>
                {isPinned ? 'Unpin' : 'Pin'}
              </DropdownMenuItemTitle>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenuRoot>

        {/* Pin indicator overlapping bottom right of profile picture - visual only */}
        {isPinned && (
          <View
            style={cn('absolute bottom-1 left-8 w-6 h-6 bg-yellow-400 rounded-full items-center justify-center border-2 border-white z-10')}
          >
            <MaterialCommunityIcons name="pin" size={14} color="white" />
          </View>
        )}
      </View>
    );
  }, [handlePinToggle]);

  const renderEmptyState = (title: string) => {
    const isGroupsSection = title === "Groups";
    const onPress = isGroupsSection ? onAddGroup : onAddDirectMessage;
    const buttonText = isGroupsSection ? "Add Group" : "Add Direct Message";
    const emptyText = isGroupsSection
            ? "No groups yet. Create your first group to start collaborating with your team."
            : "No direct messages yet. Start a conversation with a team member.";

    return <View style={cn("py-8 px-4 items-center")}>
      <View style={cn("bg-gray-50 rounded-full p-4 mb-4")}>
        <Ionicons
                name={isGroupsSection ? "people-outline" : "chatbubble-outline"}
                size={32}
                color={colors.gray[400]}
        />
      </View>
      <Text muted center style={cn("mb-4 px-4")}>
        {emptyText}
      </Text>
      <Button
              onPress={onPress}
              colorScheme="primary"
              size="sm">
        {buttonText}
      </Button>
    </View>;
  };

  const preventDefaultIfNotForThisChannel = useCallback((event: GetStreamEvent) => {
    if (includes(channelTypes, event.channel_type)) {
      return false;
    }
    return true;
  }, [channelTypes]);

  return <View style={cn("flex-1")}>
    {showHeader && (
      <Pressable
              onPress={isCollapsible ? collapsible.onToggle : undefined}
              style={cn("px-4 py-2 bg-slate-100 flex-row items-center justify-between")}>
        <Text semibold>{title}</Text>
        {isCollapsible && (
          <CollapsibleHeaderIcon
                  isCollapsed={!collapsible.isOpen}
                  icon={<Ionicons name="chevron-down-outline" size={20} color={colors.gray[500]}/>}/>
        )}
      </Pressable>
    )}

    {(isCollapsible ? collapsible.isOpen : true) ? <ChannelList
            preventDefaultNewMessage={preventDefaultIfNotForThisChannel}
            preventDefaultNewMessageNotification={preventDefaultIfNotForThisChannel}
            preventDefaultChannelVisible={preventDefaultIfNotForThisChannel}
            preventDefaultAddedToChannel={preventDefaultIfNotForThisChannel}
            filters={filters}
            options={options}
            sort={sort}
            additionalFlatListProps={{
              ...(additionalFlatListProps ?? (isCollapsible ? {scrollEnabled: false} : {})),
              onRefresh: () => {
                // Call original onRefresh if it exists
                const originalOnRefresh = (additionalFlatListProps as any)?.onRefresh;
                if (originalOnRefresh) {
                  originalOnRefresh();
                }
                // Also refresh pinned channels
                handleRefresh();
              }
            }}
            onSelect={goToChannel}
            EmptyStateIndicator={() => renderEmptyState(title)}
            channelRenderFilterFn={channelRenderFilterFn}
            Preview={CustomPreview}
    /> : null}
  </View>;
}
