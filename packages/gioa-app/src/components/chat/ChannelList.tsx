import React, {useEffect, useMemo, useRef, useState} from 'react';
import type {FlatList} from 'react-native-gesture-handler';
import {Channel, ChannelFilters, ChannelOptions, ChannelSort, Event} from 'stream-chat';
import type {ChannelListEventListenerOptions} from 'stream-chat-react-native-core';
import {
  ChannelListFooterLoadingIndicator,
  ChannelListHeaderErrorIndicator,
  ChannelListHeaderNetworkDownIndicator,
  ChannelListLoadingIndicator,
  ChannelListMessenger,
  ChannelListMessengerProps,
  ChannelPreviewMessenger,
  ChannelsContextValue,
  ChannelsProvider,
  EmptyStateIndicator as EmptyStateIndicatorDefault,
  LoadingErrorIndicator as LoadingErrorIndicatorDefault,
  Skeleton as SkeletonDefault,
  useChannelUpdated,
  useChatContext,
  useCreateChannelsContext,
  usePaginatedChannels
} from 'stream-chat-react-native-core';

export type ChannelListProps = Partial<
        Pick<
                ChannelsContextValue,
                | 'additionalFlatListProps'
                | 'EmptyStateIndicator'
                | 'FooterLoadingIndicator'
                | 'HeaderErrorIndicator'
                | 'HeaderNetworkDownIndicator'
                | 'LoadingErrorIndicator'
                | 'LoadingIndicator'
                | 'Preview'
                | 'setFlatListRef'
                | 'ListHeaderComponent'
                | 'onSelect'
                | 'PreviewAvatar'
                | 'PreviewMessage'
                | 'PreviewMutedStatus'
                | 'PreviewStatus'
                | 'PreviewTitle'
                | 'PreviewUnreadCount'
                | 'loadMoreThreshold'
                | 'Skeleton'
                | 'maxUnreadCount'
                | 'numberOfSkeletons'
        >
> & {
  /** Optional function to filter channels prior to rendering the list. Do not use any complex logic that would delay the loading of the ChannelList. We recommend using a pure function with array methods like filter/sort/reduce. */
  channelRenderFilterFn?: (channels: Array<Channel>) => Array<Channel>;
  /**
   * Object containing channel query filters
   *
   * @see See [Channel query documentation](https://getstream.io/chat/docs/query_channels) for a list of available filter fields
   *
   * @overrideType object
   * */
  filters?: ChannelFilters;
  /**
   * Custom UI component to display the list of channels
   *
   * Default: [ChannelListMessenger](https://getstream.io/chat/docs/sdk/reactnative/ui-components/channel-list-messenger/)
   */
  List?: React.ComponentType<ChannelListMessengerProps>;
  /**
   * If set to true, channels won't dynamically sort by most recent message, defaults to false
   */
  lockChannelOrder?: boolean;
  /**
   * Function that overrides default behavior when a user gets added to a channel
   *
   * @param setChannels Setter for internal state property - `channels`. It's created from useState() hook.
   * @param event An [Event Object](https://getstream.io/chat/docs/event_object) corresponding to `notification.added_to_channel` event
   * @param filters Channel filters
   * @param sort Channel sort options
   *
   * @overrideType Function
   * */
  onAddedToChannel?: (
          setChannels: React.Dispatch<React.SetStateAction<Channel[]>>,
          event: Event,
          options?: ChannelListEventListenerOptions,
  ) => void;
  /**
   * Function that overrides default behavior when a channel gets deleted. In absence of this prop, the channel will be removed from the list.
   *
   * @param setChannels Setter for internal state property - `channels`. It's created from useState() hook.
   * @param event An [Event object](https://getstream.io/chat/docs/event_object) corresponding to `channel.deleted` event
   *
   * @overrideType Function
   * */
  onChannelDeleted?: (
          setChannels: React.Dispatch<React.SetStateAction<Channel[]>>,
          event: Event,
  ) => void;
  /**
   * Function that overrides default behavior when a channel gets hidden. In absence of this prop, the channel will be removed from the list.
   *
   * @param setChannels Setter for internal state property - `channels`. It's created from useState() hook.
   * @param event An [Event object](https://getstream.io/chat/docs/event_object) corresponding to `channel.hidden` event
   *
   * @overrideType Function
   * */
  onChannelHidden?: (
          setChannels: React.Dispatch<React.SetStateAction<Channel[]>>,
          event: Event,
  ) => void;
  /**
   * Function that overrides default behavior when a channel member.updated event is triggered
   * @param lockChannelOrder If set to true, channels won't dynamically sort by most recent message, defaults to false
   * @param setChannels Setter for internal state property - `channels`. It's created from useState() hook.
   * @param event An [Event object](https://getstream.io/chat/docs/event_object) corresponding to `member.updated` event
   * @param filters Channel filters
   * @param sort Channel sort options
   * @overrideType Function
   */
  onChannelMemberUpdated?: (
          lockChannelOrder: boolean,
          setChannels: React.Dispatch<React.SetStateAction<Channel[]>>,
          event: Event,
          options?: ChannelListEventListenerOptions,
  ) => void;
  /**
   * Function to customize behavior when a channel gets truncated
   *
   * @param setChannels Setter for internal state property - `channels`. It's created from useState() hook.
   * @param event [Event object](https://getstream.io/chat/docs/event_object) corresponding to `channel.truncated` event
   *
   * @overrideType Function
   * */
  onChannelTruncated?: (
          setChannels: React.Dispatch<React.SetStateAction<Channel[]>>,
          event: Event,
  ) => void;
  /**
   * Function that overrides default behavior when a channel gets updated
   *
   * @param setChannels Setter for internal state property - `channels`. It's created from useState() hook.
   * @param event An [Event object](https://getstream.io/chat/docs/event_object) corresponding to `channel.updated` event
   *
   * @overrideType Function
   * */
  onChannelUpdated?: (
          setChannels: React.Dispatch<React.SetStateAction<Channel[]>>,
          event: Event,
  ) => void;
  /**
   * Function that overrides default behavior when a channel gets visible. In absence of this prop, the channel will be added to the list.
   *
   * @param setChannels Setter for internal state property - `channels`. It's created from useState() hook.
   * @param event An [Event object](https://getstream.io/chat/docs/event_object) corresponding to `channel.visible` event
   *
   * @overrideType Function
   * */
  onChannelVisible?: (
          setChannels: React.Dispatch<React.SetStateAction<Channel[]>>,
          event: Event,
  ) => void;

  preventDefaultChannelVisible?: (event: Event) => boolean;
  preventDefaultNewMessage?: (event: Event) => boolean;
  preventDefaultNewMessageNotification?: (event: Event) => boolean;
  preventDefaultAddedToChannel?: (event: Event) => boolean;

  /**
   * Function that overrides default behavior when a user gets removed from a channel
   *
   * @param setChannels Setter for internal state property - `channels`. It's created from useState() hook.
   * @param event An [Event object](https://getstream.io/chat/docs/event_object) corresponding to `notification.removed_from_channel` event
   *
   * @overrideType Function
   * */
  onRemovedFromChannel?: (
          setChannels: React.Dispatch<React.SetStateAction<Channel[]>>,
          event: Event,
  ) => void;
  /**
   * Object containing channel query options
   * @see See [Channel query documentation](https://getstream.io/chat/docs/query_channels) for a list of available option fields
   * */
  options?: ChannelOptions;
  /**
   * Object containing channel sort parameters
   * @see See [Channel query documentation](https://getstream.io/chat/docs/query_channels) for a list of available sorting fields
   * */
  sort?: ChannelSort;
};

const DEFAULT_FILTERS = {};
const DEFAULT_OPTIONS = {};
const DEFAULT_SORT = {};

/**
 * This component fetches a list of channels, allowing you to select the channel you want to open.
 * The ChannelList doesn't provide any UI for the underlying React Native FlatList. UI is determined by the `List` component which is
 * provided to the ChannelList component as a prop. By default, the ChannelListMessenger component is used as the list UI.
 *
 * AJW 7/17/25 I copied over this component from GetStream's code so that I could modify it to include the
 * preventDefaultNewMessage and preventDefaultNewMessageNotification props. The issue was that when any new message came in, the channel that the message belonged to was getting added and put at the top of ALL ChannelLists. So if a direct message came it, GetStream was adding it to the top of the groups (and direct messages) channel lists. And vice versa. This is due to the blurb they have in their docs here: https://getstream.io/chat/docs/sdk/react-native/core-components/channel-list/
 *
 * Quote: "When receiving channel information from channel events, the filters are not respected; the reason for this is that channel filters can get very complex, and implementing that filtering logic that supports all of the custom filter would be very hard to do. Implementing this on the backend side isn’t an option as it is inefficient and has to cater to different filters. So, to help you with it, you will have to override the notification.message_new event using the onNewMessageNotification and message.new event handlers using the onNewMessage prop of the ChannelList component."
 *
 * However I found that their suggestion of implementing onNewMessageNotification and onNewMessage didn't allow me to passthrough to the default behavior if I wanted to. So I added the preventDefault props instead. Now when a message comes through, it checks the channel type on the event and passes through to the default behavior if it matches the channel list's type.
 *
 * @example ./ChannelList.md
 */
export const ChannelList = (props: ChannelListProps) => {
  const {
    additionalFlatListProps = {},
    channelRenderFilterFn,
    EmptyStateIndicator = EmptyStateIndicatorDefault,
    filters = DEFAULT_FILTERS,
    FooterLoadingIndicator = ChannelListFooterLoadingIndicator,
    HeaderErrorIndicator = ChannelListHeaderErrorIndicator,
    HeaderNetworkDownIndicator = ChannelListHeaderNetworkDownIndicator,
    List = ChannelListMessenger,
    ListHeaderComponent,
    LoadingErrorIndicator = LoadingErrorIndicatorDefault,
    LoadingIndicator = ChannelListLoadingIndicator,
    // https://stackoverflow.com/a/60666252/10826415
    loadMoreThreshold = 0.1,
    lockChannelOrder = false,
    maxUnreadCount = 255,
    numberOfSkeletons = 6,
    onAddedToChannel,
    onChannelDeleted,
    onChannelHidden,
    onChannelMemberUpdated,
    onChannelTruncated,
    onChannelUpdated,
    onChannelVisible,
    onRemovedFromChannel,
    onSelect,
    options = DEFAULT_OPTIONS,
    Preview = ChannelPreviewMessenger,
    PreviewAvatar,
    PreviewMessage,
    PreviewMutedStatus,
    PreviewStatus,
    PreviewTitle,
    PreviewUnreadCount,
    setFlatListRef,
    Skeleton = SkeletonDefault,
    sort = DEFAULT_SORT,
    preventDefaultNewMessage,
    preventDefaultNewMessageNotification,
          preventDefaultChannelVisible,
          preventDefaultAddedToChannel,
  } = props;

  const [forceUpdate, setForceUpdate] = useState(0);
  const {client, enableOfflineSupport} = useChatContext();
  const channelManager = useMemo(() => client.createChannelManager({}), [client]);

  /**
   * This hook sets the event handler overrides in the channelManager internally
   * whenever they change. We do this to avoid recreating the channelManager instance
   * every time these change, as we want to keep it as static as possible.
   * This protects us from something like defining the overrides as inline functions
   * causing the manager instance to be recreated over and over again.
   */
  useEffect(() => {
    const origNewMessageHandler = (channelManager as any).newMessageHandler;
    const origNewMessageNotificationHandler = (channelManager as any).notificationNewMessageHandler;
    const origChannelVisibleHandler = (channelManager as any).channelVisibleHandler;
    const origAddedToChannelHandler = (channelManager as any).notificationAddedToChannelHandler;

    channelManager.setEventHandlerOverrides({
      channelDeletedHandler: onChannelDeleted,
      channelHiddenHandler: onChannelHidden,
      channelTruncatedHandler: onChannelTruncated,
      channelVisibleHandler: (setChannels, event) => {
        if (preventDefaultChannelVisible?.(event)) {
          return;
        }
        origChannelVisibleHandler(event);
      },
      memberUpdatedHandler: onChannelMemberUpdated
              ? (setChannels, event) =>
                      onChannelMemberUpdated(lockChannelOrder, setChannels, event, {filters, sort})
              : undefined,
      newMessageHandler: (setChannels, event) => {
        if (preventDefaultNewMessage?.(event)) {
          return;
        }
        origNewMessageHandler(event);
      },
      notificationAddedToChannelHandler: (setChannels, event) => {
        if (preventDefaultAddedToChannel?.(event)) {
          return;
        }
        origAddedToChannelHandler(event);
      },
      notificationNewMessageHandler: (setChannels, event) => {
        if (preventDefaultNewMessageNotification?.(event)) {
          return;
        }
        origNewMessageNotificationHandler(event);
      },
      notificationRemovedFromChannelHandler: onRemovedFromChannel,
    });
  }, [
    channelManager,
    filters,
    lockChannelOrder,
    onAddedToChannel,
    onChannelDeleted,
    onChannelHidden,
    onChannelMemberUpdated,
    onChannelTruncated,
    onChannelVisible,
    onRemovedFromChannel,
    sort,
  ]);

  useEffect(() => {
    channelManager.setOptions({abortInFlightQuery: false, lockChannelOrder});
  }, [channelManager, lockChannelOrder]);

  useEffect(() => {
    channelManager.registerSubscriptions();

    return () => {
      channelManager.unregisterSubscriptions();
    };
  }, [channelManager]);

  const {
    channelListInitialized,
    channels,
    error,
    hasNextPage,
    loadingChannels,
    loadingNextPage,
    loadNextPage,
    refreshing,
    refreshList,
    reloadList,
  } = usePaginatedChannels({
    channelManager,
    enableOfflineSupport,
    filters,
    options,
    setForceUpdate,
    sort,
  });

  useChannelUpdated({
    onChannelUpdated,
    setChannels: channelManager.setChannels,
  });

  const channelsContext = useCreateChannelsContext({
    additionalFlatListProps,
    channelListInitialized,
    channels: channelRenderFilterFn ? channelRenderFilterFn(channels ?? []) : channels,
    EmptyStateIndicator,
    error,
    FooterLoadingIndicator,
    forceUpdate,
    hasNextPage,
    HeaderErrorIndicator,
    HeaderNetworkDownIndicator,
    ListHeaderComponent,
    loadingChannels,
    LoadingErrorIndicator,
    LoadingIndicator,
    loadingNextPage,
    loadMoreThreshold,
    loadNextPage,
    maxUnreadCount,
    numberOfSkeletons,
    onSelect,
    Preview,
    PreviewAvatar,
    PreviewMessage,
    PreviewMutedStatus,
    PreviewStatus,
    PreviewTitle,
    PreviewUnreadCount,
    refreshing,
    refreshList,
    reloadList,
    setFlatListRef: (ref: FlatList<Channel> | null) => {
      if (setFlatListRef) {
        setFlatListRef(ref);
      }
    },
    Skeleton,
  });

  const interval = useRef<NodeJS.Timeout | null>(null);

  // This is sort of a hack. When a public group is created, it does not in realtime show up in everyone's channel list. There doesn't seem to be any way in the getstream API to make that happen, as most things are triggered by the members list, and a public group doesn't have a members list. So we just refresh the list every 15 minutes.
  useEffect(() => {
    interval.current = setInterval(() => {
      refreshList();
    }, 1000 * 60 * 15);

    return () => {
      if (interval.current) {
        clearInterval(interval.current);
      }
    };
  }, []);

  return (
          <ChannelsProvider value={channelsContext}>
            <List/>
          </ChannelsProvider>
  );
};
