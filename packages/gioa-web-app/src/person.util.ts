import {upperCase} from "lodash";

export function getInitials(params?: { firstName?: string | null, lastName?: string | null }) {
  const { firstName, lastName } = params ?? {};
  return upperCase((firstName?.[0] ?? "?") + (lastName?.[0] ?? "?"));
}

export function getFullName(params?: { firstName?: string | null, lastName?: string | null }) {
  const { firstName, lastName } = params ?? {};
  return `${firstName ?? ""} ${lastName ?? ""}`;
}
