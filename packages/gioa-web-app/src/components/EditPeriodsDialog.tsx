import React, {useEffect} from 'react';
import {<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ooter, <PERSON><PERSON><PERSON>eader, DialogTitle} from "@/src/components/ui/dialog.tsx";
import {Button} from "@/src/components/ui/button.tsx";
import {DialogDescription} from "@radix-ui/react-dialog";
import {zodValidator} from "@tanstack/zod-form-adapter";
import {useForm} from "@tanstack/react-form";
import {DayPart} from "../../../api/src/scheduleSchemas.ts";
import {FormControl} from "@/src/components/form/FormControl.tsx";
import {Label} from "@/src/components/ui/label.tsx";
import {FormInput} from "@/src/components/form/FormInput.tsx";
import {FieldInfo} from "@/src/components/form/FieldInfo.tsx";
import {map} from "lodash";
import {compareTimeOfDay} from "../../../api/src/date.util.ts";
import {PlusIcon} from "lucide-react";
import {DailyTimeRange} from "../../../api/src/timeSchemas.ts";
import {FormColorPicker} from "@/src/components/form/FormColorPicker.tsx";

import {incrementDurationMinutes} from "../../../api/src/scheduleBuilder.util.ts";

export interface EditPeriodsDialogProps {
  dayParts: DayPart[];
  onSave: (dayParts: DayPart[]) => void;
  dayOfWeek: number;
  isOpen: boolean;
  onClose: () => void;
  onOpen: () => void;
  storeHours: DailyTimeRange;
}

const dayPeriodColors = [
  "#F0F7FF",
  "#EAF1FF",
  "#F6F8FC",
];

export const EditPeriodsDialog: React.FC<EditPeriodsDialogProps> = (props) => {
  const form = useForm({
    defaultValues: {
      dayParts: props.dayParts
    },
    onSubmit: async ({value}) => {
      props.onSave(value.dayParts);
    },
    validatorAdapter: zodValidator(),
  });

  useEffect(() => {
    if (!props.isOpen) {
      // give a bit of time for the modal to animate out
      setTimeout(() =>
        form.reset(), 100);
    }
  }, [props.isOpen]);

  const onCancel = () => {
    props.onClose();
  }

  return (
    <Dialog open={props.isOpen} onOpenChange={open => {
      if (open) {
        props.onOpen();
      } else {
        onCancel();
      }
    }}>
      <DialogContent className={"overflow-auto max-h-screen"}>
        <DialogHeader>
          <DialogTitle>Edit Daily Periods</DialogTitle>
          <DialogDescription>
            Edit the daily periods for this week's schedule.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          <form onSubmit={(e) => {
            e.preventDefault()
            e.stopPropagation()
            form.handleSubmit()
          }}>
            <form.Field name={"dayParts"} mode={"array"}
                        children={(arrField) => {
                          return <div>
                            {map(arrField.state.value, (field, index) =>
                              <div key={index} className={"px-4 py-3 border rounded-lg mb-3"}>
                                <div className={"grid grid-cols-2 gap-2 mb-3"}>
                                  <form.Field name={`dayParts[${index}].title`}
                                              children={field =>
                                                <FormControl className={"mb-0"}>
                                                  <Label htmlFor={field.name}>Title</Label>
                                                  <FormInput field={field}
                                                             placeholder="Enter title..."/>
                                                  <FieldInfo field={field}/>
                                                </FormControl>}/>
                                  <form.Field name={`dayParts[${index}].color`}
                                              children={(field) => {
                                                return <FormControl className={"mb-0"}>
                                                  <Label htmlFor={field.name}>Color</Label>
                                                  <FormColorPicker field={field}
                                                                   colors={dayPeriodColors}/>
                                                  <FieldInfo field={field}/>
                                                </FormControl>;
                                              }}/>
                                  <form.Field name={`dayParts[${index}].range.start`}
                                              validators={{
                                                onChange: ({value, fieldApi}) => {
                                                  const comparison = compareTimeOfDay(value, fieldApi.form.getFieldValue(`dayParts[${index}].range.end`));
                                                  if (comparison > 0) {
                                                    return "Start time must be before end time";
                                                  }
                                                  return undefined;
                                                },
                                                onChangeListenTo: [`dayParts[${index}].range.end`],
                                              }}
                                              children={(field) => {
                                                return <FormControl className={"mb-0"}>
                                                  <Label htmlFor={field.name}>Start Time</Label>
                                                  <FormInput field={field} type={"time"}
                                                             step={incrementDurationMinutes * 60}
                                                             placeholder="Choose start time..."/>
                                                  <FieldInfo field={field}/>
                                                </FormControl>;
                                              }}/>
                                  <form.Field name={`dayParts[${index}].range.end`}
                                              validators={{
                                                onChange: ({value, fieldApi}) => {
                                                  const comparison = compareTimeOfDay(value, fieldApi.form.getFieldValue(`dayParts[${index}].range.start`));
                                                  if (comparison < 0) {
                                                    return "End time must be after start time";
                                                  }
                                                  return undefined;
                                                },
                                                onChangeListenTo: [`dayParts[${index}].range.start`],
                                              }}
                                              children={(field) => {
                                                return <FormControl className={"mb-0"}>
                                                  <Label htmlFor={field.name}>End Time</Label>
                                                  <FormInput field={field} type={"time"}
                                                             step={incrementDurationMinutes * 60}
                                                             placeholder="Choose end time..."/>
                                                  <FieldInfo field={field}/>
                                                </FormControl>;
                                              }}/>
                                </div>
                                <div>
                                  <Button type={"button"} variant={"outline"} size={"sm"} onClick={() => {
                                    arrField.removeValue(index);
                                  }}>
                                    Remove Period
                                  </Button>
                                </div>
                              </div>)}

                            <Button type={"button"} onClick={() => arrField.pushValue({
                              range: {
                                start: props.storeHours.start,
                                end: props.storeHours.end
                              },
                              color: dayPeriodColors[0],
                              title: "New Period"
                            })}
                                    leftIcon={<PlusIcon/>}
                                    variant={"outline"}>
                              Add New Period
                            </Button>
                          </div>
                        }}/>

            <DialogFooter>
              <Button variant={"outline"} type={"button"}
                      onClick={onCancel}>
                Cancel
              </Button>
              <Button type={"submit"}>
                Save
              </Button>
            </DialogFooter>
          </form>
        </div>
      </DialogContent>
    </Dialog>
  );
}
