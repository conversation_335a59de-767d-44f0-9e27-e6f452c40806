import React, {useState} from 'react';
import {<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ooter, <PERSON><PERSON><PERSON>eader, DialogTitle} from "@/src/components/ui/dialog.tsx";
import {Button} from "@/src/components/ui/button.tsx";
import {DialogDescription} from "@radix-ui/react-dialog";
import {useForm} from "@tanstack/react-form";
import {DraftSchedule} from "../../../api/src/scheduleSchemas.ts";
import {FormControl} from "@/src/components/form/FormControl.tsx";
import {Label} from "@/src/components/ui/label.tsx";
import {FieldInfo} from "@/src/components/form/FieldInfo.tsx";
import {filter, isEmpty, map} from "lodash";
import {Text} from "@/src/components/Text.tsx";
import {
  ScheduleValidationResult,
  severityCritical,
  ValidationMessage
} from "../../../api/src/scheduleValidation.types.ts";
import {IsoWeekDate} from "../../../api/src/timeSchemas.ts";
import {api} from "@/src/api.ts";
import {getHumanReadableErrorMessage} from "@/src/components/ErrorAlert.tsx";
import {FormTextarea} from "@/src/components/form/FormTextarea.tsx";
import {Checkbox} from './ui/checkbox.tsx';
import {ScheduleIssuesList} from "@/src/components/ScheduleIssuesList.tsx";
import {SmileIcon} from "lucide-react";
import {SchedulePersonDto} from "../../../api/src/schedulePersonDto.ts";
import {messageToHumanReadable} from "@/src/validationMessageDisplay.tsx";

export interface PublishWeekDialogProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  onGoToMessage: (week: IsoWeekDate, dayOfWeek: number, msg: ValidationMessage) => void;
  onIgnoreMessage: (msg: ValidationMessage) => void;
  onUnignoreMessage: (msg: ValidationMessage) => void;
  validationResult?: ScheduleValidationResult;
  schedule: DraftSchedule;
  people: SchedulePersonDto[];
  onPublished: () => void;
  draftVersion: number;
}

export const PublishWeekDialog: React.FC<PublishWeekDialogProps> = ({
                                                                      isOpen,
                                                                      onOpenChange, onIgnoreMessage,
                                                                      onGoToMessage,
                                                                      validationResult,
                                                                      schedule, onUnignoreMessage,
                                                                      people, draftVersion,
                                                                      onPublished
                                                                    }) => {
  const [isOverriden, setIsOverriden] = useState(false);
  const getMsg = messageToHumanReadable({
    people: people,
    activeDay: undefined,
    routeFullPath: undefined
  });
  const apiUtil = api.useUtils();

  const publish = api.user.publishDraftSchedule.useMutation();
  const form = useForm({
    defaultValues: {
      changeReason: ""
    },
    onSubmit: async ({value}) => {
      // if there are any critical errors, don't publish
      const criticalErrors = filter(validationResult?.messages, m => m.severity >= severityCritical);
      if (!isEmpty(criticalErrors)) {
        alert("There are critical errors in your schedule. Please fix them before publishing: " + map(criticalErrors, (m, idx) => (idx + 1) + ". " + getMsg(m)).join(", "));
        return;
      }

      publish.mutate({
        scheduleId: schedule.id,
        changeReason: value.changeReason || undefined,
        draftVersion: draftVersion,
        excludeOpenShifts: true
      }, {
        onSuccess: () => {
          apiUtil.user.getSchedule.invalidate();
          apiUtil.user.getScheduleWeeks.invalidate();
          onPublished();
        },
        onError: (error) => {
          console.error("Error publishing schedule:", error);
          alert("Error publishing schedule. Please refresh the page andtry again: " + getHumanReadableErrorMessage(error));
        }
      });
    },
  })

  const messages = validationResult?.messages ?? []

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent size={"3xl"} className={"overflow-auto"} style={{maxHeight: "calc(100vh - 50px)"}}>
        <DialogHeader>
          <DialogTitle>Publish Week {schedule.week.week}</DialogTitle>
          <DialogDescription>
            Review issues and publish the schedule for week {schedule.week.week}. Publishing will notify team members of
            their shifts.
          </DialogDescription>
        </DialogHeader>

        {!isEmpty(messages) ?
          <>
            <h2 className={"font-semibold text-lg"}>
              Issues
            </h2>
            <ScheduleIssuesList warnings={messages} schedule={schedule} showDay
                                onIgnoreMessage={onIgnoreMessage} onUnignoreMessage={onUnignoreMessage}
                                people={people} storeHours={schedule.storeHours} className={" rounded-lg"}
                                onGoToMessage={onGoToMessage}/>
          </> :
          <div className={"flex justify-center items-center flex-col gap-6 px-6 pb-4 pt-10 bg-gray-100 rounded-lg"}
               style={{
                 backgroundImage: `repeating-linear-gradient(
    -45deg,
    transparent,
    transparent 10px,
    rgba(0, 0, 0, 0.05) 10px,
    rgba(0, 0, 0, 0.05) 11px
  )`,
                 backgroundSize: "16px 16px",
               }}>
            <SmileIcon size={92} className={"text-gray-500"}/>
            <div>
              There are no issues detected for this week. Nice work!
            </div>
          </div>}

        <form onSubmit={(e) => {
          e.preventDefault()
          e.stopPropagation()
          form.handleSubmit()
        }}>
          <fieldset disabled={publish.isPending}>
            {!isEmpty(messages) ?
              <label htmlFor={"gioa-override-errors"}
                     className={"mt-2 mb-4 flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4 bg-white"}>
                <Checkbox checked={isOverriden}
                          id={"gioa-override-errors"}
                          onCheckedChange={state => setIsOverriden(typeof state === "boolean" ? state : false)}/>
                <div className="space-y-1 leading-none">
                  <div className={"block"}>
                    Ignore issues
                  </div>
                  <Text size={"sm"} className={"block"}>
                    Select to allow publishing the schedule despite issues.
                  </Text>
                </div>
              </label> : null}

            <form.Field name={"changeReason"}
                        children={field => <FormControl className={"mb-6"}>
                          <Label>Comments (optional)</Label>
                          <FormTextarea field={field} rows={1}
                                        placeholder="Leave a note about what changed"/>
                          <FieldInfo field={field}/>
                        </FormControl>}/>

            <DialogFooter className={"sm:justify-between"}>
              <Button variant={"outline"} type={"button"}
                      onClick={() => onOpenChange(false)}>
                Close
              </Button>
              <Button type={"submit"} isLoading={publish.isPending}
                      disabled={!validationResult?.isValid && !isOverriden}>
                Publish
              </Button>
            </DialogFooter>
          </fieldset>
        </form>
      </DialogContent>
    </Dialog>
  );
}
