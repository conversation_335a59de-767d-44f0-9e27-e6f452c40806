import React from 'react';
import {Link} from "@tanstack/react-router";

export interface DataNavProps {
  storeId: string;
  routeFullPath: string;
}

export const DataNav: React.FC<DataNavProps> = ({ storeId, routeFullPath }) => {
  return (
    <nav className={"flex flex-wrap mb-6"}>
      <Link from={routeFullPath as any} className={"border-b px-6 py-2"}
            activeProps={{className: "border-b-4 border-blue-500 text-foreground"}}
            inactiveProps={{className: "border-gray-300 hover:text-foreground text-muted-foreground"}}
            to={"../files" as any}>
        Data Files
      </Link>

      <Link from={routeFullPath as any} className={"border-b px-6 py-2"}
            activeProps={{className: "border-b-4 border-blue-500 text-foreground"}}
            inactiveProps={{className: "border-gray-300 hover:text-foreground text-muted-foreground"}}
            to={"../forecasts" as any}>
        Forecasts
      </Link>
    </nav>
  );
};
