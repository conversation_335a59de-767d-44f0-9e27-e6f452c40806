import React, { useCallback, useMemo } from "react";
import { api } from "@/src/api";
import { Text } from "@/src/components/Text";
import { filter, flatMap, includes, uniq } from "lodash";
import { PersonNoteDto } from "../../../api/src/schemas";
import { filterNotes, NotesFilterFormValues } from "../../../api/src/Notes.util";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/src/components/ui/dialog";
import { Button } from "@/src/components/ui/button";
import { useNavigate } from "@tanstack/react-router";
import { PersonNote } from "@/src/components/PersonNote";
import { ScrollArea } from "@/src/components/ui/scroll-area.tsx";
import {useGoTo} from "@/src/navigationUtils.ts";

export type NotesFilter = NotesFilterFormValues & {
  pastDays: number;
};

export function PolicyViolationListModal({
  isOpen,
  onOpenChange,
  businessId,
  storeId,
  personId,
  filters,
  omitNoteIds,
  title,
  description,
  timezone,
}: {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  businessId: string;
  storeId: string;
  personId: string;
  filters: NotesFilter;
  omitNoteIds?: string[];
  title: string;
  description: string;
  timezone: string;
}) {
  const navigate = useNavigate();
  const [{ notes }] = api.user.getPersonDetail.useSuspenseQuery(
    {
      personId: personId!,
      storeId: storeId!,
    },
    {
      refetchInterval: 1000 * 60 * 15, // 15 minutes
    },
  );

  const goTo = useGoTo();
  const onNotePressed = useCallback(
    (note: PersonNoteDto) => {
      onOpenChange(false);

      if ("correctiveAction" in note) {
        goTo.viewCorrectiveAction(businessId, storeId, personId, note.correctiveAction.id);
      } else {
        goTo.viewPersonNote(businessId, storeId, personId, note.id);
      }
    },
    [storeId, personId, navigate],
  );

  const filteredNotes = useMemo(() => {
    const policyFilters = uniq(
      flatMap(notes, (note) =>
        "policiesInAction" in note
          ? note.policiesInAction
          : "correctiveAction" in note
            ? note.correctiveAction.policiesInAction
            : [],
      ),
    );
    return filterNotes({
      personNotes: filter(notes, (n) => !includes(omitNoteIds, n.id)),
      filterValues: filters,
      searchString: "",
      policyFilters: policyFilters,
      timezone,
    });
  }, [notes, filters, omitNoteIds, timezone]);

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px] max-h-[80vh] overflow-hidden flex flex-col">
        <DialogHeader>
          <DialogTitle>{title}</DialogTitle>
          <Text size="sm">{description}</Text>
        </DialogHeader>

        <div className="flex-1 py-4 flex flex-col">
          {filteredNotes.length === 0 ? (
            <div className="flex flex-col items-center justify-center h-40">
              <Text muted>No notes found</Text>
            </div>
          ) : (
            <div className="space-y-3 h-full">
              <ScrollArea className="h-full max-h-[50vh] pr-4 overflow-auto">
                {filteredNotes.map((note) => (
                  <div
                    key={note.id}
                    className="bg-white cursor-pointer hover:bg-gray-50"
                    onClick={() => onNotePressed(note)}
                  >
                    <PersonNote note={note} onClickNote={onNotePressed} />
                  </div>
                ))}
              </ScrollArea>
            </div>
          )}
        </div>

        <div className="flex justify-end pt-4 mt-2">
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Close
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
