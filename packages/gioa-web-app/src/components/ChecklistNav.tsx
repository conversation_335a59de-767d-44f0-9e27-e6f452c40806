import React, {useEffect, useState} from 'react';
import {Helmet} from "react-helmet";
import {useNavigate, useRouterState} from "@tanstack/react-router";
import {api} from "@/src/api.ts";
import {Tabs, TabsList, TabsTrigger} from "@/src/components/ui/tabs.tsx"

export interface ChecklistNavProps {
  storeId: string;
  routeFullPath: string;
}

export const ChecklistNav: React.FC<ChecklistNavProps> = ({storeId, routeFullPath}) => {
  const navigate = useNavigate();
  const routerState = useRouterState();
  const [activeTab, setActiveTab] = useState('active');

  const [store] = api.user.getStoreAdmin.useSuspenseQuery({
    storeId
  });

  useEffect(() => {
    const currentPath = routerState.location.pathname;

    if (currentPath.includes('/active')) {
      setActiveTab('active');
    } else if (currentPath.includes('/recorded')) {
      setActiveTab('recorded');
    } else if (currentPath.includes('/templates')) {
      setActiveTab('templates');
    } else if (currentPath.includes('/upcoming')) {
      setActiveTab('upcoming');
    }
  }, [routerState.location.pathname]);

  const handleTabChange = (value: string) => {
    setActiveTab(value);
    navigate({ to: `${routeFullPath}/${value}` });
  }

  return (
    <>
      <Helmet>
        <title>
          Checklists - {store.title} - Nation
        </title>
      </Helmet>

      <Tabs value={activeTab} onValueChange={handleTabChange}>
        <TabsList>
          <TabsTrigger value="active" className={"text-md"}>Active</TabsTrigger>
          <TabsTrigger value="upcoming" className={"text-md"}>Upcoming</TabsTrigger>
          <TabsTrigger value="templates" className={"text-md"}>Templates</TabsTrigger>
          <TabsTrigger value="recorded" className={"text-md"}>Recorded</TabsTrigger>
        </TabsList>
      </Tabs>
    </>
  );
}
