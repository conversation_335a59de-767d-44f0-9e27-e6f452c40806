import {<PERSON><PERSON>, DialogContent, DialogDescription, <PERSON><PERSON><PERSON>ooter, <PERSON><PERSON>Header, DialogTitle} from "@/src/components/ui/dialog.tsx";
import {Button} from "@/src/components/ui/button.tsx";
import {DataTableColumnHeader} from "@/src/components/DataTableColumnHeader.tsx";
import {Text} from "@/src/components/Text.tsx";
import {Label} from "@/src/components/ui/label.tsx";
import {Select, SelectContent, SelectItem, SelectTrigger, SelectValue} from "@/src/components/ui/select.tsx";
import {Slider} from "@/src/components/ui/slider.tsx";
import {Tooltip, TooltipContent, TooltipProvider, TooltipTrigger} from "@/src/components/ui/tooltip.tsx";
import {createColumnHelper} from "@tanstack/react-table";
import {format} from "date-fns";
import {cn} from "@/src/util.ts";
import {HelpCircleIcon} from "lucide-react";
import {
  formatVarianceMinutes,
  formatVarianceDisplay,
  calculateVarianceCost,
  getVarianceColor,
  formatTime
} from "@/src/utils/variance.util.ts";

// Define the time punch variance entry type to match API response
export type TimePunchVarianceEntry = {
  id: string;
  employeeName: string;
  dayOfWeek: string | null;
  date: Date;
  timeIn: string | null;
  timeOut: string | null;
  totalHours: number | null;
  payType: string | null;
  totalUnpaidMinutes: number | null;
  scheduledTimeRange: string | null;
  scheduledShiftId: string | null;
  scheduledShiftDate: string | null;
  scheduledTimeDuration: number | null; // Duration of scheduled shift in minutes
  punchTimeDuration: number | null;     // Duration of actual time punch in minutes
  clockInVariance: number | null;
  clockOutVariance: number | null;
  varianceTotal: number | null; // Now integer minutes instead of formatted string
  payRate: number | null;
};

const columnHelper = createColumnHelper<TimePunchVarianceEntry>();

// Helper function to check if a time string is valid
export const isValidTime = (timeStr: string | null): boolean => {
  if (!timeStr || timeStr.trim() === "") return false;
  const cleanTime = timeStr.trim().toLowerCase();

  // Check for corrupted data patterns
  if (cleanTime.includes('oen') ||
      cleanTime.includes('un:ch') ||
      cleanTime.includes('oen un:ch') ||
      cleanTime.length < 2) {
    return false;
  }

  // Check if it contains valid time characters (digits, colon, a, p, m, space)
  const validTimeRegex = /^[\d:apm\s]+$/i;
  if (!validTimeRegex.test(cleanTime)) {
    return false;
  }

  // Must contain at least one digit
  if (!/\d/.test(cleanTime)) {
    return false;
  }

  return true;
};

// Total Labor Variance Help Dialog Component
interface TotalLaborVarianceHelpDialogProps {
  isOpen: boolean;
  onClose: () => void;
}

export function TotalLaborVarianceHelpDialog({ isOpen, onClose }: TotalLaborVarianceHelpDialogProps) {
  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle>Total Labor Variance</DialogTitle>
          <DialogDescription>
            The dollar amounts shown are calculated based on the pay rates you have entered for each team member.
            These amounts might not be accurate if pay rates are outdated or missing. You can click on "Manage Pay Rates"
            on this page to verify and update the actual pay rate of each employee.
            <br /><br />
            *Calculations exclude overtime calculations. Overtime hours are assumed to have the same wage rate.
          </DialogDescription>
        </DialogHeader>
        <DialogFooter>
          <Button onClick={onClose}>
            Close
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

// Variance Cost Tooltip Component
interface VarianceCostTooltipProps {
  totalCostCents: number;
  formattedCost: string;
  colorClass: string;
}

export function VarianceCostTooltip({ totalCostCents, formattedCost, colorClass }: VarianceCostTooltipProps) {
  if (totalCostCents === 0) {
    return <span className="text-slate-800">—</span>;
  }

  const isPositiveCost = totalCostCents > 0;

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <span className={colorClass}>{formattedCost}</span>
        </TooltipTrigger>
        <TooltipContent side="bottom" align="start" className="max-w-xs w-64">
          <div className="text-left">
            <div className={cn("font-semibold text-lg mb-1", isPositiveCost ? "text-red-600" : "text-green-600")}>
              {isPositiveCost ? "Pay" : "Save"}
            </div>
            <div className="text-xs text-gray-400">
              {isPositiveCost
                ? "Additional compensation given to team members for working more than their scheduled hours."
                : "Money saved when team members work fewer hours than scheduled."
              }
            </div>
          </div>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}

// Variance Filter Dialog Component
interface VarianceFilterDialogProps {
  isOpen: boolean;
  onClose: () => void;
  draftPunchType: string;
  setDraftPunchType: (value: string) => void;
  draftPunchTiming: string;
  setDraftPunchTiming: (value: string) => void;
  draftTimeCondition: string;
  setDraftTimeCondition: (value: string) => void;
  draftTimeThreshold: number;
  setDraftTimeThreshold: (value: number) => void;
  onReset: () => void;
  onApply: () => void;
}

export function VarianceFilterDialog({
  isOpen,
  onClose,
  draftPunchType,
  setDraftPunchType,
  draftPunchTiming,
  setDraftPunchTiming,
  draftTimeCondition,
  setDraftTimeCondition,
  draftTimeThreshold,
  setDraftTimeThreshold,
  onReset,
  onApply
}: VarianceFilterDialogProps) {
  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle>Clock-in/out Variance Filter</DialogTitle>
          <DialogDescription>
            Filter time punch entries based on variance criteria.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Punch Type */}
          <div className="space-y-2">
            <Label className="text-sm font-medium">Punch Type</Label>
            <Select value={draftPunchType} onValueChange={setDraftPunchType}>
              <SelectTrigger>
                <SelectValue placeholder="Select punch type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All</SelectItem>
                <SelectItem value="clockIn">Clock-In</SelectItem>
                <SelectItem value="clockOut">Clock-Out</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Punch Timing */}
          <div className="space-y-2">
            <Label className="text-sm font-medium">Punch Timing</Label>
            <Select value={draftPunchTiming} onValueChange={setDraftPunchTiming}>
              <SelectTrigger>
                <SelectValue placeholder="Select timing" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All</SelectItem>
                <SelectItem value="early">Early</SelectItem>
                <SelectItem value="late">Late</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Time Condition */}
          <div className="space-y-2">
            <Label className="text-sm font-medium">Time Condition</Label>
            <Select value={draftTimeCondition} onValueChange={setDraftTimeCondition}>
              <SelectTrigger>
                <SelectValue placeholder="Select condition" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All</SelectItem>
                <SelectItem value="lessThan">Less than</SelectItem>
                <SelectItem value="greaterThan">Greater than</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Time Threshold Slider */}
          {draftTimeCondition !== "all" && (
            <div className="space-y-3">
              <Label className="text-sm font-medium">
                Time Threshold: {draftTimeThreshold} minute{draftTimeThreshold !== 1 ? 's' : ''}
              </Label>
              <Slider
                value={[draftTimeThreshold]}
                onValueChange={(value) => setDraftTimeThreshold(value[0])}
                max={60}
                min={1}
                step={1}
                className="w-full"
              />
              <div className="text-xs text-gray-500">
                Filter {draftPunchType === "all" ? "Clock-In or Clock-Out" : draftPunchType === "clockIn" ? "Clock-In" : "Clock-Out"} punches that are {draftPunchTiming === "all" ? "early or late" : draftPunchTiming} by {draftTimeCondition === "lessThan" ? "less than" : "more than"} {draftTimeThreshold} minute{draftTimeThreshold !== 1 ? 's' : ''}.
              </div>
            </div>
          )}
        </div>

        <DialogFooter className="gap-2">
          <Button variant="outline" onClick={onReset}>
            Reset
          </Button>
          <Button onClick={() => {
            onApply();
            onClose();
          }}>
            Apply Filter
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

// Variance Table Columns Function
interface VarianceTableColumnsProps {
  store: {
    permissions?: {
      canViewPayRates?: boolean;
    };
  };
  helpDialogDisclosure: {
    onOpen: () => void;
  };
  showTeamMemberColumn?: boolean; // Optional parameter to show/hide team member column
}

export function createVarianceTableColumns({ store, helpDialogDisclosure, showTeamMemberColumn = true }: VarianceTableColumnsProps) {
  const columns = [];

  // Conditionally add the team member column
  if (showTeamMemberColumn) {
    columns.push(
      columnHelper.accessor("employeeName", {
        header: ({ column }) => (
          <DataTableColumnHeader column={column} title="Team Member" />
        ),
        cell: ({ getValue }) => (
          <Text className="font-medium">{getValue()}</Text>
        ),
      })
    );
  }

  // Add the rest of the columns
  columns.push(
    columnHelper.accessor("date", {
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Date" />
      ),
      cell: ({ getValue }) => {
        const date = getValue();
        // Format date in UTC to avoid timezone shift issues
        const year = date.getUTCFullYear();
        const month = date.getUTCMonth();
        const day = date.getUTCDate();
        const utcDate = new Date(year, month, day);
        return <Text>{format(utcDate, "MMM dd, yyyy")}</Text>;
      },
    }),
    columnHelper.accessor("scheduledTimeRange", {
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Scheduled Time" />
      ),
      cell: ({ getValue, row }) => {
        const scheduledTime = getValue();
        const scheduledDurationMinutes = row.original.scheduledTimeDuration;

        if (!scheduledTime || scheduledTime === "—") {
          return <Text className="text-gray-500">—</Text>;
        }

        // Use existing formatVarianceDisplay function for duration formatting
        const duration = scheduledDurationMinutes ? formatVarianceDisplay(scheduledDurationMinutes) : "N/A";

        return (
          <div className="flex flex-col">
            <Text className="font-medium">
              {scheduledTime}
            </Text>
            <Text className="text-sm text-gray-500 mt-1">
              {duration}
            </Text>
          </div>
        );
      },
    }),
    columnHelper.display({
      id: "actualTime",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Actual Time" />
      ),
      cell: ({ row }) => {
        const timeIn = row.original.timeIn;
        const timeOut = row.original.timeOut;
        const punchDurationMinutes = row.original.punchTimeDuration;

        // Check validity first
        const hasValidTimeIn = isValidTime(timeIn);
        const hasValidTimeOut = isValidTime(timeOut);

        const formattedTimeIn = hasValidTimeIn ? formatTime(timeIn) : "";
        const formattedTimeOut = hasValidTimeOut ? formatTime(timeOut) : "";

        // Check if this is an open punch (has valid time in but no valid time out)
        const isOpenPunch = hasValidTimeIn && !hasValidTimeOut;

        if (!hasValidTimeIn && !hasValidTimeOut) {
          return <Text className="text-gray-500">—</Text>;
        }

        if (isOpenPunch) {
          const totalHours = row.original.totalHours;

          return (
            <div className="flex flex-col">
              <Text className="font-medium">
                {formattedTimeIn} - Open Punch
              </Text>
              <Text className="text-sm text-gray-500 mt-1">
                {totalHours ? `Recorded time: ${Math.floor(totalHours)}:${Math.floor((totalHours % 1) * 60).toString().padStart(2, '0')}` : "N/A"}
              </Text>
            </div>
          );
        }

        // Calculate total duration including unpaid breaks for combined punches
        let totalDurationMinutes = punchDurationMinutes;
        const unpaidMinutes = row.original.totalUnpaidMinutes;

        // If this row has unpaid minutes, add them to get the total time span
        if (unpaidMinutes && punchDurationMinutes) {
          totalDurationMinutes = punchDurationMinutes + unpaidMinutes;
        }

        // Use existing formatVarianceDisplay function for duration formatting
        const duration = totalDurationMinutes ? formatVarianceDisplay(totalDurationMinutes) : "N/A";

        return (
          <div className="flex flex-col">
            <Text className="font-medium">
              {formattedTimeIn && formattedTimeOut
                ? `${formattedTimeIn} - ${formattedTimeOut}`
                : formattedTimeIn || formattedTimeOut || "—"
              }
            </Text>
            <Text className="text-sm text-gray-500 mt-1">
              {duration}
            </Text>
          </div>
        );
      },
    }),

    columnHelper.accessor("clockInVariance", {
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Clock-In" />
      ),
      cell: ({ getValue }) => {
        const variance = getValue();
        const formattedVariance = formatVarianceDisplay(variance);
        return (
          <Text className={variance ? "" : "text-gray-500"}>
            {formattedVariance}
          </Text>
        );
      },
      sortingFn: (rowA, rowB, columnId) => {
        const varianceA = rowA.getValue(columnId) as number | null;
        const varianceB = rowB.getValue(columnId) as number | null;

        // Handle null values
        const minutesA = varianceA ?? 0;
        const minutesB = varianceB ?? 0;

        return minutesA - minutesB;
      },
    }),
    columnHelper.accessor("clockOutVariance", {
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Clock-Out" />
      ),
      cell: ({ getValue }) => {
        const variance = getValue();
        const formattedVariance = formatVarianceDisplay(variance);
        return (
          <Text className={variance ? "" : "text-gray-500"}>
            {formattedVariance}
          </Text>
        );
      },
      sortingFn: (rowA, rowB, columnId) => {
        const varianceA = rowA.getValue(columnId) as number | null;
        const varianceB = rowB.getValue(columnId) as number | null;

        // Handle null values
        const minutesA = varianceA ?? 0;
        const minutesB = varianceB ?? 0;

        return minutesA - minutesB;
      },
    }),
    columnHelper.accessor("totalUnpaidMinutes", {
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Unpaid Breaks" />
      ),
      cell: ({ getValue }) => {
        const unpaidMinutes = getValue();
        const formattedBreaks = formatVarianceDisplay(unpaidMinutes);
        return (
          <Text className={unpaidMinutes ? "" : "text-gray-500"}>
            {formattedBreaks}
          </Text>
        );
      },
      sortingFn: (rowA, rowB, columnId) => {
        const minutesA = rowA.getValue(columnId) as number | null;
        const minutesB = rowB.getValue(columnId) as number | null;

        // Handle null values - put them at the end
        if (minutesA === null && minutesB === null) return 0;
        if (minutesA === null) return 1;
        if (minutesB === null) return -1;

        return minutesA - minutesB;
      },
    }),
    columnHelper.accessor("varianceTotal", {
      header: ({ column }) => (
        <div className="flex items-center space-x-1">
          <DataTableColumnHeader column={column} title="Variance Total" />
          {/* Only show help button if user has canViewPayRates permission */}
          {store.permissions?.canViewPayRates && (
            <Button
              variant="ghost"
              size="sm"
              className="h-4 w-4 p-0 hover:bg-gray-100"
              onClick={helpDialogDisclosure.onOpen}
            >
              <HelpCircleIcon className="h-3 w-3 text-gray-500" />
            </Button>
          )}
        </div>
      ),
      cell: ({ getValue, row }) => {
        const varianceMinutes = getValue();
        const payRate = row.original.payRate;
        const formattedVariance = formatVarianceMinutes(varianceMinutes, true); // Variance page: show zero as ":00"
        const monetaryValue = calculateVarianceCost(varianceMinutes, payRate);

        // Remove parentheses from monetary value display for cleaner look
        const displayMonetaryValue = monetaryValue ? monetaryValue.replace(/[()]/g, '') : monetaryValue;

        return (
          <div className="space-y-1">
            <Text className={getVarianceColor(varianceMinutes)}>
              {formattedVariance || "—"}
            </Text>
            {/* Only show dollar amount if user has canViewPayRates permission */}
            {displayMonetaryValue && store.permissions?.canViewPayRates && (
              <Text className={cn(
                "text-xs",
                getVarianceColor(varianceMinutes)
              )}>
                {displayMonetaryValue}
              </Text>
            )}
          </div>
        );
      },
      sortingFn: (rowA, rowB, columnId) => {
        const varianceA = rowA.getValue(columnId) as number | null;
        const varianceB = rowB.getValue(columnId) as number | null;

        // Handle null values
        const minutesA = varianceA ?? 0;
        const minutesB = varianceB ?? 0;

        return minutesA - minutesB;
      },
    }),

    columnHelper.display({
      id: "payingSaving",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Status" />
      ),
      cell: ({ row }) => {
        const varianceMinutes = row.original.varianceTotal;

        // Determine status based on variance total
        const getStatus = () => {
          if (varianceMinutes === null || varianceMinutes === undefined || varianceMinutes === 0) {
            return { text: "Even", style: "bg-gray-100 text-gray-600" };
          }
          if (varianceMinutes < 0) return { text: "Save", style: "bg-green-100 text-green-600" }; // Early = paying less (good)
          return { text: "Pay", style: "bg-red-100 text-red-600" }; // Late = paying more (costs more)
        };

        const status = getStatus();

        return (
          <span className={cn(
            "px-2 py-1 rounded-full text-xs font-medium",
            status.style
          )}>
            {status.text}
          </span>
        );
      },
    })
  );

  return columns;
}
