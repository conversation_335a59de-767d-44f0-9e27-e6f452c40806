import React from 'react';
import {InlineEdit} from "@/src/components/InlineEdit";

type TextInlineEditProps = {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  className?: string;
  inputClassName?: string;
  displayClassName?: string;
  showEditIcon?: boolean;
  multiline?: boolean;
  minWidth?: number;
};

export const TextInlineEdit: React.FC<TextInlineEditProps> = ({
  value,
  onChange,
  placeholder = 'Enter text',
  className = '',
  inputClassName = '',
  displayClassName = '',
  showEditIcon = true,
  multiline = false,
  minWidth,
}) => {
  return (
    <InlineEdit<string>
      value={value}
      onChange={onChange}
      inputType="text"
      placeholder={placeholder}
      className={className}
      inputClassName={inputClassName}
      displayClassName={displayClassName}
      showEditIcon={showEditIcon}
    />
  );
};
