import React from 'react';
import {Badge, BadgeProps, badgeVariants} from "@/src/components/ui/badge.tsx";
import {VariantProps} from 'class-variance-authority';
import {TimeOffStatusFilter} from "../../../api/src/personTimeOff.schemas.ts";
import {capitalize} from "lodash";

export interface ShiftOfferStatusBadgeProps extends BadgeProps {
  status: string;
}

const statusToColorScheme: {[status: string]: VariantProps<typeof badgeVariants>["colorScheme"]} = {
  pending: "default",
  approved: "success",
  declined: "destructive",
  cancelled: "default",
  expired: "secondary",
  accepted: "yellow",
};

export const ShiftOfferStatusBadge: React.FC<ShiftOfferStatusBadgeProps> = ({status, ...props}) => {
  const colorScheme = statusToColorScheme[status.toLowerCase()] ?? "default";

  return (
    <Badge colorScheme={colorScheme} {...props}>
      {capitalize(status)}
    </Badge>
  );
}
