import React, {useCallback, useEffect, useState} from 'react';
import {ArrowLeftIcon, SearchIcon, UserCircleIcon} from "lucide-react";
import {Input} from "@/src/components/ui/input.tsx";
import {DraftSchedule, BaseSchedule, HawaiiACAPersonHours} from "../../../api/src/scheduleSchemas.ts";
import {But<PERSON>} from "@/src/components/ui/button.tsx";
import {motion} from "framer-motion";
import {PersonDetailsPanelContent} from "@/src/components/PersonDetailsPanelContent.tsx";
import {StoreAreaDto} from "../../../api/src/schemas.ts";
import {DayOfWeek} from "@gioa/api/src/timeSchemas.ts";
import {TeamMemberList} from "@/src/components/TeamMemberList.tsx";
import {TeamMemberSelectButton} from "@/src/components/TeamMemberSelectButton.tsx";
import {find, map} from "lodash";
import {Select, SelectContent, SelectItem, SelectSeparator, SelectTrigger, SelectValue} from './ui/select.tsx';
import {getPersonScore} from "../../../api/src/shiftSuggestion.ts";
import {SendFn} from "@/src/components/ScheduleBuilder.machine.tsx";
import {SchedulePersonClientDto} from "../../../api/src/schedulePersonDto.ts";
import {getShift} from "../../../api/src/scheduling.util.ts";
import {ScheduleValidationSettings} from "../../../api/src/scheduleValidation.types.ts";

export interface TeamMembersPanelContentProps {
  onSelectTeamMember: (person: SchedulePersonClientDto) => void;
  onBack: () => void;
  schedule: DraftSchedule;
  people: SchedulePersonClientDto[];
  dayOfWeek: DayOfWeek;
  selectedShiftId: string | null;
  storeAreas: StoreAreaDto[];
  timezone: string;
  openPersonId?: string;
  send: SendFn;
  settings: ScheduleValidationSettings;
  weeksHoursMap: HawaiiACAPersonHours;
  storeState?: string;
}

export const TeamMembersPanelContent: React.FC<TeamMembersPanelContentProps> = ({
                                                                                  onSelectTeamMember,
                                                                                  onBack, people, openPersonId,
                                                                                  selectedShiftId, schedule,
                                                                                  storeAreas, dayOfWeek,
                                                                                  timezone, send, settings,
                                                                                  weeksHoursMap,
                                                                                  storeState = 'Unknown'
                                                                                }) => {
  const [searchInput, setSearchInput] = useState("");
  const shift = selectedShiftId ? getShift(schedule, selectedShiftId) : null;
  const selectedPerson = find(people, p => p.id === openPersonId);
  const isOnList = !Boolean(selectedPerson);
  const isSelecting = Boolean(shift);
  const selectedPersonScore = shift && selectedPerson ? getPersonScore({
    shift,
    schedule,
    storeAreas,
    person: selectedPerson,
    dayOfWeek,
    timezone,
    settings,
    weeksHoursMap,
    storeState
  }) : null;



  const day = find(schedule.days, d => d.dayOfWeek === dayOfWeek);
  const shiftArea = find(day?.areas, a => a.id === shift?.shiftAreaId);
  const [filterAreaId, setFilterAreaId] = useState<string>(shiftArea?.storeAreaId ?? "all");
  useEffect(() => {
    if (shift?.isShiftLead) {
      setFilterAreaId("shiftLeaders");
    } else {
      setFilterAreaId(shiftArea?.storeAreaId ?? "all");
    }
  }, [shiftArea, shift]);

  const onViewDetails = useCallback((person: SchedulePersonClientDto) => {
    send({type: "viewTeamMemberDetails", personId: person.id});
  }, []);

  const onBackToList = () => {
    send({type: "panelBack"});
  }

  return (
    <div className={"relative w-full h-full overflow-hidden"}>
      <motion.div key="list" className={"absolute top-0 right-0 bottom-0 left-0 overflow-auto"}
                  initial={{x: !isOnList ? "-100%" : 0}}
                  animate={{x: !isOnList ? "-100%" : 0}}
                  exit={{x: "-100%"}}
                  transition={{type: "tween", ease: "easeInOut", duration: 0.2}}>
        <div className={"flex gap-2 items-center mt-2 mb-2"}>
          <Button onClick={onBack} aria-label={"Back"} className={"ms-1"}
                  variant={"ghost"} size={"sm"}>
            <ArrowLeftIcon size={24} className={"text-gray-600"}/>
          </Button>
          <p className="font-medium mb-1">Team Members</p>
        </div>
        <div>
          <div className="flex gap-6 items-center px-4 pb-4">
            <UserCircleIcon size={36} className="text-gray-600"/>
            <div className="text-left">
              <p className="text-sm">Select and filter team members for open shifts.</p>
            </div>
          </div>

          <div className="px-4 pb-4 border-b border-gray-200 grid grid-cols-2 gap-2">
            <Select value={filterAreaId} onValueChange={setFilterAreaId}>
              <SelectTrigger className="text-left">
                <SelectValue placeholder="Filter..."/>
              </SelectTrigger>
              <SelectContent>
                <SelectItem value={"all"}>
                  All
                </SelectItem>
                <SelectItem value={"shiftLeaders"}>
                  Shift Leaders
                </SelectItem>
                <SelectSeparator/>
                {map(storeAreas, area => {
                  return <SelectItem value={area.id} key={area.id}>
                    {area.title}
                  </SelectItem>
                })}
              </SelectContent>
            </Select>

            <Input
              type="search"
              leftIcon={SearchIcon}
              placeholder="Search..."
              value={searchInput}
              onChange={(event) => setSearchInput(event.target.value)}
              className="max-w-sm"
            />

          </div>
          <TeamMemberList schedule={schedule} people={people} dayOfWeek={dayOfWeek} shift={shift ?? undefined}
                          storeHours={schedule.storeHours} settings={settings}
                          selectedShiftId={selectedShiftId} searchInput={searchInput} timezone={timezone}
                          storeAreas={storeAreas} onSelectTeamMember={onSelectTeamMember} filterAreaId={filterAreaId}
                          isSelecting={isSelecting} onViewDetails={onViewDetails}
                          weeksHoursMap={weeksHoursMap}
                          storeState={storeState}/>
        </div>
      </motion.div>

      <motion.div key="details" className={"absolute top-0 right-0 bottom-0 left-0 overflow-auto"}
                  initial={{x: "100%"}}
                  animate={{x: !isOnList ? 0 : "100%"}}
                  exit={{x: "100%"}}
                  transition={{type: "tween", ease: "easeInOut", duration: 0.2}}>
        <Button onClick={onBackToList} aria-label={"Back"} className={"my-2 ms-1 absolute top-0 left-0"}
                variant={"ghost"} size={"sm"}>
          <ArrowLeftIcon size={24} className={"text-gray-600"}/>
        </Button>
        {selectedPerson && isSelecting &&
            <TeamMemberSelectButton onClick={onSelectTeamMember}
                                    person={selectedPerson}
                                    className="absolute top-0 right-0 my-2 me-2"
                                    reasons={selectedPersonScore ? selectedPersonScore.reasons : []}
                                    score={selectedPersonScore ? selectedPersonScore.score : 0}
            />}
        {selectedPerson && (
          <PersonDetailsPanelContent schedule={schedule} selectedShiftId={selectedShiftId}
                                     storeAreas={storeAreas} timezone={timezone}
                                     person={selectedPerson}/>
        )}
      </motion.div>
    </div>
  );
};
