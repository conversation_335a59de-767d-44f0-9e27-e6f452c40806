import { difference, filter, intersection, isEmpty, orderBy } from "lodash";
import { getPersonNoteHeading } from "./PersonNoteHeader";
import { subDays } from 'date-fns';

// This will be expanded when implementing filtering functionality
export interface NotesFilterFormValues {
  orderBy: "recent" | "oldest";
  showCorrectiveActions: boolean;
  showCoaching: boolean;
  showFeedback: boolean;
  showGeneral: boolean;
  showPositionScores: boolean;
  usePolicyFilter: boolean;
  includeArchived: boolean;
  policies: string[];
  pastDays?: number;
}

export function filterNotes({
  personNotes,
  filterValues,
  searchString,
  policyFilters,
  timezone
}: {
  personNotes: any[];
  filterValues: NotesFilterFormValues;
  searchString: string;
  policyFilters: string[];
  timezone: string;
}) {
  const filteredTypes = [
    ...(filterValues.showCorrectiveActions ? ["correctiveAction"] : []),
    ...(filterValues.showCoaching ? ["coaching"] : []),
    ...(filterValues.showFeedback ? ["positive-feedback"] : []),
    ...(filterValues.showGeneral ? ["general"] : []),
    ...(filterValues.showPositionScores ? ["positionPerformanceDataPoint"] : []),
  ];

  const now = new Date();
  const pastDateBoundary = filterValues.pastDays ? subDays(now, filterValues.pastDays) : null;

  const filtered = filter(personNotes, (note) => {
    // Date filter
    const matchesDate = !pastDateBoundary || new Date(note.createdAt) >= pastDateBoundary;

    // Text search filter
    const matchesSearch =
      note.type.toLowerCase().includes(searchString.toLowerCase()) ||
      getPersonNoteHeading(note).toLowerCase().includes(searchString.toLowerCase()) ||
      note.note?.toLowerCase().includes(searchString.toLowerCase()) ||
      note.createdBy?.firstName?.toLowerCase().includes(searchString.toLowerCase()) ||
      note.createdBy?.lastName?.toLowerCase().includes(searchString.toLowerCase()) ||
      false;

    // Type filter
    const matchesType = filteredTypes.includes(note.type === "general" ? note.noteType : note.type);

    // Corrective action specific filter
    const caPolicies = note.type === "correctiveAction"
      ? note.correctiveAction.policiesInAction
      : [];
    const policiesThatDontExist = difference(caPolicies, policyFilters);

    const matchesCorrectiveAction =
      note.type !== "correctiveAction" ||
      !filterValues.usePolicyFilter ||
      (filteredTypes.includes("correctiveAction") &&
        // has policies that match the policies filter, or...
        !isEmpty(intersection(caPolicies, filterValues.policies)) ||
        // has policies that don't exist in the available policies filter. This is a fallback in case they changed their available policies and
        // old corrective actions don't have the new policies in them.
        !isEmpty(policiesThatDontExist)
      );

    const notePolicies = note.type === "general" && note.noteType === "coaching"
      ? note.policiesInAction
      : [];
    const notePoliciesThatDontExist = difference(notePolicies, policyFilters);
    const matchesNotePolicies =
      note.type !== "general" ||
      ("noteType" in note ? note.noteType !== "coaching" : true) ||
      !filterValues.usePolicyFilter ||
      // has policies that match the policies filter, or...
      !isEmpty(intersection(notePolicies, filterValues.policies)) ||
      // has policies that don't exist in the available policies filter. This is a fallback in case they changed their available policies and
      // old notes don't have the new policies in them.
      !isEmpty(notePoliciesThatDontExist);

    const matchesArchived = filterValues.includeArchived || !note.isArchived;

    return matchesDate && matchesSearch && matchesType && matchesCorrectiveAction && matchesNotePolicies && matchesArchived;
  });

  return orderBy(filtered, (note) => {
    if (filterValues.orderBy === "recent") return -new Date(note.createdAt).getTime();
    if (filterValues.orderBy === "oldest") return new Date(note.createdAt).getTime();
    return -new Date(note.createdAt).getTime(); // Default to recent
  });
}
