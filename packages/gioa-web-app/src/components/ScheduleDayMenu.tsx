import React from 'react';
import {Select, SelectContent, SelectItem, SelectTrigger, SelectValue} from "@/src/components/ui/select.tsx";
import {IsoWeekDate} from "../../../api/src/timeSchemas.ts";
import {getDateFromWeekDayTime} from "../../../api/src/date.util.ts";
import {addDays, format} from "date-fns";
import {find} from "lodash";
import {daysOfWeek} from "@/src/components/AvailabilityWeek.tsx";
import {formatDateInStoreTZ} from "../../../api/src/scheduleBuilder.util.ts";

export interface ScheduleDayMenuProps {
  dayOfWeek: number;
  onChangeDayOfWeek: (dayOfWeek: number) => void;
  scheduleWeekDate: IsoWeekDate;
  timezone: string | null;
}

export const ScheduleDayMenu: React.FC<ScheduleDayMenuProps> = ({
                                                                  dayOfWeek,
                                                                  onChangeDayOfWeek,
                                                                  scheduleWeekDate,
                                                                  timezone
                                                                }) => {
  const scheduleStartDate = getDateFromWeekDayTime({
    year: scheduleWeekDate.year,
    week: scheduleWeekDate.week,
    day: 1,
    time: "00:00",
    timezone: timezone
  })

  const dayOfWeekObj = find(daysOfWeek, d => d.dayOfWeek === dayOfWeek);

  return (
    <Select value={dayOfWeek.toString()} onValueChange={(d) => onChangeDayOfWeek(parseInt(d))}>
      <SelectTrigger className={"w-44"}>
        <SelectValue placeholder="Select day...">
          <span className={"font-bold"}>
            {dayOfWeekObj?.name}
          </span>,{' '}
          <span>{formatDateInStoreTZ.format(addDays(scheduleStartDate, dayOfWeek - 1))}</span>
        </SelectValue>
      </SelectTrigger>
      <SelectContent>
        <SelectItem value={"1"} rightElement={<span className={"text-right"}>
            {formatDateInStoreTZ.format(scheduleStartDate)}
          </span>}>
          <span className={"mr-3 font-bold"}>
            Monday
          </span>
        </SelectItem>
        <SelectItem value={"2"} rightElement={<span className={"text-right"}>
            {formatDateInStoreTZ.format(addDays(scheduleStartDate, 1))}
          </span>}>
          <span className={"mr-3 font-bold"}>
            Tuesday
          </span>
        </SelectItem>
        <SelectItem value={"3"} rightElement={<span className={"text-right"}>
            {formatDateInStoreTZ.format(addDays(scheduleStartDate, 2))}
          </span>}>
          <span className={"mr-3 font-bold"}>
            Wednesday
          </span>
        </SelectItem>
        <SelectItem value={"4"} rightElement={<span className={"text-right"}>
            {formatDateInStoreTZ.format(addDays(scheduleStartDate, 3))}
          </span>}>
          <span className={"mr-3 font-bold"}>
            Thursday
          </span>
        </SelectItem>
        <SelectItem value={"5"} rightElement={<span className={"text-right"}>
            {formatDateInStoreTZ.format(addDays(scheduleStartDate, 4))}
          </span>}>
          <span className={"mr-3 font-bold"}>
            Friday
          </span>
        </SelectItem>
        <SelectItem value={"6"} rightElement={<span className={"text-right"}>
            {formatDateInStoreTZ.format(addDays(scheduleStartDate, 5))}
          </span>}>
          <span className={"mr-3 font-bold"}>
            Saturday
          </span>
        </SelectItem>
        <SelectItem value={"7"} rightElement={<span className={"text-right"}>
            {formatDateInStoreTZ.format(addDays(scheduleStartDate, 6))}
          </span>}>
          <span className={"mr-3 font-bold"}>
            Sunday
          </span>
        </SelectItem>
      </SelectContent>
    </Select>
  );
}
