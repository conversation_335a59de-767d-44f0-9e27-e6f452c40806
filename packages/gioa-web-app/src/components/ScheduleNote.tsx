import React from "react";
import {PersonAvatar} from "@/src/components/PersonAvatar.tsx";
import {formatDistanceToNow} from "date-fns";
import {Button} from "@/src/components/ui/button.tsx";
import {api} from "@/src/api.ts";
import {find} from "lodash";
import {Edit} from "lucide-react";
import {Text} from "@/src/components/Text.tsx";

export interface ScheduleNoteProps {
  noteId: string;
  scheduleId: string;
  onEdit: (id: string, text: string) => void
  onDelete: (id: string) => void
}

export const ScheduleNote: React.FC<ScheduleNoteProps> = ({noteId, scheduleId, onEdit, onDelete}) => {
  const note = api.user.getScheduleNotes.useQuery({scheduleId: scheduleId}, {
    select: notes => {
      return find(notes, n => n.id === noteId);
    }
  });

  const [isEditing, setIsEditing] = React.useState(false);
  const [editText, setEditText] = React.useState(note.data!.note);

  if (!note.data) {
    return null;
  }

  return (
          <div key={note.data!.id}>
            <div className="flex gap-2 items-center mb-2">
              {note.data.createdByPerson
                      ? <>
                        <PersonAvatar person={note.data.createdByPerson}/>
                        <Text semibold>
                          {note.data.createdByPerson.firstName} {note.data.createdByPerson.lastName}
                          <span className="pl-2 text-muted-foreground text-sm font-normal">
                          {formatDistanceToNow(note.data!.createdAt)}
                        </span>
                        </Text>
                      </> : <Text semibold>
                        System
                      </Text>}
            </div>

            <div className="flex">
              <div className="w-[40px] min-w-[40px] flex justify-center">
                <div className="w-px bg-gray-300"/>
              </div>

              {isEditing ? (
                      <div className="flex flex-col gap-2 pl-2 w-full">
                        <textarea
                                className="border rounded p-2 w-full"
                                value={editText}
                                onChange={e => setEditText(e.target.value)}
                        />
                        <div className="flex justify-between">
                          <Button variant="destructive" onClick={() => onDelete(note.data!.id)}>
                            Delete Note
                          </Button>
                          <div className="flex flex-row gap-2">
                            <Button variant="outline" onClick={() => setIsEditing(false)}>
                              Cancel
                            </Button>
                            <Button
                                    onClick={() => {
                                      onEdit(note.data!.id, editText);
                                      setIsEditing(false);
                                    }}
                            >
                              Save
                            </Button>
                          </div>
                        </div>
                      </div>
              ) : (
                      <div className="flex items-start justify-between w-full">
                        <div className="px-2 flex-1">{note.data!.note}</div>

                        <Button
                                variant="ghost"
                                size="icon"
                                onClick={() => setIsEditing(true)}
                                title="Edit Note"
                        >
                          <Edit size={18} className="text-gray-500"/>
                        </Button>
                      </div>
              )}
            </div>
          </div>
  )
}
