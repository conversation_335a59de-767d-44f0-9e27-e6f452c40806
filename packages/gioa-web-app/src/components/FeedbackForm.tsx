import React from 'react';
import {But<PERSON>} from '@/src/components/ui/button';
import {Text} from '@/src/components/Text';
import {FormTextarea} from "@/src/components/form/FormTextarea.tsx";
import {useForm} from "@tanstack/react-form";
import {zodValidator} from "@tanstack/zod-form-adapter";
import {z} from "zod";
import {Label} from "@/src/components/ui/label.tsx";
import {api} from "@/src/api.ts";
import {FieldInfo} from "@/src/components/form/FieldInfo.tsx";
import {toast} from "sonner";

interface FeedbackFormProps {
  storeId: string;
  businessId: string;
}

export const FeedbackForm: React.FC<FeedbackFormProps> = ({storeId, businessId}) => {
  const submitAppFeedback = api.user.submitAppFeedback.useMutation();

  const form = useForm({
    defaultValues: {
      feedback: ''
    },
    onSubmit: ({value}) => {
      submitAppFeedback.mutate({
        storeId: storeId,
        feedback: value.feedback.trim()
      }, {
        onSuccess: () => {
          form.reset();
          toast.success("Thank you, we appreciate your feedback!", {closeButton: true});
        }
      });
    },
    validatorAdapter: zodValidator(),
  });

  return (
    <div className="bg-white p-6 rounded-lg shadow flex flex-col gap-3">
      <Text size="xl">Feedback</Text>
      <Text muted>
        Please share any feedback you have. As we continue to iterate, we welcome your suggestions on how
        we can improve your experience. Your feedback will be emailed directly to me. Thank you. - Pace
      </Text>
      <form.Field name="feedback"
                  validators={{
                    onSubmit: z.string().min(10).max(2000)
                  }}
                  children={field => <div className={"flex flex-col gap-2"}>
                    <Label>Your Feedback</Label>
                    <FormTextarea field={field} placeholder="Type your feedback here..."/>
                    <FieldInfo field={field}/>
                  </div>}/>
      <div className={"flex"}>
        <Button variant={"outline"}
                disabled={submitAppFeedback.isPending}
                isLoading={submitAppFeedback.isPending}
                onClick={form.handleSubmit}>
          Submit
        </Button>
      </div>
    </div>
  );
};

export default FeedbackForm;