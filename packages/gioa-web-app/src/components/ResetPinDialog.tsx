import {But<PERSON>} from "@/src/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle
} from '@/src/components/ui/dialog.tsx'

export function ResetPinDialog({
                               isOpen,
                               onOpenChange,
                             }: {
  isOpen: boolean;
  onOpenChange: (isOpen: boolean) => void;
}) {
  return (
          <Dialog open={isOpen} onOpenChange={onOpenChange}>
            <DialogContent className="w-full max-w-sm rounded-2xl p-6">
              <DialogHeader className="space-y-1">
                <DialogTitle className="text-lg font-semibold">
                  Reset PIN
                </DialogTitle>
                <DialogDescription className="text-sm">
                  Instruct the team member to reset their PIN by navigating to 'Edit Job & Details' in their profile on the mobile app.
                </DialogDescription>
              </DialogHeader>
                <div className="flex justify-end space-x-2 pt-4">
                  {
                    <Button
                            type="button"
                            variant="outline"
                            onClick={() => {
                              onOpenChange(false);
                            }}
                            className="mr-2"
                    >
                      Close
                    </Button>
                  }
                </div>
            </DialogContent>
          </Dialog>
  )
}
