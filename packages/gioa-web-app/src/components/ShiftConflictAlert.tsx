import React from 'react';
import {isEmpty, map} from "lodash";
import {TriangleAlertIcon} from "lucide-react";
import {getDateFromWeekDayTime, to12HourTime} from "../../../api/src/date.util.ts";
import {Link} from "@tanstack/react-router";
import {formatDateLongLocalized} from "@/src/date.util.ts";
import {cn} from "@/src/util.ts";


import {ShiftConflictDto} from "../../../api/src/personTimeOff.schemas.ts";

export interface ShiftConflictAlertProps {
  conflictingShifts: ShiftConflictDto[];
  routeToSchedule: (conflict: ShiftConflictDto) => string;
  className?: string;
  timezone: string | null;
}

export const ShiftConflictAlert: React.FC<ShiftConflictAlertProps> = ({conflictingShifts, className, routeToSchedule, timezone}) => {
  return !isEmpty(conflictingShifts) ? <div className={cn("border rounded-lg px-4 py-3 border-amber-700", className)}>
    <h2 className={"text-lg font-medium text-amber-700 flex items-center mb-2"}>
      <TriangleAlertIcon size={24} className={"text-amber-700 mr-2"}/>
      Conflicting shifts
    </h2>
    <p className={"mb-3"}>
      Warning: The following shifts conflict with this request:
    </p>
    <ol className={"list-decimal list-inside"}>
      {map(conflictingShifts, conflict => {
        const date = getDateFromWeekDayTime({
          ...conflict.isoWeek,
          timezone: timezone,
          day: conflict.dayOfWeek,
          time: conflict.shift.range.start  // Just need the date
        })
        return <li key={conflict.shift.id}>
          <Link to={routeToSchedule(conflict)}
                search={{dayOfWeek: conflict.dayOfWeek, shiftId: conflict.shift.id}}
                className={"text-blue-600 hover:text-blue-700 hover:underline"}>
            {formatDateLongLocalized(date)} (week {conflict.isoWeek.week}):{' '}
            {to12HourTime(conflict.shift.range.start, true)} - {to12HourTime(conflict.shift.range.end, true)}
          </Link>
        </li>;
      })}
    </ol>

  </div> : null

}
