import React, {Suspense} from 'react';
import {Heading} from "@/src/components/Heading.tsx";
import {SettingsRequestRestrictionForm} from "@/src/components/SettingsRequestRestrictionForm.tsx";
import {Spinner} from "@/src/components/Spinner.tsx";

export interface SettingsAvailabilityProps {
  storeId: string;
}

export const SettingsAvailability: React.FC<SettingsAvailabilityProps> = ({storeId}) => {
  return (
    <div>
      <Heading level={1}>Availability</Heading>

      <div className="my-8">
        <div>
          <h2 className={"font-semibold"}>Availability Request Restrictions</h2>
          <p className="mb-4 text-muted-foreground">Manage the restriction window for availability requests.</p>

          <Suspense fallback={<Spinner size={"lg"}/>}>
            <SettingsRequestRestrictionForm storeId={storeId} restrictionType='availability'/>
          </Suspense>
        </div>
      </div>
    </div>
  );
}
