import React, {useRef} from 'react';
import {cn} from "@/src/util.ts";
import {Avatar, AvatarFallback, AvatarImage} from "@/src/components/ui/avatar.tsx";
import {getInitials} from "@/src/person.util.ts";
import {DailyTimeRange} from "../../../api/src/timeSchemas.ts";
import {CrownIcon, UserRoundIcon} from "lucide-react";
import {imageUrl} from "@/src/images.ts";
import {LaborStatusIcon} from "@/src/components/LaborStatusIcon.tsx";
import {getPersonLaborStatus} from "../../../api/src/personMetrics.ts";
import {to12HourNumber} from "../../../api/src/date.util.ts";
import {dailyTimeRangeToIncrementRange} from "../../../api/src/scheduleBuilder.util.ts";
import {Activity, Shift} from "../../../api/src/scheduleSchemas.ts";
import {PersonDto} from "../../../api/src/schemas.ts";

export type ShiftSetupActivity = Activity & { shift: Shift, assignedTo: PersonDto | undefined };

export interface ScheduleShiftActivityProps {
  rowHeight: number;
  activity: ShiftSetupActivity
  incrementWidth: number;
  onOpen: (areaId: string, shiftId: string) => void;
  storeHours: DailyTimeRange;
  isSelected: boolean;
}

export const ScheduleShiftActivity: React.FC<ScheduleShiftActivityProps> = ({
                                                                              rowHeight, onOpen,
                                                                              activity,
                                                                              incrementWidth,
                                                                              isSelected, storeHours,
                                                                            }) => {
  const draggableRef = useRef<any>();

  const range = dailyTimeRangeToIncrementRange(activity.range, storeHours);
  const actStart = range.start;
  const actEnd = range.end;
  const actWidth = (actEnd - actStart) * incrementWidth;
  const actLeft = actStart * incrementWidth;

  const onClick = () => {
    onOpen("", shift.id);
  }

  const style = {
    width: actWidth,
    left: actLeft
  };

  const onKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" || e.key === " ") {
      onOpen("", shift.id);
    }
  }

  const isHighlighted = isSelected;
  const shift = activity.shift;

  return <button style={style} data-shift-id={shift.id} data-id={shift.id}
                 className={cn("top-0.5 cursor-pointer bottom-0.5 absolute bg-white hover:bg-gray-100 shadow-md rounded-md border-t border-b border-r  active:bg-gray-200 [fieldset:disabled_&]:opacity-50 [fieldset:disabled_&]:cursor-not-allowed",
                         isSelected ? "border-l-0 border-amber-300 outline outline-2 -outline-offset-2 outline-amber-300" : undefined,
                 )}
                 onKeyDown={onKeyDown}
                 onClick={onClick}
                 ref={draggableRef}>
    <div
            className={cn("gap-2 cursor-pointer w-full h-full select-none flex items-center px-4 whitespace-nowrap", isHighlighted ? "translate-x-3 transition-transform" : undefined)}>
      <div className={"text-sm text-gray-700"}>
        {to12HourNumber(activity.range.start, true)}-{to12HourNumber(activity.range.end, true)}
        {/*{hourFloatToFriendlyDuration(shiftDurationHoursFloat)}*/}
      </div>
      <div style={{pointerEvents: "none"}}>
        <Avatar size={"xs"}>
          <AvatarImage src={imageUrl(activity.assignedTo?.profileImageUrl, {width: 64})}
                       className={"select-none"}
                       alt=""/>
          <AvatarFallback className={cn("text-red-600", !activity.assignedTo ? "bg-red-50" : undefined)}>
            {activity.assignedTo ? getInitials({
                      firstName: activity.assignedTo?.firstName,
                      lastName: activity.assignedTo?.lastName
                    }) :
                    <UserRoundIcon size={20} className={"text-red-600"}/>
            }
          </AvatarFallback>
        </Avatar>
      </div>

      {activity.assignedTo
              ? <div>{activity.assignedTo.firstName} {activity.assignedTo.lastName}</div>
              : `Open shift`}

      {activity.assignedTo?.age ?
              <LaborStatusIcon size={20} laborStatus={getPersonLaborStatus(activity.assignedTo.age)}/> : null}

      {shift.isShiftLead ? <div><CrownIcon size={16}/></div> : null}
    </div>
  </button>;
}
