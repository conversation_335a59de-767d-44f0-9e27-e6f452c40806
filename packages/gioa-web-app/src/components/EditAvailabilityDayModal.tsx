import {<PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle, DialogDescription} from "@/src/components/ui/dialog";
import {Text} from "@/src/components/Text";
import {Button} from "@/src/components/ui/button";
import {api} from "@/src/api";
import {Label} from "@/src/components/ui/label.tsx";
import {FormTimePicker} from "@/src/components/form/FormTimePicker.tsx";
import {useForm} from "@tanstack/react-form";
import {compact, every, filter, find, isEmpty, map, reduce} from "lodash";
import {startOfDay, endOfDay, isBefore, addMinutes} from 'date-fns';
import {DateTime} from "luxon";
import React, {useEffect} from "react";
import {getDurationHours} from "../../../api/src/timeSchemas.util.ts";
import {isTimeAllDay, to12HourTime} from "../../../api/src/date.util.ts";
import {DayOfWeek, useDaysOfWeek} from "../../../api/src/daysOfWeek.ts";
import {t} from "@lingui/macro";
import {FormSwitch} from "@/src/components/form/FormSwitch.tsx";
import {Input} from "@/src/components/ui/input.tsx";
import {PlusIcon, TrashIcon} from "lucide-react";
import {TeamMemberPressable} from "@/src/components/TeamMemberPressable.tsx";
import {ProficiencyRating} from "@/src/components/ProficiencyRating.tsx";

export function parseDateFromTimeOfDay(time: string, timezone: string): Date {
  const [hour, minute] = time.split(":").map(Number);
  return DateTime.now().setZone(timezone).set({
    hour,
    minute,
    second: 0,
    millisecond: 0,
  }).toJSDate();
}

export function formatTimeOfDay(date: Date, timezone: string): string {
  return DateTime.fromJSDate(date, {zone: timezone}).toFormat("HH:mm");
}

export function EditAvailabilityDayModal({
                                           isOpen,
                                           onOpenChange,
                                           storeId,
                                           personId,
                                           personAvailabilityId,
                                           dayOfWeek,
                                         }: {
  isOpen: boolean;
  onOpenChange: (isOpen: boolean) => void;
  personId: string;
  storeId: string;
  personAvailabilityId: string;
  dayOfWeek: number;
}) {
  const apiUtil = api.useUtils();
  const [business] = api.user.getBusiness.useSuspenseQuery();
  const [{timezone}] = api.user.getStore.useSuspenseQuery({storeId}, {
    staleTime: 1000 * 60 * 60,
  });

  const [{people}] = api.user.getAllSchedulePeopleAtStore.useSuspenseQuery(
          {storeId},
          {staleTime: 1000 * 60 * 60}
  );
  const person = find(people, {id: personId});

  const daysOfWeek = useDaysOfWeek();
  const day = find(daysOfWeek, (d) => d.dayOfWeek === dayOfWeek);

  const [availability] = api.user.getPersonAvailabilityAdmin.useSuspenseQuery({
    storeId,
    personId,
  });

  const [state] = React.useState(
          availability.draft && availability.draft.ranges.length ? "draft" : "pending"
  );
  const availabilityRanges =
          state === "draft" && availability.draft?.ranges
                  ? availability.draft.ranges
                  : availability.pending?.ranges?.length
                          ? availability.pending.ranges
                          : [];

  const availabilityDayRanges = filter(
          availabilityRanges,
          (r) => r.dayOfWeek === day?.dayOfWeek
  );
  const noAvailability = !availabilityDayRanges.length;

  const initRanges = React.useMemo(() => {
    const daysRanges = filter(availabilityRanges, (range) => {
      return range.dayOfWeek === dayOfWeek;
    });
    const ranges = map(daysRanges, (range) => ({
      start: parseDateFromTimeOfDay(range.start, timezone),
      end: parseDateFromTimeOfDay(range.end, timezone),
    }));
    return ranges.length ? ranges : [{start: null, end: null}];
  }, [availabilityRanges, dayOfWeek, timezone]);

  const initAllDay = React.useMemo(() => {
    const thisDaysRanges = filter(availabilityRanges, (range) => range.dayOfWeek === dayOfWeek);
    if (thisDaysRanges.length === 0) return false;
    if (thisDaysRanges.length > 1) return false;
    return isTimeAllDay(thisDaysRanges[0]);
  }, [availabilityRanges, dayOfWeek]);

  const setPersonAvailabilityDayRangesAdmin = api.user.setPersonAvailabilityDayRangesAdmin.useMutation({
    onSuccess: async () => {
      await apiUtil.user.getPersonAvailabilityAdmin.invalidate({storeId, personId});
      await apiUtil.user.getAvailabilityRequestDetails.invalidate({
        personAvailabilityId: personAvailabilityId
      })
      await apiUtil.user.getPersonAvailabilityAdmin.invalidate({
        storeId: storeId!, personId: personId!
      })

      onOpenChange(false);
    },
    onError: (error) => {
      alert("An error occurred while saving your availability. Please try again later: " + error.message);
    }
  });

  const findDayFromAbbr = (d: string): DayOfWeek | undefined => find(daysOfWeek, {abbr: d});
  const form = useForm({
    defaultValues: {
      allDay: initAllDay,
      times: initRanges as { start: Date | null; end: Date | null }[],
      daysCopy: [find(daysOfWeek, {dayOfWeek: dayOfWeek})?.abbr!] as string[],
    },
    onSubmit: async ({value}) => {
      const days = map(value.daysCopy, d => findDayFromAbbr(d)?.dayOfWeek);

      let times = value.allDay
              ? [{
                start: DateTime.fromObject({hour: 0, minute: 0}, {zone: timezone}).toJSDate(),
                end: DateTime.fromObject({hour: 23, minute: 59}, {zone: timezone}).toJSDate(),
              }]
              // ignore rows where both start and end are empty
              : value.times.filter(time => time.start && time.end);

      setPersonAvailabilityDayRangesAdmin.mutate({
        id: personAvailabilityId,
        personId: personId!,
        storeId: storeId!,
        ranges: times.map(time => ({
          start: formatTimeOfDay(time.start!, timezone),
          end: formatTimeOfDay(time.end!, timezone),
        })),
        days: new Set(compact(days)),
      });
    },
  });

  useEffect(() => {
    const times = form.store.state.values.times;
    const firstTime = times?.[0];

    const isCurrentlyAllDay =
            times.length === 1 &&
            firstTime?.start &&
            firstTime?.end &&
            DateTime.fromJSDate(firstTime.start).toFormat("HH:mm") === "00:00" &&
            DateTime.fromJSDate(firstTime.end).toFormat("HH:mm") === "23:59";

    if (form.store.state.values.allDay) {
      form.setFieldValue("times", [
        {
          start: DateTime.fromObject({hour: 0, minute: 0}, {zone: timezone}).toJSDate(),
          end: DateTime.fromObject({hour: 23, minute: 59}, {zone: timezone}).toJSDate(),
        },
      ]);
    }
  }, [form.store.state.values.allDay, timezone]);

  const validateTimes = (times: { start: Date | null; end: Date | null }[]) => {
    if (every(times, (time) => !time.start && !time.end)) return undefined;

    const incompleteTimes = times.some((time) => (time.start && !time.end) || (!time.start && time.end));
    if (incompleteTimes) return t`Both start and end times must be set for each time range.`;

    const completeTimes = times.filter((time) => time.start && time.end);
    const startBeforeEnds = every(completeTimes, (time) =>
            !isBefore(time.end!, addMinutes(time.start!, 15))
    );
    if (!startBeforeEnds) return t`End time must be later than start time.`;

    const endsAfterPreviousStarts = every(completeTimes.slice(1), (time, index) => {
      return !isBefore(time.start!, completeTimes[index].end!);
    });
    if (!endsAfterPreviousStarts) return t`Times cannot overlap.`;
  };

  // set form values when the modal is first opened
  useEffect(() => {
    if (isOpen) {
      form.reset({
        allDay: initAllDay,
        times: initRanges,
        daysCopy: [find(daysOfWeek, {dayOfWeek: dayOfWeek})?.abbr!],
      });
    }
  }, [isOpen]);

  const totalHours = reduce(availabilityDayRanges, (acc, range) => acc + getDurationHours(range), 0);
  const totalHoursRounded = totalHours ? Math.round(totalHours * 10) / 10 : null;

  const formatDecimalHours = (start: Date | null, end: Date | null): string => {
    if (!start || !end) return "0 h";
    const diffMs = end.getTime() - start.getTime();
    if (diffMs <= 0) return "0 h";

    const totalHours = diffMs / (1000 * 60 * 60);
    const roundedHours = Math.round(totalHours * 10) / 10;

    return `${roundedHours % 1 === 0 ? roundedHours.toFixed(0) : roundedHours.toFixed(1)} h`;
  };

  const formatTime = (time: string) =>
          DateTime.fromFormat(time, "HH:mm", {zone: timezone}).toFormat("h:mm a");


  return (
          <Dialog open={isOpen} onOpenChange={onOpenChange}>
            <DialogContent className="max-w-lg">
              <DialogHeader>
                <DialogTitle>Edit Availability</DialogTitle>
                <DialogDescription>
                  Edit the availability of {day?.name}
                </DialogDescription>
              </DialogHeader>


              <hr className="mb-2"/>
              <TeamMemberPressable
                      person={person!}
                      className="mb-0.5"
                      Subtext={
                        <Text size="sm" muted>
                          {person!.jobTitle}
                        </Text>
                      }
                      RightElem={
                        <div className="flex flex-row justify-end items-center">
                          <ProficiencyRating
                                  rank={person!.proficiencyRanking ?? 0}
                                  colorScheme="light-bg"
                                  size={16}
                          />
                        </div>
                      }
              />
              <hr className="mb-2 mt-2"/>
              <Text className="text-sm font-semibold">Current</Text>
              <div className="flex flex-row gap-2">
                <div className="h-14 w-14 border border-gray-300 flex flex-row rounded-lg justify-center items-center">
                  <Text>{day?.abbr}</Text>
                </div>

                <div
                        className={`
                h-14 flex-1 border border-gray-300 flex flex-row rounded-lg justify-start items-center
                border-l-8 ${noAvailability ? 'border-l-gray-300' : 'border-l-green-500'}
                px-2 overflow-x-auto hover:bg-gray-50
                text-left
              `}
                >
                  {noAvailability ? (
                          <Text className="ml-4 text-sm">
                            No Availability
                          </Text>
                  ) : (
                          <div className="flex flex-col ml-4">
                            <div className="flex flex-row gap-2 items-center">
                              {map(availabilityDayRanges, (range, idx) => (
                                      <Text key={idx}
                                            className={`text-sm ${idx > 0 ? 'border-l border-gray-300 pl-2' : ''}`}>
                                        {formatTime(range.start)} - {formatTime(range.end)}
                                      </Text>
                              ))}
                            </div>
                            {totalHoursRounded && (
                                    <Text className="text-sm">
                                      {totalHoursRounded}h
                                    </Text>
                            )}
                          </div>
                  )}
                </div>
              </div>
              <hr className="mb-2 mt-2"/>
              <Text className="text-sm font-semibold">New</Text>

              <form
                      onSubmit={(e) => {
                        e.preventDefault();
                        form.handleSubmit();
                      }}
                      className="space-y-6"
              >
                {/* All Day Switch & Time Picker (collapsable) */}
                <div>
                  <form.Field
                          name={"allDay"}
                          children={field => (
                                  <div className="flex flex-col gap-4">
                                    <div className="flex border p-2 rounded-md items-center justify-between">
                                      <Label>All Day</Label>
                                      <FormSwitch field={field}/>
                                    </div>

                                    {!field.state.value && (
                                            form.Field({
                                              name: "times",
                                              mode: "array",
                                              validators: {
                                                onSubmit: ({value}) => validateTimes(value),
                                                onBlur: ({value}) => validateTimes(value),
                                              },
                                              children: (arrField) => (
                                                      <div className="flex flex-col gap-2">
                                                        <div className="max-h-64 overflow-y-auto flex flex-col gap-4">
                                                          {arrField.state.value.map((_time, i) => (
                                                                  <div key={i} className="flex items-end gap-2 pb-2">
                                                                    <div className="flex-1">
                                                                      <Label>Start Time</Label>
                                                                      {form.Field({
                                                                        name: `times[${i}].start`,
                                                                        children: (field) => (
                                                                                <FormTimePicker
                                                                                        field={field}
                                                                                        timezone={timezone}
                                                                                        placeholder="00:00"
                                                                                        minutesStep={5}
                                                                                />
                                                                        )
                                                                      })}
                                                                    </div>
                                                                    <div className="flex-1">
                                                                      <Label>End Time</Label>
                                                                      {form.Field({
                                                                        name: `times[${i}].end`,
                                                                        children: (field) => (
                                                                                <FormTimePicker
                                                                                        field={field}
                                                                                        timezone={timezone}
                                                                                        placeholder="00:00"
                                                                                        minutesStep={5}
                                                                                />
                                                                        )
                                                                      })}
                                                                    </div>
                                                                    <div className="flex-1">
                                                                      <Label>Duration</Label>
                                                                      {form.Field({
                                                                        name: `times[${i}].start`,
                                                                        children: (startField) => (
                                                                                form.Field({
                                                                                  name: `times[${i}].end`,
                                                                                  children: (endField) => (
                                                                                          <Input
                                                                                                  value={formatDecimalHours(startField.state.value, endField.state.value)}
                                                                                                  readOnly
                                                                                          />
                                                                                  )
                                                                                })
                                                                        )
                                                                      })}
                                                                    </div>
                                                                    <Button
                                                                            type="button"
                                                                            variant="ghost"
                                                                            size="icon"
                                                                            onClick={() => {
                                                                              const next = [...arrField.state.value];
                                                                              next.splice(i, 1);
                                                                              if (next.length === 0) next.push({
                                                                                start: null,
                                                                                end: null
                                                                              });
                                                                              arrField.setValue(next);
                                                                              arrField.handleBlur();
                                                                            }}
                                                                    >
                                                                      <TrashIcon size={16} className="text-red-400"/>
                                                                    </Button>
                                                                  </div>
                                                          ))}
                                                          {arrField.state.meta.errors?.length > 0 && (
                                                                  <p className="text-sm text-red-600">
                                                                    {arrField.state.meta.errors}
                                                                  </p>
                                                          )}
                                                        </div>
                                                        <Button
                                                                className="max-w-min"
                                                                variant="outline"
                                                                type="button"
                                                                leftIcon={<PlusIcon size={12}/>}
                                                                onClick={() => arrField.pushValue({
                                                                  start: null,
                                                                  end: null
                                                                })}
                                                        >
                                                          Add Additional Availability
                                                        </Button>
                                                      </div>
                                              ),
                                            })
                                    )}
                                  </div>
                          )}
                  />
                </div>

                {/* Copy To Other Days */}
                <div className="flex flex-col gap-2">
                  <Label>Copy to other days</Label>
                  <form.Field
                          name={"daysCopy"}
                          children={field => (
                                  <div className="flex flex-wrap gap-2">
                                    {daysOfWeek.map((day) => {
                                      const isCurrent = dayOfWeek ? day.dayOfWeek === dayOfWeek : false;
                                      const isChecked = field.state.value.includes(day.abbr);

                                      return (
                                              <label
                                                      key={day.abbr}
                                                      className={`px-3 py-1 border rounded cursor-pointer ${isChecked ? "bg-blue-500 text-white" : ""} ${isCurrent ? "opacity-50 pointer-events-none" : ""}`}
                                              >
                                                <input
                                                        type="checkbox"
                                                        checked={isChecked}
                                                        disabled={isCurrent || day.dayOfWeek === 7}
                                                        onChange={(e) => {
                                                          const next = new Set(field.state.value);
                                                          if (e.target.checked) next.add(day.abbr);
                                                          else next.delete(day.abbr);
                                                          field.handleChange(Array.from(next));
                                                        }}
                                                        className="hidden"
                                                />
                                                {day.abbr}
                                              </label>
                                      );
                                    })}
                                  </div>
                          )}
                  />
                </div>

                {/* Footer Buttons */}
                <div className="flex justify-end gap-3">
                  <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
                    Cancel
                  </Button>
                  <Button type="submit" isLoading={setPersonAvailabilityDayRangesAdmin.isPending}>
                    Save
                  </Button>
                </div>
              </form>
            </DialogContent>
          </Dialog>
  );
}
