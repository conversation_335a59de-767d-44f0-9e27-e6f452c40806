import { describe, it, expect } from 'vitest';
import { parseHotkeysForPlatform } from './parseHotkeys';

describe('parseHotkeysForPlatform', () => {
  describe('macOS', () => {
    const isMac = true;
    const isWin = false;

    it('should parse single key', () => {
      expect(parseHotkeysForPlatform(isMac, isWin, 'a')).toBe('A');
    });

    it('should parse single modifier', () => {
      expect(parseHotkeysForPlatform(isMac, isWin, 'cmd')).toBe('⌘');
    });

    it('should parse modifier + key', () => {
      expect(parseHotkeysForPlatform(isMac, isWin, 'cmd+a')).toBe('⌘A');
    });

    it('should parse multiple modifiers + key', () => {
      expect(parseHotkeysForPlatform(isMac, isWin, 'cmd+shift+a')).toBe('⌘⇧A');
    });

    it('should parse multiple hotkeys', () => {
      expect(parseHotkeysForPlatform(isMac, isWin, 'cmd+a, ctrl+b')).toBe('⌘A, ⌃B');
    });

    it('should handle special keys', () => {
      expect(parseHotkeysForPlatform(isMac, isWin, 'cmd+space')).toBe('⌘␣');
    });

    it('should handle complex combinations', () => {
      expect(parseHotkeysForPlatform(isMac, isWin, 'ctrl+shift+a+c, c, shift+c, alt+n, ctrl+d, meta+d'))
        .toBe('⌃⇧AC, C, ⇧C, ⌥N, ⌃D, ⌘D');
    });
  });

  describe('Windows', () => {
    const isMac = false;
    const isWin = true;

    it('should parse single key', () => {
      expect(parseHotkeysForPlatform(isMac, isWin, 'a')).toBe('A');
    });

    it('should parse single modifier', () => {
      expect(parseHotkeysForPlatform(isMac, isWin, 'ctrl')).toBe('Ctrl');
    });

    it('should parse modifier + key', () => {
      expect(parseHotkeysForPlatform(isMac, isWin, 'ctrl+a')).toBe('Ctrl+A');
    });

    it('should parse multiple modifiers + key', () => {
      expect(parseHotkeysForPlatform(isMac, isWin, 'ctrl+shift+a')).toBe('Ctrl+Shift+A');
    });

    it('should parse multiple hotkeys', () => {
      expect(parseHotkeysForPlatform(isMac, isWin, 'ctrl+a, alt+b')).toBe('Ctrl+A, Alt+B');
    });

    it('should handle special keys', () => {
      expect(parseHotkeysForPlatform(isMac, isWin, 'ctrl+space')).toBe('Ctrl+Space');
    });

    it('should handle complex combinations', () => {
      expect(parseHotkeysForPlatform(isMac, isWin, 'ctrl+shift+a+c, c, shift+c, alt+n, ctrl+d, meta+d'))
        .toBe('Ctrl+Shift+A+C, C, Shift+C, Alt+N, Ctrl+D, Win+D');
    });
  });

  describe('Other platforms', () => {
    const isMac = false;
    const isWin = false;

    it('should parse single key', () => {
      expect(parseHotkeysForPlatform(isMac, isWin, 'a')).toBe('A');
    });

    it('should parse single modifier', () => {
      expect(parseHotkeysForPlatform(isMac, isWin, 'ctrl')).toBe('Ctrl');
    });

    it('should parse modifier + key', () => {
      expect(parseHotkeysForPlatform(isMac, isWin, 'ctrl+a')).toBe('Ctrl+A');
    });

    it('should parse multiple modifiers + key', () => {
      expect(parseHotkeysForPlatform(isMac, isWin, 'ctrl+shift+a')).toBe('Ctrl+Shift+A');
    });

    it('should parse multiple hotkeys', () => {
      expect(parseHotkeysForPlatform(isMac, isWin, 'ctrl+a, alt+b')).toBe('Ctrl+A, Alt+B');
    });

    it('should handle special keys', () => {
      expect(parseHotkeysForPlatform(isMac, isWin, 'ctrl+space')).toBe('Ctrl+Space');
    });

    it('should handle complex combinations', () => {
      expect(parseHotkeysForPlatform(isMac, isWin, 'ctrl+shift+a+c, c, shift+c, alt+n, ctrl+d, meta+d'))
        .toBe('Ctrl+Shift+A+C, C, Shift+C, Alt+N, Ctrl+D, Meta+D');
    });
  });

  describe('Edge cases', () => {
    it('should handle empty string', () => {
      expect(parseHotkeysForPlatform(false, false, '')).toBe('');
    });

    it('should handle whitespace', () => {
      expect(parseHotkeysForPlatform(false, false, '  ctrl + a  ,  alt + b  ')).toBe('Ctrl+A, Alt+B');
    });

    it('should handle unknown keys on Mac', () => {
      expect(parseHotkeysForPlatform(true, false, 'cmd+unknown')).toBe('⌘Unknown');
    });

    it('should handle case insensitivity on Windows', () => {
      expect(parseHotkeysForPlatform(false, true, 'CTRL+SHIFT+a')).toBe('Ctrl+Shift+A');
    });
  });
});
