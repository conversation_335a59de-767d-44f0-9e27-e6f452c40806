import React from 'react';
import {<PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ooter, <PERSON><PERSON>Header, DialogTitle} from "@/src/components/ui/dialog.tsx";
import {Button} from "@/src/components/ui/button.tsx";
import {api} from "@/src/api.ts";
import {getHumanReadableErrorMessage} from "@/src/components/ErrorAlert.tsx";
import {toast} from "sonner";

export interface CreateHouseShiftOfferDialogProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  scheduleId: string;
  latestServerVersion: React.MutableRefObject<number>;
  shiftId: string;
}

export const CreateHouseShiftOfferDialog: React.FC<CreateHouseShiftOfferDialogProps> = ({
                                                                                          isOpen,
                                                                                          onOpenChange,
                                                                                          scheduleId,
                                                                                          latestServerVersion,
                                                                                          shiftId
                                                                                        }) => {
  const apiUtil = api.useUtils();
  const createHouseShiftOffer = api.scheduling.createHouseShiftOffer.useMutation();

  const doCreateHouseShiftOffer = () => {
    createHouseShiftOffer.mutate({
      scheduleId: scheduleId,
      draftVersion: latestServerVersion.current,
      shiftId: shiftId,
      reason: "House offer"
    }, {
      onSuccess() {
        apiUtil.user.getSchedule.invalidate();
        apiUtil.user.getScheduleWeeks.invalidate();
        toast.success("House shift offer created", {
          position: "top-center",
        });
        onOpenChange(false);
      },
      onError(error) {
        alert("Error creating house shift offer: " + getHumanReadableErrorMessage(error));
      }
    })
  }

  const onCancel = () => {
    onOpenChange(false);
  }

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Create House Shift Offer</DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          This will publish the shift and a house offer on it. The offer will be sent to all eligible Team
          Members in the store.
          Do you want to continue?
        </div>

        <DialogFooter>
          <Button variant={"outline"} disabled={createHouseShiftOffer.isPending}
                  type={"button"}
                  onClick={onCancel}>
            Cancel
          </Button>
          <Button type={"button"} isLoading={createHouseShiftOffer.isPending}
                  onClick={doCreateHouseShiftOffer}>
            Continue
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
