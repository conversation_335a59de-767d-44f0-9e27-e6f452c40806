import React, {useState} from 'react';
import {<PERSON><PERSON>, <PERSON>alog<PERSON>ontent, <PERSON>alogHeader, DialogTitle} from '@/src/components/ui/dialog';
import {Button} from '@/src/components/ui/button';
import {RadioGroup, RadioGroupItem} from '@/src/components/ui/radio-group';
import {Label} from '@/src/components/ui/label';
import {Select, SelectContent, SelectItem, SelectTrigger, SelectValue} from '@/src/components/ui/select';
import {Input} from '@/src/components/ui/input';
import {DateTime} from 'luxon';
import {Text} from './Text';
import {Tabs, TabsContent, TabsList, TabsTrigger} from '@/src/components/ui/tabs';
import {getRecurrenceString} from '@gioa/api/src/calendar/formRecurrence';
import {
  MonthDayNum,
  MonthlyDaysRecurrenceSchedule,
  MonthlyOnRecurrenceSchedule,
  RecurrenceMonthOn,
  RecurrenceSchedule,
  WeekdayNum
} from '@gioa/api/src/calendar/recurrence.types';
import {cn} from "@/src/util.ts";

interface RecurrencePickerDialogProps {
  isOpen: boolean;
  onClose: () => void;
  startDate?: Date;
  schedule: RecurrenceSchedule | null;
  timezone: string;
  onSelect: (schedule: RecurrenceSchedule | null) => void;
}

export function RecurrencePickerDialog({
                                         isOpen,
                                         onClose,
                                         startDate,
                                         schedule,
                                         timezone,
                                         onSelect
                                       }: RecurrencePickerDialogProps) {
  // Initialize with default tab based on existing schedule
  const getInitialTab = () => {
    if (!schedule) return "daily";
    if (schedule.unit === "days") return "daily";
    if (schedule.unit === "weeks") return "weekly";
    if (schedule.unit === "months") {
      if ("monthDays" in schedule) return "monthly-day";
      if ("monthOn" in schedule) return "monthly-on";
    }
    if (schedule.unit === "years") return "yearly";
    return "daily";
  };

  const [activeTab, setActiveTab] = useState(getInitialTab());

  // Daily recurrence
  const [dailyInterval, setDailyInterval] = useState(schedule?.unit === "days" ? schedule.numUnits : 1);

  // Weekly recurrence
  const [weeklyInterval, setWeeklyInterval] = useState(schedule?.unit === "weeks" ? schedule.numUnits : 1);
  const [selectedWeekdays, setSelectedWeekdays] = useState<WeekdayNum[]>(
    schedule?.unit === "weeks" ? schedule.weekDays :
      startDate ? [DateTime.fromJSDate(startDate).weekday as WeekdayNum] : [1]
  );

  // Monthly recurrence - by day
  const [monthlyDayInterval, setMonthlyDayInterval] = useState(schedule?.unit === "months" && "monthDays" in schedule ? schedule.numUnits : 1);
  const [selectedMonthDays, setSelectedMonthDays] = useState<MonthDayNum[]>(
    schedule?.unit === "months" && "monthDays" in schedule ? Array.from(schedule.monthDays) :
      startDate ? [DateTime.fromJSDate(startDate).day as MonthDayNum] : [1]
  );

  // Monthly recurrence - by position
  const [monthlyOnInterval, setMonthlyOnInterval] = useState(schedule?.unit === "months" && "monthOn" in schedule ? schedule.numUnits : 1);
  const [selectedMonthOn, setSelectedMonthOn] = useState<RecurrenceMonthOn>(
    schedule?.unit === "months" && "monthOn" in schedule ? schedule.monthOn :
      ["1st", 1]
  );

  // End options
  const [endType, setEndType] = useState<"never" | "after" | "on">(
    schedule?.end ? "on" :
      schedule?.endAfterCount ? "after" : "never"
  );
  const [endAfterCount, setEndAfterCount] = useState(schedule?.endAfterCount || 10);
  const [endDate, setEndDate] = useState<Date | undefined>(
    schedule?.end || DateTime.now().plus({months: 3}).toJSDate()
  );

  const weekdayToName = (day: WeekdayNum): string => {
    const days = ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"];
    return days[day - 1];
  };

  // Create the recurrence schedule based on the selected options
  const createRecurrenceSchedule = (): RecurrenceSchedule | null => {
    let schedule: RecurrenceSchedule;

    // Set end options
    const baseOptions = {
      end: endType === "on" ? endDate : undefined,
      endAfterCount: endType === "after" ? endAfterCount : undefined
    };

    switch (activeTab) {
      case "daily":
        schedule = {
          unit: "days",
          numUnits: dailyInterval,
          ...baseOptions
        };
        break;

      case "weekly":
        if (selectedWeekdays.length === 0) return null;
        schedule = {
          unit: "weeks",
          numUnits: weeklyInterval,
          weekDays: selectedWeekdays,
          ...baseOptions
        };
        break;

      case "monthly-day":
        if (selectedMonthDays.length === 0) return null;
        schedule = {
          unit: "months",
          numUnits: monthlyDayInterval,
          monthDays: new Set(selectedMonthDays),
          ...baseOptions
        } as MonthlyDaysRecurrenceSchedule;
        break;

      case "monthly-on":
        schedule = {
          unit: "months",
          numUnits: monthlyOnInterval,
          monthOn: selectedMonthOn,
          ...baseOptions
        } as MonthlyOnRecurrenceSchedule;
        break;

      default:
        return null;
    }

    return schedule;
  };

  const handleSave = () => {
    const newSchedule = createRecurrenceSchedule();
    onSelect(newSchedule);
  };

  const handleRemove = () => {
    onSelect(null);
  };

  // Generate a human-readable description of the recurrence
  const getRecurrenceDescription = (): string => {
    const schedule = createRecurrenceSchedule();
    if (!schedule || !startDate) return "";

    return getRecurrenceString(schedule, DateTime.fromJSDate(startDate, {zone: timezone}));
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="max-w-md overflow-auto"
                     position={"fixedTop"}
                     style={{maxHeight: "calc(100vh - 50px)"}}>
        <DialogHeader>
          <DialogTitle>Repeat</DialogTitle>
        </DialogHeader>

        <div className="py-4">
          {/* Summary of current selection */}
          <div className="mb-4 p-3 bg-gray-50 rounded-md">
            <Text className="font-medium">{getRecurrenceDescription() || "Does not repeat"}</Text>
          </div>

          {/* Tabs for different recurrence types */}
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="grid grid-cols-4 mb-4">
              <TabsTrigger value="daily">Daily</TabsTrigger>
              <TabsTrigger value="weekly">Weekly</TabsTrigger>
              <TabsTrigger value="monthly-day">Monthly</TabsTrigger>
              <TabsTrigger value="monthly-on">Monthly On</TabsTrigger>
            </TabsList>

            {/* Daily tab */}
            <TabsContent value="daily" className="space-y-4">
              <div className="flex items-center gap-2">
                <Label>Every</Label>
                <Input containerClassName={"w-auto"}
                       type="number"
                       min={1}
                       max={99}
                       value={dailyInterval}
                       onChange={(e) => setDailyInterval(parseInt(e.target.value) || 1)}
                       className="w-16"
                />
                <Text>day(s)</Text>
              </div>
            </TabsContent>

            {/* Weekly tab */}
            <TabsContent value="weekly" className="space-y-4">
              <div className="flex items-center gap-2 mb-4">
                <Label>Every</Label>
                <Input containerClassName={"w-auto"}
                       type="number"
                       min={1}
                       max={99}
                       value={weeklyInterval}
                       onChange={(e) => setWeeklyInterval(parseInt(e.target.value) || 1)}
                       className="w-16"
                />
                <Text>week(s)</Text>
              </div>

              <div className="space-y-2">
                <Label>Repeat on</Label>
                <div className="grid grid-cols-7 gap-2">
                  {[1, 2, 3, 4, 5, 6, 7].map((day) => (
                    <div
                      key={day}
                      className={cn(
                        "flex items-center justify-center h-10 rounded-md cursor-pointer text-sm",
                        selectedWeekdays.includes(day as WeekdayNum)
                          ? "bg-primary text-white"
                          : "bg-gray-100 hover:bg-gray-200"
                      )}
                      onClick={() => {
                        if (selectedWeekdays.includes(day as WeekdayNum)) {
                          if (selectedWeekdays.length > 1) {
                            setSelectedWeekdays(selectedWeekdays.filter(d => d !== day));
                          }
                        } else {
                          setSelectedWeekdays([...selectedWeekdays, day as WeekdayNum]);
                        }
                      }}
                    >
                      {weekdayToName(day as WeekdayNum).substring(0, 1)}
                    </div>
                  ))}
                </div>
              </div>
            </TabsContent>

            {/* Monthly by day tab */}
            <TabsContent value="monthly-day" className="space-y-4">
              <div className="flex items-center gap-2 mb-4">
                <Label>Every</Label>
                <Input containerClassName={"w-auto"}
                       type="number"
                       min={1}
                       max={99}
                       value={monthlyDayInterval}
                       onChange={(e) => setMonthlyDayInterval(parseInt(e.target.value) || 1)}
                       className="w-16"
                />
                <Text>month(s)</Text>
              </div>

              <div className="space-y-2">
                <Label>On day(s)</Label>
                <div className="grid grid-cols-7 gap-1">
                  {Array.from({length: 31}, (_, i) => i + 1).map((day) => (
                    <div role={"button"}
                         key={day} title={`${day} of the month`}
                         className={cn(
                           "flex items-center justify-center h-10 rounded-md cursor-pointer text-sm",
                           selectedMonthDays.includes(day as MonthDayNum)
                             ? "bg-primary text-white"
                             : "bg-gray-100 hover:bg-gray-200"
                         )}
                         onClick={() => {
                           if (selectedMonthDays.includes(day as MonthDayNum)) {
                             if (selectedMonthDays.length > 1) {
                               setSelectedMonthDays(selectedMonthDays.filter(d => d !== day));
                             }
                           } else {
                             setSelectedMonthDays([...selectedMonthDays, day as MonthDayNum]);
                           }
                         }}
                    >
                      {day}
                    </div>
                  ))}
                </div>
              </div>
            </TabsContent>

            {/* Monthly by position tab */}
            <TabsContent value="monthly-on" className="space-y-4">
              <div className="flex items-center gap-2 mb-4">
                <Label>Every</Label>
                <Input containerClassName={"w-auto"}
                       type="number"
                       min={1}
                       max={99}
                       value={monthlyOnInterval}
                       onChange={(e) => setMonthlyOnInterval(parseInt(e.target.value) || 1)}
                       className="w-16"
                />
                <Text>month(s)</Text>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="mb-2 block">Position</Label>
                  <Select
                    value={selectedMonthOn[0]}
                    onValueChange={(value) => setSelectedMonthOn([value as "1st" | "2nd" | "3rd" | "4th" | "5th" | "last", selectedMonthOn[1]])}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select position"/>
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="1st">First</SelectItem>
                      <SelectItem value="2nd">Second</SelectItem>
                      <SelectItem value="3rd">Third</SelectItem>
                      <SelectItem value="4th">Fourth</SelectItem>
                      <SelectItem value="5th">Fifth</SelectItem>
                      <SelectItem value="last">Last</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label className="mb-2 block">Day</Label>
                  <Select
                    value={selectedMonthOn[1].toString()}
                    onValueChange={(value) => setSelectedMonthOn([selectedMonthOn[0], parseInt(value) as WeekdayNum])}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select day"/>
                    </SelectTrigger>
                    <SelectContent>
                      {[1, 2, 3, 4, 5, 6, 7].map((day) => (
                        <SelectItem key={day} value={day.toString()}>
                          {weekdayToName(day as WeekdayNum)}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </TabsContent>
          </Tabs>

          {/* End options */}
          <div className="mt-6 pt-6 border-t border-gray-200">
            <Label className="mb-4 block">End</Label>
            <RadioGroup value={endType} onValueChange={(value) => setEndType(value as "never" | "after" | "on")}>
              <div className="flex items-center gap-2 mb-2">
                <RadioGroupItem value="never" id="never"/>
                <Label htmlFor="never">Never</Label>
              </div>
              <div className="flex items-center gap-2">
                <RadioGroupItem value="after" id="after"/>
                <Label htmlFor="after">After</Label>
                <div className={"flex flex-row gap-2 items-center"}>
                  <Input containerClassName={"w-auto"}
                         type="number"
                         min={1}
                         max={999}
                         value={endAfterCount}
                         onChange={(e) => setEndAfterCount(parseInt(e.target.value) || 1)}
                         className="w-16"
                         disabled={endType !== 'after'}
                  />
                  <div>occurrences</div>
                </div>
              </div>
              <div className="flex items-center gap-2">
                <RadioGroupItem value="on" id="on"/>
                <Label htmlFor="on">On</Label>
                <Input
                  type="date" className="w-36"
                  value={endDate ? DateTime.fromJSDate(endDate).toFormat('yyyy-MM-dd') : ''}
                  onChange={(e) => {
                    if (e.target.value) {
                      setEndDate(DateTime.fromFormat(e.target.value, 'yyyy-MM-dd').toJSDate());
                    }
                  }}
                  disabled={endType !== 'on'}
                />
              </div>
            </RadioGroup>
          </div>
        </div>

        <div className="flex justify-between gap-2 flex-wrap">
          <Button variant="outline" onClick={handleRemove}>
            Remove
          </Button>
          <div className="space-x-2">
            <Button variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button onClick={handleSave}>
              Save
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
