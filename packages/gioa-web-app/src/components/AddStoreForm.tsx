import React, {useState} from 'react';
import {map} from "lodash";
import {Heading} from "@/src/components/Heading.tsx";
import {FormControl} from "@/src/components/form/FormControl.tsx";
import {Label} from "@/src/components/ui/label.tsx";
import {FormInput} from "@/src/components/form/FormInput.tsx";
import {FieldInfo} from "@/src/components/form/FieldInfo.tsx";
import {Select, SelectContent, SelectItem, SelectTrigger, SelectValue} from "@/src/components/ui/select.tsx";
import {timezones} from "../../../api/src/timezones.ts";
import {getGMTOffsetLabel} from "../../../api/src/date.util.ts";
import {ReactFormExtendedApi, Validator} from '@tanstack/react-form';
import {z, ZodType} from 'zod';
import {FormRadioGroup} from "@/src/components/form/FormRadioGroup.tsx";
import {DateTime} from "luxon";
import {Text} from "@/src/components/Text.tsx";
import {defaultTrialPeriodDays} from "../../../api/src/payments/subscriptionSchemas.ts";

export interface AddStoreFormProps {
  form: ReactFormExtendedApi<any, Validator<unknown, ZodType>>
  prefix: string
}

export const AddStoreForm: React.FC<AddStoreFormProps> = ({form, prefix}) => {
  const [trialDays, setTrialDays] = useState<number>(defaultTrialPeriodDays);
  const trialEndsOn = trialDays && Number.isFinite(trialDays) && trialDays > 0 ? DateTime.now().plus({days: trialDays}) : DateTime.now()

  return <>
    <form.Field name={`${prefix}title`}
                validators={{
                  onSubmit: z.string().min(1)
                }}
                children={(field) => {
                  return <FormControl>
                    <Label htmlFor={field.name}>Store Title</Label>
                    <FormInput field={field}
                               placeholder="Enter title..."/>
                    <FieldInfo field={field}/>
                  </FormControl>;
                }}/>

    <form.Field name={`${prefix}chickfilaStoreId`}
                validators={{
                  onSubmit: z.string().min(1)
                }}
                children={(field) => {
                  return <FormControl>
                    <Label htmlFor={field.name}>Chick-fil-A Store ID</Label>
                    <FormInput field={field}
                               placeholder="Enter Store ID..."/>
                    <FieldInfo field={field}/>
                  </FormControl>;
                }}/>

    <Heading level={4} size={"xxs"}>
      Store Location
    </Heading>
    <form.Field name={`${prefix}location.line1`}
                validators={{
                  onSubmit: z.string().min(1)
                }}
                children={(field) => {
                  return <FormControl>
                    <Label htmlFor={field.name}>Address Line 1</Label>
                    <FormInput field={field}
                               placeholder="Enter address..."/>
                    <FieldInfo field={field}/>
                  </FormControl>;
                }}/>
    <form.Field name={`${prefix}location.line2`}
                children={(field) => {
                  return <FormControl>
                    <Label htmlFor={field.name}>Address Line 2</Label>
                    <FormInput field={field}
                               placeholder="Enter address..."/>
                    <FieldInfo field={field}/>
                  </FormControl>;
                }}/>
    <form.Field name={`${prefix}location.city`}
                validators={{
                  onSubmit: z.string().min(1)
                }}
                children={(field) => {
                  return <FormControl>
                    <Label htmlFor={field.name}>City</Label>
                    <FormInput field={field}
                               placeholder="Enter city..."/>
                    <FieldInfo field={field}/>
                  </FormControl>;
                }}/>
    <form.Field name={`${prefix}location.state`}
                validators={{
                  onSubmit: z.string().min(1)
                }}
                children={(field) => {
                  return <FormControl>
                    <Label htmlFor={field.name}>State</Label>
                    <Select onValueChange={field.handleChange}
                            value={field.state.value}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select a state..."/>
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="AL">Alabama</SelectItem>
                        <SelectItem value="AK">Alaska</SelectItem>
                        <SelectItem value="AZ">Arizona</SelectItem>
                        <SelectItem value="AR">Arkansas</SelectItem>
                        <SelectItem value="CA">California</SelectItem>
                        <SelectItem value="CO">Colorado</SelectItem>
                        <SelectItem value="CT">Connecticut</SelectItem>
                        <SelectItem value="DE">Delaware</SelectItem>
                        <SelectItem value="FL">Florida</SelectItem>
                        <SelectItem value="GA">Georgia</SelectItem>
                        <SelectItem value="HI">Hawaii</SelectItem>
                        <SelectItem value="ID">Idaho</SelectItem>
                        <SelectItem value="IL">Illinois</SelectItem>
                        <SelectItem value="IN">Indiana</SelectItem>
                        <SelectItem value="IA">Iowa</SelectItem>
                        <SelectItem value="KS">Kansas</SelectItem>
                        <SelectItem value="KY">Kentucky</SelectItem>
                        <SelectItem value="LA">Louisiana</SelectItem>
                        <SelectItem value="ME">Maine</SelectItem>
                        <SelectItem value="MD">Maryland</SelectItem>
                        <SelectItem value="MA">Massachusetts</SelectItem>
                        <SelectItem value="MI">Michigan</SelectItem>
                        <SelectItem value="MN">Minnesota</SelectItem>
                        <SelectItem value="MS">Mississippi</SelectItem>
                        <SelectItem value="MO">Missouri</SelectItem>
                        <SelectItem value="MT">Montana</SelectItem>
                        <SelectItem value="NE">Nebraska</SelectItem>
                        <SelectItem value="NV">Nevada</SelectItem>
                        <SelectItem value="NH">New Hampshire</SelectItem>
                        <SelectItem value="NJ">New Jersey</SelectItem>
                        <SelectItem value="NM">New Mexico</SelectItem>
                        <SelectItem value="NY">New York</SelectItem>
                        <SelectItem value="NC">North Carolina</SelectItem>
                        <SelectItem value="ND">North Dakota</SelectItem>
                        <SelectItem value="OH">Ohio</SelectItem>
                        <SelectItem value="OK">Oklahoma</SelectItem>
                        <SelectItem value="OR">Oregon</SelectItem>
                        <SelectItem value="PA">Pennsylvania</SelectItem>
                        <SelectItem value="RI">Rhode Island</SelectItem>
                        <SelectItem value="SC">South Carolina</SelectItem>
                        <SelectItem value="SD">South Dakota</SelectItem>
                        <SelectItem value="TN">Tennessee</SelectItem>
                        <SelectItem value="TX">Texas</SelectItem>
                        <SelectItem value="UT">Utah</SelectItem>
                        <SelectItem value="VT">Vermont</SelectItem>
                        <SelectItem value="VA">Virginia</SelectItem>
                        <SelectItem value="WA">Washington</SelectItem>
                        <SelectItem value="WV">West Virginia</SelectItem>
                        <SelectItem value="WI">Wisconsin</SelectItem>
                        <SelectItem value="WY">Wyoming</SelectItem>
                      </SelectContent>
                    </Select>
                    <FieldInfo field={field}/>
                  </FormControl>;
                }}/>
    <form.Field name={`${prefix}location.zipCode`}
                validators={{
                  onSubmit: z.string().min(1)
                }}
                children={(field) => {
                  return <FormControl>
                    <Label htmlFor={field.name}>Zip Code</Label>
                    <FormInput field={field}
                               placeholder="Enter zip code..."/>
                    <FieldInfo field={field}/>
                  </FormControl>;
                }}/>

    <form.Field name={`${prefix}timezone`}
                validators={{
                  onSubmit: z.string().min(1)
                }}
                children={(field) => {
                  return <FormControl>
                    <Label htmlFor={field.name}>Timezone</Label>
                    <Select onValueChange={field.handleChange}
                            value={field.state.value}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select a timezone..."/>
                      </SelectTrigger>
                      <SelectContent>
                        {map(timezones, tz => {
                          return <SelectItem value={tz.id}>
                            {getGMTOffsetLabel(tz)}{' '}
                            {tz.label}
                          </SelectItem>
                        })}
                      </SelectContent>
                    </Select>
                    <FieldInfo field={field}/>
                  </FormControl>;
                }}/>

    <form.Field name={`${prefix}subscription`}
                children={(field) => {
                  return <FormControl>
                    <Label className={"pb-2"}>Subscription</Label>
                    <FormRadioGroup
                      options={[
                        {label: "Create a Subscription", value: "annual"},
                        {label: "None (setup manually in Stripe)", value: "none"},
                      ]}
                      field={field}/>
                    <div className={"text-sm text-muted-foreground"}>
                      Select to create a default Stripe subscription for this store. The default Subscription
                      has no payment method and has a trial starting from when you create this business.
                      If you select "None", then you should go into Stripe after this business is created and set up a Subscription for this store's Customer yourself.
                    </div>
                    <FieldInfo field={field}/>
                  </FormControl>;
                }}/>

    <form.Field name={`${prefix}trialDays`}
                children={(field) => {
                  return <FormControl>
                    <Label htmlFor={field.name}>Trial Days</Label>
                    <FormInput field={field} type={"number"}
                               onChange={(e) => setTrialDays(Number(e.target.value))}
                               placeholder="Enter number of trial days..."/>
                    <Text>
                      Trial ends on {trialEndsOn.toFormat("MM/dd/yyyy")}
                    </Text>
                    <FieldInfo field={field}/>
                  </FormControl>;
                }}/>

  </>
}
