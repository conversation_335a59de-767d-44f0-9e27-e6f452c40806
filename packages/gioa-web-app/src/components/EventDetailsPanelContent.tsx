import React, {useEffect} from 'react';
import {useForm} from "@tanstack/react-form";
import {zodValidator} from "@tanstack/zod-form-adapter";
import {z} from "zod";
import {FormControl} from "@/src/components/form/FormControl.tsx";
import {Label} from "@/src/components/ui/label.tsx";
import {FormInput} from "@/src/components/form/FormInput.tsx";
import {FieldInfo} from "@/src/components/form/FieldInfo.tsx";
import {FormTextarea} from "@/src/components/form/FormTextarea.tsx";
import {Button} from "@/src/components/ui/button.tsx";
import {convertLocalTimeToTimezone} from "../../../api/src/date.util.ts";
import {ScheduleEventDto} from '@gioa/api/src/scheduleEventSchemas.ts';
import {ScheduleCalendars} from "../../../api/src/scheduleCalendars.ts";
import {map} from "lodash";
import {RadioGroup, RadioGroupItem} from './ui/radio-group.tsx';
import {FormCheckbox} from "@/src/components/form/FormCheckbox.tsx";
import {
  getScheduleEventVisibilityLevelNumber,
  getScheduleEventVisibilityLevelString
} from "../../../api/src/scheduleEvents.ts";
import {api} from "@/src/api.ts";
import {formatForDatetimeLocalInput, parseDatetimeLocalInput} from "../../../api/src/scheduleBuilder.util.ts";

export interface EventDetailsPanelContentProps {
  isNewEvent?: boolean;
  event: ScheduleEventDto;
  onUpdateEvent: (event: ScheduleEventDto) => void;
  onDeleteEvent: (event: ScheduleEventDto) => void;
  isLoading?: boolean;
  timezone: string | null;
  storeId: string;
}

export const EventDetailsPanelContent: React.FC<EventDetailsPanelContentProps> = ({
                                                                                    isNewEvent,
                                                                                    event,
                                                                                    onUpdateEvent,
                                                                                    onDeleteEvent,
                                                                                    isLoading,
                                                                                    timezone, storeId,
                                                                                  }) => {
  const storeQuery = api.user.getStoreAdmin.useQuery({storeId: storeId}, {
    staleTime: 1000 * 60 * 60 // 1 hour
  });
  const canRestrictTimeOff = storeQuery.data?.permissions?.canRestrictTimeOff ?? false;

  const form = useForm({
    defaultValues: {
      title: event.title,
      description: event.description,
      eventType: event.eventType,
      range: {
        start: formatForDatetimeLocalInput(event.range.start, timezone),
        end: formatForDatetimeLocalInput(event.range.end, timezone),
      },
      visibilityLevel: getScheduleEventVisibilityLevelString(event.visibilityLevel),
      isTimeOffRestricted: event.isTimeOffRestricted ?? false,
    },
    onSubmit: async ({value}) => {
      const range = {
        start: convertLocalTimeToTimezone(parseDatetimeLocalInput(value.range.start), timezone),
        end: convertLocalTimeToTimezone(parseDatetimeLocalInput(value.range.end), timezone),
      };
      onUpdateEvent({
        id: event.id,
        title: value.title,
        description: value.description,
        range: range,
        eventType: value.eventType,
        visibilityLevel: getScheduleEventVisibilityLevelNumber(value.visibilityLevel),
        isTimeOffRestricted: value.isTimeOffRestricted,
      })
    },
    validatorAdapter: zodValidator(),
  });

  useEffect(() => {
    form.reset();
  }, [event]);

  return (
          <form className={"px-4 pb-6"} onSubmit={(e) => {
            e.preventDefault()
            e.stopPropagation()
            form.handleSubmit()
          }}>
            <form.Field name={"title"}
                        validators={{
                          onSubmit: z.string().min(1)
                        }}
                        children={(field) => {
                          return <FormControl>
                            <Label htmlFor={field.name}>Title</Label>
                            <FormInput field={field} autoFocus
                                       placeholder="Enter title..."/>
                            <FieldInfo field={field}/>
                          </FormControl>;
                        }}/>

            <form.Field name={"range.start"}
                        validators={{
                          onChange: ({value, fieldApi}) => {
                            const end = parseDatetimeLocalInput(fieldApi.form.getFieldValue("range.end"));
                            const start = parseDatetimeLocalInput(value);
                            if (start > end) {
                              return "Start date must be before end date";
                            }
                            return undefined;
                          },
                          onChangeListenTo: ['range.end'],
                        }}
                        children={(field) => {
                          return <FormControl>
                            <Label htmlFor={field.name}>Start Date</Label>
                            <FormInput field={field} type={"datetime-local"}
                                       placeholder="Choose start date..."/>
                            <FieldInfo field={field}/>
                          </FormControl>;
                        }}/>
            <form.Field name={"range.end"}
                        validators={{
                          onChange: ({value, fieldApi}) => {
                            const start = parseDatetimeLocalInput(fieldApi.form.getFieldValue("range.start"));
                            const end = parseDatetimeLocalInput(value);
                            if (end < start) {
                              return "End date must be after start date";
                            }
                            return undefined;
                          },
                          onChangeListenTo: ['range.start'],
                        }}
                        children={(field) => {
                          return <FormControl>
                            <Label htmlFor={field.name}>End Date</Label>
                            <FormInput field={field} type={"datetime-local"}
                                       placeholder="Choose end date..."/>
                            <FieldInfo field={field}/>
                          </FormControl>;
                        }}/>
            <form.Field name={"eventType"}
                        children={(field) => {
                          return <FormControl>
                            <div>Calendar</div>
                            <div className={"flex flex-wrap gap-2"}>
                              {map(ScheduleCalendars, cal => {
                                const isSelected = cal.value === field.state.value;
                                return <Button variant={"outline"}
                                               size={"xs"}
                                               className={"border rounded-lg px-3"}
                                               onClick={(e) => {
                                                 e.preventDefault();
                                                 field.handleChange(cal.value);
                                               }}
                                               style={{
                                                 borderColor: cal.color,
                                                 backgroundColor: isSelected ? cal.color : "white",
                                                 color: isSelected ? "white" : cal.color,
                                               }}>{cal.title}</Button>
                              })}
                            </div>
                            <FieldInfo field={field}/>
                          </FormControl>;
                        }}/>


            <form.Field name={"visibilityLevel"}
                        validators={{
                          onSubmit: z.string().min(0, "Required")
                        }}
                        children={(field) => <FormControl>
                          <div className={"mb-2"}>Visibility</div>
                          <RadioGroup defaultValue="everyone" onValueChange={field.handleChange}
                                      value={field.state.value}>
                            <div className="flex items-center space-x-2">
                              <RadioGroupItem value="everyone" id="opt-everyone"/>
                              <Label htmlFor="opt-everyone">
                                Everyone
                              </Label>
                            </div>
                            <div className="flex items-center space-x-2">
                              <RadioGroupItem value="leadership" id="opt-leadership"/>
                              <Label htmlFor="opt-leadership">
                                Leadership
                              </Label>
                            </div>
                            <div className="flex items-center space-x-2">
                              <RadioGroupItem value="scheduler" id="opt-scheduler"/>
                              <Label htmlFor="opt-scheduler">
                                Scheduler
                              </Label>
                            </div>
                          </RadioGroup>

                          <FieldInfo field={field}/>
                        </FormControl>}/>

            {canRestrictTimeOff ?
                    <FormControl>
                      <div>Restrict Time Off</div>

                      <form.Field name={"isTimeOffRestricted"}
                                  children={(field) => <div>
                                    <FormCheckbox field={field} labelClassName={"font-normal leading-tight"}
                                                  label={"Prevent team members from requesting time off that overlaps with this event."}/>
                                    <FieldInfo field={field}/>
                                  </div>}/>
                    </FormControl> : null}

            <form.Field name={"description"}
                        validators={{
                          onSubmit: z.string().optional()
                        }}
                        children={(field) => {
                          return <FormControl>
                            <Label htmlFor={field.name}>Description (optional)</Label>
                            <FormTextarea field={field}
                                          placeholder="Enter description of this event..."/>
                            <FieldInfo field={field}/>
                          </FormControl>;
                        }}/>

            <div className="flex justify-between items-center gap-2">
              {!isNewEvent ? (
                      <Button
                              type="button"
                              variant="destructive"
                              onClick={() => onDeleteEvent(event)}
                              disabled={isLoading} isLoading={isLoading}
                      >
                        Delete
                      </Button>
              ) : <div/>}
              <Button type="submit" disabled={isLoading} isLoading={isLoading}>
                {isNewEvent ? "Create" : "Save"}
              </Button>
            </div>

          </form>
  );
}
