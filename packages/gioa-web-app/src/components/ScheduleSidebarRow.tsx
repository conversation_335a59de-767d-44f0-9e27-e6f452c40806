import React from 'react';
import {ProficiencyRating} from "@/src/components/ProficiencyRating.tsx";
import {cn} from "@/src/util.ts";
import {Button} from "@/src/components/ui/button.tsx";
import {ChevronDownIcon, PlusIcon} from "lucide-react";
import {DailyTimeRange} from '@gioa/api/src/timeSchemas';
import {incrementToFriendlyTime, ScheduleRowInfo} from "../../../api/src/scheduleBuilder.util.ts";
import {getChangeStatusBorder} from "@/src/components/ScheduleBuilder.util.tsx";

export interface ScheduleSidebarRowProps {
  shifts: ScheduleRowInfo[];
  isSelected: boolean;
  rowHeight: number;
  isHighlighted: boolean;
  onClickTeamMember: (shift: ScheduleRowInfo) => void;
  onClickAddShift: (areaId: string) => void;
  teamMemberNumber: number;
  rowIndex: number;
  onClickArea: (areaId: string) => void;
  onToggleArea: (areaTitle: string) => void;
  storeHours: DailyTimeRange;
}

export const ScheduleSidebarRow = React.memo(({
                                                shifts,
                                                isSelected, rowIndex,
                                                rowHeight, storeHours,
                                                isHighlighted,
                                                onClickTeamMember,
                                                onClickAddShift, onToggleArea,
                                                teamMemberNumber, onClickArea
                                              }: ScheduleSidebarRowProps) => {
  const shift = shifts[rowIndex];

  if (shift.type === "area") {
    const isCollapsed = shift.isCollapsed;
    return <div style={{height: rowHeight}} key={shift.id + "area"} className={"py-1 px-4"}>
      <div
        className={"bg-primary-600 text-white rounded-lg flex h-full w-full text-left items-stretch overflow-hidden "}>
        <button onClick={() => onClickArea(shift.id)} className={"flex items-center grow text-left pl-3 pr-2 hover:bg-primary-700"}>
                    <span className={"grow truncate pr-2"}>
                    {shift.title}
                    </span>
          <span className={"text-sm ml-2 whitespace-nowrap"}>
        {shift.numShiftsInArea} shifts | {Math.floor(shift.numShiftHoursInArea)} hrs
        </span>
        </button>
        <button title={isCollapsed ? "Expand area" : "Collapse area"} onClick={() => onToggleArea(shift.title)}
                className={"bg-primary-600 px-1 hover:bg-primary-700"}>
          <ChevronDownIcon size={16} className={cn("text-gray-100 transition-transform duration-200", {
            "rotate-180": !isCollapsed
          })}/>
        </button>
      </div>
    </div>;
  } else if (shift.type === "shift") {
    const {borderClass, color: borderColor} = getChangeStatusBorder(shift.changeStatus);

    return <div style={{height: rowHeight}} key={shift.id + "area"}
                className={cn("w-full py-1 px-4", isSelected ? "bg-primary-100 bg-opacity-70" : undefined)}>
      <Button variant={"outline"} size={"sm"} style={borderColor ? {borderLeftColor: borderColor} : undefined}
              onClick={() => onClickTeamMember(shift)}
              className={cn("w-full justify-between h-full gap-4 gioa-team-member-select", borderClass,
                isHighlighted ? "bg-amber-100 outline outline-2 outline-primary-800 -outline-offset-1 hover:bg-yellow-100" : undefined)}>

        <span>
                    <span className={"max-w-[16rem] overflow-ellipsis overflow-hidden text-left"}
                          style={{minWidth: "14ch"}}>
                      {shift.assignedTo
                        ? shift.assignedTo.firstName + " " + shift.assignedTo.lastName
                        : `${teamMemberNumber}) ${incrementToFriendlyTime(storeHours, shift.start)} - ${incrementToFriendlyTime(storeHours, shift.end)}`
                      }
                    </span>
          {shift.assignedPersonTotalWeekHours ?
            <span
              className={"text-muted-foreground text-sm ml-2"}>{Math.floor(shift.assignedPersonTotalWeekHours)}hr</span> : null}
        </span>
        <ProficiencyRating rank={shift.assignedTo ? shift.assignedTo.proficiencyRanking ?? 0 : 0}
                           colorScheme={"light-bg"}/>
      </Button>
    </div>;
  } else if (shift.type === "add") {
    let areaId = "";
    for (let i = rowIndex - 1; i >= 0; i--) {
      if (shifts[i].type === "area") {
        areaId = shifts[i].id;
        break;
      }
    }

    return <div style={{height: rowHeight}} key={shift.id + "area"} className={"py-1 px-4"}>
      <Button variant={"outline"} size={"sm"} onClick={() => onClickAddShift(areaId)}
              leftIcon={<PlusIcon size={20}/>}
              className={"h-full"}>
        Add shift
      </Button>
    </div>;
  }

});
