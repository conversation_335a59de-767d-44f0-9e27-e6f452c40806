import React, {useLayoutEffect, useMemo} from 'react';
import {api} from "@/src/api.ts";
import {filter, groupBy, map} from "lodash";
import {Text} from "@/src/components/Text.tsx";
import {Input} from "@/src/components/ui/input.tsx";
import {Switch} from "@/src/components/ui/switch.tsx";
import {SearchIcon} from "lucide-react";

export type CheckedIds = { [id: string]: boolean };

export interface PolicyEditorProps {
  value: CheckedIds;
  onChange: (value: CheckedIds) => void;
  isDisabled?: boolean;
}

export const PolicyEditor: React.FC<PolicyEditorProps> = (props) => {
  const [search, setSearch] = React.useState("");
  const searchInputRef = React.useRef<HTMLInputElement>(null);
  useLayoutEffect(() => {
    if (searchInputRef.current) {
      searchInputRef.current.value = search;
    }
  }, [search]);

  const {data: packages} = api.user.getPermissionPackages.useQuery(undefined, {
    staleTime: 24 * 60 * 60 * 1000 // 24 hours
  });

  const filteredPackages = useMemo(() => {
    if (!search) return packages;
    return filter(packages, pkg => pkg.title.toLowerCase().includes(search.toLowerCase()));
  }, [search, packages]);

  const groupedPackages = groupBy(filteredPackages, pkg => pkg.group);

  if (!packages) {
    return <div className="p-4">Loading permission packages...</div>;
  }

  return (
          <div className="flex flex-col gap-4">
            <Input type={"search"}
                   value={search} onChange={e => setSearch(e.target.value)}
                   leftIcon={SearchIcon}
                   placeholder="Search permissions..."
                   className="w-auto"/>

            {map(groupedPackages, (pkgs, group) => {
              return (
                      <div key={group} className="p-4 border border-gray-200 rounded-xl">
                        <div className="mb-2">
                          <Text bold>{group}</Text>
                        </div>
                        {map(pkgs, (pkg, index) => {
                          const isPackageChecked = props.value[pkg.id];
                          return (
                                  <div
                                          key={pkg.id}
                                          className={`flex flex-row gap-6 justify-between items-center pt-3 ${pkgs.length <= index + 1 ? "" : "pb-3 border-b"} border-gray-300`}
                                  >
                                    <div className="flex-1 flex flex-col">
                                      <Text semibold size="sm" className={props.isDisabled ? "text-gray-500" : ""}>
                                        {pkg.title}
                                      </Text>
                                      <Text muted size="sm" className={props.isDisabled ? "text-gray-500" : ""}>
                                        {pkg.description}
                                      </Text>
                                    </div>
                                    <Switch checked={isPackageChecked}
                                            disabled={props.isDisabled}
                                            onCheckedChange={(isChecked) => {
                                              props.onChange({
                                                ...props.value,
                                                [pkg.id]: isChecked
                                              });
                                            }}
                                    />
                                  </div>
                          );
                        })}
                      </div>
              );
            })}
          </div>
  );
};
