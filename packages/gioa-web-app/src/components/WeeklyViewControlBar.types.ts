import {StoreAreaDto} from "../../../api/src/schemas.ts";
import {z} from "zod";
import {keys, map} from "lodash";
import {sanitizeStoreAreaTitle} from "../../../api/src/util.ts";
import {LaborStatus} from "../../../api/src/personMetrics.ts";
import {dayOfWeek} from "../../../api/src/timeSchemas.ts";

export interface SelectOption {
  label: string;
  value: string;
}

export interface BaseFilter {
  id: string;
  label?: string;
}

export interface IsScheduledFilter extends BaseFilter {
  type: "isScheduled";
  controlType: "select";
  options: SelectOption[];
  value: string;
}

export interface NumDaysScheduledFilter extends BaseFilter {
  type: "numDaysScheduled";
  controlType: "select";
  options: SelectOption[];
  value: string;
}

export interface NumStarsFilter extends BaseFilter {
  type: "numStars";
  controlType: "multiselect";
  options: SelectOption[];
  value: string[];
}

export interface AreaFilter extends BaseFilter {
  type: "area";
  controlType: "multiselect";
  options: SelectOption[];
  value: string[];
}

export interface LaborStatusFilter extends BaseFilter {
  type: "laborStatus";
  controlType: "multiselect";
  options: SelectOption[];
  value: string[];
}

export interface TimeOffFilter extends BaseFilter {
  type: "timeOff";
  controlType: "multiselect";
  options: SelectOption[];
  value: string[];
}

export type WeeklyViewFilter =
  | NumStarsFilter
  | NumDaysScheduledFilter
  | AreaFilter
  | LaborStatusFilter
  | IsScheduledFilter
  | TimeOffFilter

// a filter serialized for use in the URL search params
export const serializedFilter = z.object({
  t: z.string(), // type
  v: z.union([z.string(), z.array(z.string())]), // value
})

export type SerializedFilter = z.infer<typeof serializedFilter>;

// TODO use shift areas instead of store areas
export function createAreaFilter(idx: number, storeAreas: StoreAreaDto[], value: string[]): AreaFilter {
  return {
    id: "area_" + idx,
    label: "Area",
    type: "area",
    controlType: "multiselect",
    options: map(storeAreas, area => ({label: sanitizeStoreAreaTitle(area.title)!, value: area.id})),
    value: value
  }
}

export function createTimeOffFilter(idx: number, value: string[]): TimeOffFilter {
  return {
    id: "timeOff_" + idx,
    label: "Time off",
    type: "timeOff",
    controlType: "multiselect",
    options: [{
      label: "Approved",
      value: "approved",
    }, {
      label: "Pending",
      value: "pending",
    }],
    value: value
  }
}

export function createNumStarsFilter(idx: number): NumStarsFilter {
  return {
    id: "numStars_" + idx,
    label: "Num. stars",
    type: "numStars",
    controlType: "multiselect",
    options: [
      {label: "3", value: "3"},
      {label: "2", value: "2"},
      {label: "1", value: "1"},
    ],
    value: []
  }
}

export function createLaborStatusFilter(idx: number, value: LaborStatus[]): LaborStatusFilter {
  const laborStatusesTypeScript: Record<LaborStatus, string> = {
    '14/15 yr old': '',
    '16/17 yr old': '',
    Adult: ''
  }
  const laborStatuses = keys(laborStatusesTypeScript);

  return {
    id: "laborStatus_" + idx,
    label: "Labor Status",
    type: "laborStatus",
    controlType: "multiselect",
    options: map(laborStatuses, (key) => ({label: key, value: key})),
    value: value
  }
}

export function createNumDaysFilter(idx: number, value: number): NumDaysScheduledFilter {
  return {
    id: "numDaysScheduled_" + idx,
    label: "Scheduled",
    type: "numDaysScheduled",
    controlType: "select",
    options: [
      {label: "1 day", value: "1"},
      {label: "2 days", value: "2"},
      {label: "3 days", value: "3"},
      {label: "4 days", value: "4"},
      {label: "5 days", value: "5"},
      {label: "6 days", value: "6"},
    ],
    value: value.toString()
  }
}

export function createIsScheduledFilter(idx: number, value: boolean): IsScheduledFilter {
  return {
    id: "isScheduled_" + idx,
    type: "isScheduled",
    controlType: "select",
    options: [
      {label: "Scheduled", value: "true"},
      {label: "Not scheduled", value: "false"},
    ],
    value: value.toString()
  }
}

function ensureArray<T>(value: T | T[]): T[] {
  return Array.isArray(value) ? value : [value];
}

export function deserializeFilter({filter, idx, storeAreas}: {
  filter: SerializedFilter,
  idx: number,
  storeAreas: StoreAreaDto[]
}): WeeklyViewFilter | undefined {
  switch (filter.t) {
    case "isScheduled":
      return createIsScheduledFilter(idx, filter.v === "true");
    case "numDaysScheduled":
      return createNumDaysFilter(idx, parseInt(filter.v as any));
    case "area":
      return createAreaFilter(idx, storeAreas, filter.v as string[]);
    case "laborStatus":
      return createLaborStatusFilter(idx, ensureArray(filter.v) as LaborStatus[]);
    default:
      return undefined;
  }
}

export const weeklyViewSort = z.object({
  column: z.string().optional(),
  day: dayOfWeek.optional(),
  isAsc: z.boolean()
})
export type WeeklyViewSort = z.infer<typeof weeklyViewSort>;
