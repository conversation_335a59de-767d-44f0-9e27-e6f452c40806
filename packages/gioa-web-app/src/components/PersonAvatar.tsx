import React from 'react';
import {PersonDto} from "../../../api/src/schemas.ts";
import {Avatar, AvatarFallback, AvatarImage, AvatarProps} from "@/src/components/ui/avatar.tsx";
import {imageUrl} from "@/src/images.ts";
import {getInitials} from "@/src/person.util.ts";

export interface PersonAvatarProps extends AvatarProps {
  person?: {
    profileImageUrl?: string | null,
    firstName?: string | null,
    lastName?: string | null
  };
}

const avatarSizeToImageSize = {
  default: 40,
  xs: 24,
  sm: 32,
  lg: 48,
  xl: 64,
  "2xl": 80,
  "3xl": 96,
  "4xl": 128,
}

export const PersonAvatar: React.FC<PersonAvatarProps> = ({person, ...props}) => {
  const imageSize = avatarSizeToImageSize[props.size || "default"];
  const densityFactor = 2;

  return (
    <Avatar {...props}>
      <AvatarImage src={imageUrl(person?.profileImageUrl ?? undefined, {width: imageSize * densityFactor})}
                   alt=""/>
      <AvatarFallback className={"text-violet-600"}>{getInitials(person)}</AvatarFallback>
    </Avatar>
  );
}
