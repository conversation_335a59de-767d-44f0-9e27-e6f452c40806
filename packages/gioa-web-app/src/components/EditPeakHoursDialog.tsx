import React, {useEffect} from 'react';
import {<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ooter, <PERSON><PERSON><PERSON>eader, DialogTitle} from "@/src/components/ui/dialog.tsx";
import {Button} from "@/src/components/ui/button.tsx";
import {DialogDescription} from "@radix-ui/react-dialog";
import {zodValidator} from "@tanstack/zod-form-adapter";
import {useForm} from "@tanstack/react-form";
import {PeakHours} from "../../../api/src/scheduleSchemas.ts";
import {FormControl} from "@/src/components/form/FormControl.tsx";
import {Label} from "@/src/components/ui/label.tsx";
import {FormInput} from "@/src/components/form/FormInput.tsx";
import {FieldInfo} from "@/src/components/form/FieldInfo.tsx";
import {filter, map} from "lodash";
import {compareTimeOfDay} from "../../../api/src/date.util.ts";
import {PlusIcon} from "lucide-react";

import {incrementDurationMinutes} from "../../../api/src/scheduleBuilder.util.ts";

export interface EditPeakHoursDialogProps {
  onSave: (peakHours: PeakHours) => void;
  isOpen: boolean;
  onClose: () => void;
  onOpen: () => void;
  peakHours: PeakHours;
  dayOfWeek: number;
}

export const EditPeakHoursDialog: React.FC<EditPeakHoursDialogProps> = (props) => {
  const todaysPeakHours = filter(props.peakHours, ph => ph.dayOfWeek === props.dayOfWeek);

  const form = useForm({
    defaultValues: {
      peakHours: todaysPeakHours
    },
    onSubmit: async ({value}) => {
      props.onSave(value.peakHours);
    },
    validatorAdapter: zodValidator(),
  });

  useEffect(() => {
    if (!props.isOpen) {
      // give a bit of time for the modal to animate out
      setTimeout(() =>
        form.reset(), 100);
    }
  }, [props.isOpen]);

  const onCancel = () => {
    props.onClose();
  }

  return (
    <Dialog open={props.isOpen} onOpenChange={open => {
      if (open) {
        props.onOpen();
      } else {
        onCancel();
      }
    }}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Edit Peak Hours</DialogTitle>
          <DialogDescription>
            Edit the peak hours for this week's schedule.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          <form onSubmit={(e) => {
            e.preventDefault()
            e.stopPropagation()
            form.handleSubmit()
          }}>
            <form.Field name={"peakHours"} mode={"array"}
                        children={(arrField) => {
                          return <div>
                            {map(arrField.state.value, (field, index) =>
                              <div key={index} className={"px-4 py-3 border rounded-lg mb-3"}>
                                <div className={"grid grid-cols-2 gap-2 mb-3"}>
                                  <form.Field name={`peakHours[${index}].start`}
                                              validators={{
                                                onChange: ({value, fieldApi}) => {
                                                  const comparison = compareTimeOfDay(value, fieldApi.form.getFieldValue(`peakHours[${index}].end`));
                                                  if (comparison > 0) {
                                                    return "Start time must be before end time";
                                                  }
                                                  return undefined;
                                                },
                                                onChangeListenTo: [`peakHours[${index}].end`],
                                              }}
                                              children={(field) => {
                                                return <FormControl className={"mb-0"}>
                                                  <Label htmlFor={field.name}>Start Time</Label>
                                                  <FormInput field={field} type={"time"}
                                                             step={incrementDurationMinutes * 60}
                                                             placeholder="Choose start time..."/>
                                                  <FieldInfo field={field}/>
                                                </FormControl>;
                                              }}/>
                                  <form.Field name={`peakHours[${index}].end`}
                                              validators={{
                                                onChange: ({value, fieldApi}) => {
                                                  const comparison = compareTimeOfDay(value, fieldApi.form.getFieldValue(`peakHours[${index}].start`));
                                                  if (comparison < 0) {
                                                    return "End time must be after start time";
                                                  }
                                                  return undefined;
                                                },
                                                onChangeListenTo: [`peakHours[${index}].start`],
                                              }}
                                              children={(field) => {
                                                return <FormControl className={"mb-0"}>
                                                  <Label htmlFor={field.name}>End Time</Label>
                                                  <FormInput field={field} type={"time"}
                                                             step={incrementDurationMinutes * 60}
                                                             placeholder="Choose end time..."/>
                                                  <FieldInfo field={field}/>
                                                </FormControl>;
                                              }}/>
                                </div>
                                <div>
                                  <Button type={"button"} variant={"outline"} size={"sm"} onClick={() => {
                                    arrField.removeValue(index);
                                  }}>
                                    Remove
                                  </Button>
                                </div>
                              </div>)}

                            <Button type={"button"} onClick={() => arrField.pushValue({
                              dayOfWeek: props.dayOfWeek,
                              start: "12:00",
                              end: "14:00",
                            })}
                                    leftIcon={<PlusIcon/>}
                                    variant={"outline"}>
                              Add New Peak Hours
                            </Button>
                          </div>
                        }}/>

            <DialogFooter>
              <Button variant={"outline"} type={"button"}
                      onClick={onCancel}>
                Cancel
              </Button>
              <Button type={"submit"}>
                Save
              </Button>
            </DialogFooter>
          </form>
        </div>
      </DialogContent>
    </Dialog>
  );
}
