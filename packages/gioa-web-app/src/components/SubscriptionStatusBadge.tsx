import React from 'react';
import {Badge} from "@/src/components/ui/badge.tsx";

export interface SubscriptionStatusBadgeProps {
  status: string;
}

export const SubscriptionStatusBadge: React.FC<SubscriptionStatusBadgeProps> = (props) => {
  switch (props.status) {
    case "active":
      return <Badge colorScheme={"success"} size={"md"}>Active</Badge>;
    case "trialing":
      return <Badge colorScheme={"default"} size={"md"}>Trial</Badge>;
    case "incomplete":
      return <Badge colorScheme={"yellow"} size={"md"}>Incomplete</Badge>;
    case "incomplete_expired":
      return <Badge colorScheme={"destructive"} size={"md"}>Incomplete Expired</Badge>;
    case "past_due":
      return <Badge colorScheme={"yellow"} size={"md"}>Past Due</Badge>;
    case "canceled":
      return <Badge colorScheme={"outline"} size={"md"}>Canceled</Badge>;
    case "unpaid":
      return <Badge colorScheme={"destructive"} size={"md"}>Unpaid</Badge>;
    case "paused":
      return <Badge colorScheme={"secondary"} size={"md"}>Paused</Badge>;
    case "none":
    default:
      return <Badge colorScheme={"destructive"} size={"md"}>
        None{' '}
        😱
      </Badge>;
  }
}
