import React, {useEffect, useState} from 'react';
import {getDerivedMetrics, ShiftsMetrics} from '../../../api/src/scheduling/metrics/metrics.util.ts';
import {
  ValidScheduleHourlySalesForecast
} from '@gioa/api/src/scheduling/metrics/scheduleHourlySalesForecast/scheduleHourlySalesForecastTypes';
import {DayOfWeek} from '@gioa/api/src/timeSchemas';
import {
  getAveragePayRateDollars,
  getForecastProductivityGoalDollars,
  getForecastTotalProjectedRevenueForDay
} from '@gioa/api/src/scheduling/metrics/scheduleHourlySalesForecast/scheduleHourlySalesForecast';
import {formatCurrency, formatHours, formatPercent} from "../../../common/src/dataFormatters.ts";
import {CircleHelpIcon} from "lucide-react";
import {Popover, PopoverContent, PopoverTrigger} from "@/src/components/ui/popover.tsx";
import {PopoverArrow} from '@radix-ui/react-popover';

export interface ScheduleForecastSideProps {
  metrics: ShiftsMetrics;
  forecast: ValidScheduleHourlySalesForecast;
  weekday: DayOfWeek;
  canViewPayRates: boolean;
}

export function getForecastTimelineHeight() {
  return document.getElementById("schedule-forecast-timeline")?.offsetHeight ?? 0;
}

export const ScheduleForecastSide: React.FC<ScheduleForecastSideProps> = ({
                                                                            metrics,
                                                                            forecast,
                                                                            weekday,
                                                                            canViewPayRates
                                                                          }) => {
  const [height, setHeight] = useState<number>(getForecastTimelineHeight());
  useEffect(() => {
    let attempts = 0;
    const maxAttempts = 20;

    const pollHeight = () => {
      const newHeight = getForecastTimelineHeight();
      if (newHeight > 0) {
        setHeight(newHeight);
        return;
      }

      if (attempts < maxAttempts) {
        attempts++;
        setTimeout(pollHeight, 10);
      }
    };

    pollHeight();
  }, []);

  const metricCard = (metric: React.ReactNode, value: string) => {
    return <div className={"border-b last:border-b-0 px-3 py-1 justify-between flex flex-row gap-2 bg-white"}>
      <div className={"font-semibold text-sm whitespace-nowrap"}>
        {metric}
      </div>

      <div className={"text-gioaBlue font-bold text-sm"}>
        {value}
      </div>
    </div>
  }

  const derivedMetrics = getDerivedMetrics({
    metrics: metrics,
    projectedRevenue: getForecastTotalProjectedRevenueForDay(forecast.dataPoints, weekday),
    productivityGoal: getForecastProductivityGoalDollars(forecast),
  });

  return (
    <div style={{height: height + "px"}} className={"mt-1 px-4 flex flex-col justify-center overflow-y-auto"}>
      {metricCard("Projected Revenue", formatCurrency(derivedMetrics.projectedRevenue))}
      {metricCard(
        canViewPayRates ? "Labor Cost" : <span className={"flex flex-row items-center"}>
          <span>Avg. Labor Cost</span>
          <Popover>
      <PopoverTrigger className={"px-2"}>
        <CircleHelpIcon className={"text-gray-500 hover:text-gioaBlue"} size={16}/>
      </PopoverTrigger>
      <PopoverContent className="w-80 text-sm">
        Since you do not have permission to view Team Member pay rates,
            the labor cost is calculated using the average pay rate entered for this schedule ({formatCurrency(forecast ? getAveragePayRateDollars(forecast) : undefined)}).
            <PopoverArrow/>
      </PopoverContent>
    </Popover>
        </span>
        , formatCurrency(derivedMetrics.laborCost))}
      {metricCard("Productivity", formatCurrency(derivedMetrics.actualProductivity) + "/hr")}
      {metricCard("Total Labor Hours", formatHours(derivedMetrics.totalLaborHours) + " hr")}
      {metricCard("Labor %", formatPercent(derivedMetrics.laborPercentage, 0))}
    </div>
  );
}
