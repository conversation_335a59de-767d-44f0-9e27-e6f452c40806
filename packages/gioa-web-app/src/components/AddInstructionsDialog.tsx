import React from 'react';
import {<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogTitle} from '@/src/components/ui/dialog';
import {Button} from '@/src/components/ui/button';
import {FormChecklistInstruction} from '@/src/components/ChecklistItemEditor';
import {FileIcon, FileTextIcon, ImageIcon} from 'lucide-react';
import {genChecklistInstructionId} from '@gioa/api/src/checklist.schemas';
import {DialogDescription} from "@radix-ui/react-dialog";

interface AddInstructionsDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onAdded: (instruction: FormChecklistInstruction) => void;
}

interface InstructionOption {
  value: string;
  label: string;
  icon: React.ReactNode;
}

const instructionOptions: InstructionOption[] = [
  {value: 'text', label: 'Add Text', icon: <FileTextIcon size={20} className="text-blue-500"/>},
  {value: 'image', label: 'Select Image', icon: <ImageIcon size={20} className="text-green-500"/>},
  {value: 'file', label: 'Upload File', icon: <FileIcon size={20} className="text-orange-500"/>},
];

export function AddInstructionsDialog({
                                        isOpen,
                                        onClose,
                                        onAdded
                                      }: AddInstructionsDialogProps) {
  const handleSelectOption = (value: string) => {
    if (value === 'text') {
      // Add an empty text instruction that will be edited inline
      onAdded({
        id: genChecklistInstructionId(),
        text: ""
      });
      onClose();
    } else if (value === 'image' || value === 'file') {
      // Handle file selection
      const input = document.createElement('input');
      input.type = 'file';
      input.accept = value === 'image' ? 'image/*' : '*/*';
      input.onchange = (e) => {
        const file = (e.target as HTMLInputElement).files?.[0];
        if (file) {
          onAdded({
            id: genChecklistInstructionId(),
            attachment: file
          });
          onClose();
        }
      };
      input.click();
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Add Instructions</DialogTitle>
          <DialogDescription>
            Add instructions to the Team Member:
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-3">
          {instructionOptions.map((option) => (
            <Button
              key={option.value}
              type="button"
              variant="outline"
              className="w-full justify-start"
              onClick={() => handleSelectOption(option.value)}
            >
              <div className="mr-2">{option.icon}</div>
              {option.label}
            </Button>
          ))}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
