import React from 'react';
import {Badge, BadgeProps, badgeVariants} from "@/src/components/ui/badge.tsx";
import {VariantProps} from 'class-variance-authority';

export interface PersonAvailabilityStatusBadgeProps extends BadgeProps {
  status: string;
}

const statusToColorScheme: {[status: string]: VariantProps<typeof badgeVariants>["colorScheme"]} = {
  Pending: "default",
  Draft: "default",
  Approved: "success",
  Declined: "destructive",
  Cancelled: "default",
};

export const PersonAvailabilityStatusBadge: React.FC<PersonAvailabilityStatusBadgeProps> = ({status, ...props}) => {
  const colorScheme = statusToColorScheme[status] ?? "default";

  return (
    <Badge colorScheme={colorScheme} {...props}>
      {status}
    </Badge>
  );
}
