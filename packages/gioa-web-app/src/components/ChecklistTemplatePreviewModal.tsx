import React, {Suspense, useCallback} from "react";
import {<PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ooter, <PERSON><PERSON><PERSON>eader, DialogTitle} from "@/src/components/ui/dialog";
import {Text} from "@/src/components/Text";
import {map} from "lodash";
import {ChecklistItemReadOnlyView} from "./ChecklistItemReadOnlyView";
import {api} from "@/src/api.ts";
import {Button} from "@/src/components/ui/button.tsx";
import {CopyPlusIcon} from "lucide-react";

interface ChecklistTemplatePreviewModalProps {
  templateId: string | null;
  storeId: string;
  isOpen: boolean;
  onClose: () => void;
  onCopyTemplate: (templateId: string) => void;
  isCopyPending: boolean;
}

function ChecklistTemplatePreviewContent({templateId, storeId, onCopyTemplate, isCopyPending}: {
  templateId: string;
  storeId: string;
  onCopyTemplate: (templateId: string) => void;
  isCopyPending: boolean;
}) {
  const [templateData] = api.checklist2.getAnonymousChecklistTemplate.useSuspenseQuery({
    storeId: storeId,
    checklistTemplateId: templateId
  });

  const template = templateData.template;

  const handleCopyTemplate = useCallback(() => {
    onCopyTemplate(templateId);
  }, [templateId, onCopyTemplate]);

  return (
          <>
            <DialogHeader>
              <DialogTitle>{template.title}</DialogTitle>
              {template.description ? <Text size={"sm"} muted>{template.description}</Text> : null}
            </DialogHeader>

            <div className="overflow-y-auto space-y-2 pr-2 border-b border-gray-200 pb-4"
                 style={{maxHeight: 'calc(90vh - 200px)'}}>
              {map(template.items, item => (
                      <ChecklistItemReadOnlyView key={item.id} item={item}/>
              ))}
            </div>

            <DialogFooter>
              <Button leftIcon={<CopyPlusIcon size={16}/>} onClick={handleCopyTemplate} isLoading={isCopyPending}
                      disabled={isCopyPending}>
                Import this Template
              </Button>
            </DialogFooter>
          </>
  );
}

export function ChecklistTemplatePreviewModal({
                                                templateId,
                                                storeId,
                                                isOpen,
                                                onClose,
                                                onCopyTemplate,
                                                isCopyPending,
                                              }: ChecklistTemplatePreviewModalProps) {

  if (!templateId) return null;

  return (
          <Dialog open={isOpen} onOpenChange={onClose}>
            <DialogContent className="max-w-[750px]">
              <Suspense fallback={
                <div className="flex justify-center items-center py-8">
                  <DialogHeader>
                    <DialogTitle>
                      Loading template...
                    </DialogTitle>
                  </DialogHeader>
                </div>
              }>
                <ChecklistTemplatePreviewContent templateId={templateId} storeId={storeId}
                                                 onCopyTemplate={onCopyTemplate} isCopyPending={isCopyPending}/>
              </Suspense>
            </DialogContent>
          </Dialog>
  );
}
