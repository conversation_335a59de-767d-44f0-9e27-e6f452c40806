import { useState, useEffect } from "react";
import { Button } from "@/src/components/ui/button.tsx";
import { Checkbox } from "@/src/components/ui/checkbox.tsx";
import {
  Dialog,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/src/components/ui/dialog.tsx";

interface TrainingStatusModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (isCompleted: boolean) => Promise<void>;
  personName: string;
  positionTitle: string;
  areaTitle: string;
  storeName: string;
  currentStatus: boolean; // true if currently trained, false if not trained
  isLoading?: boolean;
}

export function TrainingStatusModal({
  isOpen,
  onClose,
  onSave,
  personName,
  positionTitle,
  areaTitle,
  storeName,
  currentStatus,
  isLoading = false,
}: TrainingStatusModalProps) {
  const [isCompleted, setIsCompleted] = useState(!currentStatus); // Toggle the current status

  // Reset state when modal opens with new data
  useEffect(() => {
    if (isOpen) {
      setIsCompleted(!currentStatus);
    }
  }, [isOpen, currentStatus]);

  const handleSave = async () => {
    await onSave(isCompleted);
  };

  const handleCancel = () => {
    setIsCompleted(!currentStatus); // Reset to toggled state
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md rounded-3xl p-6">
        {/* Close button */}
        <button
          onClick={onClose}
          className="absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none"
          disabled={isLoading}
        >
          <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>

        <div className="space-y-4">
          {/* Header */}
          <div>
            <h2 className="text-lg font-semibold text-gray-900 mb-3">Training</h2>
            <div className="space-y-1 text-sm text-gray-600">
              <div>
                <span className="text-gray-500">Team Member: </span>
                <span className="font-medium text-gray-900">{personName}</span>
              </div>
              <div>
                <span className="text-gray-500">Position: </span>
                <span className="font-medium text-gray-900">{positionTitle}</span>
              </div>
              <div>
                <span className="text-gray-500">Area: </span>
                <span className="font-medium text-gray-900">{areaTitle}</span>
              </div>
              <div>
                <span className="text-gray-500">Store: </span>
                <span className="font-medium text-gray-900">{storeName}</span>
              </div>
            </div>
          </div>

          {/* Action Button */}
          <div className="pt-2">
            <Button
              onClick={handleSave}
              disabled={isLoading}
              className={`w-full rounded-2xl py-3 text-base font-medium transition-colors ${
                currentStatus
                  ? 'bg-red-100 text-red-800 hover:bg-red-200 border-0'
                  : 'bg-green-100 text-green-800 hover:bg-green-200 border-0'
              }`}
              variant="outline"
            >
              {isLoading
                ? 'Saving...'
                : currentStatus
                  ? 'Mark As Untrained'
                  : 'Mark As Trained'
              }
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
