import React, { useEffect, useRef, useState, useMemo } from "react";
import { map, uniq } from "lodash";
import { Badge } from "@/src/components/ui/badge.tsx";
import { Check, Edit, EditIcon, X } from "lucide-react";
import { Button } from "@/src/components/ui/button.tsx";

type ChecklistTagsInlineEditProps = {
  activeTags: string[];
  allTags: string[];
  onChange: (tags: string[]) => void;
  placeholder?: string;
  className?: string;
  displayClassName?: string;
  showEditIcon?: boolean;
  handleConfigureTags?: () => void;
};

export const ChecklistTagsInlineEdit: React.FC<ChecklistTagsInlineEditProps> = ({
  activeTags,
  allTags,
  onChange,
  placeholder = "No tags selected",
  className = "",
  displayClassName = "",
  showEditIcon = true,
  handleConfigureTags,
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [tempTags, setTempTags] = useState<string[]>(activeTags);
  const [temporaryTags, setTemporaryTags] = useState<string[]>([]);
  const displayRef = useRef<HTMLDivElement>(null);

  // Combine allTags with any active tags that aren't in allTags, plus any temporarily removed tags
  const availableTags = useMemo(() => {
    const activeTagsNotInAll = activeTags.filter(tag => !allTags.includes(tag));
    return uniq([...allTags, ...activeTagsNotInAll, ...temporaryTags]);
  }, [allTags, activeTags, temporaryTags]);

  // Update temp tags when activeTags prop changes
  useEffect(() => {
    setTempTags(activeTags);
  }, [activeTags]);

  const handleValueClick = () => {
    setIsEditing(true);
    setTempTags(activeTags);
  };

  const handleSave = () => {
    onChange(tempTags);
    setTemporaryTags([]);
    setIsEditing(false);
  };

  const handleCancel = () => {
    setTempTags(activeTags);
    setTemporaryTags([]);
    setIsEditing(false);
  };

  const handleTagToggle = (tag: string) => {
    const newTags = tempTags.includes(tag) ? tempTags.filter((t) => t !== tag) : [...tempTags, tag];

    // If we're removing a tag that wasn't in the original allTags, add it to temporaryTags
    // so it remains available for selection until the user cancels or saves
    if (tempTags.includes(tag) && !allTags.includes(tag)) {
      setTemporaryTags(prev => uniq([...prev, tag]));
    }

    setTempTags(newTags);
  };

  return (
    <div className={`inline-flex items-start ${className}`}>
      {isEditing ? (
        <div className="flex flex-col">
          <div className="flex flex-wrap gap-1 p-2 border border-gray-300 rounded focus-within:ring-2 focus-within:ring-blue-500 min-h-[40px]">
            {map(availableTags, (tag) => {
              const isActive = tempTags.includes(tag);
              return (
                <Badge
                  key={tag}
                  colorScheme={isActive ? "default" : "outline"}
                  size="sm"
                  className="cursor-pointer"
                  onClick={() => handleTagToggle(tag)}
                >
                  {tag}
                </Badge>
              );
            })}
          </div>
          <div className="flex mt-2 justify-between">
            <div>
              {handleConfigureTags ? <Button variant="link" leftIcon={<EditIcon size={16} />} type="button" size="sm" onClick={handleConfigureTags}>
                Manage Tags
              </Button> : null}
            </div>
            <div className="flex gap-2">
              <Button variant="secondary" leftIcon={<X size={16} />} type="button" size="sm" onClick={handleCancel}>
                Cancel
              </Button>
              <Button leftIcon={<Check size={16} />} type="button" size="sm" onClick={handleSave}>
                Save
              </Button>
            </div>
          </div>
        </div>
      ) : (
        <div
          ref={displayRef}
          role="button"
          tabIndex={0}
          className={`w-full rounded hover:bg-gray-100 cursor-pointer ${displayClassName}`}
          onClick={handleValueClick}
          onKeyDown={(e) => {
            if (e.key === "Enter" || e.key === " ") {
              e.preventDefault();
              handleValueClick();
            }
          }}
        >
          <div className="flex items-center gap-2">
            <div className="flex flex-wrap gap-1">
              {activeTags.length > 0 ? (
                map(activeTags, (tag) => (
                  <Badge key={tag} colorScheme="default" size="sm">
                    {tag}
                  </Badge>
                ))
              ) : (
                <span className="text-gray-400">{placeholder}</span>
              )}
            </div>
            {showEditIcon && <Edit size={14} className="text-gray-400 flex-shrink-0" />}
          </div>
        </div>
      )}
    </div>
  );
};
