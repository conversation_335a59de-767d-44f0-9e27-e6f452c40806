import React from 'react';
import {map} from "lodash";
import {cn} from "@/src/util.ts";
import {ScheduleCalendars} from "@/src/components/ScheduleTimeline.tsx";
import {ScheduleBuilderEvent} from "../../../api/src/scheduleBuilder.util.ts";

export interface ScheduleTimelineEventsProps {
  isEventsOpen: boolean;
  events: ScheduleBuilderEvent[];
  onOpenEvent: (eventId: string) => void;
  selectedEventId?: string;
  numIncrements: number;
  incrementWidth: number;
  timelineHeight: number;
}

const eventColors = [
  "#FFC0CB",
  "#FFA07A",
  "#FF0000",
  "#FFFF00",
  "#ADFF2F",
  "#808000",
  "#66CDAA",
  "#00FFFF",
  "#00BFFF",
];

export const ScheduleTimelineEvents: React.FC<ScheduleTimelineEventsProps> = ({
    isEventsOpen, events, onOpenEvent, selectedEventId, numIncrements, incrementWidth, timelineHeight
                                                                              }) => {
    return isEventsOpen ? map(events, (evt, evtIdx) => {
        const eventIncrementRange = evt.incrementRange;
        if (!eventIncrementRange) return null;

        const evtStart = eventIncrementRange.start;
        const evtEnd = eventIncrementRange.end;
        const isAtStart = evtStart === 0;
        const isAtEnd = evtEnd === numIncrements;
        const isSelected = evt.id === selectedEventId;
        const calendar = ScheduleCalendars[evt.eventType ?? "storeEvent"];

        return <button key={evt.id} style={{
          left: evtStart * incrementWidth + (isAtStart ? 8 : 0),
          top: timelineHeight + evtIdx * 20,
          width: ((evtEnd - evtStart) * incrementWidth) + (isAtStart || isAtEnd ? -8 : 0),
          backgroundColor: calendar?.color ?? eventColors[evtIdx % eventColors.length],
        }} onClick={() => onOpenEvent(evt.id)}
                       className={cn(`gioa-sched-event absolute rounded-md py-1 px-2 opacity-70 flex items-center text-sm hover:opacity-90`, isSelected ? "outline outline-2 outline-gray-800" : undefined)}>
          {evt.title}
        </button>
      }) : null
}
