import {<PERSON><PERSON>, <PERSON>alogContent, <PERSON><PERSON>Header, DialogTitle} from "@/src/components/ui/dialog";
import {Text} from "@/src/components/Text";
import {api} from "@/src/api";
import {toast} from "sonner";
import {ErrorAlert} from "@/src/components/ErrorAlert.tsx";
import {DateTime} from "luxon";
import {EventDetailsPanelContent} from "@/src/components/EventDetailsPanelContent.tsx";
import {ScheduleEventDto} from "../../../api/src/scheduleEventSchemas.ts";
import {genScheduleEventId} from "../../../api/src/schemas.ts";
import {useMemo} from "react";

export function EditEventModal({
                                 storeId,
                                 event,
                                 isOpen,
                                 onOpenChange,
                               }: {
  storeId: string;
  event?: ScheduleEventDto;
  isOpen: boolean;
  onOpenChange: (isOpen: boolean) => void;
}) {
  const apiUtil = api.useUtils();

  const [store] = api.user.getStoreAdmin.useSuspenseQuery({storeId: storeId!}, {staleTime: 1000 * 60 * 60});
  const timezone = store.timezone;
  const now = DateTime.now().setZone(timezone);

  const isNewEvent = !event;

  const defaultNewEvent: ScheduleEventDto = useMemo(() => ({
    id: genScheduleEventId(),
    title: "",
    description: "",
    range: {
      start: now.toJSDate(),
      end: now.toJSDate(),
    },
    eventType: "other", // or a valid default
    visibilityLevel: 0,
    isTimeOffRestricted: false,
  }), [isOpen]);

  const upsertEvent = api.user.upsertScheduleEvent.useMutation({
    onSuccess: async () => {
      await apiUtil.user.getStoreAdmin.invalidate();
      await apiUtil.user.getScheduleEvents.invalidate({
        storeId: storeId!
      });
      toast.success(isNewEvent ? "Event created successfully" : "Event updated successfully");
      onOpenChange(false);
    },
    onError: (error) => {
      toast.error(`Failed to ${isNewEvent ? "create" : "update"} event: ${error.message}`);
    },
  });

  const deleteEvent = api.user.deleteScheduleEvent.useMutation({
    onSuccess: async () => {
      await apiUtil.user.getStoreAdmin.invalidate();
      await apiUtil.user.getScheduleEvents.invalidate({
        storeId: storeId!
      });
      toast.success("Event deleted successfully");
      onOpenChange(false);
    },
    onError: (error) => {
      toast.error(`Failed to delete event: ${error.message}`);
    },
  });

  const handleUpsertEvent = (value: ScheduleEventDto) => {
    upsertEvent.mutate({
      id: value.id,
      storeId: storeId,
      title: value.title,
      description: value.description,
      range: value.range,
      eventType: value.eventType,
      visibilityLevel: value.visibilityLevel,
      isTimeOffRestricted: value.isTimeOffRestricted ?? false,
    })
  }

  const handleDeleteEvent = () => {
    if (confirm("Are you sure you want to delete this event?")) {
      if (!event) {
        onOpenChange(false);
        return;
      }
      deleteEvent.mutate({id: event.id});
    }
  }

  return (
          <Dialog open={isOpen} onOpenChange={onOpenChange}>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>
                  <Text size="lg" semibold>
                    {isNewEvent ? "Create Event" : "Edit Event"}
                  </Text>
                </DialogTitle>
              </DialogHeader>

              {upsertEvent.isError ? <ErrorAlert error={upsertEvent.error}/> : null}
              {deleteEvent.isError ? <ErrorAlert error={deleteEvent.error}/> : null}
              <div className="space-y-4 ">
                <EventDetailsPanelContent
                        isNewEvent={isNewEvent}
                        event={event ?? defaultNewEvent}
                        onUpdateEvent={handleUpsertEvent}
                        onDeleteEvent={handleDeleteEvent}
                        isLoading={upsertEvent.isPending ?? deleteEvent.isPending}
                        timezone={timezone}
                        storeId={storeId}
                />
              </div>
            </DialogContent>
          </Dialog>
  )
          ;
}
