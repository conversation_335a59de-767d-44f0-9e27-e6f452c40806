import React, {useState, useEffect} from 'react';
import {useForm} from "@tanstack/react-form";
import {zodValidator} from '@tanstack/zod-form-adapter';
import {z} from "zod";
import {filter, map, sortBy} from "lodash";
import {But<PERSON>} from "@/src/components/ui/button.tsx";
import {Label} from "@/src/components/ui/label.tsx";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/src/components/ui/select.tsx";
import {Card, CardContent, CardHeader, CardTitle} from "@/src/components/ui/card.tsx";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/src/components/ui/accordion.tsx";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/src/components/ui/alert-dialog.tsx";
import {TrashIcon, PlusIcon, CalendarDays, ClipboardList, Flag, CircleUser, StickyNote, FileUser, Users, Store, Clock, MessageSquareText, UserSearch, ScrollText, Table2} from "lucide-react";
import {FormSwitch} from "@/src/components/form/FormSwitch.tsx";
import {FormControl} from "@/src/components/form/FormControl.tsx";
import {FormInput} from "@/src/components/form/FormInput.tsx";
import {FieldInfo} from "@/src/components/form/FieldInfo.tsx";
import {EducationSectionDto, EducationResourceDto} from "../../../api/src/routers/gioa-web-app/admin.schemas.ts";
import {EducationResourceForm} from "./EducationResourceForm.tsx";

interface EducationSectionFormProps {
  initialData?: EducationSectionDto;
  isEditing?: boolean;
  onSubmit: (data: {
    id: string;
    title: string;
    icon: string;
    order: number;
    isVisible: boolean;
    resources: EducationResourceDto[];
  }) => Promise<void>;
  onDelete?: () => Promise<void>;
  isLoading?: boolean;
  isDeleting?: boolean;
  onCancel: () => void;
}

const educationSectionFormSchema = z.object({
  title: z.string().min(1, "Title is required"),
  icon: z.string().min(1, "Icon is required"),
  order: z.number().min(0, "Display Order is required"),
  isVisible: z.boolean(),
});

const iconOptions = [
  { value: "overview", label: "Overview", icon: Table2 },
  { value: "scheduling", label: "Scheduling", icon: CalendarDays },
  { value: "shift-leader-console", label: "Shift Leader Console", icon: Flag },
  { value: "setup-sheets", label: "Setup Sheets", icon: FileUser },
  { value: "training", label: "Training", icon: ClipboardList },
  { value: "human-resources", label: "Human Resources", icon: Users },
  { value: "checklists", label: "Checklists", icon: ScrollText },
  { value: "store", label: "Store", icon: Store },
  { value: "time-punch-variance", label: "Time Punch & Variance", icon: Clock },
  { value: "team-members", label: "Team Members", icon: CircleUser },
  { value: "messaging-groups", label: "Messaging & Groups", icon: MessageSquareText },
  { value: "insights", label: "Insights", icon: UserSearch },
  { value: "other", label: "Other", icon: StickyNote },
];

export const EducationSectionForm: React.FC<EducationSectionFormProps> = ({
  initialData,
  isEditing = false,
  onSubmit,
  onDelete,
  isLoading = false,
  isDeleting = false,
  onCancel,
}) => {
  const [resources, setResources] = useState<EducationResourceDto[]>(
    sortBy(initialData?.resources || [], 'order')
  );
  const [showAddResource, setShowAddResource] = useState(false);
  const [editingResourceIndex, setEditingResourceIndex] = useState<number | null>(null);
  const [accordionValue, setAccordionValue] = useState<string[]>([]);

  const form = useForm({
    defaultValues: {
      title: initialData?.title || '',
      icon: initialData?.icon || '',
      order: initialData?.order ?? 1,
      isVisible: initialData?.isVisible ?? true,
    },
    onSubmit: async ({value}) => {
      await onSubmit({
        id: initialData?.id || '',
        title: value.title,
        icon: value.icon,
        order: value.order,
        isVisible: value.isVisible,
        resources: sortBy(resources, 'order'),
      });
    },
    validatorAdapter: zodValidator()
  });

  useEffect(() => {
    if (initialData) {
      form.setFieldValue('title', initialData.title);
      form.setFieldValue('icon', initialData.icon);
      form.setFieldValue('order', initialData.order ?? 1);
      form.setFieldValue('isVisible', initialData.isVisible ?? true);
      setResources(sortBy(initialData.resources, 'order'));
    }
  }, [initialData, form]);

  const handleRemoveResource = (index: number) => {
    setResources(filter(resources, (_, i) => i !== index));
  };

  const handleResourceSubmit = (resource: EducationResourceDto) => {
    let updatedResources: EducationResourceDto[];

    if (editingResourceIndex !== null) {
      updatedResources = [...resources];
      updatedResources[editingResourceIndex] = {
        ...updatedResources[editingResourceIndex],
        ...resource
      };
      updatedResources = sortBy(updatedResources, r => r.order);
      updatedResources = map(updatedResources, (r, idx) => ({ ...r, order: idx + 1 }));
    } else {
      const insertOrder = resource.order;

      updatedResources = map(resources, r =>
        r.order >= insertOrder ? { ...r, order: r.order + 1 } : r
      );
      updatedResources = [...updatedResources, resource];
      updatedResources = sortBy(updatedResources, r => r.order);
      updatedResources = map(updatedResources, (r, idx) => ({ ...r, order: idx + 1 }));
    }

    setResources(updatedResources);
    setEditingResourceIndex(null);
    setShowAddResource(false);
  };

  const handleCancelResourceForm = () => {
    setShowAddResource(false);
    setEditingResourceIndex(null);
  };

  return (
    <div className="max-w-4xl space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Section Details</CardTitle>
        </CardHeader>

        <CardContent>
          <form onSubmit={e => {
            e.preventDefault();
            e.stopPropagation();
            form.handleSubmit();
          }}>
            <div className="space-y-8">
              <div>
                <form.Field
                  name="title"
                  validators={{
                    onSubmit: educationSectionFormSchema.shape.title
                  }}
                  children={(field) => (
                    <FormControl>
                      <Label htmlFor={field.name}>Title</Label>
                      <FormInput
                        field={field}
                        placeholder="Enter education section title"
                      />
                      <FieldInfo field={field}/>
                    </FormControl>
                  )}
                />

                <form.Field
                  name="order"
                  validators={{
                    onSubmit: educationSectionFormSchema.shape.order
                  }}
                  children={(field) => (
                    <FormControl>
                      <Label htmlFor={field.name}>Display Order</Label>
                      <FormInput
                        field={field}
                        type="number"
                        min={0}
                        placeholder="Display order"
                        onChange={e => field.handleChange(Number(e.target.value))}
                      />
                      <FieldInfo field={field}/>
                    </FormControl>
                  )}
                />

                <form.Field
                  name="icon"
                  validators={{
                    onSubmit: educationSectionFormSchema.shape.icon
                  }}
                  children={(field) => (
                    <FormControl>
                      <Label htmlFor={field.name}>Icon</Label>
                      <Select value={field.state.value} onValueChange={field.handleChange}>
                        <SelectTrigger>
                          <SelectValue placeholder="Select an icon" />
                        </SelectTrigger>

                        <SelectContent>
                          {map(iconOptions, (option) => (
                            <SelectItem key={option.value} value={option.value}>
                              <div className="flex items-center gap-2">
                                <option.icon className="h-4 w-4" />
                                {option.label}
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FieldInfo field={field}/>
                    </FormControl>
                  )}
                />

                <form.Field
                  name="isVisible"
                  validators={{
                    onSubmit: educationSectionFormSchema.shape.isVisible
                  }}
                  children={(field) => (
                    <FormControl>
                      <FormSwitch
                        field={field}
                        label="Visible to users"
                        description="When enabled, this education section will be visible to users in the app"
                      />
                      <FieldInfo field={field}/>
                    </FormControl>
                  )}
                />
              </div>

              <div className="space-y-4 bg-gray-100 p-4 rounded-lg shadow-sm">
                <div className="flex items-center justify-between">
                  <Label className="text-base font-medium">Resources ({resources.length})</Label>

                  <Button
                    variant="outline"
                    size="sm"
                    type="button"
                    onClick={() => setShowAddResource(true)}
                    disabled={showAddResource}
                  >
                    <PlusIcon className="h-4 w-4 mr-2" />
                    Add Resource
                  </Button>
                </div>

                {(showAddResource || resources.length > 0) && (
                  <div className="space-y-4">
                    <Accordion
                      type="multiple"
                      className="w-full space-y-2"
                      value={accordionValue}
                      onValueChange={setAccordionValue}
                    >
                      {map(sortBy(resources, 'order'), (resource, index) => (
                        <AccordionItem key={index} value={`item-${index}`} className="rounded-lg shadow-sm bg-white border">
                          <AccordionTrigger className="hover:no-underline px-4">
                            <div className="flex justify-between items-center w-full pr-4">
                              <h4 className="font-medium text-left">{resource.title}</h4>
                              <Button
                                variant="destructive"
                                size="sm"
                                type="button"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleRemoveResource(index);
                                }}
                              >
                                <TrashIcon className="h-4 w-4" />
                              </Button>
                            </div>
                          </AccordionTrigger>

                          <AccordionContent className="px-2 pb-4">
                            <EducationResourceForm
                              onSubmit={(updatedResource) => {
                                const updatedResources = [...resources];
                                updatedResources[index] = {
                                  ...updatedResources[index],
                                  ...updatedResource
                                };
                                setResources(updatedResources);
                                setAccordionValue(prev => filter(prev, value => value !== `item-${index}`));
                              }}
                              onCancel={() => {
                                setAccordionValue(prev => filter(prev, value => value !== `item-${index}`));
                              }}
                              initialData={resource}
                              isEditing={true}
                              sectionId={initialData?.id || ''}
                            />
                          </AccordionContent>
                        </AccordionItem>
                      ))}
                    </Accordion>

                    {showAddResource && (
                      <Card className="border-2 border-dashed">
                        <EducationResourceForm
                          onSubmit={handleResourceSubmit}
                          onCancel={handleCancelResourceForm}
                          initialData={editingResourceIndex !== null ? resources[editingResourceIndex] : undefined}
                          isEditing={editingResourceIndex !== null}
                          sectionId={initialData?.id || ''}
                          defaultOrder={resources.length + 1}
                        />
                      </Card>
                    )}
                  </div>
                )}
              </div>

              <div className="flex gap-2">
                <Button type="submit" disabled={isLoading}>
                  {isLoading ? (isEditing ? 'Updating...' : 'Creating...') : (isEditing ? 'Update Section' : 'Create Section')}
                </Button>

                {isEditing && onDelete && (
                  <AlertDialog>
                    <AlertDialogTrigger asChild>
                      <Button variant="destructive" type="button">
                        <TrashIcon className="h-4 w-4 mr-2" />
                        Delete Section
                      </Button>
                    </AlertDialogTrigger>

                    <AlertDialogContent>
                      <AlertDialogHeader>
                        <AlertDialogTitle>Are you sure?</AlertDialogTitle>

                        <AlertDialogDescription>
                          This action cannot be undone. This will permanently delete the education section and all of its resources.
                        </AlertDialogDescription>
                      </AlertDialogHeader>

                      <AlertDialogFooter>
                        <AlertDialogCancel>Cancel</AlertDialogCancel>

                        <AlertDialogAction onClick={onDelete} disabled={isDeleting}>
                          {isDeleting ? 'Deleting...' : 'Delete'}
                        </AlertDialogAction>
                      </AlertDialogFooter>
                    </AlertDialogContent>
                  </AlertDialog>
                )}

                <Button variant="outline" type="button" onClick={onCancel}>
                  Cancel
                </Button>
              </div>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
};
