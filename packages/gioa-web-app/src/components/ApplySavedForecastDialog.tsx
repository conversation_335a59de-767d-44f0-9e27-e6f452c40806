import React from 'react';
import {<PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ooter, <PERSON><PERSON><PERSON>eader, DialogTitle} from "@/src/components/ui/dialog.tsx";
import {Button} from "@/src/components/ui/button.tsx";
import {api} from "@/src/api.ts";
import {ForecastGraph} from "@/src/components/ForecastGraph.tsx";
import {getHumanReadableErrorMessage} from "@/src/components/ErrorAlert.tsx";
import {toast} from 'sonner';
import {calculateDaysAge} from "@/src/date.util.ts";
import {SuccessSalesForecastJobDto} from "../../../api/src/scheduling/metrics/salesForecastJob/salesForecastJobDtos.ts";
import {DateTime} from "luxon";

export interface ApplySavedForecastDialogProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  storeId: string;
  scheduleId: string;
  timezone: string;
  forecastJob: SuccessSalesForecastJobDto;
}

export function ApplySavedForecastDialog({
                                           isOpen,
                                           onOpenChange,
                                           storeId,
                                           scheduleId,
                                           timezone,
                                           forecastJob
                                         }: ApplySavedForecastDialogProps) {
  const applyForecast = api.data.applySavedScheduleHourlySalesForecast.useMutation();
  const apiUtil = api.useUtils();

  const handleApplyForecast = async () => {
    try {
      await applyForecast.mutateAsync({
        storeId: storeId,
        scheduleId: scheduleId,
        salesForecastJobId: forecastJob.id
      });

      toast.success("Forecast applied to schedule successfully");
      apiUtil.user.getSchedule.invalidate();
      apiUtil.data.getScheduleHourlySalesForecast.invalidate();
      onOpenChange(false);
    } catch (e) {
      toast.error("Error applying forecast: " + getHumanReadableErrorMessage(e));
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent size={"3xl"} className="overflow-y-auto min-h-[400px]" style={{maxHeight: "calc(100vh - 100px)"}}>
        <DialogHeader>
          <DialogTitle>Apply Saved Forecast</DialogTitle>
        </DialogHeader>

        <div className="space-y-4 mb-4">

          <p className="mb-4">
            This forecast, created on {DateTime.fromJSDate(forecastJob.createdAt).toLocaleString(DateTime.DATE_FULL)},
            is based on data that
            is <strong>{calculateDaysAge(forecastJob.dataRangeEnd, timezone)} days old</strong>.
            Are you sure you want to apply this forecast to your current schedule week?
          </p>

          <ForecastGraph
            forecast={forecastJob.forecast}
            dataRangeEnd={forecastJob.dataRangeEnd}
            timezone={timezone}
            height={300}
          />

        </div>

        <DialogFooter className={"justify-between"}>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <Button
            onClick={handleApplyForecast}
            isLoading={applyForecast.isPending}
            disabled={applyForecast.isPending}
          >
            Apply Forecast
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
