import React from 'react';
import {PersonAvatar} from "@/src/components/PersonAvatar.tsx";
import {Text} from "@/src/components/Text.tsx";
import {formatDateMonthNoTime} from "@/src/date.util.ts";
import {PersonDto} from "../../../api/src/schemas.ts";

export interface RequestCommentProps {
  person: PersonDto;
  date: Date;
  actionLabel: string;
  comment: string;
}

export const RequestComment: React.FC<RequestCommentProps> = ({person, date, actionLabel, comment}) => {
    return (
      <div>
        <div className={"flex items-center gap-3 mb-2"}>
          <PersonAvatar person={person}/>
          <div className={"flex flex-col gap-0"}>
            <Text size={"sm"}>
              {person.firstName} {person.lastName}
              <span className={"text-muted-foreground"}>
                  {' '}{actionLabel} on{' '}
                {formatDateMonthNoTime(date)}
                </span>
            </Text>
            <Text size={"sm"} muted>
              {person.jobTitle}
            </Text>
          </div>
        </div>
        <p>
          {comment || "(No comments provided)"}
        </p>
      </div>
    );
}
