import React from 'react';
import {Spinner} from "@/src/components/Spinner.tsx";

export interface FullscreenSpinnerOverlayProps {
  children?: React.ReactNode;
}

export const FullscreenSpinnerOverlay: React.FC<FullscreenSpinnerOverlayProps> = (props) => {
    return (
      <div className="flex h-screen w-screen items-center justify-center bg-opacity-50 bg-gray-800 fixed top-0 left-0 right-0 bottom-0">
        <div className="flex flex-col items-center space-y-4">
          <Spinner size={"xl"} className={"text-white"}/>
          {props.children}
        </div>
      </div>
    );
}
