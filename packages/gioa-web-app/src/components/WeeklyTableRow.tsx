import React, {useMemo} from 'react';
import {filter, groupBy, sum, times} from "lodash";
import {TableRow} from "@/src/components/ui/table.tsx";
import {cn} from "@/src/util.ts";
import {SchedulePersonClientDto, SchedulePersonTimeOffDto} from "../../../api/src/schedulePersonDto.ts";
import {WeeklyTableCell} from "@/src/components/WeeklyTableCell.tsx";
import {WeeklyTableAvailabilityCell} from "@/src/components/WeeklyTableAvailabilityCell.tsx";
import {WeeklyTableTimeOffCell} from "@/src/components/WeeklyTableTimeOffCell.tsx";
import {WeeklyTablePersonCell} from "@/src/components/WeeklyTablePersonCell.tsx";
import {WeeklyTableShift} from './WeeklyTableTypes.ts';
import {CellPosition} from "@/src/components/WeeklyTableCellContent.tsx";
import {DraggableData, DraggableEvent} from 'react-draggable';

export interface WeeklyTableRowProps {
  shifts: WeeklyTableShift[];
  person: SchedulePersonClientDto;
  totalWeekHours: number;
  filteredWeekHours: number;
  showAvailability: boolean;
  showTimeOff: boolean;
  timezone: string;
  year: number;
  week: number;
  onAddShift: (personId: string, day: number) => void;
  onDragStart: (shiftId: string, cellPosition: CellPosition) => void;
  onDragStop: (shiftId: string, data: DraggableData, e: DraggableEvent, cellPosition: CellPosition) => void;
  onShiftClick: (shift: WeeklyTableShift) => void;
  onTimeOffClick: (timeOff: SchedulePersonTimeOffDto) => void;
}

export const WeeklyTableRow = React.memo(({
                                            shifts, showAvailability, showTimeOff,
                                            person, timezone,
                                            onAddShift, onDragStart, onDragStop,
                                            totalWeekHours, onTimeOffClick,
                                            year, week,
                                            filteredWeekHours,
                                            onShiftClick,
                                          }: WeeklyTableRowProps) => {
  const rowSpan = sum([1, showAvailability ? 1 : 0, showTimeOff ? 1 : 0]);
  const shiftsByDay = useMemo(() => groupBy(shifts, s => s.isoWeek.day), [shifts]);

  return <>
    <tr className={cn("hover:bg-none!", {"border-b-2": !showAvailability && !showTimeOff})}>
      <WeeklyTablePersonCell person={person} rowSpan={rowSpan}
                             filteredWeekHours={filteredWeekHours} totalWeekHours={totalWeekHours}/>
      {times(7, i => {
        const dayOfWeek = (i + 1);
        const dayShifts = shiftsByDay[dayOfWeek];

        return <WeeklyTableCell shifts={dayShifts} key={i}
                                personId={person.id} weekday={dayOfWeek}
                                onAddShift={onAddShift} onDragStart={onDragStart} onDragStop={onDragStop}
                                onShiftClick={onShiftClick}/>
      })}
    </tr>
    {showAvailability ? <TableRow key={"avail"}>
      {times(7, i => {
        const dayOfWeek = (i + 1);
        const avail = filter(person.weekAvailability, a => a.dayOfWeek === dayOfWeek);

        return <WeeklyTableAvailabilityCell availability={avail} key={i}/>
      })}
    </TableRow> : null}
    {showTimeOff ? <TableRow key={"timeOff"}>
      {times(7, i => {
        const dayOfWeek = (i + 1);

        return <WeeklyTableTimeOffCell key={i} year={year} week={week} weekday={dayOfWeek}
                                       pendingTimeOff={person.pendingTimeOff} onTimeOffClick={onTimeOffClick}
                                       timezone={timezone} timeOff={person.timeOff}/>
      })}
    </TableRow> : null}
  </>
});

WeeklyTableRow.displayName = "WeeklyTableRow";
