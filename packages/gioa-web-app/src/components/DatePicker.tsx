import * as React from "react"
import {Calendar as CalendarIcon} from "lucide-react"
import {SelectSingleEventHandler} from "react-day-picker"
import {Button} from "@/src/components/ui/button"
import {Calendar} from "@/src/components/ui/calendar"
import {Popover, PopoverContent, PopoverTrigger} from "@/src/components/ui/popover"
import {cn} from "@/src/util.ts";
import {DateTime, DateTimeFormatOptions} from "luxon";

export interface DatePickerProps extends Omit<React.HTMLAttributes<HTMLDivElement>, "value" | "onChange"> {
  value: DateTime | undefined;
  onChange: (date: DateTime) => void;
  today: DateTime;
  labelFormat?: DateTimeFormatOptions;
  children?: React.ReactNode;
}

export function DatePicker({className, value, onChange, today,
                                 labelFormat = DateTime.DATE_MED, children
}: DatePickerProps) {

  const onSelect: SelectSingleEventHandler = (day) => {
    if (day) {
      onChange(DateTime.fromJSDate(day));
    }
  }

  const onGoToToday = () => {
    onChange(today);
  }

  return (
    <div className={cn("grid gap-2", className)}>
      <Popover>
        <PopoverTrigger asChild>
          {
            children
                    ? children
                    : <Button
                        id="date"
                        variant={"outline"}
                        className={cn(
                              "justify-start text-left font-normal",
                              !value && "text-muted-foreground"
                        )}>
                        <CalendarIcon className="mr-2 h-4 w-4"/>
                        {value
                              ? value.toLocaleString(labelFormat)
                              : <span>Pick a date</span>}
                      </Button>
          }
        </PopoverTrigger>
        <PopoverContent className="w-auto p-0" align="start">
          <div className={"flex gap-2 justify-start py-3 px-3 border-b items-center"}>
            Go to:
            <Button onClick={onGoToToday}
                    variant={"secondary"} size={"sm"}>
              Today
            </Button>
          </div>
          <Calendar
            initialFocus
            showOutsideDays
            ISOWeek={true}
            defaultMonth={value?.toJSDate()}
            mode="single"
            selected={value?.toJSDate()}
            onSelect={onSelect}
            numberOfMonths={1}
          />
        </PopoverContent>
      </Popover>
    </div>
  );
}
