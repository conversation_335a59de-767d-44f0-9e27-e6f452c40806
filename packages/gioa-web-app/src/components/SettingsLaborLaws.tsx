import React from 'react';
import {Text} from "@/src/components/Text.tsx";
import {Button} from "@/src/components/ui/button.tsx";
import {Heading} from "@/src/components/Heading.tsx";
import {LaborLawsForm} from "@/src/components/LaborLawsForm.tsx";
import {toast} from "sonner";

export interface SettingsLaborLawsProps {
  storeId: string;
}

export const SettingsLaborLaws: React.FC<SettingsLaborLawsProps> = ({storeId}) => {
  const [isLoading, setIsLoading] = React.useState(false);
  const formRef = React.useRef<{ submit: () => Promise<void> }>(null);
  const handleSaveAndContinue = async () => {
    if (formRef.current) {
      await formRef.current.submit();
    }
  }

  const onContinue = () => {
    toast.success("Labor laws updated", {
      position: "top-center",
    });
  }

  return (
    <form onSubmit={async (e) => {
      e.preventDefault()
      e.stopPropagation()
      await handleSaveAndContinue()
    }}>
      <Heading level={1}>Break Rules</Heading>
      <div className="mt-8 mb-3">
        <Text muted>
          Edit your store’s required labor laws. Below are recommended labor laws based on your specified location.
        </Text>
      </div>

      <LaborLawsForm ref={formRef}
                     storeId={storeId}
                     setIsLoading={setIsLoading}
                     onContinue={onContinue}/>
      <div className={"my-3"}>
        <Text muted size={"xs"}>
          The Nation app is not responsible for ensuring compliance with federal, state, or local laws.
        </Text>
      </div>

      <div className={"mt-8"}>
        <Button type={"submit"} isLoading={isLoading}>Save</Button>
      </div>
    </form>
  );
}
