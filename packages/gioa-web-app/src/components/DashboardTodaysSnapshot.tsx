import React, {useMemo} from 'react';
import {Card, CardContent} from '@/src/components/ui/card';
import {Button} from '@/src/components/ui/button';
import {Text} from '@/src/components/Text';
import {Separator} from '@/src/components/ui/separator.tsx';
import {ShiftLead} from '@/src/components/ShiftLead.tsx';
import peopleIcon from '@/src/assets/people_groups_icon.svg';
import personEditIcon from '@/src/assets/person_edit.svg';
import celebrationIcon from '@/src/assets/celebration_icon.svg';
import chartIcon from '@/src/assets/chart_icon.svg';
import {filter, find, first, flatMap, map, partition} from 'lodash';
import {Link} from '@/src/components/Link.tsx';
import {api} from '@/src/api.ts';
import {getMetrics} from '../../../api/src/scheduling/metrics/metrics.util.ts';
import {DateTime} from 'luxon';
import {Shift} from '../../../api/src/scheduleSchemas.ts';
import {SchedulePersonDto} from "../../../api/src/schedulePersonDto.ts";
import {
  getAveragePayRate,
  getForecastTotalProjectedRevenueForDay
} from '@gioa/api/src/scheduling/metrics/scheduleHourlySalesForecast/scheduleHourlySalesForecast.ts';
import {formatCurrency} from "../../../common/src/dataFormatters.ts";
import {dollars} from '@gioa/api/src/scheduling/metrics/dollars.ts';
import {fromSchedulePeople} from '@gioa/api/src/scheduling/metrics/payRates/payRates.ts';
import {useNavigate} from "@tanstack/react-router";

interface SnapshotCardProps {
  icon: React.ReactNode;
  title: string;
  value: string | number;
  subtitle: string | React.ReactNode;
  bgColor: string;
  linkTo?: string;
  linkSearch?: any;
}

const SnapshotCard: React.FC<SnapshotCardProps> = ({icon, title, value, subtitle, bgColor, linkTo, linkSearch}) => (
  <Card className={`${bgColor} p-4 border-0`}>
    <Link to={linkTo} search={linkSearch}>
      <CardContent className="flex flex-col items-start p-0 space-y-2">
        {icon}
        <Text size="xl" className="font-bold mb-1">{value}</Text>
        <Text size="sm" className="font-medium mb-1">{title}</Text>
        {typeof subtitle === 'string' ? <Text size="xs" colorScheme="primary">{subtitle}</Text> : subtitle}
      </CardContent>
    </Link>
  </Card>
);

export interface TodaysSnapshotProps {
  storeId: string;
  businessId: string;
  allPeopleAtStore: SchedulePersonDto[];
}

const calculateRevenuePercentageChange = (
  todayRevenue: number,
  lastWeekRevenue: number,
  currentDay: string
) => {
  if (todayRevenue > 0 && lastWeekRevenue > 0) {
    const change = ((todayRevenue - lastWeekRevenue) / lastWeekRevenue) * 100;
    return `${change > 0 ? '+' : ''}${change.toFixed(2)}% ${change > 0 ? 'increase' : 'decrease'} from last ${currentDay}`;
  }
  return `No change from last ${currentDay}`;
};

export const TodaysSnapshot: React.FC<TodaysSnapshotProps> = ({storeId, businessId, allPeopleAtStore}) => {
  const [[store, {dayOfWeek, schedule, forecast}]] = api.useSuspenseQueries(t => {
    return [
      t.user.getStoreAdmin({
        storeId: storeId!
      }),
      t.user.getPublishedScheduleForThisWeek({
        storeId: storeId!
      }, {
        refetchInterval: 1000 * 60 * 15 // 15 minutes
      }),
    ]
  })
  const payRates = useMemo(() => fromSchedulePeople(allPeopleAtStore), [allPeopleAtStore]);

  const navigate = useNavigate();
  const [now] = React.useState<DateTime>(DateTime.now().setZone(store.timezone ?? "America/New_York"));
  const weather = api.user.getWeatherForecast.useQuery({zipCode: store.address?.zipCode ?? ""}, {
    enabled: Boolean(store.address?.zipCode)
  });
  const todayDateFormatted = now.toFormat("yyyy-MM-dd");
  const todayWeather = find(weather.data?.weather?.days, d => d.date === todayDateFormatted);
  const oneWeekAgo = now.minus({weeks: 1});

  const schedulesQueryResult = api.user.getScheduleWeeks.useQuery({
    storeId,
    onOrAfter: {year: oneWeekAgo.weekYear, week: oneWeekAgo.weekNumber},
    take: 1
  });

  const scheduleEventsQueryResult = api.user.getScheduleEvents.useQuery(
    {week: {year: now.weekYear, week: now.weekNumber}, storeId},
    {staleTime: 1000 * 60 * 15}
  );

  const startOfToday = now.startOf('day').toJSDate();
  const endOfToday = now.endOf('day').toJSDate();
  const todaysEvents = filter(scheduleEventsQueryResult.data, evt => {
    return (evt.range.start >= startOfToday && evt.range.start <= endOfToday)
      || (evt.range.end >= startOfToday && evt.range.end <= endOfToday)
      || (evt.range.start <= startOfToday && evt.range.end >= endOfToday);
  });


  const queryResultTimeOffRequests = api.user.getStoreTimeOffRequests.useQuery({
    storeId,
    status: 'pending'
  });

  const queryResultShiftOffers = api.user.getShiftOffers.useQuery({
    storeId,
    status: 'pending',
    appVersion: "web"
  });

  const queryResultAvailabilityRequests = api.user.getAvailabilityRequests.useQuery({
    storeId,
    status: 'pending'
  });

  const timeOffRequestsCount = queryResultTimeOffRequests.data?.items.length || 0;
  const availabilityRequestsCount = queryResultAvailabilityRequests.data?.items.length || 0;
  const shiftOffersCount = queryResultShiftOffers.data?.items.length || 0;
  const needsAttentionTotalCount = timeOffRequestsCount + availabilityRequestsCount + shiftOffersCount;

  const today = find(schedule?.days, d => d.dayOfWeek === dayOfWeek);
  const shifts = flatMap(today?.areas, a => a.shifts);
  const [, assignedShifts] = partition(shifts, s => s.assignedPersonId === undefined);
  const todayMetrics = schedule ? getMetrics({
    schedule: schedule,
    dayOfWeek: dayOfWeek,
    countOpenShiftsTowardsLabor: false,
    payRates: payRates,
    averagePayRate: forecast ? getAveragePayRate(forecast) : undefined,
  }) : undefined;

  const todayShiftLeaders = filter(flatMap(today?.areas, a => a.shifts), {isShiftLead: true});
  const shiftLeadersShifts = map(todayShiftLeaders, (shift: Shift) => ({
    person: find(allPeopleAtStore, p => p.id === shift.assignedPersonId) || undefined,
    range: shift.range,
  }));

  const todayProjectedRevenue = forecast
    ? getForecastTotalProjectedRevenueForDay(forecast.dataPoints, dayOfWeek)
    : dollars(0);
  const todayProjectedRevenueFormatted = formatCurrency(todayProjectedRevenue);

  const previousSchedule = first(schedulesQueryResult.data)?.published;
  const previousForecast = first(schedulesQueryResult.data)?.forecast;
  const lastWeeksDay = find(previousSchedule?.days, d => d.dayOfWeek === dayOfWeek);
  const lastWeeksDayProjectedRevenue = previousForecast && lastWeeksDay
    ? getForecastTotalProjectedRevenueForDay(previousForecast.dataPoints, lastWeeksDay.dayOfWeek)
    : dollars(0);

  const weeklyProjectedRevenuePercentageIncreaseFormatted = calculateRevenuePercentageChange(
    todayProjectedRevenue,
    lastWeeksDayProjectedRevenue,
    now.weekdayLong!
  );

  const handleGoToSchedule = () => {
    if (schedule) {
      navigate({
        to: `/${businessId}/${storeId}/schedules/${schedule?.id ? `${schedule.id}` : ''}`,
        search: {dayOfWeek}
      })
    } else {
      const now = DateTime.now().setZone(store.timezone ?? "America/New_York");
      navigate({
        to: "/$businessId/$storeId/schedules/builder",
        params: {
          businessId: businessId,
          storeId: storeId,
        },
        search: {
          week: now.weekNumber,
          year: now.weekYear,
        }
      });
    }
  }

  return (
    <div className="bg-white p-6 rounded-lg shadow">
      <div className="flex justify-between mb-6">
        <div>
          <Text size="lg" className="mb-2">Today's Snapshot</Text>
          {todayWeather
            ? <div className="flex items-center">
              <img src={todayWeather?.weatherIcon} alt="Weather data by WeatherAPI.com"/>
              <div className={"flex flex-col gap-0 items-center"}>
                <Text size={"xs"}>{Math.round(todayWeather.highTempF)}°F / {Math.round(todayWeather.lowTempF)}°F</Text>
                <Text size={"xs"}>high low</Text>
              </div>
            </div>
            : null}
        </div>
        <Button variant="outline" onClick={handleGoToSchedule}>
          <Text size="sm">Go to Schedule</Text>
        </Button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <SnapshotCard
          icon={<div className="rounded-full bg-green-400 w-10 h-10 flex items-center justify-center">
            <img src={peopleIcon} alt="people"/>
          </div>}
          title="Active on Shifts "
          value={assignedShifts.length}
          subtitle={`${todayMetrics?.totalLaborHours?.toFixed(0) || 0} Labor Hrs`}
          bgColor="bg-green-100"
          linkTo={schedule?.id
                  ? `/${businessId}/${storeId}/schedules/${schedule?.id}`
                  : `/${businessId}/${storeId}/schedules/builder`}
          linkSearch={{dayOfWeek: dayOfWeek}}
        />
        <SnapshotCard
          icon={<div className="rounded-full bg-red-400 w-10 h-10 flex items-center justify-center">
            <img src={personEditIcon} alt="people"/>
          </div>}
          title="Needs Attention"
          value={needsAttentionTotalCount}
          subtitle="See Details"
          bgColor="bg-red-100"
          linkTo={`/${businessId}/${storeId}/schedules/requests/availability`}
        />
        <SnapshotCard
          icon={<div className="rounded-full bg-purple-400 w-10 h-10 flex items-center justify-center">
            <img src={celebrationIcon} alt="people"/>
          </div>}
          title="Events"
          value={todaysEvents.length}
          subtitle=" " // TODO: View Events link
          bgColor="bg-purple-100"
        />
        <SnapshotCard
          icon={<div className="rounded-full bg-yellow-400 w-10 h-10 flex items-center justify-center">
            <img src={chartIcon} alt="people"/>
          </div>}
          title="Proj. Revenue"
          value={todayProjectedRevenueFormatted}
          subtitle={weeklyProjectedRevenuePercentageIncreaseFormatted}
          bgColor="bg-yellow-100"
        />
      </div>

      <Separator className="my-6 mb-3"/>

      <div className="flex flex-col md:flex-row gap-4">
        {map(shiftLeadersShifts, (shiftLead, index) => (
          <ShiftLead
            key={shiftLead.person?.id ?? index}
            person={shiftLead.person}
            range={shiftLead.range}
          />
        ))}
      </div>
    </div>
  );
};

export default TodaysSnapshot;
