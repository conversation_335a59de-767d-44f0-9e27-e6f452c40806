import React, { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Title } from "@/src/components/ui/dialog";
import { Button } from "@/src/components/ui/button";
import { api } from "@/src/api";
import { isEmpty, map } from "lodash";
import { Checkbox } from "@/src/components/ui/checkbox";
import { Label } from "@/src/components/ui/label";
import { FormControl } from "@/src/components/form/FormControl";
import { FormInput } from "@/src/components/form/FormInput";
import { FieldInfo } from "@/src/components/form/FieldInfo";
import { FieldApi, useForm } from "@tanstack/react-form";
import { zodValidator } from "@tanstack/zod-form-adapter";
import { z } from "zod";
import { PlusIcon, X } from "lucide-react";
import { toast } from "sonner";
import { Badge } from "@/src/components/ui/badge.tsx";

interface SelectPolicyModalProps {
  field: FieldApi<any, any, any, any>;
}

export function FormSelectPolicy({ field }: SelectPolicyModalProps) {
  const [isOpen, setIsOpen] = useState(false);
  const onClose = () => {
    form.reset();
    setIsOpen(false);
  };

  const [selectedPolicies, setSelectedPolicies] = useState<Record<string, boolean>>({});
  const [isCreatingPolicy, setIsCreatingPolicy] = useState(false);

  React.useEffect(() => {
    setSelectedPolicies(
      field.state.value.reduce(
        (acc: Record<string, boolean>, policy: string) => {
          acc[policy] = true;
          return acc;
        },
        {} as Record<string, boolean>,
      ),
    );
  }, [field.state.value, isOpen]);

  const { data: policies = [] } = api.user.getCorrectiveActionPolicies.useQuery();
  const apiUtil = api.useUtils();

  const addPolicyInAction = api.user.addPolicyInAction.useMutation({
    onSuccess: () => {
      apiUtil.user.getCorrectiveActionPolicies.invalidate();
      form.reset();
      setIsCreatingPolicy(false);
      toast.success("Policy added successfully");
    },
    onError: (error) => {
      toast.error("Failed to add policy: " + error.message);
    },
  });

  const deletePolicy = api.user.deleteCorrectiveActionPolicy.useMutation({
    onSuccess: () => {
      apiUtil.user.getCorrectiveActionPolicies.invalidate();
      toast.success("Policy deleted successfully");
    },
    onError: (error) => {
      toast.error("Failed to delete policy: " + error.message);
    },
  });

  const form = useForm({
    defaultValues: {
      title: "",
    },
    validatorAdapter: zodValidator(),
    onSubmit: async ({ value }) => {
      addPolicyInAction.mutate({
        title: value.title,
      });
    },
  });

  const togglePolicy = (policy: string) => {
    setSelectedPolicies((prev) => ({
      ...prev,
      [policy]: !prev[policy],
    }));
  };

  const handleDeletePolicy = (policy: string) => {
    if (
      confirm(
        `Are you sure you want to delete the policy "${policy}"? Previous incident reports that use this policy will NOT be impacted.`,
      )
    ) {
      deletePolicy.mutate({
        policyTitle: policy,
      });

      if (selectedPolicies[policy]) {
        const newSelectedPolicies = { ...selectedPolicies };
        delete newSelectedPolicies[policy];
        setSelectedPolicies(newSelectedPolicies);
      }
    }
  };

  const handleDone = () => {
    const selectedPoliciesArray = Object.keys(selectedPolicies).filter((policy) => selectedPolicies[policy]);
    field.handleChange(selectedPoliciesArray);
    onClose();
  };

  return (
    <div>
      <div onClick={() => setIsOpen(true)}>
        {isEmpty(field.state.value) ? (
          <div>
            <Button variant="outline" type={"button"}>
              Select Policies...
            </Button>
          </div>
        ) : (
          <div className="flex flex-row flex-wrap gap-2 items-center">
            {map(field.state.value, (policy) => (
              <Badge key={policy} colorScheme="default" size={"md"}>
                {policy}
              </Badge>
            ))}
            <Button variant="outline" size="icon" type={"button"}>
              <PlusIcon size={16} />
            </Button>
          </div>
        )}
      </div>

      <Dialog open={isOpen} onOpenChange={setIsOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Select Policies</DialogTitle>
          </DialogHeader>

          <div className="max-h-[60vh] overflow-y-auto">
            {map(policies, (policy) => (
              <div key={policy} className="flex items-center justify-between cursor-pointer py-1 border-b border-gray-200" onClick={() => togglePolicy(policy)}>
                <div className="flex items-center space-x-3">
                  <Checkbox
                    id={`policy-${policy}`}
                    checked={selectedPolicies?.[policy] || false}
                  />
                  <Label>
                    {policy}
                  </Label>
                </div>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={(e) => {
                    e.stopPropagation();
                    handleDeletePolicy(policy)
                  }}
                  className="h-8 w-8"
                  type={"button"}
                >
                  <X size={16} />
                </Button>
              </div>
            ))}

            {isCreatingPolicy ? (
              <div className="mt-4 p-3 border border-gray-200 rounded-md">
                <form.Field
                  name="title"
                  validators={{
                    onSubmit: z.string().min(1, "Required"),
                  }}
                  children={(field) => (
                    <FormControl>
                      <Label>New Policy Title</Label>
                      <FormInput field={field} placeholder="Enter policy title..." />
                      <FieldInfo field={field} />
                    </FormControl>
                  )}
                />
                <div className="flex space-x-2 mt-2">
                  <Button type="button" onClick={form.handleSubmit} disabled={addPolicyInAction.isPending} size="sm">
                    {addPolicyInAction.isPending ? "Saving..." : "Save"}
                  </Button>
                  <Button type="button" variant="outline" onClick={() => setIsCreatingPolicy(false)} size="sm">
                    Cancel
                  </Button>
                </div>
              </div>
            ) : (
              <Button variant="link" onClick={() => setIsCreatingPolicy(true)} className="mt-2">
                + Add New Policy
              </Button>
            )}
          </div>

          <DialogFooter>
            <Button type="button" onClick={handleDone}>
              Done
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
