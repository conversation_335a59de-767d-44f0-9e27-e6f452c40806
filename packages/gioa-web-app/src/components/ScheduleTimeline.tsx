import React, {useCallback, useMemo, useRef, useState} from 'react';
import {filter, find, groupBy, map, mapKeys, mapValues, times} from "lodash";
import {DailyTimeRange, DayOfWeek, IsoWeekDate} from "../../../api/src/timeSchemas.ts";
import {cn} from "@/src/util.ts";
import {PeakHours} from "../../../api/src/scheduleSchemas.ts";
import {ScheduleCalendarColors} from "../../../api/src/scheduleCalendars.ts";
import {
  incrementToFriendlyTime,
  ScheduleBuilderEvent,
  ScheduleRowInfo,
  storeTimeToIncrement
} from "../../../api/src/scheduleBuilder.util.ts";
import {ScheduleTimelineEvents} from "@/src/components/ScheduleTimelineEvents.tsx";

export interface ScheduleTimelineProps {
  width: number;
  topbarHeight: number;
  numIncrements: number;
  incrementWidth: number;
  timelineHeight: number;
  isEventsOpen: boolean;
  events: ScheduleBuilderEvent[];
  shifts: ScheduleRowInfo[];
  leftSidebarRef: React.RefObject<HTMLDivElement>;
  storeHours: DailyTimeRange;
  dayOfWeek: DayOfWeek;
  scheduleWeek: IsoWeekDate;
  onOpenEvent: (eventId: string) => void;
  selectedEventId?: string;
  peakHours: PeakHours;
  isPeakHoursOpen: boolean;
}


const opacity = "70";

export const ScheduleCalendars: Record<string, {title: string, color: string}> = {
  "storeEvent": {title: "Store Event", color: `${ScheduleCalendarColors.storeEvent}${opacity}`},
  "meeting": {title: "Meeting", color: `${ScheduleCalendarColors.meeting}${opacity}`},
  "catering": {title: "Catering", color: `${ScheduleCalendarColors.catering}${opacity}`},
  "localEvent": {title: "Local Event", color: `${ScheduleCalendarColors.localEvent}${opacity}`},
  "holiday": {title: "Holiday", color: `${ScheduleCalendarColors.holiday}${opacity}`},
  "other": {title: "Other", color: `${ScheduleCalendarColors.other}${opacity}`},
}

const InfoItem: React.FC<{ label: string; value: number | string }> = ({label, value}) => (
  <div>
    <div className="text-gray-500 text-xs">{label}</div>
    <div className={"text-sm"}>{value}</div>
  </div>
);

const InfoSeparator: React.FC = () => (
  <div className={"w-[1px] h-[32px] bg-gray-300"}/>
);

export const ScheduleTimeline: React.FC<ScheduleTimelineProps> = ({
                                                                    width,
                                                                    topbarHeight,
                                                                    numIncrements,
                                                                    incrementWidth,
                                                                    timelineHeight,
                                                                    isEventsOpen,
                                                                    events,
                                                                    shifts,
                                                                    leftSidebarRef,
                                                                    storeHours,
                                                                    dayOfWeek,
                                                                    scheduleWeek,
                                                                    peakHours,
                                                                    onOpenEvent,
                                                                    isPeakHoursOpen,
                                                                    selectedEventId,
                                                                  }) => {
  const lineRef = React.useRef<HTMLDivElement>(null);
  const rafRef = useRef<number>();
  const lastMouseXRef = useRef<number>();
  const todaysPeakHours = filter(peakHours, ph => ph.dayOfWeek === dayOfWeek);

  const updateLinePosition = useCallback(() => {
    if (lineRef.current && lastMouseXRef.current !== null) {
      lineRef.current.style.transform = `translateX(${lastMouseXRef.current}px)`;
    }
    rafRef.current = undefined;
  }, []);

  const scheduleUpdate = useCallback(() => {
    if (!rafRef.current) {
      rafRef.current = requestAnimationFrame(updateLinePosition);
    }
  }, [updateLinePosition]);
  const [scrubbedIncrement, setScrubbedIncrement] = useState<number>(0);
  const metricsAtScrubbedIncrement = useMemo(() => {
    const shiftsAtIncrement = filter(shifts, shift => shift.type === "shift" && shift.start <= scrubbedIncrement && shift.end > scrubbedIncrement && Boolean(shift.assignedTo));

    const areaIdToShifts = groupBy(shiftsAtIncrement, shift => shift.areaId);
    const areaIdToShiftCount = mapValues(areaIdToShifts, shifts => shifts.length);
    const areaToShiftCount = mapKeys(areaIdToShiftCount, (_, areaId) =>
      find(shifts, s => s.type === "area" && s.id === areaId)?.title ?? "Unknown Area");

    return {
      totalShiftCount: filter(shiftsAtIncrement, shift => Boolean(shift.assignedTo)).length,
      areaToShiftCount,
    };
  }, [shifts, scrubbedIncrement]);

  const onTimelineMouseMove = useCallback((event: React.MouseEvent) => {
    const containerRect = event.currentTarget.getBoundingClientRect();
    lastMouseXRef.current = event.clientX - containerRect.left;
    scheduleUpdate();

    // calculate the increment that the scrubber is on
    const increment = Math.floor(lastMouseXRef.current / incrementWidth);
    setScrubbedIncrement(increment);
  }, [scheduleUpdate, incrementWidth]);

  return (
    <div style={{
      minWidth: width,
      height: topbarHeight,
      background: `rgba(255, 255, 255, 0.8)`,
      backdropFilter: `blur(2px)`
    }}
         onMouseMove={onTimelineMouseMove}
         className={"z-10 rounded-lg sticky mx-4 border-b border-slate-200 shadow-sm top-0"}>
      <div className={"group h-full"}>
        {times(numIncrements / 4 + 1, (markIndex) => {
          const isLast = markIndex === numIncrements / 4;
          return <div key={markIndex} style={{width: incrementWidth * 4}}
                      className={cn("inline-block border-slate-300 h-full py-3 px-2", !isLast ? "border-r" : undefined)}>
            {incrementToFriendlyTime(storeHours, markIndex * 4, true)}
          </div>;
        })}
        {/*Vertical Scrubber indicator line with a caret on top*/}
        <div ref={lineRef}
             className={"absolute pointer-events-none transition-opacity opacity-0 group-hover:opacity-100 -top-1 left-0 w-0.5 bg-primary-600 before:content-[''] before:absolute before:-top-1 before:left-1/2 before:-translate-x-1/2 before:border-8 before:border-transparent before:border-b-0 before:border-t-primary-600 will-change-transform z-50"}
             style={{bottom: -((leftSidebarRef.current?.clientHeight ?? 300) - topbarHeight - 20) + "px"}}>
          <div
            className={"absolute bg-white shadow-lg rounded-full px-6 py-2 left-1/2 -translate-x-1/2 border border-primary-100 whitespace-nowrap flex items-center gap-2 flex-nowrap"}
            style={{top: topbarHeight}}>
            <InfoItem label={"Time"} value={incrementToFriendlyTime(storeHours, scrubbedIncrement)}/>
            <InfoSeparator/>
            <InfoItem label="Total" value={metricsAtScrubbedIncrement.totalShiftCount}/>
            {map(metricsAtScrubbedIncrement.areaToShiftCount, (shiftCount, area, idx) => {
              return <React.Fragment key={area + idx}>
                <InfoSeparator/>
                <InfoItem label={area} value={shiftCount}/>
              </React.Fragment>
            })}
          </div>
        </div>

        {
          isPeakHoursOpen ? map(todaysPeakHours, (peakHour, peakHourIdx) => {
            const peakHourIncStart = storeTimeToIncrement(storeHours, peakHour.start);
            const peakHourIncEnd = storeTimeToIncrement(storeHours, peakHour.end);
            const isAtStart = peakHourIncStart === 0;
            const isAtEnd = peakHourIncEnd === numIncrements;

            return <div key={peakHourIdx} style={{
              left: peakHourIncStart * incrementWidth + (isAtStart ? 8 : 0),
              top: 4,
              bottom: topbarHeight - timelineHeight + 4,
              width: ((peakHourIncEnd - peakHourIncStart) * incrementWidth) + (isAtStart || isAtEnd ? -8 : 0),
              backgroundColor: "rgba(255, 0, 0, 0.3)",
            }} className={"absolute rounded-md py-1 px-2 opacity-${opacity}"}>
            </div>
          }) : null}
      </div>

      <ScheduleTimelineEvents isEventsOpen={isEventsOpen} events={events} onOpenEvent={onOpenEvent}
                              selectedEventId={selectedEventId} numIncrements={numIncrements}
                              incrementWidth={incrementWidth} timelineHeight={timelineHeight}/>
    </div>
  );
}
