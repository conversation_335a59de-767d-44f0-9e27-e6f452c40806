import { Popover, PopoverContent, PopoverTrigger } from "@/src/components/ui/popover.tsx";
import { cn } from "@/src/util.ts";
import React from "react";
import { Text } from "@/src/components/Text.tsx";
import { <PERSON><PERSON> } from "@/src/components/ui/button.tsx";
import { useForm } from "@tanstack/react-form";
import { map } from "lodash";
import { Separator } from "@/src/components/ui/separator.tsx";
import { FormCheckbox } from "@/src/components/form/FormCheckbox.tsx";
import { FormControl } from "@/src/components/form/FormControl.tsx";
import { Label } from "@/src/components/ui/label.tsx";
import { FieldInfo } from "@/src/components/form/FieldInfo.tsx";
import { FormSelect } from "@/src/components/form/FormSelect.tsx";
import { Switch } from "@/src/components/ui/switch.tsx";
import { ScrollArea } from "@/src/components/ui/scroll-area.tsx";
import { NotesFilterFormValues } from "@/src/components/Notes.util.ts";
import { useDisclosure } from "@/src/hooks/useDisclosure.tsx";
import { Checkbox } from "@/src/components/ui/checkbox.tsx";

export interface NotesFilterPopoverProps {
  children: React.ReactNode;
  onReset: () => void;
  policies: string[];
  initialValues?: Partial<NotesFilterFormValues>;
  onSubmit: (values: NotesFilterFormValues) => void;
}

export function NotesFilterPopover({ children, onReset, policies, initialValues, onSubmit }: NotesFilterPopoverProps) {
  const popover = useDisclosure();

  const defaultValues: NotesFilterFormValues = {
    orderBy: "recent",
    showCorrectiveActions: true,
    showCoaching: true,
    showFeedback: true,
    showGeneral: true,
    showPositionScores: true,
    pastDays: undefined,
    policies: policies,
    usePolicyFilter: false,
    includeArchived: false,
  };

  const form = useForm({
    defaultValues: {
      ...defaultValues,
      ...initialValues,
    },
    onSubmit: ({ value }) => {
      onSubmit(value);
      popover.onClose();
    },
  });

  const resetForm = () => {
    form.store.setState((state) => ({
      ...state,
      values: defaultValues,
    }));
    onReset();
    onSubmit(defaultValues);
  };

  const showPolicyFilterSwitch = form.useStore(
    (state) => state.values.showCorrectiveActions || state.values.showCoaching,
  );
  const showPolicyList = form.useStore((state) => state.values.usePolicyFilter);

  return (
    <Popover open={popover.isOpen} onOpenChange={popover.setOpen}>
      <PopoverTrigger asChild>{children}</PopoverTrigger>
      <PopoverContent className={cn("p-4 w-[420px] h-full overflow-auto")} style={{ maxHeight: "70vh" }}>
        <div className="flex flex-row justify-between items-center mb-4">
          <Button variant="link" onClick={resetForm}>
            Reset
          </Button>
          <Text size="lg" semibold className="text-center flex-1">
            Filters
          </Text>
          <Button variant="link" onClick={popover.onClose}>
            Close
          </Button>
        </div>

        <ScrollArea className="pr-4">
          <div className="flex flex-row justify-between items-center mb-2">
            <div className="flex-1">
              <Text semibold>Sort by</Text>
            </div>
            <div className="flex-1">
              <form.Field
                name="orderBy"
                children={(field) => (
                  <FormSelect
                    field={field}
                    options={[
                      { label: "Recent First", value: "recent" },
                      { label: "Oldest First", value: "oldest" },
                    ]}
                  />
                )}
              />
            </div>
          </div>

          <Separator className="my-3" />

          <div className="mb-2">
            <Text semibold>Type</Text>
          </div>

          <form.Field
            name="showCorrectiveActions"
            children={(field) => (
              <label className="flex justify-between items-center p-3 border rounded-md border-gray-300 mb-2">
                <Text>Corrective Actions</Text>
                <FormCheckbox field={field} />
              </label>
            )}
          />

          <form.Field
            name="showCoaching"
            children={(field) => (
              <label className="flex justify-between items-center p-3 border rounded-md border-gray-300 mb-2">
                <Text>Coaching Moments</Text>
                <FormCheckbox field={field} />
              </label>
            )}
          />

          <form.Field
            name="showFeedback"
            children={(field) => (
              <label className="flex justify-between items-center p-3 border rounded-md border-gray-300 mb-2">
                <Text>Positive Feedback</Text>
                <FormCheckbox field={field} />
              </label>
            )}
          />

          <form.Field
            name="showGeneral"
            children={(field) => (
              <label className="flex justify-between items-center p-3 border rounded-md border-gray-300 mb-2">
                <Text>General Notes</Text>
                <FormCheckbox field={field} />
              </label>
            )}
          />

          <form.Field
            name="showPositionScores"
            children={(field) => (
              <label className="flex justify-between items-center p-3 border rounded-md border-gray-300 mb-2">
                <Text>Position Scores</Text>
                <FormCheckbox field={field} />
              </label>
            )}
          />

          <Separator className="my-3" />

          <div className={cn({ "opacity-0": !showPolicyFilterSwitch })}>
            <form.Field
              name="usePolicyFilter"
              children={(field) => (
                <FormControl>
                  <div className="flex items-center space-x-2 my-2">
                    <Switch
                      id={field.name}
                      checked={field.state.value}
                      onCheckedChange={field.handleChange}
                      disabled={!showPolicyFilterSwitch}
                    />
                    <Label htmlFor={field.name}>Filter Policy Violations</Label>
                  </div>
                  <FieldInfo field={field} />
                </FormControl>
              )}
            />

            {showPolicyFilterSwitch && showPolicyList ? (
              <form.Field
                name="policies"
                children={(field) => (
                  <>
                    {map(policies, (policy) => {
                      const onValueChange = () => {
                        console.log("onValueChange", field.state.value);
                        const newValue = field.state.value.includes(policy)
                          ? field.state.value.filter((p) => p !== policy)
                          : [...field.state.value, policy];
                        field.handleChange(newValue);
                      };
                      return (
                        <div
                          key={policy}
                          className="flex justify-between items-center p-3 border rounded-md border-gray-300 mb-2 cursor-pointer"
                          onClick={onValueChange}
                        >
                          <Text>{policy}</Text>
                          <Checkbox id="trained" checked={field.state.value.includes(policy)} />
                        </div>
                      );
                    })}
                  </>
                )}
              />
            ) : null}
          </div>

          <form.Field
            name="includeArchived"
            children={(field) => (
              <FormControl>
                <div className="flex items-center space-x-2 my-2">
                  <Switch id={field.name} checked={field.state.value} onCheckedChange={field.handleChange} />
                  <Label htmlFor={field.name}>Include Archived</Label>
                </div>
                <FieldInfo field={field} />
              </FormControl>
            )}
          />
        </ScrollArea>

        <Button className="w-full mt-4" onClick={form.handleSubmit}>
          View Results
        </Button>
      </PopoverContent>
    </Popover>
  );
}
