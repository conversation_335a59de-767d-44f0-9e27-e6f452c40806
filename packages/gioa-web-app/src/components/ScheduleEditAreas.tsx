import React, {useEffect, useState} from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger
} from "@/src/components/ui/dialog.tsx";
import {Button} from "@/src/components/ui/button.tsx";
import {find, map} from "lodash";
import {Table, TableBody, TableCell, TableHead, TableHeader, TableRow} from './ui/table';
import {Checkbox} from "./ui/checkbox";
import {daysOfWeek} from "@/src/components/AvailabilityWeek.tsx";
import {ArrowDownIcon, ArrowUpIcon, PlusIcon, TrashIcon} from "lucide-react";
import {useAutoAnimate} from '@formkit/auto-animate/react'
import {useDisclosure} from "@/src/hooks/useDisclosure.tsx";
import {EditAreaDialogForm} from "@/src/components/EditAreaDialogForm.tsx";
import {genScheduleAreaId, StoreAreaDto} from "../../../api/src/schemas.ts";
import {api} from "@/src/api.ts";
import {ScheduleArea} from "../../../api/src/scheduleBuilder.util.ts";

export interface ScheduleEditAreasProps {
  areas: ScheduleArea[];
  dayOfWeek: number;
  onSave: (areas: ScheduleArea[]) => void;
  onAreaEdited: (area: ScheduleArea) => void;
  storeId: string;
  storeAreas: StoreAreaDto[];
}

export const ScheduleEditAreas: React.FC<ScheduleEditAreasProps> = (props) => {
  const [editingArea, setEditingArea] = useState<ScheduleArea | null>(null);
  const [areas, setAreas] = useState<ScheduleArea[]>(props.areas);
  const dayObj = find(daysOfWeek, d => d.dayOfWeek === props.dayOfWeek)!;
  const [tableBodyRef] = useAutoAnimate();

  useEffect(() => {
    setAreas(props.areas);
  }, [props.areas]);

  const handleEditClick = async (area: ScheduleArea) => {
    setEditingArea(area);
  };

  const handleMoveUp = (index: number) => {
    if (index > 0) {
      const newAreas = [...areas];
      [newAreas[index - 1], newAreas[index]] = [newAreas[index], newAreas[index - 1]];
      setAreas(newAreas);
    }
  };

  const handleMoveDown = (index: number) => {
    if (index < areas.length - 1) {
      const newAreas = [...areas];
      [newAreas[index], newAreas[index + 1]] = [newAreas[index + 1], newAreas[index]];

      setAreas(newAreas);
    }
  };

  const handleDelete = (index: number) => {
    const shouldDelete = confirm("Are you sure you want to delete this work area? All shifts in the area will also be deleted.");
    if (!shouldDelete) {
      return
    }

    const newAreas = [...areas];
    newAreas.splice(index, 1);
    setAreas(newAreas);
  };

  const dialog = useDisclosure();

  const onOpenChange = (open: boolean) => {
    dialog.setOpen(open);
    if (!open) {
      setAreas(props.areas);
      setEditingArea(null);
    }
  }

  const onSave = () => {
    props.onSave(areas);
    onOpenChange(false);
  }

  const onEditAreaSubmit = (area: ScheduleArea) => {
    props.onAreaEdited(area);
    onOpenChange(false);
  }

  const onCancel = () => {
    onOpenChange(false);
  };

  return (
    <Dialog open={dialog.isOpen} onOpenChange={onOpenChange}>
      <DialogTrigger asChild>
        <Button variant="outline">
          Edit areas...
        </Button>
      </DialogTrigger>
      <DialogContent size={"2xl"}>
        <div aria-live="polite">
          {editingArea ?
            <div className="space-y-4">
              <DialogHeader>
                {editingArea.title ?
                  <DialogTitle>
                    Edit Area: {editingArea.title}
                  </DialogTitle> : <DialogTitle>
                    Add Area
                  </DialogTitle>}
                <DialogDescription>
                  Edit area details below.
                </DialogDescription>
              </DialogHeader>
              <EditAreaDialogForm area={editingArea} onCancel={onCancel}
                                  storeAreas={props.storeAreas}
                                  onSubmit={onEditAreaSubmit}/>
            </div>
            :
            <div>
              <DialogHeader>
                <DialogTitle>
                  Edit Areas
                </DialogTitle>
                <DialogDescription>
                  Edit and reorder areas for {dayObj.name}.
                </DialogDescription>
              </DialogHeader>

              <div className={'overflow-auto'} style={{maxHeight: `calc(100vh - 220px)`}}>
                <Table className={"mt-4 mb-2"}>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Title</TableHead>
                      <TableHead>Counts Towards Labor</TableHead>
                      <TableHead>Store Area</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody ref={tableBodyRef}>
                    {map(areas, (area, index) => (
                      <TableRow key={area.id}>
                        <TableCell className="font-medium">{area.title}</TableCell>
                        <TableCell>
                          <Checkbox checked={area.countsTowardsLabor}/>
                        </TableCell>
                        <TableCell>
                          {area.storeAreaId ? find(props.storeAreas, sa => sa.id === area.storeAreaId)?.title : "No store area set"}
                        </TableCell>
                        <TableCell className="flex items-center gap-2">
                          <Button variant="outline" size="sm" onClick={() => handleEditClick(area)}>
                            Edit
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            aria-label="move up"
                            onClick={() => handleMoveUp(index)}
                            disabled={index === 0}
                          >
                            <ArrowUpIcon size={16}/>
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            aria-label="move down"
                            onClick={() => handleMoveDown(index)}
                            disabled={index === areas.length - 1}
                          >
                            <ArrowDownIcon size={16}/>
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            aria-label="delete"
                            onClick={() => handleDelete(index)}
                          >
                            <TrashIcon size={16} className="text-destructive"/>
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>

              <DialogFooter>
                <Button variant="secondary" onClick={onCancel} type={"button"}>
                  Cancel
                </Button>
                <Button onClick={onSave}>
                  Save
                </Button>
              </DialogFooter>
            </div>}
        </div>
      </DialogContent>
    </Dialog>
  );
}
