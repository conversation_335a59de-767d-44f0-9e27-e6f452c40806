import React from 'react';
import {isTimeAllDay} from "../../../api/src/date.util.ts";
import {cn} from "@/src/util.ts";
import {filter, map} from "lodash";
import {storeTimeToIncrement} from "../../../api/src/scheduleBuilder.util.ts";
import {SchedulePersonClientDto} from "../../../api/src/schedulePersonDto.ts";
import {DailyTimeRange} from '@gioa/api/src/timeSchemas.ts';
import {RangeOverlayLabel} from "@/src/components/RangeOverlayLabel.tsx";

export interface ShiftRowAvailabilityOverlayProps {
  person: SchedulePersonClientDto;
  dayOfWeek: number;
  storeHours: DailyTimeRange;
  showAvail: boolean;
  incrementWidth: number;
}

export function edgesGradient({
                                show,
                                rgb: [r, g, b] = [0, 20, 0],
                                edgeOpacity = 0.2,
                                middleOpacity = 0.1
                              }: {
  show: boolean
  rgb?: [r: number, g: number, b: number]
  edgeOpacity?: number;
  middleOpacity?: number;
}) {
  return `linear-gradient(to right,
rgba(${r}, ${g}, ${b}, ${show ? edgeOpacity : 0}) 0%,
rgba(${r}, ${g}, ${b}, ${show ? middleOpacity : 0}) 15%,
rgba(${r}, ${g}, ${b}, ${show ? middleOpacity : 0}) 85%,
rgba(${r}, ${g}, ${b}, ${show ? edgeOpacity : 0}) 100%
)`;
}

export const ShiftRowAvailabilityOverlay: React.FC<ShiftRowAvailabilityOverlayProps> = ({
                                                                                          dayOfWeek,
                                                                                          storeHours,
                                                                                          showAvail,
                                                                                          person,
                                                                                          incrementWidth
                                                                                        }) => {
  const dayAvailability = filter(person?.weekAvailability, a => a.dayOfWeek === dayOfWeek);

  const availabilityRanges = map(dayAvailability, avail => {
    const start = storeTimeToIncrement(storeHours, avail.start);
    const end = storeTimeToIncrement(storeHours, avail.end);
    const width = (end - start) * incrementWidth;

    return {
      range: avail,
      start: start * incrementWidth + 16,
      end: end * incrementWidth,
      width: width,
    }
  })

  const availabilityOverlay = (avail: typeof availabilityRanges[number]) => {
    const isAllDay = isTimeAllDay(avail.range)

    return <div key={avail.start}
                className={cn("absolute top-0 bottom-0 rounded-md pointer-events-none transition-opacity", showAvail ? "bg-opacity-10" : "bg-opacity-0")}
                style={{
                  left: avail.start, width: avail.width, background: edgesGradient({show: showAvail})
                }}>
      <RangeOverlayLabel showAvail={showAvail}
                         isAllDay={isAllDay}
                         range={avail.range}/>
    </div>;
  }

  return map(availabilityRanges, availabilityOverlay);
}
