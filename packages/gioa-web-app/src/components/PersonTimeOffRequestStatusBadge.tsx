import React from 'react';
import {Badge, BadgeProps, badgeVariants} from "@/src/components/ui/badge.tsx";
import {VariantProps} from 'class-variance-authority';
import {TimeOffStatusFilter} from "../../../api/src/personTimeOff.schemas.ts";
import {capitalize} from "lodash";

export interface PersonTimeOffRequestStatusBadgeProps extends BadgeProps {
  status: string;
}

const statusToColorScheme: {[status: string]: VariantProps<typeof badgeVariants>["colorScheme"]} = {
  pending: "yellow",
  approved: "success",
  declined: "destructive",
  cancelled: "default",
  expired: "secondary",
};

export const PersonTimeOffRequestStatusBadge: React.FC<PersonTimeOffRequestStatusBadgeProps> = ({status, ...props}) => {
  const colorScheme = statusToColorScheme[status] ?? "default";

  return (
    <Badge colorScheme={colorScheme} {...props}>
      {capitalize(status)}
    </Badge>
  );
}
