import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON><PERSON>er, DialogTitle } from "@/src/components/ui/dialog";
import { Text } from "@/src/components/Text";
import { Button } from "@/src/components/ui/button";
import { api } from "@/src/api";
import { Label } from "@/src/components/ui/label.tsx";
import { FormInput } from "@/src/components/form/FormInput.tsx";
import { FieldInfo } from "@/src/components/form/FieldInfo.tsx";
import { FormControl } from "@/src/components/form/FormControl.tsx";
import { useForm } from "@tanstack/react-form";
import { z } from "zod";
import { zodValidator } from "@tanstack/zod-form-adapter";
import {ErrorAlert} from "@/src/components/ErrorAlert.tsx";
import {genStoreResourceId} from "../../../api/src/schemas.ts";

export function AddLinkModal({
  storeId,
  isOpen,
  onOpenChange,
}: {
  storeId: string;
  isOpen: boolean;
  onOpenChange: (isOpen: boolean) => void;
}) {
  const apiUtil = api.useUtils();
  const addLink = api.user.upsertStoreResource.useMutation();

  const form = useForm({
    defaultValues: {
      title: "",
      link: "",
    },
    onSubmit: ({ value }) => {
      addLink.mutate(
        {
          id: genStoreResourceId(),
          storeId: storeId,
          resourceType: "link",
          resourceTitle: value.title,
          linkTitle: value.title,
          linkUrl: value.link,
        },
        {
          onSuccess: async () => {
            await apiUtil.user.getStoreResources.invalidate();
            form.reset();
            onOpenChange(false);
          },
        },
      );
    },
    validatorAdapter: zodValidator(),
  });

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>
            <Text size="lg" semibold>
              Add Link
            </Text>
          </DialogTitle>
        </DialogHeader>

        {addLink.isError ? <ErrorAlert error={addLink.error} /> : null}

        <div className="space-y-4 ">
          <form
            onSubmit={(e) => {
              e.preventDefault();
              e.stopPropagation();
              form.handleSubmit();
            }}
            className="space-y-4"
          >
            <form.Field
              name="title"
              validators={{
                onSubmit: z.string().min(1, "Required"),
              }}
            >
              {(field) => (
                <FormControl>
                  <Label htmlFor={field.name}>Title</Label>
                  <FormInput field={field} placeholder="Add title" />
                  <FieldInfo field={field} />
                </FormControl>
              )}
            </form.Field>

            <form.Field
              name="link"
              validators={{
                onSubmit: z.string().url(),
              }}
            >
              {(field) => (
                <FormControl>
                  <Label htmlFor={field.name}>Link</Label>
                  <FormInput field={field} placeholder="Add link" />
                  <FieldInfo field={field} />
                </FormControl>
              )}
            </form.Field>

            <div className="flex justify-end space-x-2 pt-4">
              {
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => {
                    form.reset();
                    onOpenChange(false);
                  }}
                  className="mr-2"
                >
                  Cancel
                </Button>
              }
              <Button type="submit">Save</Button>
            </div>
          </form>
        </div>
        <DialogFooter></DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
