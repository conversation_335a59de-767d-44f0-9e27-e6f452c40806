import React, {useEffect} from 'react';
import {<PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle} from "@/src/components/ui/dialog.tsx";
import {zodValidator} from "@tanstack/zod-form-adapter";
import {useForm} from "@tanstack/react-form";
import {FormControl} from "@/src/components/form/FormControl.tsx";
import {FieldInfo} from "@/src/components/form/FieldInfo.tsx";
import {DayOfWeek} from "../../../api/src/timeSchemas.ts";
import {z} from "zod";
import {filter, find, isEmpty, map} from "lodash";
import {daysOfWeek} from "@/src/components/AvailabilityWeek.tsx";
import {api} from "@/src/api.ts";
import {FormTextarea} from "@/src/components/form/FormTextarea.tsx";
import {genEntityNoteId} from '@gioa/api/src/schemas.ts';
import {Spinner} from "@/src/components/Spinner.tsx";
import {ArrowUpIcon} from "lucide-react";
import {ScheduleNote} from "./ScheduleNote";

export interface EditScheduleNotesDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onOpen: () => void;
  dayOfWeek: DayOfWeek;
  scheduleId: string;
  week: number;
}

export const EditScheduleNotesDialog: React.FC<EditScheduleNotesDialogProps> = (props) => {
  const createNote = api.user.createScheduleNote.useMutation();
  const getNotes = api.user.getScheduleNotes.useQuery({scheduleId: props.scheduleId}, {
    select: notes => {
      return filter(notes, n => n.dayOfWeek === props.dayOfWeek);
    }
  });

  const form = useForm({
    defaultValues: {
      note: ""
    },
    onSubmit: async ({value}) => {
      if (value.note) {
        createNote.mutate({
          noteId: genEntityNoteId(),
          scheduleId: props.scheduleId,
          dayOfWeek: props.dayOfWeek,
          note: value.note
        }, {
          onSuccess: () => {
            form.reset();
            getNotes.refetch();
          }
        })
      }
    },
    validatorAdapter: zodValidator(),
  });

  useEffect(() => {
    if (!props.isOpen) {
      // give a bit of time for the modal to animate out
      setTimeout(() =>
        form.reset(), 100);
    }
  }, [props.isOpen]);

  const onCancel = () => {
    props.onClose();
  }

  const dayOfWeekObj = find(daysOfWeek, d => d.dayOfWeek === props.dayOfWeek);

  const editNote = api.user.editScheduleNote.useMutation({});
  const onEditNote = (noteId: string, noteText: string) => {
    editNote.mutate(
            {
              noteId,
              scheduleId: props.scheduleId,
              note: noteText,
            }, {onSuccess: () => getNotes.refetch()}
    );
  };

  const deleteNote = api.user.deleteScheduleNote.useMutation({});
  const onDeleteNote = (noteId: string) => {
    deleteNote.mutate(
            {
              noteId,
              scheduleId: props.scheduleId
            }, {onSuccess: () => getNotes.refetch()}
    );
  };

  return (
    <Dialog open={props.isOpen} onOpenChange={open => {
      if (open) {
        props.onOpen();
      } else {
        onCancel();
      }
    }}>
      <DialogContent padding={"none"} className={"gap-0"}>
        <DialogHeader className={"pl-6 pr-6 pt-6 pb-3 border-b"}>
          <DialogTitle>
            Notes for {dayOfWeekObj?.name} of week {props.week}
          </DialogTitle>
        </DialogHeader>

        {getNotes.isLoading ? <Spinner size={"lg"} className={"my-6"}/> : null}

        <div>
          <div className="space-y-4 overflow-auto py-4 px-4"
               style={{maxHeight: "calc(100vh - 200px)", minHeight: "120px"}}>

            {getNotes.isSuccess && isEmpty(getNotes.data) ?
              <div className={"text-muted-foreground text-center"}>
                No notes are recorded for this day yet.
              </div> : null}

            {map(getNotes.data, note => (
                    <ScheduleNote
                            key={note.id}
                            noteId={note.id}
                            scheduleId={props.scheduleId}
                            onEdit={onEditNote}
                            onDelete={onDeleteNote}
                    />
            ))}

          </div>

          <form onSubmit={(e) => {
            e.preventDefault()
            e.stopPropagation()
            form.handleSubmit()
          }}>
            <div className={"border-t pt-4 px-4 pb-4"}>
              <form.Field name={"note"}
                          validators={{
                            onSubmit: z.string().optional()
                          }}
                          children={(field) => {
                            return <FormControl className={"m-0"}>
                              <div className={"flex items-start gap-2"}>
                                <FormTextarea field={field} aria-label={"Note"}
                                              className={"min-h-[auto]"}
                                              placeholder="Enter notes..."/>
                                <button type={"submit"}
                                        className={"text-white rounded-full min-w-[40px] w-[40px] min-h-[40px] h-[40px] hover:bg-primary-500 bg-primary-600 flex items-center justify-center"}
                                        title={"Save note"}>
                                  <ArrowUpIcon size={24}/>
                                </button>
                              </div>
                              <FieldInfo field={field}/>
                            </FormControl>;
                          }}/>
            </div>
          </form>
        </div>
      </DialogContent>
    </Dialog>
  );
}
