import {assign, EventFromLogic, setup, SnapshotFrom} from 'xstate';
import {getHumanReadableErrorMessageString} from "@/src/components/ErrorAlert.tsx";

export const autoSaveMachine = setup({
  types: {
    events: {} as { type: "saved" } | { type: "changed" } | { type: "savingStarted" } | {
      type: "saveError",
      error: any
    },
  },
  actions: {
    handleError: (_, {error}: { error: any }) => {
      console.error('Pipeline error:', error);
      if (error?.data?.httpStatus === 409) {
        alert(getHumanReadableErrorMessageString(error));
      } else {
        alert("An error occurred while saving your most recent change to the schedule. Check your Internet connection. Press OK to refresh the page and try again.");
      }

      // set timeout is necessary here to give the useBlocker hook time to update with the save error state so that it doesn't block the user from leaving the page
      setTimeout(() => {
        window.location.reload();
      }, 500);
    }
  }
}).createMachine({
  id: 'autoSave',
  initial: 'saved',
  context: {
    count: 0
  },
  states: {
    saved: {
      on: {
        changed: {target: 'unsaved',}
      }
    },
    unsaved: {
      on: {
        savingStarted: {target: 'saving',}
      }
    },
    saving: {
      on: {
        saved: {target: 'saved',},
        saveError: {target: 'saveError',}
      }
    },
    saveError: {
      entry: {
        type: "handleError",
        params: ({event}) => {
          return ({error: "error" in event ? event.error : null});
        }
      },
      type: "final"
    },
  }
})

export type AutoSaveEvent = EventFromLogic<typeof autoSaveMachine>;
export type AutoSaveSendFn = (event: AutoSaveEvent) => void;

export const scheduleBuilderMachine = setup({
  types: {
    events: {} as
      | { type: "shiftOpened", shiftId: string, assignedPersonId?: string }
      | { type: "shiftClosed" }
      | { type: "shiftDeleted" }
      | { type: "dayChanged" }
      | { type: "shiftAdded", shiftId: string }
      | { type: "sidebarShiftOpened", shiftId: string, assignedPersonId?: string }
      | { type: "listEvents" }
      | { type: "listTeamMembers" }
      | { type: "listMetrics" }
      | { type: "panelOpened" }
      | { type: "panelClosed" }
      | { type: "panelBack" }
      | { type: "teamMemberAssigned", assignedPersonId: string }
      | { type: "viewTeamMemberDetails", personId: string }
      | { type: "viewEventDetails", eventId: string }
      | { type: "eventSaved" }
      | { type: "eventDeleted" }
  },
  actors: {
    autoSave: autoSaveMachine
  },
  actions: {
    clearSelectedShiftId: assign({
      selectedShiftId: () => undefined
    }),
    clearSelectedEventId: assign({
      selectedEventId: () => undefined
    }),
    clearSelectedPersonId: assign({
      selectedPersonId: () => undefined
    }),
  }
}).createMachine({
  id: 'scheduleBuilder',
  initial: 'noShiftSelected',
  context: {
    selectedShiftId: undefined as string | undefined,
    selectedEventId: undefined as string | undefined,
    selectedPersonId: undefined as string | undefined,
  },
  invoke: {src: 'autoSave', id: 'autoSave'},
  states: {
    noShiftSelected: {
      initial: "idle",
      states: {
        idle: {},
        floatingPanelOpen: {
          initial: "showingMenu",
          exit: [{type: "clearSelectedEventId"}, {type: "clearSelectedPersonId"}],
          states: {
            showingMenu: {},
            showingTeamMembers: {
              initial: "list",
              states: {
                details: {
                  on: {
                    panelBack: {
                      target: "list",
                    }
                  },
                  exit: [{type: "clearSelectedPersonId"}],
                },
                list: {
                  entry: [{type: "clearSelectedPersonId"}],
                  on: {
                    panelBack: {target: "#scheduleBuilder.noShiftSelected.floatingPanelOpen.showingMenu"},
                    viewTeamMemberDetails: {
                      target: "details",
                      actions: assign({
                        selectedPersonId: ({event}) => event.personId,
                      })
                    }
                  }
                }
              }
            },
            showingMetrics: {
              on: {
                panelBack: {
                  target: "#scheduleBuilder.noShiftSelected.floatingPanelOpen.showingMenu"
                }
              }
            },
            showingEvents: {
              initial: "details",
              states: {
                details: {
                  on: {
                    panelBack: {
                      target: "list",
                    }
                  },
                  exit: [{type: "clearSelectedEventId"}],
                },
                list: {
                  entry: [{type: "clearSelectedEventId"}],
                  on: {
                    panelBack: {target: "#scheduleBuilder.noShiftSelected.floatingPanelOpen.showingMenu"},
                    viewEventDetails: {
                      target: "details",
                      actions: assign({
                        selectedEventId: ({event}) => event.eventId,
                      })
                    }
                  }
                }
              },
            }
          },
        }
      },
      on: {
        shiftOpened: {
          target: 'shiftSelected.shiftDetailsOpen',
          actions: assign({
            selectedShiftId: ({event}) => event.shiftId,
            selectedPersonId: ({event}) => event.assignedPersonId,
          }),
        },
        shiftAdded: {
          target: 'shiftSelected.shiftDetailsOpen',
          actions: assign({
            selectedShiftId: ({event}) => event.shiftId,
          }),
        },
        sidebarShiftOpened: [{
          guard: ({event}) => Boolean(event.assignedPersonId),
          target: "shiftSelected.quickFillingShift.details",
          actions: assign({
            selectedShiftId: ({event}) => event.shiftId,
            selectedPersonId: ({event}) => event.assignedPersonId,
          }),
        }, {
          target: "shiftSelected.quickFillingShift.list",
          actions: assign({
            selectedShiftId: ({event}) => event.shiftId,
            selectedPersonId: ({event}) => event.assignedPersonId,
          }),
        }],
        viewEventDetails: {
          target: ".floatingPanelOpen.showingEvents.details",
          actions: assign({
            selectedEventId: ({event}) => event.eventId,
          }),
        },
        panelOpened: {
          target: ".floatingPanelOpen",
        },
        panelClosed: {
          target: "noShiftSelected",
        },
        listEvents: {
          target: ".floatingPanelOpen.showingEvents.list",
        },
        listTeamMembers: {
          target: ".floatingPanelOpen.showingTeamMembers",
        },
        listMetrics: {
          target: ".floatingPanelOpen.showingMetrics",
        },
      },
    },
    shiftSelected: {
      exit: [{type: "clearSelectedShiftId"}],
      initial: "shiftDetailsOpen",
      states: {
        shiftDetailsOpen: {},
        quickFillingShift: {
          initial: "list",
          states: {
            details: {
              on: {
                panelBack: {
                  target: "list",
                }
              },
              exit: [{type: "clearSelectedPersonId"}],
            },
            list: {
              entry: [{type: "clearSelectedPersonId"}],
              on: {
                panelBack: {target: "#scheduleBuilder.noShiftSelected.floatingPanelOpen.showingMenu"},
                viewTeamMemberDetails: {
                  target: "details",
                  actions: assign({
                    selectedPersonId: ({event}) => event.personId,
                  })
                }
              }
            }
          }
        },
      },
      on: {
        shiftOpened: {
          target: '.shiftDetailsOpen',
          actions: assign({
            selectedShiftId: ({event}) => event.shiftId,
            selectedPersonId: ({event}) => event.assignedPersonId,
          }),
        },
        sidebarShiftOpened: [{
          guard: ({event}) => Boolean(event.assignedPersonId),
          target: ".quickFillingShift.details",
          actions: assign({
            selectedShiftId: ({event}) => event.shiftId,
            selectedPersonId: ({event}) => event.assignedPersonId,
          }),
        }, {
          target: ".quickFillingShift.list",
          actions: assign({
            selectedShiftId: ({event}) => event.shiftId,
            selectedPersonId: ({event}) => event.assignedPersonId,
          }),
        }],
        panelClosed: {
          target: "noShiftSelected",
        },
        shiftAdded: {
          target: 'noShiftSelected',
        },
        dayChanged: {
          target: 'noShiftSelected',
        },
        shiftDeleted: {
          target: 'noShiftSelected',
        },
        shiftClosed: {
          target: 'noShiftSelected',
        }
      }
    }
  }
});

export type ScheduleBuilderEvent = EventFromLogic<typeof scheduleBuilderMachine>;
export type SendFn = (event: ScheduleBuilderEvent) => void;
export type ScheduleBuilderMachineSnapshot = SnapshotFrom<typeof scheduleBuilderMachine>;
