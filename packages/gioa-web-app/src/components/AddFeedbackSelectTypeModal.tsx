import React from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/src/components/ui/dialog";
import { Text } from "@/src/components/Text";
import { Button } from "@/src/components/ui/button";
import { api } from "@/src/api";
import { EditNotificationIcon, SportsIcon, StylusNoteIcon, ThumbsUpIcon } from "@/src/feedbackIcons";

interface AddFeedBackSelectTypeModalProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  personId: string;
  storeId: string;
  onCreateActionableItem: () => void;
  onCreateNote: (type: string) => void;
}

export function AddFeedBackSelectTypeModal({
  isOpen,
  onOpenChange,
  personId,
  storeId,
  onCreateActionableItem,
  onCreateNote,
}: AddFeedBackSelectTypeModalProps) {
  const [person] = api.user.getPersonDetail.useSuspenseQuery({
    personId: personId,
    storeId: storeId,
  });

  const canCreateActionableItems = Boolean(person.requesterPermissionEvaluation?.canCreateActionableItems);
  const canCreateGeneralNotes = Boolean(person.requesterPermissionEvaluation?.canCreateGeneralNotes);

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent size={"xl"} className="overflow-y-auto min-h-[400px]" style={{ maxHeight: "calc(100vh - 100px)" }}>
        <DialogHeader>
          <DialogTitle>
            <Text size="lg" semibold>
              Add Feedback for {person.firstName} {person.lastName}
            </Text>
          </DialogTitle>
        </DialogHeader>

        <div>
          {canCreateActionableItems || canCreateGeneralNotes ? (
            <div className="flex flex-col gap-3 ">
              <hr />
              <Text className="mb-2">What type of feedback would you like to add for {person.firstName}?</Text>

              {canCreateActionableItems && (
                <Button
                  variant="outline"
                  className="flex justify-between items-center h-[110px] border border-gray-300 rounded-lg p-4"
                  onClick={onCreateActionableItem}
                >
                  <div className="flex-1 text-left flex flex-col">
                    <Text size="lg" semibold>
                      Actionable Item
                    </Text>
                    <Text size="sm" muted>
                      Create an initial report for
                    </Text>
                    <Text size="sm" muted>
                      a violation of store policies.
                    </Text>
                  </div>
                  <div className="flex items-center justify-center w-10 h-10 bg-[#FFF4DE] rounded-md">
                    <EditNotificationIcon />
                  </div>
                </Button>
              )}

              {canCreateGeneralNotes && (
                <>
                  <Button
                    variant="outline"
                    className="flex justify-between items-center h-[110px] border border-gray-300 rounded-lg p-4"
                    onClick={() => onCreateNote("coaching")}
                  >
                    <div className="flex-1 text-left flex flex-col">
                      <Text size="lg" semibold>
                        Coaching Moment
                      </Text>
                      <Text size="sm" muted>
                        Provide guidance or feedback to help
                      </Text>
                      <Text size="sm" muted>
                        a team member improve performance.
                      </Text>
                    </div>
                    <div className="flex items-center justify-center w-10 h-10 bg-[#DCFCE7] rounded-md">
                      <SportsIcon />
                    </div>
                  </Button>

                  <Button
                    variant="outline"
                    className="flex justify-between items-center h-[110px] border border-gray-300 rounded-lg p-4"
                    onClick={() => onCreateNote("positive-feedback")}
                  >
                    <div className="flex-1 text-left flex flex-col">
                      <Text size="lg" semibold>
                        Positive Feedback
                      </Text>
                      <Text size="sm" muted>
                        Give your team member
                      </Text>
                      <Text size="sm" muted>
                        an encouraging acknowledgment.
                      </Text>
                    </div>
                    <div className="flex items-center justify-center w-10 h-10 bg-[#F3E8FF] rounded-md">
                      <ThumbsUpIcon />
                    </div>
                  </Button>

                  <Button
                    variant="outline"
                    className="flex justify-between items-center h-[110px] border border-gray-300 rounded-lg p-4"
                    onClick={() => onCreateNote("general")}
                  >
                    <div className="flex-1 text-left flex flex-col">
                      <Text size="lg" semibold>
                        General Note
                      </Text>
                      <Text size="sm" muted>
                        Add a note on {person.firstName}'s record.
                      </Text>
                    </div>
                    <div className="flex items-center justify-center w-10 h-10 bg-[#E0EFFF] rounded-md">
                      <StylusNoteIcon />
                    </div>
                  </Button>
                </>
              )}
            </div>
          ) : (
            <Text className="text-center mb-4">
              Sorry, you don't have permission to add feedback on Team Members. Please contact your manager to request
              permissions to create Actionable Items and/or General Notes.
            </Text>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}
