import React, {SetStateAction, useCallback, useEffect, useMemo, useRef, useState} from 'react';
import {clamp, compact, filter, find, flatMap, isEmpty, isEqual, map} from "lodash";
import {Button} from "@/src/components/ui/button.tsx";
import {ArrowLeftIcon} from "lucide-react";
import {useDisclosure} from "@/src/hooks/useDisclosure.tsx";
import {genScheduleAreaId, genShiftId, StoreAreaDto} from "../../../api/src/schemas.ts";
import {
  Activity,
  BaseSchedule,
  DraftSchedule,
  HawaiiACAPersonHours,
  ScheduleDto,
  ShiftOfferDto
} from "../../../api/src/scheduleSchemas.ts";
import {getShift, getShiftArea} from "../../../api/src/scheduling.util.ts";
import {Helmet} from "react-helmet";
import {useNavigate, useRouter} from "@tanstack/react-router";
import {DayOfWeek, IsoWeekDate, TimeOfDay} from "../../../api/src/timeSchemas.ts";
import {
  ScheduleValidationResult,
  ScheduleValidationSettings,
  ValidationMessage
} from "../../../api/src/scheduleValidation.types.ts";
import {Toggle} from "@/src/components/ui/toggle.tsx";
import {usePrevious} from "react-use";
import {DocumentSaveStatus, SaveStatus} from "@/src/components/DocumentSaveStatus.tsx";
import {useWeeklyUndoStack} from "@/src/hooks/useUndoStack.tsx";
import {useHotkeys} from "react-hotkeys-hook";
import {cn} from '../util.ts';
import {useIgnoreScheduleValidationMessages} from "@/src/hooks/useIgnoreScheduleValidationMessages.tsx";
import {scheduleBuilderMachine} from "@/src/components/ScheduleBuilder.machine.tsx";
import {useScheduleBuilderMachineUIState} from "@/src/hooks/useScheduleBuilderMachineUIState.tsx";
import {SchedulePersonClientDto} from "../../../api/src/schedulePersonDto.ts";
import {rightPanelWidth} from "@/src/components/ScheduleBuilder.util.tsx";
import {
  FocusOutsideEvent,
  getShiftChangeStatus,
  PointerDownOutsideEvent,
  ScheduleRowInfo,
  shiftToScheduleRowInfo
} from "../../../api/src/scheduleBuilder.util.ts";
import {appendShiftToArea, getDayAreas, getShiftWeekday, moveAndAssignShift} from "../../../api/src/schedule.ts";

import {isMessageIgnorable} from "../../../api/src/scheduleValidation/scheduleValidationMessagesL2.ts";
import {getFriendlyIsoWeekString} from "@/src/schedule/schedule.ts";
import {SegmentedControl} from "@/src/components/SegmentedControl.tsx";
import {WeeklyTeamMembersView} from "@/src/components/WeeklyTeamMembersView.tsx";
import {WeeklyViewControlBar} from "@/src/components/WeeklyViewControlBar.tsx";
import {
  deserializeFilter,
  SerializedFilter,
  WeeklyViewFilter,
  WeeklyViewSort
} from "@/src/components/WeeklyViewControlBar.types.ts";
import {WeeklyFilterBar} from "@/src/components/WeeklyFilterBar.tsx";
import {WeeklyScheduleValidationSheet} from "@/src/components/WeeklyScheduleValidationSheet.tsx";
import {WeeklyTableShift} from "@/src/components/WeeklyTableTypes.ts";
import {Sheet, SheetContent} from "@/src/components/ui/sheet.tsx";
import {ShiftDetailsSheetContent} from "@/src/components/ShiftDetailsSheetContent.tsx";
import {constructShift} from "../../../api/src/shift.ts";
import {addToTime} from "../../../api/src/date.util.ts";
import {addShiftArea} from "../../../api/src/scheduling.ts";
import {constructScheduleArea} from "../../../api/src/scheduleArea.ts";
import {Route} from "@/src/routes/_signedin/$businessId/$storeId/schedules/$scheduleId";
import {Actor, StateFrom} from 'xstate';
import "./weekBuilder.css"
import {PublishWeekDialog} from "@/src/components/PublishWeekDialog.tsx";
import {toast} from 'sonner';
import {SchedulePresenceIndicator} from "@/src/components/SchedulePresenceIndicator.tsx";

//import { createBrowserInspector } from '@statelyai/inspect';

export interface ScheduleWeekBuilderProps {
  routeFullPath: string;
  businessId: string;
  storeId: string;
  scheduleId: string;
  shiftId?: string;
  cameFromWeeklyView: boolean;
  people: SchedulePersonClientDto[];
  currentPersonId: string;
  isPresenceEnabled: boolean;

  filters: SerializedFilter[] | undefined;
  sort: WeeklyViewSort | undefined;
  search: string | undefined;
  daySort: DayOfWeek | undefined;
  views: string[] | undefined;

  shiftOffers: ShiftOfferDto[];
  scheduleRev: ScheduleDto;
  schedule: DraftSchedule;
  onScheduleChange: (schedule: SetStateAction<DraftSchedule>, hints: {weekday: DayOfWeek | undefined}) => void;
  snapshot: StateFrom<typeof scheduleBuilderMachine>;
  send: Actor<typeof scheduleBuilderMachine>["send"];
  storeAreas: StoreAreaDto[];

  timezone: string;
  hasDraft: boolean;
  savingStatus: SaveStatus;

  onDeleteShift: (shift: ScheduleRowInfo) => void;
  onDuplicateShift: (shift: ScheduleRowInfo) => void;
  onShiftUpdated: (shiftId: string, values: {
    start: TimeOfDay;
    end: TimeOfDay;
    assignedPersonId: string | undefined;
    description: string | undefined;
    isShiftLead: boolean;
    storePositionId: string | undefined;
    shiftAreaId: string;
  }) => void;
  onShiftActivitiesUpdated: (shiftId: string, activities: Activity[]) => void;
  onDiscardDraft: () => void;
  validationResult: ScheduleValidationResult;
  onChangeView: (view: string, day?: DayOfWeek) => void;

  latestServerVersion: React.MutableRefObject<number>;
  settings: ScheduleValidationSettings;

  // Hawaii ACA validation props
  weeksHoursMap: HawaiiACAPersonHours;
  storeState?: string;
}

export const ScheduleWeekBuilder: React.FC<ScheduleWeekBuilderProps> = (props) => {
  const {
    onDeleteShift,
    onDuplicateShift, timezone, hasDraft, savingStatus,
    onShiftUpdated,
    onShiftActivitiesUpdated,
    onDiscardDraft,
    schedule, businessId,
    send, people, currentPersonId,
    snapshot, storeAreas,
    scheduleRev,
    onScheduleChange, latestServerVersion,
    onChangeView, shiftOffers,
    validationResult, settings,
    weeksHoursMap,
    storeState,
  } = props;

  const navigate = useNavigate({from: Route.fullPath});

  const {
    selectedShiftId,
  } = useScheduleBuilderMachineUIState(snapshot);

  const selectedShift = useMemo((): ScheduleRowInfo | undefined => {
    if (!selectedShiftId) {
      return undefined;
    }
    const shift = getShift(schedule, selectedShiftId);
    if (!shift) {
      return undefined;
    }
    const shiftArea = getShiftArea(schedule, shift.id);
    if (!shiftArea) {
      return undefined;
    }

    const published = scheduleRev.published;
    const positions = flatMap(storeAreas, a => a.positions);
    const person = find(people, p => p.id === shift.assignedPersonId);
    const shiftDay = getShiftWeekday(schedule, shift.id);
    const publishedShift = published ? getShift(published, shift.id) : undefined;
    const publishedShiftDay = published ? getShiftWeekday(published, shift.id) : undefined;

    const shiftChangeStatus = getShiftChangeStatus({
      draftShift: shift,
      draftShiftDay: shiftDay,
      publishedShift: publishedShift,
      publishedShiftDay: publishedShiftDay
    });
    const position = find(positions, p => p.id === shift.storePositionId);

    return shiftToScheduleRowInfo(shift, {
      draft: schedule,
      person,
      position,
      area: shiftArea,
      idx: 0,
      shiftChangeStatus,
      shiftOffers: shiftOffers,
      dayOfWeek: shiftDay,
    })

  }, [selectedShiftId, schedule, storeAreas]);

  const undoStack = useWeeklyUndoStack(schedule);
  const isUndoRedoChange = useRef(false);

  const onUndo = () => {
    const undoneSched = undoStack.onUndo();
    if (undoneSched) {
      isUndoRedoChange.current = true;
      onScheduleChange(undoneSched, {weekday: undefined})
    }
  }

  const onRedo = () => {
    const redoneSched = undoStack.onRedo();
    if (redoneSched) {
      isUndoRedoChange.current = true;
      onScheduleChange(redoneSched, {weekday: undefined})
    }
  }

  const scrollContainerRef = React.useRef<HTMLDivElement>(null);

  const onShiftMoved = useCallback((shiftId: string, personId: string, weekday: number) => {
    unignoreAllMessages(shiftId);

    onScheduleChange(schedule => {
      return moveAndAssignShift(schedule, {
        shiftId,
        day: weekday,
        newScheduleAreaId: genScheduleAreaId(),
        assignedPersonId: personId
      })
    }, {weekday: undefined})
  }, []);

  const [expanderWidth, setExpanderWidth] = useState(0);

  const scrollShiftIntoView = useCallback((shiftId: string) => {
    // scroll the shift into view if the panel slides over it
    // get the shift element by data-id
    const shiftElement = document.querySelector(`[data-id="${shiftId}"]`);
    if (!shiftElement) {
      return;
    }

    const container = scrollContainerRef.current;
    if (!container) {
      return;
    }
    const containerRect = container.getBoundingClientRect();
    const elementRect = shiftElement.getBoundingClientRect();
    const elemLeft = elementRect.left;
    const elemRight = elementRect.right;

    const startOffsetX = 0;
    const endOffsetX = rightPanelWidth;
    const startOffsetY = topbarHeight;

    const isElementInViewHorizontally =
      elemLeft >= containerRect.left + startOffsetX &&
      elemRight <= containerRect.right - endOffsetX;

    const isElementInViewVertically =
      elementRect.top >= containerRect.top + startOffsetY &&
      elementRect.bottom <= containerRect.bottom;

    if (!isElementInViewHorizontally || !isElementInViewVertically) {
      setTimeout(() => {
        const scrollLeft = !isElementInViewHorizontally
          ? elementRect.left - containerRect.left + container.scrollLeft - startOffsetX
          : container.scrollLeft;

        const scrollTop = !isElementInViewVertically
          ? elementRect.top - containerRect.top + container.scrollTop - startOffsetY
          : container.scrollTop;

        container.scrollTo({
          left: scrollLeft,
          top: scrollTop,
          behavior: 'smooth'
        });
      }, 0);
    }
  }, []);

  const onShiftAdded = useCallback((personId: string, day: number) => {
    const newShiftId = genShiftId();

    onScheduleChange(schedule => {
      const areas = getDayAreas(schedule, day);
      const firstArea = areas[0];
      const areaId = firstArea?.id ?? genScheduleAreaId();

      let newSched = schedule;

      // if the day has no areas, create a new area to contain the new shift
      if (!firstArea) {
        const newArea = constructScheduleArea({
          id: areaId,
          title: "New area",
          description: undefined,
          countsTowardsLabor: true,
          shifts: [],
          storeAreaId: undefined,
        });

        newSched = addShiftArea(newSched, {
          day,
          area: newArea,
          order: 0
        })
      }

      const newShift = constructShift({
        title: "New shift",
        id: newShiftId,
        range: {
          start: newSched.storeHours.start,
          end: addToTime(newSched.storeHours.start, 4, "hours")
        },
        shiftAreaId: areaId,
        storePositionId: undefined,
        assignedPersonId: personId,
        isShiftLead: false
      })

      return appendShiftToArea(newSched, areaId, newShift)
    }, {weekday: day})

    send({type: "shiftAdded", shiftId: newShiftId});
  }, []);

  const onShiftOpened = useCallback((shiftId: string) => {
    send({type: "shiftOpened", shiftId});
  }, []);

  const onShiftClick = useCallback((shift: WeeklyTableShift) => {
    onShiftOpened(shift.id);
  }, []);

  const onInteractOutside = useCallback((e: PointerDownOutsideEvent | FocusOutsideEvent) => {
    // if the target is a shift or team member button, then don't close the sheet
    const target = e.target as HTMLElement;
    if (target.closest(".gioa-shift, .gioa-team-member-select")) {
      e.preventDefault();
      return;
    }
  }, []);

  const onShiftDetailsOpenChanged = useCallback((isOpen: boolean) => {
    if (!isOpen) {
      send({type: "shiftClosed"});
      setSelectedActivityId(undefined);
    }
  }, []);

  const timelineHeight = 48
  const topbarHeight = timelineHeight;

  const {
    ignoredMessages,
    isMessageIgnored,
    ignoreMessage, unignoreMessage,
    unignoreAllMessages
  } = useIgnoreScheduleValidationMessages({
    scheduleId: props.scheduleId,
    storeId: props.storeId,
  });

  const notIgnoredWarnings = useMemo(() => {
    return filter(validationResult.messages, (m) => {
      return !isMessageIgnored(m) && isMessageIgnorable(m);
    });
  }, [validationResult.messages, ignoredMessages]);

  const validationPanel = useDisclosure();

  const onPublish = () => {
    publish.onOpen();
  }

  const onGoToValidationMessage = useCallback((week: IsoWeekDate, dayOfWeek: number, msg: ValidationMessage) => {
    if ("shift" in msg.info && msg.dayOfWeek) {
      publish.onClose();
      onShiftOpened(msg.info.shift.id);
    }
  }, []);

  const isShiftDetailsOpen = snapshot.matches({
    shiftSelected: "shiftDetailsOpen"
  });
  const areEditorHotkeysEnabled = !isShiftDetailsOpen && !validationPanel.isOpen;

  const isUndoEnabled = undoStack.stackPointer > 0 && undoStack.stackLength > 0;
  useHotkeys("mod+z", onUndo, {
    enableOnFormTags: false,
    preventDefault: true,
    enabled: areEditorHotkeysEnabled && isUndoEnabled
  });

  const isRedoEnabled = undoStack.stackPointer < undoStack.stackLength - 1;
  useHotkeys("mod+shift+z", onRedo, {
    enableOnFormTags: false,
    preventDefault: true,
    enabled: areEditorHotkeysEnabled && isRedoEnabled
  });

  const prevSched = usePrevious(schedule);

  // save schedule changes on the undo/redo stack
  useEffect(() => {
    if (prevSched && !isUndoRedoChange.current && !isEqual(schedule, prevSched)) {
      undoStack.onScheduleChange(schedule);
    }
    isUndoRedoChange.current = false;
  }, [schedule]);

  const publish = useDisclosure();
  // TODO
  // const onPublished = useCallback((numShifts: number) => {
  //   toast.success(`${numShifts} shifts on ${dayOfWeekObj?.name} of week ${schedule.week.week} published. Team members will be notified.`, {closeButton: true});
  //   publish.onClose();
  // }, []);  const discardScheduleDraft = api.user.discardScheduleDraft.useMutation();

  useEffect(() => {
    if (props.shiftId) {
      onShiftOpened(props.shiftId);
    }
  }, [props.shiftId]);

  const [selectedActivityId, setSelectedActivityId] = useState<string>();
  const router = useRouter();
  const onBack = useCallback(() => {
    // if the previous page is the weekly schedule page, go back so that we don't mess up the query params of the previous page
    if (props.cameFromWeeklyView) {
      router.history.back();
    }
    // else go up
    else {
      navigate({
        from: props.routeFullPath as any,
        search: schedule.week as any,
        to: ".."
      })
    }
  }, [props.cameFromWeeklyView, props.routeFullPath, schedule.week, navigate]);

  const serializedFilters = props.filters;
  const filters = useMemo(() => compact(map(serializedFilters, (filter, idx) => {
    return deserializeFilter({filter, idx, storeAreas: storeAreas});
  })), [serializedFilters, storeAreas]);
  const search = props.search ?? "";
  const serializedSort = props.sort;
  const sortColumn = serializedSort?.column ?? "lastName";
  const sortAscending = serializedSort?.isAsc ?? true;
  const daySort = props.daySort;
  const views = props.views ?? [];

  const setFilters = useCallback((filters: WeeklyViewFilter[]) => {
    const serializedFilters = map(filters, (filter): SerializedFilter => {
      return {
        t: filter.type,
        v: filter.value
      }
    })

    navigate({
      replace: true,
      search: (prev) => {
        return {
          ...prev,
          filters: serializedFilters
        }
      }
    })
  }, []);

  const onSetSort = useCallback((sort: WeeklyViewSort) => {
    navigate({
      replace: true,
      search: (prev) => {
        return {
          ...prev,
          sort: sort
        }
      }
    })
  }, []);

  const onSetSearch = useCallback((search: string) => {
    navigate({
      replace: true,
      search: (prev) => {
        return {
          ...prev,
          search: search,
        }
      }
    })
  }, []);

  const onReset = useCallback(() => {
    navigate({
      replace: true,
      search: (prev) => {
        return {
          ...prev,
          search: undefined,
          filters: undefined,
        }
      }
    })
  }, []);

  const onSetDaySort = useCallback((sort: number) => {
    if (daySort === sort) {
      navigate({
        replace: true,
        search: (prev) => {
          return {
            ...prev,
            daySort: undefined,
          }
        }
      })
    } else {
      navigate({
        replace: true,
        search: (prev) => {
          return {
            ...prev,
            daySort: sort,
          }
        }
      })
    }
  }, [daySort]);

  const onSetViews = useCallback((views: string[]) => {
    navigate({
      replace: true,
      search: (prev) => {
        return {
          ...prev,
          views: views,
        }
      }
    })
  }, []);

  useEffect(() => {
    if (isShiftDetailsOpen) {
      setTimeout(() => {
        setExpanderWidth(clamp((document.getElementById("right-panel")?.clientWidth ?? 0) - 20, 0, 500));
        setTimeout(() => scrollShiftIntoView(selectedShiftId));
      });
    } else {
      setExpanderWidth(0)
    }
  }, [isShiftDetailsOpen, selectedShiftId])

  const onPublished = () => {
    toast.success("Schedule published. Team members will be notified.", {closeButton: true});
    publish.onClose();
  }

  // TODO make header sticky

  return (
    <div className={`overflow-auto h-screen w-screen bg-[#F6F8FC] flex flex-col gioa-scroll items-stretch`}
         ref={scrollContainerRef}>
      <Helmet>
        <title>
          {getFriendlyIsoWeekString(schedule.week)} - Nation
        </title>
      </Helmet>
      {/*Topbar*/}
      <div className={"border-b-2 border-slate-300 sticky left-0"}>
        <style>
          {`[data-shift-id="${selectedShiftId}"] { outline: 4px solid rgb(252 211 77 / var(--tw-bg-opacity)); outline-offset: -2px; }`}
        </style>
        <div
          className={"bg-white flex flex-wrap gap-2 items-center px-5 py-3  border-b border-slate-200"}>
          <div className={"flex items-stretch gap-1"}>
            <Button variant={"outline"} onClick={onBack}>
              <ArrowLeftIcon size={16} className={"text-gray-500 mr-2"}/>
              To week {schedule.week.week}
            </Button>

            <SegmentedControl options={["Day", "Week"]}
                              value={"Week"} onChange={onChangeView}/>

            <div className={"w-[2px] h-8 bg-slate-300 mx-2 self-center"}/>
          </div>
          <WeeklyViewControlBar setSearch={onSetSearch} search={search}
                                onSetSort={onSetSort} sortColumn={sortColumn} sortAscending={sortAscending}
                                views={views} setViews={onSetViews}/>
          <div className={"flex items-center gap-2 justify-end flex-1"}>
            {hasDraft ?
              <div className={"pr-1"}>
                <DocumentSaveStatus status={savingStatus}/>
              </div> :
              null}
            {props.isPresenceEnabled ?
              <SchedulePresenceIndicator scheduleId={props.scheduleId}
                                        currentPersonId={currentPersonId}
                                        people={people}/> : null}
            <Toggle aria-label={"Show issues"}
                    variant={"outline"} title={"Show issues"}
                    onPressedChange={(pressed) => {
                      validationPanel.setOpen(pressed);
                    }}
                    pressed={validationPanel.isOpen}
                    className={cn("text-gray-700 whitespace-nowrap", {"border-amber-400": !isEmpty(notIgnoredWarnings)})}>
              Issues {notIgnoredWarnings.length > 0 ? `(${notIgnoredWarnings.length})` : ""}
            </Toggle>
            {hasDraft ?
              <Button onClick={onPublish}>
                Publish Week
              </Button> : null}
          </div>
        </div>

        <WeeklyFilterBar storeAreas={storeAreas} setFilters={setFilters}
                         className={"py-2 bg-white px-5"}
                         filters={filters} onReset={onReset}/>
      </div>

      <section className={"grow min-w-min"} style={{paddingRight: expanderWidth}}>
        <WeeklyTeamMembersView year={schedule.week.year} week={schedule.week.week}
                               storeId={schedule.storeId}
                               businessId={businessId}
                               schedule={schedule}
                               published={scheduleRev.published}
                               daySort={daySort} onShiftMove={onShiftMoved}
                               filters={filters} onShiftClick={onShiftClick}
                               search={search} onAddShift={onShiftAdded}
                               sortColumn={sortColumn}
                               sortAscending={sortAscending}
                               views={views}
                               onSortByDay={onSetDaySort}
                               routeToScheduleTo={props.routeFullPath}
                               timezone={timezone} routeToScheduleFrom={props.routeFullPath}
                               people={people} storeAreas={storeAreas}/>
      </section>

      <Sheet modal={false} open={isShiftDetailsOpen}
             onOpenChange={onShiftDetailsOpenChanged}>
        <SheetContent side="right" hideCloseButton={true}
                      onInteractOutside={onInteractOutside}
                      id="right-panel"
                      className="flex flex-col overflow-auto px-0">
          {selectedShift
            ? <ShiftDetailsSheetContent shift={selectedShift} storeHours={schedule.storeHours} storeId={props.storeId}
                                        onDeleteShift={onDeleteShift} schedule={schedule}
                                        latestServerVersion={latestServerVersion} settings={settings}
                                        onDuplicateShift={onDuplicateShift} currentView={"Week"}
                                        onChangeView={onChangeView} canChangeView={true} hideDuplicateShift={true}
                                        onSelectedActivityChange={setSelectedActivityId}
                                        timezone={timezone} routeFullPath={props.routeFullPath}
                                        people={people} storeAreas={storeAreas} validationResult={validationResult}
                                        onShiftActivitiesUpdated={onShiftActivitiesUpdated}
                                        onShiftUpdated={onShiftUpdated}
                                        weeksHoursMap={weeksHoursMap}
                                        storeState={storeState}/>
            : null}

        </SheetContent>
      </Sheet>

      <WeeklyScheduleValidationSheet isOpen={validationPanel.isOpen} onIgnoreMessage={ignoreMessage}
                                     storeHours={schedule.storeHours} onUnignoreMessage={unignoreMessage}
                                     scheduleRev={scheduleRev} people={people}
                                     validationResult={validationResult}
                                     onOpenChange={validationPanel.setOpen} storeId={props.storeId}
                                     onGoToMessage={onGoToValidationMessage}/>

      {schedule && validationResult ?
        <PublishWeekDialog isOpen={publish.isOpen} draftVersion={latestServerVersion.current}
                           onOpenChange={publish.setOpen} onUnignoreMessage={unignoreMessage}
                           onIgnoreMessage={ignoreMessage}
                           onGoToMessage={onGoToValidationMessage}
                           validationResult={validationResult}
                           schedule={schedule}
                           people={people}
                           onPublished={onPublished}/> : null}
    </div>
  );
}

