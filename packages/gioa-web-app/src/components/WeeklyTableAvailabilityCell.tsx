import React from 'react';
import {cn} from "@/src/util.ts";
import {isEmpty, map, sumBy} from "lodash";
import {getRangeDurationHours, isTimeAllDay, to12HourTime} from "../../../api/src/date.util.ts";
import {DurationText} from "@/src/components/DurationText.tsx";
import {TableCell} from "@/src/components/ui/table.tsx";
import {WeekDayTimeRange} from "../../../api/src/timeSchemas.ts";

export interface WeeklyTableAvailabilityCellProps {
  availability: WeekDayTimeRange[];
}

export const WeeklyTableAvailabilityCell = React.memo(({availability}: WeeklyTableAvailabilityCellProps) => {
  const availDuration = sumBy(availability, a => getRangeDurationHours(a));
  const isAllDay = availability[0] ? isTimeAllDay(availability[0]) : false;

  return (
    <TableCell className={"px-2  h-full even:bg-slate-100 odd:bg-slate-200 border-t border-slate-300"}>
      <div className={"h-full"}>
        <div
          className={cn("bg-white border border-gray-200 rounded-lg border-l-8 pl-3 pr-2 py-2 text-left w-full", {"border-l-green-500": !isEmpty(availability)})}>

          <div className={"text-muted-foreground text-sm mb-1"}>
            {isEmpty(availability) ? "No availability" : "Available"}
          </div>

          <div className={"flex flex-row h-full items-start gap-1 flex-wrap"}>
            {map(availability, range => {
              if (isAllDay) {
                return <div key={range.start}>
                  All day
                </div>
              }

              return <div key={range.start} className={"px-2 py-0.5 text-sm rounded-md border whitespace-nowrap"}>
                {to12HourTime(range.start)} - {to12HourTime(range.end)}
              </div>
            })}

            {!isAllDay && !isEmpty(availability) ? <div className={"px-2 py-0.5 text-sm rounded-md border"}>
              <DurationText durationHours={availDuration} size={"sm"}/>
            </div> : null}
          </div>
        </div>
      </div>
    </TableCell>
  );
})
