import React from 'react';
import {TeamMemberCombobox} from "@/src/components/TeamMemberCombobox.tsx";
import {FieldApi} from "@tanstack/react-form";
import {cn} from "@/src/util.ts";
import {isEmpty} from "lodash";
import {SchedulePersonDto} from "../../../../api/src/schedulePersonDto.ts";

export interface FormTeamMemberComboboxProps {
  people: SchedulePersonDto[];
  field: FieldApi<any, any, any, any>;
  className?: string;
  listClassName?: string;
}

export const FormTeamMemberCombobox: React.FC<FormTeamMemberComboboxProps> = ({people, field, className, listClassName}) => {
  const hasErrors = !isEmpty(field.state.meta.errors);
    return (
      <TeamMemberCombobox
              people={people}
              className={cn({"border-red-600": hasErrors}, className)}
              listClassName={listClassName}
              value={field.state.value}
              onValueChange={field.handleChange}/>
    );
}
