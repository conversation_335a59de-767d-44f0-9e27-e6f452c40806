import React from 'react';
import {<PERSON><PERSON><PERSON>} from "@tanstack/react-form";
import {Popover, PopoverContent, PopoverTrigger} from '../ui/popover';
import {map} from "lodash";
import {PopoverClose} from "@radix-ui/react-popover";

export interface FormColorPickerProps {
  field: FieldApi<any, any, any, any>;
  colors: string[];
}

export const FormColorPicker: React.FC<FormColorPickerProps> = (props) => {
  const currentColor = props.field.state.value;

  return (
    <Popover>
      <PopoverTrigger asChild>
        <button className={"rounded-md min-w-8 w-full h-10 border"} type={"button"}
                style={{backgroundColor: currentColor}}
                aria-label={`Pick color (current color: ${currentColor})`}>
        </button>
      </PopoverTrigger>
      <PopoverContent className={"p-0 grid grid-cols-3 gap-0 w-auto"}>
        {map(props.colors, (color, index) => <PopoverClose key={color} asChild>
            <button className={"h-10 w-10"}
                    style={{backgroundColor: color}}
                    onClick={() => props.field.handleChange(color)}
                    aria-label={`Pick color ${color}`}>
            </button>
          </PopoverClose>
        )}
      </PopoverContent>
    </Popover>
  );
}
