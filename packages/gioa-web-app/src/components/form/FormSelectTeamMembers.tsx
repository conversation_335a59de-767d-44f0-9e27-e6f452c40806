
import React, { use<PERSON>allback, useMemo, useState } from "react";
import {FieldApi} from "@tanstack/react-form";
import { SchedulePersonDto } from "../../../../api/src/schedulePersonDto.ts";
import { Text } from "@/src/components/Text.tsx";
import { PlusIcon, SearchIcon } from "lucide-react";
import {But<PERSON>} from "@/src/components/ui/button.tsx";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/src/components/ui/dialog";
import { ScrollArea } from "@/src/components/ui/scroll-area";
import { TeamMemberPressable } from "@/src/components/TeamMemberPressable";
import { ProficiencyRating } from "@/src/components/ProficiencyRating.tsx";
import { Input } from "@/src/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/src/components/ui/select";
import { filter, map, includes, sortBy, orderBy } from "lodash";
import { cn } from "@/src/util.ts";

export interface FormSelectTeamMembersProps {
  people: SchedulePersonDto[];
  field: FieldApi<any, any, any, any>;
  onChange?: (personIds: string[]) => void;
}

type SortBy = "firstName" | "lastName" | "proficiency" | "newest" | "oldest";

export const FormSelectTeamMembers: React.FC<FormSelectTeamMembersProps> = ({
  people,
  field,
  onChange,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [searchInput, setSearchInput] = useState("");
  const [sortBy, setSortBy] = useState<SortBy>("firstName");
  const [selectedIds, setSelectedIds] = useState<string[]>(field.state.value || []);

  const handlePersonClick = useCallback((personId: string) => {
    setSelectedIds(prev => {
      if (includes(prev, personId)) {
        return prev.filter(id => id !== personId);
      } else {
        return [...prev, personId];
      }
    });
  }, []);

  // Filter and sort people based on search and sort criteria
  const filteredAndSortedPeople = useMemo(() => {
    let filtered = people;

    // Filter by search input
    if (searchInput.trim()) {
      const searchLower = searchInput.toLowerCase();
      filtered = filtered.filter(
        (person) =>
          person.firstName?.toLowerCase().includes(searchLower) ||
          person.lastName?.toLowerCase().includes(searchLower) ||
          person.jobTitle?.toLowerCase().includes(searchLower),
      );
    }

    // Sort by selected criteria
    switch (sortBy) {
      case "firstName":
        return orderBy(filtered, [(person) => person.firstName?.toLowerCase() || ""], ["asc"]);
      case "lastName":
        return orderBy(filtered, [(person) => person.lastName?.toLowerCase() || ""], ["asc"]);
      case "proficiency":
        return orderBy(filtered, ["proficiencyRanking"], ["desc"]);
      case "newest":
        return orderBy(filtered, ["createdAt"], ["desc"]);
      case "oldest":
        return orderBy(filtered, ["createdAt"], ["asc"]);
      default:
        return filtered;
    }
  }, [people, searchInput, sortBy]);

  const handleSubmit = useCallback(() => {
    field.handleChange(selectedIds);
    onChange?.(selectedIds);
    setIsOpen(false);
  }, [selectedIds, field, onChange]);

  const handleOpenModal = useCallback(() => {
    setSelectedIds(field.state.value || []);
    setIsOpen(true);
  }, [field.state.value]);

  const selectedCount = field.state.value?.length || 0;
  const buttonText = selectedCount > 0 ? `(${selectedCount}) Team Members` : "Select Team Members";

  return (
    <>
      <div>
        <Button variant={"outline"} type={"button"} rightIcon={<PlusIcon size={16}/>} onClick={handleOpenModal}>
          {buttonText}
        </Button>
      </div>

      <Dialog open={isOpen} onOpenChange={setIsOpen}>
        <DialogContent size={"xl"} className="overflow-y-auto min-h-[400px]" style={{ maxHeight: "calc(100vh - 100px)" }}>
          <DialogHeader>
            <DialogTitle>Select Team Members</DialogTitle>
            <Text size="sm" muted>
              Selected team members will receive this Announcement
            </Text>
          </DialogHeader>

          {/* Search and Sort Controls */}
          <div className="flex flex-row gap-3 mb-4">
            <div className="flex-1">
              <Input
                placeholder="Search"
                value={searchInput}
                onChange={(e) => setSearchInput(e.target.value)}
                leftIcon={SearchIcon}
              />
            </div>
            <div className="w-48">
              <Select
                value={sortBy}
                onValueChange={(value: SortBy) => setSortBy(value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Sort By" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="firstName">First Name</SelectItem>
                  <SelectItem value="lastName">Last Name</SelectItem>
                  <SelectItem value="proficiency">Proficiency</SelectItem>
                  <SelectItem value="newest">Newest</SelectItem>
                  <SelectItem value="oldest">Oldest</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <ScrollArea className="max-h-[60vh]">
            <div className="space-y-0 pr-4">
              {map(filteredAndSortedPeople, (person) => {
                const isSelected = includes(selectedIds, person.id);
                return (
                  <div
                    onClick={() => handlePersonClick(person.id)}
                    key={person.id}
                    className={cn("cursor-pointer rounded-lg transition-colors")}
                  >
                    <TeamMemberPressable
                      person={person}
                      className={`mb-0.5 ${isSelected ? "bg-blue-50" : undefined}`}
                      Subtext={
                        <Text size={"sm"} muted>
                          {person.jobTitle}
                        </Text>
                      }
                      RightElem={
                        <div className="flex-1 flex flex-row justify-end items-center">
                          <ProficiencyRating rank={person.proficiencyRanking ?? 0} colorScheme={"light-bg"} size={16} />
                        </div>
                      }
                    />
                  </div>
                );
              })}

              {filteredAndSortedPeople.length === 0 && (
                <Text className="text-center py-4 text-muted-foreground">
                  No team members found.
                </Text>
              )}
            </div>
          </ScrollArea>


          <div className="flex justify-end gap-4 pt-4">
            <Button variant="outline" onClick={() => setIsOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleSubmit} disabled={selectedIds.length === 0}>
              Select ({selectedIds.length})
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
};
