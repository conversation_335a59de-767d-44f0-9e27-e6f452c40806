import React from 'react';
import {Textarea, TextareaProps} from "@/src/components/ui/textarea.tsx";
import {FieldApi} from "@tanstack/react-form";
import {isEmpty} from "lodash";

export interface FormTextareaProps extends TextareaProps {
  field: FieldApi<any, any, any, any>;
}

export const FormTextarea: React.FC<FormTextareaProps> = ({field, onChange, ...props}) => {
  return (
    <Textarea id={field.name} hasError={!isEmpty(field.state.meta.errors)}
              name={field.name}
              value={field.state.value}
              onBlur={field.handleBlur}
              onChange={(e) => {
                field.handleChange(e.target.value);
                onChange?.(e);
              }}
              {...props}/>
  );
}
