import React, { useState } from 'react';
import { FieldApi } from "@tanstack/react-form";
import { isEmpty } from "lodash";
import {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuItem,
} from "@/src/components/ui/dropdown-menu";
import { Button } from "@/src/components/ui/button";
import { ChevronDown } from "lucide-react";

export interface DropdownOption {
  value: string;
  label: string;
}

export interface FormDropDownProps {
  field: FieldApi<any, any, any, any>;
  options: DropdownOption[];
  placeholder?: string;
  className?: string;
  onChange?: (value: string) => void;
}

export const FormDropDown: React.FC<FormDropDownProps> = ({
                                                            field,
                                                            options,
                                                            placeholder = "Select an option",
                                                            className,
                                                            onChange,
                                                          }) => {
  const [isOpen, setIsOpen] = useState(false);

  const handleSelect = (value: string) => {
    field.handleChange(value);
    setIsOpen(false);
    onChange?.(value);
  };

  const selectedOption = options.find(option => option.value === field.state.value);

  return (
    <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
      <DropdownMenuTrigger asChild>
        <Button
          id={field.name}
          variant="outline"
          className={`w-full justify-between ${className} ${!isEmpty(field.state.meta.errors) ? 'border-red-600' : ''}`}
        >
          {selectedOption ? selectedOption.label : placeholder}
          <ChevronDown className="ml-2 h-4 w-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent className="w-full">
        {options.map((option) => (
          <DropdownMenuItem
            key={option.value}
            onSelect={() => handleSelect(option.value)}
          >
            {option.label}
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
};
