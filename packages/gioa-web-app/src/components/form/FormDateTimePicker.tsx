import * as React from "react";
import {forwardRef} from "react";
import {FieldApi} from "@tanstack/react-form";
import {DateTime} from "luxon";
import {Input} from "@/src/components/ui/input";
import {Calendar as CalendarIcon, Clock} from "lucide-react";
import {But<PERSON>} from "@/src/components/ui/button";
import {Popover, PopoverContent, PopoverTrigger} from "@/src/components/ui/popover";
import {Calendar} from "@/src/components/ui/calendar";
import {Label} from "@/src/components/ui/label";
import {cn} from "@/src/util.ts";
import {PopoverClose} from "@radix-ui/react-popover";
import {parse24HourTime} from "../../../../api/src/date.util.ts";
import {isEmpty} from "lodash";

interface FormDateTimePickerProps {
  field: FieldApi<any, any, any, any>;
  showTimeSelect?: boolean;
  dateFormat?: (date: Date) => string;
  timezone: string;
  minutesStep?: number;
  placeholder?: string;
  className?: string;
  disabled?: boolean;
}

export const FormDateTimePicker = forwardRef<HTMLInputElement, FormDateTimePickerProps>(
  ({
     field,
     showTimeSelect = true,
     dateFormat,
     timezone,
     minutesStep = 1,
     placeholder = "Select date and time",
     className,
     disabled = false
   }, ref) => {
    const toDt = (date?: Date): DateTime | null => {
      if (!date) return null;
      return DateTime.fromJSDate(date, {zone: timezone});
    };

    const dateTime = toDt(field.state.value);

    // Format the date for display
    const formatDate = (date?: Date): string => {
      if (!date) return "";
      if (dateFormat) return dateFormat(date);

      const dt = toDt(date);
      if (!dt) return "";

      return showTimeSelect
        ? dt.toLocaleString({...DateTime.DATETIME_SHORT, timeZone: timezone})
        : dt.toLocaleString({...DateTime.DATE_SHORT, timeZone: timezone});
    };

    // Handle date selection
    const handleDateSelect = (date: Date | undefined) => {
      if (!date) return null;

      // Create DateTime directly in the target timezone to avoid conversion issues
      const newDateTime = DateTime.fromObject({
        year: date.getFullYear(),
        month: date.getMonth() + 1,
        day: date.getDate(),
        hour: 0,
        minute: 0,
        second: 0,
        millisecond: 0
      }, { zone: timezone });

      // If we already have a time, preserve it
      if (dateTime) {
        const updatedDateTime = newDateTime.set({
          hour: dateTime.hour,
          minute: dateTime.minute,
          second: 0,
          millisecond: 0
        });
        field.handleChange(updatedDateTime.toJSDate());
      } else {
        // Set default time to current time
        const now = DateTime.now().setZone(timezone);
        const updatedDateTime = newDateTime.set({
          hour: now.hour,
          minute: now.minute,
          second: 0,
          millisecond: 0
        });
        field.handleChange(updatedDateTime.toJSDate());
      }
    };

    // Handle time change
    const handleTimeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      if (!dateTime) return;

      const timeString = e.target.value;
      const [hours, minutes] = parse24HourTime(timeString)

      if (isNaN(hours) || isNaN(minutes)) return;

      const updatedDateTime = dateTime.set({
        hour: hours,
        minute: minutes,
        second: 0,
        millisecond: 0
      });

      field.handleChange(updatedDateTime.toJSDate());
    };

    // Format time for the time input
    const formatTimeForInput = (dt?: DateTime): string => {
      if (!dt) return "";
      const hours = dt.hour.toString().padStart(2, '0');
      const minutes = dt.minute.toString().padStart(2, '0');
      return `${hours}:${minutes}`;
    };

    // Clear the selection
    const handleClear = () => {
      field.handleChange(null);
    };

    const hasErrors = !isEmpty(field.state.meta.errors);

    return (
      <div className={cn(className)}>
        <Popover>
          <PopoverTrigger asChild>
            <Button leftIcon={<CalendarIcon className="h-4 w-4"/>}
                    variant="outline"
                    className={cn("font-normal",
                      !dateTime && "text-muted-foreground",
                      {
                        "border-red-500": hasErrors
                      }
                    )}
                    disabled={disabled}>
              {dateTime ? formatDate(dateTime.toJSDate()) : placeholder}
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-auto p-0" align="start">
            <Calendar
              mode="single"
              selected={dateTime?.toJSDate()}
              onSelect={handleDateSelect}
              initialFocus
            />
            {showTimeSelect && dateTime && (
              <div className="p-3 border-t border-gray-200">
                <div className="flex items-center gap-2">
                  <Clock className="h-4 w-4 text-gray-500"/>
                  <Label>Time</Label>
                  <Input
                    type="time"
                    value={formatTimeForInput(dateTime)}
                    onChange={handleTimeChange}
                    className="w-32"
                    step={minutesStep * 60}
                  />
                </div>
              </div>
            )}
            <div className="p-3 border-t border-gray-200 flex justify-between">
              <Button variant="ghost"
                      size="sm"
                      onClick={handleClear}
                      type="button">
                Clear
              </Button>
              <PopoverClose asChild>
                <Button variant="outline"
                        size="sm"
                        type="button">
                  Done
                </Button>
              </PopoverClose>
            </div>
          </PopoverContent>
        </Popover>
      </div>
    );
  }
);

FormDateTimePicker.displayName = "FormDateTimePicker";
