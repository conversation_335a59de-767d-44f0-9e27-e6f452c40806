import React, {useCallback, useEffect, useMemo, useState} from "react";
import {filter, includes, isEmpty, map} from "lodash";
import {Badge} from "@/src/components/ui/badge";
import {Button} from "@/src/components/ui/button";
import {Input} from "@/src/components/ui/input";
import {Text} from "@/src/components/Text";
import {ChevronDownIcon, ChevronUpIcon, SearchIcon, XIcon} from "lucide-react";
import {cn} from "@/src/util.ts";

export interface SelectChecklistTagsProps {
  selectedTags: string[];
  onTagsChange: (tags: string[]) => void;
  allTags: string[];
  maxVisibleTags?: number;
}

export const SelectChecklistTags: React.FC<SelectChecklistTagsProps> = ({
                                                                          selectedTags,
                                                                          onTagsChange,
                                                                          allTags,
                                                                          maxVisibleTags = 12
                                                                        }) => {
  const [searchInput, setSearchInput] = useState("");
  const [isExpanded, setIsExpanded] = useState(false);
  const [localSelectedTags, setLocalSelectedTags] = useState<string[]>(selectedTags);

  // Debounce the tag changes to avoid excessive API calls
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      onTagsChange(localSelectedTags);
    }, 1000);

    return () => clearTimeout(timeoutId);
  }, [localSelectedTags, onTagsChange]);

  // Update local state when external selectedTags change
  useEffect(() => {
    setLocalSelectedTags(selectedTags);
  }, [selectedTags]);

  // Filter tags based on search input
  const filteredTags = useMemo(() => {
    if (!searchInput.trim()) {
      return allTags;
    }
    const searchLower = searchInput.toLowerCase();
    return filter(allTags, (tag) =>
            tag.toLowerCase().includes(searchLower)
    );
  }, [allTags, searchInput]);

  // Determine which tags to display based on expansion state
  const displayedTags = useMemo(() => {
    if (isExpanded || filteredTags.length <= maxVisibleTags) {
      return filteredTags;
    }
    return filteredTags.slice(0, maxVisibleTags);
  }, [filteredTags, isExpanded, maxVisibleTags]);

  const shouldShowExpandButton = filteredTags.length > maxVisibleTags;

  const handleTagToggle = useCallback((tag: string) => {
    const newTags = includes(localSelectedTags, tag)
            ? filter(localSelectedTags, (t: string) => t !== tag)
            : [...localSelectedTags, tag];

    setLocalSelectedTags(newTags);
  }, [localSelectedTags]);

  const handleExpandToggle = () => {
    setIsExpanded(!isExpanded);
  };

  const clearSearch = () => {
    setSearchInput("");
  };

  return (
          <div className={"flex flex-col gap-1 items-start"}>
            {/* Search input */}
            <div className="flex gap-3 ">
              <Input
                      placeholder="Filter tags..."
                      value={searchInput}
                      onChange={(e) => setSearchInput(e.target.value)}
                      leftIcon={SearchIcon}
                      rightIcon={searchInput ? XIcon : undefined}
                      onRightIconClick={searchInput ? clearSearch : undefined}
              />
            </div>

            {/* Tags display */}
            <div className="flex-1">
              <div className="flex-1 flex flex-wrap gap-1 p-2 border border-gray-200 rounded min-h-[40px]">
                {map(displayedTags, (tag) => {
                  const isActive = includes(localSelectedTags, tag);
                  return (
                          <Badge
                                  key={tag}
                                  colorScheme={isActive ? "default" : "outline"}
                                  size="sm"
                                  className={cn("cursor-pointer", {
                                    "hover:bg-gray-200": !isActive,
                                    "hover:bg-blue-700": isActive
                                  })}
                                  onClick={() => handleTagToggle(tag)}
                          >
                            {tag}
                          </Badge>
                  );
                })}

                {shouldShowExpandButton && (
                        <Button
                                variant="outline"
                                size="sm"
                                type="button"
                                onClick={handleExpandToggle}
                                className="h-7 px-3 text-xs rounded-lg border-blue-200 text-blue-600 hover:bg-blue-50 hover:border-blue-300"
                                rightIcon={isExpanded ? <ChevronUpIcon size={14}/> : <ChevronDownIcon size={14}/>}
                        >
                          {isExpanded ? "See less" : `See all (${filteredTags.length - maxVisibleTags} more)`}
                        </Button>
                )}

                {filteredTags.length === 0 && searchInput && (
                        <Text size="sm" muted>No tags found matching "{searchInput}"</Text>
                )}

                {allTags.length === 0 && !searchInput && (
                        <Text size="sm" muted>No tags available</Text>
                )}
              </div>

              {isEmpty(localSelectedTags) ? null : (
                      <div className="mt-2 flex flex-row flex-wrap gap-3">
                        {map(localSelectedTags, string => (<Text size="xs" muted className="italic">
                          {string}
                        </Text>))}

                        <div onClick={() => setLocalSelectedTags([])} className="cursor-pointer">
                          <XIcon color={"red"} size={16}/>
                        </div>
                      </div>
              )}
            </div>
          </div>
  );
};
