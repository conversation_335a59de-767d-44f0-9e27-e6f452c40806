import React from 'react';
import { FieldApi } from "@tanstack/react-form";
import { isEmpty } from "lodash";
import { Switch } from "@/src/components/ui/switch";
import { Label } from "@/src/components/ui/label";
import { cn } from "@/src/util";

export interface FormSwitchProps {
  field: FieldApi<any, any, any, any>;
  label?: React.ReactNode;
  description?: React.ReactNode;
  disabled?: boolean;
  className?: string;
  onValueChange?: (checked: boolean) => void;
}

export const FormSwitch: React.FC<FormSwitchProps> = ({
                                                        field,
                                                        label,
                                                        description,
                                                        disabled,
                                                        className,
                                                        onValueChange,
                                                      }) => {
  const handleCheckedChange = (checked: boolean) => {
    field.handleChange(checked);
    onValueChange?.(checked);
  };

  return (
          <div className={cn("flex items-center space-x-2", className)}>
            <Switch
                    id={field.name}
                    checked={field.state.value}
                    onCheckedChange={handleCheckedChange}
                    disabled={disabled}
                    className={!isEmpty(field.state.meta.errors) ? 'border-red-600 bg-primary-700' : 'bg-primary-700'}
            />
            {label && (
                    <div className="grid gap-1.5 leading-none">
                      <Label
                              htmlFor={field.name}
                              className={cn(
                                      "cursor-pointer",
                                      !isEmpty(field.state.meta.errors) ? 'text-red-600' : ''
                              )}
                      >
                        {label}
                      </Label>
                      {description && (
                              <p className="text-sm text-muted-foreground">
                                {description}
                              </p>
                      )}
                    </div>
            )}
          </div>
  );
};
