import React from 'react';
import {FieldApi} from "@tanstack/react-form";
import {isEmpty, map} from "lodash";
import {Select, SelectContent, SelectGroup, SelectItem, SelectTrigger, SelectValue,} from "@/src/components/ui/select";
import {Button} from "@/src/components/ui/button.tsx";
import {XIcon} from 'lucide-react';

export interface DropdownOption {
  value: string;
  label: string;
}

export interface FormSelectProps {
  field: FieldApi<any, any, any, any>;
  options?: DropdownOption[];
  placeholder?: string;
  className?: string;
  onChange?: (value: string) => void;
  isDisabled?: boolean;
  isClearable?: boolean;
  children?: any;
}

export const FormSelect: React.FC<FormSelectProps> = ({
                                                        field,
                                                        options, children,
                                                        placeholder = "Select an option",
                                                        className, isDisabled,
                                                        onChange, isClearable,
                                                      }) => {
  const handleValueChange = (value: string) => {
    field.handleChange(value);
    onChange?.(value);
  };

  return <Select disabled={isDisabled}
                 value={field.state.value}
                 onValueChange={handleValueChange}>
    <div className={"flex flex-row gap-2 items-center"}>
      <SelectTrigger
              id={field.name}
              className={`${className} ${!isEmpty(field.state.meta.errors) ? 'border-red-600' : ''}`}
      >
        <SelectValue placeholder={placeholder} className={"grow"}/>
      </SelectTrigger>
      {isClearable && !isEmpty(field.state.value) ?
              <Button variant={"ghost"} size={"iconSm"}
                      type="button"
                      onClick={() => field.handleChange("")}>
                <XIcon size={16}/>
              </Button> : null}
    </div>
    <SelectContent>
      {children ?
              children
              : <SelectGroup>
                {map(options, (option) => <SelectItem
                        key={option.value}
                        value={option.value}>
                  {option.label}
                </SelectItem>)}
              </SelectGroup>}
    </SelectContent>
  </Select>;
};
