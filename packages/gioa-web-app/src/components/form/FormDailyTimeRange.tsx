import React, {useMemo} from 'react';
import {compareTimeOfDay, getRangeDurationHours, parse24HourTime} from "../../../../api/src/date.util.ts";
import {FormControl} from "@/src/components/form/FormControl.tsx";
import {Label} from "@/src/components/ui/label.tsx";
import {FormInput} from "@/src/components/form/FormInput.tsx";
import {hourFloatToFriendlyDuration, incrementDurationMinutes} from "../../../../api/src/scheduleBuilder.util.ts";
import {FieldInfo} from "@/src/components/form/FieldInfo.tsx";
import {ValueBadge} from "@/src/components/ValueBadge.tsx";
import {ReactFormExtendedApi, Validator} from '@tanstack/react-form';
import {ZodType} from 'zod';
import {cn} from "@/src/util.ts";

export interface FormDailyTimeRangeProps {
  form: ReactFormExtendedApi<any, Validator<unknown, ZodType>>;
  onStartTimeChange?: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onEndTimeChange?: (e: React.ChangeEvent<HTMLInputElement>) => void;
  className?: string;
  fieldPrefix?: string;
  max?: string;
  min?: string;
}

export const FormDailyTimeRange: React.FC<FormDailyTimeRangeProps> = ({form, max, min, fieldPrefix, onEndTimeChange, onStartTimeChange, className}) => {
  const formStart = form.useStore(s => s.values.start);
  const formEnd = form.useStore(s => s.values.end);

  const friendlyDuration = useMemo(() => {
    const duration = getRangeDurationHours({
      start: formStart,
      end: formEnd
    });
    if (Number.isNaN(duration)) {
      return "---";
    }

    return hourFloatToFriendlyDuration(duration);
  }, [formStart, formEnd]);

  return <div className={cn("flex gap-2 items-start", className)}>
    <form.Field name={`${fieldPrefix || ""}start`}
                validators={{
                  onChange: ({value, fieldApi}) => {
                    if (!value) {
                      return "Required";
                    }

                    const parsed = parse24HourTime(value);
                    if (!parsed || Number.isNaN(parsed[0]) || Number.isNaN(parsed[1])) {
                      return "Invalid time";
                    }

                    const comparison = compareTimeOfDay(value, fieldApi.form.getFieldValue("end"));
                    if (comparison >= 0) {
                      return "Start time must be before end time";
                    }
                    return undefined;
                  },
                  onChangeListenTo: ['end'],
                }}
                children={(field) => {
                  return <FormControl className={"w-1/2"}>
                    <Label htmlFor={field.name}>Start Time</Label>
                    <FormInput field={field} type={"time"} step={incrementDurationMinutes * 60}
                               onChange={onStartTimeChange}
                               placeholder="Choose start time..."/>
                    <FieldInfo field={field}/>
                  </FormControl>;
                }}/>
    <form.Field name={`${fieldPrefix || ""}end`}
                validators={{
                  onChange: ({value, fieldApi}) => {
                    if (!value) {
                      return "Required";
                    }

                    const parsed = parse24HourTime(value);
                    if (!parsed || Number.isNaN(parsed[0]) || Number.isNaN(parsed[1])) {
                      return "Invalid time";
                    }

                    const comparison = compareTimeOfDay(value, fieldApi.form.getFieldValue("start"));
                    if (comparison <= 0) {
                      return "End time must be after start time";
                    }
                    return undefined;
                  },
                  onChangeListenTo: ['start'],
                }}
                children={(field) => {
                  return <FormControl className={"w-1/2"}>
                    <Label htmlFor={field.name}>End Time</Label>
                    <FormInput field={field} type={"time"} step={incrementDurationMinutes * 60}
                               onChange={onEndTimeChange}
                               placeholder="Choose end time..."/>
                    <FieldInfo field={field}/>
                  </FormControl>;
                }}/>
    <FormControl>
      <Label>Duration</Label>
      <ValueBadge className={"whitespace-nowrap h-10 flex justify-center items-center"}>
        {friendlyDuration}
      </ValueBadge>
    </FormControl>
  </div>
}
