import React, {useEffect, useRef, useState} from 'react';
import {Button} from '@/src/components/ui/button';
import {FieldApi} from '@tanstack/react-form';
import {Card} from '@/src/components/ui/card';
import {Text} from '@/src/components/Text';
import {FileTextIcon} from 'lucide-react';
import {Textarea} from '../ui/textarea';

interface FormTextareaInlineProps {
  field: FieldApi<any, any, any, any>;
  isNew?: boolean;
  onSave?: () => void;
  onCancel?: () => void;
}

export function FormTextareaInline({
  field,
  isNew = false,
  onSave,
  onCancel
}: FormTextareaInlineProps) {
  const [isEditing, setIsEditing] = useState(isNew);
  const [localValue, setLocalValue] = useState(field.state.value || '');
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  useEffect(() => {
    if (isEditing && textareaRef.current) {
      textareaRef.current.focus();
    }
  }, [isEditing]);

  const handleSave = () => {
    if (localValue.trim()) {
      field.handleChange(localValue);
      setIsEditing(false);
      onSave?.();
    }
  };

  const handleCancel = () => {
    setLocalValue(field.state.value || '');
    setIsEditing(false);
    onCancel?.();
  };

  if (isEditing) {
    return (
      <Card className="p-4 mb-3 border border-gray-200">
        <div className="space-y-3">
          <div className="flex items-center">
            <FileTextIcon size={20} className="text-blue-500 mr-2" />
            <Text semibold>Text Instruction</Text>
          </div>

          <Textarea
            value={localValue}
            onChange={(e) => setLocalValue(e.target.value)}
            placeholder="Enter text instructions..."
            rows={4}
            ref={textareaRef}
          />

          <div className="flex justify-end space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleCancel}
            >
              Cancel
            </Button>
            <Button
              size="sm"
              onClick={handleSave}
              disabled={!localValue.trim()}
            >
              Save
            </Button>
          </div>
        </div>
      </Card>
    );
  }

  return (
    <Card
      className="p-4 mb-3 border border-gray-200 cursor-pointer hover:bg-gray-50"
      onClick={() => setIsEditing(true)}
    >
      <div className="flex items-center">
        <FileTextIcon size={20} className="text-blue-500 mr-2 flex-shrink-0" />
        <Text className="flex-1">
          {field.state.value || <span className="text-gray-400">Click to add text instruction</span>}
        </Text>
      </div>
    </Card>
  );
}
