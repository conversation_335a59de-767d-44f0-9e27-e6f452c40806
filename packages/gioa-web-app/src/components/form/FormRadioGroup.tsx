import React from 'react';
import { FieldApi } from "@tanstack/react-form";
import { map } from "lodash";
import { cn } from "@/src/util";
import { Text } from "@/src/components/Text";
import { CircleIcon, CircleDotIcon } from "lucide-react";
import { cva, type VariantProps } from "class-variance-authority";

export interface RadioOption {
  label: string;
  value: any;
}

export const formRadioGroupVariants = cva(
  "flex flex-wrap gap-2",
  {
    variants: {
      variant: {
        default: "",
        outline: "",
        solid: "",
      },
      size: {
        sm: "gap-1",
        md: "gap-2",
        lg: "gap-3",
      },
      orientation: {
        horizontal: "flex-row",
        vertical: "flex-col justify-start items-start",
      },
      colorScheme: {
        default: "",
        secondary: "",
        green: "",
        red: "",
        blue: "",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "md",
      orientation: "horizontal",
      colorScheme: "default",
    },
  }
);

export const radioItemVariants = cva(
  "flex flex-row items-center gap-2 px-4 py-2 rounded-full transition-colors",
  {
    variants: {
      variant: {
        default: "",
        outline: "border",
        solid: "",
      },
      size: {
        sm: "px-3 py-1 text-xs",
        md: "px-4 py-2 text-sm",
        lg: "px-5 py-3 text-base",
      },
      colorScheme: {
        default: [
          "data-[state=selected]:bg-primary-700 data-[state=selected]:text-white",
          "data-[state=unselected]:bg-gray-200 data-[state=unselected]:text-black data-[state=unselected]:hover:bg-gray-300"
        ],
        secondary: [
          "data-[state=selected]:bg-secondary-700 data-[state=selected]:text-white",
          "data-[state=unselected]:bg-gray-200 data-[state=unselected]:text-black data-[state=unselected]:hover:bg-gray-300"
        ],
        green: [
          "data-[state=selected]:bg-green-600 data-[state=selected]:text-white",
          "data-[state=unselected]:bg-green-100 data-[state=unselected]:text-green-800 data-[state=unselected]:hover:bg-green-200"
        ],
        red: [
          "data-[state=selected]:bg-red-600 data-[state=selected]:text-white",
          "data-[state=unselected]:bg-red-100 data-[state=unselected]:text-red-800 data-[state=unselected]:hover:bg-red-200"
        ],
        blue: [
          "data-[state=selected]:bg-blue-600 data-[state=selected]:text-white",
          "data-[state=unselected]:bg-blue-100 data-[state=unselected]:text-blue-800 data-[state=unselected]:hover:bg-blue-200"
        ],
      },
      isSelected: {
        true: "data-[state=selected]",
        false: "data-[state=unselected]",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "md",
      colorScheme: "default",
    },
  }
);

export interface FormRadioGroupProps extends VariantProps<typeof formRadioGroupVariants> {
  field: FieldApi<any, any, any, any>;
  options: RadioOption[];
  className?: string;
  orientation?: "horizontal" | "vertical";
}

export const FormRadioGroup: React.FC<FormRadioGroupProps> = ({
  field,
  options,
  className = "mb-4",
  orientation = "horizontal",
  variant,
  size,
  colorScheme,
}) => {
  return (
    <div className={cn(
      formRadioGroupVariants({ orientation, variant, size, colorScheme }),
      className
    )}>
      {map(options, (opt) => {
        const isSelected = field.state.value === opt.value;
        return (
          <button
            type="button"
            key={opt.value}
            onClick={() => field.handleChange(opt.value)}
            className={cn(
              radioItemVariants({
                variant,
                size,
                colorScheme,
                isSelected,
              })
            )}
            data-state={isSelected ? "selected" : "unselected"}
          >
            {isSelected
              ? <CircleDotIcon
                  size={size === "sm" ? 16 : size === "lg" ? 24 : 20}
                  className="text-current" />
              : <CircleIcon
                  size={size === "sm" ? 16 : size === "lg" ? 24 : 20}
                  className="text-current" />}
            <Text
              size={size === "sm" ? "xs" : size === "lg" ? "md" : "sm"}
              colorScheme={isSelected ? "light" : "default"}
              className="leading-none"
            >
              {opt.label}
            </Text>
          </button>
        );
      })}
    </div>
  );
};
