import * as React from "react";
import {forwardRef} from "react";
import {FieldApi} from "@tanstack/react-form";
import {DateTime} from "luxon";
import {Clock} from "lucide-react";
import {Label} from "@/src/components/ui/label";
import {Input} from "@/src/components/ui/input";
import {cn} from "@/src/util";
import {parse24HourTime} from "../../../../api/src/date.util";
import {isEmpty} from "lodash";

interface FormTimePickerProps {
  field: FieldApi<any, any, any, any>;
  timezone: string;
  minutesStep?: number;
  placeholder?: string;
  className?: string;
  disabled?: boolean;
}

export const FormTimePicker = forwardRef<HTMLInputElement, FormTimePickerProps>(
        (
                {
                  field,
                  timezone,
                  minutesStep = 1,
                  placeholder = "Select time",
                  className,
                  disabled = false,
                },
                ref
        ) => {
          const toDt = (date?: Date): DateTime | null => {
            if (!date) return null;
            return DateTime.fromJSDate(date, { zone: timezone });
          };

          const dateTime = toDt(field.state.value);

          const handleTimeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
            const timeString = e.target.value;
            const [hours, minutes] = parse24HourTime(timeString);
            if (isNaN(hours) || isNaN(minutes)) return;

            const base = dateTime ?? DateTime.now().setZone(timezone);

            const updated = base.set({
              hour: hours,
              minute: minutes,
              second: 0,
              millisecond: 0,
            });

            field.handleChange(updated.toJSDate());
          };

          const formatTimeForInput = (dt?: DateTime | null): string => {
            if (!dt) return "";
            return `${dt.hour.toString().padStart(2, "0")}:${dt.minute
                    .toString()
                    .padStart(2, "0")}`;
          };

          const hasErrors = !isEmpty(field.state.meta.errors);

          return (
                  <div className={cn("flex items-center gap-2", className)}>
                    <Input
                            ref={ref}
                            type="time"
                            value={formatTimeForInput(dateTime)}
                            onChange={handleTimeChange}
                            step={minutesStep * 60}
                            placeholder={placeholder}
                            disabled={disabled}
                            className={cn("w-32", {
                              "border-red-500": hasErrors,
                            })}
                    />
                  </div>
          );
        }
);

FormTimePicker.displayName = "FormTimePicker";
