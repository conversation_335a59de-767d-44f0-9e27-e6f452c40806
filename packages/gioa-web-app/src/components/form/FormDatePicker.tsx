import React, { useState } from 'react';
import { FieldApi } from "@tanstack/react-form";
import { isEmpty } from "lodash";
import { cn } from "@/src/util";
import { Calendar as CalendarIcon } from "lucide-react";
import { Button } from "@/src/components/ui/button";
import { Calendar } from "@/src/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/src/components/ui/popover";
import { format, parse } from "date-fns";

interface FormDatePickerProps {
  field: FieldApi<any, any, any, any>;
  placeholder?: string;
  className?: string;
  onChange?: (value: Date) => void;
  dateFormat?: string;
}

export const FormDatePicker: React.FC<FormDatePickerProps> = ({
  field,
  placeholder = "Select date",
  className,
  onChange,
  dateFormat = "MM/dd/yyyy",
}) => {
  const [inputValue, setInputValue] = useState<string>(
    field.state.value ? format(field.state.value, dateFormat) : ""
  );
  const [isOpen, setIsOpen] = useState(false);

  const handleCalendarSelect = (date: Date | undefined) => {
    if (date) {
      field.handleChange(date);
      setInputValue(format(date, dateFormat));
      onChange?.(date);
      setIsOpen(false);
    }
  };

  const handleInputBlur = () => {
    // Try to parse the date on blur
    try {
      const date = parse(inputValue, dateFormat, new Date());
      if (!isNaN(date.getTime())) {
        field.handleChange(date);
        onChange?.(date);
      } else {
        // If invalid, reset to the current field value or empty
        setInputValue(field.state.value ? format(field.state.value, dateFormat) : "");
      }
    } catch (error) {
      // Invalid date format, reset to the current field value or empty
      setInputValue(field.state.value ? format(field.state.value, dateFormat) : "");
    }
    field.handleBlur();
  };

  // Format the input as the user types (add slashes automatically)
  const formatInput = (input: string): string => {
    // For MM/DD/YYYY format
    if (dateFormat === "MM/dd/yyyy") {
      // Remove any non-digit characters
      const digits = input.replace(/\D/g, '');

      if (digits.length <= 2) {
        return digits;
      } else if (digits.length <= 4) {
        return `${digits.substring(0, 2)}/${digits.substring(2)}`;
      } else {
        return `${digits.substring(0, 2)}/${digits.substring(2, 4)}/${digits.substring(4, 8)}`;
      }
    }

    // Default case, just return the input
    return input;
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    // Handle backspace to remove the last character including the slash
    if (e.key === 'Backspace' &&
        (inputValue.endsWith('/') || inputValue.length > 0 && inputValue[inputValue.length - 2] === '/')) {
      e.preventDefault();
      setInputValue(prev => prev.slice(0, -1));
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const rawValue = e.target.value;
    const formattedValue = formatInput(rawValue);
    setInputValue(formattedValue);

    // Try to parse the date if we have a complete format
    if (formattedValue.length === dateFormat.length) {
      try {
        const date = parse(formattedValue, dateFormat, new Date());
        if (!isNaN(date.getTime())) {
          field.handleChange(date);
          onChange?.(date);
        }
      } catch (error) {
        // Invalid date, just update the input
      }
    }
  };

  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <div className="relative">
        <input
          type="text"
          value={inputValue}
          onChange={handleInputChange}
          onKeyDown={handleKeyDown}
          onBlur={handleInputBlur}
          placeholder={placeholder}
          className={cn(
            "flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",
            !isEmpty(field.state.meta.errors) ? "border-red-600" : "",
            className
          )}
        />
        <PopoverTrigger asChild>
          <Button
            variant="ghost"
            className="absolute right-0 top-0 h-10 w-10 p-0"
            type="button"
          >
            <CalendarIcon size={16} />
          </Button>
        </PopoverTrigger>
      </div>
      <PopoverContent className="w-auto p-0" align="start">
        <Calendar
          mode="single"
          selected={field.state.value}
          onSelect={handleCalendarSelect}
          initialFocus
        />
      </PopoverContent>
    </Popover>
  );
};

export default FormDatePicker;
