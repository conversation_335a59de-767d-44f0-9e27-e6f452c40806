import React, {useRef, useState} from 'react';
import {FieldA<PERSON>} from '@tanstack/react-form';
import {Text} from '@/src/components/Text';
import {FileAttachmentDto} from '@gioa/api/src/fileAttachment.dto';
import {FileIcon, ImageIcon, PaperclipIcon, PlusIcon, XIcon} from 'lucide-react';
import {cn} from "@/src/util.ts";
import {imageUrl} from "@/src/images.ts";
import {Button} from "@/src/components/ui/button.tsx";


export type AttachmentValue = File | FileAttachmentDto | null | undefined;

export type AttachmentType = 'image' | 'video' | 'file';

export interface AttachmentDisplayProps {
  placeholder?: React.ReactNode;
  value?: AttachmentValue | FileAttachmentDto | File;
  hasErrors?: boolean;
}

export const AttachmentDisplay: React.FC<AttachmentDisplayProps> = ({
                                                                      placeholder,
                                                                      value,
                                                                      hasErrors,
                                                                    }) => {
  const renderPlaceholder = placeholder ? (
    <div className="flex items-center justify-between text-gray-400 p-2">
      <Text>{placeholder}</Text>
      <PlusIcon size={20}/>
    </div>
  ) : null;

  const renderFile = (label: string, filename?: string) => (
    <div className="p-2">
      <Text>
        {label} {filename}
      </Text>
    </div>
  );

  const renderPressableImage = (attachment: FileAttachmentDto) => {
    const imgUrl = imageUrl(attachment.url, {width: 1280});
    return attachment.url ? (
      <a href={imgUrl} className={"p-2 block"}
         target="_blank"
         rel="noopener noreferrer">
        <img
          src={imgUrl}
          alt={attachment.filename || 'Image'}
          className="object-cover w-[100px] h-[100px]"
        />
      </a>
    ) : (
      <Text>No image URL</Text>
    );
  };

  // Handle File objects
  if (value instanceof File) {
    const isImage = value.type.startsWith('image/');
    const isVideo = value.type.startsWith('video/');
    const isDocument = value.type.includes('pdf') || value.type.includes('document');

    if (isImage) {
      const objectUrl = URL.createObjectURL(value);
      return (
        <div className={cn(
          "border rounded-md overflow-hidden p-2 flex flex-row items-center gap-3",
          hasErrors ? "border-red-600" : "border-gray-200"
        )}>
          <img
            src={objectUrl}
            alt={value.name}
            className="object-cover w-[100px] h-[100px]"
            onLoad={() => URL.revokeObjectURL(objectUrl)}
          />
          <Text>{value.name}</Text>
        </div>
      );
    } else if (isVideo) {
      return (
        <div className={cn(
          "border rounded-md overflow-hidden",
          hasErrors ? "border-red-600" : "border-gray-200"
        )}>
          {renderFile("Video:", value.name)}
        </div>
      );
    } else if (isDocument) {
      return (
        <div className={cn(
          "border rounded-md overflow-hidden",
          hasErrors ? "border-red-600" : "border-gray-200"
        )}>
          {renderFile("Document:", value.name)}
        </div>
      );
    } else {
      return (
        <div className={cn(
          "border rounded-md overflow-hidden",
          hasErrors ? "border-red-600" : "border-gray-200"
        )}>
          {renderFile("File:", value.name)}
        </div>
      );
    }
  }

  return (
    <div className={cn(
      "border rounded-md overflow-hidden",
      hasErrors ? "border-red-600" : "border-gray-200"
    )}>
      {value && "id" in value
        ? value.mediaType === "image"
          ? renderPressableImage(value)
          : value.mediaType === "video"
            ? renderFile("Video:", value.filename)
            : value?.mediaType === "document"
              ? renderFile("Document:", value.filename)
              : value.mediaType === "file"
                ? renderFile("File:", value.filename)
                : renderPlaceholder
        : renderPlaceholder}
    </div>
  );
};

interface FormAttachmentInputProps {
  field: FieldApi<any, any, any, any>;
  types?: ('image' | 'file')[];
  disableImagePress?: boolean;
}

export function FormAttachmentInput({
                                      field,
                                      types = ['image', 'file'],
                                      disableImagePress = false
                                    }: FormAttachmentInputProps) {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [error, setError] = useState<string | null>(null);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    // Reset the file input
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }

    // Check file size (10MB limit)
    if (file.size > 10 * 1024 * 1024) {
      setError('File size exceeds 10MB limit');
      return;
    }

    // Check file type
    const isImage = file.type.startsWith('image/');
    if (isImage && !types.includes('image')) {
      setError('Image files are not allowed');
      return;
    }

    if (!isImage && !types.includes('file')) {
      setError('Only image files are allowed');
      return;
    }

    setError(null);

    // Pass the File object directly
    field.handleChange(file);
  };

  const handleRemove = () => {
    field.handleChange(null);
    setError(null);
  };

  const renderAttachment = () => {
    const value = field.state.value;
    if (!value) return null;

    // Handle File objects and FileAttachmentDto objects
    if (value instanceof File) {
      // It's a File object
      const isImage = value.type.startsWith('image/');
      const objectUrl = URL.createObjectURL(value);

      if (isImage) {
        const imageObjectUrl = new URL(objectUrl);
        imageObjectUrl.searchParams.append('w', '1280');

        return (
          <div className="relative">
            <div className="border border-gray-200 rounded-md overflow-hidden p-2">
              <img
                src={objectUrl}
                alt={value.name}
                className="max-h-64 object-contain mx-auto"
                onClick={disableImagePress ? undefined : () => window.open(imageObjectUrl, '_blank')}
                style={{cursor: disableImagePress ? 'default' : 'pointer'}}
                onLoad={() => URL.revokeObjectURL(objectUrl)} // Clean up the URL when done
              />
            </div>
            <button
              className="absolute top-2 right-2 bg-white rounded-full p-1 shadow-md"
              onClick={handleRemove}
              type="button"
            >
              <XIcon size={16}/>
            </button>
          </div>
        );
      }

      return (
        <div className="flex items-center justify-between p-3 border border-gray-200 rounded-md">
          <div className="flex items-center">
            <FileIcon size={20} className="mr-2 text-blue-500"/>
            <Text>{value.name}</Text>
          </div>
          <button
            className="text-gray-500 hover:text-gray-700"
            onClick={handleRemove}
            type="button"
          >
            <XIcon size={16}/>
          </button>
        </div>
      );
    } else if ('fileId' in value) {
      // It's a FileAttachmentDto
      const isImage = value.mimeType?.startsWith('image/');
      const url = value.url;

      if (isImage && url) {
        const imageObjectUrl = new URL(url);
        imageObjectUrl.searchParams.append('w', '1280');

        return (
          <div className="relative">
            <div className="border border-gray-200 rounded-md overflow-hidden">
              <img
                src={imageUrl(url, {
                  // max-h-64 * 2 for high density screens
                  height: 256 * 2
                })}
                alt={value.filename || 'Attachment'}
                className="max-h-64 object-contain mx-auto"
                onClick={disableImagePress ? undefined : () => window.open(imageObjectUrl, '_blank')}
                style={{cursor: disableImagePress ? 'default' : 'pointer'}}
              />
            </div>
            <button
              className="absolute top-2 right-2 bg-white rounded-full p-1 shadow-md"
              onClick={handleRemove}
              type="button"
            >
              <XIcon size={16}/>
            </button>
          </div>
        );
      }

      return (
        <div className="flex items-center justify-between p-3 border border-gray-200 rounded-md">
          <div className="flex items-center">
            <FileIcon size={20} className="mr-2 text-blue-500"/>
            <Text>{value.filename || 'Attachment'}</Text>
          </div>
          <button
            className="text-gray-500 hover:text-gray-700"
            onClick={handleRemove}
            type="button"
          >
            <XIcon size={16}/>
          </button>
        </div>
      );
    }

    // Unknown type
    return (
      <div className="flex items-center justify-between p-3 border border-gray-200 rounded-md">
        <div className="flex items-center">
          <FileIcon size={20} className="mr-2 text-blue-500"/>
          <Text>Unknown attachment</Text>
        </div>
        <button
          className="text-gray-500 hover:text-gray-700"
          onClick={handleRemove}
          type="button"
        >
          <XIcon size={16}/>
        </button>
      </div>
    );
  };

  return (
    <div className="space-y-2">
      {field.state.value ? (
        renderAttachment()
      ) : (
        <div className="flex gap-2">
          {types.includes('image') && (
            <Button
              type="button"
              variant="outline"
              onClick={() => fileInputRef.current?.click()}
              className="flex-1"
            >
              <ImageIcon size={16} className="mr-2"/>
              Add Image
            </Button>
          )}
          {types.includes('file') && (
            <Button
              type="button"
              variant="outline"
              onClick={() => fileInputRef.current?.click()}
              className="flex-1"
            >
              <PaperclipIcon size={16} className="mr-2"/>
              Add File
            </Button>
          )}
          <input
            ref={fileInputRef}
            type="file"
            className="hidden"
            accept={types.includes('image') && types.includes('file')
              ? '*/*'
              : types.includes('image')
                ? 'image/*'
                : '.pdf,.doc,.docx,.txt,.xls,.xlsx'}
            onChange={handleFileChange}
          />
        </div>
      )}
      {error && <Text className="text-red-500 text-sm">{error}</Text>}
    </div>
  );
}
