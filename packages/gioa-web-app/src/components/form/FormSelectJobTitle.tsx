import React, {useEffect, useRef, useState} from 'react';
import {FieldApi} from "@tanstack/react-form";
import {find, isEmpty, reduce} from "lodash";
import {cn} from "@/src/util";
import {Briefcase} from "lucide-react";
import {But<PERSON>} from "@/src/components/ui/button";
import {api} from "@/src/api";
import {CheckedIds} from "@/src/components/PolicyEditor.tsx";
import {SelectJobTitle} from "../SelectJobTitle.tsx";
import {Label} from "@/src/components/ui/label";
import {FieldInfo} from "./FieldInfo";

interface FormSelectJobTitleProps {
  field: FieldApi<any, any, any, any>;
  storeId: string;
  personId?: string;
  placeholder?: string;
  className?: string;
  skipPermissions?: boolean;
}

export const FormSelectJobTitle = React.forwardRef<HTMLButtonElement, FormSelectJobTitleProps>(({
  field,
  storeId,
  personId,
  placeholder = "Select Job Title...",
  className,
  skipPermissions = false,
}, ref) => {
  const [isOpen, setIsOpen] = useState(false);

  const { data: business } = api.user.getBusiness.useQuery();
  const { data: person } = api.user.getPersonDetail.useQuery(
    { personId: personId!, storeId: storeId! },
    { enabled: !!personId }
  );

  const selectedJobId = field.state.value?.jobId;
  const selectedJob = business?.jobs ?
    find(business.jobs, job => job.id === selectedJobId) : undefined;

  const handleSelectJob = (jobId: string, permissionPackages: CheckedIds) => {
    field.handleChange({
      jobId: jobId,
      permissionPackages: permissionPackages,
    });
    setIsOpen(false);
  };

  return (
    <div className="space-y-2">
      <Label>Job Title</Label>
      <Button
        ref={ref}
        type="button"
        variant="outline"
        className={cn(
          "w-full justify-between",
          !isEmpty(field.state.meta.errors) ? "border-red-600" : "",
          className
        )}
        onClick={(e) => {
          e.preventDefault();
          e.stopPropagation();
          setIsOpen(true);
        }}
      >
        <div className="flex items-center">
          <Briefcase className="mr-2 h-4 w-4" />
          <span>{selectedJob?.title || placeholder}</span>
        </div>
      </Button>

      {isOpen && (
        <SelectJobTitle
          initialJobId={selectedJobId}
          personName={person ? `${person.firstName} ${person.lastName}` : undefined}
          skipPermissions={skipPermissions}
          onSelect={handleSelectJob}
          onCancel={() => setIsOpen(false)}
        />
      )}
      <FieldInfo field={field} />
    </div>
  );
});

export default FormSelectJobTitle;
