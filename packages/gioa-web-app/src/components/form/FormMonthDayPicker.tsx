import React, { useState, useRef, useEffect } from 'react';
import { FieldApi } from "@tanstack/react-form";
import { isEmpty } from "lodash";
import {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuItem,
} from "@/src/components/ui/dropdown-menu";
import { Button } from "@/src/components/ui/button";
import { ChevronDown } from "lucide-react";
import {
  format,
  getDaysInMonth,
  parse,
  setMonth,
  setDate
} from 'date-fns';

interface MonthDayPickerProps {
  field: FieldApi<any, any, any, any>;
  placeholder?: string;
  className?: string;
  onChange?: (value: string) => void;
}

export const FormMonthDayPicker: React.FC<MonthDayPickerProps> = ({
                                                                    field,
                                                                    placeholder = "Select month and day",
                                                                    className,
                                                                    onChange,
                                                                  }) => {
  const [isMonthOpen, setIsMonthOpen] = useState(false);
  const [isDayOpen, setIsDayOpen] = useState(false);
  const dayDropdownRef = useRef<HTMLDivElement>(null);

  const currentYear = new Date().getFullYear();
  const selectedDate = field.state.value
    ? parse(field.state.value, 'MM-dd', new Date(currentYear, 0, 1))
    : new Date(currentYear, 0, 1);

  const handleMonthSelect = (monthIndex: number) => {
    const newDate = setMonth(selectedDate, monthIndex);
    const newValue = format(newDate, 'MM-dd');
    field.handleChange(newValue);
    setIsMonthOpen(false);
    onChange?.(newValue);
  };

  const handleDaySelect = (day: number) => {
    const newDate = setDate(selectedDate, day);
    const newValue = format(newDate, 'MM-dd');
    field.handleChange(newValue);
    setIsDayOpen(false);
    onChange?.(newValue);
  };

  const selectedMonth = format(selectedDate, 'MMMM');
  const selectedDay = format(selectedDate, 'd');

  useEffect(() => {
    if (isDayOpen && dayDropdownRef.current) {
      const selectedDayElement = dayDropdownRef.current.querySelector(`[data-day="${selectedDay}"]`);
      if (selectedDayElement) {
        selectedDayElement.scrollIntoView({ block: 'nearest', behavior: 'smooth' });
      }
    }
  }, [isDayOpen, selectedDay]);

  return (
    <div className="flex gap-2">
      <DropdownMenu open={isMonthOpen} onOpenChange={setIsMonthOpen}>
        <DropdownMenuTrigger asChild>
          <Button
            variant="outline"
            className={`w-full justify-between ${className} ${!isEmpty(field.state.meta.errors) ? 'border-red-600' : ''}`}
          >
            {field.state.value ? selectedMonth : "Month"}
            <ChevronDown className="ml-2 h-4 w-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent className="w-full">
          {Array.from({ length: 12 }, (_, i) => (
            <DropdownMenuItem
              key={i}
              onSelect={() => handleMonthSelect(i)}
              className={`${i === selectedDate.getMonth() ? 'bg-blue-100 text-blue-800' : ''}`}
            >
              {format(new Date(currentYear, i, 1), 'MMMM')}
            </DropdownMenuItem>
          ))}
        </DropdownMenuContent>
      </DropdownMenu>

      <DropdownMenu open={isDayOpen} onOpenChange={setIsDayOpen}>
        <DropdownMenuTrigger asChild>
          <Button
            variant="outline"
            className={`w-full justify-between ${className} ${!isEmpty(field.state.meta.errors) ? 'border-red-600' : ''}`}
            disabled={!field.state.value}>
            {field.state.value ? selectedDay : "Day"}
            <ChevronDown className="ml-2 h-4 w-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent className="w-full max-h-60 overflow-y-auto" ref={dayDropdownRef}>
          {field.state.value &&
            Array.from({ length: getDaysInMonth(selectedDate) }, (_, i) => (
              <DropdownMenuItem
                key={i + 1}
                onSelect={() => handleDaySelect(i + 1)}
                data-day={i + 1}
                className={`${i + 1 === parseInt(selectedDay) ? 'bg-blue-100 text-blue-800' : ''}`}
              >
                {i + 1}
              </DropdownMenuItem>
            ))
          }
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
};

export default FormMonthDayPicker;