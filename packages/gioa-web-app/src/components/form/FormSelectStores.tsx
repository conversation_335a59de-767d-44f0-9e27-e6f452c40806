import React, { useEffect, useState } from "react";
import { FieldA<PERSON> } from "@tanstack/react-form";
import { find, includes, isEmpty, map, reject } from "lodash";
import { cn } from "@/src/util";
import { AlertCircle, CircleHelpIcon, Store } from "lucide-react";
import { But<PERSON> } from "@/src/components/ui/button";
import { <PERSON><PERSON>, DialogContent, DialogFooter, DialogHeader, DialogTitle } from "@/src/components/ui/dialog";
import { api } from "@/src/api";
import { ScrollArea } from "@/src/components/ui/scroll-area";
import { Checkbox } from "@/src/components/ui/checkbox";
import { Label } from "@/src/components/ui/label";
import { Text } from "@/src/components/Text";
import { Alert, AlertDescription } from "@/src/components/ui/alert";

interface FormSelectStoresProps {
  field: FieldApi<any, any, any, any>;
  businessId: string;
  personId: string;
  className?: string;
  canUpdateEmployeeStores: boolean;
}

export const FormSelectStores: React.FC<FormSelectStoresProps> = ({
  field,
  businessId,
  personId,
  className,
  canUpdateEmployeeStores,
}) => {
  const [isOpen, setIsOpen] = useState(false);

  const { data: business } = api.user.getBusiness.useQuery();
  const { data: person } = api.user.getPersonDetail.useQuery(
    { personId: personId!, storeId: business?.stores?.[0]?.id || "" },
    { enabled: !!personId && !!business?.stores?.[0]?.id },
  );

  const handleStoreToggle = (storeId: string, isChecked: boolean) => {
    const newStoreIds = isChecked ? [...field.state.value, storeId] : reject(field.state.value, (id) => id === storeId);

    field.handleChange(newStoreIds);
  };

  const storeOptions = map(business?.stores || [], (store) => ({
    id: store.id,
    label: store.title,
  }));

  const onHelpWithStoreEmployments = () => {
    alert(
      "Store employments: A team member must be approved in at least one store. If you add a store to the team member, they will need to be approved in that store.\n\nYou cannot edit the stores before the team member is approved in at least one store.\n\nYou cannot remove a store from the team member if it is their only approved store. If you want to remove a store, add and approve them in another store first.",
    );
  };

  if (!business?.stores?.length) {
    return null;
  }

  return (
    <>
      <div className="space-y-2">
        <div className="flex justify-between items-center">
          <Label>Stores</Label>
          <Button
            variant="ghost"
            size="sm"
            onClick={onHelpWithStoreEmployments}
            type="button"
            leftIcon={<CircleHelpIcon size={16} />}
          >
            Help
          </Button>
        </div>

        <div
          onClick={() => {
            if (canUpdateEmployeeStores) {
              setIsOpen(true);
            }
          }}
          className={cn(
            "w-full justify-between min-h-[40px] h-auto px-4 py-2 border border-gray-200 rounded-lg hover:bg-gray-100",
            !isEmpty(field.state.meta.errors) ? "border-red-600" : "",
            className,
          )}
        >
          <div className="w-full">
            <div className="flex flex-col items-start text-left w-full gap-1">
              {isEmpty(field.state.value) && <Text>No stores selected</Text>}
              {map(field.state.value, (storeId: string, index: number) => {
                const store = find(business?.stores, (s) => s.id === storeId);
                if (!store) {
                  console.log("Store not found", storeId, index);
                  console.log(field.state.value);
                }
                const storeTitle = store?.title || "Unknown store";
                return (
                  <div className={"flex flex-row items-center gap-2"} key={storeTitle}>
                    <Store className="mr- h-4 w-4 shrink-0" key={storeTitle} />
                    <Text key={index} size="sm">
                      {storeTitle}
                    </Text>
                  </div>
                );
              })}
            </div>
          </div>
        </div>

        {!canUpdateEmployeeStores && (
          <Alert variant="destructive" className="mt-2">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              You cannot edit stores until the team member is approved in at least one store.
            </AlertDescription>
          </Alert>
        )}
      </div>

      <Dialog open={isOpen} onOpenChange={setIsOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Select Stores</DialogTitle>
          </DialogHeader>
          <ScrollArea className="max-h-[60vh]">
            <div className="space-y-4 p-2">
              {storeOptions.map((storeOption) => {
                const isChecked = includes(field.state.value, storeOption.id);
                const isPendingApproval = person?.employments?.requestedStoreIds?.includes(storeOption.id);

                return (
                  <div key={storeOption.id} className="flex items-center justify-between border-b border-gray-200 pb-2">
                    <Text className="flex-1">
                      {storeOption.label}
                      {isPendingApproval && <Text muted> (Pending approval)</Text>}
                    </Text>
                    <Checkbox
                      id={`store-${storeOption.id}`}
                      checked={isChecked}
                      onCheckedChange={(checked) => handleStoreToggle(storeOption.id, !!checked)}
                      disabled={!canUpdateEmployeeStores}
                    />
                  </div>
                );
              })}
            </div>
          </ScrollArea>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsOpen(false)}>
              Close
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default FormSelectStores;
