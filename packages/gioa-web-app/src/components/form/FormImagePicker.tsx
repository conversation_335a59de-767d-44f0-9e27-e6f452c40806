import React, { useCallback, useEffect } from "react";
import { FieldApi } from "@tanstack/react-form";
import { useDropzone } from "react-dropzone";
import { isEmpty, map } from "lodash";
import { Image } from "@/src/components/Image";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/src/components/ui/dropdown-menu";
import { CameraIcon, TrashIcon, X } from "lucide-react";

export interface SelectedMedia {
  type: "image";
  uri: string;
  file: File;
  width?: number;
  height?: number;
  mimeType?: string;
  filename?: string;
}

export interface FormImagePickerProps {
  field: FieldApi<any, any, any, any>;
  maxImages?: number;
  label?: string;
}

export const FormImagePicker: React.FC<FormImagePickerProps> = ({ field, maxImages = 5, label = "Add Image" }) => {
  const onDrop = useCallback(
    (acceptedFiles: File[]) => {
      acceptedFiles.forEach((file) => {
        // Create a preview URL for the image
        const uri = URL.createObjectURL(file);

        // Get image dimensions when possible
        const img = document.createElement("img");
        img.onload = () => {
          const newImage: SelectedMedia = {
            type: "image" as const,
            uri,
            file,
            filename: file.name,
            mimeType: file.type,
            width: img.width,
            height: img.height,
          };

          // Add new image to the existing array
          field.pushValue(newImage);
        };

        img.onerror = () => {
          // If we can't load the image for dimensions, add it without dimensions
          const newImage: SelectedMedia = {
            type: "image" as const,
            uri,
            file,
            filename: file.name,
            mimeType: file.type,
          };

          field.pushValue(newImage);
        };

        img.src = uri;
      });
    },
    [field],
  );

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      "image/*": [".jpeg", ".jpg", ".png", ".gif", ".webp"],
    },
    multiple: true,
    maxFiles: maxImages - (field.state.value?.length || 0),
    disabled: field.state.value?.length >= maxImages,
  });

  const handleRemoveImage = (index: number) => {
    // Get the image to be removed
    const image = field.state.value[index];

    // Revoke the object URL to prevent memory leaks
    if (image && image.uri && image.uri.startsWith("blob:")) {
      URL.revokeObjectURL(image.uri);
    }

    // Remove the image at the specified index
    field.removeValue(index);
  };

  // Clean up object URLs when component unmounts
  useEffect(() => {
    return () => {
      // Revoke all object URLs to prevent memory leaks
      if (field.state.value) {
        field.state.value.forEach((image: SelectedMedia) => {
          if (image && image.uri && image.uri.startsWith("blob:")) {
            URL.revokeObjectURL(image.uri);
          }
        });
      }
    };
  }, [field]);

  return (
    <div className="space-y-2">
      <div className="flex flex-row flex-wrap gap-2">
        {map(field.state.value, (image: SelectedMedia, idx: number) => (
          <div key={image.uri} className="relative">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <div className="cursor-pointer">
                  <Image
                    src={image.uri}
                    alt="Selected image"
                    width={100}
                    height={100}
                    className="object-contain rounded-md border border-gray-200"
                  />
                </div>
              </DropdownMenuTrigger>
              <DropdownMenuContent>
                <DropdownMenuItem
                  onClick={() => handleRemoveImage(idx)}
                  className="text-red-600 focus:text-red-600 cursor-pointer"
                >
                  <TrashIcon className="mr-2 h-4 w-4" />
                  Remove
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
            <button
              type="button"
              onClick={() => handleRemoveImage(idx)}
              className="absolute -top-2 -right-2 bg-white rounded-full p-1 shadow-sm border border-gray-200"
              aria-label="Remove image"
            >
              <X className="h-3 w-3" />
            </button>
          </div>
        ))}

        {field.state.value?.length < maxImages && (
          <div
            {...getRootProps()}
            className={`rounded-lg bg-gray-100 flex items-center justify-center p-3 cursor-pointer border-2 border-dashed ${
              isDragActive
                ? "border-primary bg-primary/10"
                : !isEmpty(field.state.meta.errors)
                  ? "border-red-300"
                  : "border-gray-300"
            }`}
            style={{ width: 100, height: 100 }}
          >
            <input {...getInputProps()} />
            <div className="flex flex-col items-center justify-center">
              <CameraIcon className="h-6 w-6 text-gray-500 mb-1" />
              <span className="text-xs text-gray-500">{label}</span>
            </div>
          </div>
        )}
      </div>

      {!isEmpty(field.state.meta.errors) && (
        <p className="text-sm text-red-500">{field.state.meta.errors.join(", ")}</p>
      )}
    </div>
  );
};
