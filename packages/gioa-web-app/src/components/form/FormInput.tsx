import React from 'react';
import {FieldA<PERSON>} from "@tanstack/react-form";
import {Input, InputProps} from "@/src/components/ui/input.tsx";
import {isEmpty} from "lodash";

export interface FormInputProps extends InputProps {
  field: FieldApi<any, any, any, any>;
  convertToNumber?: boolean;
  onRightIconClick?: () => void;
}

export const FormInput: React.FC<FormInputProps> = ({field, convertToNumber, onChange, ...props}) => {
  return (
    <Input id={field.name} hasError={!isEmpty(field.state.meta.errors)}
           name={field.name}
           value={field.state.value}
           onBlur={field.handleBlur}
           onChange={(e) => {
             if (convertToNumber) {
               field.handleChange(parseInt(e.target.value));
             } else {
               field.handleChange(e.target.value);
             }

             onChange?.(e);
           }}
           {...props}/>
  );
}
