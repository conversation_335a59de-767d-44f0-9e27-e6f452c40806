import React, { useState, useMemo } from "react";
import { FieldApi } from "@tanstack/react-form";
import { map, uniq } from "lodash";
import { Badge } from "@/src/components/ui/badge";
import { Button } from "@/src/components/ui/button";
import { EditIcon } from "lucide-react";
import { ChecklistTagsManagementModal } from "@/src/components/ChecklistTagsManagementModal";
import { api } from "@/src/api";

export interface FormChecklistTagsProps {
  field: FieldApi<any, any, any, any>;
  allTags: string[];
  storeId: string;
}

export const FormChecklistTags: React.FC<FormChecklistTagsProps> = ({
  field,
  allTags,
  storeId
}) => {
  const [isManageTagsOpen, setIsManageTagsOpen] = useState(false);
  const [temporaryTags, setTemporaryTags] = useState<string[]>([]);
  const activeTags = field.state.value || [];
  const apiUtils = api.useUtils();

  // Combine allTags with any active tags that aren't in allTags, plus any temporarily removed tags
  const availableTags = useMemo(() => {
    const activeTagsNotInAll = activeTags.filter((tag: string) => !allTags.includes(tag));
    return uniq([...allTags, ...activeTagsNotInAll, ...temporaryTags]);
  }, [allTags, activeTags, temporaryTags]);

  const handleTagToggle = (tag: string) => {
    const newTags = activeTags.includes(tag)
      ? activeTags.filter((t: string) => t !== tag)
      : [...activeTags, tag];

    // If we're removing a tag that wasn't in the original allTags, add it to temporaryTags
    // so it remains available for selection until the user leaves
    if (activeTags.includes(tag) && !allTags.includes(tag)) {
      setTemporaryTags(prev => uniq([...prev, tag]));
    }

    field.handleChange(newTags);
  };

  const handleManageTagsClose = (open: boolean) => {
    setIsManageTagsOpen(open);
    if (!open) {
      // Invalidate tags query when modal closes to refresh the available tags
      apiUtils.checklist2.getChecklistTags.invalidate();
    }
  };

  return (
    <div className="space-y-3">
      {/* Tags display */}
      <div className="flex flex-wrap gap-1 p-2 border border-gray-300 rounded min-h-[40px]">
        {map(availableTags, (tag) => {
          const isActive = activeTags.includes(tag);
          return (
            <Badge
              key={tag}
              colorScheme={isActive ? "default" : "outline"}
              size="sm"
              className="cursor-pointer"
              onClick={() => handleTagToggle(tag)}
            >
              {tag}
            </Badge>
          );
        })}
        {availableTags.length === 0 && (
          <span className="text-gray-400 text-sm">No tags available</span>
        )}
      </div>

      {/* Manage tags button */}
      <div>
        <Button
          variant="link"
          leftIcon={<EditIcon size={16} />}
          type="button"
          size="sm"
          onClick={() => setIsManageTagsOpen(true)}
        >
          Manage Tags
        </Button>
      </div>

      {/* Manage tags modal */}
      <ChecklistTagsManagementModal
        isOpen={isManageTagsOpen}
        onOpenChange={handleManageTagsClose}
        storeId={storeId}
      />
    </div>
  );
};
