import React from 'react';
import {FieldApi} from "@tanstack/react-form";
import {isEmpty} from "lodash";
import {Checkbox} from "@/src/components/ui/checkbox";
import {cn} from "@/src/util";

export interface FormCheckboxProps extends React.ComponentPropsWithoutRef<typeof Checkbox> {
  field: FieldApi<any, any, any, any>;
  label?: string | React.ReactNode;
  labelClassName?: string;
}

export const FormCheckbox: React.FC<FormCheckboxProps> = ({
                                                            field,
                                                            label, labelClassName,
                                                            className,
                                                            ...props
                                                          }) => {
  return (
    <div className="flex items-center space-x-2 p-1">
      <Checkbox
        id={field.name}
        name={field.name}
        checked={field.state.value}
        onCheckedChange={(checked) => {
          field.handleChange(checked);
        }}
        className={cn(
          className,
          !isEmpty(field.state.meta.errors) ? 'border-red-600' : ''
        )}
        {...props}
      />
      {label && (
        <label
          htmlFor={field.name}
          className={cn(
            "text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",
            !isEmpty(field.state.meta.errors) ? 'text-red-600' : '',
            labelClassName
          )}
        >
          {label}
        </label>
      )}
    </div>
  );
};
