import React, {useCallback, useEffect, useState} from 'react';
import {Popover, PopoverContent, PopoverTrigger} from "@/src/components/ui/popover.tsx";
import {Button} from "@/src/components/ui/button.tsx";
import {motion} from "framer-motion";
import {ArrowLeftIcon, SearchIcon, XIcon} from "lucide-react";
import {Input} from "@/src/components/ui/input.tsx";
import {find, map} from "lodash";
import {BaseSchedule, HawaiiACAPersonHours} from "../../../../api/src/scheduleSchemas.ts";
import {PersonDetailsPanelContent} from "@/src/components/PersonDetailsPanelContent.tsx";
import {StoreAreaDto} from "../../../../api/src/schemas.ts";
import {FieldApi} from "@tanstack/react-form";
import {DayOfWeek} from "../../../../api/src/timeSchemas.ts";
import {TeamMemberList} from "@/src/components/TeamMemberList.tsx";
import {TeamMemberSelectButton} from "@/src/components/TeamMemberSelectButton.tsx";
import {TeamMemberInfo} from "@/src/components/TeamMemberInfo.tsx";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectSeparator,
  SelectTrigger,
  SelectValue
} from "@/src/components/ui/select.tsx";
import {getPersonScore} from "../../../../api/src/shiftSuggestion.ts";
import {SchedulePersonClientDto, SchedulePersonDto} from "../../../../api/src/schedulePersonDto.ts";
import {getShift} from "../../../../api/src/scheduling.util.ts";
import {ScheduleValidationSettings} from "../../../../api/src/scheduleValidation.types.ts";

export interface FormSelectTeamMemberProps {
  people: SchedulePersonClientDto[]
  field: FieldApi<any, any, any, any>;
  schedule: BaseSchedule;
  selectedShiftId: string | null;
  storeAreas: StoreAreaDto[];
  settings: ScheduleValidationSettings;
  dayOfWeek: DayOfWeek;
  timezone: string;
  onChange?: (personId: string | null) => void;
  label?: string;
  weeksHoursMap: HawaiiACAPersonHours;
  storeState?: string;
}

export const FormSelectTeamMember: React.FC<FormSelectTeamMemberProps> = ({
                                                                            people, settings,
                                                                            selectedShiftId,
                                                                            schedule, onChange,
                                                                            storeAreas, timezone,
                                                                            field, dayOfWeek, label,
                                                                            weeksHoursMap,
                                                                            storeState = 'Unknown'
                                                                          }) => {
  const valuePerson = find(people, person => person.id === field.state.value);
  const [searchInput, setSearchInput] = useState("");
  const [selectedPerson, setSelectedPerson] = useState<SchedulePersonClientDto | null>(valuePerson ?? null);
  const [isOpen, setIsOpen] = useState(false);

  useEffect(() => {
    setSelectedPerson(valuePerson ?? null);
  }, [valuePerson]);

  const shift = selectedShiftId ? getShift(schedule, selectedShiftId) : null;
  const selectedPersonScore = shift && selectedPerson ? getPersonScore({
    shift,
    schedule,
    storeAreas,
    person: selectedPerson,
    dayOfWeek,
    timezone,
    settings,
    weeksHoursMap,
    storeState
  }) : null;

  const onViewDetails = useCallback((person: SchedulePersonClientDto) => {
    setSelectedPerson(person);
  }, []);

  const goBackToList = () => {
    setSelectedPerson(null);
  }

  const onOpenChange = (isOpen: boolean) => {
    if (!isOpen) {
      setSelectedPerson(null);
      field.handleBlur();
      setSearchInput("");
    }

    setIsOpen(isOpen);
  }

  const onSelectTeamMember = useCallback((person: SchedulePersonDto) => {
    field.handleChange(person.id);
    setSelectedPerson(null);
    field.handleBlur();
    setIsOpen(false);
    setSearchInput("");
    onChange?.(person.id);
  }, [onChange]);

  const day = find(schedule.days, d => d.dayOfWeek === dayOfWeek);
  const shiftArea = find(day?.areas, a => a.id === shift?.shiftAreaId);
  const [filterAreaId, setFilterAreaId] = useState<string>(shiftArea?.storeAreaId ?? "all");
  useEffect(() => {
    if (shift?.isShiftLead) {
      setFilterAreaId("shiftLeaders");
    } else {
      setFilterAreaId(shiftArea?.storeAreaId ?? "all");
    }
  }, [shiftArea, shift]);

  const isOnList = !Boolean(selectedPerson);

  return (
          <Popover onOpenChange={onOpenChange} open={isOpen}>
            <div className="flex items-center gap-1">
              <PopoverTrigger asChild>
                <Button variant={"outline"} className={"h-auto flex gap-2 py-2 whitespace-normal grow"} type={"button"}>
                  {valuePerson ? <TeamMemberInfo person={valuePerson}/> : label ?? "Select Team Member"}
                </Button>
              </PopoverTrigger>
              {valuePerson ? <Button size="icon" colorScheme="red" onClick={() => {
                field.handleChange(null);
                onChange?.(null);
              }}
                                     type={"button"}
                                     className="basis-auto flex-shrink-0">
                <XIcon className="text-red-700" size={20}/>
              </Button> : null}
            </div>
            <PopoverContent style={{
              maxHeight: 500,
              height: "calc(100vh - 300px)",
            }}
                            className={"p-0 w-[360px] overflow-hidden relative"}>
              <motion.div key="list" className={"absolute top-0 right-0 bottom-0 left-0 overflow-auto"}
                          initial={{x: !isOnList ? "-100%" : 0}}
                          animate={{x: !isOnList ? "-100%" : 0}}
                          exit={{x: "-100%"}}
                          transition={{type: "tween", ease: "easeInOut", duration: 0.2}}>

                <div className="px-4 py-4 border-b border-gray-200 grid grid-cols-2 gap-2">
                  <Select value={filterAreaId} onValueChange={setFilterAreaId}>
                    <SelectTrigger className="text-left">
                      <SelectValue placeholder="Filter..."/>
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value={"all"}>
                        All
                      </SelectItem>
                      <SelectItem value={"shiftLeaders"}>
                        Shift Leaders
                      </SelectItem>
                      <SelectSeparator/>
                      {map(storeAreas, area => {
                        return <SelectItem value={area.id} key={area.id}>
                          {area.title}
                        </SelectItem>
                      })}
                    </SelectContent>
                  </Select>

                  <Input
                          type="search"
                          leftIcon={SearchIcon}
                          placeholder="Search..."
                          value={searchInput}
                          onChange={(event) => setSearchInput(event.target.value)}
                          className="max-w-sm"
                  />

                </div>

                <TeamMemberList schedule={schedule} people={people} dayOfWeek={dayOfWeek} timezone={timezone}
                                settings={settings}
                                selectedShiftId={selectedShiftId} searchInput={searchInput} filterAreaId={filterAreaId}
                                storeAreas={storeAreas} onSelectTeamMember={onSelectTeamMember}
                                storeHours={schedule.storeHours} shift={shift ?? undefined}
                                isSelecting={true} onViewDetails={onViewDetails}
                                weeksHoursMap={weeksHoursMap}
                                storeState={storeState}/>
              </motion.div>
              <motion.div key="details" className={"absolute top-0 right-0 bottom-0 left-0 overflow-auto"}
                          initial={{x: "100%"}}
                          animate={{x: !isOnList ? 0 : "100%"}}
                          exit={{x: "100%"}}
                          transition={{type: "tween", ease: "easeInOut", duration: 0.2}}>
                <Button onClick={goBackToList} aria-label={"Back"} className={"my-2 ms-1 absolute top-0 left-0"}
                        variant={"ghost"} size={"sm"} type={"button"}>
                  <ArrowLeftIcon size={24} className={"text-gray-600"}/>
                </Button>
                {selectedPerson &&
                        <TeamMemberSelectButton onClick={onSelectTeamMember}
                                                person={selectedPerson}
                                                className="absolute top-0 right-0 my-2 me-2"
                                                reasons={selectedPersonScore ? selectedPersonScore.reasons : []}
                                                score={selectedPersonScore ? selectedPersonScore.score : 0}
                        />}
                {selectedPerson && (
                        <PersonDetailsPanelContent schedule={schedule} selectedShiftId={selectedShiftId}
                                                   storeAreas={storeAreas} timezone={timezone}
                                                   person={selectedPerson}/>
                )}
              </motion.div>
            </PopoverContent>
          </Popover>
  );
}
