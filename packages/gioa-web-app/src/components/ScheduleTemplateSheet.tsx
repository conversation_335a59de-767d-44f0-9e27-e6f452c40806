import React, {useEffect, useState} from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON>D<PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>er,
  Sheet<PERSON>it<PERSON>
} from "@/src/components/ui/sheet.tsx";
import {Button} from "@/src/components/ui/button.tsx";
import {useForm} from "@tanstack/react-form";
import {zodValidator} from "@tanstack/zod-form-adapter";
import {z} from "zod";
import {FormControl} from "@/src/components/form/FormControl.tsx";
import {Label} from "@/src/components/ui/label.tsx";
import {FormInput} from "@/src/components/form/FormInput.tsx";
import {FieldInfo} from "@/src/components/form/FieldInfo.tsx";
import {Text} from "@/src/components/Text.tsx";
import {api} from "@/src/api.ts";
import {find, includes, map} from "lodash";
import {cn} from "@/src/util.ts";
import {getMetrics} from "../../../api/src/scheduling/metrics/metrics.util.ts";
import {DraftSchedule} from "../../../api/src/scheduleSchemas.ts";
import {toast} from "sonner";
import {EditScheduleTemplateDialog} from "@/src/components/EditScheduleTemplateDialog.tsx";
import {useDisclosure} from "@/src/hooks/useDisclosure.tsx";
import {Badge} from "@/src/components/ui/badge.tsx";
import {daysOfWeek} from './AvailabilityWeek.tsx';
import {Spinner} from "@/src/components/Spinner.tsx";
import {ErrorAlert} from "@/src/components/ErrorAlert.tsx";
import {ApplyScheduleTemplateDialog} from "@/src/components/ApplyScheduleTemplateDialog.tsx";
import {payRates} from "../../../api/src/scheduling/metrics/payRates/payRates.ts";

export interface ScheduleTemplateSheetProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  storeId: string;
  scheduleId: string;
  dayCreatedFromId?: string;
  dayOfWeek: number;
  onApplyTemplate: (template: DraftSchedule, areaIds: string[], shouldFilterAreas: boolean) => void;
}

export const ScheduleTemplateSheet: React.FC<ScheduleTemplateSheetProps> = ({
                                                                              isOpen,
                                                                              onOpenChange,
                                                                              storeId, scheduleId,
                                                                              dayOfWeek,
                                                                              dayCreatedFromId,
                                                                              onApplyTemplate
                                                                            }) => {

  const upsertTemplate = api.user.upsertScheduleTemplate.useMutation();
  const deleteTemplate = api.user.deleteScheduleTemplate.useMutation();

  const form = useForm({
    defaultValues: {
      title: '',
    },
    onSubmit: async ({value}) => {
      if (find(templates, t => t.title?.trim() === value.title.trim())) {
        const shouldOverwrite = confirm(`Are you sure you want to overwrite the "${value.title}" template with your current schedule?`);
        if (!shouldOverwrite) return;
      }

      upsertTemplate.mutate({
        scheduleId: scheduleId,
        storeId: storeId,
        title: value.title,
        dayOfWeek: dayOfWeek
      }, {
        onSuccess: () => {
          toast.success(`Template "${value.title}" was saved.`);
          form.reset();
          apiUtil.user.getScheduleTemplates.invalidate();
        }
      });
    },
    validatorAdapter: zodValidator()
  })

  const templatesQuery = api.user.getScheduleTemplates.useQuery({
    storeId
  }, {
    staleTime: 1000 * 60 * 60 // 1 hour
  });
  const templates = templatesQuery.data ?? [];

  const apiUtil = api.useUtils();

  const [selectedTemplateId, setSelectedTemplateId] = useState<string>();
  const selectedTemplate = find(templates, t => t.id === selectedTemplateId);

  const tryApply = () => {
    if (!selectedTemplate) return;

    applyModal.onOpen();
  }

  const editDialog = useDisclosure();

  const onDeleteTemplate = () => {
    const shouldDelete = confirm("Are you sure you want to delete this template?");
    if (shouldDelete) {
      deleteTemplate.mutate({
        id: selectedTemplateId!
      }, {
        onSuccess: () => {
          toast.success(`Template "${selectedTemplate?.title}" was deleted.`);
          apiUtil.user.getScheduleTemplates.invalidate();
        }
      });
    }
  }

  useEffect(() => {
    if (!isOpen) {
      setSelectedTemplateId(undefined);
    }
  }, [isOpen]);

  const dayOfWeekObj = find(daysOfWeek, d => d.dayOfWeek === dayOfWeek);

  const applyModal = useDisclosure();

  const handleSelectTemplate = (template: DraftSchedule) => {
    setSelectedTemplateId(template.id);
    if (template.title) {
      form.setFieldValue("title", template.title);
    }
  }

  const handleApply = (template: DraftSchedule, areaIds: string[], shouldFilterAreas: boolean) => {
    if (!selectedTemplate) return;
    onApplyTemplate(template, areaIds, shouldFilterAreas);
    onOpenChange(false);
    applyModal.onClose();
  }

  return (<Sheet modal={false} open={isOpen}
                 onOpenChange={onOpenChange}>
      <SheetContent side="right"
                    className="px-0">
        <fieldset disabled={upsertTemplate.isPending || deleteTemplate.isPending}
                  className="flex flex-col gap-4 max-h-full overflow-auto px-6">
          <SheetHeader>
            <SheetTitle>Templates</SheetTitle>
            <SheetDescription>
              Create a template from this day or apply a saved template.
            </SheetDescription>
          </SheetHeader>

          <form onSubmit={e => {
            e.preventDefault();
            e.stopPropagation();
            form.handleSubmit();
          }}>
            <form.Field name={"title"}
                        validators={{
                          onSubmit: z.string().min(1, "Title is required")
                        }}
                        children={(field) => {
                          return <FormControl className={"flex flex-col mb-0"}>
                            <Label htmlFor={field.name} className={"mb-2"}>Create a template from this day</Label>
                            <div className={"flex gap-1"}>
                              <FormInput field={field}
                                         placeholder="Save as..."/>
                              <Button type={"submit"} isLoading={upsertTemplate.isPending}
                                      variant={"secondary"}>
                                {includes(map(templates, t => t.title), field.state.value) ? "Overwrite" : "Save"}
                              </Button>
                            </div>
                            <FieldInfo field={field}/>
                          </FormControl>;
                        }}/>

          </form>

          <div className={"space-y-2"}>
            <p className={"font-medium"}>Apply a saved template to {dayOfWeekObj?.name}</p>
            <Text muted size={"sm"}>
              Choose a saved template to apply. This will overwrite the current schedule for {dayOfWeekObj?.name}.
            </Text>

            {templatesQuery.isLoading ? <Spinner size={"lg"}/> : null}
            {templatesQuery.isError ? <ErrorAlert error={templatesQuery.error}/> : null}

            {map(templates, template => {
              const isSelected = selectedTemplateId === template.id;
              const metrics = getMetrics({
                schedule: template,
                dayOfWeek: 1,
                countOpenShiftsTowardsLabor: true,
                payRates: payRates(),
                averagePayRate: undefined,
              });

              return <div key={template.id}>
                <button onClick={() => handleSelectTemplate(template)}
                        className={cn("py-3 px-4 bg-gray-50 rounded-lg text-left border hover:bg-gray-100 w-full",
                          {
                            "bg-gray-700 text-white border-gray-900 hover:bg-gray-700": isSelected
                          })}>
                  <div className={"flex justify-between gap-2 items-center"}>
                    {template.title}
                    {template.id === dayCreatedFromId ?
                      <Badge colorScheme={"success"} className={"text-gray-800"}>
                        Currently in use
                      </Badge> : null}
                  </div>
                  <div className={"flex flex-wrap items-center gap-1"}>
                    <div className={"text-sm opacity-80"}>
                      {metrics.totalLaborHours.toFixed(0)} labor hours
                    </div>
                    •
                    <div className={"text-sm opacity-80"}>
                      {metrics.shiftCount} shifts
                    </div>
                  </div>
                </button>
              </div>
            })}
          </div>

          <SheetFooter>
            <Button className={"grow"}
                    onClick={tryApply}
                    disabled={!selectedTemplateId}>
              Apply Template
            </Button>

            <Button variant={"outline"} disabled={!selectedTemplateId} onClick={editDialog.onOpen}>
              Edit
            </Button>

            <Button variant={"outline"} onClick={onDeleteTemplate}
                    isLoading={deleteTemplate.isPending}
                    disabled={!selectedTemplateId}>
              Delete
            </Button>

          </SheetFooter>
        </fieldset>

        {selectedTemplate ? <EditScheduleTemplateDialog dayOfWeek={1}
                                                        {...editDialog} template={selectedTemplate}/> : null}

        {selectedTemplate ?
          <ApplyScheduleTemplateDialog isOpen={applyModal.isOpen} onOpenChange={applyModal.setOpen}
                                       dayOfWeek={dayOfWeek}
                                       onApplyTemplate={handleApply}
                                       selectedTemplate={selectedTemplate}/> : null}
      </SheetContent>
    </Sheet>
  );
}

