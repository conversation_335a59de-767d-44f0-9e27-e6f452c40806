import React, { useMemo } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/src/components/ui/dialog";
import { Text } from "@/src/components/Text";
import { ChevronRight } from "lucide-react";
import { DateTime } from "luxon";
import { pick } from "lodash";
import { useNavigate } from "@tanstack/react-router";
import { PersonAvatar } from "@/src/components/PersonAvatar.tsx";
import { compareNoteVersions, formatDiff, TRACKED_FIELDS } from "../../../api/src/NoteHistory.util.ts";

interface NoteHistoryModalProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  note: any;
  personId: string;
  storeId: string;
  noteId: string;
  timezone: string;
}

export function NoteHistoryModal({
  isOpen,
  onOpenChange,
  note,
  personId,
  storeId,
  noteId,
  timezone,
}: NoteHistoryModalProps) {
  const navigate = useNavigate();

  const historyEntries = useMemo(() => {
    if (!note) return [];

    const entries = [];

    // Add current version (most recent)
    entries.push({
      ...pick(note, TRACKED_FIELDS),
      version: note.version,
      createdAt: note.createdAt,
      createdByPerson: note.updatedBy ?? note.createdBy,
      effectiveDateEnd: new Date(),
      effectiveDateStart: note.updatedAt ?? note.createdAt,
    });

    // Add history entries if they exist
    if (note.history) {
      entries.push(...note.history);
    }

    return entries.sort((a, b) => b.version - a.version); // Sort by version descending
  }, [note]);

  const formatDateTime = (date: Date) => {
    return DateTime.fromJSDate(date, { zone: timezone }).toFormat("MMM d, yyyy • h:mm a");
  };

  const viewNoteVersion = (version: number) => {
    // Update the URL with the version number
    const businessId = window.location.pathname.split("/")[1];
    navigate({
      to: `/${businessId}/${storeId}/team/directory/${personId}/notes/note/${noteId}`,
      search: { versionNumber: version },
    });

    onOpenChange(false);
  };

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent size="2xl">
        <DialogHeader>
          <DialogTitle>Version History</DialogTitle>
        </DialogHeader>
        <div className="space-y-4 max-h-[70vh] overflow-y-auto p-1">
          {historyEntries.map((item, index) => {
            const prevItem = historyEntries[index + 1];

            let changes = null;
            if (prevItem) {
              changes = compareNoteVersions(prevItem, item);
            }

            const diff = changes ? formatDiff(changes) : "";
            const bulletPoints = diff
              .split("\n")
              .slice(1)
              .filter((line) => line.trim());

            return (
              <div
                key={item.version}
                className="flex flex-row py-3 px-4 border border-gray-300 rounded-lg cursor-pointer hover:bg-gray-50"
                onClick={() => viewNoteVersion(item.version)}
              >
                <div className="flex-1 space-y-3">
                  <Text semibold>
                    Version {item.version} • {formatDateTime(item.effectiveDateStart)}
                  </Text>
                  <div className="ml-2">
                    {index === historyEntries.length - 1 ? (
                      <Text size="sm" muted className="mb-1">
                        Note created
                      </Text>
                    ) : (
                      <div className={"flex flex-col"}>
                        {bulletPoints.map((point, idx) => (
                          <Text key={idx} size="sm" muted className="mb-1">
                            {point}
                          </Text>
                        ))}
                      </div>
                    )}
                  </div>
                  {item.createdByPerson && (
                    <div className="flex flex-row items-center gap-2">
                      <PersonAvatar person={item.createdByPerson} />
                      <Text>
                        {item.createdByPerson.firstName} {item.createdByPerson.lastName}
                      </Text>
                    </div>
                  )}
                </div>
                <div className="flex items-center">
                  <ChevronRight className="h-5 w-5 text-gray-400" />
                </div>
              </div>
            );
          })}
        </div>
      </DialogContent>
    </Dialog>
  );
}
