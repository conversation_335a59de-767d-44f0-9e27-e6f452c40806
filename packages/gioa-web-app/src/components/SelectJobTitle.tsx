import React, { useEffect, useState } from "react";
import { find, map, reduce, reject } from "lodash";
import { api } from "@/src/api.ts";
import { Dialog, DialogContent, DialogFooter, DialogHeader, DialogTitle } from "@/src/components/ui/dialog.tsx";
import { But<PERSON> } from "@/src/components/ui/button.tsx";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/src/components/ui/tabs.tsx";
import { ScrollArea } from "@/src/components/ui/scroll-area.tsx";
import { Text } from "@/src/components/Text.tsx";
import { CheckedIds, PolicyEditor } from "@/src/components/PolicyEditor.tsx";
import { useForm } from "@tanstack/react-form";

interface SelectJobTitleProps {
  initialJobId?: string;
  personName?: string;
  skipPermissions?: boolean;
  onSelect: (jobId: string, permissionPackages: CheckedIds) => void;
  onCancel: () => void;
  isPending?: boolean;
}

export const SelectJobTitle: React.FC<SelectJobTitleProps> = ({
  initialJobId,
  personName,
  skipPermissions = false,
  onSelect,
  onCancel,
  isPending,
}) => {
  const [activeTab, setActiveTab] = useState<string>("select-job");
  const [selectedJobId, setSelectedJobId] = useState<string | undefined>(initialJobId);

  const { data: business } = api.user.getBusiness.useQuery();
  // TODO: Implement adding new jobs!
  const canCreateNewJobs = false; //user ? hasPermissionPackage(user, "businessAdmin") : false;

  const jobs = business?.jobs || [];
  const jobOptions = React.useMemo(() => {
    const availableOptions = reject(jobs, (j) => j.defaultRoleId === "BUSINESS");
    return map(availableOptions, (option) => ({
      label: option.title,
      value: option.id,
    }));
  }, [jobs]);

  const selectedJob = React.useMemo(() => {
    return find(jobs, (j) => j.id === selectedJobId);
  }, [jobs, selectedJobId]);

  const checkedPackages = React.useMemo(() => {
    if (!selectedJob) return {} as CheckedIds;

    // Handle different permission policy structures
    if (selectedJob.permissionPolicy && Array.isArray(selectedJob.permissionPolicy)) {
      return reduce(
        selectedJob.permissionPolicy,
        (acc, v) => {
          acc[v.id] = true;
          return acc;
        },
        {} as CheckedIds,
      );
    } else if (selectedJob.permissionPolicy && typeof selectedJob.permissionPolicy === "object") {
      // Handle case where permissionPolicy is an object with statements array
      const statements = (selectedJob.permissionPolicy as any).statements || [];
      return reduce(
        statements,
        (acc, v) => {
          if (v.packageId) {
            acc[v.packageId] = true;
          }
          return acc;
        },
        {} as CheckedIds,
      );
    }

    return {} as CheckedIds;
  }, [selectedJob]);

  const form = useForm({
    defaultValues: {
      jobId: initialJobId,
      permissionPackages: checkedPackages,
    },
    onSubmit: async (event) => {
      const { jobId, permissionPackages } = event.value;
      onSelect(jobId!, permissionPackages);
    },
  });

  // Update form values when selectedJobId or checkedPackages change
  useEffect(() => {
    form.setFieldValue("jobId", selectedJobId);
    form.setFieldValue("permissionPackages", checkedPackages);
  }, [selectedJobId, checkedPackages]);

  const handleSelectJob = (jobId: string) => {
    setSelectedJobId(jobId);

    if (skipPermissions) {
      // If skipping permissions, submit the form immediately with the job's default permissions
      const job = find(jobs, (j) => j.id === jobId);
      let newPermissions = {} as CheckedIds;

      // Extract permissions from the job
      if (job && job.permissionPolicy) {
        if (Array.isArray(job.permissionPolicy)) {
          newPermissions = reduce(
            job.permissionPolicy,
            (acc, v) => {
              acc[v.id] = true;
              return acc;
            },
            {} as CheckedIds,
          );
        } else if (typeof job.permissionPolicy === "object") {
          const statements = (job.permissionPolicy as any).statements || [];
          newPermissions = reduce(
            statements,
            (acc, v) => {
              if (v.packageId) {
                acc[v.packageId] = true;
              }
              return acc;
            },
            {} as CheckedIds,
          );
        }
      }

      form.setFieldValue("jobId", jobId);
      form.setFieldValue("permissionPackages", newPermissions);
      form.handleSubmit();
    } else {
      setActiveTab("permissions");
    }
  };

  const onAddNewJob = () => {
    // TODO Implement adding a new job
  };

  return (
    <Dialog open={true} onOpenChange={() => onCancel()}>
      <DialogContent className="sm:max-w-md md:max-w-2xl">
        <DialogHeader>
          <DialogTitle>Select Job Title</DialogTitle>
        </DialogHeader>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="select-job">Select Job</TabsTrigger>
            <TabsTrigger value="permissions" disabled={!selectedJobId || skipPermissions}>
              Permissions
            </TabsTrigger>
          </TabsList>

          <TabsContent value="select-job" className="mt-4">
            <ScrollArea className="h-[60vh] pr-4">
              <div className="space-y-2">
                {jobOptions.map((job) => (
                  <Button
                    key={job.value}
                    variant={job.value === selectedJobId ? "default" : "outline"}
                    className="w-full justify-start"
                    onClick={() => handleSelectJob(job.value)}
                  >
                    {job.label}
                  </Button>
                ))}
              </div>
            </ScrollArea>
          </TabsContent>

          <TabsContent value="permissions" className="mt-4">
            {selectedJob && (
              <ScrollArea className="h-[60vh] pr-4">
                <Text bold className="mb-2">
                  {selectedJob.title}
                </Text>
                {personName && (
                  <div className="mb-4">
                    <Text>Customize job permissions for {personName}</Text>
                  </div>
                )}
                <form.Field
                  name="permissionPackages"
                  children={(field) => (
                    <div>
                      <PolicyEditor value={field.state.value} onChange={field.handleChange} />
                    </div>
                  )}
                />
              </ScrollArea>
            )}
          </TabsContent>
        </Tabs>

        <DialogFooter className="flex justify-between">
          {activeTab === "select-job" ? (
            <>
              {canCreateNewJobs && (
                <Button variant="outline" onClick={onAddNewJob}>
                  Add New
                </Button>
              )}
              <div className="flex gap-2">
                <Button variant="outline" onClick={onCancel}>
                  Cancel
                </Button>
                {selectedJobId && skipPermissions && (
                  <Button onClick={form.handleSubmit} isLoading={isPending} disabled={isPending}>
                    Submit
                  </Button>
                )}
                {selectedJobId && !skipPermissions && <Button onClick={() => setActiveTab("permissions")}>Next</Button>}
              </div>
            </>
          ) : (
            <div className="flex flex-row w-full justify-end gap-2">
              <Button variant="outline" onClick={() => setActiveTab("select-job")}>
                Back
              </Button>
              <Button onClick={form.handleSubmit} disabled={isPending} isLoading={isPending}>
                Submit
              </Button>
            </div>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default SelectJobTitle;
