import React from 'react';

interface StarSvgProps {
  fill?: string;
  width?: number;
}

export function StarSvg({ fill = '#E4E1E1', width = 44 }: StarSvgProps) {
  const defaultWidth = 44;
  const defaultHeight = 40;
  const aspectRatio = defaultWidth / defaultHeight;

  const height = width / aspectRatio;

  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 44 40"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M9.92927 39.848C8.86777 40.376 7.66327 39.4507 7.87777 38.2694L10.1603 25.6565L0.472046 16.7074C-0.432702 15.8701 0.0375468 14.3395 1.25029 14.1742L14.7198 12.3183L20.7257 0.779974C21.2675 -0.259991 22.7332 -0.259991 23.275 0.779974L29.281 12.3183L42.7504 14.1742C43.9632 14.3395 44.4334 15.8701 43.5259 16.7074L33.8404 25.6565L36.1229 38.2694C36.3374 39.4507 35.1329 40.376 34.0714 39.848L21.9962 33.8322L9.92927 39.848Z"
        fill={fill}
      />
    </svg>
  );
}
