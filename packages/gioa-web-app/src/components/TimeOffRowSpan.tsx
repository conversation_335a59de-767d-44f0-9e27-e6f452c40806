import React from 'react';
import {edgesGradient} from "@/src/components/ShiftRowAvailabilityOverlay.tsx";
import {RangeOverlayLabel} from "@/src/components/RangeOverlayLabel.tsx";
import {DailyTimeRange} from "../../../api/src/timeSchemas.ts";
import {SchedulePersonTimeOffDto} from "../../../api/src/schedulePersonDto.ts";
import {hexToRgb} from "@/src/color.util.ts";
import {cn} from "@/src/util.ts";

export interface TimeOffRowSpanProps {
  rowIndex: number;
  isAllDay: boolean;
  timeOff: SchedulePersonTimeOffDto;
  range: DailyTimeRange;
  onClick: (timeOff: SchedulePersonTimeOffDto) => void;
  isSelected: boolean;
}

export const TimeOffRowSpan: React.FC<TimeOffRowSpanProps> = React.memo((props) => {
  const pendingColor = "#ffc100";
  const approvedColor = "#0080ff";
  const timeOffColor = props.timeOff.status === "pending"
    ? pendingColor
    : approvedColor;

  return (
    <div className={"h-full flex flex-row py-0.5"}>
      <button title="View time off"
              onClick={() => props.onClick(props.timeOff)}
              style={{
                background: edgesGradient({
                  show: true,
                  middleOpacity: 0.8,
                  edgeOpacity: 0.9,
                  rgb: hexToRgb(timeOffColor)
                })
              }} className={cn(`rounded-md h-full grow hover:!bg-white`, {
        "outline outline-4 outline-yellow-500": props.isSelected
      })}>
        <RangeOverlayLabel showAvail={true} className={"top-1/2 transform -translate-y-1/2"}
                           label={props.timeOff.status === "pending" ? "Pending time off" : "Time off"}
                           labelClassName={"sticky right-1"}
                           isAllDay={props.isAllDay}
                           range={props.range}/>
      </button>
    </div>
  );
})
