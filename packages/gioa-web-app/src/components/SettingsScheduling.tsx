import React from 'react';
import {Text} from "@/src/components/Text.tsx";
import {Button} from "@/src/components/ui/button.tsx";
import {CircleHelp} from "lucide-react";
import {Heading} from "@/src/components/Heading.tsx";
import {useForm} from "@tanstack/react-form";
import {api} from "@/src/api.ts";
import {zodValidator} from '@tanstack/zod-form-adapter'
import {FormInput} from "@/src/components/form/FormInput.tsx";
import {ErrorAlert} from "@/src/components/ErrorAlert.tsx";
import {Label} from "@/src/components/ui/label.tsx";
import {toast} from "sonner";
import {Popover, PopoverContent, PopoverTrigger} from "./ui/popover.tsx"

import {incrementDurationMinutes} from "../../../api/src/scheduleBuilder.util.ts";

export interface SettingsSchedulingProps {
  storeId: string;
}

export const SettingsScheduling: React.FC<SettingsSchedulingProps> = ({storeId}) => {
  const updateStoreHours = api.user.updateStoreHours.useMutation({
    onSuccess: () => {
      apiUtil.user.getStoreAdmin.invalidate({storeId});
      toast.success("Store hours updated", {
        position: "top-center",
      });
    }
  });

  const [store] = api.user.getStoreAdmin.useSuspenseQuery({storeId: storeId!}, {
    staleTime: 1000 * 60 * 60 // 1 hour
  });

  const apiUtil = api.useUtils();

  const form = useForm({
    defaultValues: {
      start: store.storeHours?.start ?? "06:00",
      end: store.storeHours?.end ?? "21:00",
    },
    validatorAdapter: zodValidator(),
    onSubmit: ({value}) => {
      updateStoreHours.mutate({
        storeId: storeId!,
        hours: value
      })
    }
  })

  return (
    <form onSubmit={(e) => {
      e.preventDefault()
      e.stopPropagation()
      form.handleSubmit()
    }}>
      <Heading level={1}>Scheduling</Heading>
      <div className="my-8">
        <div className={"flex flex-col"}>
          <Text semibold>Schedule Display</Text>
          <Text muted className="mb-4">When do your team members' days begin and end?</Text>
        </div>
      </div>
      {updateStoreHours.isError && <ErrorAlert error={updateStoreHours.error}/>}
      <div className="my-8 flex flex-col lg:flex-row gap-4"> {/* Change this line */}
        <form.Field
          name="start"

          children={field => (
            <div> {/* Adjust this */}
              <div className="flex flex-row gap-3 items-center mb-2">
                <Label>Start Time</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <CircleHelp size={18} className="cursor-help"/>
                  </PopoverTrigger>
                  <PopoverContent className="p-4 bg-white border rounded shadow-lg z-50">
                    <div className="flex flex-col gap-3">
                      <Text size={"sm"} bold>Start Time</Text>
                      <Text size={"sm"}>Choose the earliest time a team member can start a shift.</Text>
                    </div>
                  </PopoverContent>
                </Popover>
              </div>
              <div className={"w-80"}>
                <FormInput
                  field={field}
                  type="time"
                  step={incrementDurationMinutes * 60}
                  placeholder="Choose start time..."
                />
              </div>
            </div>
          )}
        />
        <form.Field
          name="end"
          children={field => (
            <div> {/* Adjust this */}
              <div className="flex flex-row gap-3 items-center mb-2">
                <Label>End Time</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <CircleHelp size={18} className="cursor-help"/>
                  </PopoverTrigger>
                  <PopoverContent className="p-4 bg-white border rounded shadow-lg z-50">
                    <div className="flex flex-col gap-3">
                      <Text size={"sm"} bold>End Time</Text>
                      <Text size={"sm"}>Choose the latest time a team member can finish a shift.</Text>
                    </div>
                  </PopoverContent>
                </Popover>
              </div>
              <div className={"w-80"}>
                <FormInput
                  field={field}
                  type="time"
                  step={incrementDurationMinutes * 60}
                  placeholder="Choose end time..."
                />
              </div>
            </div>
          )}
        />
      </div>


      <div className="mt-4">
        <Button type={"submit"} isLoading={updateStoreHours.isPending}>Save</Button>
      </div>
    </form>
  );
}
