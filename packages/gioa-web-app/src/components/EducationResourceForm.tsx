import React, {useEffect} from 'react';
import {useForm} from "@tanstack/react-form";
import {zodValidator} from '@tanstack/zod-form-adapter';
import {z} from "zod";
import {<PERSON><PERSON>} from "@/src/components/ui/button.tsx";
import {Textarea} from "@/src/components/ui/textarea.tsx";
import {Label} from "@/src/components/ui/label.tsx";
import {FormControl} from "@/src/components/form/FormControl.tsx";
import {FormInput} from "@/src/components/form/FormInput.tsx";
import {FieldInfo} from "@/src/components/form/FieldInfo.tsx";
import {EducationResourceDto} from "../../../api/src/routers/gioa-web-app/admin.schemas.ts";

interface EducationResourceFormProps {
  onSubmit: (resource: EducationResourceDto) => void;
  onCancel: () => void;
  initialData?: EducationResourceDto;
  isEditing?: boolean;
  sectionId?: string;
  defaultOrder?: number;
}

const resourceFormSchema = z.object({
  title: z.string().min(1, "Title is required"),
  description: z.string().optional(),
  videoUrl: z.string().optional(),
  playbackId: z.string().optional(),
  order: z.number().min(1, "Display Order is required"),
});

export const EducationResourceForm: React.FC<EducationResourceFormProps> = ({
  onSubmit,
  onCancel,
  initialData,
  isEditing = false,
  sectionId = '',
  defaultOrder = 1,
}) => {
  const form = useForm({
    defaultValues: {
      title: initialData?.title || '',
      description: initialData?.description || '',
      videoUrl: initialData?.videoUrl || '',
      playbackId: initialData?.playbackId || '',
      order: isEditing
        ? initialData?.order ?? defaultOrder
        : (initialData?.order ?? defaultOrder),
    },
    onSubmit: async ({value}) => {
      const resource: EducationResourceDto = {
        id: initialData?.id || '',
        sectionId: sectionId,
        title: value.title,
        description: value.description,
        videoUrl: value.videoUrl,
        playbackId: value.playbackId,
        order: value.order,
      };
      onSubmit(resource);
    },
    validatorAdapter: zodValidator()
  });

  useEffect(() => {
    if (isEditing && initialData) {
      form.setFieldValue('title', initialData.title || '');
      form.setFieldValue('description', initialData.description || '');
      form.setFieldValue('videoUrl', initialData.videoUrl || '');
      form.setFieldValue('playbackId', initialData.playbackId || '');
      form.setFieldValue('order', initialData.order ?? defaultOrder);
    }
  }, [initialData, isEditing, form, defaultOrder]);

  const handleCancel = () => {
    if (isEditing && initialData) {
      form.setFieldValue('title', initialData.title || '');
      form.setFieldValue('description', initialData.description || '');
      form.setFieldValue('videoUrl', initialData.videoUrl || '');
      form.setFieldValue('playbackId', initialData.playbackId || '');
    }
    onCancel();
  };

  return (
    <div className={`p-4 ${isEditing ? 'pt-2' : ''}`}>
      <form onSubmit={e => {
        e.preventDefault();
        e.stopPropagation();
        form.handleSubmit();
      }}>
        <div className="space-y-4">
          <form.Field
            name="title"
            validators={{
              onSubmit: resourceFormSchema.shape.title
            }}
            children={(field) => (
              <FormControl>
                <Label htmlFor={field.name}>Title</Label>
                <FormInput
                  field={field}
                  placeholder="Resource title"
                />
                <FieldInfo field={field}/>
              </FormControl>
            )}
          />

          <form.Field
            name="order"
            validators={{
              onSubmit: resourceFormSchema.shape.order
            }}
            children={(field) => (
              <FormControl>
                <Label htmlFor={field.name}>Display Order</Label>
                <FormInput
                  field={field}
                  type="number"
                  min={1}
                  placeholder="Display order"
                  onChange={e => field.handleChange(Number(e.target.value))}
                />
                <FieldInfo field={field}/>
              </FormControl>
            )}
          />

          <form.Field
            name="description"
            validators={{
              onSubmit: resourceFormSchema.shape.description
            }}
            children={(field) => (
              <FormControl>
                <Label htmlFor={field.name}>Description</Label>
                <Textarea
                  value={field.state.value || ''}
                  onChange={(e) => field.handleChange(e.target.value)}
                  placeholder="Resource description"
                  rows={2}
                />
                <FieldInfo field={field}/>
              </FormControl>
            )}
          />

          <form.Field
            name="playbackId"
            validators={{
              onSubmit: resourceFormSchema.shape.playbackId
            }}
            children={(field) => (
              <FormControl>
                <Label htmlFor={field.name}>Mux Playback ID</Label>
                <FormInput
                  field={field}
                  placeholder="Playback ID"
                />
                <FieldInfo field={field}/>
              </FormControl>
            )}
          />

          <form.Field
            name="videoUrl"
            validators={{
              onSubmit: resourceFormSchema.shape.videoUrl
            }}
            children={(field) => (
              <FormControl>
                <Label htmlFor={field.name}>Video URL</Label>
                <FormInput
                  field={field}
                  placeholder="Video URL"
                />
                <FieldInfo field={field}/>
              </FormControl>
            )}
          />
        </div>

        <div className="mt-4 flex gap-2">
          <Button type="button" onClick={() => form.handleSubmit()}>
            {isEditing ? 'Update Resource' : 'Add Resource'}
          </Button>

          <Button variant="outline" type="button" onClick={handleCancel}>
            Cancel
          </Button>
        </div>
      </form>
    </div>
  );
};
