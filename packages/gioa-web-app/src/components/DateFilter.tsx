import React, {useState} from "react";
import {DateTime} from "luxon";
import {IsoCompleteDate} from "../../../api/src/timeSchemas.ts";
import {TimeOffTimeFrameFilter} from "../../../api/src/personTimeOff.schemas.ts";
import {Select, SelectContent, SelectItem, SelectTrigger, SelectValue} from "./ui/select.tsx";
import {Button, buttonVariants} from "./ui/button.tsx";
import {ArrowLeftIcon, ArrowRightIcon} from "lucide-react";
import {IsoDayPicker} from "./IsoDayPicker.tsx";
import {WeekPicker} from "./WeekPicker.tsx";
import {YearMonthPicker} from "./MonthPicker.tsx";
import {getDateFromIsoCompleteDate} from "../../../api/src/date.util.ts";
import {generateIsoWeekDates, takeFromGenerator} from "../../../api/src/scheduleBuilder.util.ts";
import {cn} from "@/src/util.ts";

interface DayFilterProps {
  selectedWeek: IsoCompleteDate;
  timezone: string | null;
  setSelectedWeek: (week: IsoCompleteDate) => void;
  today: DateTime;
}

const DayFilter: React.FC<DayFilterProps> = ({
  selectedWeek,
  timezone,
  setSelectedWeek,
  today
}) => {
  const thisDay = getDateFromIsoCompleteDate(selectedWeek, timezone);
  const nextDay = thisDay.plus({days: 1});
  const prevDay = thisDay.minus({days: 1});

  return (
    <div className="flex flex-row flex-wrap items-center justify-between gap-3">
      <IsoDayPicker value={selectedWeek} onChange={setSelectedWeek} today={today}/>
      <div className="inline-flex rounded-md" role="group">
        <Button
          title={"Go to the previous day"}
          variant={"outline"}
          className={"rounded-r-none relative hover:z-10"}
          onClick={() => setSelectedWeek({
            day: prevDay.weekday,
            week: prevDay.weekNumber,
            year: prevDay.weekYear
          })}
        >
          <ArrowLeftIcon size={16} className={"mr-1"}/>
          Prev
        </Button>
        <Button
          title={"Go to the next day"}
          variant={"outline"}
          className={"rounded-l-none border-l-0 relative hover:z-10"}
          onClick={() => setSelectedWeek({
            day: nextDay.weekday,
            week: nextDay.weekNumber,
            year: nextDay.weekYear
          })}
        >
          Next
          <ArrowRightIcon size={16} className={"ml-1"}/>
        </Button>
      </div>
    </div>
  );
};

interface WeekFilterProps {
  selectedWeek: IsoCompleteDate;
  timezone: string | null;
  setSelectedWeek: (week: IsoCompleteDate) => void;
  today: DateTime;
}

const WeekFilter: React.FC<WeekFilterProps> = ({
  selectedWeek,
  timezone,
  setSelectedWeek,
  today
}) => {
  const [, nextWeek] = takeFromGenerator(generateIsoWeekDates(selectedWeek, timezone), 2);
  const [, prevWeek] = takeFromGenerator(generateIsoWeekDates(selectedWeek, timezone, (d: DateTime) => d.minus({weeks: 1})), 2);

  return (
    <div className="flex flex-row flex-wrap items-center justify-between gap-3">
      <WeekPicker
        value={selectedWeek}
        today={today}
        timezone={timezone}
        onChange={setSelectedWeek}
      />
      <div className="inline-flex rounded-md" role="group">
        <button
          title={"Go to the previous week"}
          className={cn(buttonVariants({variant: "outline"}), "rounded-r-none relative hover:z-10")}
          onClick={() => setSelectedWeek({...prevWeek})}
        >
          <ArrowLeftIcon size={16} className={"mr-1"}/>
          Prev
        </button>
        <button
          title={"Go to the next week"}
          className={cn(buttonVariants({variant: "outline"}), "rounded-l-none border-l-0 relative hover:z-10")}
          onClick={() => setSelectedWeek({...nextWeek})}
        >
          Next
          <ArrowRightIcon size={16} className={"ml-1"}/>
        </button>
      </div>
    </div>
  );
};

interface MonthFilterProps {
  selectedWeek: IsoCompleteDate;
  timezone: string | null;
  setSelectedMonth: (week: IsoCompleteDate) => void;
}

const MonthFilter: React.FC<MonthFilterProps> = ({
  selectedWeek,
  timezone,
  setSelectedMonth
}) => {
  const thisMonth = getDateFromIsoCompleteDate(selectedWeek, timezone);
  const nextMonth = thisMonth.plus({months: 1});
  const prevMonth = thisMonth.minus({months: 1});

  return (
    <div className="flex flex-row flex-wrap items-center justify-between gap-3">
      <YearMonthPicker value={selectedWeek} onChange={setSelectedMonth}/>
      <div className="inline-flex rounded-md" role="group">
        <Button
          title={"Go to the previous month"}
          variant={"outline"}
          className={"rounded-r-none relative hover:z-10"}
          onClick={() => setSelectedMonth({
            day: prevMonth.weekday,
            week: prevMonth.weekNumber,
            year: prevMonth.weekYear,
            month: prevMonth.month
          })}
        >
          <ArrowLeftIcon size={16} className={"mr-1"}/>
          Prev
        </Button>
        <Button
          title={"Go to the next month"}
          variant={"outline"}
          className={"rounded-l-none border-l-0 relative hover:z-10"}
          onClick={() => setSelectedMonth({
            day: nextMonth.weekday,
            week: nextMonth.weekNumber,
            year: nextMonth.weekYear,
            month: nextMonth.month
          })}
        >
          Next
          <ArrowRightIcon size={16} className={"ml-1"}/>
        </Button>
      </div>
    </div>
  );
};

interface DateFilterProps {
  store: {
    timezone: string | null;
  };
  timeRange: TimeOffTimeFrameFilter;
  selectedDate: IsoCompleteDate;
  onDateChange: (date: IsoCompleteDate, timeRange: TimeOffTimeFrameFilter) => void;
}

export const DateFilter: React.FC<DateFilterProps> = ({
  store,
  timeRange,
  selectedDate,
  onDateChange
}) => {
  const timezone = store.timezone ?? "America/New_York";
  const [today] = useState(DateTime.now().setZone(timezone));

  const setTimeFrameFilter = (val: TimeOffTimeFrameFilter) => {
    onDateChange(selectedDate, val);
  };

  const setSelectedDate = (date: IsoCompleteDate) => {
    onDateChange(date, timeRange);
  };

  const setSelectedWeek = (week: IsoCompleteDate) => {
    onDateChange(week, timeRange);
  };

  return (
    <div className="flex flex-row items-center gap-2 flex-wrap">
      <div>
        <Select
          onValueChange={val => setTimeFrameFilter(val as TimeOffTimeFrameFilter)}
          value={timeRange}
        >
          <SelectTrigger className="min-w-[100px]">
            <SelectValue placeholder="Time Frame"/>
          </SelectTrigger>
          <SelectContent>
            <SelectItem value={"day"}>
              Day
            </SelectItem>
            <SelectItem value={"week"}>
              Week
            </SelectItem>
            <SelectItem value={"month"}>
              Month
            </SelectItem>
          </SelectContent>
        </Select>
      </div>

      {timeRange === "day" && (
        <DayFilter
          selectedWeek={selectedDate}
          timezone={timezone}
          setSelectedWeek={setSelectedDate}
          today={today}
        />
      )}

      {timeRange === "week" && (
        <WeekFilter
          selectedWeek={selectedDate}
          timezone={timezone}
          setSelectedWeek={setSelectedWeek}
          today={today}
        />
      )}

      {timeRange === "month" && (
        <MonthFilter
          selectedWeek={selectedDate}
          timezone={timezone}
          setSelectedMonth={setSelectedDate}
        />
      )}
    </div>
  );
};
