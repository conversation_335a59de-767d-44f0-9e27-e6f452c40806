import React from 'react';
import {cn} from "@/src/util.ts";
import {DailyTimeRange, TimeOfDay} from "../../../api/src/timeSchemas.ts";
import {ScheduleShiftActivity, ShiftSetupActivity} from "@/src/components/ScheduleShiftActivity.tsx";
import {map} from "lodash";
import {getDayPartsCssBackground} from "../../../api/src/scheduleBuilder.util.ts";

export interface SetupActivitiesRowProps {
  rowHeight: number;
  setupPositionStart: TimeOfDay | undefined;
  setupPositionEnd: TimeOfDay | undefined;
  activities: ShiftSetupActivity[];
  incrementWidth: number;
  onOpen: (areaId: string, shiftId: string) => void;
  storeHours: DailyTimeRange;
  selectedShiftId: string | undefined;
}

export const SetupActivitiesRow = React.memo(({
                                                storeHours, setupPositionStart, setupPositionEnd,
                                                rowHeight, activities, incrementWidth, onOpen, selectedShiftId,
                                              }: SetupActivitiesRowProps) => {


  const bgStyle = setupPositionStart && setupPositionEnd ? getDayPartsCssBackground({
    dayParts: [
            {title: "", color: `rgba(0, 36, 147, 0.3)`, range: {start: setupPositionStart, end: setupPositionEnd}}
    ],
    storeHours: storeHours,
    incrementWidth,
  }) : undefined;

  return <div style={{height: rowHeight, background: bgStyle}}
              className={cn("relative w-full px-4")}>
    {/*<div className={"sticky bg-red-400 left-0 inline-block z-20"} style={{width: 120}}>Test</div>*/}
    {map(activities, activity => {
      const isSelected = selectedShiftId === activity.shift.id;

      return <ScheduleShiftActivity rowHeight={rowHeight} key={activity.id} storeHours={storeHours}
                                    onOpen={onOpen} isSelected={isSelected}
                                    activity={activity}
                                    incrementWidth={incrementWidth}/>;
    })}
  </div>;
});
