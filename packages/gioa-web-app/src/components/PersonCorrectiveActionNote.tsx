import React from "react";
import { Text } from "./Text";
import { format } from "date-fns";
import { trim } from "lodash";
import { PersonNoteHeader } from "./PersonNoteHeader";
import { CorrectiveActionStatusBadge } from "./CorrectiveActionStatusBadge";
import { PersonCorrectiveActionNoteDto } from "../../../api/src/schemas.ts";

export interface PersonCorrectiveActionNoteProps {
  note: PersonCorrectiveActionNoteDto;
}

export const PersonCorrectiveActionNote = React.memo((props: PersonCorrectiveActionNoteProps) => {
  const { note } = props;

  return (
    <div className="px-4 py-3 mb-2 bg-white relative shadow-sm border border-gray-200 rounded-xl">
      <PersonNoteHeader note={note} />
      <div className="mt-2">
        <div className="flex flex-row items-center gap-1 mb-2">
          <Text muted className="flex-1 text-sm">
            {format(new Date(note.createdAt), "PP")} by {note.createdBy.firstName} {note.createdBy.lastName}
          </Text>
        </div>
        <Text className="mb-2">{trim(note.note || note.correctiveAction.policiesInAction.join(", "))}</Text>
        <div className="flex flex-row mt-2">
          <CorrectiveActionStatusBadge status={note.correctiveAction.status} />
        </div>
      </div>
    </div>
  );
});
