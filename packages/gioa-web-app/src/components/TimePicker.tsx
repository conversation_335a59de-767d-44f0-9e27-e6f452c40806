import React from "react";
import {Input} from "@/src/components/ui/input";

export interface TimePickerProps {
  value?: string;
  onChange: (value: string) => void;
  placeholder?: string;
  minuteInterval?: number;
  minTime?: string;
  maxTime?: string;
  icon?: React.ReactNode | null;
  className?: string;
}

export const TimePicker: React.FC<TimePickerProps> = ({
  value,
  onChange,
  placeholder = "Select time",
  minuteInterval = 1,
  minTime,
  maxTime,
  className
}) => {
  return (
    <Input
      type="time"
      value={value || ""}
      onChange={(e) => onChange(e.target.value)}
      placeholder={placeholder}
      step={minuteInterval * 60}
      min={minTime}
      max={maxTime}
      className={className}
    />
  );
};

