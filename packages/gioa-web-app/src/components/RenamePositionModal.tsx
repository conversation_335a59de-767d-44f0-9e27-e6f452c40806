import React, {useCallback, useEffect} from 'react';
import {<PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ooter, <PERSON><PERSON>Header, DialogTitle} from "@/src/components/ui/dialog.tsx";
import {Button} from "@/src/components/ui/button.tsx";
import {useForm} from "@tanstack/react-form";
import {FormInput} from "@/src/components/form/FormInput.tsx";
import {Label} from "@/src/components/ui/label.tsx";
import {FieldInfo} from "@/src/components/form/FieldInfo.tsx";

export interface RenamePositionModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (oldTitle: string, newTitle: string) => void;
  currentTitle: string;
  existingTitles: string[];
}

export const RenamePositionModal: React.FC<RenamePositionModalProps> = ({
  isOpen,
  onClose,
  onSave,
  currentTitle,
  existingTitles
}) => {
  const form = useForm({
    defaultValues: {
      title: currentTitle
    },
    onSubmit: async ({value}) => {
      const trimmedTitle = value.title.trim();
      onSave(currentTitle, trimmedTitle);
      form.reset();
      onClose();
    },
  });

  useEffect(() => {
    if (isOpen) {
      form.reset();
      form.setFieldValue('title', currentTitle);
    }
  }, [isOpen, currentTitle]);

  const handleCancel = useCallback(() => {
    form.reset();
    onClose();
  }, [form, onClose]);

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && handleCancel()}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Rename Position</DialogTitle>
        </DialogHeader>

        <div className="px-1 gap-6">
          <form.Field name="title" validators={{
            onSubmit: ({ value }) => {
              const trimmedTitle = value.trim();
              if (!trimmedTitle.length) {
                return "Position name is required";
              }
              if (trimmedTitle !== currentTitle && existingTitles.includes(trimmedTitle)) {
                return "A position with this name already exists. Please choose a different name.";
              }
              if (trimmedTitle.length > 512) {
                return "Position title is too long";
              }
              if (trimmedTitle.length < 1) {
                return "Position title is too short";
              }
              return undefined;
            }
          }}>
            {(field) => (
              <div>
                <Label>Position Title</Label>
                <FormInput field={field} placeholder="Enter position name..." autoFocus/>
                <FieldInfo field={field}/>
              </div>
            )}
          </form.Field>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={handleCancel}>Cancel</Button>
          <Button onClick={form.handleSubmit}>Rename</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

