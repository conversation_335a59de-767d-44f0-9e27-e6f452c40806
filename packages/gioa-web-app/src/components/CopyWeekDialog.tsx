import React, {useEffect} from 'react';
import {<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON>ooter, <PERSON><PERSON><PERSON>eader, DialogTitle} from "@/src/components/ui/dialog.tsx";
import {Button} from "@/src/components/ui/button.tsx";
import {DialogDescription} from "@radix-ui/react-dialog";
import {zodValidator} from "@tanstack/zod-form-adapter";
import {useForm} from "@tanstack/react-form";
import {FormControl} from "@/src/components/form/FormControl.tsx";
import {Label} from "@/src/components/ui/label.tsx";
import {FieldInfo} from "@/src/components/form/FieldInfo.tsx";
import {z} from "zod";
import {Text} from "@/src/components/Text.tsx";
import {ToggleGroup, ToggleGroupItem} from "@/src/components/ui/toggle-group.tsx";
import {flatMap, keys, map, pickBy, uniqBy} from "lodash";
import {Checkbox} from "@/src/components/ui/checkbox.tsx";
import {BaseSchedule} from "../../../api/src/scheduleSchemas.ts";
import {FormCheckbox} from "@/src/components/form/FormCheckbox.tsx";
import {useAutoAnimate} from "@formkit/auto-animate/react";
import {generateIsoWeekDates, takeFromGenerator} from "../../../api/src/scheduleBuilder.util.ts";

export interface CopyWeekDialogProps {
  onCopy: (value: {
    days: number[]
    copyShiftAssignments: boolean;
    includeAreaTitles: string[];
    shouldFilterAreas: boolean;
  }) => void;
  isOpen: boolean;
  onClose: () => void;
  onOpen: () => void;
  schedule: BaseSchedule;
  timezone: string;
  isLoading: boolean;
}

export const CopyWeekDialog: React.FC<CopyWeekDialogProps> = (props) => {
  const schedule = props.schedule;
  const scheduleAreas = uniqBy(flatMap(schedule.days, d => d.areas), a => a.title);

  const form = useForm({
    defaultValues: {
      days: [1,2,3,4,5,6,7] as number[],
      shouldFilterAreas: false,
      copyShiftAssignments: true,
      includeAreaTitles: {} as Record<string, boolean>,
    },
    onSubmit: async ({value}) => {
      props.onCopy({
        days: value.days,
        copyShiftAssignments: value.copyShiftAssignments,
        includeAreaTitles: keys(pickBy(value.includeAreaTitles, v => v)),
        shouldFilterAreas: value.shouldFilterAreas
      });
    },
    validatorAdapter: zodValidator(),
  });

  useEffect(() => {
    if (!props.isOpen) {
      // give a bit of time for the modal to animate out
      setTimeout(() =>
        form.reset(), 100);
    }
  }, [props.isOpen]);

  const onCancel = () => {
    if (props.isLoading) {
      return;
    }
    props.onClose();
  }

  const shouldFilterAreas = form.useStore(state => state.values.shouldFilterAreas);
  const [autoContainerRef] = useAutoAnimate();
  const weeks = takeFromGenerator(generateIsoWeekDates(props.schedule.week, props.timezone), 2);

  return (
    <Dialog open={props.isOpen} onOpenChange={open => {
      if (open) {
        props.onOpen();
      } else {
        onCancel();
      }
    }}>
      <DialogContent className={"overflow-auto"} style={{maxHeight: "calc(100vh - 50px)"}}>
        <DialogHeader>
          <DialogTitle>Copy Week</DialogTitle>
          <DialogDescription>
            Copy week {schedule.week.week} to week {weeks[1].week} of {weeks[1].year}. All shifts in the areas selected below will be
            overwritten. This action cannot be undone.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          <form onSubmit={(e) => {
            e.preventDefault()
            e.stopPropagation()
            form.handleSubmit()
          }}>
            <div className={"mb-3"}>
              <form.Field name={`days`}
                          validators={{
                            onSubmit: z.array(z.number()).min(1)
                          }}
                          children={(field) => {
                            return <FormControl>
                              <Label htmlFor={field.name}>Copy week days</Label>
                              <ToggleGroup value={map(field.state.value, d => d.toString())}
                                           onValueChange={dayStrs => field.handleChange(map(dayStrs, Number))}
                                           onBlur={field.handleBlur}
                                           type="multiple" variant={"outline"} colorScheme={"primary"}
                                           className={"justify-start"}>
                                <ToggleGroupItem value="1">Mon</ToggleGroupItem>
                                <ToggleGroupItem value="2">Tue</ToggleGroupItem>
                                <ToggleGroupItem value="3">Wed</ToggleGroupItem>
                                <ToggleGroupItem value="4">Thu</ToggleGroupItem>
                                <ToggleGroupItem value="5">Fri</ToggleGroupItem>
                                <ToggleGroupItem value="6">Sat</ToggleGroupItem>
                                <ToggleGroupItem value="7">Sun</ToggleGroupItem>
                              </ToggleGroup>
                              <FieldInfo field={field}/>
                            </FormControl>;
                          }}/>
              <form.Field name={`copyShiftAssignments`}
                          children={(field) => {
                            return <div
                              className={"flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4 bg-white mb-3"}>
                              <Checkbox id={field.name}
                                        onCheckedChange={isChecked => typeof isChecked === "boolean" ? field.handleChange(isChecked) : null}
                                        checked={field.state.value}>
                              </Checkbox>
                              <div className="space-y-1 leading-none">
                                <Label htmlFor={field.name} className={"block"}>
                                  Copy shift assignments
                                </Label>
                                <Text size={"sm"} className={"block"}>
                                  Select to copy the team members with the shifts.
                                </Text>
                              </div>
                              <FieldInfo field={field}/>
                            </div>
                          }}/>

              <div className="border rounded-lg">
                <form.Field name={`shouldFilterAreas`}
                            children={(field) => <FormControl className={"border-b p-4 m-0"}>
                              <FormCheckbox field={field}
                                            label={"Filter areas"}/>
                              <FieldInfo field={field}/>
                            </FormControl>}/>


                <div ref={autoContainerRef}>
                  {shouldFilterAreas ?
                    <div className={"p-4 space-y-4"} key={"areas"}>
                      {map(scheduleAreas, area => {
                        return <form.Field key={area.id} name={`includeAreaTitles.${area.title}`}
                                           children={(field) => <FormControl className={"m-0"}>
                                             <FormCheckbox field={field}
                                                           label={area.title}/>
                                             <FieldInfo field={field}/>
                                           </FormControl>}/>
                      })}
                    </div> : <div className={"text-muted-foreground text-sm p-4"} key={"explainer"}>
                      Select "Filter areas" to
                      allow selecting areas to restrict the copy to.
                    </div>}
                </div>

              </div>
            </div>

            <DialogFooter>
              <Button variant={"outline"} type={"button"}
                      disabled={props.isLoading}
                      onClick={onCancel}>
                Cancel
              </Button>
              <Button type={"submit"} isLoading={props.isLoading}>
                Copy Week
              </Button>
            </DialogFooter>
          </form>
        </div>
      </DialogContent>
    </Dialog>
  );
}
