import React from "react";
import {But<PERSON>} from "@/src/components/ui/button";
import {Minus, Plus} from "lucide-react";
import {cn} from "@/src/util";
import {clamp} from "lodash";

export interface NumberStepperProps {
  value: number;
  onChange: (value: number) => void;
  min?: number;
  max?: number;
  step?: number;
  className?: string;
}

export const NumberStepper: React.FC<NumberStepperProps> = ({
  value,
  onChange,
  min = 0,
  max = 100,
  step = 1,
  className
}) => {
  const decDisabled = value <= min;
  const incDisabled = value >= max;

  const dec = () => onChange(clamp(value - step, min, max));
  const inc = () => onChange(clamp(value + step, min, max));

  return (
    <div className={cn("flex items-center gap-2", className)}>
      <Button size="icon" variant="outline" onClick={dec} disabled={decDisabled} aria-label="Decrease">
        <Minus className="w-4 h-4" />
      </Button>
      <span className="min-w-6 text-center">{value}</span>
      <Button size="icon" variant="outline" onClick={inc} disabled={incDisabled} aria-label="Increase">
        <Plus className="w-4 h-4" />
      </Button>
    </div>
  );
};

