import React, {useEffect} from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription, DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger
} from "@/src/components/ui/dialog.tsx";
import {Button} from "@/src/components/ui/button.tsx";
import {DraftSchedule} from "../../../api/src/scheduleSchemas.ts";
import {find, flatMap, intersectionBy, keys, map, pickBy, reduce} from "lodash";
import {daysOfWeek} from "@/src/components/AvailabilityWeek.tsx";
import {useForm} from "@tanstack/react-form";
import {FieldInfo} from "@/src/components/form/FieldInfo.tsx";
import {FormCheckbox} from "@/src/components/form/FormCheckbox.tsx";
import {FormControl} from "@/src/components/form/FormControl.tsx";
import {useAutoAnimate} from "@formkit/auto-animate/react";


export interface ApplyScheduleTemplateDialogProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  dayOfWeek: number;
  selectedTemplate: DraftSchedule;
  onApplyTemplate: (template: DraftSchedule, areaIds: string[], shouldFilterAreas: boolean) => void;
}

export const ApplyScheduleTemplateDialog: React.FC<ApplyScheduleTemplateDialogProps> = ({
                                                                                          isOpen,
                                                                                          onOpenChange,
                                                                                          dayOfWeek,
                                                                                          selectedTemplate,
                                                                                          onApplyTemplate
                                                                                        }) => {

  const templateDay = find(selectedTemplate.days, d => d.dayOfWeek === dayOfWeek);
  const templateAreas = templateDay?.areas ?? [];

  const dayOfWeekObj = find(daysOfWeek, d => d.dayOfWeek === dayOfWeek);
  const form = useForm({
    defaultValues: {
      shouldFilterAreas: false,
      includeAreaIds: reduce(templateAreas, (acc, area) => {
        acc[area.id] = area.shifts?.length > 0;
        return acc;
      }, {} as Record<string, boolean>)
    },
    onSubmit: ({value}) => {
      onApplyTemplate(selectedTemplate, keys(pickBy(value.includeAreaIds, v => v)), value.shouldFilterAreas);
    }
  })

  useEffect(() => {
    if (!isOpen) {
      form.reset();
    }
  }, [isOpen]);

  const shouldFilterAreas = form.useStore(state => state.values.shouldFilterAreas);
  const [autoContainerRef] = useAutoAnimate();

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>

      <DialogContent>
        <form className={"space-y-4"}
              onSubmit={(e) => {
                e.preventDefault()
                e.stopPropagation()
                form.handleSubmit()
              }}>
          <DialogHeader>
            <DialogTitle>Apply Template</DialogTitle>
            <DialogDescription>
              Apply the selected template.
            </DialogDescription>
          </DialogHeader>

          <p>
            Are you sure you want to apply the template "{selectedTemplate?.title}" to {dayOfWeekObj?.name}? All
            of {dayOfWeekObj?.name}'s shifts {shouldFilterAreas ? "in the areas selected below" : "and areas"} will be
            overwritten by the template.
          </p>

          <div className="border rounded-lg">
            <form.Field name={`shouldFilterAreas`}
                        children={(field) => <FormControl className={"border-b p-4 m-0"}>
                          <FormCheckbox field={field}
                                        label={"Filter areas to apply the template to"}/>
                          <FieldInfo field={field}/>
                        </FormControl>}/>


            <div ref={autoContainerRef}>
              {shouldFilterAreas ?
                <div className={"p-4 space-y-4"} key={"areas"}>
                  {map(templateAreas, area => {
                    return <form.Field key={area.id} name={`includeAreaIds.${area.id}`}
                                       children={(field) => <FormControl className={"m-0"}>
                                         <FormCheckbox field={field}
                                                       label={<span>{area.title} <span
                                                         className={"text-muted-foreground"}>({area.shifts?.length} shifts in template)</span></span>}/>
                                         <FieldInfo field={field}/>
                                       </FormControl>}/>
                  })}
                </div> : <div className={"text-muted-foreground text-sm p-4"} key={"explainer"}>
                  Select "Filter areas to apply the template to" to
                  allow selecting areas to restrict the template to.
                </div>}
            </div>

          </div>

          <DialogFooter>
            <Button variant={"outline"} type={"button"}
                    onClick={() => onOpenChange(false)}>
              Cancel
            </Button>
            <Button type={"submit"}>
              Apply Template
            </Button>
          </DialogFooter>
        </form>

      </DialogContent>
    </Dialog>
  )
    ;
}
