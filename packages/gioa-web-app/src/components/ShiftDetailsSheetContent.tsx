import React, {MutableRefObject, useCallback, useEffect, useMemo, useState} from 'react';
import {<PERSON><PERSON><PERSON><PERSON>, Sheet<PERSON>ooter, Sheet<PERSON>eader, SheetTitle} from "@/src/components/ui/sheet.tsx";
import {Button} from "@/src/components/ui/button.tsx";
import {useForm} from "@tanstack/react-form";
import {zodValidator} from "@tanstack/zod-form-adapter";
import {DailyTimeRange, TimeOfDay} from "../../../api/src/timeSchemas.ts";
import {FormControl} from "@/src/components/form/FormControl.tsx";
import {Label} from "@/src/components/ui/label.tsx";
import {FieldInfo} from "@/src/components/form/FieldInfo.tsx";
import {Activity, DraftSchedule, HawaiiACAPersonHours} from "../../../api/src/scheduleSchemas.ts";
import {FormSelectTeamMember} from "@/src/components/form/FormSelectTeamMember.tsx";
import {StoreAreaDto, StorePositionDto} from "../../../api/src/schemas.ts";
import {FormTextarea} from "@/src/components/form/FormTextarea.tsx";
import {Checkbox} from './ui/checkbox.tsx';
import {CrownIcon, EllipsisIcon, HandHelpingIcon, LinkIcon, XIcon} from "lucide-react";
import {useCopyToClipboard, useDebounceCallback} from "usehooks-ts";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue
} from "@/src/components/ui/select.tsx";
import {chain, filter, find, isEmpty, map, reduce} from "lodash";
import {useIgnoreScheduleValidationMessages} from "@/src/hooks/useIgnoreScheduleValidationMessages.tsx";
import {
  ScheduleValidationResult,
  ScheduleValidationSettings
} from "../../../api/src/scheduleValidation.types.ts";
import {ValidationMessageIcon} from "@/src/components/ValidationMessageIcon.tsx";
import {useAutoAnimate} from "@formkit/auto-animate/react";
import {DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger} from './ui/dropdown-menu.tsx';
import {toast} from "sonner";
import {SchedulePersonClientDto} from "../../../api/src/schedulePersonDto.ts";
import {
  getHumanReadableMessageCodeSummary,
  incrementTo24HourTime,
  ScheduleRowInfo,
  ShiftActivity
} from "../../../api/src/scheduleBuilder.util.ts";
import {ShiftActivityAccordionItem} from "@/src/components/ShiftActivityAccordionItem.tsx";
import {FormDailyTimeRange} from "@/src/components/form/FormDailyTimeRange.tsx";
import {Accordion} from './ui/accordion.tsx';
import {CreateActivityDialog} from "@/src/components/CreateActivityDialog.tsx";
import {messageToHumanReadable} from "@/src/validationMessageDisplay.tsx";

import {
  getIgnoreId,
  isMessageForShift,
  isMessageIgnorable
} from "../../../api/src/scheduleValidation/scheduleValidationMessagesL2.ts";
import {getDateTimeFromWeekDayTime} from "../../../api/src/date.util.ts";
import {getDayAreas, getShiftWeekday} from "../../../api/src/schedule.ts";
import {useDisclosure} from "@/src/hooks/useDisclosure.tsx";
import {CreateHouseShiftOfferDialog} from "@/src/components/CreateHouseShiftOfferDialog.tsx";

export interface ShiftDetailsProps {
  shift: ScheduleRowInfo;
  onSelectedActivityChange: (activityId: string | undefined) => void;
  onShiftUpdated: (shiftId: string, values: {
    start: TimeOfDay;
    end: TimeOfDay;
    description: string | undefined;
    assignedPersonId: string | undefined;
    isShiftLead: boolean;
    storePositionId: string | undefined;
    shiftAreaId: string;
  }) => void;
  onShiftActivitiesUpdated: (shiftId: string, activities: Activity[]) => void;
  storeHours: DailyTimeRange;
  onDeleteShift: (shift: ScheduleRowInfo) => void;
  onDuplicateShift: (shift: ScheduleRowInfo) => void;
  people: SchedulePersonClientDto[];
  schedule: DraftSchedule;
  storeAreas: StoreAreaDto[];
  settings: ScheduleValidationSettings;
  timezone: string;
  validationResult?: ScheduleValidationResult;
  routeFullPath: string;
  onChangeView: (view: string, day: number) => void;
  currentView: string;
  hideDuplicateShift?: boolean;
  canChangeView?: boolean;
  latestServerVersion: MutableRefObject<number>;
  storeId: string;
  weeksHoursMap: HawaiiACAPersonHours;
  storeState?: string;
}

export const ShiftDetailsSheetContent: React.FC<ShiftDetailsProps> = ({
                                                                        shift, hideDuplicateShift, canChangeView,
                                                                        timezone, settings,
                                                                        onDeleteShift, storeId,
                                                                        storeAreas, currentView,
                                                                        schedule, onChangeView,
                                                                        onShiftUpdated, latestServerVersion,
                                                                        onShiftActivitiesUpdated,
                                                                        storeHours, onSelectedActivityChange,
                                                                        people, routeFullPath, onDuplicateShift,
                                                                        validationResult,
                                                                        weeksHoursMap,
                                                                        storeState
                                                                      }) => {

  const dayOfWeek = getShiftWeekday(schedule, shift.id);
  const form = useForm({
    defaultValues: {
      start: incrementTo24HourTime(storeHours, shift.start),
      end: incrementTo24HourTime(storeHours, shift.end),
      description: shift.description,
      assignedPersonId: shift.assignedTo?.id,
      isShiftLead: Boolean(shift.isShiftLead),
      storePositionId: shift.storePositionId,
      shiftAreaId: shift.areaId ?? undefined
    },
    onSubmit: async ({value}) => {
      onShiftUpdated(shift.id, {
        start: value.start,
        end: value.end,
        description: value.description,
        assignedPersonId: value.assignedPersonId || undefined,
        isShiftLead: value.isShiftLead,
        storePositionId: value.storePositionId || undefined,
        shiftAreaId: value.shiftAreaId!
      });
    },
    validatorAdapter: zodValidator(),
  });

  const shiftAreaOptions = useMemo(() => {
    return map(getDayAreas(schedule, dayOfWeek), a => {
      return {
        label: a.title,
        value: a.id
      }
    })
  }, [schedule, dayOfWeek]);

  const onActivitiesUpdated = (activities: ShiftActivity[]) => {
    onShiftActivitiesUpdated(shift.id, map(activities, (activity): Activity => {
      return {
        id: activity.id,
        activityType: activity.activityType,
        title: activity.title,
        description: activity.description,
        range: {
          start: incrementTo24HourTime(storeHours, activity.start),
          end: incrementTo24HourTime(storeHours, activity.end),
        },
        countsTowardsLabor: activity.countsTowardsLabor,
        setupPositionTitle: activity.setupPositionTitle,
        payStatus: activity.payStatus,
      }
    }));
  }

  const {ignoreMessage, isMessageIgnored} = useIgnoreScheduleValidationMessages({
    scheduleId: schedule.id,
    storeId: schedule.storeId,
  });
  const messagesForShift = chain(validationResult?.messages ?? [])
    .filter(isMessageForShift({
      id: shift.id,
      assignedPersonId: shift.assignedTo?.id ?? undefined
    }))
    .filter(isMessageIgnorable)
    .filter(m => !isMessageIgnored(m))
    .value();
  const getMsg = messageToHumanReadable({
    people: people,
    activeDay: getDateTimeFromWeekDayTime({
      year: schedule.week.year,
      week: schedule.week.week,
      day: dayOfWeek,
      time: "00:00",
      timezone
    }),
    routeFullPath: routeFullPath
  });

  useEffect(() => {
    form.setFieldValue("start", incrementTo24HourTime(storeHours, shift.start));
    form.setFieldValue("end", incrementTo24HourTime(storeHours, shift.end));
    form.setFieldValue("description", shift.description ?? "");
    form.setFieldValue("assignedPersonId", shift.assignedTo?.id ?? "");
    form.setFieldValue("isShiftLead", Boolean(shift.isShiftLead));
    form.setFieldValue("storePositionId", shift.storePositionId ?? "");
    form.setFieldValue("shiftAreaId", shift.areaId ?? "");
  }, [shift]);

  const onDeleteClick = () => {
    onDeleteShift(shift);
  }

  const handleStartTimeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    form.handleSubmit();
  }
  const handleEndTimeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    form.handleSubmit();
  }

  const handleAssignedPersonChange = useCallback((personId: string | null) => {
    form.handleSubmit();
  }, []);

  const handleDescriptionChange = useDebounceCallback((e: React.ChangeEvent<HTMLTextAreaElement>) => {
    form.handleSubmit();
  }, 1000);

  const handleIsShiftLeadChange = (isShiftLead: boolean) => {
    form.handleSubmit();
  }

  const handleStorePositionChange = (storePositionId: string) => {
    form.handleSubmit();
  }

  const handleShiftAreaChange = (shiftAreaId: string) => {
    if (!shiftAreaId) {
      // we should never not have a shift area id
      return;
    }
    form.handleSubmit();
  }

  const day = find(schedule.days, d => d.dayOfWeek === dayOfWeek);
  const scheduleArea = find(day?.areas, a => a.id === shift.areaId);
  const storeArea = find(storeAreas, sa => sa.id === scheduleArea?.storeAreaId);
  const storeAreaPositions = storeArea?.positions
    ? {[storeArea.title]: storeArea.positions}
    : reduce(storeAreas,
      (acc, area) => Object.assign(acc, {[area.title]: area.positions})
      , {} as { [areaTitle: string]: StorePositionDto[] });

  const [messagesContainerRef] = useAutoAnimate()
  const [, copyToClipboard] = useCopyToClipboard();
  const onCopyLinkToShift = () => {
    const currentUrl = new URL(window.location.href);
    currentUrl.searchParams.set('shiftId', shift.id);
    const newUrl = currentUrl.toString();

    copyToClipboard(newUrl)
      .then(() => {
        toast.success("Link copied to clipboard", {
          position: "top-center",
        });
      })
      .catch(() => {
        toast.error("Failed to copy link to clipboard", {
          position: "top-center",
        });
      });
  }

  const onDuplicateShiftClick = () => {
    onDuplicateShift(shift);
  }

  const [accordionValue, setAccordionValue] = useState<string>();
  useEffect(() => {
    onSelectedActivityChange(accordionValue);
  }, [accordionValue])

  const onAccordionChange = (value: string) => {
    setAccordionValue(value);
  }

  const addActivity = (activity: ShiftActivity) => {
    onActivitiesUpdated([...shift.activities ?? [], activity]);
  }

  const removeActivity = (activity: ShiftActivity) => {
    onActivitiesUpdated(filter(shift.activities, (a) => a.id !== activity.id));
  }

  const onActivityChange = (activity: ShiftActivity) => {
    onActivitiesUpdated(map(shift.activities, (a) => {
      if (a.id === activity.id) {
        return activity;
      }
      return a;
    }));
  }
  const otherView = currentView === "Day" ? "Week" : "Day";
  const houseOffer = useDisclosure();

  return (
    <>
      <SheetHeader className={"px-6"}>
        <div className={"flex items-center gap-3"}>
          <SheetTitle className={"grow"}>Shift Details</SheetTitle>

          {canChangeView ?
            <Button variant={"outline"} onClick={() => onChangeView(otherView, dayOfWeek)}
                    size={"sm"}>
              View on {otherView}
            </Button> : null}

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant={"outline"} size={"iconSm"}>
                <EllipsisIcon className="h-5 w-5"/>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              {!shift.assignedTo && !shift.shiftOffer ?
                <DropdownMenuItem onSelect={houseOffer.onOpen}>
                  <HandHelpingIcon className="mr-2 h-4 w-4"/>
                  <span>
                Create house shift offer
                  </span>
                </DropdownMenuItem> : null}
              <DropdownMenuItem onSelect={onCopyLinkToShift}>
                <LinkIcon className="mr-2 h-4 w-4"/>
                <span>
                Copy link to shift
                </span>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>

        </div>
      </SheetHeader>

      <div ref={messagesContainerRef} className={"px-6"}>
        {map(messagesForShift, msg => {
          const ignoreId = getIgnoreId(msg);
          return <div className={"border rounded-lg px-4 py-3 mb-2"} key={ignoreId + msg.id}>
            <div className={"mb-2"}>
              <div className={"flex items-center gap-3 mb-2"}>
                <ValidationMessageIcon severity={msg.severity}/>
                <div className={"text-md font-normal"}>
                  {getHumanReadableMessageCodeSummary(msg.code)}
                </div>
              </div>
              <div className={"text-sm text-muted-foreground"}>
                {getMsg(msg)}
              </div>
            </div>
            {ignoreId ?
              <Button onClick={() => ignoreMessage(msg)}
                      variant={"outline"} size={"sm"}>
                Ignore
              </Button> : null}
          </div>
        })}
      </div>

      <form onSubmit={(e) => {
        e.preventDefault()
        e.stopPropagation()
        form.handleSubmit()
      }}>
        <FormDailyTimeRange form={form as any} onStartTimeChange={handleStartTimeChange}
                            className={"px-6"}
                            onEndTimeChange={handleEndTimeChange}/>

        <div className={"px-6"}>
          <form.Field name={"assignedPersonId"}
                      children={(field) => {
                        return <FormControl>
                          <Label htmlFor={field.name}>Assigned To</Label>
                          <FormSelectTeamMember people={people} timezone={timezone} settings={settings}
                                                field={field} dayOfWeek={dayOfWeek}
                                                onChange={handleAssignedPersonChange}
                                                selectedShiftId={shift.id} storeAreas={storeAreas} schedule={schedule}
                                                weeksHoursMap={weeksHoursMap}
                                                storeState={storeState}/>
                          <FieldInfo field={field}/>
                        </FormControl>;
                      }}/>

          <form.Field name={"shiftAreaId"}
                      children={field => <FormControl>
                        <Label htmlFor={field.name}>Area</Label>
                        <div className={"flex gap-1 items-center"}>
                          <Select onValueChange={val => {
                            field.handleChange(val);
                            handleShiftAreaChange(val);
                          }}
                                  value={field.state.value}>
                            <SelectTrigger hasError={!isEmpty(field.state.meta.errors)}>
                              <SelectValue placeholder="Select a work area..."/>
                            </SelectTrigger>
                            <SelectContent>
                              {map(shiftAreaOptions, (areaOption) =>
                                <SelectItem value={areaOption.value}
                                            key={areaOption.value}>{areaOption.label}</SelectItem>)}
                            </SelectContent>
                          </Select>
                        </div>
                        <FieldInfo field={field}/>
                      </FormControl>}/>

          <form.Field name={"storePositionId"}
                      children={field => <FormControl>
                        <Label htmlFor={field.name}>Position (optional)</Label>
                        <div className={"flex gap-1 items-center"}>
                          <Select onValueChange={val => {
                            field.handleChange(val);
                            handleStorePositionChange(val);
                          }}
                                  value={field.state.value}>
                            <SelectTrigger hasError={!isEmpty(field.state.meta.errors)}>
                              <SelectValue placeholder="Select a position..."/>
                            </SelectTrigger>
                            <SelectContent>
                              {map(storeAreaPositions, (positions, areaTitle) =>
                                <SelectGroup key={areaTitle}>
                                  <SelectLabel>{areaTitle}</SelectLabel>
                                  {map(positions, position =>
                                    <SelectItem value={position.id} key={position.id}>{position.title}</SelectItem>
                                  )}
                                </SelectGroup>)}
                            </SelectContent>
                          </Select>
                          {field.state.value ?
                            <Button size={"icon"} variant={"ghost"} aria-label={"Clear position"} type={"button"}
                                    onClick={() => {
                                      field.handleChange("");
                                      handleStorePositionChange("");
                                    }}>
                              <XIcon size={20} className={"text-gray-600"}/>
                            </Button> : null}
                        </div>
                        <FieldInfo field={field}/>
                      </FormControl>}/>

          <form.Field name={"description"}
                      children={(field) => {
                        return <FormControl>
                          <Label htmlFor={field.name}>Description (optional)</Label>
                          <FormTextarea field={field} onChange={handleDescriptionChange}
                                        placeholder="Enter comments or description of this shift..."/>
                          <FieldInfo field={field}/>
                        </FormControl>;
                      }}/>

          <form.Field name={`isShiftLead`}
                      children={(field) => {
                        return <Label htmlFor={field.name}
                                      className={"flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4 bg-white mb-3"}>
                          <Checkbox id={field.name}
                                    onCheckedChange={isChecked => {
                                      if (typeof isChecked === "boolean") {
                                        field.handleChange(isChecked)
                                        handleIsShiftLeadChange(isChecked);
                                      }
                                    }}
                                    checked={field.state.value}>
                          </Checkbox>
                          <div className="space-y-1 leading-none">
                            <div className={"flex gap-2"}>
                              Is Shift Lead
                              <CrownIcon size={16}/>
                            </div>
                            <div className={"text-sm font-normal"}>
                              Select if this shift will be assigned the shift leader for this period.
                            </div>
                          </div>
                          <FieldInfo field={field}/>
                        </Label>
                      }}/>

        </div>


        <Accordion type="single" collapsible onValueChange={onAccordionChange}
                   value={accordionValue}
                   className="w-full mb-3">
          {map(shift.activities, (activity, idx) => {
            const isSelected = accordionValue === activity.id;

            return <ShiftActivityAccordionItem key={activity.id}
                                               shift={shift} storeId={storeId}
                                               onDelete={() => removeActivity(activity)}
                                               isSelected={isSelected}
                                               value={activity}
                                               storeHours={storeHours}
                                               onChange={a => {
                                                 onActivityChange(a);
                                               }}/>;
          })}
        </Accordion>

        <div className={"px-6"}>
          <CreateActivityDialog onSubmit={activity => {
            addActivity(activity);
            setAccordionValue(activity.id);
          }} storeId={storeId}
                                storeHours={storeHours}
                                shift={shift}/>
        </div>

        <SheetFooter className={" border-t pt-3 mt-3 px-6"}>
          <SheetClose asChild>
            <Button type="submit">
              Close
            </Button>
          </SheetClose>
          <div className={"grow"}>
            {!hideDuplicateShift ?
              <Button variant={"outline"} type={"button"}
                      onClick={onDuplicateShiftClick}>
                Duplicate
              </Button> : null}
          </div>
          <Button type={"button"} variant={"outline"} onClick={onDeleteClick}>
            Delete Shift
          </Button>
        </SheetFooter>
      </form>

      <CreateHouseShiftOfferDialog isOpen={houseOffer.isOpen}
                                   latestServerVersion={latestServerVersion}
                                   scheduleId={schedule.id}
                                   shiftId={shift.id}
                                   onOpenChange={houseOffer.setOpen}/>
    </>
  );
}
