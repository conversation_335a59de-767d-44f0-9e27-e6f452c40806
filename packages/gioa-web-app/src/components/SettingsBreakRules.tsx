import React from 'react';
import {Text} from "@/src/components/Text.tsx";
import {Button} from "@/src/components/ui/button.tsx";
import {Heading} from "@/src/components/Heading.tsx";
import {BreakRulesForm} from "@/src/components/BreakRulesForm.tsx";
import {toast} from "sonner";

export interface SettingsBreakRulesProps {
  storeId: string;
}

export const SettingsBreakRules: React.FC<SettingsBreakRulesProps> = ({storeId}) => {
  const [isLoading, setIsLoading] = React.useState(false);
  const formRef = React.useRef<{ submit: () => Promise<void> }>(null);
  const handleSaveAndContinue = async () => {
    if (formRef.current) {
      await formRef.current.submit();
    }
  }

  const onContinue = () => {
    toast.success("Break rules updated", {
      position: "top-center",
    });
  }

  return (
    <div>
      <Heading level={1}>Break Rules</Heading>
      <div className="mt-8 mb-3">
        <Text muted>
          Edit your store’s required team member breaks. Below are recommended breaks based on your specified location.
        </Text>
      </div>

      <BreakRulesForm ref={formRef}
                      storeId={storeId}
                      setIsLoading={setIsLoading}
                      onContinue={onContinue}/>
      <div className={"my-3"}>
        <Text muted size={"xs"}>
          The Nation app is not responsible for ensuring compliance with federal, state, or local laws.
        </Text>
      </div>

      <div className={"mt-8"}>
        <Button onClick={handleSaveAndContinue} isLoading={isLoading}>Save</Button>
      </div>
    </div>
  );
}
