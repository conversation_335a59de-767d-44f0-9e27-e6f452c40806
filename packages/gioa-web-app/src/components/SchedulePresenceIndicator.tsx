import React, {useEffect, useRef, useState} from 'react';
import {api} from "@/src/api.ts";
import {AvatarGroup} from "@/src/components/AvatarStack.tsx";
import {SchedulePersonDto} from "../../../api/src/schedulePersonDto.ts";
import {compact, filter, find, forEach, includes, isEmpty, map} from "lodash";
import {createPortal} from 'react-dom';

export interface SchedulePresenceIndicatorProps {
  scheduleId: string;
  people: SchedulePersonDto[];
  currentPersonId: string;
}

interface PersonActivity {
  timestamp: number;
  shiftId?: string;
}

interface ShiftHighlightProps {
  people: SchedulePersonDto[];
}

// Component to render the shift highlight with avatars
const ShiftHighlight: React.FC<ShiftHighlightProps> = ({people}) => {
  return (
    <div className="absolute inset-0 pointer-events-none">
      <div className="absolute -top-2 -left-2 z-20">
        <AvatarGroup avatarClassName={"border-0"}
                     people={people}
                     size="xs"
                     overlap={8}
        />
      </div>
      <div className="absolute inset-0 outline outline-2 outline-offset-2 outline-blue-400 rounded-md z-10"></div>
    </div>
  );
};

export const SchedulePresenceIndicator: React.FC<SchedulePresenceIndicatorProps> = ({scheduleId, people, currentPersonId}) => {
  const [activePeopleIds, setActivePeopleIds] = useState<string[]>([]);
  // Track the last activity timestamp and selected shift for each person
  const [personActivities, setPersonActivities] = useState<Record<string, PersonActivity>>({});
  const timerRef = useRef<number | null>(null);

  // Subscribe to schedule activity events
  api.scheduling.onScheduleActivity.useSubscription({
    scheduleId: scheduleId
  }, {
    onData: (activity) => {
      const now = Date.now();
      // Update the activity data for this person
      setPersonActivities(prev => ({
        ...prev,
        [activity.personId]: {
          timestamp: now,
          shiftId: activity.selectedShiftId
        }
      }));

      // Add the person to the active people list if not already present
      setActivePeopleIds(prev => {
        if (!includes(prev, activity.personId)) {
          return [activity.personId, ...prev.slice(0, 20)];
        }
        return prev;
      });
    }
  });

  // Set up a timer to check for stale activities
  useEffect(() => {
    // Check every 5 seconds for people who haven't sent a heartbeat in 10 seconds
    const checkStaleActivities = () => {
      const now = Date.now();
      const staleThreshold = 10 * 1000; // 10 seconds in milliseconds

      // Remove stale activities
      setPersonActivities(prev => {
        const updatedActivities: Record<string, PersonActivity> = {};
        let hasChanges = false;

        forEach(prev, (activity, personId) => {
          if (now - activity.timestamp < staleThreshold) {
            updatedActivities[personId] = activity;
          } else {
            hasChanges = true;
          }
        });

        return hasChanges ? updatedActivities : prev;
      });

      // Update active people IDs based on current activities
      setActivePeopleIds(prev => {
        return filter(prev, personId => {
          const activity = personActivities[personId];
          // Keep the person if they've had activity within the threshold
          return Boolean(activity && (now - activity.timestamp) < staleThreshold);
        });
      });
    };

    // Set up the interval
    timerRef.current = window.setInterval(checkStaleActivities, 5000);

    // Clean up the interval when the component unmounts
    return () => {
      if (timerRef.current !== null) {
        window.clearInterval(timerRef.current);
      }
    };
  }, [personActivities]);

  // Group people by the shift they're viewing
  const peopleByShift: Record<string, SchedulePersonDto[]> = {};

  forEach(personActivities, (activity, personId) => {
    if (!activity.shiftId) return;

    const person = find(people, p => p.id === personId);
    if (!person) return;

    if (!peopleByShift[activity.shiftId]) {
      peopleByShift[activity.shiftId] = [];
    }

    peopleByShift[activity.shiftId].push(person);
  });

  const shiftHighlights = map(peopleByShift, (shiftPeople, shiftId) => {
    const otherShiftPeople = filter(shiftPeople, p => p.id !== currentPersonId);

    // don't show the highlight if there's only one person and it's the current person
    if (isEmpty(otherShiftPeople)) return null;

    // Find the shift element in the DOM
    const shiftElement = document.querySelector(`[data-id="${shiftId}"]`);
    if (!shiftElement) return null;

    // Make sure the shift element has position relative for proper positioning
    if (window.getComputedStyle(shiftElement).position === 'static') {
      (shiftElement as HTMLElement).style.position = 'relative';
    }

    // Create a portal to render the highlight inside the shift element
    return createPortal(
      <ShiftHighlight people={otherShiftPeople} key={shiftId}/>,
      shiftElement
    );
  });

  const recentPeople = compact(map(activePeopleIds, id => find(people, p => p.id === id)));

  return (
    <>
      <AvatarGroup people={recentPeople} size={"default"} overlap={16}/>
      {shiftHighlights}
    </>
  );
}

