import React from 'react';
import {Text} from "@/src/components/Text.tsx";
import {Separator} from "@/src/components/ui/separator.tsx";
import {api} from "@/src/api.ts";
import {Link} from "@/src/components/Link.tsx";
import {utcNow} from "../../../api/src/date.util.ts";
import {formatInTimeZone} from 'date-fns-tz';

export interface DashboardWelcomeHeaderProps {
  storeId: string;
  businessId: string;
}

export const DashboardWelcomeHeader: React.FC<DashboardWelcomeHeaderProps> = ({storeId, businessId}) => {
  const [user] = api.user.getUserProfile.useSuspenseQuery();

  const queries = {
    timeOffRequests: api.user.getStoreTimeOffRequests.useQuery({storeId, status: "pending"}),
    shiftOffers: api.user.getShiftOffers.useQuery({storeId, status: "pending", appVersion: "web"}),
    availabilityRequests: api.user.getAvailabilityRequests.useQuery({storeId, status: "pending"}),
  };

  const {data: timeOffData} = queries.timeOffRequests;
  const {data: shiftOffersData} = queries.shiftOffers;
  const {data: availabilityRequestsData} = queries.availabilityRequests;

  const numAvailabilityPending = availabilityRequestsData?.items.length ?? 0;
  const numTimeOffPending = timeOffData?.items.length ?? 0;
  const numShiftOffersPending = shiftOffersData?.items.length ?? 0;

  const [store] = api.user.getStoreAdmin.useSuspenseQuery({storeId}, {
    staleTime: 1000 * 60 * 60 // 1 hour
  });
  const {timezone} = store;
  const nowDateString = formatInTimeZone(utcNow(), timezone ?? "America/New_York", "EEE, MMMM d, yyyy");

  const links = [
    {
      to: `/${businessId}/${storeId}/schedules/requests/availability`,
      text: "Availability",
      count: numAvailabilityPending
    },
    {to: `/${businessId}/${storeId}/schedules/requests/timeOff`, text: "Time Off", count: numTimeOffPending},
    {to: `/${businessId}/${storeId}/schedules/requests/shiftOffers`, text: "Shift Offer", count: numShiftOffersPending},
  ];

  return (
    <header>
      <div className="flex flex-col justify-start md:flex-row md:justify-between">
        <div className={"flex flex-col"}>
          <Text className={"text-2xl"}>Hello, {user.person.firstName}!</Text>
          <Text>Today is {nowDateString}</Text>
        </div>
        <div className="flex gap-2 bg-white shadow-sm p-4 rounded-2xl justify-start shadow items-center">
          <div className={"mr-5 h-6 flex flex-col items-center justify-center"}>
            <Text size={"sm"} bold>Requests</Text>
          </div>
          {links.map(({to, text, count}, index) => (
            <React.Fragment key={text}>

              <Link className="flex items-center gap-2" to={to}>
                <Text size="sm">{text}</Text>
                <Text colorScheme={count === 0 ? "default" : "danger"} size={"sm"}
                      className={`${count === 0 ? "bg-gray-100" : "bg-red-100"} w-6 h-6 flex items-center justify-center rounded-full`}>{count}</Text>
              </Link>
              {index < links.length - 1 ? <Separator orientation={"vertical"}/> : null}
            </React.Fragment>
          ))}
        </div>
      </div>
    </header>
  );
};

export default DashboardWelcomeHeader;
