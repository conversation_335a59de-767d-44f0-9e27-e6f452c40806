import React from "react";
import { useDropzone } from "react-dropzone";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, DialogHeader, DialogTitle } from "@/src/components/ui/dialog.tsx";
import { Button } from "@/src/components/ui/button.tsx";
import { Text } from "@/src/components/Text.tsx";
import { CloudUploadIcon, FileImageIcon, FileTextIcon, FileVideoIcon, Trash2Icon } from "lucide-react";
import { formatFileSize } from "@/src/utils/formatters.ts";
import { api } from "@/src/api.ts";
import { ErrorAlert, getHumanReadableErrorMessage } from "@/src/components/ErrorAlert.tsx";
import { useForm } from "@tanstack/react-form";
import { zodValidator } from "@tanstack/zod-form-adapter";
import { z } from "zod";
import { toast } from "sonner";
import { useFormAttachmentUpload } from "@/src/hooks/useFormAttachmentUpload.ts";
import { FileAttachmentDto } from "@gioa/api/src/fileAttachment.dto";
import { Progress } from "@/src/components/ui/progress.tsx";
import { Label } from "@/src/components/ui/label.tsx";
import { FormInput } from "@/src/components/form/FormInput.tsx";
import { FieldInfo } from "@/src/components/form/FieldInfo.tsx";
import { genStoreResourceId } from "@gioa/api/src/schemas";

interface DocumentFile {
  file: File;
  name: string;
  size: number;
  type: string;
}

interface AddStoreFileModalProps {
  isOpen: boolean;
  onClose: () => void;
  storeId: string;
  onSuccess?: () => void;
}

export function AddStoreFileModal({ isOpen, onClose, storeId, onSuccess }: AddStoreFileModalProps) {
  const getPresignedPost = api.user.getPresignedPostForStoreResource.useMutation();
  const upsertStoreResource = api.user.upsertStoreResource.useMutation({
    onSuccess: () => {
      toast.success("File uploaded successfully", {
        position: "top-center",
      });
      onSuccess?.();
      form.reset();
    },
    onError: (error) => {
      toast.error("Failed to upload file: " + getHumanReadableErrorMessage(error), {
        position: "top-center",
      });
    },
  });

  const { toDto: uploadAttachment, status: uploadStatus } = useFormAttachmentUpload(({ contentType, mediaType }) =>
    getPresignedPost.mutateAsync({
      storeId,
      contentType,
      mediaType: mediaType || "file",
    }),
  );

  const form = useForm({
    defaultValues: {
      document: null as DocumentFile | null,
      fileName: "",
    },
    validatorAdapter: zodValidator(),
    onSubmit: async ({ value }) => {
      try {
        let attachment: FileAttachmentDto | undefined;
        if (value.document) {
          attachment = await uploadAttachment(value.document.file);
          if (!attachment) {
            toast.error("Failed to upload attachment");
            return;
          }
        }

        if (!attachment) {
          toast.error("Failed to upload attachment");
          return;
        }

        upsertStoreResource.mutate({
          id: genStoreResourceId(),
          storeId,
          resourceType: "file",
          resourceTitle: value.fileName,
          attachment: attachment,
        });
      } catch (error) {
        console.error("Error uploading file:", error);
        toast.error("Error uploading file: " + getHumanReadableErrorMessage(error), {
          position: "top-center",
        });
      }
    },
  });

  const formatFileName = (filename: string): string => {
    const nameWithoutExtension = filename.replace(/\.[^/.]+$/, "");
    const nameWithSpaces = nameWithoutExtension.replace(/[_\-.]+/g, " ");

    const titleCased = nameWithSpaces
      .split(" ")
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join(" ");

    const maxLength = 50;
    return titleCased.length > maxLength ? titleCased.substring(0, maxLength) : titleCased;
  };

  const onDrop = React.useCallback(
    (acceptedFiles: File[]) => {
      const file = acceptedFiles[0];
      if (file) {
        form.setFieldValue("document", {
          file,
          name: file.name,
          size: file.size,
          type: file.type,
        });

        form.setFieldValue("fileName", formatFileName(file.name));
      }
    },
    [form],
  );

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      // All images, videos, PDFs, and office documents
      "image/*": [],
      "video/*": [],
      "text/plain": [],
      "application/pdf": [],
      "application/msword": [],
      "application/vnd.openxmlformats-officedocument.wordprocessingml.document": [],
      "application/vnd.ms-excel": [],
      "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet": [],
    },
    multiple: false,
    maxSize: 100 * 1024 * 1024, // 100MB max size
  });

  const handleClose = () => {
    form.reset();
    onClose();
  };

  const isLoading = upsertStoreResource.isPending || uploadStatus.isLoading;

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && handleClose()}>
      <DialogContent className="sm:max-w-3xl w-full max-w-[95vw] overflow-hidden">
        <DialogHeader>
          <DialogTitle>Add File</DialogTitle>
        </DialogHeader>

        {uploadStatus.isError && <ErrorAlert error={uploadStatus.error} className="my-2" />}
        {upsertStoreResource.isError && <ErrorAlert error={upsertStoreResource.error} className="my-2" />}

        <div className="space-y-4 py-4 w-full overflow-hidden">
          <form.Field
            name="document"
            validators={{
              onSubmit: z.any().refine((val) => val !== null, {
                message: "Please select a file to upload",
              }),
            }}
            children={(field) => (
              <>
                <div
                  {...getRootProps()}
                  className={`flex flex-col gap-4 border-2 border-dashed rounded-lg
                    p-12 text-center cursor-pointer transition-colors justify-center items-center
                    ${isDragActive ? "border-primary bg-primary/10" : "border-gray-300 hover:border-primary/50"}`}
                >
                  <input {...getInputProps()} />
                  <CloudUploadIcon className="mx-auto h-12 w-12 text-gray-400" />
                  <Text className="mt-2">Choose a file or drag & drop it here</Text>
                  <Button variant="outline" type="button" className="mt-2 border-gray-400">
                    Browse
                  </Button>
                </div>

                {field.state.value && (
                  <div className={"space-y-3"}>
                    <div className="bg-gray-100 p-4 rounded-lg mt-4 w-full max-w-full overflow-hidden">
                      <div className="flex flex-row items-center gap-6 w-full overflow-hidden">
                        <div>
                          {field.state.value.type.startsWith("image/") ? (
                            <FileImageIcon size={36} className="text-orange-600" />
                          ) : field.state.value.type.startsWith("video/") ? (
                            <FileVideoIcon size={36} className="text-green-600" />
                          ) : (
                            <FileTextIcon size={36} className="text-primary-600" />
                          )}
                        </div>
                        <div className="flex-1 flex flex-col overflow-hidden min-w-0">
                          <Text size="sm" className="truncate w-full">
                            {field.state.value.name}
                          </Text>
                          <Text size="xs" muted>
                            {formatFileSize(field.state.value.size)}
                          </Text>
                        </div>
                        <div>
                          <Button variant="ghost" size="sm" onClick={() => field.handleChange(null)} type="button">
                            <Trash2Icon size={24} className="text-red-500" />
                          </Button>
                        </div>
                      </div>
                    </div>
                    <div className={"px-1 flex gap-1 justify-between"}>
                      <form.Field
                        name={"fileName"}
                        validators={{
                          onSubmit: z.string().min(1, "Please enter a file name"),
                        }}
                        children={(field) => (
                          <div className={"flex-1 flex flex-col gap-2"}>
                            <div className={"flex justify-start items-center h-4"}>
                              <Label>File Name</Label>
                            </div>
                            <FormInput field={field} placeholder="Enter file name..." />
                            <FieldInfo field={field} />
                          </div>
                        )}
                      />
                    </div>
                  </div>
                )}

                {uploadStatus.isLoading && (
                  <div>
                    <Progress value={uploadStatus.progress} max={100} className={"h-3"} />
                    <div className={"flex flex-row justify-center"}>
                      <Text size={"sm"} muted>
                        Uploading...
                      </Text>
                    </div>
                  </div>
                )}
              </>
            )}
          />
        </div>

        <div className="flex justify-between gap-2">
          <Button variant="outline" onClick={handleClose} type="button">
            Cancel
          </Button>
          <Button disabled={!form.getFieldValue("document") || isLoading} onClick={form.handleSubmit} type="button">
            {isLoading ? "Uploading..." : "Upload"}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
