import React, {useEffect, useRef, useState} from 'react';
import {Resizable, ResizeCallbackData} from "react-resizable";
import Draggable, {DraggableEvent, DraggableEventHandler} from 'react-draggable';
import {cn} from "@/src/util.ts";
import {Avatar, AvatarFallback, AvatarImage} from "@/src/components/ui/avatar.tsx";
import {getInitials} from "@/src/person.util.ts";
import {DailyTimeRange} from "../../../api/src/timeSchemas.ts";
import {ChevronLeftIcon, ChevronRightIcon, CrownIcon, HouseIcon, NotepadTextIcon, UserRoundIcon} from "lucide-react";
import {imageUrl} from "@/src/images.ts";
import {LaborStatusIcon} from "@/src/components/LaborStatusIcon.tsx";
import {getPersonLaborStatus} from "../../../api/src/personMetrics.ts";
import {to12HourNumber} from "../../../api/src/date.util.ts";
import {
  incrementTo24HourTime,
  incrementToFriendlyTime,
  isHouseOffer,
  ScheduleRowInfo
} from "../../../api/src/scheduleBuilder.util.ts";
import {getChangeStatusBorder} from "@/src/components/ScheduleBuilder.util.tsx";
import {longDash} from "@/src/date.util.ts";
import {isEmpty, map} from "lodash";
import {activityTypeToColorScheme} from "@/src/components/ShiftActivityTypeChooser.tsx";

export interface ScheduleShiftProps {
  rowHeight: number;
  rowIndex: number;
  shift: ScheduleRowInfo;
  incrementWidth: number;
  onShiftEndChange: (areaId: string, shiftId: string, duration: number) => void;
  onShiftStartChange: (areaId: string, shiftId: string, start: number) => void;
  onShiftMoved: (areaId: string, shiftId: string, start: number, end: number) => void;
  onShiftOpened: (areaId: string, shiftId: string) => void;
  storeHours: DailyTimeRange;
  isSelected: boolean;
  selectedActivityId: string | undefined;
  isActivitiesView: boolean;
}

export const ScheduleShift: React.FC<ScheduleShiftProps> = ({
                                                              onShiftEndChange,
                                                              onShiftStartChange,
                                                              rowHeight,
                                                              rowIndex,
                                                              shift,
                                                              onShiftMoved,
                                                              incrementWidth,
                                                              onShiftOpened,
                                                              isSelected, storeHours,
                                                              selectedActivityId,
                                                              isActivitiesView
                                                            }) => {
  const draggableRef = useRef<any>();
  const showActivitiesOnly = isActivitiesView && !isEmpty(shift.activities);

  const shiftStart = shift.start;
  const shiftEnd = shift.end;
  const shiftWidth = (shift.end - shift.start) * incrementWidth;
  const [localShiftStart, setLocalShiftStart] = useState<number>(shiftStart);
  const [localShiftEnd, setLocalShiftEnd] = useState<number>(shiftEnd);
  const localShiftLeft = localShiftStart * incrementWidth;
  const localShiftWidth = (localShiftEnd - localShiftStart) * incrementWidth;
  const cumulativeDeltaWidth = useRef<number>(0);

  useEffect(() => {
    setLocalShiftStart(shiftStart);
  }, [shiftStart]);
  useEffect(() => {
    setLocalShiftEnd(shiftEnd);
  }, [shiftEnd]);

  const onResize = (e: React.SyntheticEvent, {node, size, handle}: ResizeCallbackData) => {
    const deltaWidth = size.width - shiftWidth;
    cumulativeDeltaWidth.current += deltaWidth;
    const numIncrementsMoved = Math.round(Math.abs(cumulativeDeltaWidth.current) / incrementWidth);

    // four cases:
    // 1. pull start handle to the left
    if (handle === "w" && cumulativeDeltaWidth.current > 0) {
      const targetStart = shiftStart - numIncrementsMoved;
      setLocalShiftStart(targetStart);
    }

    // 2. pull start handle to the right
    if (handle === "w" && cumulativeDeltaWidth.current < 0) {
      const targetStart = shiftStart + numIncrementsMoved;
      setLocalShiftStart(targetStart);
    }

    // 3. pull end handle to the left
    if (handle === "e" && cumulativeDeltaWidth.current < 0) {
      const targetEnd = shiftEnd - numIncrementsMoved;
      setLocalShiftEnd(targetEnd);
    }

    // 4. pull end handle to the right
    if (handle === "e" && cumulativeDeltaWidth.current > 0) {
      const targetEnd = shiftEnd + numIncrementsMoved;
      setLocalShiftEnd(targetEnd);
    }
  }
  const onResizeStart = (e: React.SyntheticEvent, data: ResizeCallbackData) => {
    setIsResizing(true);
  }
  const onResizeStop = (e: React.SyntheticEvent, data: ResizeCallbackData) => {

    // give some time for the onClick handler to fire, so that we don't fire
    // onClick after a resizing session has stopped
    setTimeout(() => {
      setIsResizing(false);
    });

    if (localShiftStart !== shift.start) {
      onShiftStartChange(shift.areaId!, shift.id, localShiftStart);
    }

    if (localShiftEnd !== shift.end) {
      onShiftEndChange(shift.areaId!, shift.id, localShiftEnd);
    }

    cumulativeDeltaWidth.current = 0;
  }

  const cumulativeDeltaX = useRef<number>(0);
  const [isDragging, setIsDragging] = useState(false);
  const [isResizing, setIsResizing] = useState(false);
  const onDragStart = () => {
    // react-draggable fires onStart instantly on mouseDown, so we need to
    // delay setting isDragging to true to prevent showing the shift as dragging
    // when the user only intends to click it to open it
    cumulativeDeltaX.current = 0;
  }
  const onDrag: DraggableEventHandler = (e, dragData) => {
    cumulativeDeltaX.current += dragData.deltaX;
    if (Math.abs(cumulativeDeltaX.current) > 15) {
      setIsDragging(true);
    }
  }
  const onDragStop = (e: DraggableEvent, dragData: { x: number }) => {
    // give some time for the onClick handler to fire, so that we don't fire
    // onClick after a dragging session has stopped
    setTimeout(() => {
      setIsDragging(false);
    });

    if (cumulativeDeltaX.current === 0) {
      return;
    }

    cumulativeDeltaX.current = 0;
    const newStart = Math.round(dragData.x / incrementWidth);
    const newEnd = newStart + (localShiftEnd - localShiftStart);

    if (newStart !== localShiftStart || newEnd !== localShiftEnd) {
      setLocalShiftStart(newStart);
      setLocalShiftEnd(newEnd);
      onShiftMoved(shift.areaId!, shift.id, newStart, newEnd);
    }
  };
  const onClick = () => {
    if (!isDragging && !isResizing) {
      onShiftOpened(shift.areaId!, shift.id);
    }
  }

  const {borderClass, color: borderLeftColor} = getChangeStatusBorder(shift.changeStatus);

  const style = {
    width: localShiftWidth,
    borderLeftColor: borderLeftColor
  };

  const onKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" || e.key === " ") {
      onShiftOpened(shift.areaId!, shift.id);
    }
  }

  const positionSuffix = shift.storePositionTitle ? longDash + " " + shift.storePositionTitle : "";
  const isHighlighted = isSelected && !selectedActivityId;

  return (
          <Draggable nodeRef={draggableRef}
                     axis="x"
                     grid={[incrementWidth, rowHeight]}
                     bounds="parent" defaultClassName={"gioa-shift"}
                     cancel={".react-resizable-handle"}
                     position={{x: localShiftLeft, y: 0}}
                     onDrag={onDrag}
                     onStart={onDragStart}
                     onStop={onDragStop}>
            <Resizable axis={"x"}
                       width={shiftWidth}
                       height={rowHeight}
                       onResize={onResize}
                       onResizeStart={onResizeStart}
                       onResizeStop={onResizeStop}
                       resizeHandles={["e", "w"]}
                       handle={(handleAxis, ref) => {
                         const startTimeLabel = incrementToFriendlyTime(storeHours, localShiftStart);
                         const endTimeLabel = incrementToFriendlyTime(storeHours, localShiftEnd);
                         const startLabelWidth = startTimeLabel.length * 8;
                         const endLabelWidth = endTimeLabel.length * 8;

                         return isHighlighted ?
                                 <div style={{
                                   width: 24, top: -1,
                                   ...handleAxis === "w" ? {
                                     left: -4, borderTopLeftRadius: "6px", borderBottomLeftRadius: "6px"
                                   } : {
                                     right: 0, borderTopRightRadius: "6px", borderBottomRightRadius: "6px"
                                   },
                                   bottom: -1
                                 }} ref={ref}
                                      className={cn("absolute group react-resizable-handle cursor-col-resize py-2 flex items-center justify-center bg-amber-300 select-none")}>
                       <span>
                         {handleAxis === "w" ? <ChevronLeftIcon size={16}/> : <ChevronRightIcon size={16}/>}
                       </span>
                                   <div
                                           className={cn("absolute top-1/2 transform -translate-y-1/2 text-sm bg-white rounded-md px-1 text-gray-700 opacity-0 group-hover:opacity-100", {
                                             "opacity-100": isResizing,
                                             "left-[-56px]": handleAxis === "w",
                                             "right-[-56px]": handleAxis === "e"
                                           })}>
                                     {handleAxis === "e" ? endTimeLabel : startTimeLabel}
                                   </div>
                                 </div>
                                 :
                                 <div style={{
                                   width: 20, top: 0,
                                   ...handleAxis === "w" ? {left: -20} : {right: -20},
                                   height: "100%",
                                 }} ref={ref}
                                      className={cn("absolute react-resizable-handle cursor-col-resize py-2 flex opacity-0 hover:opacity-100 select-none", {
                                        "opacity-100": isResizing,
                                        "justify-start": handleAxis === "e",
                                        "justify-end": handleAxis === "w"
                                      })}>
                                   <div className={cn("w-1 h-full bg-gray-400 rounded-full relative", {
                                     "left-[-1px]": handleAxis === "e",
                                     "right-[2px]": handleAxis === "w"
                                   })}/>
                                   <div
                                           className={cn("absolute top-1/2 transform -translate-y-1/2 text-sm bg-white rounded-md px-1 text-gray-700")}
                                           style={{
                                             left: handleAxis === "w" ? -startLabelWidth : undefined,
                                             right: handleAxis === "e" ? -endLabelWidth : undefined
                                           }}>
                                     {handleAxis === "e" ? endTimeLabel : startTimeLabel}
                                   </div>
                                 </div>;
                       }}>
              <button style={style} data-id={shift.id}
                      className={cn("top-0.5 cursor-pointer bottom-0.5 absolute bg-white hover:bg-gray-100 shadow-md rounded-md border-t border-b border-r  active:bg-gray-200 [fieldset:disabled_&]:opacity-50 [fieldset:disabled_&]:cursor-not-allowed",
                              isDragging || isResizing ? "bg-gray-200" : undefined,
                              isDragging ? "opacity-70" : undefined,
                              isSelected ? "border-l-0 border-amber-300 outline outline-2 -outline-offset-2 outline-amber-300" : undefined,
                              borderClass,
                      )}
                      onClick={onClick}
                      ref={draggableRef}>
                <div
                        className={cn("gap-2 cursor-pointer w-full h-full select-none flex items-center px-4 whitespace-nowrap", isHighlighted ? "translate-x-3 transition-transform" : undefined)}>
                  {!showActivitiesOnly ?
                          <div className={"text-sm text-gray-700"}>
                            {to12HourNumber(incrementTo24HourTime(storeHours, shift.start), true)}-{to12HourNumber(incrementTo24HourTime(storeHours, shift.end), true)}
                            {/*{hourFloatToFriendlyDuration(shiftDurationHoursFloat)}*/}
                          </div> : null}
                  {!showActivitiesOnly ?
                    <div style={{pointerEvents: "none"}}>
                    <Avatar size={"xs"}>
                      <AvatarImage src={imageUrl(shift.assignedTo?.profileImageUrl, {width: 64})}
                                   className={"select-none"}
                                   alt=""/>
                      <AvatarFallback className={cn("text-red-600", !shift.assignedTo ? "bg-red-50" : undefined)}>
                        {shift.assignedTo ? getInitials({
                                  firstName: shift.assignedTo?.firstName,
                                  lastName: shift.assignedTo?.lastName
                                }) :
                                shift.shiftOffer && isHouseOffer(shift.shiftOffer) ?
                                        <HouseIcon size={20} className={"text-red-600"}/> :
                                        <UserRoundIcon size={20} className={"text-red-600"}/>
                        }
                      </AvatarFallback>
                    </Avatar>
                  </div> : null}

                  {!showActivitiesOnly ? <>
                    {shift.assignedTo
                            ? <div>{shift.assignedTo.firstName} {shift.assignedTo.lastName}</div>
                            : shift.shiftOffer && isHouseOffer(shift.shiftOffer) ? `House shift` : `Open shift`}

                    {shift.assignedTo ?
                            <LaborStatusIcon size={20}
                                             laborStatus={getPersonLaborStatus(shift.assignedTo.age)}/> : null}

                    {shift.isShiftLead ? <div><CrownIcon size={16}/></div> : null}
                  </> : null}

                  {shift.hasNotes ? <div><NotepadTextIcon size={16}/></div> : null}
                  {positionSuffix ? <div className={"text-gray-600"}>{positionSuffix}</div> : null}
                </div>

                {map(shift.activities, (activity, idx) => {
                  const isActivitySelected = selectedActivityId === activity.id;
                  const isFirst = idx === 0;

                  return <div key={activity.id} className={cn("absolute top-0 bottom-0 border-transparent text-left flex px-1 overflow-hidden hover:overflow-visible items-center whitespace-nowrap rounded-md", {
                    "my-1": !isSelected,
                    "pl-6": isHighlighted && isFirst,
                    "rounded-l-md": activity.start === localShiftStart,
                    "rounded-r-md": activity.end === localShiftEnd,
                    "outline outline-2 outline-blue-600": isActivitySelected
                  })} style={{
                    zIndex: isActivitySelected ? 10 : -100,
                    left: ((activity.start - localShiftStart) * incrementWidth) - (borderClass === "border-l-4" ? 4 : 0),
                    width: (activity.end - activity.start) * incrementWidth,
                    background: activityTypeToColorScheme[activity.activityType]?.shiftRowBg
                  }}>
                    {showActivitiesOnly
                            ? activity.activityType === "setups"
                                    ? activity.setupPositionTitle
                                    : activity.title : null}
                  </div>
                })}
              </button>
            </Resizable>
          </Draggable>
  );
}
