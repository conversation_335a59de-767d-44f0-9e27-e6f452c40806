import React, {useRef} from 'react';
import {api} from "@/src/api.ts";
import {PersonAvatar} from "@/src/components/PersonAvatar.tsx";
import {isEmpty} from "lodash";
import {PersonTimeOffRequestStatusBadge} from "@/src/components/PersonTimeOffRequestStatusBadge.tsx";
import {TimeOffRequestStatusSubtitle} from "@/src/components/TimeOffRequestStatusSubtitle.tsx";
import {ErrorAlert} from "@/src/components/ErrorAlert.tsx";
import {Button, buttonVariants} from "@/src/components/ui/button.tsx";
import {toast} from 'sonner';
import {useForm} from '@tanstack/react-form';
import {zodValidator} from "@tanstack/zod-form-adapter";
import {SheetFooter} from "@/src/components/ui/sheet.tsx";
import {TimeOffRequestHistory} from "@/src/components/TimeOffRequestHistory.tsx";
import {TimeOffRequestApprovalForm} from "@/src/components/TimeOffRequestApprovalForm.tsx";
import {Link} from '@tanstack/react-router';
import {useCancelTimeOff} from "@/src/hooks/useCancelTimeOff.tsx";


export interface TimeOffRequestSheetContentProps {
  timeOffId: string;
  onOpenChange: (open: boolean) => void;
  businessId: string;
  storeId: string;
}

export const TimeOffRequestSheetContent: React.FC<TimeOffRequestSheetContentProps> = ({
                                                                                        businessId,
                                                                                        storeId,
                                                                                        timeOffId,
                                                                                        onOpenChange
                                                                                      }) => {
  const [{
    request: req,
    conflictingShifts,
    lastRequests,
    timezone: tz,
  }] = api.user.getStoreTimeOffRequestDetails.useSuspenseQuery({
    id: timeOffId
  });

  const timezone = tz ?? "America/New_York";
  const apiUtil = api.useUtils();
  const approve = api.user.approveTimeOffRequest.useMutation();
  const decline = api.user.declineTimeOffRequest.useMutation();
  const submitAction = useRef<string>();

  const form = useForm({
    defaultValues: {
      reason: ""
    },
    onSubmit: (e) => {
      if (submitAction.current === "approve") {
        approve.mutate({
          id: timeOffId,
          reason: e.value.reason
        }, {
          onSuccess: () => {
            toast.success("Time off request approved", {
              position: "top-center"
            });
            apiUtil.user.getStoreTimeOffRequestDetails.invalidate();
            apiUtil.user.getStoreTimeOffRequests.invalidate();
            apiUtil.user.getAllSchedulePeopleAtStore.invalidate();
            onOpenChange(false);
          }
        })
      } else if (submitAction.current === "decline") {
        decline.mutate({
          id: timeOffId,
          reason: e.value.reason
        }, {
          onSuccess: () => {
            apiUtil.user.getStoreTimeOffRequestDetails.invalidate();
            apiUtil.user.getStoreTimeOffRequests.invalidate();
            toast.success("Time off request declined", {
              position: "top-center"
            })
            onOpenChange(false);
          }
        })
      }
    },
    validatorAdapter: zodValidator()
  })
  const onCancelTimeOff = useCancelTimeOff(req.id);

  return (
    <div>
      <div className={"flex flex-row justify-center"}>
        <div className={"px-4 text-center"}>
          <PersonAvatar person={req.person} size={"3xl"}
                        className={"inline-block mb-2"}/>
          <div>
            {req.person.firstName} {req.person.lastName}
          </div>
          <div className={"text-sm"}>
            {req.person.jobTitle}
          </div>
        </div>
      </div>

      <section className="p-4">
        <PersonTimeOffRequestStatusBadge status={req.status} size={"md"}/>

        <TimeOffRequestStatusSubtitle request={req}/>

        {approve.isError && <ErrorAlert error={approve.error}/>}
        {decline.isError && <ErrorAlert error={decline.error}/>}

        <TimeOffRequestApprovalForm request={req} timezone={timezone}
                                    businessId={businessId} storeId={storeId}
                                    conflictingShifts={conflictingShifts} form={form}>
          <SheetFooter>
            <fieldset disabled={approve.isPending || decline.isPending}
                      className={"flex gap-3 items-center"}>
              <Button type={"button"} isLoading={approve.isPending}
                      onClick={() => {
                        submitAction.current = "approve"
                        form.handleSubmit();
                      }}
                      id={"approve-btn"}>
                Approve
              </Button>
              <Button type={"button"} isLoading={decline.isPending}
                      variant={"outline"}
                      onClick={() => {
                        submitAction.current = "decline";
                        form.handleSubmit();
                      }}
                      id={"decline-btn"}>
                Decline
              </Button>
              <Link className={buttonVariants({variant: "outline"})}
                    to={`/${businessId}/${storeId}/requests/timeOff/edit/$timeOffId` as any}
                    params={{timeOffId: req.id} as any}
              >
                Edit
              </Link>
            </fieldset>
          </SheetFooter>
        </TimeOffRequestApprovalForm>

        {req.status === "approved" && <div className={"flex gap-2"}>
            <Link className={buttonVariants({})}
                  to={`/${businessId}/${storeId}/requests/timeOff/edit/$timeOffId` as any}
                  params={{timeOffId: req.id} as any}>
                Edit
            </Link>
            <Button variant={"outline"} onClick={onCancelTimeOff}>
                Cancel Time Off
            </Button>
        </div>}
      </section>


      {!isEmpty(lastRequests) &&
          <div className={"bg-gray-100 p-4"}>
              <TimeOffRequestHistory timezone={timezone}
                                     lastRequests={lastRequests}
                                     linkTo={"../../requests/timeOff/"}/>
          </div>
      }
    </div>
  );
}
