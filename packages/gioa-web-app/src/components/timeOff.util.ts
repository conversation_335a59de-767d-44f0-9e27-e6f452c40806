import {endOfDay, startOfDay} from "date-fns";
import {DateTimeRange} from "../../../api/src/timeSchemas.ts";
import {convertLocalTimeToTimezone} from "../../../api/src/date.util.ts";
import {parseHtmlDateInputToLocalDate, parseHtmlTimeInputToLocalTime} from "../../../api/src/scheduleBuilder.util.ts";

export function getTimeOffRangeFromFormInputs(params: {
  isAllDay: boolean;
  isMultipleDays: boolean;
  startDate: string | undefined;
  startTime: string | undefined;
  endDate: string | undefined;
  endTime: string | undefined;
}, timezone: string | null): DateTimeRange | undefined {
  const {isAllDay, isMultipleDays} = params;
  if (!params.startDate) {
    return;
  }

  const startDate = parseHtmlDateInputToLocalDate(params.startDate);
  const startTime = params.startTime ? parseHtmlTimeInputToLocalTime(params.startTime) : undefined;
  const endDate = params.endDate ? parseHtmlDateInputToLocalDate(params.endDate) : undefined;
  const endTime = params.endTime ? parseHtmlTimeInputToLocalTime(params.endTime) : undefined;

  const start = startOfDay(startDate);
  if (!isAllDay) {
    if (!startTime) {
      return;
    }
    start.setHours(startTime.getHours());
    start.setMinutes(startTime.getMinutes());
    start.setSeconds(0);
    start.setMilliseconds(0);
  }
  const end = isMultipleDays && endDate ? endOfDay(endDate) : endOfDay(startDate);
  if (!isAllDay) {
    if (!end || !endTime) {
      return;
    }
    end.setHours(endTime.getHours());
    end.setMinutes(endTime.getMinutes());
    end.setSeconds(0);
    end.setMilliseconds(0);
  }

  // We assume that the team member is requesting time off in their store's timezone. Not their local device timezone.
  const tz = timezone ?? "America/New_York";
  return {
    start: convertLocalTimeToTimezone(start, tz),
    end: convertLocalTimeToTimezone(end, tz)
  }
}
