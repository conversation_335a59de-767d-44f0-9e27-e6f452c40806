import React from "react";
import {useForm} from "@tanstack/react-form";
import {FormControl} from "@/src/components/form/FormControl.tsx";
import {Label} from "@/src/components/ui/label.tsx";
import {FormTextarea} from "@/src/components/form/FormTextarea.tsx";
import {FieldInfo} from "@/src/components/form/FieldInfo.tsx";
import {Button} from '@/src/components/ui/button.tsx';
import {format} from "date-fns";
import {find, isEmpty, map} from "lodash";
import {FormCheckbox} from '@/src/components/form/FormCheckbox.tsx';
import {cn} from "@/src/util.ts";
import {FormInput} from '@/src/components/form/FormInput.tsx';
import {getTimeOffRangeFromFormInputs} from "@/src/components/timeOff.util.ts";
import {FormDropDown} from "@/src/components/form/FormDropDown.tsx";
import {FormTeamMemberCombobox} from "@/src/components/form/FormTeamMemberCombobox.tsx";
import {api} from "@/src/api.ts";
import {DateTimeRange} from "../../../api/src/timeSchemas.ts";
import {SchedulePersonDto} from "../../../api/src/schedulePersonDto.ts";

export interface TimeOffFormValues {
  personId: string;
  isAllDay: boolean;
  isMultipleDays: boolean;
  startDate: string | undefined;
  startTime: string | undefined;
  endDate: string | undefined;
  endTime: string | undefined;
  timeOffType?: string;
  reason?: string;
}

export interface TimeOffFormProps {
  hidePersonSelect?: boolean;
  initialValues?: TimeOffFormValues;
  onSubmit: (values: TimeOffFormValues, range: DateTimeRange, person: SchedulePersonDto) => void;
  submitButtonText: string;
  storeId: string;
  isPending: boolean;
}

export function TimeOffRequestForm({
                                     storeId,
                                     isPending, hidePersonSelect,
                                     initialValues = {
                                       personId: "",
                                       isAllDay: false,
                                       isMultipleDays: true,
                                       startDate: undefined as string | undefined,
                                       startTime: undefined as string | undefined,
                                       endDate: undefined as string | undefined,
                                       endTime: undefined as string | undefined,
                                       timeOffType: undefined as string | undefined,
                                       reason: undefined as string | undefined,
                                     },
                                     onSubmit,
                                     submitButtonText = "Submit",
                                   }: TimeOffFormProps) {
  const [{people, timezone}] = api.user.getAllSchedulePeopleAtStore.useSuspenseQuery({storeId: storeId}, {
    staleTime: 1000 * 60 * 60
  });
  const [timeOffTypes] = api.user.getTimeOffTypes.useSuspenseQuery({storeId});

  const form = useForm({
    defaultValues: initialValues,
    onSubmit: (e) => {
      const range = getTimeOffRangeFromFormInputs(e.value, timezone ?? null);
      if (!range) {
        return;
      }

      const person = find(people, p => p.id === e.value.personId);
      if (!person) {
        return;
      }

      onSubmit(e.value, range, person);
    },
    validators: {
      onSubmit: ({value}) => {
        const {isAllDay, isMultipleDays, startTime, endTime, startDate, personId, endDate} = value;

        if (isEmpty(personId)) {
          return "Please select a team member"
        }

        if (!startDate) {
          return "Missing start date"
        }

        if (isMultipleDays) {
          if (!endDate) {
            return "Missing end date"
          }
          if (format(new Date(), "yyyy-MM-dd") > format(endDate, "yyyy-MM-dd")) {
            return "End Date cannot be in the past"
          }
          if (endDate < startDate) {
            return "End Date cannot be before Start Date";
          }
        }

        if (!isAllDay) {
          if (!startTime || !endTime) {
            return "Missing start time and/or end time"
          }
          if (!isMultipleDays && endTime < startTime) {
            return "End Time cannot be before Start Time";
          }
        }

        return undefined
      }
    }
  })

  const isMultipleDays = form.useStore((state) => state.values.isMultipleDays)
  const isAllDay = form.useStore((state) => state.values.isAllDay)
  const formErrorMap = form.useStore((state) => state.errorMap)

  return (
    <form onSubmit={(e) => {
      e.preventDefault()
      e.stopPropagation()
      form.handleSubmit()
    }} className={"mb-4"}>
      {!hidePersonSelect ?
        <form.Field name={"personId"}
                   children={field => <FormControl>
                     <FormTeamMemberCombobox people={people} className={"flex"} listClassName="max-h-[60vh]"
                                             field={field}/>
                     <FieldInfo field={field}/>
                   </FormControl>}/> : null}

      <div className={"mb-6 space-y-2"}>
        <h2 className={"mb-4 text-lg font-semibold"}>Details</h2>
        <form.Field name={"isAllDay"} children={(field) => (
          <FormCheckbox label={"All Day"} field={field}/>
        )}/>
        <form.Field name={"isMultipleDays"} children={(field) => (
          <FormCheckbox label={"Multiple Days"} field={field}/>
        )}/>
      </div>
      <div className={"mb-6"}>
        <h2 className={"mb-4 text-lg font-semibold"}>When</h2>
        <div className={cn("flex flex-row gap-4")}>
          <div className={cn("flex-1")}>
            <form.Field name={"startDate"}
                        children={field => <FormControl>
                          <Label>{isMultipleDays ? "Start Date" : "Date"}</Label>
                          <FormInput field={field}
                                     type={"date"}
                                     placeholder={"Start date..."}/>
                          <FieldInfo field={field}/>
                        </FormControl>}/>
          </div>
          <div className={cn("flex-1")}>
            <div className={isMultipleDays ? "" : "hidden"}>
              <form.Field name={"endDate"}
                          children={field => <FormControl>
                            <Label>End Date</Label>
                            <FormInput field={field}
                                       placeholder={"End date..."}
                                       type={"date"}/>
                            <FieldInfo field={field}/>
                          </FormControl>}/>
            </div>
          </div>

        </div>
        <div className={cn("flex flex-row gap-4")}>
          <div className={cn("flex-1")}>
            <div className={isAllDay ? "hidden" : ""}>
              <form.Field name={"startTime"}
                          children={field => <FormControl>
                            <Label>Start time</Label>
                            <FormInput field={field}
                                       type={"time"}
                                       placeholder={"Start time..."}/>
                            <FieldInfo field={field}/>
                          </FormControl>}/>
            </div>
          </div>
          <div className={cn("flex-1")}>
            <div className={isAllDay ? "hidden" : ""}>
              <form.Field name={"endTime"}
                          children={field => <div>
                            <Label>End time</Label>
                            <FormInput field={field}
                                       type={"time"}
                                       placeholder={"End time..."}/>
                            <FieldInfo field={field}/>
                          </div>}/>
            </div>
          </div>
        </div>
        <form.Field name={"timeOffType"}
                    children={field => <FormControl>
                      <Label>Type</Label>
                      <FormDropDown field={field}
                                    placeholder={"Select a reason..."}
                                    options={map(timeOffTypes.types, type => ({
                                      label: type.title,
                                      value: type.title
                                    }))}/>
                    </FormControl>}/>
        <form.Field name={"reason"}
                    children={field => <FormControl>
                      <Label>Reason for request</Label>
                      <FormTextarea field={field}
                                    placeholder={"Reason for requesting time off..."}/>
                    </FormControl>}/>


        <div>
          {formErrorMap.onSubmit && typeof formErrorMap.onSubmit === "string" ? (
            <div className={"text-red-600"}>{formErrorMap.onSubmit}</div>
          ) : null}
        </div>
      </div>

      <Button type={"submit"} isLoading={isPending}>
        {submitButtonText}
      </Button>
    </form>
  );
}
