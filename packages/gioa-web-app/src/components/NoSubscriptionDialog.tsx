import React, {useState} from 'react';
import {<PERSON>alog, DialogContent, DialogDescription, DialogHeader, DialogTitle} from "@/src/components/ui/dialog.tsx";
import {Button} from "@/src/components/ui/button.tsx";
import {Text} from "@/src/components/Text.tsx";
import {PlanChooser} from "@/src/components/PlanChooser.tsx";
import {api} from "@/src/api.ts";
import {getHumanReadableErrorMessageString} from "@/src/components/ErrorAlert.tsx";

import {PriceCommitmentType} from "../../../api/src/payments/priceSchemas.ts";

interface NoSubscriptionDialogProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  storeId: string;
}

export function NoSubscriptionDialog({
                                       isOpen,
                                       onOpenChange,
                                       storeId,
                                     }: NoSubscriptionDialogProps) {
  const [selectedPlan, setSelectedPlan] = useState<PriceCommitmentType>();

  const startCheckoutSession = api.payment.startCheckoutSession.useMutation();
  const pricesQuery = api.payment.getPrices.useQuery();

  const handleContinue = () => {
    if (!selectedPlan) {
      return;
    }

    startCheckoutSession.mutate({
      storeId: storeId,
      commitmentType: selectedPlan,
    }, {
      onSuccess: (data) => {
        window.location.href = data.url;
      },
      onError: (error) => {
        alert("Failed to start payment setup: " + getHumanReadableErrorMessageString(error));
      }
    });
  };

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent size="2xl">
        <DialogHeader>
          <DialogTitle className="text-center">Get Started with Nation</DialogTitle>
          <DialogDescription className="text-center">
            Choose your plan to start managing your store's scheduling.
          </DialogDescription>
        </DialogHeader>

        <PlanChooser
          selectedPlan={selectedPlan}
          onPlanChange={setSelectedPlan}
          pricesData={pricesQuery.data}
        />

        <Text className="text-center">
          You'll add a payment method and start your subscription in the next step.
        </Text>

        <div className="justify-center flex-1 pt-6 pb-4 flex">
          <Button size={"lg"}
                  onClick={handleContinue}
                  isLoading={startCheckoutSession.isPending}
                  disabled={startCheckoutSession.isPending || !selectedPlan}
                  className="bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700"
          >
            Continue with {selectedPlan === 'monthly'
            ? 'Monthly'
            : selectedPlan === 'annual' ?
              'Annual' : "Selected"} Plan
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
