import React, {useEffect} from 'react';
import {<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ooter, <PERSON><PERSON><PERSON>eader, DialogTitle} from "@/src/components/ui/dialog.tsx";
import {Button} from "@/src/components/ui/button.tsx";
import {DialogDescription} from "@radix-ui/react-dialog";
import {zodValidator} from "@tanstack/zod-form-adapter";
import {useForm} from "@tanstack/react-form";
import {FormControl} from "@/src/components/form/FormControl.tsx";
import {Label} from "@/src/components/ui/label.tsx";
import {FieldInfo} from "@/src/components/form/FieldInfo.tsx";
import {DayOfWeek} from "../../../api/src/timeSchemas.ts";
import {z} from "zod";
import {Text} from "@/src/components/Text.tsx";
import {ToggleGroup, ToggleGroupItem} from "@/src/components/ui/toggle-group.tsx";
import {find, keys, map, pickBy, reduce} from "lodash";
import {daysOfWeek} from "@/src/components/AvailabilityWeek.tsx";
import {Checkbox} from "@/src/components/ui/checkbox.tsx";
import {DraftSchedule} from "../../../api/src/scheduleSchemas.ts";
import {FormCheckbox} from "@/src/components/form/FormCheckbox.tsx";
import {useAutoAnimate} from "@formkit/auto-animate/react";

export interface CopyDayDialogProps {
  onCopy: (value: {
    days: number[]
    copyShiftAssignments: boolean;
    includeAreaIds: string[];
    shouldFilterAreas: boolean;
  }) => void;
  isOpen: boolean;
  onClose: () => void;
  onOpen: () => void;
  dayOfWeek: DayOfWeek;
  schedule: DraftSchedule;
}

export const CopyDayDialog: React.FC<CopyDayDialogProps> = (props) => {
  const scheduleDay = find(props.schedule.days, d => d.dayOfWeek === props.dayOfWeek);
  const scheduleAreas = scheduleDay?.areas ?? [];

  const form = useForm({
    defaultValues: {
      days: [] as number[],
      shouldFilterAreas: false,
      copyShiftAssignments: true,
      includeAreaIds: reduce(scheduleAreas, (acc, area) => {
        acc[area.id] = area.shifts?.length > 0;
        return acc;
      }, {} as Record<string, boolean>)
    },
    onSubmit: async ({value}) => {
      props.onCopy({
        days: value.days,
        copyShiftAssignments: value.copyShiftAssignments,
        includeAreaIds: keys(pickBy(value.includeAreaIds, v => v)),
        shouldFilterAreas: value.shouldFilterAreas
      });
    },
    validatorAdapter: zodValidator(),
  });

  useEffect(() => {
    if (!props.isOpen) {
      // give a bit of time for the modal to animate out
      setTimeout(() =>
        form.reset(), 100);
    }
  }, [props.isOpen]);

  const onCancel = () => {
    props.onClose();
  }

  const dayOfWeekObj = find(daysOfWeek, d => d.dayOfWeek === props.dayOfWeek);
  const shouldFilterAreas = form.useStore(state => state.values.shouldFilterAreas);
  const [autoContainerRef] = useAutoAnimate();

  return (
    <Dialog open={props.isOpen} onOpenChange={open => {
      if (open) {
        props.onOpen();
      } else {
        onCancel();
      }
    }}>
      <DialogContent className={"overflow-auto"} style={{maxHeight: "calc(100vh - 50px)"}}>
        <DialogHeader>
          <DialogTitle>Copy Day</DialogTitle>
          <DialogDescription>
            Copy {dayOfWeekObj?.name} to other days of the week. All shifts in the areas selected below will be
            overwritten by the template.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          <form onSubmit={(e) => {
            e.preventDefault()
            e.stopPropagation()
            form.handleSubmit()
          }}>
            <div className={"mb-3"}>
              <form.Field name={`days`}
                          validators={{
                            onSubmit: z.array(z.number()).min(1)
                          }}
                          children={(field) => {
                            return <FormControl>
                              <Label htmlFor={field.name}>Copy {dayOfWeekObj?.name} to days</Label>
                              <ToggleGroup value={map(field.state.value, d => d.toString())}
                                           onValueChange={dayStrs => field.handleChange(map(dayStrs, Number))}
                                           onBlur={field.handleBlur}
                                           type="multiple" variant={"outline"} colorScheme={"primary"}
                                           className={"justify-start"}>
                                <ToggleGroupItem value="1" disabled={props.dayOfWeek === 1}>Mon</ToggleGroupItem>
                                <ToggleGroupItem value="2" disabled={props.dayOfWeek === 2}>Tue</ToggleGroupItem>
                                <ToggleGroupItem value="3" disabled={props.dayOfWeek === 3}>Wed</ToggleGroupItem>
                                <ToggleGroupItem value="4" disabled={props.dayOfWeek === 4}>Thu</ToggleGroupItem>
                                <ToggleGroupItem value="5" disabled={props.dayOfWeek === 5}>Fri</ToggleGroupItem>
                                <ToggleGroupItem value="6" disabled={props.dayOfWeek === 6}>Sat</ToggleGroupItem>
                                <ToggleGroupItem value="7" disabled={props.dayOfWeek === 7}>Sun</ToggleGroupItem>
                              </ToggleGroup>
                              <FieldInfo field={field}/>
                            </FormControl>;
                          }}/>
              <form.Field name={`copyShiftAssignments`}
                          children={(field) => {
                            return <div
                              className={"flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4 bg-white mb-3"}>
                              <Checkbox id={field.name}
                                        onCheckedChange={isChecked => typeof isChecked === "boolean" ? field.handleChange(isChecked) : null}
                                        checked={field.state.value}>
                              </Checkbox>
                              <div className="space-y-1 leading-none">
                                <Label htmlFor={field.name} className={"block"}>
                                  Copy shift assignments
                                </Label>
                                <Text size={"sm"} className={"block"}>
                                  Select to copy the team members with the shifts.
                                </Text>
                              </div>
                              <FieldInfo field={field}/>
                            </div>
                          }}/>

              <div className="border rounded-lg">
                <form.Field name={`shouldFilterAreas`}
                            children={(field) => <FormControl className={"border-b p-4 m-0"}>
                              <FormCheckbox field={field}
                                            label={"Filter areas"}/>
                              <FieldInfo field={field}/>
                            </FormControl>}/>


                <div ref={autoContainerRef}>
                  {shouldFilterAreas ?
                    <div className={"p-4 space-y-4"} key={"areas"}>
                      {map(scheduleAreas, area => {
                        return <form.Field key={area.id} name={`includeAreaIds.${area.id}`}
                                           children={(field) => <FormControl className={"m-0"}>
                                             <FormCheckbox field={field}
                                                           label={<span>{area.title} <span
                                                             className={"text-muted-foreground"}>({area.shifts?.length} shifts in template)</span></span>}/>
                                             <FieldInfo field={field}/>
                                           </FormControl>}/>
                      })}
                    </div> : <div className={"text-muted-foreground text-sm p-4"} key={"explainer"}>
                      Select "Filter areas" to
                      allow selecting areas to restrict the copy to.
                    </div>}
                </div>

              </div>
            </div>

            <DialogFooter>
              <Button variant={"outline"} type={"button"}
                      onClick={onCancel}>
                Cancel
              </Button>
              <Button type={"submit"}>
                Copy Day
              </Button>
            </DialogFooter>
          </form>
        </div>
      </DialogContent>
    </Dialog>
  );
}
