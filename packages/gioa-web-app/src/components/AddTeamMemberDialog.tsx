import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/src/components/ui/dialog.tsx";
import { Button } from "@/src/components/ui/button.tsx";
import { Text } from "@/src/components/Text.tsx";
import React, {useLayoutEffect} from "react";
import {AddTeamMemberManual} from "@/src/components/AddTeamMemberManual.tsx";
import {AddTeamMembersBulk} from "@/src/components/AddTeamMembersBulk.tsx";

interface AddTeamMemberDialogProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  storeId?: string;
}

export function AddTeamMemberDialog({
  isOpen,
  onOpenChange,
  storeId,
}: AddTeamMemberDialogProps) {
  const [step, setStep] = React.useState<"Intro" | "Manual" | "Upload">("Intro");

  useLayoutEffect(() => {
    setStep("Intro");
  }, [isOpen]);

  const onClose = () => onOpenChange(false);
  const avoidDefaultDomBehavior = (e: Event) => {
    e.preventDefault();
  };

  const handleBackToIntro = () => {
    setStep("Intro");
  };

  const Intro = () => {
    return <div className="space-y-4">
      <div onClick={() => setStep("Upload")} className={"border border-gray-300 rounded-xl p-4 hover:bg-gray-50 cursor-pointer"}>
        <div className="space-y-1">
          <Text semibold>Bulk upload team members</Text>
          <Text muted>Import team members from CFA Home</Text>
        </div>
      </div>
      <div onClick={() => setStep("Manual")} className={"border border-gray-300 rounded-xl p-4 hover:bg-gray-50 cursor-pointer"}>
        <div className="space-y-1">
          <Text semibold>Add team member manually</Text>
          <Text muted>Enter individual team member information</Text>
        </div>
      </div>
      <div className={"flex flex-row justify-end"}>
        <Button variant={"outline"} onClick={onClose}>
          Cancel
        </Button>
      </div>
    </div>
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent size={"xl"} className="overflow-y-auto min-h-[400px]" style={{ maxHeight: "calc(100vh - 100px)", maxWidth: step === "Upload" ? "70vw" : undefined }} onInteractOutside={avoidDefaultDomBehavior}>
        <DialogHeader>
          <DialogTitle>
            {step === "Intro" ? "Add Team Member" :
             step === "Manual" ? "Add Team Member Manually" :
             "Bulk Upload Team Members"}
          </DialogTitle>
        </DialogHeader>
        <hr />
        {step === "Intro" ? <Intro /> : null}
        {step === "Manual" ? (
          <AddTeamMemberManual
            storeId={storeId!}
            onClose={handleBackToIntro}
          />
        ) : null}
        {step === "Upload" ? <AddTeamMembersBulk storeId={storeId!}
                                                 onClose={handleBackToIntro}
                                                 onFinish={onClose}/> : null}
      </DialogContent>
    </Dialog>
  );
}
