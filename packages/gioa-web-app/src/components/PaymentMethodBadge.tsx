import React from 'react';
import {Badge} from "@/src/components/ui/badge.tsx";

export interface PaymentMethodBadgeProps {
  hasPaymentMethod: boolean;
}

export const PaymentMethodBadge: React.FC<PaymentMethodBadgeProps> = ({hasPaymentMethod}) => {
  if (hasPaymentMethod) {
    return <Badge colorScheme="success">
      Yes
    </Badge>
  }

  return <Badge colorScheme="destructive">
    No
  </Badge>
}
