import React from 'react';
import {Text} from '@/src/components/Text';
import {Calendar} from "@/src/components/ui/calendar.tsx";
import {Button} from "@/src/components/ui/button.tsx";
import {ChevronDown, ChevronRight, CirclePlus} from "lucide-react";
import {filter, map, sortBy} from "lodash";
import {api} from "@/src/api.ts";
import {DateTime} from "luxon";
import {formatAbsDateRangeFriendly} from "@/src/date.util.ts";
import {ScheduleCalendarColors, ScheduleEventType} from "../../../api/src/scheduleCalendars.ts";
import {EditEventPopover} from "@/src/components/EditEventPopover.tsx";
import {ScheduleEventDto} from "../../../api/src/scheduleEventSchemas.ts";
import {genScheduleEventId} from "../../../api/src/schemas.ts";
import {toast} from 'sonner';
import {FilterEventsFormValues, FilterEventTypesPopover} from "@/src/components/FilterEventsPopover.tsx";
import {Badge} from "@/src/components/ui/badge.tsx";

const getWeekDaysStartingFromSunday = (date: Date): Date[] => {
  const start = date.getDate() - date.getDay(); // Start with Sunday
  return Array.from({length: 7}, (_, i) => new Date(date.getFullYear(), date.getMonth(), start + i));
};

interface CalendarStoreEventsProps {
  storeId: string;
  timezone: string | null;
}

const buildNewEvent = (startDate?: Date): ScheduleEventDto => {
  const start = startDate ?? DateTime.now().toJSDate();
  const end = new Date(start.getTime() + 1000 * 60 * 60);
  return {
    id: genScheduleEventId(),
    title: "",
    description: "",
    range: {start, end},
    eventType: "storeEvent",
    visibilityLevel: 0,
  }
};

export const CalendarStoreEvents: React.FC<CalendarStoreEventsProps> = ({storeId, timezone}) => {
  const [date, setDate] = React.useState<Date>(new Date());
  const [activeEvent, setActiveEvent] = React.useState<ScheduleEventDto>(buildNewEvent());

  const [filterValues, setFilterValues] = React.useState<FilterEventsFormValues>({eventTypes: []});
  const defaultFilterValues: FilterEventsFormValues = {
    eventTypes: [],
  }

  const apiUtil = api.useUtils();
  const upsertScheduleEventMutation = api.user.upsertScheduleEvent.useMutation({
    onSuccess: () => {
      apiUtil.user.getScheduleEvents.invalidate({
        storeId: storeId
      });
    }
  });
  const deleteScheduleEventMutation = api.user.deleteScheduleEvent.useMutation({
    onSuccess: () => {
      apiUtil.user.getScheduleEvents.invalidate({
        storeId: storeId
      });
    }
  });

  const selectedDateTime = date ? DateTime.fromJSDate(date) : undefined;
  const weeklyScheduleEvents = api.user.getScheduleEvents.useQuery({
    week: {
      year: selectedDateTime?.weekYear ?? new Date().getFullYear(),
      week: selectedDateTime?.weekNumber ?? DateTime.local().weekNumber
    },
    storeId: storeId
  }, {
    enabled: Boolean(selectedDateTime),
    staleTime: 1000 * 60 * 15 // 15 minutes
  });

  const displayedEvents = filter(weeklyScheduleEvents.data, event => {
    if (!filterValues.eventTypes.length) {
      return true;
    }
    return filterValues.eventTypes.includes(event.eventType as ScheduleEventType);
  });

  const weekDays = date ? getWeekDaysStartingFromSunday(date) : [];

  const onUpsertEvent = (event: ScheduleEventDto) => {
    upsertScheduleEventMutation.mutate({
      id: event.id,
      storeId: storeId,
      title: event.title,
      description: event.description,
      range: event.range,
      eventType: event.eventType,
      visibilityLevel: event.visibilityLevel,
      isTimeOffRestricted: event.isTimeOffRestricted ?? false,
    }, {
      onError: () => {
        toast.error("An error occurred trying to save your event update to the server. Please try again later.");
      }
    });

    toast.success("Event updated", {
      closeButton: true
    });
  }

  const onDeleteEvent = (event: ScheduleEventDto) => {
    deleteScheduleEventMutation.mutate({
      id: event.id
    }, {
      onError: () => {
        toast.error("An error occurred trying to delete the event on the server. Please try again later.");
      }
    });

    toast.success("Event deleted", {
      closeButton: true
    });
  }

  return (
    <div className="bg-white p-6 rounded-lg shadow flex flex-col gap-3">
      <div className={"border border-gray-200 rounded-lg flex flex-col justify-center items-center"}>
        <Calendar
          mode="single"
          selected={date}
          onSelect={(selectedDate) => {
            if (selectedDate) {
              setDate(selectedDate);
            }
          }}
          onMonthChange={(selectedMonth) => {
            setDate(selectedMonth);
          }}
          showOutsideDays
          classNames={{
            day: "h-9 w-9 p-0 font-normal ",
          }}
          modifiers={{
            weekSelected: weekDays,
            weekSelectedStart: [weekDays[0]], // Sunday
            weekSelectedEnd: [weekDays[6]], // Saturday
          }}
          modifiersClassNames={{
            outside: "text-primary-200",
            weekSelected: "bg-blue-50 border-blue-300 text-blue-300",
            weekSelectedStart: "rounded-l-full", // Round left edges (Sunday)
            weekSelectedEnd: "rounded-r-full", // Round right edges (Saturday)
            selected: "bg-blue-500 text-white",
          }}
        />
      </div>
      <div className="flex flex-col sm:flex-row gap-1">
        <EditEventPopover event={activeEvent} timezone={timezone}
                          onUpsertEvent={onUpsertEvent} storeId={storeId}
                          onDeleteEvent={onDeleteEvent}>
          <Button className={"flex-1"} variant={"outline"} onClick={() => {
            // If it's today, start the event at the current time. Otherwise, start it at 9am.
            const isToday = date.getDate() === new Date().getDate();
            const eventStart = isToday ? new Date() : new Date(date.getTime() + 9 * 60 * 60 * 1000);
            setActiveEvent(buildNewEvent(eventStart));
          }}>
            <div className="flex flex-row justify-between items-center w-full">
              <Text size={"xs"}>Add Event</Text>
              <CirclePlus size={18}/>
            </div>
          </Button>
        </EditEventPopover>
        <FilterEventTypesPopover initialValues={filterValues}
                                 defaultValues={defaultFilterValues}
                                 onSubmit={setFilterValues}>
          <div className={"flex-1 relative"}>
            <Button variant={"outline"} className={"w-full"}>
              <div className="flex flex-row justify-between items-center w-full">
                <Text size={"xs"}>Filter</Text>
                <ChevronDown size={18}/>
              </div>
            </Button>
            {filterValues.eventTypes.length > 0 ? <div className={"absolute"} style={{top: -3, right: -5}}>
              <Badge size={"sm"} colorScheme={"destructive"}
                     className={"min-h-5 min-w-5 ml-2 px-1 flex items-center justify-center"}>
                {filterValues.eventTypes.length}
              </Badge>
            </div> : null}
          </div>
        </FilterEventTypesPopover>
      </div>
      <div>
        {!displayedEvents.length ? <div className={"flex flex-col items-center"}>
          <Text muted className={"my-4"}>
            {filterValues.eventTypes.length > 0 ? "No events matching your filters" : "No events for this week"}
          </Text>
        </div> : null}
        {map(sortBy(displayedEvents, event => event.range.start), (event) => (
          <EditEventPopover key={event.id} event={event} storeId={storeId}
                            timezone={timezone} side={"left"}
                            onUpsertEvent={onUpsertEvent}
                            onDeleteEvent={onDeleteEvent}>
            <div
              className={"flex flex-row mb-2 items-center border border-gray-200 rounded-lg border-l-8 pl-3 pr-2 py-2"}
              style={{borderLeftColor: ScheduleCalendarColors[(event.eventType ?? "storeEvent") as ScheduleEventType]}}
            >
              <div className={"flex-1 flex flex-col"}>
                <Text>
                  {event.title}
                </Text>
                <Text size={"sm"} muted>
                  {formatAbsDateRangeFriendly(event.range.start, event.range.end, new Date())}
                </Text>
              </div>
              <ChevronRight/>
            </div>
          </EditEventPopover>
        ))}
      </div>
    </div>
  );
};

export default CalendarStoreEvents;
