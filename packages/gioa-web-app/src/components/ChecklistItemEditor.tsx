import React, {useState} from 'react';
import {useForm} from '@tanstack/react-form';
import {zodValidator} from '@tanstack/zod-form-adapter';
import {z} from 'zod';
import {Button} from '@/src/components/ui/button';
import {FormControl} from '@/src/components/form/FormControl';
import {Label} from '@/src/components/ui/label';
import {FormInput} from '@/src/components/form/FormInput';
import {FieldInfo} from '@/src/components/form/FieldInfo';
import {FormTextarea} from '@/src/components/form/FormTextarea';
import {Text} from '@/src/components/Text';
import {PlusIcon, Trash2Icon} from 'lucide-react';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/src/components/ui/alert-dialog';
import {Spinner} from '@/src/components/Spinner';
import {compact, map, uniq} from 'lodash';
import {AttachmentValue, FormAttachmentInput} from './form/FormAttachmentInput';
import {ChecklistRequirementFormRow} from './checklists2/ChecklistRequirementFormRow';
import {ChecklistItemFormRow} from './ChecklistItemFormRow';
import {BooleanInputDialog} from './checklists2/BooleanInputDialog';
import {NumberInputDialog} from './checklists2/NumberInputDialog';
import {AddInstructionsDialog} from './AddInstructionsDialog';
import {FormTextareaInline} from './form/FormTextareaInline';
import {useFormAttachmentUpload} from '@/src/hooks/useFormAttachmentUpload';
import {Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle,} from '@/src/components/ui/dialog';
import {toast} from 'sonner';
import {getInstructionDetails} from '@/src/utils/checklist.util.tsx';
import {AttachmentMediaType, FileAttachmentDto} from "../../../api/src/fileAttachment.dto.ts";
import {
  ChecklistItemInstructionUploadDto,
  ChecklistTemplateItemDto,
  ItemRequirementDto
} from "../../../api/src/checklists/checklistTemplate/checklistTemplateDtos.ts";
import {genChecklistRequirementId} from '@gioa/api/src/checklist.schemas.ts';
import {requirementOptions} from './checklists2/checklistRequirements.tsx';
import {useAutoAnimate} from "@formkit/auto-animate/react";

export interface FormChecklistInstruction {
  id: string;
  text?: string;
  attachment?: AttachmentValue;
}

export interface GetPresignedPostParams {
  storeId: string;
  contentType: string;
  mediaType: AttachmentMediaType;
}

export interface UpsertItemParams {
  title: string;
  description?: string;
  attachment?: FileAttachmentDto;
  requirements: ItemRequirementDto[];
  instructions: ChecklistItemInstructionUploadDto[];
}

export interface DeleteItemParams {
  itemId: string;
}

export interface MutationOptions {
  onSuccess?: () => void;
  onError?: (error: any) => void;
}

export interface ChecklistItemEditorProps {
  storeId: string;
  itemId: string;
  checklistItem: ChecklistTemplateItemDto | undefined;
  getPresignedPost: (params: GetPresignedPostParams) => Promise<any>;
  upsertItem: (params: UpsertItemParams, options: MutationOptions) => void;
  deleteItem: (params: DeleteItemParams, options: MutationOptions) => void;
  invalidateApi: () => Promise<void>;
  isUpsertPending: boolean;
  isDeletePending: boolean;
  className?: string;
}

function requirementTypeToNewReqObj(reqType: string): ItemRequirementDto {
  const newReqId = genChecklistRequirementId();
  const commonProps = {
    id: newReqId,
    isRequired: true,
  };

  switch (reqType) {
    case "addImage":
      return {
        type: "addImage",
        ...commonProps,
      };
    case "inputBoolean":
      return {
        type: "inputBoolean",
        ...commonProps,
        conditionals: []
      };
    case "inputNumber":
      return {
        type: "inputNumber",
        ...commonProps,
        numberType: "number"
      };
    case "writeComment":
      return {
        type: "writeComment",
        ...commonProps,
      };
    default:
      throw new Error(`Unknown requirement type: ${reqType}`);
  }
}

/**
 * A generic component for editing checklist items.
 * Can be used for both template items and checklist items.
 */
export function ChecklistItemEditor({
                                      storeId,
                                      itemId, className,
                                      checklistItem,
                                      getPresignedPost,
                                      upsertItem,
                                      deleteItem,
                                      invalidateApi,
                                      isUpsertPending,
                                      isDeletePending
                                    }: ChecklistItemEditorProps) {
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isRequirementDialogOpen, setIsRequirementDialogOpen] = useState(false);
  const [isInstructionDialogOpen, setIsInstructionDialogOpen] = useState(false);
  const [editingBooleanRequirementIdx, setEditingBooleanRequirementIdx] = useState<number | undefined>();
  const [editingNumberRequirementIdx, setEditingNumberRequirementIdx] = useState<number | undefined>();

  const {
    toDto: uploadAttachment,
    status: uploadStatus
  } = useFormAttachmentUpload(({contentType, mediaType}) => {
    return getPresignedPost({
      storeId: storeId,
      contentType: contentType,
      mediaType: mediaType as AttachmentMediaType
    });
  });

  const form = useForm({
    defaultValues: {
      title: checklistItem?.title ?? "",
      description: checklistItem?.description ?? "",
      attachment: checklistItem?.attachments?.[0] ?? null as AttachmentValue,
      requirements: checklistItem?.requirements ?? [],
      instructions: checklistItem?.instructions ?? [] as FormChecklistInstruction[]
    },
    validatorAdapter: zodValidator(),
    onSubmit: async (event) => {
      const values = event.value;

      try {
        // Handle attachment upload
        let attachment;
        if (values.attachment) {
          attachment = await uploadAttachment(values.attachment);
          if (!attachment) {
            toast.error("Failed to upload attachment");
            return;
          }
        }

        // Handle instruction attachments
        const instructions = compact(await Promise.all(map(values.instructions, async (instruction): Promise<ChecklistItemInstructionUploadDto | undefined> => {
          if (!instruction) {
            return undefined;
          }

          const attachment = instruction.attachment;
          if (!attachment) {
            return {
              ...instruction,
              attachment: undefined
            };
          }

          // If attachment is FileAttachmentDto, then nothing has changed
          if ("id" in attachment) {
            return {
              ...instruction,
              attachment: attachment
            };
          }

          // If attachment is a new upload, process it
          const fileAttachmentDto = await uploadAttachment(attachment);
          if (!fileAttachmentDto) {
            return undefined;
          }

          return {
            ...instruction,
            attachment: fileAttachmentDto
          };
        })));

        // Submit the form data
        upsertItem({
          title: values.title,
          description: values.description,
          attachment: attachment,
          requirements: values.requirements,
          instructions: instructions
        }, {
          onSuccess: async () => {
            await invalidateApi();
            toast.success("Item saved");
          },
          onError: (error) => {
            console.error("Error saving checklist item", error);
            toast.error(`Error saving checklist item: ${error.message}`);
          }
        });
      } catch (error) {
        console.error("Error in form submission", error);
        toast.error(`Error: ${error instanceof Error ? error.message : "Unknown error"}`);
      }
    }
  });

  const handleDeleteItem = () => {
    deleteItem({
      itemId: itemId
    }, {
      onSuccess: async () => {
        await invalidateApi();
        setIsDeleteDialogOpen(false);
        toast.success("Item deleted");
      },
      onError: (error) => {
        console.error("Error deleting checklist item", error);
        toast.error(`Error deleting checklist item: ${error.message}`);
        setIsDeleteDialogOpen(false);
      }
    });
  };

  const isLoading = isUpsertPending || uploadStatus.isLoading;
  const [autoContainerRef] = useAutoAnimate();

  return (
    <div className={className}>
      <div className="space-y-3 mb-6">
        <Text size="xl" semibold>Checklist Item</Text>

        <form.Field name="title"
                    validators={{
                      onSubmit: z.string().min(1, "Required")
                    }}
                    children={(field) => (
                      <FormControl>
                        <Label htmlFor={field.name}>Item Name</Label>
                        <FormInput field={field}
                                   placeholder="Give this checklist item a name..."/>
                        <FieldInfo field={field}/>
                      </FormControl>
                    )}
        />

        <form.Field name="description"
                    children={(field) => (
                      <FormControl>
                        <Label htmlFor={field.name}>Description</Label>
                        <FormTextarea
                          field={field}
                          placeholder="Add text about this checklist item..."
                        />
                        <FieldInfo field={field}/>
                      </FormControl>
                    )}
        />

        <form.Field name="attachment"
                    children={(field) => (
                      <FormControl>
                        <Label htmlFor={field.name}>Attachments</Label>
                        <FormAttachmentInput
                          field={field}
                          types={['image', 'file']}
                          disableImagePress={true}
                        />
                        <FieldInfo field={field}/>
                      </FormControl>
                    )}
        />
      </div>

      <div className="space-y-3 mb-6">
        <Text size="xl" semibold>Additional Requirements</Text>
        <form.Field name="requirements"
                    mode="array"
                    children={(field) => (
                      <div>
                        <Button
                          variant="outline"
                          className="w-full justify-between font-normal mb-4"
                          onClick={() => setIsRequirementDialogOpen(true)}
                        >
                          <Text className="text-muted-foreground">
                            Add additional requirements for this item.
                          </Text>
                          <PlusIcon size={20}/>
                        </Button>

                        <Dialog open={isRequirementDialogOpen} onOpenChange={setIsRequirementDialogOpen}>
                          <DialogContent>
                            <DialogHeader>
                              <DialogTitle>Additional Requirement</DialogTitle>
                              <DialogDescription>
                                Require the team member to do the following:
                              </DialogDescription>
                            </DialogHeader>
                            <div className="grid gap-4 py-4">
                              {map(requirementOptions, (option) => (
                                <Button
                                  key={option.value}
                                  variant="outline"
                                  className="justify-start"
                                  onClick={() => {
                                    const reqType = option.value;
                                    field.handleChange((prev) => {
                                      const newReq = requirementTypeToNewReqObj(reqType);
                                      return uniq([...prev, newReq]);
                                    });
                                    setIsRequirementDialogOpen(false);

                                    // Open the appropriate dialog for the new requirement
                                    if (reqType === "inputBoolean") {
                                      setEditingBooleanRequirementIdx(field.state.value.length - 1);
                                    } else if (reqType === "inputNumber") {
                                      setEditingNumberRequirementIdx(field.state.value.length - 1);
                                    }
                                  }}
                                >
                                  {option.label}
                                </Button>
                              ))}
                            </div>
                          </DialogContent>
                        </Dialog>

                        <div ref={autoContainerRef}>
                          {map(field.state.value, (req, idx) => (
                            <ChecklistRequirementFormRow
                              key={req.id}
                              isRequired={req.isRequired}
                              onRequiredChange={isRequired => {
                                field.replaceValue(idx, {
                                  ...req,
                                  isRequired
                                });
                              }}
                              onPress={req.type === "inputBoolean"
                                ? () => setEditingBooleanRequirementIdx(idx)
                                : req.type === "inputNumber"
                                  ? () => setEditingNumberRequirementIdx(idx)
                                  : undefined}
                              requirement={req}
                              onRemove={() => {
                                field.removeValue(idx);
                              }}
                            />
                          ))}
                        </div>
                      </div>
                    )}
        />

        <form.Field
          name={`requirements[${editingBooleanRequirementIdx}]` as any}
          key={"booleanInputEdit" + editingBooleanRequirementIdx}
          children={(field) => (
            <BooleanInputDialog
              isOpen={editingBooleanRequirementIdx !== undefined}
              onClose={() => setEditingBooleanRequirementIdx(undefined)}
              onChange={field.handleChange}
              initialValue={field.state.value}
            />
          )}
        />

        <form.Field
          name={`requirements[${editingNumberRequirementIdx}]` as any}
          key={"numberInputEdit" + editingNumberRequirementIdx}
          children={(field) => (
            <NumberInputDialog
              initialValue={field.state.value?.numberType}
              isOpen={editingNumberRequirementIdx !== undefined}
              onClose={() => setEditingNumberRequirementIdx(undefined)}
              onChange={numberType => {
                field.handleChange({
                  ...field.state.value,
                  numberType
                });
              }}
            />
          )}
        />
      </div>

      <div className="space-y-3 mb-6">
        <Text size="xl" semibold>Instructions</Text>

        <form.Field
          name="instructions"
          mode="array"
          children={(field) => (
            <div>
              <Button
                variant="outline"
                className="w-full justify-between font-normal mb-4"
                onClick={() => setIsInstructionDialogOpen(true)}
              >
                <Text className="text-muted-foreground">
                  Add instructions for this item.
                </Text>
                <PlusIcon size={20}/>
              </Button>

              <AddInstructionsDialog
                isOpen={isInstructionDialogOpen}
                onClose={() => setIsInstructionDialogOpen(false)}
                onAdded={instruction => field.handleChange(prev => [...prev, instruction])}
              />

              {map(field.state.value, (instruction, instructionIdx) => {
                if (!instruction) {
                  return null;
                }

                // For text instructions, use inline editing
                if (instruction.text !== undefined) {
                  return (
                    <form.Field
                      key={instruction.id}
                      name={`instructions[${instructionIdx}].text` as any}
                      children={(textField) => (
                        <FormTextareaInline
                          field={textField}
                          isNew={instruction.text === ""}
                          onSave={() => {
                            // Nothing special needed here
                          }}
                          onCancel={() => {
                            // If it was a new empty instruction and user cancels, remove it
                            if (instruction.text === "") {
                              field.removeValue(instructionIdx);
                            }
                          }}
                        />
                      )}
                    />
                  );
                }

                // For other instruction types (images, files)
                const {icon, label} = getInstructionDetails(instruction);
                return (
                  <ChecklistItemFormRow
                    key={instruction.id}
                    icon={icon}
                    label={label}
                    onRemove={() => {
                      field.removeValue(instructionIdx);
                    }}
                  />
                );
              })}
            </div>
          )}
        />

        {/* Inline editing is now handled directly in the instruction rows */}
      </div>

      <hr className="my-5"/>
      <div className="flex justify-between">
        <Button
          variant="outline"
          onClick={() => setIsDeleteDialogOpen(true)}
          disabled={isDeletePending}
        >
          <Trash2Icon className="mr-2 h-4 w-4"/>
          Delete
        </Button>

        <Button
          onClick={form.handleSubmit}
          disabled={isLoading}
        >
          {isLoading ? <Spinner className="mr-2" size="sm"/> : null}
          Save Item
        </Button>
      </div>

      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Item</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete this item?
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={handleDeleteItem} className="bg-red-600 hover:bg-red-700">
              {isDeletePending ? <Spinner className="mr-2" size="sm"/> : null}
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Full screen loading indicator for attachment uploads */}
      {uploadStatus.isLoading && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-white p-6 rounded-lg max-w-md w-full">
            <div className="flex flex-col items-center">
              <Text size="lg" semibold className="mb-2">Uploading attachment</Text>
              <div className="w-full bg-gray-200 rounded-full h-2.5 mb-4">
                <div
                  className="bg-blue-600 h-2.5 rounded-full transition-all duration-300"
                  style={{width: `${uploadStatus.progress}%`}}
                ></div>
              </div>
              <Text className="text-center">
                {uploadStatus.filename
                  ? `Uploading ${uploadStatus.filename} (${Math.round(uploadStatus.progress)}%). Please do not close this screen.`
                  : `Uploading attachment (${Math.round(uploadStatus.progress)}%). Please do not close this screen.`}
              </Text>
              {uploadStatus.error && (
                <Text className="text-red-500 mt-2">{uploadStatus.error}</Text>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
