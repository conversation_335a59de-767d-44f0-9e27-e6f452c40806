import React from 'react';
import {Spinner} from "@/src/components/Spinner.tsx";
import {cn} from "@/src/util.ts";
import {AnimatePresence, motion} from "framer-motion";
import {useDebounceValue} from "usehooks-ts";

export interface LoadingOverlayProps {
  isLoading: boolean;
}

export const LoadingOverlay: React.FC<LoadingOverlayProps> = (props) => {
  const [isLoadingDebounced] = useDebounceValue(props.isLoading, 200);

  return (
    <AnimatePresence initial={false}>
      {isLoadingDebounced ? <>
        <motion.div initial={{opacity: 0, y: 10}}
                    animate={{opacity: 0.3, y: 0}}
                    exit={{opacity: 0, y: -10}}
                    className={cn("absolute top-0 left-0 right-0 bottom-0 bg-gray-800")}>
        </motion.div>
        <div className={"absolute top-0 left-0 right-0 bottom-0 flex items-center justify-center"}>
          <Spinner size={"xl"}/>
        </div>
      </> : null}
    </AnimatePresence>
  );
}
