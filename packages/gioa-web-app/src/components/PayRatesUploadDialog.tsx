import React from 'react';
import {<PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ooter, <PERSON><PERSON>Header, DialogTitle} from "@/src/components/ui/dialog.tsx";
import {Button} from "@/src/components/ui/button.tsx";
import {api} from "@/src/api.ts";
import {getHumanReadableErrorMessage} from "@/src/components/ErrorAlert.tsx";
import {FileIcon, UploadIcon} from 'lucide-react';
import {useDropzone} from 'react-dropzone';
import {toast} from 'sonner';
import {parseAndMatchPayRates} from '@/src/payRates/payRates';
import {toCents} from '@gioa/api/src/scheduling/metrics/dollars';
import {filter, find, isEmpty, map, some} from 'lodash';
import {Table, TableBody, TableCell, TableHead, TableHeader, TableRow} from './ui/table';
import {Cents, toDollars, toDollarsOrUndef} from "../../../api/src/scheduling/metrics/cents.ts";
import {formatCurrency} from "../../../common/src/dataFormatters.ts";
import {parseDollarsOrUndef} from "@/src/util.tsx";
import {isPayRateValid, MappedPayRate, PayRateRow} from "@/src/components/PayRateRow.tsx";
import {SchedulePersonDto} from "../../../api/src/schedulePersonDto.ts";
import {calculateAveragePayRate} from "../../../api/src/scheduling/metrics/payRates/payRates.ts";

enum WizardStep {
  UPLOAD_FILE = 0,
  MAP_PAY_RATES = 1,
  REVIEW_PAY_RATES = 2
}

export interface PayRatesUploadDialogProps {
  isOpen: boolean;
  onClose: () => void;
  storeId: string;
  scheduleId: string;
  people: SchedulePersonDto[];
}

export function PayRatesUploadDialog({isOpen, onClose, scheduleId, storeId, people}: PayRatesUploadDialogProps) {
  const [currentStep, setCurrentStep] = React.useState<WizardStep>(WizardStep.UPLOAD_FILE);
  const [file, setFile] = React.useState<File | null>(null);
  const [isProcessing, setIsProcessing] = React.useState(false);
  const [mappedPayRates, setMappedPayRates] = React.useState<MappedPayRate[]>([]);
  const [savedPayRates, setSavedPayRates] = React.useState<Map<string, Cents>>();

  const handleClose = React.useCallback(() => {
    setCurrentStep(WizardStep.UPLOAD_FILE);
    setFile(null);
    setMappedPayRates([]);
    setSavedPayRates(undefined);
    onClose();
  }, [onClose]);

  const onDrop = React.useCallback((acceptedFiles: File[]) => {
    const selectedFile = acceptedFiles?.[0];
    if (selectedFile) {
      setFile(selectedFile);
    }
  }, []);

  const {getRootProps, getInputProps, isDragActive} = useDropzone({
    onDrop,
    accept: {
      'text/csv': ['.csv'],
      'application/vnd.ms-excel': ['.csv']
    },
    multiple: false,
    disabled: isProcessing
  });

  const handleProcessFile = React.useCallback(async () => {
    if (!file) return;

    try {
      setIsProcessing(true);

      const peopleWithIds = map(people, person => ({
        id: person.id || '',
        firstName: person.firstName || '',
        lastName: person.lastName || ''
      }));

      const matches = await parseAndMatchPayRates({
        csv: file,
        people: peopleWithIds
      });

      const mappedRates = map(Array.from(matches.entries()), ([personPayRate, person]) => ({
        personPayRate,
        personId: person?.id ?? null,
        payRate: personPayRate.rate?.toFixed(2)
      }));

      setMappedPayRates(mappedRates);
      setCurrentStep(WizardStep.MAP_PAY_RATES);
    } catch (e) {
      alert("Error processing file: " + getHumanReadableErrorMessage(e));
    } finally {
      setIsProcessing(false);
    }
  }, [file, people]);


  const updateRowPayRate = React.useCallback((index: number, value: string) => {
    setMappedPayRates(prevRates => {
      const updatedRates = [...prevRates];
      updatedRates[index] = {
        ...updatedRates[index],
        payRate: value
      };
      return updatedRates;
    });
  }, []);

  const updateRowPersonId = React.useCallback((index: number, value: string | null) => {
    setMappedPayRates(prevRates => {
      const updatedRates = [...prevRates];
      updatedRates[index] = {
        ...updatedRates[index],
        personId: value
      };
      return updatedRates;
    });
  }, []);

  const removeMappedPayRate = React.useCallback((index: number) => {
    setMappedPayRates(prevRates => {
      const updatedRates = [...prevRates];
      updatedRates.splice(index, 1);
      return updatedRates;
    });
  }, []);

  const apiUtil = api.useUtils();
  const updatePayRates = api.data.updatePayRates.useMutation();
  const handleSavePayRates = React.useCallback(async () => {
    try {
      const payRatesMap = new Map<string, Cents>();
      for (const rate of mappedPayRates) {
        const payRateDollars = parseDollarsOrUndef(rate.payRate);
        if (rate.personId && payRateDollars) {
          payRatesMap.set(rate.personId, toCents(payRateDollars));
        }
      }

      await updatePayRates.mutateAsync({
        storeId,
        scheduleId,
        payRates: payRatesMap
      });

      apiUtil.data.invalidate();
      apiUtil.user.getAllSchedulePeopleAtStore.invalidate();

      setSavedPayRates(payRatesMap);
      setCurrentStep(WizardStep.REVIEW_PAY_RATES);
      toast.success("Pay rates saved successfully");
    } catch (e) {
      alert("Error saving pay rates: " + getHumanReadableErrorMessage(e));
    }
  }, [mappedPayRates, people, storeId, updatePayRates]);

  const renderStepContent = React.useCallback(() => {
    switch (currentStep) {
      case WizardStep.UPLOAD_FILE:
        return (
          <div className="space-y-4">
            <div
              {...getRootProps()}
              className={`border-2 border-dashed rounded-lg p-8 text-center cursor-pointer ${isDragActive ? 'border-primary bg-primary/10' : 'border-gray-300'}`}
            >
              <input {...getInputProps()} />
              <UploadIcon className="mx-auto h-12 w-12 text-gray-400"/>

              {file ? (
                <div className="mt-4 flex flex-col items-center">
                  <div className="flex items-center gap-2 text-primary">
                    <FileIcon size={16}/>
                    <span className="font-medium">{file.name}</span>
                  </div>
                  <p className="mt-2 text-sm text-gray-500">
                    Click or drag to replace
                  </p>
                </div>
              ) : (
                <>
                  <p className="mt-2 text-sm text-gray-600">
                    Drag and drop your CSV file here, or click to browse
                  </p>
                  <p className="mt-1 text-xs text-gray-500">
                    File must be a CSV containing pay rate data
                  </p>
                </>
              )}
            </div>
          </div>
        );

      case WizardStep.MAP_PAY_RATES:
        return (
          <div>
            {!isEmpty(mappedPayRates) ? <Table>
              <TableHeader>
                <TableRow>
                  <TableHead></TableHead>
                  <TableHead>CSV Name</TableHead>
                  <TableHead>Person</TableHead>
                  <TableHead>Pay Rate</TableHead>
                  <TableHead className="w-[50px]"></TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {map(mappedPayRates, (rate, index) => {
                  const personAlreadyAssignedToRate = rate.personId ? find(mappedPayRates, r => r.personId === rate.personId && r.payRate !== rate.payRate) : null;
                  return <PayRateRow key={rate.personPayRate.fullName}
                                     people={people} personAlreadyAssignedToRate={personAlreadyAssignedToRate ?? null}
                                     index={index}
                                     onUpdatePayRate={updateRowPayRate}
                                     onUpdatePersonId={updateRowPersonId}
                                     rate={rate}
                                     removeMappedPayRate={removeMappedPayRate}/>;
                })}
              </TableBody>
            </Table> : null}
          </div>
        );

      case WizardStep.REVIEW_PAY_RATES:
        return (
          <Table className={"w-auto"}>
            <TableHeader>
              <TableRow>
                <TableHead>Name</TableHead>
                <TableHead>Pay Rate</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {map(savedPayRates ? Array.from(savedPayRates.entries()) : [], ([personId, payRateCents], index) => {
                const payRate = toDollars(payRateCents);
                const person = find(people, p => p.id === personId);
                if (!person) {
                  return null;
                }
                return (
                  <TableRow key={index}>
                    <TableCell>{person.firstName} {person.lastName}</TableCell>
                    <TableCell>{formatCurrency(payRate)}/hr</TableCell>
                  </TableRow>
                );
              })}
            </TableBody>
          </Table>
        );

      default:
        return null;
    }
  }, [currentStep, file, getRootProps, getInputProps, isDragActive, mappedPayRates, savedPayRates, updateRowPayRate, updateRowPersonId, removeMappedPayRate, people]);

  const renderFooterButtons = React.useCallback(() => {
    switch (currentStep) {
      case WizardStep.UPLOAD_FILE:
        return (
          <>
            <Button variant="outline" onClick={handleClose}>
              Cancel
            </Button>
            <Button
              onClick={handleProcessFile}
              disabled={!file || isProcessing}
              isLoading={isProcessing}
            >
              Continue
            </Button>
          </>
        );

      case WizardStep.MAP_PAY_RATES:
        const hasValidMappings = some(mappedPayRates, rate => rate.personId);
        const numValidPayRates = filter(mappedPayRates, isPayRateValid).length;

        return (
          <>
            <Button variant="outline" onClick={handleClose}>
              Cancel
            </Button>
            <Button
              onClick={handleSavePayRates}
              disabled={!hasValidMappings || updatePayRates.isPending}
              isLoading={updatePayRates.isPending}
            >
              Save {numValidPayRates} Pay Rates
            </Button>
          </>
        );

      case WizardStep.REVIEW_PAY_RATES:
        return (
          <Button onClick={handleClose}>
            Done
          </Button>
        );

      default:
        return null;
    }
  }, [currentStep, file, isProcessing, handleProcessFile, handleClose, mappedPayRates, handleSavePayRates, updatePayRates.isPending]);

  const getStepTitle = React.useCallback(() => {
    switch (currentStep) {
      case WizardStep.UPLOAD_FILE:
        return "Upload Pay Rates";
      case WizardStep.MAP_PAY_RATES:
        return "Map Pay Rates";
      case WizardStep.REVIEW_PAY_RATES:
        return "Review Pay Rates";
      default:
        return "";
    }
  }, [currentStep]);

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && handleClose()}>
      <DialogContent size={"3xl"}>
        <DialogHeader>
          <DialogTitle className={"text-center"}>{getStepTitle()}</DialogTitle>
        </DialogHeader>

        <div className="flex justify-center my-3">
          <div className="flex items-center">
            <div
              className={`flex items-center justify-center w-8 h-8 rounded-full ${currentStep >= WizardStep.UPLOAD_FILE ? 'bg-primary text-white' : 'bg-gray-200'}`}>
              1
            </div>
            <div className={`w-12 h-1 ${currentStep > WizardStep.UPLOAD_FILE ? 'bg-primary' : 'bg-gray-200'}`}></div>
            <div
              className={`flex items-center justify-center w-8 h-8 rounded-full ${currentStep >= WizardStep.MAP_PAY_RATES ? 'bg-primary text-white' : 'bg-gray-200'}`}>
              2
            </div>
            <div className={`w-12 h-1 ${currentStep > WizardStep.MAP_PAY_RATES ? 'bg-primary' : 'bg-gray-200'}`}></div>
            <div
              className={`flex items-center justify-center w-8 h-8 rounded-full ${currentStep >= WizardStep.REVIEW_PAY_RATES ? 'bg-primary text-white' : 'bg-gray-200'}`}>
              3
            </div>
          </div>
        </div>

        {currentStep === WizardStep.UPLOAD_FILE && (
          <div>
            <p className="mb-2">
              Setting pay rates for your Team Members increases the accuracy of your labor cost calculations.
              Team Members without pay rates will get your configured average pay rate.
            </p>

            <ol className="space-y-3 list-decimal list-outside ml-6">
              <li>
                Log into {' '}
                <a rel="noreferrer noopener" href={"https://backoffice.cfahome.com/df/pickFeed.do"}
                   className={"text-blue-600 hover:underline"} target={"_blank"}>
                  cfahome.com
                </a>
              </li>
              <li>
                Go to <strong className={"font-semibold text-gray-900"}>Data Feed</strong>.
              </li>
              <li>
                Scroll down to <strong className="font-semibold text-gray-900">People</strong>.
              </li>
              <li>
                Select <strong className={"font-semibold text-gray-900"}>Current Team Member Jobs and Pay Rates</strong>.
              </li>
              <li>
                Download the CSV file.
              </li>
              <li>
                Upload the CSV file here.
              </li>
            </ol>

          </div>
        )}

        {currentStep === WizardStep.MAP_PAY_RATES ?
          !isEmpty(mappedPayRates) ? <p>
            Map the names from your CSV file to people in your store. Adjust pay rates if needed.
          </p> : <p>
            There are no matches between the names in your CSV file and the people in your store. Please try
            again with a different CSV file.
          </p> : null}

        {currentStep === WizardStep.REVIEW_PAY_RATES && (
          <p>
            Pay rates have been saved successfully. Review the saved pay rates below.
            {savedPayRates ? " The average pay rate is " + formatCurrency(toDollarsOrUndef(calculateAveragePayRate(savedPayRates))) : ""}
          </p>
        )}

        <div className="overflow-y-auto" style={{maxHeight: "calc(100vh - 300px)"}}>
          {renderStepContent()}
        </div>

        <DialogFooter className={"justify-between"}>
          {renderFooterButtons()}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
