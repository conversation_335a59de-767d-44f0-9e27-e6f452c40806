import React, {useEffect, useState} from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger
} from "@/src/components/ui/dialog.tsx";
import {Button} from "@/src/components/ui/button.tsx";
import {find, map} from "lodash";
import {Table, TableBody, TableCell, TableHead, TableHeader, TableRow} from './ui/table';
import {Checkbox} from "./ui/checkbox";
import {daysOfWeek} from "@/src/components/AvailabilityWeek.tsx";
import {ArrowDownIcon, ArrowUpIcon, PlusIcon, TrashIcon} from "lucide-react";
import {useAutoAnimate} from '@formkit/auto-animate/react'
import {useDisclosure} from "@/src/hooks/useDisclosure.tsx";
import {EditAreaDialogForm} from "@/src/components/EditAreaDialogForm.tsx";
import {genScheduleAreaId, StoreAreaDto} from "../../../api/src/schemas.ts";
import {api} from "@/src/api.ts";
import {ScheduleArea} from "../../../api/src/scheduleBuilder.util.ts";

export interface ScheduleEditAreaDialogProps {
  area: ScheduleArea;
  dayOfWeek: number;
  onSave: (area: ScheduleArea) => void;
  onDelete: (area: ScheduleArea) => void;
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  storeAreas: StoreAreaDto[];
}

export const ScheduleEditAreaDialog: React.FC<ScheduleEditAreaDialogProps> = (props) => {
  const area = props.area;
  const handleDelete = (index: number) => {
    const shouldDelete = confirm("Are you sure you want to delete this work area? All shifts in the area will also be deleted.");
    if (!shouldDelete) {
      return
    }

    props.onDelete(props.area);
  };

  const onEditAreaSubmit = (area: ScheduleArea) => {
    props.onSave(area);
    props.onOpenChange(false);
  }

  const onCancel = () => {
    props.onOpenChange(false);
  };

  return (
    <Dialog open={props.isOpen} onOpenChange={props.onOpenChange}>
      <DialogContent>
        <div className="space-y-4">
          <DialogHeader>
            <DialogTitle>
              Edit Area: {area.title}
            </DialogTitle>
            <DialogDescription>
              Edit area details below.
            </DialogDescription>
          </DialogHeader>
          <EditAreaDialogForm area={area} onCancel={onCancel}
                              storeAreas={props.storeAreas}
                              onSubmit={onEditAreaSubmit}/>
        </div>
      </DialogContent>
    </Dialog>)
}
