import React from 'react';
import {cn} from "@/src/util.ts";
import {RowSpan} from "@/src/components/TimelineGrid.tsx";
import {HeatmapSpanMeta} from "@/src/availabilityReport.ts";
import {maxBy} from "lodash";

export interface HeatmapRowSpanProps {
  rowIndex: number;
  allSpans: RowSpan<HeatmapSpanMeta>[];
  spanIdx: number;
  span: RowSpan<HeatmapSpanMeta>;
  includeApprovedTimeOff: boolean;
  includePendingTimeOff: boolean;
}

export const displayNumFactory = ({includeApprovedTimeOff, includePendingTimeOff}: {
  includeApprovedTimeOff: boolean;
  includePendingTimeOff: boolean;
}) => (span: RowSpan<HeatmapSpanMeta> | undefined): number => {
  if (!span) {
    return 0;
  }
  return span.metadata.numAvailable
    - (includeApprovedTimeOff ? span.metadata.numTimeOff : 0)
    - (includePendingTimeOff ? span.metadata.numTimeOffPending : 0);
}

export const HeatmapRowSpan: React.FC<HeatmapRowSpanProps> = React.memo(({allSpans, rowIndex, spanIdx, span, ...toSettings}) => {
  const getDisplayNum = displayNumFactory(toSettings);
  const maxMetric = getDisplayNum(maxBy(allSpans, s => getDisplayNum(s)))
  const prevSpan = allSpans[spanIdx - 1];
  const nextSpan = allSpans[spanIdx + 1];
  const opacity = maxMetric > 0 ? getDisplayNum(span) / maxMetric : 0;

  return (
    <div className={"h-full flex flex-row py-0.5"}>
      <div key={span.start + span.end} style={{
        background: `rgba(62, 75, 126, ${opacity})`
      }} className={cn("h-full flex items-center grow", {
        "text-white": opacity > 0.6,
        "text-black": opacity < 0.6,
        "rounded-l-md": !prevSpan || getDisplayNum(prevSpan) === 0,
        "rounded-r-md": !nextSpan || getDisplayNum(nextSpan) === 0,
      })}>
        <span className={"pl-1"}>
        {getDisplayNum(span) > 0 ? getDisplayNum(span) : ""}
          </span>
      </div>
    </div>
  );
});
