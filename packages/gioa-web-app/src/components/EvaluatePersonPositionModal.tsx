import React, { useState } from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  Di<PERSON>Footer,
  DialogHeader,
  DialogTitle,
} from "@/src/components/ui/dialog.tsx";
import { But<PERSON> } from "@/src/components/ui/button.tsx";
import { PersonPositionScoreHistory } from "@/src/components/PersonPositionScoreHistory.tsx";
import { find, flatMap, isEmpty } from "lodash";
import { api } from "@/src/api.ts";
import { StorePositionDto } from "@gioa/api/src/schemas";
import { Checkbox } from "@/src/components/ui/checkbox.tsx";
import { Slider } from "@/src/components/ui/slider.tsx";
import { Text } from "@/src/components/Text.tsx";
import { Textarea } from "@/src/components/ui/textarea.tsx";
import { toast } from "sonner";
import { Separator } from "@/src/components/ui/separator.tsx";
import {ErrorAlert} from "@/src/components/ErrorAlert.tsx";
import {Label} from "@/src/components/ui/label.tsx";

interface EvaluatePersonPositionModalProps {
  isOpen: boolean;
  onClose: () => void;
  personId: string;
  storeId: string;
  positionId: string;
}

export const EvaluatePersonPositionModal: React.FC<EvaluatePersonPositionModalProps> = ({
  isOpen,
  onClose,
  personId,
  storeId,
  positionId,
}) => {
  // Fetch person details
  const { data: person, isLoading } = api.user.getPersonDetail.useQuery(
    {
      personId,
      storeId,
    },
    {
      staleTime: 5 * 60 * 1000, // 5 minutes
      enabled: isOpen, // Only fetch when modal is open
    },
  );

  // Get store details
  const { data: store } = api.user.getStore.useQuery(
    {
      storeId,
    },
    {
      staleTime: 60 * 60 * 1000, // 1 hour
      enabled: isOpen,
    },
  );

  // Find position and statistics
  const positions = person ? flatMap(person.storeAreas, (a) => a.positions) : [];
  const position = find(positions, (p) => p.id === positionId) as StorePositionDto | undefined;
  const statistic = person ? find(person.positionStatistics, (s) => s.positionId === positionId) : undefined;
  const dataPoints = statistic?.dataPoints ?? [];
  const trainingRecord = person ? find(person.training, (t) => t.positionId === positionId) : undefined;

  // Determine permissions
  const canViewPositionScores = Boolean(person?.requesterPermissionEvaluation?.canViewPositionScores);
  const canEditPositionScores = Boolean(person?.requesterPermissionEvaluation?.canEditPositionScores);
  const canViewTraining = Boolean(person?.requesterPermissionEvaluation?.canViewTraining);
  const canEditTraining = Boolean(person?.requesterPermissionEvaluation?.canEditTraining);

  // Form state
  const [isTrained, setIsTrained] = useState<boolean>(Boolean(trainingRecord?.isCompleted));
  const [metricValue, setMetricValue] = useState<number>(0);
  const [note, setNote] = useState<string>("");

  // API mutations
  const updateTraining = api.user.upsertPositionTrainingHistory.useMutation();
  const createEvaluation = api.user.createEvaluation.useMutation();
  const apiUtil = api.useUtils();

  // Handle form submission
  const handleSubmit = async () => {
    if (!person || !position) return;

    try {
      // Update training if permissions allow
      if (canEditTraining) {
        await updateTraining.mutateAsync({
          personId: person.id!,
          storeId,
          storePositionId: positionId,
          isCompleted: isTrained,
          isPersonPreferred: trainingRecord?.isPersonPreferred ?? false,
        });
      }

      // Create evaluation if permissions allow and a score was provided
      if (canEditPositionScores && metricValue > 0) {
        await createEvaluation.mutateAsync({
          personId: person.id!,
          storeId,
          storePositionId: positionId,
          storePositionTitle: position.title,
          notes: note,
          rating: metricValue,
        });
      }

      apiUtil.user.getPersonDetail.invalidate();
      apiUtil.user.getPersonPositionStatistics.invalidate();
      toast.success("Position evaluation saved successfully");

      // Reset form
      setMetricValue(0);
      setNote("");
      setIsTrained(Boolean(trainingRecord?.isCompleted));
      onClose();
    } catch (error) {
      toast.error("Failed to save position evaluation");
      console.error("Error saving position evaluation:", error);
    }
  };

  const isSubmitting = updateTraining.isPending || createEvaluation.isPending;

  if (isLoading) {
    return (
      <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Loading...</DialogTitle>
          </DialogHeader>
        </DialogContent>
      </Dialog>
    );
  }

  if (!person || !position || !store) {
    return null;
  }

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent size={"lg"} className={"overflow-auto"} style={{ maxHeight: "calc(100vh - 50px)" }}>
        <DialogHeader>
          <DialogTitle>{position.title}</DialogTitle>
          <DialogDescription>
            Evaluate {person.firstName} {person.lastName}'s performance
          </DialogDescription>
        </DialogHeader>
        {updateTraining.isError && (
          <div className={"my-3"}>
            <ErrorAlert error={updateTraining.error} />
          </div>
        )}
        {createEvaluation.isError && (
          <div className={"my-3"}>
            <ErrorAlert error={createEvaluation.error} />
          </div>
        )}
        <div className="flex-1 overflow-hidden">
          <div className="space-y-6 mb-4 px-1">
            {canViewTraining && (
              <div className="mb-6">
                <div className="mb-3">
                  <Text size="lg" semibold>
                    Training
                  </Text>
                </div>
                <Text className="mb-2">
                  Has{" "}
                  <span className="font-semibold">
                    {person.firstName} {person.lastName}
                  </span>{" "}
                  been trained in the <span className="font-semibold">{position.title}</span> position at{" "}
                  <span className="font-semibold">{store.title}</span>?
                </Text>

                <Label
                  htmlFor="trained"
                  className="flex items-center space-x-2 mt-2 border border-gray-300 rounded-lg p-2 cursor-pointer"
                >
                  <Checkbox
                    id="trained"
                    checked={isTrained}
                    disabled={!canEditTraining}
                    onCheckedChange={() => setIsTrained(!isTrained)}
                  />
                  <div className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
                    Yes, training completed
                  </div>
                </Label>
              </div>
            )}

            {canEditPositionScores && (
              <>
                <Separator className="my-4" />
                <div className="mb-6">
                  <div className="mb-3">
                    <Text size="lg" semibold>
                      Evaluation
                    </Text>
                  </div>
                  <Text className="mb-3">
                    Leave a score for {person.firstName} {person.lastName} in the position of {position.title}.
                  </Text>

                  <div className="mb-4">
                    <div className="flex justify-between items-center mb-2">
                      <Text>New Score</Text>
                      {metricValue > 0 && <Text bold>{metricValue}%</Text>}
                    </div>
                    <Slider
                      value={[metricValue]}
                      min={0}
                      max={100}
                      step={10}
                      onValueChange={(value) => setMetricValue(value[0])}
                    />
                  </div>

                  <div className="mb-4">
                    <Text className="mb-2">Notes (optional)</Text>
                    <Textarea
                      disabled={metricValue <= 0}
                      placeholder="Add notes about this evaluation..."
                      value={note}
                      onChange={(e) => setNote(e.target.value)}
                      className="min-h-[100px]"
                    />
                  </div>
                </div>
              </>
            )}

            {!isEmpty(dataPoints) && canViewPositionScores && (
              <>
                <Separator className="my-4" />
                <PersonPositionScoreHistory dataPoints={dataPoints} aggregateValue={statistic?.value ?? 0} />
              </>
            )}
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onClose} disabled={isSubmitting}>
            Cancel
          </Button>
          <Button onClick={handleSubmit} disabled={isSubmitting}>
            {isSubmitting ? "Saving..." : "Save"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
