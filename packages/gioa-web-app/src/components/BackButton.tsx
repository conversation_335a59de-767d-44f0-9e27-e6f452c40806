import React from 'react';
import {Button} from "@/src/components/ui/button.tsx";
import {cn} from "@/src/util.ts";
import {useRouter} from '@tanstack/react-router';
import {ArrowLeftIcon} from 'lucide-react';

export interface BackButtonProps {
  className?: string;
}

export function useBack() {
  const router = useRouter();
  return (numTimes = 1) => router.history.go(-numTimes);
}

export const BackButton: React.FC<BackButtonProps> = ({className}) => {
  const onBack = useBack();

  return <Button variant={"link"} onClick={() => onBack()} className={cn("pl-0 mb-2", className)}
                 leftIcon={<ArrowLeftIcon size={16}/>}>
    Back
  </Button>
}
