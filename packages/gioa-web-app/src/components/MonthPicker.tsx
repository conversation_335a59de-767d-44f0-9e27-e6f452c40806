import * as React from "react";
import {Calendar as CalendarIcon} from "lucide-react"
import { Button } from "@/src/components/ui/button";
import { Popover, PopoverContent, PopoverTrigger } from "@/src/components/ui/popover";
import { cn } from "@/src/util.ts";
import { IsoCompleteDate } from "../../../api/src/timeSchemas.ts";
import { DateTime } from "luxon";

export interface YearMonthPickerProps extends Omit<React.HTMLAttributes<HTMLDivElement>, "value" | "onChange"> {
  value: IsoCompleteDate;
  onChange: (date: IsoCompleteDate) => void;
}

export function YearMonthPicker({ className, value, onChange }: YearMonthPickerProps) {

  const [currentYear, setCurrentYear] = React.useState(value.year);
  React.useEffect(() => {
    setCurrentYear(value.year);
  }, [value.year]);

  const onMonthSelect = (month: number) => {
    onChange({
      year: currentYear,
      week: value.week,
      month,
      day: value.day ?? 1,
    });
  };

  const monthButtons = Array.from({ length: 12 }, (_, i) => i + 1).map(month => (
    <Button
      key={month}
      onClick={() => onMonthSelect(month)}
      variant={(value.month === month && value.year === currentYear) ? "default" : "outline"}
      // variant="outline"
      size="sm"
    >
      {DateTime.local(currentYear, month).toFormat("MMM")}
    </Button>
  ));

  return (
    <div className={cn("grid gap-2", className)}>
      <Popover>
        <PopoverTrigger asChild>
          <Button
            id="year-month"
            variant={"outline"}
            className="justify-start text-left font-normal"
          >
            <CalendarIcon className="mr-2 h-4 w-4"/>
            {value.month ? DateTime.local(currentYear, value.month).toFormat("MMM yyyy") : "Select a month..."}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-auto p-0" align="start">
          <div className="flex gap-2 items-center justify-between py-3 px-3 border-b">
            <Button onClick={() => setCurrentYear(currentYear - 1)} variant="outline" size="sm">
              -
            </Button>
            <span>{currentYear}</span>
            <Button onClick={() => setCurrentYear(currentYear + 1)} variant="outline" size="sm">
              +
            </Button>
          </div>
          <div className="grid grid-cols-4 gap-2 p-3">
            {monthButtons}
          </div>
        </PopoverContent>
      </Popover>
    </div>
  );
}