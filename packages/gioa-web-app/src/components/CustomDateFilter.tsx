import React, {useState} from "react";
import {DateTime, WeekdayNumbers} from "luxon";
import {IsoCompleteDate} from "../../../api/src/timeSchemas.ts";
import {Select, SelectContent, SelectItem, SelectTrigger, SelectValue} from "./ui/select.tsx";
import {Button, buttonVariants} from "./ui/button.tsx";
import {ArrowLeftIcon, ArrowRightIcon, CalendarIcon} from "lucide-react";
import {IsoDayPicker} from "./IsoDayPicker.tsx";
import {WeekPicker} from "./WeekPicker.tsx";
import {YearMonthPicker} from "./MonthPicker.tsx";
import {getDateFromIsoCompleteDate} from "../../../api/src/date.util.ts";
import {generateIsoWeekDates, takeFromGenerator} from "../../../api/src/scheduleBuilder.util.ts";
import {cn} from "@/src/util.ts";
import {Popover, PopoverContent, PopoverTrigger} from "./ui/popover.tsx";
import {Calendar} from "./ui/calendar.tsx";

export type TimeFrameFilter = 'day' | 'week' | 'month' | 'custom';

interface DayFilterProps {
  selectedDate: IsoCompleteDate;
  timezone: string | null;
  setSelectedDate: (date: IsoCompleteDate) => void;
  today: DateTime;
}

const DayFilter: React.FC<DayFilterProps> = ({
                                               selectedDate,
                                               timezone,
                                               setSelectedDate,
                                               today
                                             }) => {
  const thisDay = getDateFromIsoCompleteDate(selectedDate, timezone);
  const nextDay = thisDay.plus({days: 1});
  const prevDay = thisDay.minus({days: 1});

  return (
          <div className="flex flex-row flex-wrap items-center justify-between gap-3">
            <IsoDayPicker value={selectedDate} onChange={setSelectedDate} today={today}/>
            <div className="inline-flex rounded-md" role="group">
              <Button
                      title={"Go to the previous day"}
                      variant={"outline"}
                      className={"rounded-r-none relative hover:z-10"}
                      onClick={() => setSelectedDate({
                        day: prevDay.weekday,
                        week: prevDay.weekNumber,
                        year: prevDay.weekYear
                      })}
              >
                <ArrowLeftIcon size={16} className={"mr-1"}/>
                Prev
              </Button>
              <Button
                      title={"Go to the next day"}
                      variant={"outline"}
                      className={"rounded-l-none border-l-0 relative hover:z-10"}
                      onClick={() => setSelectedDate({
                        day: nextDay.weekday,
                        week: nextDay.weekNumber,
                        year: nextDay.weekYear
                      })}
              >
                Next
                <ArrowRightIcon size={16} className={"ml-1"}/>
              </Button>
            </div>
          </div>
  );
};

interface WeekFilterProps {
  selectedDate: IsoCompleteDate;
  timezone: string | null;
  setSelectedDate: (date: IsoCompleteDate) => void;
  today: DateTime;
}

const WeekFilter: React.FC<WeekFilterProps> = ({
                                                 selectedDate,
                                                 timezone,
                                                 setSelectedDate,
                                                 today
                                               }) => {
  const [, nextWeek] = takeFromGenerator(generateIsoWeekDates(selectedDate, timezone), 2);
  const [, prevWeek] = takeFromGenerator(generateIsoWeekDates(selectedDate, timezone, (d: DateTime) => d.minus({weeks: 1})), 2);

  return (
          <div className="flex flex-row flex-wrap items-center justify-between gap-3">
            <WeekPicker
                    value={selectedDate}
                    today={today}
                    timezone={timezone}
                    onChange={setSelectedDate}
            />
            <div className="inline-flex rounded-md" role="group">
              <button
                      title={"Go to the previous week"}
                      className={cn(buttonVariants({variant: "outline"}), "rounded-r-none relative hover:z-10")}
                      onClick={() => setSelectedDate({...prevWeek})}
              >
                <ArrowLeftIcon size={16} className={"mr-1"}/>
                Prev
              </button>
              <button
                      title={"Go to the next week"}
                      className={cn(buttonVariants({variant: "outline"}), "rounded-l-none border-l-0 relative hover:z-10")}
                      onClick={() => setSelectedDate({...nextWeek})}
              >
                Next
                <ArrowRightIcon size={16} className={"ml-1"}/>
              </button>
            </div>
          </div>
  );
};

interface MonthFilterProps {
  selectedDate: IsoCompleteDate;
  timezone: string | null;
  setSelectedDate: (date: IsoCompleteDate) => void;
}

const MonthFilter: React.FC<MonthFilterProps> = ({
                                                   selectedDate,
                                                   timezone,
                                                   setSelectedDate
                                                 }) => {
  const thisMonth = getDateFromIsoCompleteDate(selectedDate, timezone);
  const nextMonth = thisMonth.plus({months: 1});
  const prevMonth = thisMonth.minus({months: 1});

  return (
          <div className="flex flex-row flex-wrap items-center justify-between gap-3">
            <YearMonthPicker value={selectedDate} onChange={setSelectedDate}/>
            <div className="inline-flex rounded-md" role="group">
              <Button
                      title={"Go to the previous month"}
                      variant={"outline"}
                      className={"rounded-r-none relative hover:z-10"}
                      onClick={() => setSelectedDate({
                        day: prevMonth.weekday,
                        week: prevMonth.weekNumber,
                        year: prevMonth.weekYear,
                        month: prevMonth.month
                      })}
              >
                <ArrowLeftIcon size={16} className={"mr-1"}/>
                Prev
              </Button>
              <Button
                      title={"Go to the next month"}
                      variant={"outline"}
                      className={"rounded-l-none border-l-0 relative hover:z-10"}
                      onClick={() => setSelectedDate({
                        day: nextMonth.weekday,
                        week: nextMonth.weekNumber,
                        year: nextMonth.weekYear,
                        month: nextMonth.month
                      })}
              >
                Next
                <ArrowRightIcon size={16} className={"ml-1"}/>
              </Button>
            </div>
          </div>
  );
};

interface CustomFilterProps {
  startDate: IsoCompleteDate | null;
  endDate: IsoCompleteDate | null;
  onDateRangeChange: (startDate: IsoCompleteDate | null, endDate: IsoCompleteDate | null) => void;
  timezone: string | null;
}

const CustomFilter: React.FC<CustomFilterProps> = ({
                                                     startDate,
                                                     endDate,
                                                     onDateRangeChange,
                                                     timezone
                                                   }) => {
  const [isOpen, setIsOpen] = useState(false);

  // Create DateTime objects in store timezone (single source of truth)
  const startDateTime = startDate && startDate.month && startDate.day ?
          DateTime.fromObject({
            weekYear: startDate.year,
            weekNumber: startDate.week,
            weekday: startDate.day as WeekdayNumbers
          }, {zone: timezone || undefined}).set({month: startDate.month}) : null;
  const endDateTime = endDate && endDate.month && endDate.day ?
          DateTime.fromObject({
            weekYear: endDate.year,
            weekNumber: endDate.week,
            weekday: endDate.day as WeekdayNumbers
          }, {zone: timezone || undefined}).set({month: endDate.month}) : null;

  // Convert to timezone-neutral JS Date objects for Calendar component
  // Use new Date(year, month, day) to create dates that represent the calendar date regardless of timezone
  const startDateAsDate = startDateTime ?
          new Date(startDateTime.year, startDateTime.month - 1, startDateTime.day) : null;
  const endDateAsDate = endDateTime ?
          new Date(endDateTime.year, endDateTime.month - 1, endDateTime.day) : null;

  const formatDateRange = () => {
    if (startDateTime && endDateTime) {
      return `${startDateTime.toFormat("MMM d")} - ${endDateTime.toFormat("MMM d, yyyy")}`;
    } else if (startDateTime) {
      return `${startDateTime.toFormat("MMM d, yyyy")} - Select end date`;
    } else {
      return "Select date range";
    }
  };

  // Convert Date back to IsoCompleteDate
  // Since we created timezone-neutral dates, extract the calendar components directly
  const convertDateToIsoCompleteDate = (date: Date): IsoCompleteDate => {
    // Extract calendar date components (year, month, day) from the Date object
    const year = date.getFullYear();
    const month = date.getMonth() + 1; // getMonth() returns 0-11
    const day = date.getDate();

    // Create DateTime in store timezone to get ISO week properties
    const dateTime = DateTime.fromObject({year, month, day}, {zone: timezone || undefined});
    return {
      year: dateTime.weekYear,      // Use ISO week year
      month: dateTime.month,
      day: dateTime.weekday,        // Use ISO weekday (1-7)
      week: dateTime.weekNumber
    };
  };

  return (
          <div className="flex flex-row flex-wrap items-center gap-3">
            <Popover open={isOpen} onOpenChange={setIsOpen}>
              <PopoverTrigger asChild>
                <Button
                        variant="outline"
                        className={cn(
                                "w-[280px] justify-start text-left font-normal",
                                !startDate && !endDate && "text-muted-foreground"
                        )}
                >
                  <CalendarIcon className="mr-2 h-4 w-4"/>
                  {formatDateRange()}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0" align="start">
                <div className="p-3">
                  <div className="flex justify-between items-center mb-3">
                    <span className="text-sm font-medium">Select Date Range</span>
                    {(startDate || endDate) && (
                            <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => {
                                      onDateRangeChange(null, null);
                                    }}
                                    className="h-6 px-2 text-xs"
                            >
                              Clear
                            </Button>
                    )}
                  </div>
                  <Calendar
                          mode="range"
                          selected={{
                            from: startDateAsDate || undefined,
                            to: endDateAsDate || undefined
                          }}
                          onSelect={(range) => {
                            if (range) {
                              const startIso = range.from ? convertDateToIsoCompleteDate(range.from) : null;
                              const endIso = range.to ? convertDateToIsoCompleteDate(range.to) : null;
                              onDateRangeChange(startIso, endIso);
                              // Close the popover when both dates are selected
                              if (range.from && range.to) {
                                setIsOpen(false);
                              }
                            } else {
                              onDateRangeChange(null, null);
                            }
                          }}
                          numberOfMonths={2}
                          initialFocus
                  />
                  {startDate && !endDate && (
                          <div className="mt-2 p-2 bg-blue-50 rounded text-xs text-blue-700">
                            Click another date to complete the range
                          </div>
                  )}
                </div>
              </PopoverContent>
            </Popover>
          </div>
  );
};

interface CustomDateFilterProps {
  timezone: string;
  timeRange: TimeFrameFilter;
  selectedDate: IsoCompleteDate;
  customStartDate?: IsoCompleteDate | null;
  customEndDate?: IsoCompleteDate | null;
  onDateChange: (date: IsoCompleteDate, timeRange: TimeFrameFilter, customStart?: IsoCompleteDate | null, customEnd?: IsoCompleteDate | null) => void;
}

export const CustomDateFilter: React.FC<CustomDateFilterProps> = ({
                                                                    timezone,
                                                                    timeRange,
                                                                    selectedDate,
                                                                    customStartDate,
                                                                    customEndDate,
                                                                    onDateChange
                                                                  }) => {
  const [today] = useState(DateTime.now().setZone(timezone));

  const setTimeFrameFilter = (val: TimeFrameFilter) => {
    onDateChange(selectedDate, val, customStartDate, customEndDate);
  };

  const setSelectedDate = (date: IsoCompleteDate) => {
    onDateChange(date, timeRange, customStartDate, customEndDate);
  };

  const handleCustomDateChange = (startDate: IsoCompleteDate | null, endDate: IsoCompleteDate | null) => {
    onDateChange(selectedDate, timeRange, startDate, endDate);
  };

  return (
          <div className="flex flex-row items-center gap-2 flex-wrap">
            <div>
              <Select
                      onValueChange={val => setTimeFrameFilter(val as TimeFrameFilter)}
                      value={timeRange}
              >
                <SelectTrigger className="min-w-[100px]">
                  <SelectValue placeholder="Time Frame"/>
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value={"day"}>
                    Day
                  </SelectItem>
                  <SelectItem value={"week"}>
                    Week
                  </SelectItem>
                  <SelectItem value={"month"}>
                    Month
                  </SelectItem>
                  <SelectItem value={"custom"}>
                    Custom
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>

            {timeRange === "day" && (
                    <DayFilter
                            selectedDate={selectedDate}
                            timezone={timezone}
                            setSelectedDate={setSelectedDate}
                            today={today}
                    />
            )}

            {timeRange === "week" && (
                    <WeekFilter
                            selectedDate={selectedDate}
                            timezone={timezone}
                            setSelectedDate={setSelectedDate}
                            today={today}
                    />
            )}

            {timeRange === "month" && (
                    <MonthFilter
                            selectedDate={selectedDate}
                            timezone={timezone}
                            setSelectedDate={setSelectedDate}
                    />
            )}

            {timeRange === "custom" && (
                    <CustomFilter
                            startDate={customStartDate || null}
                            endDate={customEndDate || null}
                            onDateRangeChange={(startDate, endDate) => handleCustomDateChange(startDate, endDate)}
                            timezone={timezone}
                    />
            )}
          </div>
  );
};
