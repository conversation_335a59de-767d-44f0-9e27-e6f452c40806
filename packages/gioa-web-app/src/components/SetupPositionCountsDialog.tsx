import React, {useEffect, useState} from 'react';
import {<PERSON><PERSON>, <PERSON>alogContent, <PERSON><PERSON>Footer, Di<PERSON>Header, DialogTitle} from "@/src/components/ui/dialog";
import {StorePositionCounts} from "../../../api/src/setupSheets/storeSetupPositions/storeSetupPositionSchemas.ts";
import {Button} from "@/src/components/ui/button.tsx";
import {PositionWithAreaTitle} from "@/src/hooks/useStorePositionsWithAreas.tsx";
import {cn} from "@/src/util.ts";
import {StorePositionCountPicker} from "@/src/components/StorePositionCountPicker.tsx";

export interface SetupPositionCountsDialogProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  counts: StorePositionCounts;
  storePositions: PositionWithAreaTitle[];
  onSave: (counts: StorePositionCounts) => void;
}

export const SetupPositionCountsDialog: React.FC<SetupPositionCountsDialogProps> = ({
                                                                                      isOpen,
                                                                                      onSave,
                                                                                      counts,
                                                                                      storePositions,
                                                                                      onOpenChange
                                                                                    }) => {
  const [localCounts, setLocalCounts] = useState<StorePositionCounts>(counts);
  useEffect(() => {
    setLocalCounts(counts);
  }, [counts]);

  const onClose = () => {
    onOpenChange(false);
  }

  return <Dialog open={isOpen} onOpenChange={onOpenChange}>
    <DialogContent className={cn("max-w-[720px]")}>
      <DialogHeader>
        <DialogTitle>Select Store Positions</DialogTitle>
      </DialogHeader>
      <div className="max-h-[60vh] overflow-auto">
        <StorePositionCountPicker storePositions={storePositions} value={localCounts} onChange={(value) => {
          setLocalCounts(value);
        }}/>
      </div>
      <DialogFooter>
        <Button variant="outline" onClick={onClose}>
          Cancel
        </Button>
        <Button onClick={() => {
          onSave(localCounts)
          onClose();
        }}>
          Save
        </Button>
      </DialogFooter>
    </DialogContent>
  </Dialog>
}
