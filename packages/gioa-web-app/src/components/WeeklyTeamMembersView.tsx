import React, {useCallback, useMemo, useState} from 'react';
import {Table, TableBody, TableHead, TableHeader, TableRow} from "./ui/table";
import {filter, find, flatMap, groupBy, includes, isEmpty, map, mapValues, minBy, orderBy, sumBy, times} from "lodash";
import {DraftSchedule, PublishedSchedule} from "../../../api/src/scheduleSchemas.ts";
import {StoreAreaDto} from "../../../api/src/schemas.ts";
import {getDateTimeFromWeekDayTime, getRangeDurationHours} from "../../../api/src/date.util.ts";
import {getShifts} from "../../../api/src/getShiftsWithIsoWeek.ts";
import {useNavigate} from "@tanstack/react-router";
import {WeeklyViewFilter} from "@/src/components/WeeklyViewControlBar.types.ts";
import {WeeklyTableRow} from "@/src/components/WeeklyTableRow.tsx";
import {filterWeeklyViewPeople, filterWeeklyViewShifts} from "@/src/components/weeklyViewFilters.ts";
import {ArrowDownIcon, ChevronsUpDownIcon} from "lucide-react";
import {cn} from "@/src/util.ts";
import {getEffectiveAvailability} from "../../../api/src/getEffectiveAvailability.ts";
import {
  SchedulePersonClientDto,
  SchedulePersonDto,
  SchedulePersonTimeOffDto
} from "../../../api/src/schedulePersonDto.ts";
import {getPersonTotalWeekHours, getShiftChangeStatus} from "../../../api/src/scheduleBuilder.util.ts";
import {WeeklyTableShift} from "@/src/components/WeeklyTableTypes.ts";
import {getShift} from "../../../api/src/scheduling.util.ts";
import {CellPosition} from './WeeklyTableCellContent.tsx';
import {DraggableData, DraggableEvent} from 'react-draggable';
import {getShiftWeekday} from '@gioa/api/src/schedule.ts';

import {useAutoAnimate} from '@formkit/auto-animate/react'
import {TimeOffRequestSheet} from "@/src/components/TimeOffRequestSheet.tsx";
import {useDisclosure} from "@/src/hooks/useDisclosure.tsx";

export interface WeeklyTeamMembersViewProps {
  year: number;
  week: number;
  businessId: string;
  storeId: string;
  schedule: DraftSchedule;
  published: PublishedSchedule | undefined;
  routeToScheduleFrom: string;
  routeToScheduleTo?: string;
  people: SchedulePersonDto[];
  timezone: string
  storeAreas: StoreAreaDto[];
  onSortByDay: (day: number) => void;
  filters: WeeklyViewFilter[];
  daySort: number | undefined;
  sortColumn: string;
  sortAscending: boolean;
  search: string | undefined;
  views: string[];
  onShiftClick: (shift: WeeklyTableShift) => void;
  onShiftMove: (shiftId: string, personId: string, weekday: number) => void;
  onAddShift: (personId: string, day: number) => void;
}

const getCellKey = (personId: string, day: number): string => `${personId}-${day}`;

export const WeeklyTeamMembersView = React.memo((props: WeeklyTeamMembersViewProps) => {
  const {
    filters, views, daySort, onShiftClick, onShiftMove, onAddShift,
    search, sortAscending, sortColumn, schedule
  } = props;
  const weekStartDate = getDateTimeFromWeekDayTime({
    week: props.week,
    year: props.year,
    day: 1,
    time: "00:00",
    timezone: props.timezone
  });

  const people: SchedulePersonClientDto[] = useMemo(() => {
    if (!schedule) return [];

    return map(props.people, p => {
      const {
        availability: weekAvailability,
        preferences: weekPreferences
      } = getEffectiveAvailability(p.availability, {year: props.year, week: props.week}, props.timezone);
      return {
        ...p,
        weekAvailability: weekAvailability,
        weekPreferences: weekPreferences
      }
    });
  }, [props.people, schedule, props.timezone]);

  const personToShifts = useMemo(() => {
    const positions = flatMap(props.storeAreas, a => a.positions);
    const published = props.published;
    const allShifts = getShifts(schedule, (sched, day, area, shift): WeeklyTableShift => {
      const position = find(positions, p => p.id === shift.storePositionId);
      const publishedShift = published ? getShift(published, shift.id) : undefined;
      const shiftChangeStatus = getShiftChangeStatus({
        draftShift: shift,
        draftShiftDay: day.dayOfWeek,
        publishedShift: publishedShift,
        publishedShiftDay: published ? getShiftWeekday(published, shift.id) : undefined
      });
      const storeArea = find(props.storeAreas, sa => sa.id === area.storeAreaId);

      return {
        range: shift.range,
        title: shift.title ?? "",
        description: shift.description,
        id: shift.id,
        assignedPersonId: shift.assignedPersonId,
        // This is the schedule area, not the store area
        areaId: area.id,
        areaTitle: area.title,
        storeAreaId: storeArea?.id,
        isShiftLead: shift.isShiftLead,

        storePositionId: shift.storePositionId,
        storePositionTitle: position?.title,
        changeStatus: shiftChangeStatus,

        activities: shift.activities,

        isoWeek: {
          year: sched.week.year,
          week: sched.week.week,
          day: day.dayOfWeek
        }
      }
    });
    return groupBy(allShifts, s => s.assignedPersonId);
  }, [schedule, filters, props.storeAreas, props.published]);

  const personToFilteredShifts = useMemo(() => {
    return mapValues(personToShifts, shifts => filterWeeklyViewShifts({
      filters,
      shifts
    }));
  }, [personToShifts, filters]);

  const sortedPeople: SchedulePersonClientDto[] = useMemo(() => {
    const filteredPeople = filterWeeklyViewPeople({
      filters,
      people: people,
      personToShifts: personToFilteredShifts
    });
    const searchedPeople = search
      ? filter(filteredPeople, p => p.firstName.toLowerCase().includes(search.toLowerCase()) || p.lastName.toLowerCase().includes(search.toLowerCase()))
      : filteredPeople;
    const iteratees = daySort ?
      // if there's a day sort, put the people scheduled for that day first sorted by shift start time
      (p: SchedulePersonDto) => {
        const personShifts = personToFilteredShifts[p.id];
        const personShiftsForDay = filter(personShifts, s => s.isoWeek.day === daySort);
        return isEmpty(personShiftsForDay) ? "24:00" : minBy(personShiftsForDay, s => s.range.start)?.range.start;
      }
      :
      (p: SchedulePersonDto) => {
        switch (sortColumn) {
          case "hours":
            return getPersonTotalWeekHours(schedule, p);
          case "firstName":
            return p.firstName;
          case "lastName":
            return p.lastName;
          default:
            return 0;
        }
      };
    return orderBy(searchedPeople, iteratees, daySort ? "asc" : sortAscending ? "asc" : "desc");
  }, [filters, people, daySort, sortColumn, sortAscending, personToFilteredShifts, search, schedule]);

  const navigate = useNavigate();


  const [isDragging, setIsDragging] = useState(false);

  const handleDragStart = useCallback((shiftId: string, cellPosition: CellPosition) => {
    setIsDragging(true);
  }, []);

  const handleDragStop = useCallback((
    shiftId: string,
    data: DraggableData,
    e: DraggableEvent,
    sourceCell: CellPosition
  ) => {
    setIsDragging(false);

    // Find the element under the cursor when dragging stops
    const elementsUnderCursor = document.elementsFromPoint(
      (e as MouseEvent).clientX,
      (e as MouseEvent).clientY
    );

    // Find the TD element under the cursor
    const tdElement = elementsUnderCursor.find(el =>
      el.tagName === 'TD' && el.hasAttribute('data-person-id') && el.hasAttribute('data-day')
    ) as HTMLTableCellElement | undefined;

    if (!tdElement) return;

    // Get the target cell's person ID and day
    const targetPersonId = tdElement.getAttribute('data-person-id');
    const targetDay = tdElement.getAttribute('data-day');

    if (!targetPersonId || targetDay === null) return;

    const sourceCellKey = getCellKey(sourceCell.personId, sourceCell.day);
    const targetWeekday = parseInt(targetDay);
    const targetCellKey = getCellKey(targetPersonId, targetWeekday);

    // Don't do anything if source and target are the same
    if (sourceCellKey === targetCellKey) return;

    console.log("move", shiftId, targetPersonId, targetWeekday);
    onShiftMove(shiftId, targetPersonId, targetWeekday);
  }, [onShiftMove]);

  const [autoContainerRef] = useAutoAnimate();
  const timeOffSheet = useDisclosure();
  const [timeOffRequestId, setTimeOffRequestId] = useState<string>();
  const onTimeOffClick = useCallback((timeOff: SchedulePersonTimeOffDto) => {
    setTimeOffRequestId(timeOff.requestId);
    timeOffSheet.onOpen();
  }, []);

  return (
    <div>
      <Table className={cn("h-fit", {"is-dragging": isDragging})}>
        <TableHeader className={"bg-blue-50 "}>
          <TableRow>
            <TableHead className={"sticky top-0 bg-blue-50 z-10 bg-opacity-50 backdrop-blur"}>Team Member</TableHead>
            {times(7, i => {
              const dayOfWeek = i + 1;
              const date = weekStartDate.plus({days: i});
              return <TableHead key={i}
                                className={"text-center pr-0 last:border-0 border-r sticky top-0 bg-blue-50 z-10 bg-opacity-50 backdrop-blur"}>
                <button onClick={() => props.onSortByDay(dayOfWeek)}
                        className={cn("w-full h-full flex justify-center items-center hover:underline whitespace-nowrap", {
                          "font-bold": daySort === dayOfWeek,
                        })}
                        title={"Sort shifts by day, ascending"}>
                  {date.toLocaleString({
                    weekday: "short",
                    month: 'numeric',
                    day: 'numeric'
                  })}
                  {daySort === dayOfWeek
                    ? <ArrowDownIcon size={16} className={"ml-1"}/>
                    : <ChevronsUpDownIcon size={16} className={"ml-1"}/>}
                </button>
              </TableHead>
            })}
          </TableRow>
        </TableHeader>
        <TableBody ref={autoContainerRef}>

          {isEmpty(sortedPeople) ?
            <div className={"text-center text-muted-foreground py-6"}>
              No team members{search || !isEmpty(filters) ? " match your criteria" : "found"}.
            </div>
            : null
          }

          {map(sortedPeople, (person) => {
            const filteredShifts = personToFilteredShifts[person.id] ?? [];
            const shifts = personToShifts[person.id] ?? [];
            const totalWeekHours = Math.floor(sumBy(shifts, shift => getRangeDurationHours(shift.range)));
            const filteredWeekHours = Math.floor(sumBy(filteredShifts, shift => getRangeDurationHours(shift.range)));

            return <WeeklyTableRow key={person.id} week={props.week} onShiftClick={onShiftClick}
                                   onAddShift={onAddShift} onDragStart={handleDragStart} onDragStop={handleDragStop}
                                   year={props.year} timezone={props.timezone}
                                   showAvailability={includes(views, "avail")}
                                   showTimeOff={includes(views, "timeOff")} onTimeOffClick={onTimeOffClick}
                                   totalWeekHours={totalWeekHours} filteredWeekHours={filteredWeekHours}
                                   shifts={filteredShifts} person={person}/>
          })}
        </TableBody>
      </Table>

      {timeOffRequestId ?
        <TimeOffRequestSheet isOpen={timeOffSheet.isOpen}
                             businessId={props.businessId}
                             storeId={props.storeId}
                             onOpenChange={timeOffSheet.setOpen}
                             timeOffId={timeOffRequestId}/> : null}
    </div>
  );
});

WeeklyTeamMembersView.displayName = "WeeklyTeamMembersView";
