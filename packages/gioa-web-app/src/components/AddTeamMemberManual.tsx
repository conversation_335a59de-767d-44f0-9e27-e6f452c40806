import React from "react";
import { useForm } from "@tanstack/react-form";
import { zodValidator } from "@tanstack/zod-form-adapter";
import { z } from "zod";
import { FormControl } from "@/src/components/form/FormControl.tsx";
import { Label } from "@/src/components/ui/label.tsx";
import { FormInput } from "@/src/components/form/FormInput.tsx";
import { FieldInfo } from "@/src/components/form/FieldInfo.tsx";
import { Button } from "@/src/components/ui/button.tsx";
import { api } from "@/src/api.ts";
import { toast } from "sonner";
import { ErrorAlert } from "@/src/components/ErrorAlert.tsx";

interface AddTeamMemberManualProps {
  storeId: string;
  onClose?: () => void;
}

export function AddTeamMemberManual({ storeId, onClose }: AddTeamMemberManualProps) {
  const apiUtil = api.useUtils();
  const sendInvite = api.user.createAndInvitePerson.useMutation();

  const form = useForm({
    defaultValues: {
      firstName: "",
      lastName: "",
      phoneNumber: "",
      email: "",
    },
    onSubmit: ({ value }) => {
      if (!storeId) {
        toast.error("Store ID is required");
        return;
      }

      sendInvite.mutate(
        {
          firstName: value.firstName,
          lastName: value.lastName,
          storeIds: [storeId],
          phoneNumber: value.phoneNumber || undefined,
          email: value.email || undefined,
          requiresApproval: true,
          transport: "phone",
          initDestination: "dest_onboarding",
        },
        {
          onSuccess: () => {
            toast.success(`Sent invitation to ${value.firstName}`);
            apiUtil.user.getStore.invalidate();
            form.reset();
            if (onClose) {
              onClose();
            }
          },
        },
      );
    },
    validatorAdapter: zodValidator(),
  });

  const isLoading = sendInvite.isPending;

  return (
    <div className="space-y-4 ">
      {sendInvite.isError && <ErrorAlert error={sendInvite.error} />}

      <form
        onSubmit={(e) => {
          e.preventDefault();
          e.stopPropagation();
          form.handleSubmit();
        }}
        className="space-y-4"
      >
        <form.Field
          name="firstName"
          validators={{
            onSubmit: z.string().min(1, "Required"),
          }}
        >
          {(field) => (
            <FormControl>
              <Label htmlFor={field.name}>First Name</Label>
              <FormInput field={field} placeholder="Enter first name" />
              <FieldInfo field={field} />
            </FormControl>
          )}
        </form.Field>

        <form.Field
          name="lastName"
          validators={{
            onSubmit: z.string().min(1, "Required"),
          }}
        >
          {(field) => (
            <FormControl>
              <Label htmlFor={field.name}>Last Name</Label>
              <FormInput field={field} placeholder="Enter last name" />
              <FieldInfo field={field} />
            </FormControl>
          )}
        </form.Field>

        <form.Field
          name="phoneNumber"
          validators={{
            // Using a simpler validation for phone number since we don't have direct access to the phoneNumberSchema
            onSubmit: z
              .string()
              .min(10, "Please enter a valid phone number")
              .regex(/^\d{10}$|^\(\d{3}\)\s?\d{3}-\d{4}$|^\d{3}-\d{3}-\d{4}$/, "Please enter a valid phone number"),
          }}
        >
          {(field) => (
            <FormControl>
              <Label htmlFor={field.name}>Phone Number</Label>
              <FormInput field={field} placeholder="************" type="tel" />
              <FieldInfo field={field} />
            </FormControl>
          )}
        </form.Field>

        <form.Field
          name="email"
          validators={{
            onSubmit: z.string().email("Please enter a valid email").optional().or(z.literal("")),
          }}
        >
          {(field) => (
            <FormControl>
              <Label htmlFor={field.name}>Email (Optional)</Label>
              <FormInput field={field} placeholder="Enter email address" type="email" />
              <FieldInfo field={field} />
            </FormControl>
          )}
        </form.Field>

        <div className="flex justify-end space-x-2 pt-4">
          {onClose && (
            <Button type="button" variant="outline" onClick={onClose} disabled={isLoading} className="mr-2">
              Cancel
            </Button>
          )}
          <Button type="submit" disabled={isLoading}>
            {isLoading ? "Sending Invite..." : "Send Invite"}
          </Button>
        </div>
      </form>
    </div>
  );
}
