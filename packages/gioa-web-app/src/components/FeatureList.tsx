import React from "react";
import {map} from "lodash";
import {Text} from "@/src/components/Text.tsx";
import {Switch} from "@/src/components/ui/switch.tsx";

interface Feature {
  id: string;
  name: string;
  description: string;
  isEnabled: boolean;
}

interface FeatureListProps {
  features: Feature[];
  onToggle: (featureId: string, isEnabled: boolean) => void;
}

export function FeatureList({features, onToggle}: FeatureListProps) {
  return (
    <div className={"bg-white px-6 py-4 rounded-lg max-w-lg"}>
      {map(features, feature => (
        <div key={feature.id} className={"flex justify-between items-center py-2 border-b last:border-b-0"}>
          <div>
            <Text className={"text-medium block"}>{feature.name}</Text>
            <Text size={"sm"} className={"text-muted-foreground"}>{feature.description}</Text>
          </div>
          <Switch
            checked={feature.isEnabled}
            onCheckedChange={(isEnabled) => onToggle(feature.id, isEnabled)}
          />
        </div>
      ))}
      {features.length === 0 && (
        <Text className="text-muted-foreground">No features available</Text>
      )}
    </div>
  );
}
