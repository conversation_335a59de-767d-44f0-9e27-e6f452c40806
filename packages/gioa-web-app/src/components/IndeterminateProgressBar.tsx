import React from 'react';
import {cva, type VariantProps} from 'class-variance-authority';

const progressBarVariants = cva(
  'w-full overflow-hidden rounded-full bg-gray-200',
  {
    variants: {
      height: {
        thin: 'h-1',
        normal: 'h-2',
        thick: 'h-3',
      },
    },
    defaultVariants: {
      height: 'normal',
    },
  }
);

const progressIndicatorVariants = cva(
  'rounded-full w-1/3',
  {
    variants: {
      height: {
        thin: 'h-1',
        normal: 'h-2',
        thick: 'h-3',
      },
      color: {
        primary: 'bg-blue-500',
        secondary: 'bg-purple-500',
        success: 'bg-green-500',
        warning: 'bg-yellow-500',
        error: 'bg-red-500',
      },
      variant: {
        pulse: 'animate-pulse',
        bounce: 'animate-bounce',
        slide: 'animate-slide',
      },
    },
    defaultVariants: {
      height: 'normal',
      color: 'primary',
      variant: 'slide',
    },
  }
);

type ProgressBarProps = VariantProps<typeof progressBarVariants> &
  VariantProps<typeof progressIndicatorVariants> & {
  className?: string;
};

/**
 * An animated indeterminate progress bar component
 */
const IndeterminateProgressBar: React.FC<ProgressBarProps> = ({
                                                                height,
                                                                color,
                                                                variant,
                                                                className = '',
                                                              }) => {
  return (
    <div className={progressBarVariants({ height, className })}>
      <style>{`
        @keyframes slide {
          0% {
            transform: translateX(-100%);
          }
          50% {
            transform: translateX(200%);
          }
          100% {
            transform: translateX(-100%);
          }
        }
        .animate-slide {
          animation: slide 1.5s ease-in-out infinite;
        }
      `}</style>
      <div
        className={progressIndicatorVariants({ height, color, variant })}
      />
    </div>
  );
};

export default IndeterminateProgressBar;
