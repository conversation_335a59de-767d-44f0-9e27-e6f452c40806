import React from 'react';
import { Text } from './Text';
import { PersonCorrectiveActionNote } from './PersonCorrectiveActionNote';
import { PersonPositionPerformanceDataPointNote } from './PersonPositionPerformanceDataPointNote';
import { PersonGeneralNote } from './PersonGeneralNote';
import {
  PersonCorrectiveActionNoteDto,
  PersonGeneralNoteDto,
  PersonNoteDto,
  PersonPositionPerformanceDataPointNoteDto
} from "../../../api/src/schemas.ts";

export interface PersonNoteProps {
  note: PersonNoteDto;
  onClickNote: (note: PersonNoteDto) => void;
}

export const PersonNote = React.memo(({ note, onClickNote }: PersonNoteProps) => {
  const renderNoteType = (type: string) => {
    switch (type) {
      case "correctiveAction":
        return <PersonCorrectiveActionNote note={note as PersonCorrectiveActionNoteDto} />;
      case "general":
        return <PersonGeneralNote note={note as PersonGeneralNoteDto} />;
      case "positionPerformanceDataPoint":
        return <PersonPositionPerformanceDataPointNote note={note as PersonPositionPerformanceDataPointNoteDto} />;
      default:
        return <Text>Unknown note type</Text>;
    }
  }

  return <div className={"cursor-pointer"} onClick={() => onClickNote(note)}>
    {renderNoteType(note.type)}
  </div>
});
