import React, {useState} from 'react';
import {ArrowLeftIcon, CalendarPlusIcon, ChevronRightIcon, SearchIcon} from "lucide-react";
import {Button} from "@/src/components/ui/button.tsx";
import {Input} from "@/src/components/ui/input.tsx";
import {filter, find, map} from 'lodash';
import {cn} from "@/src/util.ts";
import {motion} from "framer-motion";
import {EventDetailsPanelContent} from "@/src/components/EventDetailsPanelContent.tsx";
import {addHours, getISODay} from "date-fns";
import {genScheduleEventId} from "../../../api/src/schemas.ts";
import {DayOfWeek, IsoWeekDate} from "../../../api/src/timeSchemas.ts";
import {daysOfWeek} from "@/src/components/AvailabilityWeek.tsx";
import {getDateFromWeekDayTime} from "../../../api/src/date.util.ts";
import {ScheduleEventDto} from '@gioa/api/src/scheduleEventSchemas.ts';
import {formatDateInStoreTZ, formatDayOfWeek} from "../../../api/src/scheduleBuilder.util.ts";

export interface EventsPanelContentProps {
  onBack: () => void;
  isoWeek: IsoWeekDate;
  dayOfWeek: DayOfWeek;
  events: ScheduleEventDto[];
  onViewEventDetails: (event: ScheduleEventDto) => void;
  onPanelBack: () => void;
  onUpsertEvent: (event: ScheduleEventDto) => void;
  onDeleteEvent: (event: ScheduleEventDto) => void;
  selectedEventId?: string;
  timezone: string | null;
  storeId: string;
}

export const EventsPanelContent: React.FC<EventsPanelContentProps> = ({
                                                                        onBack,
                                                                        isoWeek, storeId,
                                                                        events: unfilteredEvents,
                                                                        selectedEventId,
                                                                        onViewEventDetails, onPanelBack,
                                                                        dayOfWeek, timezone,
                                                                        onDeleteEvent: _onDeleteEvent,
                                                                        onUpsertEvent: _onUpsertEvent,
                                                                      }) => {
  const [searchInput, setSearchInput] = useState("");
  const events = filter(unfilteredEvents, evt => {
    const startIsoDay = getISODay(evt.range.start);
    const endIsoDay = getISODay(evt.range.end);
    return evt.title.toLowerCase().includes(searchInput.toLowerCase())
      && (startIsoDay === dayOfWeek
        || endIsoDay === dayOfWeek
        || (dayOfWeek > startIsoDay && dayOfWeek < endIsoDay));
  });
  const selectedEvent = find(unfilteredEvents, e => e.id === selectedEventId);
  const isOnList = !Boolean(selectedEvent);

  const onViewDetails = (event: ScheduleEventDto) => {
    onViewEventDetails(event);
  };

  const goBackToList = () => {
    onPanelBack();
  }

  const onUpdateEvent = (event: ScheduleEventDto) => {
    _onUpsertEvent(event);
    goBackToList();
  }

  const onDeleteEvent = (event: ScheduleEventDto) => {
    _onDeleteEvent(event);
    goBackToList();
  }

  const onCreateEventClick = () => {
    const start = getDateFromWeekDayTime({
      year: isoWeek.year,
      week: isoWeek.week,
      day: dayOfWeek,
      time: "09:00",
      timezone: timezone
    });
    onViewDetails({
      range: {
        start: start,
        end: addHours(start, 4),
      },
      id: genScheduleEventId(),
      title: "New event",
      description: "",
      eventType: "", // TODO
      visibilityLevel: 0 // TODO
    })
  }

  const dayObj = find(daysOfWeek, d => d.dayOfWeek === dayOfWeek);

  return (
    <div className={"relative w-full h-full overflow-hidden"}>
      <motion.div key="list" className={"absolute top-0 right-0 bottom-0 left-0 overflow-auto"}
                  initial={{x: !isOnList ? "-100%" : 0}}
                  animate={{x: !isOnList ? "-100%" : 0}}
                  exit={{x: "-100%"}}
                  transition={{type: "tween", ease: "easeInOut", duration: 0.2}}>
        <div>
          <Button onClick={onBack} aria-label={"Back"} className={"mt-2 ms-1"}
                  variant={"ghost"} size={"sm"}>
            <ArrowLeftIcon size={24} className={"text-gray-600"}/>
          </Button>
        </div>
        <div className="px-4 flex items-center gap-6">
          <div>
            <CalendarPlusIcon size={36} className={"text-gray-600"}/>
          </div>
          <div className={"text-left"}>
            <p className={"font-medium mb-1"}>
              Events on {dayObj?.name}
            </p>
            <p className={"text-sm"}>
              Add or edit events happening at your store.
            </p>
          </div>
        </div>

        <div className="px-4 py-4 border-b border-gray-200">
          <Input
            type="search"
            leftIcon={SearchIcon}
            placeholder="Search..."
            value={searchInput}
            onChange={(event) => setSearchInput(event.target.value)}
            className="max-w-sm"
          />
        </div>

        <ul className={"overflow-y-auto grow"}>
          {map(events, evt => {
            return <li key={evt.id}>
              <button onClick={() => onViewDetails(evt)}
                      className={cn("flex items-center gap-2 border-b text-left w-full px-4 py-2 hover:bg-gray-50")}>
                <div className={"grow"}>
                  <div className={"font-medium"}>
                    {evt.title}
                  </div>
                  <div className={"text-gray-700"}>
                    {formatEventDateTimeRange(evt)}
                  </div>
                </div>
                <div>
                  <ChevronRightIcon size={24} className="text-gray-500"/>
                </div>
              </button>
            </li>
          })}
        </ul>

        <div className={"px-4 py-2 bg-white border-t"}>
          <Button variant={"outline"} onClick={onCreateEventClick}>
            Create New Event
          </Button>
        </div>
      </motion.div>
      <motion.div key="details" className={"absolute top-0 right-0 bottom-0 left-0 overflow-auto"}
                  initial={{x: "100%"}}
                  animate={{x: !isOnList ? 0 : "100%"}}
                  exit={{x: "100%"}}
                  transition={{type: "tween", ease: "easeInOut", duration: 0.2}}>
        <div className={"flex gap-2 items-center"}>
          <Button onClick={goBackToList} aria-label={"Back"} className={"my-2 ms-1"}
                  variant={"ghost"} size={"sm"}>
            <ArrowLeftIcon size={24} className={"text-gray-600"}/>
          </Button>
          <h3 className={"mb-0 text-lg font-bold"}>Event Details</h3>
        </div>
        {selectedEvent && <>
            <EventDetailsPanelContent onUpdateEvent={onUpdateEvent} storeId={storeId}
                                      onDeleteEvent={onDeleteEvent}
                                      timezone={timezone}
                                      event={selectedEvent}/>
        </>}
      </motion.div>
    </div>
  );
}

function formatEventDateTimeRange(event: ScheduleEventDto): string {
  const startDate = formatDateInStoreTZ.format(event.range.start);
  const endDate = formatDateInStoreTZ.format(event.range.end);
  const format = (date: Date) => `${formatDayOfWeek.format(date)} ${formatDateInStoreTZ.format(date)}`;

  if (startDate === endDate) {
    return format(event.range.start);
  }
  return `${format(event.range.start)} — ${format(event.range.end)}`;
}
