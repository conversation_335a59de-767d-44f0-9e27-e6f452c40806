import React from 'react';
import {cn} from "@/src/util.ts";
import {Button} from "@/src/components/ui/button.tsx";
import {ProficiencyRating} from "@/src/components/ProficiencyRating.tsx";
import {RowData} from "@/src/components/TimelineGrid.tsx";
import {SchedulePersonDto} from "../../../api/src/schedulePersonDto.ts";
import {getDurationAvailable, isAvailabilityRow, ReportSpanMeta} from "@/src/availabilityReport.ts";
import {isEmpty} from "lodash";

export interface ReportTeamMemberSidebarRowProps {
  rowHeight: number;
  row: RowData<SchedulePersonDto, ReportSpanMeta>;
  onClickTeamMember: (row: RowData<SchedulePersonDto, ReportSpanMeta>) => void;
}

export const ReportTeamMemberSidebarRow: React.FC<ReportTeamMemberSidebarRowProps> = React.memo(({
                                                                                                   rowHeight,
                                                                                                   row,
                                                                                                   onClickTeamMember
                                                                                                 }) => {
  const person = row.metadata;

  return <div style={{height: rowHeight}} key={row.id + "sidebar"}
              className={cn("w-full py-1 px-4")}>
    <Button variant={"outline"} size={"sm"}
            onClick={() => onClickTeamMember(row)}
            className={cn("w-full justify-between h-full gap-4 gioa-team-member-select")}>

        <span>
                    <span className={"max-w-[16rem] overflow-ellipsis overflow-hidden text-left"}
                          style={{minWidth: "14ch"}}>
                      {person.firstName + " " + person.lastName}
                    </span>
          {!isEmpty(row.spans) ?
            <span
              className={"text-muted-foreground text-sm ml-2"}>{
              isAvailabilityRow(row)
                ? getDurationAvailable(row)
                : ""
            }</span> : null}
        </span>
      <ProficiencyRating rank={person ? person.proficiencyRanking ?? 0 : 0}
                         colorScheme={"light-bg"}/>
    </Button>
  </div>
})
