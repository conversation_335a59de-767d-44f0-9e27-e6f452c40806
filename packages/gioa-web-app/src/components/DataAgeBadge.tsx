import React from 'react';
import {Badge} from "@/src/components/ui/badge.tsx";
import {calculateDataAge, calculateDaysAge} from "@/src/date.util.ts";

export interface DataAgeBadgeProps {
  dataRangeEnd: Date;
  timezone: string;
}

export const getDataAgeBadgeColorScheme = (days: number) => {
  if (days < 8) {
    return "success";
  } else if (days < 14) {
    return "yellow";
  } else {
    return "destructive";
  }
}

export const DataAgeBadge: React.FC<DataAgeBadgeProps> = ({dataRangeEnd, timezone}) => {
  const days = calculateDaysAge(dataRangeEnd, timezone);
  const badgeColor = getDataAgeBadgeColorScheme(days);

  return (
    <Badge colorScheme={badgeColor as any}>
      {calculateDataAge(dataRangeEnd, timezone)}
    </Badge>
  );
}
