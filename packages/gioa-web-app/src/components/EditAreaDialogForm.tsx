import React from 'react';
import {FormControl} from "@/src/components/form/FormControl.tsx";
import {Label} from "@/src/components/ui/label.tsx";
import {FormInput} from "@/src/components/form/FormInput.tsx";
import {FieldInfo} from "@/src/components/form/FieldInfo.tsx";
import {FormTextarea} from "@/src/components/form/FormTextarea.tsx";
import {Checkbox} from "@/src/components/ui/checkbox.tsx";
import {Text} from "@/src/components/Text.tsx";
import {DialogDescription, DialogFooter, DialogHeader, DialogTitle} from "@/src/components/ui/dialog.tsx";
import {Button} from "@/src/components/ui/button.tsx";
import {zodValidator} from "@tanstack/zod-form-adapter";
import {z} from "zod";
import {ArrowLeftIcon} from "lucide-react";
import {useForm} from "@tanstack/react-form";
import {genScheduleAreaId, StoreAreaDto} from "../../../api/src/schemas.ts";
import {Select, SelectContent, SelectItem, SelectTrigger, SelectValue} from "@/src/components/ui/select.tsx";
import {api} from "@/src/api.ts";
import {isEmpty, map} from "lodash";
import {ScheduleArea} from "../../../api/src/scheduleBuilder.util.ts";

export interface EditAreaDialogFormProps {
  area?: ScheduleArea;
  onSubmit: (area: ScheduleArea) => void;
  onCancel: () => void;
  storeAreas: StoreAreaDto[];
}

export const EditAreaDialogForm: React.FC<EditAreaDialogFormProps> = ({area, storeAreas, onSubmit, onCancel}) => {
  const form = useForm({
    defaultValues: {
      title: area?.title ?? '',
      description: area?.description ?? '',
      isNonOps: area ? !area.countsTowardsLabor : false,
      storeAreaId: area?.storeAreaId ?? ''
    },
    onSubmit: async ({value}) => {
      onSubmit({
        id: area?.id ?? genScheduleAreaId(),
        title: value.title,
        description: value.description,
        countsTowardsLabor: !value.isNonOps,
        storeAreaId: value.storeAreaId
      })
    },
    validatorAdapter: zodValidator(),
  });

  const isNonOps = form.useStore(s => s.values.isNonOps);

  return (
    <form onSubmit={(e) => {
      e.preventDefault()
      e.stopPropagation()
      form.handleSubmit()
    }}>
      <form.Field name={"title"}
                  validators={{
                    onSubmit: z.string().min(1)
                  }}
                  children={(field) => {
                    return <FormControl>
                      <Label htmlFor={field.name}>Work area title</Label>
                      <FormInput field={field}
                                 placeholder="Enter title..."/>
                      <FieldInfo field={field}/>
                    </FormControl>;
                  }}/>

      <form.Field name={"description"}
                  validators={{
                    onSubmit: z.string().optional()
                  }}
                  children={(field) => {
                    return <FormControl>
                      <Label htmlFor={field.name}>Description (optional)</Label>
                      <FormTextarea field={field}
                                    placeholder="Enter comments or description of this work area..."/>
                      <FieldInfo field={field}/>
                    </FormControl>;
                  }}/>

      <form.Field name={"isNonOps"}
                  validators={{
                    onSubmit: z.boolean()
                  }}
                  children={(field) => {
                    return <div
                      className={"flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4 bg-white mb-3"}>
                      <Checkbox id={field.name}
                                onCheckedChange={isChecked => typeof isChecked === "boolean" ? field.handleChange(isChecked) : null}
                                checked={field.state.value}>
                        {/*{map(store.data?.)}*/}
                      </Checkbox>
                      <div className="space-y-1 leading-none">
                        <Label htmlFor={field.name} className={"block"}>
                          Admin/Non-Operations
                        </Label>
                        <Text size={"sm"} className={"block"}>
                          Select if this work area does not towards labor hours
                        </Text>
                      </div>
                      <FieldInfo field={field}/>
                    </div>
                  }}/>

      <form.Field name={"storeAreaId"}
                  children={(field) => {
                    return <FormControl>
                      <Label htmlFor={field.name}>Store Area</Label>
                      <Select onValueChange={field.handleChange} disabled={isNonOps}
                              value={isNonOps ? undefined : field.state.value}>
                        <SelectTrigger hasError={!isEmpty(field.state.meta.errors)}>
                          <SelectValue placeholder="Select a store area"/>
                        </SelectTrigger>
                        <SelectContent>
                          {map(storeAreas, area =>
                            <SelectItem key={area.id} value={area.id}>
                              {area.title}
                            </SelectItem>)}
                        </SelectContent>
                      </Select>
                      <FieldInfo field={field}/>
                      <Text size={"sm"} muted>
                        Does this area on the schedule correspond to an area in the store?
                      </Text>
                    </FormControl>;
                  }}/>

      <DialogFooter>
        <Button variant={"outline"} type={"button"}
                onClick={onCancel}>
          Cancel
        </Button>
        <Button type={"submit"}>
          Save
        </Button>
      </DialogFooter>
    </form>
  );
}
