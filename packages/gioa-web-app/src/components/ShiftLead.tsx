import React from "react";
import {PersonAvatar} from "@/src/components/PersonAvatar.tsx";
import {Text} from "@/src/components/Text.tsx";
import {DailyTimeRange} from "../../../api/src/timeSchemas.ts";
import {convertTo12HourFormat} from "../../../api/src/date.util.ts";
import {SchedulePersonDto} from "../../../api/src/schedulePersonDto.ts";

interface ShiftLeadProps {
  range: DailyTimeRange;
  person?: SchedulePersonDto;
  showPosition?: boolean;
}

export const ShiftLead: React.FC<ShiftLeadProps> = ({ range, person, showPosition = true }) => {
  return <div className="flex items-center">
    <PersonAvatar className="h-10 w-10 mr-3" person={person}/>
    <div className="flex flex-col">
      <Text size="sm" className="font-medium">{person?.firstName} {person?.lastName}</Text>
      <Text size="xs" muted>{showPosition ? "Shift Lead" : null} {range ? `${convertTo12HourFormat(range.start)} - ${convertTo12HourFormat(range.end)}` : null}</Text>
    </div>
  </div>
};
