import React from 'react';
import {But<PERSON>} from '@/src/components/ui/button';
import {ReactFormExtendedApi} from '@tanstack/react-form';

export interface ChecklistEventActionButtonsProps {
  formIsDirty: boolean;
  form: ReactFormExtendedApi<any, any>;
  isSavePending: boolean;
}

export const Checklist2EventActionButtons: React.FC<ChecklistEventActionButtonsProps> = ({
  formIsDirty,
  form,
  isSavePending,
}) => {


  if (formIsDirty) {
    return (
      <div className="flex flex-row gap-2 py-4 mt-4 border-t border-gray-200">
        <Button
          variant="outline"
          disabled={isSavePending}
          onClick={() => form.reset()}
          className="flex-1"
        >
          Discard Changes
        </Button>
        <Button
          onClick={() => form.handleSubmit()}
          disabled={isSavePending}
          className="flex-1"
        >
          Save Changes
        </Button>
      </div>
    );
  }

  return null;
};
