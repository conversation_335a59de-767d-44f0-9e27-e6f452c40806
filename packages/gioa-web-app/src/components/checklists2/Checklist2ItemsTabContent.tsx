import React from 'react';
import {Text} from '@/src/components/Text';
import {Card} from '@/src/components/ui/card';
import {Button} from '@/src/components/ui/button';
import {ChevronRightIcon, CirclePlusIcon, MoveVertical} from 'lucide-react';
import {closestCenter, DndContext, KeyboardSensor, PointerSensor, useSensor, useSensors} from '@dnd-kit/core';
import {
  SortableContext,
  sortableKeyboardCoordinates,
  useSortable,
  verticalListSortingStrategy
} from '@dnd-kit/sortable';
import {CSS} from '@dnd-kit/utilities';
import {restrictToVerticalAxis} from '@dnd-kit/modifiers';
import {map} from "lodash";

export interface ChecklistItem {
  id: string;
  title: string;
  description?: string;
}

export interface Checklist2ItemsTabContentProps {
  checklist: any; // This would be properly typed in a real implementation
  isReordering: boolean;
  onStartReordering: () => void;
  reorderedItems: ChecklistItem[];
  onItemDragged: (params: { data: ChecklistItem[], from: number, to: number }) => void;
  addItemHref: () => void;
  editItemHref: (itemId: string) => void;
}

const SortableItem = ({item, editItemHref}: { item: ChecklistItem, editItemHref: (itemId: string) => void }) => {
  const {attributes, listeners, setNodeRef, transform, transition} = useSortable({id: item.id});

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  };

  return (
    <div ref={setNodeRef} style={style} {...attributes} {...listeners} className="mb-4">
      <Card className="p-4 flex justify-between items-center hover:bg-gray-50 cursor-pointer">
        <div className="flex items-center w-full">
          <MoveVertical size={20} className="mr-3 text-gray-400"/>
          <div className="flex flex-col gap-1 flex-1">
            <Text semibold>{item.title}</Text>
            {item.description && (
              <Text className="text-muted-foreground">{item.description}</Text>
            )}
          </div>
        </div>
      </Card>
    </div>
  );
};

export const Checklist2ItemsTabContent = React.memo(({
                                                       checklist,
                                                       isReordering,
                                                       onStartReordering,
                                                       reorderedItems,
                                                       onItemDragged,
                                                       addItemHref,
                                                       editItemHref
                                                     }: Checklist2ItemsTabContentProps) => {
  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  const handleDragEnd = (event: any) => {
    const {active, over} = event;

    if (active.id !== over.id) {
      const oldIndex = reorderedItems.findIndex(item => item.id === active.id);
      const newIndex = reorderedItems.findIndex(item => item.id === over.id);

      const newItems = [...reorderedItems];
      const [removed] = newItems.splice(oldIndex, 1);
      newItems.splice(newIndex, 0, removed);

      onItemDragged({
        data: newItems,
        from: oldIndex,
        to: newIndex
      });
    }
  };

  return (
    <div className="flex-1">
      {isReordering ? (
        <div>
          <p className="mb-4">
            Drag and drop to reorder items.
          </p>
          <DndContext
            sensors={sensors}
            collisionDetection={closestCenter}
            onDragEnd={handleDragEnd}
            modifiers={[restrictToVerticalAxis]}
          >
            <SortableContext
              items={reorderedItems.map(item => item.id)}
              strategy={verticalListSortingStrategy}
            >
              {reorderedItems.map(item => (
                <SortableItem key={item.id} item={item} editItemHref={editItemHref}/>
              ))}
            </SortableContext>
          </DndContext>
        </div>
      ) : (
        <div>
          {map(checklist.items, (item) => (
            <Card
              key={item.id}
              className="px-4 py-3 flex justify-between items-center hover:bg-gray-50 cursor-pointer mb-3"
              onClick={() => editItemHref(item.id)}
            >
              <div className="flex flex-col gap-1">
                <Text>{item.title}</Text>
                {item.description && (
                  <Text size={"sm"} className="text-muted-foreground">{item.description}</Text>
                )}
              </div>
              <ChevronRightIcon size={24} className="text-gray-800 flex-shrink-0" strokeWidth={1.5}/>
            </Card>
          ))}

          <div className="flex flex-row gap-2 pt-2">
            <Button leftIcon={<CirclePlusIcon size={18}/>}
                    colorScheme={"blue"}
                    onClick={addItemHref}>
              Add Checklist Item
            </Button>
            <Button
              variant="secondary" title={"Reorder Items"}
              onClick={onStartReordering}>
              <MoveVertical size={18}/>
            </Button>
          </div>
        </div>
      )}
    </div>
  );
});
