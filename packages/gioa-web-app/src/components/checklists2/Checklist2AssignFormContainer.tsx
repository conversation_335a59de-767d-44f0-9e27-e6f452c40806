import React, {useEffect, useState} from 'react';
import {useForm} from '@tanstack/react-form';
import {toast} from 'sonner';
import {zodValidator} from '@tanstack/zod-form-adapter';
import {api} from '@/src/api';
import {ChecklistInstanceDto, ChecklistUpdateType} from '../../../../api/src/checklists/checklist/checklistDto';
import {Checklist2EventActionButtons} from '@/src/components/checklists2/Checklist2EventActionButtons';
import {Checklist2AssignFormFields} from '@/src/components/checklists2/Checklist2AssignFormFields';
import {Text} from '@/src/components/Text';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle
} from '@/src/components/ui/alert-dialog';

export interface ChecklistAssignFormEventContainerProps {
  checklist: ChecklistInstanceDto;
  canUpdateSeries: boolean;
  storeId: string;
  onFormDirty?: (isDirty: boolean) => void;
  onSave: () => void;
}

export const Checklist2AssignFormContainer: React.FC<ChecklistAssignFormEventContainerProps> = ({
  checklist,
  canUpdateSeries,
  storeId,
  onFormDirty,
  onSave
}) => {
  const saveAssign = api.checklist2.updateChecklistAssign.useMutation();
  const apiUtil = api.useUtils();
  const [isRecurrenceDialogOpen, setIsRecurrenceDialogOpen] = useState(false);
  const [formValues, setFormValues] = useState<any>(null);

  const { data: store } = api.user.getStore.useQuery({ storeId: storeId }, {
    staleTime: 1000 * 60 * 60 // 1 hour
  });

  const form = useForm({
    defaultValues: {
      assignPeopleIds: checklist.assignPeopleIds ?? [],
      assignShiftLead: checklist.assignShiftLead,
      assignStoreAreaId: checklist.assignStoreAreaId,
      assignScheduleAreaTitle: checklist.assignScheduleAreaTitle,
      assignStorePositionId: checklist.assignStorePositionId
    },
    validatorAdapter: zodValidator(),
    onSubmit: (event) => {
      const vals = event.value;
      setFormValues(vals);

      if (checklist.rrule && canUpdateSeries) {
        setIsRecurrenceDialogOpen(true);
      } else {
        doUpdate("this", vals);
      }
    },
  });

  const doUpdate = (updateType: ChecklistUpdateType, vals: any) => {
    saveAssign.mutate({
      storeId: storeId,
      checklistId: checklist.id,
      recurrenceId: checklist.recurrenceId,
      version: checklist.version,
      updateType: updateType,
      assignPeople: vals.assignPeopleIds,
      assignShiftLead: vals.assignShiftLead,
      assignStoreAreaId: vals.assignStoreAreaId || undefined,
      assignScheduleAreaTitle: vals.assignScheduleAreaTitle || undefined,
      assignStorePositionId: vals.assignStorePositionId || undefined,
    }, {
      onSuccess: async () => {
        apiUtil.checklist2.invalidate();
        toast.success("Checklist assignment saved", {
          position: "top-center"
        });
        onSave();
      },
      onError: (error) => {
        toast.error(`Error: ${error.message}`, {
          position: "top-center"
        });
      }
    });
  };

  const formIsDirty = form.useStore((state) => state.isDirty);
  useEffect(() => {
    onFormDirty?.(formIsDirty);
  }, [formIsDirty, onFormDirty]);

  if (!store) return null;

  return (
    <div>
      <div>
        <Text size="lg" className="font-semibold mb-2">
          Assign
        </Text>
        <Text size="sm" className="text-gray-500 mb-3">
          Assign Team Members directly or assign to shift leaders, store areas, positions. Team members linked to these
          designations will receive the checklist.
        </Text>
        <Checklist2AssignFormFields form={form} store={store} />
      </div>

      <Checklist2EventActionButtons
        form={form}
        formIsDirty={formIsDirty}
        isSavePending={saveAssign.isPending}
      />

      <AlertDialog open={isRecurrenceDialogOpen} onOpenChange={setIsRecurrenceDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Edit recurring checklist</AlertDialogTitle>
            <AlertDialogDescription>
              Which checklists do you want to apply your changes to?
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={() => doUpdate("this", formValues)}>
              This checklist
            </AlertDialogAction>
            <AlertDialogAction onClick={() => doUpdate("thisAndFuture", formValues)}>
              This and future checklists
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
};
