import React, {useState} from 'react';
import {Text} from '@/src/components/Text';
import {Button} from '@/src/components/ui/button';
import {DateTime} from 'luxon';
import {Label} from '@/src/components/ui/label';
import {FieldInfo} from '@/src/components/form/FieldInfo';
import {isEqual} from 'lodash';
import {ReactFormExtendedApi, Validator} from '@tanstack/react-form';
import {ZodType} from 'zod';
import {PlusIcon, XIcon} from 'lucide-react';
import {AnimatePresence, motion} from 'framer-motion';
import {RecurrencePickerDialog} from '@/src/components/RecurrencePickerDialog';
import {getRecurrenceString} from "../../../../api/src/calendar/formRecurrence.ts";
import {cn} from "@/src/util.ts";
import {format} from 'date-fns';
import {FormDateTimePicker} from "@/src/components/form/FormDateTimePicker.tsx";
import {RecurrenceSchedule} from '@gioa/api/src/calendar/recurrence.types.ts';

export function formatDateNoTime(date: Date): string {
  return format(date, "yyyy-MM-dd");
}

export interface ChecklistTimingSettingsFormValues {
  isExplicitTiming: boolean;
  start: Date | undefined;
  end: Date | undefined;
  recurrence: RecurrenceSchedule | null;
}

export interface Checklist2TimingSettingsFormFieldsProps {
  form: ReactFormExtendedApi<ChecklistTimingSettingsFormValues, Validator<unknown, ZodType>>;
  timezone: string;
  defaultStart: Date | undefined;
  defaultEnd: Date | undefined;
  canUseRecurringTiming: boolean;
}

function formatDuration(minutes: number): string {
  if (minutes < 0) return "Invalid duration";
  if (minutes === 0) return "0 mins";

  const days = Math.floor(minutes / (24 * 60));
  const hours = Math.floor((minutes % (24 * 60)) / 60);
  const remainingMinutes = minutes % 60;

  const parts: string[] = [];

  if (days > 0) {
    parts.push(`${days} ${days === 1 ? 'day' : 'days'}`);
  }
  if (hours > 0) {
    parts.push(`${hours} ${hours === 1 ? 'hr' : 'hrs'}`);
  }
  if (remainingMinutes > 0) {
    parts.push(`${remainingMinutes} ${remainingMinutes === 1 ? 'min' : 'mins'}`);
  }

  return parts.join(", ");
}

export const Checklist2TimingSettingsFormFields: React.FC<Checklist2TimingSettingsFormFieldsProps> = ({
                                                                                                        form,
                                                                                                        canUseRecurringTiming,
                                                                                                        defaultStart,
                                                                                                        defaultEnd,
                                                                                                        timezone
                                                                                                      }) => {
  const isAllDay = false; // TODO support all day in future
  const formatDateInTimezone = (date: Date) => {
    const dt = DateTime.fromJSDate(date, {zone: timezone});
    return dt.toLocaleString(isAllDay
      ? DateTime.DATE_FULL
      : {
        weekday: "short",
        month: "short",
        day: "numeric",
        year: dt.hasSame(DateTime.now(), "year") ? undefined : "numeric",
        hour: "numeric",
        minute: "numeric",
        timeZoneName: "short"
      });
  }

  const startDate = form.state.values.start;
  const endDate = form.state.values.end;
  const startDt = startDate ? DateTime.fromJSDate(startDate, {zone: timezone}) : undefined;
  const endDt = endDate ? DateTime.fromJSDate(endDate, {zone: timezone}) : undefined;

  const durationMins = endDt && startDt ? endDt.diff(startDt, 'minutes').as("minutes") : null;
  const hasExplicitTiming = form.useStore((state) => state.values.isExplicitTiming);

  const [isRecurrenceDialogOpen, setIsRecurrenceDialogOpen] = useState(false);

  return (
    <AnimatePresence>
      <form.Field name="isExplicitTiming"
                  children={field => (
                    <div>
                      <div className="flex items-center gap-2">
                        <h2 className="flex-1 text-xl font-semibold mb-1">
                          Timing
                        </h2>
                        {field.state.value && (
                          <Button variant={"ghost"} size={"icon"} className="text-gray-500"
                                  type="button" title="Remove Timing Settings"
                                  onClick={() => field.handleChange(false)}>
                            <XIcon size={24}/>
                          </Button>
                        )}
                      </div>

                      <div className="text-sm text-gray-500 mb-4">
                        Input timing settings for this
                        checklist. {canUseRecurringTiming ? "You can set it as a one-time or recurring checklist." : null}
                      </div>

                      {!field.state.value && (
                        <Button
                          variant="outline"
                          size="sm"
                          className="text-gray-500"
                          onClick={() => {
                            const now = DateTime.now().setZone(timezone);
                            const start = now.set({minute: 0, second: 0, millisecond: 0}).plus({hours: 1});
                            const end = start.plus({hours: 1});

                            form.setFieldValue("start", start.toJSDate());
                            form.setFieldValue("end", end.toJSDate());

                            field.handleChange(true);
                            form.update()
                          }}
                        >
                          <PlusIcon size={16} className="mr-2"/>
                          Add Timing Settings
                        </Button>
                      )}
                    </div>
                  )}/>

      {hasExplicitTiming && (
        <motion.div
          className="flex-1"
          key="explicitTiming"
          initial={{opacity: 0, scale: 0.9}}
          animate={{opacity: 1, scale: 1}}
          exit={{opacity: 0, scale: 0.1}}
          transition={{duration: 0.15}}
        >
          <form.Field name="start"
                      listeners={{
                        onChange: ({value}) => {
                          // move the end up proportionally
                          if (durationMins && Number.isInteger(durationMins) && value) {
                            const newEndDate = DateTime.fromJSDate(value).plus({minutes: durationMins}).toJSDate();
                            form.setFieldValue("end", newEndDate);
                          }
                        },
                      }}
                      children={(field) => (
                        <div className="mb-4">
                          <Label>Start Time</Label>
                          <FormDateTimePicker
                            field={field}
                            showTimeSelect={!isAllDay}
                            dateFormat={formatDateInTimezone}
                            timezone={timezone}
                            minutesStep={5}
                            placeholder="00:00 AM"
                          />
                          <FieldInfo field={field}/>
                        </div>
                      )}/>

          <form.Field name="end"
                      validators={{
                        onSubmit: ({value, fieldApi}) => {
                          if (!value) return "Required";
                          const {start} = fieldApi.form.state.values;
                          if (isAllDay) {
                            if (start && formatDateNoTime(start) > formatDateNoTime(value)) {
                              return "End Time must be after Start Time."
                            }
                          } else {
                            if (start && start >= value) {
                              return "End Time must be after Start Time."
                            }
                          }
                          return undefined;
                        }
                      }}
                      children={(field) => (
                        <div className="mb-4">
                          <Label>End Time</Label>
                          <FormDateTimePicker
                            field={field}
                            showTimeSelect={!isAllDay}
                            dateFormat={formatDateInTimezone}
                            timezone={timezone}
                            minutesStep={5}
                            placeholder="00:00 PM"
                          />
                          <Text className="text-sm text-gray-500 mt-1">
                            Duration: {durationMins ? formatDuration(Math.floor(durationMins)) : null}
                          </Text>
                          <FieldInfo field={field}/>
                        </div>
                      )}/>

          {canUseRecurringTiming && (
            <form.Field name="recurrence"
                        children={field => {
                          const repeatStr = field.state.value && startDt ? getRecurrenceString(field.state.value, startDt) : "";
                          return (
                            <div className="mb-4">
                              <button
                                type="button"
                                disabled={!startDt}
                                className={cn(
                                  "flex items-center justify-between w-full px-3 py-2 text-left border rounded-md",
                                  !startDt ? "bg-gray-100 text-gray-400 cursor-not-allowed" : "hover:bg-gray-50"
                                )}
                                onClick={() => {
                                  if (!startDt) return;
                                  setIsRecurrenceDialogOpen(true);
                                }}
                              >
                      <span className="flex-1">
                        {repeatStr ? (
                          <>
                            <span className="text-gray-500">Repeat: </span>
                            <span>{repeatStr}</span>
                          </>
                        ) : (
                          <span>Does not repeat</span>
                        )}
                      </span>
                                <span className="text-gray-500">›</span>
                              </button>

                              <RecurrencePickerDialog
                                isOpen={isRecurrenceDialogOpen}
                                onClose={() => setIsRecurrenceDialogOpen(false)}
                                startDate={field.form.getFieldValue("start")}
                                schedule={field.state.value}
                                timezone={timezone}
                                onSelect={(schedule) => {
                                  if (schedule) {
                                    if (!isEqual(schedule, field.state.value)) {
                                      field.handleChange(schedule);
                                    }
                                  } else {
                                    if (field.state.value) {
                                      field.handleChange(null);
                                    }
                                  }
                                  setIsRecurrenceDialogOpen(false);
                                }}
                              />
                            </div>
                          );
                        }}/>
          )}
        </motion.div>
      )}
    </AnimatePresence>
  );
}
