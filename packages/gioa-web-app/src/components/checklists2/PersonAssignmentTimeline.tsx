import React from 'react';
import {compact, find, map, orderBy} from 'lodash';
import {Text} from '@/src/components/Text';
import {DateTimeRange, DtRange} from '@gioa/api/src/timeSchemas';
import {DateTime} from 'luxon';
import {PersonDto} from '@gioa/api/src/schemas';
import {cn} from "@/src/util.ts";
import {RangeAssignmentDto} from "../../../../api/src/checklists/checklist/checklistDto.ts";
import {findGapsBetweenDtRanges} from "../../../../api/src/date.util.ts";
import {useNow} from "@/src/hooks/useToday.tsx";
import {DurationDaysText} from "@/src/components/DurationDaysText.tsx";
import {TeamMemberPressable} from "@/src/components/TeamMemberPressable.tsx";

export interface PersonAssignmentTimelineProps {
  assignments: RangeAssignmentDto[];
  container: DateTimeRange;
  timezone: string;
  currentPersonId: string;
  storeId: string;
  showCurrentTime: boolean;
  showUnassignedAtEnd: boolean;
  people: PersonDto[];
}

interface AssignmentSegment extends DtRange {
  type: "assignment";
  person: PersonDto;
  shiftId: string | null;
}

interface GapSegment extends DtRange {
  type: "gap";
}

interface CurrentSegment extends DtRange {
  type: "current";
}

interface StartSegment extends DtRange {
  type: "0start";
}

interface EndSegment extends DtRange {
  type: "zend";
}

type TimelineSegment = AssignmentSegment | GapSegment | CurrentSegment | StartSegment | EndSegment;

export const PersonAssignmentTimeline: React.FC<PersonAssignmentTimelineProps> = (props) => {

  const assignments = compact(map(props.assignments, (a): TimelineSegment | null => {
    const person = find(props.people, p => p.id === a.personId);
    if (!person) return null;

    return {
      type: "assignment",
      start: DateTime.fromJSDate(a.range.start, {zone: props.timezone}),
      end: DateTime.fromJSDate(a.range.end, {zone: props.timezone}),
      person: person,
      shiftId: a.shiftId
    }
  }));

  const container = {
    start: DateTime.fromJSDate(props.container.start, {zone: props.timezone}),
    end: DateTime.fromJSDate(props.container.end, {zone: props.timezone}),
  }

  const [now] = useNow(props.timezone);
  const gaps = map(findGapsBetweenDtRanges(container, assignments), (gap, idx): TimelineSegment => {
    return ({
      type: "gap",
      ...gap
    });
  });

  const segments = orderBy(assignments
      .concat(gaps),
    // commenting out for now as it feels a little weird to have the start, current, and end segments
    // .concat([currentTimeSegment, startSegment, endSegment]),
    [r => r.start, r => r.type], ["asc", "asc"]);
  const containerDuration = container.end.diff(container.start);
  const startsToday = DateTime.fromJSDate(props.container.start).hasSame(now, "day");

  const dateTimeFormat = containerDuration.as("days") > 7 || !startsToday
    ? {
      hour: "numeric",
      minute: "numeric",
      weekday: "short",
      day: "numeric",
    } as const
    : DateTime.TIME_SIMPLE

  return <div className={cn("flex flex-col gap-2")}>
    {map(segments, (segment, idx) => {
      const isLastSegment = idx === segments.length - 1;
      const key = segment.start + "_" + segment.end + "_" + ("person" in segment ? segment.person.id : "");
      if (segment.type === "assignment") {
        const subtext = <div className={"text-sm"}>
          {segment.start.toLocaleString(dateTimeFormat)}
          {" - "}
          {segment.end.toLocaleString(dateTimeFormat)}
          {" "}
          (<DurationDaysText durationHours={segment.end.diff(segment.start).as("hours")}/>)
        </div>;

        return <TeamMemberPressable key={key}
                                    className={"mb-0"}
                                    Subtext={subtext}
                                    RightElem={null}
                                    person={segment.person}
        />
      } else if (segment.type === "current") {
        const dateFormat = DateTime.DATETIME_SHORT;

        return props.showCurrentTime ? <div key={key}
                                            className={cn("border bg-blue-100 px-4 py-2 border-blue-300 rounded-lg")}>
          <Text size={"sm"}>
            {segment.start.toLocaleString(dateFormat)}
            {" - "}
            Current Time
          </Text>
        </div> : null;
      } else if (segment.type === "0start") {
        const dateFormat = DateTime.DATETIME_SHORT;

        return <div key={key}
                    className={cn("border bg-green-100 px-4 py-2 border-green-300 rounded-lg")}>
          <Text size={"sm"}>
            {segment.start.toLocaleString(dateFormat)}
            {" - "}
            Checklist Starts
          </Text>
        </div>
      } else if (segment.type === "zend") {
        const dateFormat = DateTime.DATETIME_SHORT;

        return <div key={key}
                    className={cn("border bg-red-100 px-4 py-2 border-red-300 rounded-lg")}>
          <Text size={"sm"}>
            {segment.start.toLocaleString(dateFormat)}
            {" - "}
            Checklist Ends
          </Text>
        </div>
      } else if (segment.type === "gap") {
        if (isLastSegment && !props.showUnassignedAtEnd) {
          return null;
        }

        return <div key={key}
                    className={cn("border bg-gray-200 px-4 py-2 border-gray-300 rounded-lg")}>
          <Text>
            Unassigned
          </Text>

          <Text size={"sm"}>
            {segment.start.toLocaleString(dateTimeFormat)}
            {" - "}
            {segment.end.toLocaleString(dateTimeFormat)}
          </Text>
        </div>
      }

    })}
  </div>
}

