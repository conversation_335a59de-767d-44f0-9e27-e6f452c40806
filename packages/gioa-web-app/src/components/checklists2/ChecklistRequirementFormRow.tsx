import React from 'react';
import {Card} from '@/src/components/ui/card';
import {Text} from '@/src/components/Text';
import {Switch} from '@/src/components/ui/switch';
import {CheckIcon, HashIcon, ImageIcon, MessageSquareIcon, XIcon} from 'lucide-react';
import {ItemRequirementDto} from '@gioa/api/src/checklists/checklistTemplate/checklistTemplateDtos';
import {capitalize, map} from 'lodash';

interface ChecklistRequirementFormRowProps {
  requirement: ItemRequirementDto;
  isRequired: boolean;
  onRequiredChange: (isRequired: boolean) => void;
  onPress?: () => void;
  onRemove?: () => void;
}

export const getRequirementDetails = (req: ItemRequirementDto): { label: string; icon: React.ReactNode } => {
  switch (req.type) {
    case 'addImage':
      return {
        icon: <ImageIcon size={20} className="text-blue-500"/>,
        label: 'Add Image'
      };
    case 'writeComment':
      return {
        icon: <MessageSquareIcon size={20} className="text-green-500"/>,
        label: 'Write Comment'
      };
    case 'inputBoolean':
      return {
        icon: <CheckIcon size={20} className="text-purple-500"/>,
        label: 'Yes/no'
      };
    case 'inputNumber':
      return {
        icon: <HashIcon size={20} className="text-orange-500"/>,
        label: 'Number Input'
      };
    default:
      return {
        icon: <CheckIcon size={20} className="text-gray-500"/>,
        label: 'Unknown Requirement'
      };
  }
};

export function ChecklistRequirementFormRow({
                                              requirement,
                                              isRequired,
                                              onRequiredChange,
                                              onPress,
                                              onRemove
                                            }: ChecklistRequirementFormRowProps) {
  const {label} = getRequirementDetails(requirement);

  const isBooleanInput = requirement.type === "inputBoolean";
  const showBooleanInputConditionals = isBooleanInput && requirement.conditionals?.length > 0;

  return (
    <Card className={`mb-3 border border-gray-200`}>
      <div className={`flex items-center ${showBooleanInputConditionals ? 'border-b border-gray-200 mb-3' : ''}`}>
        <label className={"pl-4 py-4 flex"}>
          <Switch
            checked={isRequired}
            onCheckedChange={onRequiredChange}
          />
        </label>

        <button className="grow text-left pr-4 pl-4 py-4"
                onClick={onPress}>
          <Text className="text-gray-700">
            {label} {requirement.type === "inputNumber" && (
            <Text size="sm" className="text-muted-foreground" asChild={"span"}>
              ({capitalize(requirement.numberType)})
            </Text>
          )}
          </Text>
          <Text size="xs" className="text-gray-500">
            {isRequired ? "Required" : "Optional"}
          </Text>
        </button>

        {onRemove && (
          <button
            onClick={(e) => {
              e.stopPropagation();
              onRemove();
            }}
            className="text-gray-400 hover:text-gray-600 py-4 pr-4 rounded-full"
            type="button"
          >
            <XIcon size={18}/>
          </button>
        )}
      </div>

      {showBooleanInputConditionals && (
        <div>
          {map(requirement.conditionals, (cond, idx) => (
            <div key={idx} className="mb-3 px-4">
              <Text size="sm" className="text-gray-700 mb-2">
                <Text semibold size="sm" className="text-gray-700" asChild={"span"}>
                  If {cond.if ? "Yes" : "No"} → {' '}
                </Text>
                {cond.thenText}
              </Text>

              {map(cond.thenRequirements, (req) => {
                const {label} = getRequirementDetails(req);
                return (
                  <div
                    key={req.id}
                    className="ml-14 border border-gray-200 rounded-lg py-2 px-3 mb-2 flex flex-row gap-3 justify-between items-center"
                  >
                    <Text size="sm" className="text-gray-700">
                      {label}
                    </Text>
                    <Text size="xs" className="text-gray-700">
                      {req.isRequired ? "Required" : "Optional"}
                    </Text>
                  </div>
                );
              })}
            </div>
          ))}
        </div>
      )}
    </Card>
  );
}
