import React from 'react';
import {find, isEmpty, map, sortBy} from 'lodash';
import {Text} from '@/src/components/Text';
import {DateTimeRange} from '@gioa/api/src/timeSchemas';
import {DateTime} from 'luxon';
import {ChecklistInstanceDto} from '@gioa/api/src/checklists/checklist/checklistDto';

import {PersonDto} from '@gioa/api/src/schemas';
import {PersonAssignmentTimeline} from '@/src/components/checklists2/PersonAssignmentTimeline';
import {PersonId} from '@gioa/api/src/peopleSelection/peopleSelection';
import {cn} from "@/src/util.ts";
import {useChecklist2Info} from "@/src/hooks/useChecklist2Info.ts";
import {PersonAvatar} from "@/src/components/PersonAvatar.tsx";

export interface ChecklistAssignmentFullDisplayProps {
  checklist: ChecklistInstanceDto;
  storeId: string;
  currentPersonId: string;
  timezone: string;
  people: PersonDto[];
}

/**
 * Displays the assignments for a checklist. If the checklist is the in the past and complete, it will display the completed by list.
 * If the checklist is live, it will display the live assignments.
 * If the checklist is in the future, it will display the future assignments.
 */
export const Checklist2AssignmentFullDisplay: React.FC<ChecklistAssignmentFullDisplayProps> = ({
  checklist,
  storeId,
  currentPersonId,
  timezone,
  people
}) => {
  const {
    isChecklistInPast,
    isChecklistArchived,
    isChecklistComplete,
    completedBy,
    assignmentSchedule,
    liveAssignments,
    completedAt,
  } = useChecklist2Info({ timezone, timingStrOverride: undefined, checklist });

  const checklistCompleteSection = isChecklistComplete && !isEmpty(completedBy) ? (
    <div>
      <Text semibold className="mb-3">
        Completed by
      </Text>

      {map(completedBy, personId => {
        const person = find(people, p => p.id === personId);
        if (!person) return null;

        return (
          <div key={personId}
            className="flex items-center gap-2 mb-3 hover:bg-gray-50 p-2 rounded-md"
          >
            <PersonAvatar person={person}/>
            <div>
              <Text>
                {person?.firstName} {person?.lastName}
              </Text>
              {completedAt && (
                <Text size="sm" className="text-gray-500">
                  At {DateTime.fromJSDate(completedAt).toLocaleString(DateTime.DATETIME_FULL)}
                </Text>
              )}
            </div>
          </div>
        );
      })}
    </div>
  ) : null;

  const liveAssignmentsSection = (assignments: PersonId[]) => {
    // sort the current person to the top
    const sortedAssignments = sortBy(assignments, personId => personId === currentPersonId ? 0 : 1);

    if (isChecklistInPast || isChecklistArchived) {
      return null;
    }

    return (
      <div>
        <Text semibold className="mb-2">
          Current assignments
        </Text>

        {isEmpty(sortedAssignments) ? (
          <div className="flex items-center gap-2">
            <Text size="sm" className="text-gray-700">
              Checklist not currently assigned. Checklists are assigned to Team Members currently on shift who match the
              assignment settings. {hasAssignmentSchedule
                ? "See the assignment schedule below."
                : ""}
            </Text>
          </div>
        ) : null}

        {map(sortedAssignments, personId => {
          const person = find(people, p => p.id === personId);
          if (!person) return null;

          return (
            <div key={personId} className="flex items-center gap-2">
              <div
                className="flex-1 flex items-center gap-2 mb-3 p-2 rounded-md"
              >
                <PersonAvatar person={person}/>
                <div>
                  <Text className={cn({ "font-semibold": personId === currentPersonId })}>
                    {person?.firstName} {person?.lastName} {personId === currentPersonId ? "(You)" : ""}
                  </Text>
                  <Text size="sm" className="text-gray-500">
                    {person.jobTitle}
                  </Text>
                </div>
              </div>
            </div>
          );
        })}
      </div>
    );
  };

  const hasAssignmentSchedule = assignmentSchedule && !isEmpty(assignmentSchedule);
  const assignmentScheduleSection = (
    <div>
      <Text semibold className="mb-2">
        Assignment schedule
      </Text>

      {hasAssignmentSchedule && checklist.start && checklist.end ? (
        <PersonAssignmentTimeline
          assignments={assignmentSchedule}
          people={people}
          showUnassignedAtEnd={checklist.isExplicitTiming}
          timezone={timezone}
          showCurrentTime={!isChecklistInPast}
          currentPersonId={currentPersonId}
          storeId={storeId}
          container={checklist as DateTimeRange}
        />
      ) : (
        <Text size="sm" className="text-gray-700">
          Checklist has no scheduled assignments. There are no Team Members scheduled for shifts during this checklist's
          time range that match the assignment settings.
        </Text>
      )}
    </div>
  );

  return (
    <>
      {checklistCompleteSection}
      {liveAssignmentsSection(liveAssignments ?? [])}
      {assignmentScheduleSection}
    </>
  );
};
