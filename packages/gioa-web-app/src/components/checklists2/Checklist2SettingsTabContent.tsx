import React from 'react';
import {Text} from '@/src/components/Text';
import {ReactFormExtendedApi, Validator} from '@tanstack/react-form';
import {Label} from '@/src/components/ui/label';
import {FormInput} from '@/src/components/form/FormInput';
import {FieldInfo} from '@/src/components/form/FieldInfo';
import {z, ZodType} from 'zod';
import {FormTextarea} from '@/src/components/form/FormTextarea';
import {Card} from '@/src/components/ui/card';
import {ChecklistInstanceDto} from '../../../../api/src/checklists/checklist/checklistDto';
import {PersonDto} from '../../../../api/src/schemas';
import {Checklist2TimingSettingsFormFields} from '@/src/components/checklists2/Checklist2TimingSettingsFormFields';
import {
  Checklist2NotificationSettingsFormFields
} from '@/src/components/checklists2/Checklist2NotificationSettingsFormFields';
import {RecurrenceSchedule} from '../../../../api/src/calendar/recurrence.types';
import {FormChecklistTags} from '@/src/components/form/FormChecklistTags';
import {api} from '@/src/api';

export interface ChecklistSettingsFormValues {
  title: string;
  description: string | undefined;
  tags: string[];
  start: Date | undefined;
  end: Date | undefined;
  recurrence: RecurrenceSchedule | null;
  isExplicitTiming: boolean;
  notifyOnIncomplete: boolean;
  notifyOnComplete: boolean;
  notifyGeneral: boolean;
  peopleToNotify: string[];
}

export interface Checklist2SettingsTabContentProps {
  checklist: ChecklistInstanceDto;
  timezone: string;
  storeId: string;
  people: PersonDto[];
  form: ReactFormExtendedApi<ChecklistSettingsFormValues, Validator<unknown, ZodType>>;
}

export const Checklist2SettingsTabContent = React.memo(({
  checklist,
  timezone,
  form,
  people,
  storeId,
}: Checklist2SettingsTabContentProps) => {
  const [{ tags }] = api.checklist2.getChecklistTags.useSuspenseQuery({storeId});

  return (
    <div className="space-y-4">
      <Card className="p-4">
        <Text className="font-bold text-lg mb-2">
          Checklist
        </Text>

        <form.Field
          name="title"
          validators={{
            onSubmit: z.string().min(1, "Required")
          }}
          children={(field) => (
            <div className="mb-4">
              <Label>Title</Label>
              <FormInput
                field={field}
                type="text"
                placeholder="Give this checklist a name..."
              />
              <FieldInfo field={field} />
            </div>
          )}
        />

        <form.Field
          name="description"
          children={(field) => (
            <div className="mb-4">
              <Label>Description</Label>
              <FormTextarea
                field={field}
                placeholder="Add text about this checklist..."
              />
              <FieldInfo field={field} />
            </div>
          )}
        />

        <form.Field
          name="tags"
          children={(field) => (
            <div>
              <Label>Tags</Label>
              <FormChecklistTags field={field} allTags={tags} storeId={storeId} />
              <FieldInfo field={field} />
            </div>
          )}
        />
      </Card>

      <Card className="p-4">
        <Checklist2TimingSettingsFormFields
          form={form as any}
          canUseRecurringTiming={true}
          defaultStart={checklist.start}
          defaultEnd={checklist.end}
          timezone={timezone}
        />
      </Card>

      <Card className="p-4">
        <Checklist2NotificationSettingsFormFields
          form={form as any}
          storeId={storeId}
          people={people}
        />
      </Card>
    </div>
  );
});
