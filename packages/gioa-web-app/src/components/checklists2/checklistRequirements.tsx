import {ChecklistRequirementType} from "../../../../api/src/checklist.dto.ts";

export const requirementOptions: { value: ChecklistRequirementType; label: string; roLabel: string; icon: string; description: string }[] = [
  {
    value: 'addImage', label: 'Add Image', roLabel: "Image",
    icon: 'image', description: "Image upload is required."
  },
  {
    value: 'writeComment', label: 'Write Comment', roLabel: "Comment",
    icon: 'chatbox', description: "Comment is required."
  },
  {
    value: 'inputNumber', label: 'Add Number Input', roLabel: "Number Input",
    icon: 'calculator', description: "Number input is required."
  },
  {
    value: 'inputBoolean', label: 'Provide Yes/No Input', roLabel: "Yes/No Input",
    icon: 'checkbox-outline', description: "Yes/No input is required."
  },
]
