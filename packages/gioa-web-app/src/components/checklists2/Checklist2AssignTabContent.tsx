import React, {Suspense} from 'react';
import {useForm} from '@tanstack/react-form';
import {zodValidator} from '@tanstack/zod-form-adapter';
import {Button} from '@/src/components/ui/button';
import {Checklist2AssignFormFields, ChecklistAssignFormValues} from './Checklist2AssignFormFields';
import {Spinner} from '@/src/components/Spinner';
import {api} from '@/src/api';
import {Text} from '@/src/components/Text';
import {cn} from "@/src/util.ts";

export interface Checklist2AssignTabContentProps {
  storeId: string;
  defaultValues?: Partial<ChecklistAssignFormValues>;
  onSubmit?: (values: ChecklistAssignFormValues) => void;
  onPrev?: (values: ChecklistAssignFormValues) => void;
  isSubmitLoading?: boolean;
  submitLabel?: string;
}

function AssignTabContentInner({
                                 storeId,
                                 defaultValues = {},
                                 onSubmit,
                                 onPrev,
                                 isSubmitLoading = false,
                                 submitLabel = "Next"
                               }: Checklist2AssignTabContentProps) {

  const form = useForm({
    defaultValues: {
      assignPeopleIds: defaultValues.assignPeopleIds || [],
      assignShiftLead: defaultValues.assignShiftLead,
      assignStoreAreaId: defaultValues.assignStoreAreaId,
      assignScheduleAreaTitle: defaultValues.assignScheduleAreaTitle,
      assignStorePositionId: defaultValues.assignStorePositionId,
    },
    validatorAdapter: zodValidator(),
    onSubmit: (event) => {
      if (onSubmit) {
        onSubmit(event.value);
      }
    },
  });

  const [store] = api.user.getStore.useSuspenseQuery({storeId: storeId!});

  return (
    <section>
      <div>
        <h2 className="mb-2 text-xl font-semibold">
          Assign
        </h2>
        <Text className={cn("mb-4 block")} size={"sm"} muted>
          Assign Team Members directly or assign to shift leaders, store areas, positions. Team members linked to these
          designations will receive the checklist.
        </Text>

        <Checklist2AssignFormFields
          form={form}
          store={store}
        />
      </div>

      <hr className="my-4"/>

      <div className="flex justify-between ">
        <Button variant="outline" onClick={() => {
          onPrev?.(form.state.values)
        }}>
          Back
        </Button>
        <Button isLoading={isSubmitLoading}
                onClick={() => form.handleSubmit()}>
          {submitLabel}
        </Button>
      </div>
    </section>
  );
}

export function Checklist2AssignTabContent(props: Checklist2AssignTabContentProps) {
  return (
    <Suspense fallback={<div className="flex justify-center items-center h-64"><Spinner/></div>}>
      <AssignTabContentInner {...props} />
    </Suspense>
  );
}
