import React from 'react';
import {useForm} from '@tanstack/react-form';
import {zodValidator} from '@tanstack/zod-form-adapter';
import {Button} from '@/src/components/ui/button';
import {Checklist2NotificationSettingsFormFields} from './Checklist2NotificationSettingsFormFields';
import {api} from "@/src/api.ts";

export interface ChecklistTemplateNotificationsFormValues {
  notifyOnIncomplete: boolean;
  notifyOnComplete: boolean;
  notifyGeneral: boolean;
  peopleToNotify: string[];
}

export interface ChecklistTemplateNotificationsTabContentProps {
  storeId: string;
  defaultValues?: Partial<ChecklistTemplateNotificationsFormValues>;
  onSubmit?: (values: ChecklistTemplateNotificationsFormValues) => void;
  onPrev?: () => void;
}

export function ChecklistTemplateNotificationsTabContent({
  storeId,
  defaultValues = {},
  onSubmit,
  onPrev,
}: ChecklistTemplateNotificationsTabContentProps) {
  const [store] = api.user.getStore.useSuspenseQuery({storeId: storeId}, {
    staleTime: 1000 * 60 * 60 // 1 hour
  });
  const people = store.employees;

  const form = useForm({
    defaultValues: {
      notifyOnIncomplete: defaultValues.notifyOnIncomplete ?? false,
      notifyOnComplete: defaultValues.notifyOnComplete ?? false,
      notifyGeneral: defaultValues.notifyGeneral ?? false,
      peopleToNotify: defaultValues.peopleToNotify ?? [],
    },
    validatorAdapter: zodValidator(),
    onSubmit: (event) => {
      if (onSubmit) {
        onSubmit(event.value);
      }
    },
  });

  return (
    <div >
      <div >
        <Checklist2NotificationSettingsFormFields
          form={form}  people={people}
          storeId={storeId}
        />
      </div>

      <hr className="my-4"/>

      <div className="flex justify-between ">
        <Button variant="outline" onClick={onPrev}>
          Back
        </Button>
        <Button onClick={() => form.handleSubmit()}>
          Next
        </Button>
      </div>
    </div>
  );
}
