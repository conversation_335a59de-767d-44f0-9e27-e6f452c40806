import React from 'react';
import {<PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON>eader, DialogTitle} from '@/src/components/ui/dialog';
import {Button} from '@/src/components/ui/button';
import {FormControl} from '@/src/components/form/FormControl';
import {Label} from '@/src/components/ui/label';
import {Text} from '@/src/components/Text';
import {PlusIcon} from 'lucide-react';
import {BooleanRequirementDto} from '@gioa/api/src/checklists/checklistTemplate/checklistTemplateDtos';
import {FormTextarea} from '@/src/components/form/FormTextarea';
import {useForm} from '@tanstack/react-form';
import {zodValidator} from '@tanstack/zod-form-adapter';
import {z} from 'zod';
import {RadioGroup, RadioGroupItem} from '@/src/components/ui/radio-group';
import {FieldInfo} from '@/src/components/form/FieldInfo';
import {isEmpty, map} from 'lodash';
import {genChecklistRequirementId} from '@gioa/api/src/checklist.schemas';
import {ChecklistRequirementFormRow} from './ChecklistRequirementFormRow';

interface BooleanInputDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onChange: (value: BooleanRequirementDto) => void;
  initialValue: BooleanRequirementDto;
}

const yesNoOptions = [
  {label: "Yes", value: true},
  {label: "No", value: false}
];

export function BooleanInputDialog({
                                     isOpen,
                                     onClose,
                                     onChange,
                                     initialValue
                                   }: BooleanInputDialogProps) {
  const form = useForm({
    defaultValues: initialValue,
    onSubmit: ({value}) => {
      onChange(value);
      onClose();
    },
    validatorAdapter: zodValidator(),
  });

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent position={"fixedTop"}
                     className="max-w-2xl max-h-[90vh] overflow-auto">
        <DialogHeader>
          <DialogTitle>Provide Yes/No Input</DialogTitle>
        </DialogHeader>

        <form.Field name="conditionals"
                    mode="array"
                    children={field => (
                      <div>
                        {isEmpty(field.state.value) ? (
                          <>
                            <Text className="mb-1">Requirement</Text>
                            <div className="bg-gray-100 px-4 py-2 rounded-lg mb-2">
                              <Text>Yes/No</Text>
                            </div>
                          </>
                        ) : null}

                        {map(field.state.value, (cond, condIdx) =>
                          <div key={condIdx} className="mb-6 border-b border-gray-300 pb-6">
                            <Text className="mb-1">Requirement</Text>
                            <div className="flex flex-row items-center gap-3 mb-3">
                              <div className="bg-gray-100 px-4 py-2 rounded-lg flex-1">
                                <Text>Yes/No</Text>
                              </div>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => field.removeValue(condIdx)}
                              >
                                Remove
                              </Button>
                            </div>

                            <form.Field name={`conditionals[${condIdx}].if`}
                                        validators={{
                                          onSubmit: z.boolean()
                                        }}
                                        children={field => (
                                          <FormControl>
                                            <Label>If</Label>
                                            <RadioGroup
                                              value={field.state.value ? "true" : "false"}
                                              onValueChange={(value) => field.handleChange(value === "true")}
                                              className="mb-1"
                                            >
                                              {yesNoOptions.map(option => (
                                                <div key={String(option.value)} className="flex items-center space-x-2">
                                                  <RadioGroupItem
                                                    value={String(option.value)}
                                                    id={`if-${condIdx}-${String(option.value)}`}
                                                  />
                                                  <Label htmlFor={`if-${condIdx}-${String(option.value)}`}>
                                                    {option.label}
                                                  </Label>
                                                </div>
                                              ))}
                                            </RadioGroup>
                                            <FieldInfo field={field}/>
                                          </FormControl>
                                        )}
                            />

                            <form.Field name={`conditionals[${condIdx}].thenText`}
                                        validators={{
                                          onSubmit: z.string().optional()
                                        }}
                                        children={field => (
                                          <FormControl>
                                            <Label>Then</Label>
                                            <FormTextarea
                                              field={field}
                                              placeholder="Enter text..."
                                            />
                                            <FieldInfo field={field}/>
                                          </FormControl>
                                        )}
                            />

                            <form.Field name={`conditionals[${condIdx}].thenRequirements`}
                                        mode="array"
                                        children={thenReqArrayField => (
                                          <div>
                                            <Label className="mb-1">And require</Label>

                                            <div className="mb-2 mt-2">
                                              {map(thenReqArrayField.state.value, (req, reqIdx) => (
                                                <ChecklistRequirementFormRow
                                                  key={req.id}
                                                  isRequired={req.isRequired}
                                                  onRequiredChange={isRequired => {
                                                    thenReqArrayField.replaceValue(reqIdx, {
                                                      ...req,
                                                      isRequired
                                                    });
                                                  }}
                                                  requirement={req}
                                                  onRemove={() => {
                                                    thenReqArrayField.removeValue(reqIdx);
                                                  }}
                                                />
                                              ))}
                                            </div>

                                            <div className="flex flex-row gap-2">
                                              <Button
                                                variant="outline"
                                                size="sm"
                                                onClick={() => thenReqArrayField.pushValue({
                                                  id: genChecklistRequirementId(),
                                                  type: "addImage",
                                                  isRequired: true
                                                })}
                                              >
                                                <PlusIcon size={16} className="mr-1"/>
                                                Image
                                              </Button>
                                              <Button
                                                variant="outline"
                                                size="sm"
                                                onClick={() => thenReqArrayField.pushValue({
                                                  id: genChecklistRequirementId(),
                                                  type: "writeComment",
                                                  isRequired: true
                                                })}
                                              >
                                                <PlusIcon size={16} className="mr-1"/>
                                                Comment
                                              </Button>
                                              <Button
                                                variant="outline"
                                                size="sm"
                                                onClick={() => thenReqArrayField.pushValue({
                                                  id: genChecklistRequirementId(),
                                                  type: "inputNumber",
                                                  isRequired: true,
                                                  numberType: "number"
                                                })}
                                              >
                                                <PlusIcon size={16} className="mr-1"/>
                                                Number Input
                                              </Button>
                                            </div>
                                          </div>
                                        )}
                            />
                          </div>
                        )}

                        <Button
                          variant="outline"
                          size="sm"
                          className="mb-4"
                          onClick={() => {
                            field.pushValue({
                              if: true,
                              isNonCompliant: false,
                              thenText: "",
                              thenRequirements: []
                            });
                          }}
                        >
                          <PlusIcon size={16} className="mr-1"/>
                          Add Conditional
                        </Button>
                      </div>
                    )}
        />

        <DialogFooter className="px-2 pt-3 border-t border-gray-300">
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button onClick={form.handleSubmit}>
            Save
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
