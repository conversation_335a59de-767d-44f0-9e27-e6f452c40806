import React from 'react';
import {useForm} from '@tanstack/react-form';
import {zodValidator} from '@tanstack/zod-form-adapter';
import {Button} from '@/src/components/ui/button';
import {ChecklistTemplateDto} from '@gioa/api/src/checklists/checklistTemplate/checklistTemplateDtos';
import {Checklist2TimingSettingsFormFields} from './Checklist2TimingSettingsFormFields';
import {RecurrenceSchedule} from '@gioa/api/src/calendar/recurrence.types';

export interface ChecklistTemplateTimingTabContentFormValues {
  start: Date | undefined;
  end: Date | undefined;
  recurrence: RecurrenceSchedule | null;
  isExplicitTiming: boolean;
}

export interface ChecklistTemplateTimingTabContentProps {
  checklist: ChecklistTemplateDto;
  timezone: string;
  defaultValues?: Partial<ChecklistTemplateTimingTabContentFormValues>;
  onSubmit?: (values: ChecklistTemplateTimingTabContentFormValues) => void;
  onBack?: () => void;
  canUseRecurringTiming: boolean;
}

export function ChecklistTemplateTimingTabContent({
                                                    checklist,
                                                    onBack,
                                                    timezone,
                                                    defaultValues = {},
                                                    onSubmit,
                                                    canUseRecurringTiming,
                                                  }: ChecklistTemplateTimingTabContentProps) {
  const form = useForm({
    defaultValues: {
      start: defaultValues.start,
      end: defaultValues.end,
      recurrence: defaultValues.recurrence ?? null,
      isExplicitTiming: defaultValues.isExplicitTiming ?? false,
    },
    validatorAdapter: zodValidator(),
    onSubmit: (event) => {
      if (onSubmit) {
        onSubmit(event.value);
      }
    },
  });

  return (
    <div>
      <div>
        <Checklist2TimingSettingsFormFields
          form={form as any}
          canUseRecurringTiming={canUseRecurringTiming}
          defaultStart={undefined}
          defaultEnd={undefined}
          timezone={timezone}
        />
      </div>

      <hr className="my-4"/>

      <div className="flex justify-between ">
        <Button variant="outline" onClick={onBack}>
          Back
        </Button>
        <Button onClick={() => form.handleSubmit()}>
          Next
        </Button>
      </div>
    </div>
  );
}
