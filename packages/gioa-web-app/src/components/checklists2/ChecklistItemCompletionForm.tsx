import React from 'react';
import {api} from '@/src/api';
import {Button} from '@/src/components/ui/button';
import {Text} from '@/src/components/Text';
import {compact, find, flatMap, groupBy, isEmpty, map, mapValues, reduce, trim} from 'lodash';
import {useChecklistItemCompletion} from '@/src/hooks/useChecklistItemCompletion';
import {CheckIcon} from 'lucide-react';
import {useFormAttachmentUpload} from '@/src/hooks/useFormAttachmentUpload';
import {AttachmentMediaType, FileAttachmentDto} from '@gioa/api/src/fileAttachment.dto';
import {isCompletion, isItemComplete} from '@/src/utils/checklistInteractionUtil';
import {RequirementCompletionDto, RequirementCompletionsDto} from '@gioa/api/src/checklists/checklist/checklistDto';
import {getHumanReadableErrorMessage} from '@/src/components/ErrorAlert';
import {DateTime} from 'luxon';
import {FormChecklistRequirementInput} from './FormChecklistRequirementInput';
import {ItemRequirementDto} from '@gioa/api/src/checklists/checklistTemplate/checklistTemplateDtos';
import {assertUnreachable} from '@gioa/api/src/util';
import {useForm} from '@tanstack/react-form';
import {zodValidator} from '@tanstack/zod-form-adapter';
import {getAttachmentIconAndLabel} from "@/src/utils/checklist.util.tsx";
import {cn} from "@/src/util.ts";
import {imageUrl} from "@/src/images.ts";
import {PersonAvatar} from "@/src/components/PersonAvatar.tsx";

export interface ChecklistItemCompletionFormProps {
  storeId: string;
  checklistId: string;
  recurrenceId: Date | undefined;
  itemId: string;
  onCompleted?: () => void;
  onUncompleted?: () => void;
  onCompletionFailed?: () => void;
  onUncompletionFailed?: () => void;
  onCompletedOptimistic?: () => void;
  onUncompletedOptimistic?: () => void;
}

interface FormRequirement {
  type: string;
  requirementId: string;
  attachment?: File | FileAttachmentDto | null;
  text?: string;
  numberInput?: string;
  booleanInput?: boolean;
  // for boolean inputs, yes or no branches may have requirements
  thenRequirements?: {
    [reqId: string]: FormRequirement
  };
}

function toFormRequirement(req: ItemRequirementDto, completions: RequirementCompletionsDto | undefined): FormRequirement[] {
  const getCompletionForRequirement = (reqId: string) => {
    if (!completions) return undefined;
    return completions[reqId];
  }
  const completion = getCompletionForRequirement(req.id);

  switch (req.type) {
    case "addImage":
      return [{
        type: req.type,
        requirementId: req.id,
        attachment: completion?.type === "addImage" ? completion.attachment : undefined
      }]
    case "writeComment":
      return [{
        type: req.type,
        requirementId: req.id,
        text: completion?.type === "writeComment" ? completion.text : undefined
      }]
    case "inputNumber":
      return [{
        type: req.type,
        requirementId: req.id,
        numberInput: completion?.type === "inputNumber" ? completion.numberInput?.toString() : undefined
      }]
    case "inputBoolean":
      const flattenedConditionalReqs = flatMap(req.conditionals, cond => {
        return flatMap(cond.thenRequirements, req => toFormRequirement(req, completions))
      });
      return [{
        type: req.type,
        requirementId: req.id,
        booleanInput: completion?.type === "inputBoolean" ? completion.booleanInput : undefined,
      },
        ...flattenedConditionalReqs
      ]
    default:
      assertUnreachable(req)
  }
}

async function fromFormRequirement(req: FormRequirement, uploadAttachment: (value: File | FileAttachmentDto) => Promise<FileAttachmentDto | undefined>): Promise<RequirementCompletionDto | undefined> {
  switch (req.type) {
    case "addImage": {
      const attachment = req.attachment;
      if (!attachment) return;

      // if attachment is FileAttachmentDto, then nothing has changed
      if ("id" in attachment) {
        return {
          type: req.type,
          requirementId: req.requirementId,
          attachment: attachment
        };
      }

      // If attachment is a File or other object, convert it to FileAttachmentDto
      const fileAttachmentDto = await uploadAttachment(attachment);
      if (!fileAttachmentDto) throw new Error("Failed to upload attachment");

      return {
        type: req.type,
        requirementId: req.requirementId,
        attachment: fileAttachmentDto
      }
    }
    case "writeComment":
      if (req.text === undefined) return;

      return {
        type: req.type,
        requirementId: req.requirementId,
        text: req.text
      }
    case "inputNumber":
      const parsedNumberInput = req.numberInput !== undefined ? parseFloat(trim(req.numberInput, " $%")) : undefined;
      if (parsedNumberInput === undefined) return;

      return {
        type: req.type,
        requirementId: req.requirementId,
        numberInput: parsedNumberInput
      }
    case "inputBoolean":
      if (req.booleanInput === undefined) return;

      return {
        type: req.type,
        requirementId: req.requirementId,
        booleanInput: req.booleanInput,
      }
    default:
      throw new Error("Unknown requirement type");
  }
}

export const ChecklistItemCompletionForm: React.FC<ChecklistItemCompletionFormProps> = ({
                                                                                          storeId,
                                                                                          checklistId,
                                                                                          recurrenceId,
                                                                                          itemId,
                                                                                          onCompleted,
                                                                                          onUncompleted,
                                                                                          onCompletionFailed,
                                                                                          onUncompletionFailed,
                                                                                          onCompletedOptimistic,
                                                                                          onUncompletedOptimistic
                                                                                        }) => {
  const [{checklist, timezone}] = api.checklist2.getChecklist2.useSuspenseQuery({
    storeId: storeId,
    checklistId: checklistId,
    recurrenceId
  });
  const [store] = api.user.getStore.useSuspenseQuery({storeId}, {staleTime: 1000 * 60 * 60});
  const people = store.employees;

  const {completeChecklistItem, isPending} = useChecklistItemCompletion({
    onCompleted,
    onCompletionFailed,
    onUncompleted,
    onUncompletionFailed
  });
  const item = find(checklist.items, i => i.id === itemId)!;
  const getPresignedPost = api.checklist2.getPresignedPostForChecklistItem.useMutation();
  const {toDto: uploadAttachment, status: uploadAttachmentStatus} = useFormAttachmentUpload(({
                                                                                               contentType,
                                                                                               mediaType
                                                                                             }) => {
    return getPresignedPost.mutateAsync({
      storeId: storeId,
      contentType: contentType,
      mediaType: mediaType as AttachmentMediaType
    });
  });

  const completion = item.latestInteraction && isCompletion(item.latestInteraction) ? item.latestInteraction : undefined;
  const completedBy = completion ? find(people, p => p.id === completion.personId) : undefined;
  const formRequirements = reduce(item.requirements, (acc, req) => {
    const formReqs = toFormRequirement(req, completion?.requirementCompletions);
    return {
      ...acc,
      ...mapValues(groupBy(formReqs, "requirementId"), reqs => reqs[0])
    }
  }, {} as { [requirementId: string]: FormRequirement });

  const form = useForm({
    defaultValues: {
      requirements: formRequirements
    },
    validatorAdapter: zodValidator(),
    onSubmit: async (event) => {
      const save = async () => {
        // upload the attachments for any requirements that have attachments
        let reqsWithAttachments: (RequirementCompletionDto | undefined)[] = [];
        try {
          reqsWithAttachments = await Promise.all(map(event.value.requirements, async (req, reqId) => {
            if (!req) {
              return undefined;
            }

            return fromFormRequirement(req, uploadAttachment)
          }));
        } catch (e) {
          alert("Error completing item: " + getHumanReadableErrorMessage(e));
          return;
        }

        const requirements = reduce(compact(reqsWithAttachments), (acc, req) => {
          return {
            ...acc,
            [req.requirementId]: req
          }
        }, {} as RequirementCompletionsDto);

        completeChecklistItem({
          checklistId: checklistId,
          recurrenceId: recurrenceId,
          item: item,
          requirements: requirements,
          checklistVersion: checklist.version
        })
      }

      save()
    }
  })

  const renderAttachment = (attachment: FileAttachmentDto) => {
    const {icon, label} = getAttachmentIconAndLabel(attachment);
    return <div className={cn("flex flex-row items-center gap-3")} key={attachment.id}>
      {attachment.url && attachment.mediaType === "image" ?
        <a href={imageUrl(attachment.url, {width: 1280})}
           target={"_blank"}
           rel="noopener noreferrer">
          {icon}
        </a>
        :
        <div>
          {icon}
        </div>
      }
      {attachment.url && attachment.mediaType !== "image" ?
        <a href={attachment.url}
           target={"_blank"}
           rel="noopener noreferrer"
           className={cn("flex-1")}>
          {attachment.filename}
        </a> : null}
    </div>;
  };

  const isItemCompleted = isItemComplete(item);

  return <form onSubmit={(e) => {
    e.preventDefault()
    e.stopPropagation()
    form.handleSubmit()
  }}>
    {!isEmpty(item.attachments) ?
      <div className={cn("bg-white my-4")}>
        <div className={cn("mt-4")}>
          {map(item.attachments, attachment => {
            return renderAttachment(attachment);
          })}
        </div>
      </div> : null}

    {map(item.requirements, req => {
      // @ts-ignore "Type instantiation is excessively deep and possibly infinite."
      return <FormChecklistRequirementInput key={req.id} requirement={req}
                                            form={form} isItemCompleted={isItemCompleted}/>
    })}

    {!isEmpty(item.instructions) ?
      <div className={cn("bg-white my-4")}>
        <Text bold className={cn("mb-1")}>
          Instructions
        </Text>
        {map(item.instructions, instruction => {
          return <div key={instruction.id} className={cn("mb-3")}>
            {instruction.text ? <Text>{instruction.text}</Text> : null}
            {instruction.attachment?.url ? renderAttachment(instruction.attachment) : null}
          </div>
        })}
      </div> : null}
    {item.canComplete ?
      <div>
        <Button type={"submit"}
                isLoading={isPending || getPresignedPost.isPending || uploadAttachmentStatus.isLoading}
                leftIcon={<CheckIcon size={24} color={"white"}/>}>
          Mark Complete
        </Button>
      </div> : null}

    {isItemCompleted && completion ?
      <div className={cn("bg-white py-4")}>
        <Text bold className={cn("mb-1")}>
          Completed by
        </Text>
        <div className={cn("flex flex-row items-center gap-3")}>
          <PersonAvatar person={completedBy}/>
          <div>
            <Text>
              {completedBy?.firstName} {completedBy?.lastName}
            </Text>
            <Text muted size={"sm"}>
              On {completion.interactedAt ? DateTime.fromJSDate(completion.interactedAt).toLocaleString(DateTime.DATETIME_FULL) : null}
            </Text>
          </div>
        </div>
      </div> : null}
  </form>
};
