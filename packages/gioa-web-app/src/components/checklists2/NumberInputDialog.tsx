import React, {useEffect, useState} from 'react';
import {<PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>er, <PERSON><PERSON>Header, DialogTitle} from '@/src/components/ui/dialog';
import {Button} from '@/src/components/ui/button';
import {FormControl} from '@/src/components/form/FormControl';
import {Label} from '@/src/components/ui/label';
import {NumberTypeSchema} from '@gioa/api/src/checklists/checklistTemplate/checklistTemplateDtos';
import {RadioGroup, RadioGroupItem} from '@/src/components/ui/radio-group';
import {DialogDescription} from "@radix-ui/react-dialog";

interface NumberInputDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onChange: (value: NumberTypeSchema) => void;
  initialValue: NumberTypeSchema;
}

const numberTypeOptions = [
  {label: "Number", value: "number"},
  {label: "Currency", value: "currency"},
  {label: "Percentage", value: "percentage"},
  {label: "Temperature", value: "temperature"},
];

export function NumberInputDialog({
                                    isOpen,
                                    onClose,
                                    onChange,
                                    initialValue
                                  }: NumberInputDialogProps) {
  const [numberType, setNumberType] = useState<NumberTypeSchema>(initialValue);

  useEffect(() => {
    setNumberType(initialValue);
  }, [initialValue]);

  const onSave = () => {
    onChange(numberType);
    onClose();
  }

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Add Number Input</DialogTitle>
          <DialogDescription>

          </DialogDescription>
        </DialogHeader>

        <FormControl>
          <RadioGroup
            value={numberType}
            onValueChange={v => setNumberType(v as NumberTypeSchema)}
            className="mb-1"
          >
            {numberTypeOptions.map(option => (
              <div key={option.value} className="flex items-center space-x-2 mt-2">
                <RadioGroupItem value={option.value} id={`number-type-${option.value}`}/>
                <Label htmlFor={`number-type-${option.value}`} className="cursor-pointer">
                  {option.label}
                </Label>
              </div>
            ))}
          </RadioGroup>
        </FormControl>

        <DialogFooter>
          <Button variant="outline" type={"button"}
                  onClick={onClose}>
            Cancel
          </Button>
          <Button type={"button"}
                  onClick={onSave}>
            Save
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
