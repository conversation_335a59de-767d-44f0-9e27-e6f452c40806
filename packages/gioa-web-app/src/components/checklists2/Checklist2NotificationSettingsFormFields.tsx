import React from 'react';
import {Text} from '@/src/components/Text';
import {ReactFormExtendedApi, Validator} from '@tanstack/react-form';
import {ZodType} from 'zod';
import {Switch} from '@/src/components/ui/switch';
import {Label} from '@/src/components/ui/label';
import {FormControl} from '@/src/components/form/FormControl';
import {FieldInfo} from '@/src/components/form/FieldInfo';
import {find, includes, map} from "lodash";
import {TeamMemberCombobox} from "@/src/components/TeamMemberCombobox.tsx";
import {PersonDto} from "../../../../api/src/schemas.ts";
import {SelectedTeamMemberRow} from "@/src/components/SelectedTeamMemberRow.tsx";

export interface ChecklistNotificationSettingsFormValues {
  notifyOnIncomplete: boolean;
  notifyOnComplete: boolean;
  notifyGeneral: boolean;
  peopleToNotify: string[];
}

export interface Checklist2NotificationSettingsFormFieldsProps {
  form: ReactFormExtendedApi<ChecklistNotificationSettingsFormValues, Validator<unknown, ZodType>>;
  people: PersonDto[];
  storeId: string;
}

export const Checklist2NotificationSettingsFormFields: React.FC<Checklist2NotificationSettingsFormFieldsProps> = ({
                                                                                                                    form,
                                                                                                                    people,
                                                                                                                    storeId
                                                                                                                  }) => {

  return (
    <>
      <form.Field name="notifyGeneral"
                  children={field => (
                    <FormControl className="flex flex-row items-center justify-between mb-4">
                      <Label htmlFor={field.name} className="cursor-pointer text-xl font-semibold">Notifications</Label>
                      <Switch
                        id={field.name}
                        checked={field.state.value}
                        onCheckedChange={field.handleChange}
                      />
                    </FormControl>
                  )}/>

      <Text size={"sm"} muted className={"mb-3 block"}>
        Enable notifications for this checklist. Assign them to team members or default to the checklist manager if none
        are selected.
      </Text>

      {form.useStore(state => state.values.notifyGeneral) ?
        <>
          <hr className="my-4"/>

          <form.Field name="notifyOnComplete"
                      children={field => (
                        <FormControl className="flex flex-row items-center justify-between mb-4">
                          <div>
                            <Label htmlFor={field.name} className="cursor-pointer">Notify if complete</Label>
                            <Text className="text-sm text-gray-500 block">
                              Send a notification when the checklist is completed.
                            </Text>
                          </div>
                          <Switch
                            id={field.name}
                            checked={field.state.value}
                            onCheckedChange={field.handleChange}
                          />
                        </FormControl>
                      )}/>

          <form.Field name="notifyOnIncomplete"
                      children={field => (
                        <FormControl className="flex flex-row items-center justify-between mb-4">
                          <div>
                            <Label htmlFor={field.name} className="cursor-pointer">Notify if incomplete</Label>
                            <Text className="text-sm text-gray-500 block">
                              Send a notification if the checklist is not completed by its end time.
                            </Text>
                          </div>
                          <Switch
                            id={field.name}
                            checked={field.state.value}
                            onCheckedChange={field.handleChange}
                          />
                        </FormControl>
                      )}/>

          <hr className="my-4"/>


          <form.Field name="peopleToNotify"
                      mode="array"
                      children={(arrField) => (
                        <div className="mb-4">

                          {/* Display selected team members */}
                          <div className="space-y-2 mb-2">
                            {map(arrField.state.value, (personId, idx) => {
                              const person = find(people, p => p.id === personId);
                              if (!person) return null;

                              return <SelectedTeamMemberRow key={personId} person={person}
                                                            onRemove={() => arrField.removeValue(idx)}/>
                            })}
                          </div>

                          {/* Team member selector */}
                          <TeamMemberCombobox
                            people={people}
                            value="" // Always empty to allow new selections
                            className="w-full"
                            onValueChange={(personId) => {
                              if (personId) {
                                // Add the person if not already selected
                                if (!includes(arrField.state.value, personId)) {
                                  arrField.pushValue(personId);
                                }
                              }
                            }}
                          />
                          <FieldInfo field={arrField}/>
                        </div>
                      )}/>
        </> : null}
    </>
  );
}
