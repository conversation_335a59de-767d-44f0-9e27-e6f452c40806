import React, { useCallback, useEffect, useMemo, useState } from "react";
import { DataTable } from "@/src/components/ui/data-table.tsx";
import { Route } from "@/src/routes/_signedin/$businessId/$storeId/_nav/checklists/_nav/recorded.tsx";
import { PersonDto } from "../../../../api/src/schemas.ts";
import {
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  PaginationState,
  SortingState,
  Updater,
  useReactTable,
} from "@tanstack/react-table";
import { api } from "@/src/api";
import { DtRange } from "../../../../api/src/timeSchemas.ts";
import { dtRangeToDateRange } from "../../../../api/src/date.util.ts";
import { useChecklistTableColumns } from "@/src/hooks/useChecklistTableColumns.tsx";
import { every, filter, includes, partition } from "lodash";

export interface PastChecklistsListProps {
  storeId: string;
  range: DtRange;
  timezone: string;
  people: PersonDto[];
  searchFilter?: string;
  tagFilters?: string[];
  onStatsChange?: (stats: { total: number; completed: number; overdue: number }) => void;
  initialSorting?: {
    id: string;
    desc: boolean;
  }[];
  initialPageIndex?: number;
  initialPageSize?: number;
  onSortingChange?: (sorting: SortingState) => void;
  onPaginationChange?: (pagination: { pageIndex: number; pageSize: number }) => void;
  queryType: "getPastChecklists" | "getUpcomingChecklists";
  defaultExpanded: boolean;
}

export const ChecklistList: React.FC<PastChecklistsListProps> = React.memo(
  ({
    storeId,
    range,
    people,
    timezone,
    searchFilter = "",
    tagFilters = [],
    onStatsChange,
    defaultExpanded,
    initialSorting = [],
    initialPageIndex = 0,
    initialPageSize = 50,
    onSortingChange,
    onPaginationChange,
    queryType,
  }) => {
    const [checklists] = api.checklist2[queryType].useSuspenseQuery({
      storeId: storeId,
      range: dtRangeToDateRange(range),
    });
    const columns = useChecklistTableColumns({ people, timezone });

    const filteredData = useMemo(() => {
      let items = checklists?.items;

      if (tagFilters.length > 0) {
        items = filter(items, (item) => every(tagFilters, (tag) => item.tags.includes(tag)));
      }

      if (searchFilter && searchFilter.trim() !== "") {
        const searchLower = searchFilter.toLowerCase();
        items = filter(
          items,
          (item) =>
            includes(item.title.toLowerCase(), searchLower) ||
            (includes(item.description?.toLowerCase(), searchLower)),
        );
      }

      return items;
    }, [checklists?.items, searchFilter, tagFilters]);

    const [sorting, setSorting] = useState<SortingState>(initialSorting);
    const [pagination, setPagination] = useState({
      pageIndex: initialPageIndex,
      pageSize: initialPageSize,
    });

    // Handle sorting changes
    const handleSortingChange = useCallback(
      (sortingState: Updater<SortingState>) => {
        const newSorting = typeof sortingState === "function" ? sortingState(sorting) : sortingState;

        setSorting(newSorting);
        if (onSortingChange) {
          onSortingChange(newSorting);
        }
      },
      [onSortingChange, sorting],
    );

    // Handle pagination changes
    const handlePaginationChange = useCallback(
      (paginationState: Updater<PaginationState>) => {
        const newPagination = typeof paginationState === "function" ? paginationState(pagination) : paginationState;

        setPagination(newPagination);
        if (onPaginationChange) {
          onPaginationChange(newPagination);
        }
      },
      [onPaginationChange, pagination],
    );

    const table = useReactTable({
      data: filteredData,
      columns,
      getCoreRowModel: getCoreRowModel(),
      getPaginationRowModel: getPaginationRowModel(),
      onSortingChange: handleSortingChange,
      getSortedRowModel: getSortedRowModel(),
      getFilteredRowModel: getFilteredRowModel(),
      onPaginationChange: handlePaginationChange,
      state: {
        sorting,
        pagination,
      },
    });

    // Update stats when data changes
    useEffect(() => {
      const total = filteredData.length;
      const [completed, overdue] = partition(filteredData, (item) => item.isComplete);

      onStatsChange?.({
        total,
        completed: completed.length,
        overdue: overdue.length,
      });
    }, [filteredData, onStatsChange]);

    return (
      <div>
        <DataTable
          table={table}
          rowDetailLinkFrom={Route.fullPath}
          getRowDetailTo={(row) =>
            `../${row.original.id}/view?defaultExpanded=${defaultExpanded.toString()}&recurrenceId=${row.original.recurrenceId ? row.original.recurrenceId.toISOString() : ""}`
          }
        />
      </div>
    );
  },
);
