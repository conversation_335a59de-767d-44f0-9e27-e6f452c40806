import React, {useState} from 'react';
import {Button} from '@/src/components/ui/button';
import {map} from 'lodash';
import {api} from '@/src/api';
import {toast} from 'sonner';
import {Checklist2ItemsTabContent, ChecklistItem} from './Checklist2ItemsTabContent';

export interface Checklist2ItemsTabContainerProps {
  onFormDirty?: (isDirty: boolean) => void;
  storeId: string;
  checklist: any; // This would be properly typed in a real implementation
  checklistTemplateId: string | undefined;
  checklistId: string | undefined;
  recurrenceId: Date | undefined;
  children?: React.ReactNode;
  addItemHref: () => void;
  editItemHref: (itemId: string) => void;
}

export const Checklist2ItemsTabContainer: React.FC<Checklist2ItemsTabContainerProps> = ({
  storeId,
  onFormDirty,
  checklist,
  checklistTemplateId,
  checklistId,
  recurrenceId,
  children,
  addItemHref,
  editItemHref
}) => {
  const [isReordering, setIsReordering] = useState(false);
  const updateChecklistTemplateItemsOrder = api.checklist2.updateChecklistTemplateItemsOrder.useMutation();
  const updateChecklistItemsOrder = api.checklist2.updateChecklistItemsOrder.useMutation();
  const [reorderedItems, setReorderedItems] = useState<ChecklistItem[]>([]);

  const apiUtil = api.useUtils();
  const handleSaveOrder = () => {
    if (checklistTemplateId) {
      updateChecklistTemplateItemsOrder.mutate({
        storeId: storeId,
        checklistTemplateId: checklistTemplateId,
        itemOrder: map(reorderedItems, item => item.id)
      }, {
        onSuccess: () => {
          apiUtil.checklist2.invalidate();
          setIsReordering(false);
          onFormDirty?.(false);
          toast.success('Item order saved');
        },
        onError: (error) => {
          toast.error(`Error: ${error.message}`);
        }
      });
    } else if (checklistId) {
      updateChecklistItemsOrder.mutate({
        storeId: storeId,
        checklistId: checklistId,
        recurrenceId: recurrenceId,
        itemOrder: map(reorderedItems, item => item.id)
      }, {
        onSuccess: () => {
          apiUtil.checklist2.invalidate();
          setIsReordering(false);
          onFormDirty?.(false);
          toast.success('Item order saved');
        },
        onError: (error) => {
          toast.error(`Error: ${error.message}`);
        }
      });
    }
  };

  const handleDiscardReorder = () => {
    setIsReordering(false);
    onFormDirty?.(false);
    setReorderedItems([]);
  };

  const handleStartReorder = () => {
    const items: ChecklistItem[] = map(checklist.items, item => ({
      id: item.id,
      title: item.title,
      description: item.description || ''
    }));
    setReorderedItems(items);
    setIsReordering(true);
    onFormDirty?.(true);
  };

  const handleItemDragged = ({ data }: { data: ChecklistItem[], from: number, to: number }) => {
    setReorderedItems(data);
  };

  return (
    <div className="flex-1 flex flex-col max-w-3xl">
      <div className="flex-1">
        <Checklist2ItemsTabContent
          checklist={checklist}
          isReordering={isReordering}
          onStartReordering={handleStartReorder}
          reorderedItems={reorderedItems}
          onItemDragged={handleItemDragged}
          addItemHref={addItemHref}
          editItemHref={editItemHref}
        />
      </div>

      {isReordering ? (
        <div className="flex flex-row pt-3 border-t border-gray-300 bg-white gap-2">
          <Button
            variant="outline"
            disabled={updateChecklistItemsOrder.isPending}
            onClick={handleDiscardReorder}
            className="flex-1"
          >
            Discard Changes
          </Button>
          <Button
            onClick={handleSaveOrder}
            isLoading={updateChecklistItemsOrder.isPending}
            className="flex-1"
          >
            Save Order
          </Button>
        </div>
      ) : children}
    </div>
  );
};
