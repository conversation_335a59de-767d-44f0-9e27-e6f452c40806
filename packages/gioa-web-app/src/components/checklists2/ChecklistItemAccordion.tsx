import React, {useEffect, useState} from 'react';
import {Text} from '@/src/components/Text';
import {isEmpty} from 'lodash';
import {LiveChecklistItemDto} from '@gioa/api/src/checklists/checklist/checklistDto';
import {useChecklistItemCompletion} from '@/src/hooks/useChecklistItemCompletion';
import {DomainEventId} from '@gioa/api/src/eventSourcing/domainEvent';
import {Separator} from '@/src/components/ui/separator';
import {ChecklistItemCompletionForm} from '@/src/components/checklists2/ChecklistItemCompletionForm';
import {Spinner} from '@/src/components/Spinner';
import {CheckCircle, ChevronDown, Circle} from 'lucide-react';
import {toast} from 'sonner';
import {AnimatePresence, motion} from 'framer-motion';
import {cn} from "@/src/util.ts";

export interface ChecklistItemAccordionProps {
  storeId: string;
  checklistId: string;
  recurrenceId: Date | undefined;
  checklistVersion: DomainEventId;
  item: LiveChecklistItemDto;
  isExpanded: boolean;
  onToggleExpand: (itemId: string) => void;
  onCollapse: (itemId: string) => void;
  currentPersonId?: string;
}

export const ChecklistItemAccordion: React.FC<ChecklistItemAccordionProps> = ({
                                                                                storeId,
                                                                                item,
                                                                                onCollapse,
                                                                                checklistId,
                                                                                checklistVersion,
                                                                                recurrenceId,
                                                                                isExpanded,
                                                                                onToggleExpand,
                                                                                currentPersonId
                                                                              }) => {
  const completionCallbacks = {
    onCompleted: () => {
      setIsComplete(true);
      toast.success("Checklist item completed");
      onCollapse(item.id);
    },
    onUncompleted: () => {
      setIsComplete(false);
      toast.success("Checklist item un-completed");
    },
    onCompletionFailed: () => {
      setIsComplete(false);
    },
    onUncompletionFailed: () => {
      setIsComplete(true);
    },
    onCompletedOptimistic: () => {
      setIsComplete(true);
      onCollapse(item.id);
    },
    onUncompletedOptimistic: () => {
      setIsComplete(false);
    }
  };

  const {toggleChecklistItem} = useChecklistItemCompletion(completionCallbacks);

  const onItemPress = () => {
    onToggleExpand(item.id);
  };

  const [isComplete, setIsComplete] = useState<boolean>(item.isComplete);

  useEffect(() => {
    setIsComplete(item.isComplete);
  }, [item.isComplete]);

  const completeIfPossible = () => {
    // If the item has no requirements, we can complete it.
    if (isEmpty(item.requirements) || isComplete) {
      toggleChecklistItem({
        checklistId: checklistId,
        recurrenceId: recurrenceId,
        item: item,
        requirements: {},
        checklistVersion: checklistVersion
      });
    } else {
      // else expand the item
      onToggleExpand(item.id);
    }
  };

  return (
    <div>
      <div className={cn("flex flex-row items-stretch bg-white", {
        "rounded-lg": !isExpanded,
        "rounded-t-lg": isExpanded
      })}>
        <button className="pl-4 pr-4 py-4 flex items-center justify-center"
                onClick={completeIfPossible}>
          {isComplete ? (
            <CheckCircle size={24} className="text-green-500"/>
          ) : (
            <Circle size={24} className="text-gray-500"/>
          )}
        </button>

        <button onClick={onItemPress}
                className="flex flex-row items-center gap-3 flex-1 pr-3 py-2 text-left">
          <div className="flex-1">
            <Text semibold>
              {item.title}
            </Text>
            {item.description && (
              <Text size="sm" muted>
                {item.description}
              </Text>
            )}
          </div>

          <ChevronDown
            size={24}
            className={cn("text-gray-500 transition-transform", {
              "transform rotate-180": isExpanded
            })}
          />
        </button>
      </div>

      <AnimatePresence>
        {isExpanded && (
          <motion.div
            className="bg-white rounded-b-lg px-4 pb-4"
            initial={{opacity: 0}}
            animate={{opacity: 1}}
            transition={{duration: 0.2}}
          >
            <Separator className="mb-4"/>
            <React.Suspense fallback={<div className="py-4 flex justify-center"><Spinner/></div>}>
              <ChecklistItemCompletionForm
                itemId={item.id}
                storeId={storeId}
                {...completionCallbacks}
                checklistId={checklistId}
                recurrenceId={recurrenceId}
              />
            </React.Suspense>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};
