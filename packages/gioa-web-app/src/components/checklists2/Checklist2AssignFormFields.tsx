import React from 'react';
import {ReactFormExtendedApi, Validator} from '@tanstack/react-form';
import {ZodType} from 'zod';
import {Switch} from '@/src/components/ui/switch';
import {Label} from '@/src/components/ui/label';
import {FormControl} from '@/src/components/form/FormControl';
import {FieldInfo} from '@/src/components/form/FieldInfo';
import {FormSelect} from '@/src/components/form/FormSelect';

import {find, flatMap, includes, map} from 'lodash';
import {Separator} from '@/src/components/ui/separator';
import {TeamMemberCombobox} from '@/src/components/TeamMemberCombobox';
import {StoreDashboardDto} from "../../../../api/src/schemas.ts";
import {SelectedTeamMemberRow} from "@/src/components/SelectedTeamMemberRow.tsx";

export interface ChecklistAssignFormValues {
  assignPeopleIds: string[],
  assignShiftLead: boolean | undefined,
  assignStoreAreaId: string | undefined,
  assignScheduleAreaTitle: string | undefined,
  assignStorePositionId: string | undefined,
}

export interface Checklist2AssignFormFieldsProps {
  form: ReactFormExtendedApi<ChecklistAssignFormValues, Validator<unknown, ZodType>>;
  store: StoreDashboardDto;
}

export const Checklist2AssignFormFields: React.FC<Checklist2AssignFormFieldsProps> = ({
  form,
  store
}) => {

  const hasSelectedTeamMembers = form.useStore((state) => state.values.assignPeopleIds.length > 0);
  const storeAreaOptions = map(store.storeAreas, area => {
    return {label: area.title, value: area.id}
  });
  const people = store.employees;

  const selectedAreaId = form.useStore((state) => state.values.assignStoreAreaId);
  const selectedArea = find(store.storeAreas, area => area.id === selectedAreaId);
  const storePositionOptions = flatMap(selectedArea ? [selectedArea] : store.storeAreas, area => {
    return map(area.positions, position => {
      return {label: position.title, value: position.id}
    })
  });

  return (
    <div>
      <form.Field name="assignPeopleIds"
        mode="array"
        children={(arrField) => (
          <div className="mb-4">
            <Label className={"mb-2 block"}>Assign to specific people</Label>

            {/* Display selected team members */}
            <div className="space-y-2 mb-2">
              {map(arrField.state.value, (personId, idx) => {
                const person = find(people, p => p.id === personId);
                if (!person) return null;

                return <SelectedTeamMemberRow person={person} key={person.id}
                                              onRemove={() => arrField.removeValue(idx)} />;
              })}
            </div>

            {/* Team member selector */}
            <TeamMemberCombobox
              people={people}
              value="" // Always empty to allow new selections
              className="w-full"
              onValueChange={(personId) => {
                if (personId) {
                  // Clear other assignment options when selecting a team member
                  form.setFieldValue("assignShiftLead", false);
                  form.setFieldValue("assignStoreAreaId", "");
                  form.setFieldValue("assignStorePositionId", "");
                  form.setFieldValue("assignScheduleAreaTitle", "");

                  // Add the person if not already selected
                  if (!includes(arrField.state.value, personId)) {
                    arrField.pushValue(personId);
                  }
                }
              }}
            />
            <FieldInfo field={arrField} />
          </div>
        )} />

      {/* Divider with OR label */}
      <div className="relative mb-6 mt-6">
        <Separator />
        <div className="absolute inset-0 flex items-center justify-center">
          <span className="bg-white px-2 text-gray-500 text-sm">OR</span>
        </div>
      </div>

      <form.Field name="assignShiftLead"
        children={(field) => (
          <FormControl className="flex flex-row items-center justify-between mb-4">
            <div>
              <Label htmlFor={field.name} className="cursor-pointer">Assign to shift lead</Label>
            </div>
            <Switch
              id={field.name}
              checked={field.state.value}
              onCheckedChange={(checked) => {
                if (checked && hasSelectedTeamMembers) {
                  // Clear team members when selecting shift lead
                  form.setFieldValue("assignPeopleIds", []);
                }
                field.handleChange(checked);
              }}
              disabled={hasSelectedTeamMembers}
            />
          </FormControl>
        )} />

      <form.Field name="assignStoreAreaId"
        children={(field) => (
          <FormControl className="mb-4">
            <Label htmlFor={field.name}>Store Area</Label>
            <FormSelect isClearable
              field={field}
              options={storeAreaOptions}
              placeholder="Select store area"
              isDisabled={hasSelectedTeamMembers}
              onChange={() => {
                // Clear position when changing area
                form.setFieldValue("assignStorePositionId", "");
              }}
            />
            <FieldInfo field={field} />
          </FormControl>
        )} />

      <form.Field name="assignStorePositionId"
        children={(field) => (
          <FormControl className="mb-4">
            <Label htmlFor={field.name}>Position</Label>
            <FormSelect isClearable
              field={field}
              options={storePositionOptions}
              placeholder="Select position"
              isDisabled={hasSelectedTeamMembers}
            />
            <FieldInfo field={field} />
          </FormControl>
        )} />
    </div>
  );
}
