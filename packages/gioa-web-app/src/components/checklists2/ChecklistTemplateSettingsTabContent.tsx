import React from 'react';
import {Text} from '@/src/components/Text';
import {ReactFormExtendedApi, Validator} from '@tanstack/react-form';
import {Label} from '@/src/components/ui/label';
import {FormInput} from '@/src/components/form/FormInput';
import {FieldInfo} from '@/src/components/form/FieldInfo';
import {z, ZodType} from 'zod';
import {FormTextarea} from '@/src/components/form/FormTextarea';
import {FormControl} from '@/src/components/form/FormControl';
import {Card} from '@/src/components/ui/card';

export interface ChecklistSettingsFormValues {
  title: string;
  description: string | undefined
}

export interface ChecklistTemplateSettingsTabContentProps {
  form: ReactFormExtendedApi<ChecklistSettingsFormValues, Validator<unknown, ZodType>>;
}

export const ChecklistTemplateSettingsTabContent = React.memo(({
  form,
}: ChecklistTemplateSettingsTabContentProps) => {
  return (
    <div className="flex-1">
      <div className="px-4 pb-6">
        <Card className="bg-white rounded-xl p-4 mb-4 border border-gray-200">
          <Text size="xl" semibold className="mb-2">
            Checklist
          </Text>

          <form.Field name="title"
            validators={{
              onSubmit: z.string().min(1, "Required")
            }}
            children={field => (
              <FormControl>
                <Label htmlFor={field.name}>Title</Label>
                <FormInput field={field}
                  placeholder="Give this checklist a name..." />
                <FieldInfo field={field} />
              </FormControl>
            )} />

          <form.Field name="description"
            children={field => (
              <FormControl>
                <Label htmlFor={field.name}>Description</Label>
                <FormTextarea
                  field={field}
                  placeholder="Add text about this checklist..." />
                <FieldInfo field={field} />
              </FormControl>
            )} />
        </Card>
      </div>
    </div>
  );
});
