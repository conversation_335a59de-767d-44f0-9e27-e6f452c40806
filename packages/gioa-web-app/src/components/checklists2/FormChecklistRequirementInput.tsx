import React from 'react';
import {capitalize, find, isEmpty, map, partition} from "lodash";
import {ReactFormExtendedApi} from '@tanstack/react-form';
import {
  ItemRequirementDto,
  RequirementConditionalDto
} from "../../../../api/src/checklists/checklistTemplate/checklistTemplateDtos";
import {cn} from '@/src/util';
import {Text} from "@/src/components/Text";
import {FieldInfo} from "@/src/components/form/FieldInfo.tsx";
import {FormTextarea} from "@/src/components/form/FormTextarea.tsx";
import {AttachmentDisplay, FormAttachmentInput} from "@/src/components/form/FormAttachmentInput.tsx";
import {FormInput} from "@/src/components/form/FormInput.tsx";
import {FormRadioGroup} from "@/src/components/form/FormRadioGroup.tsx";
import {requirementOptions} from "@/src/components/checklists2/checklistRequirements.tsx";

export interface FormChecklistRequirementInputProps {
  requirement: ItemRequirementDto;
  form: ReactFormExtendedApi<any, any>;
  isItemCompleted: boolean;
  className?: string;
}

const yesNoOptions = [{
  label: "Yes",
  value: true
}, {
  label: "No",
  value: false
}];

export const FormChecklistRequirementInput: React.FC<FormChecklistRequirementInputProps> = ({
                                                                                              requirement: req,
                                                                                              form, className,
                                                                                              isItemCompleted
                                                                                            }) => {
  const reqOption = find(requirementOptions, o => o.value === req.type);
  const renderConditional = (cond: RequirementConditionalDto, condIdx: number) => {
    return <div key={req.id + "-" + condIdx}>
      <Text className={cn("mb-2")}>
        {cond.thenText}
      </Text>

      {map(cond.thenRequirements, (req, reqIdx) => {
        return <FormChecklistRequirementInput requirement={req} key={reqIdx}
                                              className={"border border-gray-200 rounded-lg mb-4 pb-2 pt-3 px-4"}
                                              form={form} isItemCompleted={isItemCompleted}/>
      })}
    </div>
  }

  const renderInput = () => {
    const noneProvided = <Text size={"sm"} muted>None provided</Text>

    const formReq = find(form.state.values.requirements, (_, reqId) => reqId === req.id);
    switch (req.type) {
      case 'addImage': {
        return isItemCompleted
          ? formReq?.attachment ? <AttachmentDisplay hasErrors={false} value={formReq?.attachment}/> : noneProvided
          : <form.Field name={`requirements.${req.id}.attachment`}
                        children={field => <>
                          <FormAttachmentInput field={field} types={['image']}/>
                          <FieldInfo field={field}/>
                        </>
                        }/>
      }
      case 'writeComment': {
        return isItemCompleted
          ? formReq?.text ? <Text>{formReq?.text}</Text> : noneProvided
          : <form.Field name={`requirements.${req.id}.text`}
                        children={field => <>
                          <FormTextarea field={field} placeholder={"Write your comment here..."}/>
                          <FieldInfo field={field}/>
                        </>
                        }/>
      }
      case 'inputNumber': {
        return isItemCompleted
          ? formReq?.numberInput !== undefined
            ? <Text>{formReq?.numberInput}</Text>
            : noneProvided
          : <form.Field name={`requirements.${req.id}.numberInput`}
                        children={field => <>
                          <FormInput field={field}
                                     placeholder={"Enter a number..."}/>
                          <FieldInfo field={field}/>
                        </>
                        }/>
      }
      case 'inputBoolean': {
        const [yesConditionals, noConditionals] = partition(req.conditionals, cond => cond.if);

        return isItemCompleted
          ? <div>
            {formReq?.booleanInput !== undefined ?
              <Text>{formReq?.booleanInput ? "Yes" : "No"}</Text>
              : noneProvided}
            {formReq?.booleanInput !== undefined && !isEmpty(req.conditionals) ? <hr className={"my-4"}/> : null}
            {formReq?.booleanInput !== undefined ? map(formReq?.booleanInput ? yesConditionals : noConditionals, (cond, condIdx) => {
              return renderConditional(cond, condIdx);
            }) : null}
          </div>
          : <form.Field name={`requirements.${req.id}.booleanInput`}
                        children={field => {

                          return <div>
                            <div>
                              <div className={"mt-2"}>
                                <FormRadioGroup options={yesNoOptions} className={"mb-1"}
                                                field={field}/>
                              </div>
                              <FieldInfo field={field}/>
                            </div>
                            {field.state.value && !isEmpty(yesConditionals)
                              ? map(yesConditionals, (cond, condIdx) => {
                                return renderConditional(cond, condIdx);
                              })
                              : null}

                            {typeof field.state.value === "boolean" && !field.state.value && !isEmpty(noConditionals)
                              ? map(noConditionals, (cond, condIdx) => {
                                return renderConditional(cond, condIdx);
                              })
                              : null}
                          </div>
                        }}/>
      }

      default:
        return <Text>Unknown requirement type.</Text>
    }
  }

  return <div className={cn("bg-white mb-4", className)} key={req.id}>
    <Text bold className={cn("mb-1")}>
      {isItemCompleted ? reqOption?.roLabel : reqOption?.label}{' '}
      {req.type === "inputNumber" ? <Text muted className={cn("font-normal")} asChild={"span"}>({capitalize(req.numberType)})</Text> : null}
      {req.isRequired ? "" : <Text muted className={cn("font-normal")} asChild={"span"}>(optional)</Text>}
    </Text>

    {renderInput()}

  </div>
}
