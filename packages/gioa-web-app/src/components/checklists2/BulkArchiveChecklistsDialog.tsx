import React, {useState} from 'react';
import {<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON>ooter, <PERSON><PERSON><PERSON>eader, DialogTitle} from "@/src/components/ui/dialog.tsx";
import {FormControl} from "@/src/components/form/FormControl.tsx";
import {Label} from "@/src/components/ui/label.tsx";
import {FormTextarea} from "@/src/components/form/FormTextarea.tsx";
import {FieldInfo} from "@/src/components/form/FieldInfo.tsx";
import {Button} from "@/src/components/ui/button.tsx";
import {useForm} from "@tanstack/react-form";
import {zodValidator} from "@tanstack/zod-form-adapter";
import {toast} from "sonner";
import {api} from "@/src/api.ts";
import {Progress} from "@/src/components/ui/progress.tsx";

export interface BulkArchiveChecklistsDialogProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  selectedChecklists: Array<{ checklistId: string, recurrenceId?: Date }>;
  onArchiveSuccess: () => void;
}

export const BulkArchiveChecklistsDialog: React.FC<BulkArchiveChecklistsDialogProps> = ({
                                                                                          isOpen,
                                                                                          onOpenChange: _onOpenChange,
                                                                                          onArchiveSuccess,
                                                                                          selectedChecklists,
                                                                                        }) => {
  const apiUtil = api.useUtils();
  const archiveChecklist = api.checklist2.archiveChecklist.useMutation();
  const [numSuccess, setNumSuccess] = useState<number>(0);
  const [numFailed, setNumFailed] = useState<number>(0);
  const progressValue = ((numSuccess + numFailed) / selectedChecklists.length) * 100;

  const archiveForm = useForm({
    defaultValues: {
      reason: ""
    },
    validatorAdapter: zodValidator(),
    onSubmit: async ({value}) => {

      for (const {checklistId, recurrenceId} of selectedChecklists) {
        try {
          await archiveChecklist.mutateAsync({
            checklistId,
            recurrenceId,
            reason: value.reason || "No reason provided"
          });
          setNumSuccess(n => n + 1);
        } catch (e) {
          setNumFailed(n => n + 1);
        }
      }

      apiUtil.checklist2.invalidate();
      toast.success(`Successfully archived ${numSuccess} checklist${selectedChecklists.length === 1 ? '' : 's.'} Failed to archive ${numFailed}.`, {
        position: "top-center"
      });
      onArchiveSuccess();
      onOpenChange(false);
      archiveForm.reset();
    },
  });

  const onOpenChange = (open: boolean) => {
    if (archiveChecklist.isPending) {
      return;
    }
    setNumSuccess(0);
    setNumFailed(0);
    _onOpenChange(open);
  }

  return <Dialog open={isOpen} onOpenChange={onOpenChange}>
    <DialogContent>
      <DialogHeader>
        <DialogTitle>
          Archive {selectedChecklists.length} Checklist{selectedChecklists.length === 1 ? "" : "s"}
        </DialogTitle>
      </DialogHeader>

      <form
              onSubmit={(e) => {
                e.preventDefault();
                e.stopPropagation();
                archiveForm.handleSubmit();
              }}
      >
        <archiveForm.Field
                name="reason"
                children={(field) => (
                        <FormControl>
                          <Label htmlFor={field.name}>Reason (optional)</Label>
                          <FormTextarea
                                  field={field}
                                  placeholder="Enter the reason why you are archiving these checklists..."
                                  rows={3}
                          />
                          <div className="text-sm text-muted-foreground">
                            The reason will be saved in the record of past checklists.
                          </div>
                          <FieldInfo field={field}/>
                        </FormControl>
                )}
        />


        <Progress value={progressValue} className="mt-4"/>

        <DialogFooter className="mt-4">
          <Button
                  type="button"
                  variant="outline"
                  onClick={() => onOpenChange(false)}
                  disabled={archiveChecklist.isPending}
          >
            Cancel
          </Button>
          <Button type="submit" variant="destructive"
                  isLoading={archiveChecklist.isPending}
                  disabled={archiveChecklist.isPending}>
            {archiveChecklist.isPending ? "Archiving..." : "Archive Checklists"}
          </Button>
        </DialogFooter>
      </form>
    </DialogContent>
  </Dialog>
}
