import React from 'react';
import {CheckIcon} from 'lucide-react';
import {cn} from '../util';

interface WizardStepperProps {
  steps: string[];
  currentStep: number;
  className?: string;
}

export function WizardStepper({ steps, currentStep, className }: WizardStepperProps) {
  return (
    <div className={cn("flex items-center", className)}>
      {steps.map((step, index) => {
        const isActive = index === currentStep;
        const isCompleted = index < currentStep;

        return (
          <React.Fragment key={index}>
            {/* Step circle */}
            <div className="flex items-center">
              <div
                className={cn(
                  "flex items-center justify-center w-8 h-8 rounded-full border transition-colors",
                  isActive && "border-primary bg-primary text-white",
                  isCompleted && "border-primary bg-primary text-white",
                  !isActive && !isCompleted && "border-gray-300 bg-white text-gray-500"
                )}
              >
                {isCompleted ? (
                  <CheckIcon className="w-4 h-4" />
                ) : (
                  <span className="text-sm">{index + 1}</span>
                )}
              </div>
            </div>

            {/* Step label */}
            <div className="ml-2 text-sm font-medium">
              <span
                className={cn(
                  "transition-colors",
                  isActive && "text-primary",
                  isCompleted && "text-primary",
                  !isActive && !isCompleted && "text-gray-500"
                )}
              >
                {step}
              </span>
            </div>

            {/* Connector line */}
            {index < steps.length - 1 && (
              <div
                className={cn(
                  "flex-1 h-0.5 mx-4 transition-colors",
                  index < currentStep ? "bg-primary" : "bg-gray-300"
                )}
              />
            )}
          </React.Fragment>
        );
      })}
    </div>
  );
}
