import {useForm} from "@tanstack/react-form";
import {InputOTP, InputOTPGroup, InputOTPSlot} from "@/src/components/ui/input-otp";
import {REGEXP_ONLY_DIGITS} from "input-otp";
import {Button} from "@/src/components/ui/button";
import {ErrorAlert} from "@/src/components/ErrorAlert";
import {api} from "@/src/api";
import {toast} from "sonner";
import React from "react";
import {UserDto} from "../../../api/src/schemas.ts";

interface MfaVerificationProps {
  pendingSessionId: string;
  onVerificationSuccess: (user: UserDto) => Promise<void>;
}

export function MfaVerification({pendingSessionId, onVerificationSuccess}: MfaVerificationProps) {
  const verifyMfaMutation = api.auth.verifySmsMfaCode.useMutation();
  const resendMfaCodeMutation = api.mfa.resendSmsMfaCode.useMutation();

  const form = useForm({
    defaultValues: {
      code: ""
    },
    onSubmit: async ({value}) => {
      verifyMfaMutation.mutate({
        pendingSessionId,
        code: value.code
      }, {
        onSuccess: async (data) => {
          onVerificationSuccess(data.user);
        }
      });
    }
  });

  const handleResendCode = () => {
    resendMfaCodeMutation.mutate({
      pendingSessionId
    }, {
      onSuccess: () => {
        toast.success("Verification code resent to your phone", {
          position: "top-center",
          dismissible: true
        });
      }
    });
  };

  return (
    <>
      {verifyMfaMutation.isError &&
          <ErrorAlert className="mb-3 animate-fade-in-up" error={verifyMfaMutation.error}/>}
      {resendMfaCodeMutation.isError &&
          <ErrorAlert className="mb-3 animate-fade-in-up" error={resendMfaCodeMutation.error}/>}

      <form onSubmit={e => {
        e.preventDefault();
        e.stopPropagation();
        form.handleSubmit();
      }}>
        <div>
          <form.Field name="code"
                      children={(field) => <div className={"flex justify-center mb-6"}>
                        <InputOTP
                          maxLength={6}
                          pattern={REGEXP_ONLY_DIGITS}
                          name={field.name}
                          value={field.state.value}
                          onBlur={field.handleBlur}
                          onChange={(value) => field.handleChange(value)}
                          id={field.name}
                        >
                          <InputOTPGroup>
                            <InputOTPSlot index={0}/>
                            <InputOTPSlot index={1}/>
                            <InputOTPSlot index={2}/>
                            <InputOTPSlot index={3}/>
                            <InputOTPSlot index={4}/>
                            <InputOTPSlot index={5}/>
                          </InputOTPGroup>
                        </InputOTP>
                      </div>}
          />

          <Button
            type="submit"
            className="w-full mb-3"
            isLoading={verifyMfaMutation.isPending}
          >
            {verifyMfaMutation.isPending ? "Verifying..." : "Verify Code"}
          </Button>

          <div className="text-center text-sm">
            Didn't receive the code?{" "}
            <Button
              variant="link"
              type="button"
              onClick={handleResendCode}
              isLoading={resendMfaCodeMutation.isPending}
            >
              Resend code
            </Button>
          </div>
        </div>
      </form>
    </>
  );
}
