import {Sheet, SheetContent, SheetDes<PERSON>, She<PERSON><PERSON>eader, SheetTitle,} from "@/src/components/ui/sheet"
import React, {Suspense} from "react";
import {LoadingOverlay} from "@/src/components/LoadingOverlay.tsx";
import {TimeOffRequestSheetContent} from "@/src/components/TimeOffRequestSheetContent.tsx";

export interface TimeOffRequestProps {
  timeOffId: string;
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  businessId: string;
  storeId: string;
}

export function TimeOffRequestSheet(props: TimeOffRequestProps) {
  const timeOffId = props.timeOffId;

  return (
    <Sheet modal={false} open={props.isOpen}
           onOpenChange={props.onOpenChange}>
      <SheetContent className="flex flex-col overflow-auto px-0">
        <SheetHeader className={"pb-4 px-4"}>
          <SheetTitle>Time Off Request</SheetTitle>
          <SheetDescription>
            Review time off request.
          </SheetDescription>
        </SheetHeader>
        <Suspense fallback={<LoadingOverlay isLoading={true}/>}>
          <TimeOffRequestSheetContent onOpenChange={props.onOpenChange}
                                      businessId={props.businessId}
                                      storeId={props.storeId}
                                      timeOffId={timeOffId}/>
        </Suspense>
      </SheetContent>
    </Sheet>
  )
}
