import React, {useLayoutEffect} from 'react';
import {Input} from "@/src/components/ui/input.tsx";
import {EyeIcon, SearchIcon, SortAscIcon, SortDescIcon} from "lucide-react";
import {Button} from "@/src/components/ui/button.tsx";
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger
} from "@/src/components/ui/dropdown-menu.tsx";
import {WeeklyViewSort} from "@/src/components/WeeklyViewControlBar.types.ts";
import {includes} from "lodash";
import {toggleInArray} from "@/src/components/WeeklyFilterControl.tsx";

export interface WeeklyViewControlBarProps {
  onSetSort: (sort: WeeklyViewSort) => void;
  sortColumn: string;
  sortAscending: boolean;
  setSearch: (search: string) => void;
  search: string;
  setViews: (views: string[]) => void;
  views: string[];
}

export const WeeklyViewControlBar: React.FC<WeeklyViewControlBarProps> = ({
                                                                            setSearch,
                                                                            search,
                                                                            setViews, views,
                                                                            onSetSort,
                                                                            sortColumn,
                                                                            sortAscending,
                                                                          }) => {


  const searchInputRef = React.useRef<HTMLInputElement>(null);
  useLayoutEffect(() => {
    if (searchInputRef.current) {
      searchInputRef.current.value = search;
    }
  }, [search]);

  const onToggleView = (view: string) => {
    const newViews = toggleInArray(views, view);
    setViews(newViews);
  }

  return (
    <div className={"flex flex-row flex-wrap gap-1 items-center"}>
      <form onReset={e => setSearch("")}
            onSubmit={e => {
              e.preventDefault();
              setSearch(searchInputRef.current?.value ?? "");
            }}>
        <Input type={"search"} ref={searchInputRef}
               leftIcon={SearchIcon}
               placeholder="Search..."
               className="w-auto"/>
      </form>

      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant={"outline"}
                  rightIcon={sortAscending ? <SortAscIcon className={"text-gray-700"} size={16}/> :
                    <SortDescIcon className={"text-gray-700"} size={16}/>}>
            <span className={"font-normal pr-1"}>
            Sort by:
            </span>
            {sortColumn === "firstName" ? "First name" : sortColumn === "lastName" ? "Last name" : "Hours"}
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent>
          <DropdownMenuLabel>Sort by</DropdownMenuLabel>
          <DropdownMenuSeparator/>
          <DropdownMenuItem onSelect={() => onSetSort({column: "firstName", isAsc: true})}>First name
            A-Z</DropdownMenuItem>
          <DropdownMenuItem onSelect={() => onSetSort({column: "firstName", isAsc: false})}>First name
            Z-A</DropdownMenuItem>
          <DropdownMenuItem onSelect={() => onSetSort({column: "lastName", isAsc: true})}>Last name
            A-Z</DropdownMenuItem>
          <DropdownMenuItem onSelect={() => onSetSort({column: "lastName", isAsc: false})}>Last name
            Z-A</DropdownMenuItem>
          <DropdownMenuSeparator/>
          <DropdownMenuItem onSelect={() => onSetSort({column: "hours", isAsc: false})}>Max hours</DropdownMenuItem>
          <DropdownMenuItem onSelect={() => onSetSort({column: "hours", isAsc: true})}>Min hours</DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant={"outline"} rightIcon={<EyeIcon className={"text-gray-700"} size={16}/>}>
            View
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent>
          <DropdownMenuCheckboxItem onCheckedChange={() => onToggleView("avail")}
                                    checked={includes(views, "avail")}>
            Availability
          </DropdownMenuCheckboxItem>
          <DropdownMenuCheckboxItem onCheckedChange={() => onToggleView("timeOff")}
                                    checked={includes(views, "timeOff")}>
            Time off
          </DropdownMenuCheckboxItem>
        </DropdownMenuContent>
      </DropdownMenu>

    </div>
  );
}
