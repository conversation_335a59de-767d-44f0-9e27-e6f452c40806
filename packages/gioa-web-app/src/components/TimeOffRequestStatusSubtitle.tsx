import React from 'react';
import {formatDateMonthNoTime} from "@/src/date.util.ts";
import {TimeOffAdminDto} from "../../../api/src/personTimeOff.schemas.ts";

export interface TimeOffRequestStatusSubtitleProps {
  request: TimeOffAdminDto
}

export const TimeOffRequestStatusSubtitle: React.FC<TimeOffRequestStatusSubtitleProps> = ({request: req}) => {
    return <div className={"flex flex-wrap gap-3 items-center mb-6"}>
      <div>
        Requested
        on {formatDateMonthNoTime(req.submittedAt!)} by {req.submittedByPerson?.firstName} {req.submittedByPerson?.lastName}
      </div>
      {req.approvedAt ? <>
        |
        <div>
          Approved
          on {formatDateMonthNoTime(req.approvedAt)} by {req.approvedByPerson?.firstName} {req.approvedByPerson?.lastName}
        </div>
      </> : null}
      {req.declinedAt ? <>
        |
        <div>
          Declined
          on {formatDateMonthNoTime(req.declinedAt)} by {req.declinedByPerson?.firstName} {req.declinedByPerson?.lastName}
        </div>
      </> : null}
    </div>
}
