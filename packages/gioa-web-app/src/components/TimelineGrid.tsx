import React from 'react';
import {map} from "lodash";

export interface RowSpan<TSpanData> {
  start: number; // col index
  end: number; // col index
  metadata: TSpanData;
}

export interface RowData<TData, TSpanData> {
  id: string;
  spans: RowSpan<TSpanData>[];
  metadata: TData;
}

export interface TimelineGridProps<TData, TSpanData> {
  columnWidth: number;
  rowHeight: number;
  rows: RowData<TData, TSpanData>[];
  renderSpan: (params: {
    row: RowData<TData, TSpanData>
    span: RowSpan<TSpanData>;
    rowIndex: number;
    spanIdx: number;
  }) => React.ReactNode;
}

export function TimelineGrid<TData, TSpanData>({
                                                 columnWidth,
                                                 rowHeight,
                                                 renderSpan,
                                                 rows
                                               }: TimelineGridProps<TData, TSpanData>) {
  return map(rows, (row, rowIdx) => {
    return <div key={row.id} className={"relative"}
                style={{height: rowHeight}}>
      {map(row.spans, (span, spanIdx) => {
        return <div key={span.start + span.end + spanIdx} style={{
          position: 'absolute',
          top: 0,
          left: span.start * columnWidth,
          width: (span.end - span.start) * columnWidth,
          height: rowHeight,
        }}>
          {renderSpan({
            row,
            span: span,
            rowIndex: rowIdx,
            spanIdx: spanIdx,
          })}
        </div>
      })}
    </div>
  })
}

