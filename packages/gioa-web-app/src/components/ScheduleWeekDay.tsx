import React, {useMemo, useState} from 'react';
import {ScheduleRevision} from "../../../api/src/scheduleSchemas.ts";
import {daysOfWeek} from "@/src/components/AvailabilityWeek.tsx";
import {chain, filter, find, flatMap, isEmpty, map, partition} from "lodash";
import {formatDateToMonthDay} from "@/src/date.util.ts";
import {doDateTimeRangesOverlap, getDateFromWeekDayTime} from "../../../api/src/date.util.ts";
import {calculateMetrics, getAreaMetrics} from "../../../api/src/scheduling/metrics/metrics.util.ts";
import {CircleDollarSignIcon, GaugeIcon, NotebookPenIcon} from "lucide-react";
import {cn} from "@/src/util.ts";
import {ValidationMessage} from "../../../api/src/scheduleValidation.types.ts";
import {Button} from './ui/button.tsx';
import {EventsPopover} from "@/src/components/EventsPopover.tsx";
import {IsoWeekDate} from "../../../api/src/timeSchemas.ts";
import {addDays} from "date-fns";
import {Link} from "@tanstack/react-router";
import {ScheduleDayStatusBadge} from "@/src/components/ScheduleDayStatusBadge.tsx";
import {useIgnoreScheduleValidationMessages} from "@/src/hooks/useIgnoreScheduleValidationMessages.tsx";
import {ScheduleEventDto} from '@gioa/api/src/scheduleEventSchemas.ts';
import {Accordion, AccordionContent, AccordionItem, AccordionTrigger} from "@/src/components/ui/accordion.tsx";
import {getDay} from "../../../api/src/schedule.ts";
import {ValueBadge} from "@/src/components/ValueBadge.tsx";

import {
  isMessageForWeekday,
  isMessageIgnorable
} from "../../../api/src/scheduleValidation/scheduleValidationMessagesL2.ts";
import {
  ValidScheduleHourlySalesForecast
} from "../../../api/src/scheduling/metrics/scheduleHourlySalesForecast/scheduleHourlySalesForecastTypes.ts";
import {formatCurrency, formatHours} from "../../../common/src/dataFormatters.ts";
import {PayRates} from "../../../api/src/scheduling/metrics/payRates/payRatesDto.ts";
import {Route} from "@/src/routes/_signedin/$businessId/$storeId/_nav/schedules/builder.tsx";

export interface ScheduleWeekDayProps {
  schedule?: ScheduleRevision;
  forecast: ValidScheduleHourlySalesForecast | undefined;
  payRates: PayRates;
  dayOfWeek: number;
  date: Date;
  isFirst: boolean;
  isLast: boolean;
  isWeekSelected: boolean;
  warnings: ValidationMessage[];
  events: ScheduleEventDto[];
  week: IsoWeekDate;
  storeId: string;
  routeFrom: string;
  onViewNotes: (scheduleId: string, dayOfWeek: number) => void;
  numNotes: number;
  timezone: string | null;
  onToggleAreaStats: () => void;
  areaStatsExpanded: boolean;
  isForecastingEnabled: boolean;
}

export const moneyFormat = new Intl.NumberFormat('en-US', {
  style: 'currency',
  currency: 'USD',
  maximumFractionDigits: 0
});


export const ScheduleWeekDay: React.FC<ScheduleWeekDayProps> = ({
                                                                  schedule: rev, isForecastingEnabled,
                                                                  forecast, payRates,
                                                                  dayOfWeek,
                                                                  date, storeId,
                                                                  isWeekSelected,
                                                                  isLast, onViewNotes,
                                                                  isFirst, week, numNotes,
                                                                  warnings, events, routeFrom,
                                                                  onToggleAreaStats, areaStatsExpanded,
                                                                  timezone
                                                                }) => {
  const dayStart = getDateFromWeekDayTime({...week, day: dayOfWeek, time: "00:00", timezone: timezone});
  const dayEnd = addDays(dayStart, 1);
  const [now] = useState(new Date());
  const isPastDay = dayEnd < now;
  const eventsForDay = useMemo(() => filter(events, e => doDateTimeRangesOverlap(e.range, {
    start: dayStart,
    end: dayEnd
  })), [events]);

  const sched = rev?.draft ?? rev?.published;
  const hasSchedule = Boolean(sched);
  const dayOfWeekObj = find(daysOfWeek, d => d.dayOfWeek === dayOfWeek)!;
  const day = sched ? getDay(sched, dayOfWeek) : undefined;
  const metrics = calculateMetrics({
    isForecastingEnabled: isForecastingEnabled,
    payRates: payRates,
    schedule: sched,
    weekday: dayOfWeek,
    forecast: forecast,
    metricsInput: day?.metricsInput
  })
  const areaMetrics = day ? getAreaMetrics(day, {
    averagePayRate: metrics.averagePayRate,
    payRates: payRates
  }) : null;

  const shifts = flatMap(day?.areas, a => a.shifts);
  const [, assignedShifts] = partition(shifts, s => s.assignedPersonId === undefined);
  const areAllShiftsScheduled = assignedShifts.length === shifts.length;
  const isComplete = !isEmpty(shifts) && areAllShiftsScheduled;

  const {ignoredMessages, isMessageIgnored} = useIgnoreScheduleValidationMessages({
    scheduleId: sched?.id,
    storeId: storeId,
  });
  const notIgnoredWarningsForToday = useMemo(() => {
    return chain(warnings)
      .filter(isMessageForWeekday(dayOfWeek))
      .filter(isMessageIgnorable)
      .filter(m => !isMessageIgnored(m))
      .value();
  }, [warnings, ignoredMessages]);

  const onAccordionChange = (value: string) => {
    onToggleAreaStats();
  }

  const DayContainer = ({children}: { children: React.ReactNode }) => {
    return hasSchedule ? <Link from={Route.fullPath}
                               to={"../../schedules/$scheduleId"}
                               params={{scheduleId: sched?.id!}}
                               search={{
                                 dayOfWeek,
                                 w: true,
                                 week: false
                               }}
                               className={cn("pt-2 px-3 block border-b bg-[#FAFBFF]", {
                                 "hover:bg-slate-50": isWeekSelected && sched,
                               })}>
        {children}
      </Link>
      : <div className={cn("pt-2 px-3 block border-b bg-[#FAFBFF]")}>
        {children}
      </div>
  }

  return (
    <div className={cn({"bg-white": isWeekSelected})}>
      <DayContainer>
        <div className={"flex gap-2 justify-between items-start "}>
          <div>
            <h3 className={"font-bold leading-none flex gap-1 items-center"}>
              {dayOfWeekObj.abbr}
              {/*{isCurrentDay && <CircleIcon size={16} className={"text-blue-300 fill-blue-300"}/>}*/}
            </h3>
            <div className={"mb-3 text-xs text-muted-foreground"}>
              {formatDateToMonthDay(date)}
            </div>
          </div>
          <ScheduleDayStatusBadge warningsCount={notIgnoredWarningsForToday.length}
                                  isPast={isPastDay}
                                  isComplete={isComplete}/>
        </div>
        <div
          className={"flex flex-wrap gap-3 items-center justify-between border border-gray-300 rounded-md px-3 py-2 whitespace-nowrap text-sm"}>
          <div className={"flex gap-1 items-center"}>
            <GaugeIcon className={"text-gioaBlue"} size={20}/>
            {formatCurrency(metrics?.actualProductivity, true)}
          </div>
          <div className={"flex gap-1 items-center"}>
            <CircleDollarSignIcon className={"text-green-500"} size={20}/>
            {formatCurrency(metrics.projectedRevenue, true)}
          </div>
        </div>

        <div className={"flex flex-wrap justify-between items-center gap-2 pb-2 mb-2 pt-3"}>
          <div className={"text-sm whitespace-nowrap"}>
            Hours <span className={"font-bold"}>{formatHours(metrics?.totalLaborHours)}</span>
          </div>
          <div className={"text-sm whitespace-nowrap"}>
            {assignedShifts.length}
            /
            {shifts?.length}{' '}
            Shifts
          </div>
        </div>
      </DayContainer>

      <div className={"flex justify-between flex-wrap gap-2 px-3 pt-3 border-b pb-3 bg-[#FAFBFF]"}>
        <EventsPopover dayOfWeek={dayOfWeek} storeId={storeId} timezone={timezone}
                       events={eventsForDay} week={week}/>
        <Button size={"xs"} variant={"secondary"} disabled={!rev}
                onClick={() => onViewNotes(sched?.id!, dayOfWeek)}
                leftIcon={<NotebookPenIcon size={16} className={"text-gray-600"}/>}>
          Notes {numNotes > 0 ? `(${numNotes})` : null}
        </Button>
      </div>

      <Accordion type="single" collapsible onValueChange={onAccordionChange}
                 value={areaStatsExpanded ? "areaStats" : ""}
                 className="w-full bg-white">
        <AccordionItem key={"areaStats"} value={"areaStats"} className={"border-b-0"}>
          <AccordionTrigger className={"px-3 py-2 text-sm "}>
            Stats
          </AccordionTrigger>
          <AccordionContent className={"py-3 px-3 text-sm"}>
            {areaMetrics ?
              <table className={'border-collapse w-full'}>
                <thead>
                <tr>
                  <th className={"text-left  pr-1 font-semibold"}>
                    Area
                  </th>
                  <th className={"text-left whitespace-nowrap  px-1 font-semibold"}>
                    Hrs
                  </th>
                  <th className={"text-left  pl-1 font-semibold"}>
                    Shifts
                  </th>
                </tr>
                </thead>
                <tbody>
                {map(areaMetrics, (areaMetrics, areaId) => {
                  const area = find(day?.areas, a => a.id === areaId);
                  return <tr key={areaId}>
                    <td className={"text-left pr-1 text-sm py-0.5"}>
                      {area?.title}
                    </td>
                    <td className={"px-0.5 text-center text-sm py-0.5"}>
                      <ValueBadge>
                        {formatHours(areaMetrics.totalOpsLaborHours, 0)}
                      </ValueBadge>
                    </td>
                    <td className={"pl-0.5 text-center text-sm py-0.5"}>
                      <ValueBadge>
                        {areaMetrics.assignedShiftCount}
                      </ValueBadge>
                    </td>
                  </tr>
                })}
                </tbody>
              </table> : null}

            <hr className={"my-3"}/>
            <table className={'border-collapse w-full'}>
              <tbody>
              <tr>
                <td className={"pr-1"}>
                  Total hrs
                </td>
                <td className={"text-center text-sm py-0.5"}>
                  <ValueBadge>{formatHours(metrics?.totalLaborHours)}</ValueBadge>
                </td>
              </tr>
              <tr>
                <td className={"pr-1"}>
                  Total ops hrs
                </td>
                <td className={"text-center text-sm py-0.5"}>
                  <ValueBadge>{formatHours(metrics?.totalOpsLaborHours)}</ValueBadge>
                </td>
              </tr>
              <tr>
                <td className={"pr-1"}>
                  Total non-ops hrs
                </td>
                <td className={"text-center text-sm py-0.5"}>
                  <ValueBadge>
                    {formatHours(metrics?.totalNonOpsLaborHours)}
                  </ValueBadge>
                </td>
              </tr>
              </tbody>
            </table>
          </AccordionContent>
        </AccordionItem>
      </Accordion>
    </div>
  );
}
