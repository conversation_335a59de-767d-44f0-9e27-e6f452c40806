import React, { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>Footer,
} from "@/src/components/ui/dialog";
import { Button } from "@/src/components/ui/button";
import { Text } from "@/src/components/Text";
import { api } from "@/src/api";
import { useForm } from "@tanstack/react-form";
import { zodValidator } from "@tanstack/zod-form-adapter";
import { z } from "zod";
import { FormControl } from "@/src/components/form/FormControl";
import { Label } from "@/src/components/ui/label";
import { FormInput } from "@/src/components/form/FormInput";
import { FieldInfo } from "@/src/components/form/FieldInfo";
import { map } from "lodash";
import { toast } from "sonner";
import { X } from "lucide-react";

interface ManageCorrectiveActionTypesModalProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  storeId: string;
}

export function ManageCorrectiveActionTypesModal({
  isOpen,
  onOpenChange,
  storeId,
}: ManageCorrectiveActionTypesModalProps) {
  const [isAddingNew, setIsAddingNew] = useState(false);
  
  // Fetch custom action types
  const [{ items: customTypes }] = api.correctiveAction.getCorrectiveActionCustomActionTypes.useSuspenseQuery(
    { storeId },
    {
      staleTime: 1000 * 60 * 15, // 15 minutes
    },
  );
  
  const apiUtil = api.useUtils();
  const createCustomType = api.correctiveAction.createCorrectiveActionCustomActionType.useMutation();
  const archiveType = api.correctiveAction.archiveCorrectiveActionCustomActionType.useMutation();

  const form = useForm({
    defaultValues: {
      title: "",
    },
    validatorAdapter: zodValidator(),
    onSubmit: async ({ value }) => {
      createCustomType.mutate(
        {
          storeId,
          title: value.title,
        },
        {
          onSuccess: () => {
            apiUtil.correctiveAction.getCorrectiveActionCustomActionTypes.invalidate({ storeId });
            form.reset();
            setIsAddingNew(false);
            toast.success("New action type added successfully");
          },
          onError: () => {
            toast.error("Failed to add custom action type");
          },
        },
      );
    },
  });

  const onArchive = (id: string) => {
    if (confirm("Are you sure you want to archive this action type? Previous corrective actions that use this type will NOT be impacted.")) {
      archiveType.mutate(
        { id, storeId },
        {
          onSuccess() {
            apiUtil.correctiveAction.getCorrectiveActionCustomActionTypes.invalidate({ storeId });
            toast.success("Action type archived successfully");
          },
          onError() {
            toast.error("Failed to archive action type");
          },
        },
      );
    }
  };

  const onDone = () => {
    onOpenChange(false);
  };

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Manage Action Types</DialogTitle>
        </DialogHeader>
        
        <div className="py-4">
          {/* List of existing custom types */}
          <div className="mb-4">
            {map(customTypes, (type) => (
              <div key={type.id} className="flex items-center justify-between py-2 border-b border-gray-200">
                <Text>{type.title}</Text>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => onArchive(type.id)}
                  className="h-8 w-8 p-0"
                >
                  <X className="h-4 w-4" />
                  <span className="sr-only">Delete</span>
                </Button>
              </div>
            ))}
          </div>

          {/* Form to add new type */}
          {isAddingNew ? (
            <div className="mb-4">
              <form.Field
                name="title"
                validators={{
                  onSubmit: z.string().min(1, "Title is required"),
                }}
                children={(field) => (
                  <FormControl>
                    <Label>New Action Type</Label>
                    <FormInput field={field} placeholder="Enter action type..." />
                    <FieldInfo field={field} />
                  </FormControl>
                )}
              />
              <div className="flex gap-2 mt-2">
                <Button
                  type="button"
                  onClick={form.handleSubmit}
                  disabled={createCustomType.isPending}
                >
                  {createCustomType.isPending ? "Saving..." : "Save"}
                </Button>
                <Button
                  variant="outline"
                  onClick={() => setIsAddingNew(false)}
                  disabled={createCustomType.isPending}
                >
                  Cancel
                </Button>
              </div>
            </div>
          ) : (
            <Button
              variant="outline"
              onClick={() => setIsAddingNew(true)}
              className="w-full"
            >
              + Add New Action Type
            </Button>
          )}
        </div>

        <DialogFooter>
          <Button onClick={onDone}>Done</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
