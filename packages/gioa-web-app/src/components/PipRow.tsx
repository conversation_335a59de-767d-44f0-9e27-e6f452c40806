import React from 'react';
import {map, times} from "lodash";
import {Pip} from "./Pip";
import {Text} from "./Text";

export interface PipRowProps {
  numSteps: number;
  activeStep: number;
}

export const PipRow: React.FC<PipRowProps> = ({numSteps, activeStep}) => {
  // if there are more than 16 pips, then fugghedaboutit
  if (numSteps > 16) {
    return <div className={"flex flex-row justify-center"}>
      <Text>Step {activeStep + 1} of {numSteps}</Text>
    </div>
  }
  return (
    <div className={"flex flex-row justify-center gap-2"}>
      {map(times(numSteps, (i) => <Pip key={i} isActive={i === activeStep}/>))}
    </div>
  );
}
