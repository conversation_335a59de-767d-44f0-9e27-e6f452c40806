import React, { useEffect } from "react";
import { api } from "@/src/api";
import { Text } from "@/src/components/Text";
import { find } from "lodash";
import { useForm } from "@tanstack/react-form";
import { PolicyViolationListModal } from "@/src/components/PolicyViolationListModal";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/src/components/ui/select";

const pastDayOptions = [
  { value: 7, label: "7 Days" },
  { value: 30, label: "30 Days" },
  { value: 90, label: "90 Days" },
  { value: 180, label: "180 Days" },
  { value: 365, label: "1 year" },
];

const getPastDaysOptionLabel = (pastDays: number) =>
  find(pastDayOptions, (o) => o.value === pastDays)?.label ?? "30 Days";

function usePolicyViolationCounts({
  personId,
  storeId,
  pastDays,
}: {
  personId: string;
  storeId: string;
  pastDays: number;
}) {
  return api.user.getPolicyViolationCountsNew.useQuery(
    {
      personId: personId,
      storeId: storeId,
      pastDays: pastDays,
    },
    {
      staleTime: 1000 * 60 * 15, // 15 minutes
      refetchInterval: 1000 * 60 * 15, // 15 minutes
    },
  );
}

function ViolationCountDisplay({ isLoading, totalCount }: { isLoading: boolean; totalCount: number }) {
  return (
    <div className="flex-1 bg-gray-100 p-2 rounded-md border border-gray-100">
      {isLoading ? (
        <div className="animate-spin h-4 w-4 border-2 border-gray-400 rounded-full border-t-transparent" />
      ) : (
        <Text size={"sm"} className={totalCount > 0 ? "underline cursor-pointer" : ""}>
          {totalCount ?? 0} time{totalCount !== 1 ? "s" : ""}
        </Text>
      )}
    </div>
  );
}

export function PolicyViolationHistoricalCount({
  personId,
  businessId,
  storeId,
  policy,
  omitNoteIds,
  timezone,
}: {
  personId: string;
  businessId: string;
  storeId: string;
  policy: string;
  omitNoteIds?: string[];
  timezone: string;
}) {
  const form = useForm({
    defaultValues: {
      pastDays: 90,
    },
  });

  const [isModalOpen, setIsModalOpen] = React.useState(false);
  const pastDays = form.useStore((state) => state.values.pastDays);

  const query = usePolicyViolationCounts({ personId, storeId, pastDays });
  const { data, isLoading, refetch } = query;

  const correctiveActionPolicies = data?.correctiveActionPolicies ?? {};
  const coachingMomentPolicies = data?.coachingMomentPolicies ?? {};

  const correctiveActionCount = correctiveActionPolicies[policy] ?? 0;
  const coachingMomentCount = coachingMomentPolicies[policy] ?? 0;
  const totalCount = correctiveActionCount + coachingMomentCount;

  // Refetch data when pastDays changes
  useEffect(() => {
    refetch();
  }, [pastDays, refetch]);

  const handlePastDaysChange = (value: string) => {
    form.setFieldValue("pastDays", parseInt(value));
  };

  return (
    <div className="mb-3">
      <Text>{policy}</Text>
      <div className="flex flex-row gap-3 items-center">
        <div className="cursor-pointer flex-1" onClick={() => setIsModalOpen(true)}>
          <ViolationCountDisplay isLoading={isLoading} totalCount={totalCount} />
        </div>
        <div className="flex-1">
          <Select value={pastDays.toString()} onValueChange={handlePastDaysChange}>
            <SelectTrigger className="w-full">
              <SelectValue placeholder="Select period">{getPastDaysOptionLabel(pastDays)}</SelectValue>
            </SelectTrigger>
            <SelectContent>
              {pastDayOptions.map((option) => (
                <SelectItem key={option.value} value={option.value.toString()}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      <PolicyViolationListModal
        isOpen={isModalOpen}
        onOpenChange={setIsModalOpen}
        businessId={businessId}
        storeId={storeId}
        personId={personId}
        omitNoteIds={omitNoteIds}
        title={policy}
        description={`(${totalCount}) violations in the past ${getPastDaysOptionLabel(pastDays)}`}
        timezone={timezone}
        filters={{
          orderBy: "recent",
          pastDays: pastDays,
          policies: [policy],
          usePolicyFilter: true,
          showCorrectiveActions: true,
          showCoaching: true,
          showFeedback: false,
          showGeneral: false,
          showPositionScores: false,
          includeArchived: false,
        }}
      />
    </div>
  );
}

export function PolicyViolationRecordHistoricalCount({
  personId,
  businessId,
  storeId,
  type,
  omitNoteIds,
  timezone,
}: {
  personId: string;
  businessId: string;
  storeId: string;
  type: "correctiveAction" | "coachingMoment";
  omitNoteIds?: string[];
  timezone: string;
}) {
  const form = useForm({
    defaultValues: {
      pastDays: 90,
    },
  });

  const [isModalOpen, setIsModalOpen] = React.useState(false);
  const pastDays = form.useStore((state) => state.values.pastDays);

  const query = usePolicyViolationCounts({ personId, storeId, pastDays });
  const { data, isLoading, refetch } = query;

  const totalCoachingMoments = data?.totalCoachingMoments ?? 0;
  const totalCorrectiveActions = data?.totalCorrectiveActions ?? 0;

  const totalCount = type === "correctiveAction" ? totalCorrectiveActions : totalCoachingMoments;

  // Refetch data when pastDays changes
  useEffect(() => {
    refetch();
  }, [pastDays, refetch]);

  const handlePastDaysChange = (value: string) => {
    form.setFieldValue("pastDays", parseInt(value));
  };

  return (
    <div className="mb-3">
      <Text>Total {type === "correctiveAction" ? "Corrective Actions" : "Coaching Moments"}</Text>
      <div className="flex flex-row gap-3 items-center">
        <div className="cursor-pointer flex-1" onClick={() => setIsModalOpen(true)}>
          <ViolationCountDisplay isLoading={isLoading} totalCount={totalCount} />
        </div>
        <div className="flex-1">
          <Select value={pastDays.toString()} onValueChange={handlePastDaysChange}>
            <SelectTrigger className="w-full">
              <SelectValue placeholder="Select period">{getPastDaysOptionLabel(pastDays)}</SelectValue>
            </SelectTrigger>
            <SelectContent>
              {pastDayOptions.map((option) => (
                <SelectItem key={option.value} value={option.value.toString()}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      <PolicyViolationListModal
        isOpen={isModalOpen}
        onOpenChange={setIsModalOpen}
        businessId={businessId}
        storeId={storeId}
        personId={personId}
        omitNoteIds={omitNoteIds}
        title={type === "correctiveAction" ? "Corrective Actions" : "Coaching Moments"}
        description={`(${totalCount}) in the past ${getPastDaysOptionLabel(pastDays)}`}
        timezone={timezone}
        filters={{
          orderBy: "recent",
          pastDays: pastDays,
          policies: [],
          usePolicyFilter: false,
          showCorrectiveActions: type === "correctiveAction",
          showCoaching: type === "coachingMoment",
          showFeedback: false,
          showGeneral: false,
          showPositionScores: false,
          includeArchived: false,
        }}
      />
    </div>
  );
}
