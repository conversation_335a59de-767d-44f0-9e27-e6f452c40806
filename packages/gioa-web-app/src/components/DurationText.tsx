import React from 'react';
import {Text} from "@/src/components/Text.tsx";

export interface DurationTextProps {
  durationHours: number;
  className?: string;
  prefix?: string;
  size?: "sm" | "md" | "lg";
}

export const DurationText: React.FC<DurationTextProps> = ({durationHours, className, prefix, size = "sm"}) => {
  const durationHoursLabel = Math.floor(durationHours);
  const durationMinutesLabel = Math.floor((durationHours * 60) % 60);
  const mainTextSize = size;
  const secondaryTextSize = size === "sm"
    ? "xs"
    : size === "md"
      ? "sm"
      : "md";

  return (
    <Text size={mainTextSize} className={className} center asChild={"span"}>
      {prefix ?? ""}{durationHoursLabel}<Text size={secondaryTextSize} className={className} asChild={"span"}>h</Text>
      {durationMinutesLabel > 0 ? " " + durationMinutesLabel : null}<Text size={secondaryTextSize} asChild={"span"}
                                                                          className={className}>{durationMinutesLabel > 0 ? "m" : ""}</Text>
    </Text>
  );
}
