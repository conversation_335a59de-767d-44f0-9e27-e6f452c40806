import React, {useState} from 'react';
import {Tooltip, TooltipContent, TooltipProvider, TooltipTrigger} from "@/src/components/ui/tooltip.tsx";
import {DerivedMetrics} from "../../../api/src/scheduling/metrics/metrics.util.ts";
import {TooltipPortal} from '@radix-ui/react-tooltip';
import {BarChart, Clock, DollarSign, Percent, TrendingUp, Users} from 'lucide-react';
import {to12HourTime} from "../../../api/src/date.util.ts";
import {formatCurrency, formatFixed, formatPercent} from "../../../common/src/dataFormatters.ts";

export interface ForecastTimelineColumnProps {
  metrics: DerivedMetrics;
  rounding: boolean;
  hour24: string;
}

export const ForecastTimelineColumn: React.FC<ForecastTimelineColumnProps> = React.memo(({
                                                                                           rounding,
                                                                                           metrics,
                                                                                           hour24
                                                                                         }) => {
  const {projectedRevenue} = metrics;
  const [hoveredMetric, setHoveredMetric] = useState<string>();

  const getHoveredMetricContent = () => {
    if (!hoveredMetric) return null;
    const time = to12HourTime(hour24, true);

    const metricData = {
      sales: {
        label: `Projected Revenue (${time})`,
        value: formatCurrency(projectedRevenue),
        icon: <DollarSign size={16} className="text-emerald-500"/>,
        description: "Forecasted sales revenue"
      },
      laborCost: {
        label: `Labor Cost (${time})`,
        value: formatCurrency(metrics.laborCost),
        icon: <Users size={16} className="text-blue-500"/>,
        description: "Labor hours × Pay Rate"
      },
      actualProductivity: {
        label: `Productivity $/hr (${time})`,
        value: formatCurrency(metrics.actualProductivity),
        icon: <TrendingUp size={16} className="text-purple-500"/>,
        description: "Projected Revenue ÷ Labor Hours"
      },
      productivityPercentage: {
        label: `Productivity % (${time})`,
        value: formatPercent(metrics.productivityPercentage, 2),
        icon: <BarChart size={16} className="text-amber-500"/>,
        description: "Projected Revenue ÷ Labor Cost"
      },
      totalLaborHours: {
        label: `Total Labor Hours (${time})`,
        value: formatFixed(metrics.totalLaborHours, 2),
        icon: <Clock size={16} className="text-indigo-500"/>,
        description: "Sum of all shift time"
      },
      laborPercentage: {
        label: `Labor % (${time})`,
        value: formatPercent(metrics.laborPercentage, 2),
        icon: <Percent size={16} className="text-rose-500"/>,
        description: "Labor Cost ÷ Projected Revenue"
      }
    };

    return metricData[hoveredMetric as keyof typeof metricData];
  }

  const hoveredMetricContent = getHoveredMetricContent();
  return (
    <TooltipProvider delayDuration={0}>
      <Tooltip>
        <TooltipTrigger asChild>
          <div className={"flex flex-col overflow-hidden text-right"}>
            <div className={"py-1 px-2 mt-1 hover:bg-primary-100 "}
                 onMouseEnter={() => setHoveredMetric("sales")}
                 onMouseLeave={() => setHoveredMetric(undefined)}>
              {formatCurrency(projectedRevenue, rounding)}
            </div>
            <div className={"py-1 px-2 hover:bg-primary-100 "}
                 onMouseEnter={() => setHoveredMetric("laborCost")}
                 onMouseLeave={() => setHoveredMetric(undefined)}>
              {formatCurrency(metrics.laborCost)}
            </div>
            <div className={"py-1 px-2 hover:bg-primary-100 "}
                 onMouseEnter={() => setHoveredMetric("actualProductivity")}
                 onMouseLeave={() => setHoveredMetric(undefined)}>
              {formatCurrency(metrics.actualProductivity, rounding)}
            </div>
            <div className={"py-1 px-2 hover:bg-primary-100 "}
                 onMouseEnter={() => setHoveredMetric("productivityPercentage")}
                 onMouseLeave={() => setHoveredMetric(undefined)}>
              {formatPercent(metrics.productivityPercentage, 2)}
            </div>
            <div className={"py-1 px-2 hover:bg-primary-100 "}
                 onMouseEnter={() => setHoveredMetric("totalLaborHours")}
                 onMouseLeave={() => setHoveredMetric(undefined)}>
              {formatFixed(metrics.totalLaborHours, 2)}hr
            </div>
            <div className={"py-1 px-2 mb-1 hover:bg-primary-100 "}
                 onMouseEnter={() => setHoveredMetric("laborPercentage")}
                 onMouseLeave={() => setHoveredMetric(undefined)}>
              {formatPercent(metrics.laborPercentage, 2)}
            </div>
          </div>
        </TooltipTrigger>
        <TooltipPortal>
          {hoveredMetric && hoveredMetricContent ? (
            <TooltipContent side="right"
                            className="max-w-xs animate-in fade-in-50 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95">
              <div className="flex flex-col gap-1">
                <div className="flex items-center gap-2">
                  {hoveredMetricContent.icon}
                  <span className="font-medium text-sm">{hoveredMetricContent.label}</span>
                </div>
                <div className="text-lg font-semibold">{hoveredMetricContent.value}</div>
                <div className="text-xs text-gray-500">{hoveredMetricContent.description}</div>
              </div>
            </TooltipContent>
          ) : null}
        </TooltipPortal>
      </Tooltip>
    </TooltipProvider>
  );
});
