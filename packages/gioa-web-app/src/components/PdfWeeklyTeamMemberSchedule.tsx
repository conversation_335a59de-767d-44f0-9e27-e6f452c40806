import React from 'react';
import {Document, Page, PDFViewer, StyleSheet, Text, View} from '@react-pdf/renderer';
import {filter, groupBy, sortBy} from 'lodash';
import {ScheduleDto} from "../../../api/src/scheduleSchemas.ts";
import {
  getDatesInRange,
  getIsoWeekDateTimeRangeInTimezone,
  timeToMinutes,
  to12HourTime
} from "../../../api/src/date.util.ts";
import {SchedulePersonDto} from '@gioa/api/src/schedulePersonDto.ts';
import {Table, TD, TR} from '@ag-media/react-pdf-table';

const styles = StyleSheet.create({
  page: {
    padding: 30,
    fontSize: 7,
  },
  header: {
    marginBottom: 10,
    fontSize: 14,
    color: '#374151',
  },
  cell: {
    borderCollapse: 'collapse',
    paddingVertical: 2,
    paddingLeft: 2,
    paddingRight: 1,
    borderColor: '#bbb',
  },
  nameCell: {
    backgroundColor: '#FFFFFF',
    borderTopColor: "#fff",
    borderLeftColor: "#bbb"
  },
  alternateColumn: {
    backgroundColor: '#F3F4F6',
  },
  headerRow: {
    backgroundColor: '#FFFFFF',
    fontWeight: 'bold',
  },
  shiftInfo: {
    fontSize: 7,
    color: '#363636',
  },
  areaTitle: {
    paddingVertical: 1,
    paddingHorizontal: 2,
    borderRadius: 2,
    backgroundColor: '#e8e8e8',
  },
  totalHours: {
    fontSize: 7,
    color: '#6B7280',
  },
});

interface Shift {
  dayOfWeek: number;
  range: { start: string; end: string };
  areaTitle: string;
  isShiftLead: boolean;
}

interface PersonSchedule {
  person: SchedulePersonDto;
  shiftsByDay: Record<number, Shift[]>;
  maxShiftsPerDay: number;
  totalHours: number;
}

interface PDFMatrixScheduleProps {
  storeTitle: string;
  schedule: ScheduleDto;
  people: SchedulePersonDto[];
}

export const PDFMatrixSchedule: React.FC<PDFMatrixScheduleProps> = ({
                                                                      storeTitle,
                                                                      schedule,
                                                                      people,
                                                                    }) => {
  const {start, end} = getIsoWeekDateTimeRangeInTimezone({
    week: schedule.published!.week,
    timezone: schedule.timezone ?? null
  });

  const weekDates = filter(
    getDatesInRange(start, end),
    d => d.weekday !== 7
  );

  const personSchedules: PersonSchedule[] = people.map(person => {
    const shifts: Shift[] = [];
    let totalHours = 0;

    if (schedule.published?.days) {
      for (const day of schedule.published.days) {
        for (const area of day.areas) {
          for (const shift of area.shifts) {
            if (shift.assignedPersonId === person.id) {
              const startMinutes = timeToMinutes(shift.range.start);
              const endMinutes = timeToMinutes(shift.range.end);
              totalHours += (endMinutes - startMinutes) / 60;

              shifts.push({
                dayOfWeek: day.dayOfWeek,
                range: shift.range,
                areaTitle: area.title,
                isShiftLead: shift.isShiftLead ?? false,
              });
            }
          }
        }
      }
    }

    const shiftsByDay = groupBy(shifts, 'dayOfWeek');
    Object.values(shiftsByDay).forEach(dayShifts => {
      dayShifts.sort((a, b) =>
        timeToMinutes(a.range.start) - timeToMinutes(b.range.start)
      );
    });

    return {
      person,
      shiftsByDay,
      maxShiftsPerDay: Math.max(...Object.values(shiftsByDay).map(s => s.length), 0),
      totalHours,
    };
  });

  const sortedSchedules = sortBy(personSchedules, [
    s => s.person.lastName.toLowerCase(),
    s => s.person.firstName.toLowerCase()
  ]);

  const isAlternateColumn = (index: number) => index % 2 === 0;

  return (
    <PDFViewer style={{width: '100%', height: '100vh'}}>
      <Document title={`${storeTitle} - Week ${schedule.published?.week.week}`}>
        <Page size="LETTER" style={styles.page}>
          <Text style={styles.header}>
            {storeTitle} - Week {schedule.published?.week.week}
          </Text>

          <Table>
            {/* Header Row */}
            <TR style={styles.headerRow}>
              <TD style={{...styles.cell}}></TD>
              {weekDates.map((date, idx) => (
                <TD
                  key={date.toFormat('yyyy-MM-dd')}
                  style={{
                    ...styles.cell,
                    ...(isAlternateColumn(idx) ? styles.alternateColumn : {})
                  }}
                >
                  <Text>{date.toFormat('EEE M/d')}</Text>
                </TD>
              ))}
            </TR>

            {/* People Rows */}
            {sortedSchedules.map(({person, shiftsByDay, maxShiftsPerDay, totalHours}) => (
              <View wrap={false} key={person.id}>
                {totalHours === 0 ? (
                  <TR key={`${person.id}`}>
                    <TD style={styles.cell}>
                      <View style={{flexDirection: 'column'}}>
                        <Text style={{fontWeight: 'bold', fontSize: 7}}>
                          {person.lastName}, {person.firstName}
                        </Text>
                        <Text style={styles.totalHours}>0.0 hrs</Text>
                      </View>
                    </TD>
                    {weekDates.map((date, idx) => (
                      <TD
                        key={date.toFormat('yyyy-MM-dd')}
                        style={{
                          ...styles.cell,
                          ...(isAlternateColumn(idx) ? styles.alternateColumn : {})
                        }}
                      />
                    ))}
                  </TR>
                ) : (
                  [...Array(maxShiftsPerDay)].map((_, shiftIndex) => (
                    <TR key={`${person.id}-${shiftIndex}`}>
                      <TD style={{
                        ...styles.cell,
                        ...(shiftIndex === 0 ? {} : styles.nameCell)
                      }}>
                        {shiftIndex === 0 ? (
                          <View style={{flexDirection: 'column'}}>
                            <Text style={{fontWeight: 'bold', fontSize: 7}}>
                              {person.lastName}, {person.firstName}
                            </Text>
                            <Text style={styles.totalHours}>
                              {totalHours.toFixed(1)} hrs
                            </Text>
                          </View>
                        ) : null}
                      </TD>

                      {weekDates.map((date, idx) => {
                        const dayShifts = shiftsByDay[date.weekday] || [];
                        const shift = dayShifts[shiftIndex];

                        return (
                          <TD
                            key={date.toFormat('yyyy-MM-dd')}
                            style={{
                              ...styles.cell,
                              ...(isAlternateColumn(idx) ? styles.alternateColumn : {})
                            }}
                          >
                            {shift && (
                              <View style={{flexDirection: 'column', gap: 2}}>
                                <View style={{flexDirection: 'row', gap: 2}}>
                                  <Text style={[styles.shiftInfo, styles.areaTitle]}>
                                    {shift.areaTitle}
                                  </Text>
                                  {shift.isShiftLead ?
                                    <Text style={[styles.shiftInfo, styles.areaTitle, {
                                      backgroundColor: '#a8a8a8',
                                      color: '#fff'
                                    }]}>
                                      L
                                    </Text> : null}
                                </View>
                                <Text style={styles.shiftInfo}>
                                  {to12HourTime(shift.range.start)} - {to12HourTime(shift.range.end)}
                                </Text>
                              </View>
                            )}
                          </TD>
                        );
                      })}
                    </TR>
                  ))
                )}
              </View>
            ))}
          </Table>
        </Page>
      </Document>
    </PDFViewer>
  );
};
