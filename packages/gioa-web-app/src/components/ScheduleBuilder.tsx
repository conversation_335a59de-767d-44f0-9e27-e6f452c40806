import React, {MutableRefObject, SetStateAction, useCallback, useEffect, useMemo, useRef, useState} from 'react';
import {
  clamp,
  filter,
  find,
  findIndex,
  flatMap,
  flatten,
  forEach,
  groupBy,
  includes,
  isEmpty,
  isEqual,
  isNil,
  map,
  omitBy,
  reduce,
  sortBy,
  sumBy,
  times
} from "lodash";
import {useLocalStorage} from "usehooks-ts";
import {ShiftRow} from "@/src/components/ShiftRow.tsx";
import {Sheet, SheetContent} from "@/src/components/ui/sheet.tsx";
import {Button, buttonVariants} from "@/src/components/ui/button.tsx";
import {
  ArrowLeftIcon,
  ArrowRightIcon,
  ChevronDownIcon,
  MinusIcon,
  PlusIcon,
  ZoomInIcon,
  ZoomOutIcon
} from "lucide-react";
import {produce} from "immer";
import {BlankRow} from "@/src/components/BlankRow.tsx";
import {ShiftDetailsSheetContent} from "@/src/components/ShiftDetailsSheetContent.tsx";
import {
  Menubar,
  MenubarCheckboxItem,
  MenubarContent,
  MenubarMenu,
  MenubarShortcut,
  MenubarTrigger,
} from "@/src/components/ui/menubar"
import {ScheduleSidebarRow} from "@/src/components/ScheduleSidebarRow.tsx";
import {ScheduleTimeline} from "@/src/components/ScheduleTimeline.tsx";
import {ScheduleAddArea} from "@/src/components/ScheduleAddArea.tsx";
import {ScheduleEditAreas} from "@/src/components/ScheduleEditAreas.tsx";
import {useDisclosure} from "@/src/hooks/useDisclosure.tsx";
import {genShiftId, PersonDto, StoreAreaDto} from "../../../api/src/schemas.ts";
import {
  Activity,
  DayPart,
  DraftSchedule,
  HawaiiACAPersonHours,
  ScheduleDto,
  ScheduleMetricsInput,
  ShiftArea,
  ShiftOfferDto
} from "../../../api/src/scheduleSchemas.ts";
import {addToTime, getRangeDurationHours, sanitizeTimeRange, to12HourTime} from "../../../api/src/date.util.ts";
import {updateScheduleShiftRange} from "../../../api/src/scheduling.ts";
import {assignPersonToShift, getShift, getShiftArea} from "../../../api/src/scheduling.util.ts";
import {Helmet} from "react-helmet";
import {daysOfWeek} from "@/src/components/AvailabilityWeek.tsx";
import {ScheduleDayMenu} from "@/src/components/ScheduleDayMenu.tsx";
import {Link, useNavigate} from "@tanstack/react-router";
import {FloatingPanel} from "@/src/components/FloatingPanel.tsx";
import {getMetrics} from "../../../api/src/scheduling/metrics/metrics.util.ts";
import {api} from "@/src/api.ts";
import {DailyTimeRange, DayOfWeek, IsoWeekDate, TimeOfDay} from "../../../api/src/timeSchemas.ts";
import {MenubarItem, MenubarSeparator} from "@/src/components/ui/menubar.tsx";
import {ScheduleTemplateSheet} from "@/src/components/ScheduleTemplateSheet.tsx";
import {EditPeriodsDialog} from "@/src/components/EditPeriodsDialog.tsx";
import {EditStoreHoursDialog} from "@/src/components/EditStoreHoursDialog.tsx";
import {toast} from "sonner";
import {EditPeakHoursDialog} from "@/src/components/EditPeakHoursDialog.tsx";
import {CopyDayDialog} from "@/src/components/CopyDayDialog.tsx";
import {ScheduleValidationSheet} from "@/src/components/ScheduleValidationSheet.tsx";
import {
  ScheduleValidationResult,
  ScheduleValidationSettings,
  ValidationMessage
} from "../../../api/src/scheduleValidation.types.ts";
import {Toggle} from "@/src/components/ui/toggle.tsx";
import {usePrevious} from "react-use";
import {DocumentSaveStatus, SaveStatus} from "@/src/components/DocumentSaveStatus.tsx";
import {useUndoStack} from "@/src/hooks/useUndoStack.tsx";
import {HotkeyLabel} from "@/src/components/HotkeyLabel.tsx";
import {useHotkeys} from "react-hotkeys-hook";
import {EditScheduleNotesDialog} from "@/src/components/EditScheduleNotesDialog.tsx";
import {ScheduleEditAreaDialog} from "@/src/components/ScheduleEditAreaDialog.tsx";
import {cn} from '../util.ts';
import {useIgnoreScheduleValidationMessages} from "@/src/hooks/useIgnoreScheduleValidationMessages.tsx";
import {PublishDayDialog} from "@/src/components/PublishDayDialog.tsx";
import {ScheduleEventDto} from '@gioa/api/src/scheduleEventSchemas.ts';
import {Separator} from "@/src/components/ui/separator.tsx";
import {scheduleBuilderMachine} from "@/src/components/ScheduleBuilder.machine.tsx";
import {useScheduleBuilderMachineUIState} from "@/src/hooks/useScheduleBuilderMachineUIState.tsx";
import {SchedulePersonClientDto, SchedulePersonDto} from "../../../api/src/schedulePersonDto.ts";
import {rightPanelWidth, rowHeight} from "@/src/components/ScheduleBuilder.util.tsx";
import {
  AreaSettings,
  FocusOutsideEvent,
  genShifts,
  getDayPartsCssBackground,
  incrementTo24HourTime,
  PointerDownOutsideEvent,
  ScheduleArea,
  ScheduleBuilderEvent,
  ScheduleRowInfo,
  storeTimeToIncrement
} from "../../../api/src/scheduleBuilder.util.ts";
import {applyTemplateToSchedule, copyDay} from "../../../api/src/scheduleTemplates.ts";
import {AreaRow} from "@/src/components/AreaRow.tsx";
import {DateTime, WeekdayNumbers} from 'luxon';
import {
  isMessageForWeekday,
  isMessageIgnorable
} from "../../../api/src/scheduleValidation/scheduleValidationMessagesL2.ts";
import {SegmentedControl} from "@/src/components/SegmentedControl.tsx";
import {constructShift} from "../../../api/src/shift.ts";
import {appendShiftToArea, getDayShifts} from "../../../api/src/schedule.ts";
import {Actor, StateFrom} from 'xstate';
import {ScheduleForecastTimeline} from "@/src/components/ScheduleForecastTimeline.tsx";
import {getForecastTimelineHeight, ScheduleForecastSide} from "@/src/components/ScheduleForecastSide.tsx";
import {Checkbox} from "@/src/components/ui/checkbox.tsx";
import {
  getAveragePayRate
} from '@gioa/api/src/scheduling/metrics/scheduleHourlySalesForecast/scheduleHourlySalesForecast.ts';
import {fromSchedulePeople} from "../../../api/src/scheduling/metrics/payRates/payRates.ts";
import {SchedulePresenceIndicator} from "@/src/components/SchedulePresenceIndicator.tsx";
import * as SP from "../../../api/src/setupSheets/storeSetupPositions/storeSetupPositions.ts";
import {SetupActivitiesRow} from "@/src/components/SetupActivitiesRow.tsx";
import {ShiftSetupActivity} from "@/src/components/ScheduleShiftActivity.tsx";


export interface ScheduleBuilderProps {
  routeFullPath: string;
  dayOfWeek: number;
  storeId: string;
  scheduleId: string;
  shiftId?: string;
  cameFromWeeklyView: boolean;
  people: SchedulePersonClientDto[];
  scheduleRev: ScheduleDto;
  schedule: DraftSchedule;
  shiftOffers: ShiftOfferDto[];
  onScheduleChange: (schedule: SetStateAction<DraftSchedule>, hints: { weekday: DayOfWeek | undefined }) => void;
  snapshot: StateFrom<typeof scheduleBuilderMachine>;
  send: Actor<typeof scheduleBuilderMachine>["send"];
  storeAreas: StoreAreaDto[];
  settings: ScheduleValidationSettings;
  isPublishedWithDraft: boolean;
  hasDraft: boolean;
  storeTitle: string;
  savingStatus: SaveStatus;
  canViewPayRates: boolean;
  isForecastingEnabled: boolean;
  currentPersonId: string;
  isPresenceEnabled: boolean;

  onDeleteShift: (shift: ScheduleRowInfo) => void;
  onDuplicateShift: (shift: ScheduleRowInfo) => void;
  onShiftUpdated: (shiftId: string, values: {
    start: TimeOfDay;
    end: TimeOfDay;
    assignedPersonId: string | undefined;
    description: string | undefined;
    isShiftLead: boolean;
    storePositionId: string | undefined;
    shiftAreaId: string;
  }) => void;
  onShiftActivitiesUpdated: (shiftId: string, activities: Activity[]) => void;
  onDiscardDraft: () => void;
  validationResult: ScheduleValidationResult;

  onChangeView: (view: string) => void;
  latestServerVersion: MutableRefObject<number>;

  localRawEvents: ScheduleEventDto[];
  setLocalRawEvents: (events: SetStateAction<ScheduleEventDto[]>) => void;
  eventsForToday: ScheduleBuilderEvent[];
  scheduleEvents: ScheduleBuilderEvent[];
  weeksHoursMap: HawaiiACAPersonHours;
  storeState?: string;
}

const defaultIncrementWidth = 32;

export const ScheduleBuilder: React.FC<ScheduleBuilderProps> = (props) => {
  const {
    onDeleteShift, savingStatus,
    onDuplicateShift, shiftOffers,
    onShiftUpdated, eventsForToday, scheduleEvents,
    onShiftActivitiesUpdated,
    onDiscardDraft, canViewPayRates,
    validationResult, currentPersonId,
    onChangeView, settings,
    scheduleRev, isPublishedWithDraft, hasDraft,
    schedule, isForecastingEnabled,
    onScheduleChange,
    snapshot, localRawEvents, setLocalRawEvents,
    send, storeTitle,
    storeAreas, latestServerVersion,
    weeksHoursMap,
    storeState
  } = props;
  const payRates = useMemo(() => fromSchedulePeople(props.people), [props.people]);

  const positionCoverageView = useDisclosure();

  const hasForecast = Boolean(scheduleRev.forecast);
  const [isForecastOpen, setIsForecastOpen] = useLocalStorage<boolean>("gioanation_scheduleForecastOpen", true, {
    initializeWithValue: true
  });
  const navigate = useNavigate({from: props.routeFullPath as any});
  const dayOfWeek = props.dayOfWeek;
  const [zoom, setZoom] = useLocalStorage<number>("gioanation_scheduleZoom", 1, {
    initializeWithValue: true
  });
  const [countOpenShifts, setCountOpenShifts] = useState<boolean>(false);

  const sched = props.scheduleRev;
  const people = props.people;
  const peopleInfo = useMemo(() => {
    return reduce(people, (acc, p) => {
      acc[p.id!] = p;
      return acc;
    }, {} as Record<string, PersonDto>);
  }, [people]);

  const timezone = sched.timezone;
  const initAreaSettings: AreaSettings = {};
  const [areaSettings, setAreaSettings] = useLocalStorage<AreaSettings>("gioanation_scheduleAreaSettings", initAreaSettings, {
    serializer: (value) => JSON.stringify(value),
    deserializer: (value) => JSON.parse(value),
    initializeWithValue: true
  });

  const {
    isTeamMembersMenuOpen,
    isMetricsMenuOpen,
    selectedShiftId,
    selectedEventId,
    isQuickFillingShift,
  } = useScheduleBuilderMachineUIState(snapshot);

  const undoStack = useUndoStack(dayOfWeek, schedule.days);
  const isUndoRedoChange = useRef(false);

  const onUndo = () => {
    const undoneDay = undoStack.onUndo();
    if (undoneDay) {
      isUndoRedoChange.current = true;
      onScheduleChange(sched => {
        return produce(sched, schedDraft => {
          const dayIdx = findIndex(schedDraft.days, d => d.dayOfWeek === dayOfWeek);
          if (dayIdx !== -1) {
            schedDraft.days[dayIdx] = undoneDay;
          }
        })
      }, {weekday: dayOfWeek})
    }
  }

  const onRedo = () => {
    const redoneDay = undoStack.onRedo();
    if (redoneDay) {
      isUndoRedoChange.current = true;
      onScheduleChange(sched => {
        return produce(sched, schedDraft => {
          const dayIdx = findIndex(schedDraft.days, d => d.dayOfWeek === dayOfWeek);
          if (dayIdx !== -1) {
            schedDraft.days[dayIdx] = redoneDay;
          }
        })
      }, {weekday: dayOfWeek})
    }
  }

  const [isTemplateSheetOpen, setIsTemplateSheetOpen] = useState(false);
  const onTemplateSheetOpenChange = (open: boolean) => {
    setIsTemplateSheetOpen(open);
  }

  const day = find(schedule.days, d => d.dayOfWeek === dayOfWeek)!;
  const dayAreas = useMemo(() => {
    return map(day.areas, (a): ScheduleArea => {
      return {
        id: a.id,
        title: a.title,
        description: a.description,
        countsTowardsLabor: a.countsTowardsLabor,
        storeAreaId: a.storeAreaId || undefined,
      }
    })
  }, [day])

  const selectedScheduleShift = useMemo(() => {
    return getShift(schedule, selectedShiftId);
  }, [selectedShiftId, schedule]);

  const areaSettingsMemo = useMemo(() => {
    const selectedShiftId = selectedScheduleShift?.id;
    if (!selectedShiftId) return areaSettings;

    const selectedShiftArea = getShiftArea(schedule, selectedShiftId);
    if (!selectedScheduleShift || !selectedShiftArea) {
      return areaSettings;
    }

    // if a shift is selected in an area that we have collapsed, then expand that area temporarily
    const shiftAreaSettings = areaSettings[selectedShiftArea.title];
    if (shiftAreaSettings?.isCollapsed) {
      return produce(areaSettings, draft => {
        draft[selectedShiftArea.title].isCollapsed = false;
      });
    }

    return areaSettings;
  }, [areaSettings, selectedScheduleShift, schedule]);

  const dayShifts = useMemo(() => {
    return getDayShifts(schedule, dayOfWeek);
  }, [schedule, dayOfWeek])

  const setupPositionToActivities = useMemo(() => {
    return reduce(dayShifts, (posToActs, shift) => {
      for (const act of shift.activities) {
        if (act.activityType === "setups" && act.setupPositionTitle) {
          const person = shift.assignedPersonId ? peopleInfo[shift.assignedPersonId] : undefined;
          posToActs[act.setupPositionTitle] ??= [];
          posToActs[act.setupPositionTitle].push({...act, shift, assignedTo: person})
        }
      }

      return posToActs;
    }, {} as Record<string, Array<ShiftSetupActivity>>);
  }, [dayShifts, peopleInfo])

  const [storeSetupPositions] = api.setups.getStoreSetupPositions.useSuspenseQuery({storeId: props.storeId})
  const setupPositionsByArea = useMemo(() => {
    const positions = SP.toArray({
      storeSetupPositions: storeSetupPositions,
      storeAreas: props.storeAreas
    });

    return groupBy(positions, pos => pos.areaTitle || "(no area)");
  }, [storeSetupPositions, props.storeAreas]);

  const shifts = useMemo(() => {
    const positions = flatMap(storeAreas, a => a.positions);
    return genShifts({
      draft: schedule,
      published: scheduleRev.published,
      people: people,
      dayOfWeek: dayOfWeek,
      positions: positions,
      areaSettings: areaSettingsMemo,
      shiftOffers: shiftOffers,
      countOpenShiftsTowardsLabor: countOpenShifts
    });
  }, [schedule, scheduleRev, people, dayOfWeek, countOpenShifts, storeAreas, shiftOffers, areaSettingsMemo]);
  const selectedShift = find(shifts, s => s.id === selectedShiftId);

  const scrollContainerRef = React.useRef<HTMLDivElement>(null);
  const [isEventsOpen, setIsEventsOpen] = useState(true);
  const [isAvailabilityShown, setIsAvailabilityShown] = useState(false);
  const [isAreaHeatmapShown, setIsAreaHeatmapShown] = useState(true);
  const [isPeakHoursOpen, setIsPeakHoursOpen] = useState(true);
  const [arePeriodsVisible, setArePeriodsVisible] = useState(true);
  const editPeriods = useDisclosure();
  const editStoreHours = useDisclosure();
  const editPeakHours = useDisclosure();
  const editNotes = useDisclosure();
  const copy = useDisclosure();
  const activitiesView = useDisclosure();

  const incrementWidth = defaultIncrementWidth * zoom;
  const gridLineInterval = incrementWidth * 4;

  const onShiftEndChange = useCallback((areaId: string, shiftId: string, endIncrement: number) => {
    unignoreAllMessages(shiftId);

    onScheduleChange(sched => {
      const shift = getShift(sched, shiftId);
      if (!shift) {
        return sched;
      }
      const end = incrementTo24HourTime(sched.storeHours, endIncrement);
      return updateScheduleShiftRange(sched, {
        shiftId,
        range: sanitizeTimeRange({
          start: shift.range.start,
          end: end
        })
      });
    }, {weekday: dayOfWeek})
  }, [dayOfWeek]);

  const onShiftStartChange = useCallback((areaId: string, shiftId: string, startIncrement: number) => {
    unignoreAllMessages(shiftId);

    onScheduleChange(sched => {
      const shift = getShift(sched, shiftId);
      if (!shift) {
        return sched;
      }
      const start = incrementTo24HourTime(sched.storeHours, startIncrement);
      return updateScheduleShiftRange(sched, {
        shiftId,
        range: sanitizeTimeRange({
          start: start,
          end: shift.range.end
        })
      });
    }, {weekday: dayOfWeek})
  }, [dayOfWeek]);

  const onShiftMoved = useCallback((areaId: string, shiftId: string, startIncrement: number, endIncrement: number) => {
    unignoreAllMessages(shiftId);

    onScheduleChange(sched => {
      return updateScheduleShiftRange(sched, {
        shiftId,
        range: sanitizeTimeRange({
          start: incrementTo24HourTime(sched.storeHours, startIncrement),
          end: incrementTo24HourTime(sched.storeHours, endIncrement)
        })
      });
    }, {weekday: dayOfWeek})
  }, [dayOfWeek]);

  const scrollShiftIntoView = useCallback((shiftId: string) => {
    // scroll the shift into view if the panel slides over it
    // get the shift element by data-id
    const shiftElement = document.querySelector(`[data-id="${shiftId}"]`);
    if (!shiftElement) {
      return;
    }

    const container = scrollContainerRef.current;
    if (!container) {
      return;
    }
    const containerRect = container.getBoundingClientRect();
    const elementRect = shiftElement.getBoundingClientRect();
    const startOffsetX = leftSidebarRef.current?.clientWidth || 0;
    const endOffsetX = rightPanelWidth;
    const startOffsetY = topbarHeight;

    const isElementInViewHorizontally =
            elementRect.left >= containerRect.left + startOffsetX &&
            elementRect.right <= containerRect.right - endOffsetX;

    const isElementInViewVertically =
            elementRect.top >= containerRect.top + startOffsetY &&
            elementRect.bottom <= containerRect.bottom;

    if (!isElementInViewHorizontally || !isElementInViewVertically) {
      const scrollLeft = !isElementInViewHorizontally
              ? elementRect.left - containerRect.left + container.scrollLeft - startOffsetX
              : container.scrollLeft;

      const scrollTop = !isElementInViewVertically
              ? elementRect.top - containerRect.top + container.scrollTop - startOffsetY
              : container.scrollTop;

      container.scrollTo({
        left: scrollLeft,
        top: scrollTop,
        behavior: 'smooth'
      });
    }
  }, []);

  const onClickAreaSidebarRow = (areaId: string) => {
    const area = find(day.areas, a => a.id === areaId);
    if (area) {
      setEditingArea(area);
    }
  }

  const onShiftAdded = useCallback((areaId: string) => {
    const newShiftId = genShiftId();
    onScheduleChange(sched => {
      return appendShiftToArea(sched, areaId, constructShift({
        title: "New shift",
        id: newShiftId,
        range: {
          start: sched.storeHours.start,
          end: addToTime(sched.storeHours.start, 4, "hours")
        },
        shiftAreaId: areaId,
        storePositionId: undefined,
        assignedPersonId: undefined,
        isShiftLead: false
      }))
    }, {weekday: dayOfWeek})

    send({type: "shiftAdded", shiftId: newShiftId});

    setTimeout(() =>
            scrollShiftIntoView(newShiftId));
  }, [dayOfWeek]);

  const onShiftOpened = useCallback((areaId: string, shiftId: string) => {
    const shiftToOpenId = shiftId;
    send({type: "shiftOpened", shiftId});

    scrollShiftIntoView(shiftToOpenId);
  }, [dayOfWeek]);

  const onInteractOutside = (e: PointerDownOutsideEvent | FocusOutsideEvent) => {
    // if the target is a shift or team member button, then don't close the sheet
    const target = e.target as HTMLElement;
    if (target.closest(".gioa-shift, .gioa-team-member-select")) {
      e.preventDefault();
      return;
    }
  }

  const numIncrements = storeTimeToIncrement(schedule.storeHours, schedule.storeHours.end);
  const incrementsContainerWidth = incrementWidth * numIncrements + rightPanelWidth;

  const leftSidebarRef = React.useRef<HTMLDivElement>(null);
  const onShiftDetailsOpenChanged = (isOpen: boolean) => {
    if (!isOpen) {
      send({type: "shiftClosed"});
      setSelectedActivityId(undefined);

      // if they've closed a shift, and the right side is in the blank space to the right of the schedule, scroll back
      const scrollingElem = scrollContainerRef.current;
      if (!scrollingElem) {
        return;
      }

      const leftBarWidth = leftSidebarRef.current?.clientWidth || 0;
      const distToEnd = incrementsContainerWidth - scrollingElem.clientWidth - (scrollingElem.scrollLeft - leftBarWidth);
      const distToMove = rightPanelWidth - distToEnd;
      if (distToMove > 0) {
        scrollingElem.scrollBy({
          left: -distToMove,
          behavior: "smooth"
        });
      }
    }
  }

  const timelineHeight = 48
  const eventsHeight = rowHeight;
  const topbarHeight = timelineHeight + (isEventsOpen && !isEmpty(eventsForToday) ? eventsHeight : 0);

  const onFillOpenShifts = (shift: ScheduleRowInfo) => {
    send({type: "sidebarShiftOpened", shiftId: shift.id, assignedPersonId: shift.assignedTo?.id});
    scrollShiftIntoView(shift.id);
  }

  const onTeamMemberSelected = useCallback((person: SchedulePersonDto) => {
    if (!selectedShiftId) {
      return;
    }

    // assign the shift
    onScheduleChange(sched => {
      return assignPersonToShift(sched, {shiftId: selectedShiftId, personId: person.id});
    }, {weekday: dayOfWeek});

    send({type: "teamMemberAssigned", assignedPersonId: person.id});

    const shouldGoToNext = isQuickFillingShift;
    if (shouldGoToNext) {
      // go to next open shift
      let nextShiftIndex = shifts.findIndex(s => s.id === selectedShiftId) + 1;
      let nextShift = shifts[nextShiftIndex];
      while ((nextShift?.type !== "shift" || nextShift?.assignedTo) && nextShiftIndex < shifts.length) {
        nextShiftIndex++;
        nextShift = shifts[nextShiftIndex];
      }

      if (nextShift) {
        send({type: "sidebarShiftOpened", shiftId: nextShift.id, assignedPersonId: nextShift.assignedTo?.id});
        scrollShiftIntoView(nextShift.id);
      }
    }
  }, [selectedShiftId, isQuickFillingShift, shifts, dayOfWeek]);

  const bgStyle = {
    width: incrementsContainerWidth,
    // height,
    minHeight: `calc(100% - ${topbarHeight + getForecastTimelineHeight()}px)`,
    backgroundImage: `
    repeating-linear-gradient(
      to right,
      transparent,
      transparent ${gridLineInterval - 1}px,
      rgba(0,0,0,0.1) ${gridLineInterval - 1}px,
      rgba(0,0,0,0.1) ${gridLineInterval}px
    )
  `,
    backgroundSize: '100% 100%',
    backgroundPosition: '0 0',
  };

  const onClickTeamMember = useCallback((shift: ScheduleRowInfo) => {
    onFillOpenShifts(shift);
  }, []);

  const addArea = useDisclosure();
  const onAddArea = (area: ScheduleArea) => {
    onScheduleChange(sched => {
      return produce(sched, draftSched => {
        const day = find(draftSched.days, d => d.dayOfWeek === dayOfWeek);
        day?.areas.push({
          title: area.title,
          id: area.id,
          description: area.description,
          countsTowardsLabor: area.countsTowardsLabor,
          storeAreaId: area.storeAreaId || undefined,
          shifts: []
        });
      });
    }, {weekday: dayOfWeek});
    addArea.onClose();
    toast.success("Area added", {closeButton: true});
  }

  const dayOfWeekObj = find(daysOfWeek, d => d.dayOfWeek === dayOfWeek);
  const onChangeDayOfWeek = (newDayOfWeek: number) => {
    navigate({
      search: {
        dayOfWeek: newDayOfWeek
      } as any
    })
    send({type: "dayChanged"});
  }

  const onGoPrevDay = () => {
    const prevDayOfWeek = clamp(dayOfWeek - 1, 1, 6);
    onChangeDayOfWeek(prevDayOfWeek);
  }

  const onGoNextDay = () => {
    const nextDayOfWeek = clamp(dayOfWeek + 1, 1, 7);
    onChangeDayOfWeek(nextDayOfWeek);
  }

  const onSaveAreas = (areas: ScheduleArea[]) => {
    onScheduleChange(sched => {
      return produce(sched, draftSched => {
        const day = find(draftSched.days, d => d.dayOfWeek === dayOfWeek)!;
        day.areas = map(areas, (area): ShiftArea => {
          const existingArea = find(day.areas, a => a.id === area.id);
          if (existingArea) {
            return {
              ...existingArea,
              title: area.title,
              description: area.description,
              countsTowardsLabor: area.countsTowardsLabor,
              storeAreaId: area.storeAreaId || undefined
            }
          } else {
            return {
              shifts: [],
              storeAreaId: undefined,
              countsTowardsLabor: area.countsTowardsLabor,
              id: area.id,
              title: area.title,
              description: area.description
            };
          }
        })
      });
    }, {weekday: dayOfWeek})

    toast.success("Areas updated", {closeButton: true});
  }

  const [editingArea, setEditingArea] = useState<ScheduleArea>();
  const onScheduleEditAreaDialogOpenChange = (open: boolean) => {
    if (!open) {
      setEditingArea(undefined);
    }
  }

  const onAreaEdited = (area: ScheduleArea) => {
    onScheduleChange(sched => {
      return produce(sched, draftSched => {
        const day = find(draftSched.days, d => d.dayOfWeek === dayOfWeek)!;
        const existingArea = find(day.areas, a => a.id === area.id);
        if (existingArea) {
          existingArea.title = area.title;
          existingArea.description = area.description;
          existingArea.countsTowardsLabor = area.countsTowardsLabor;
          existingArea.storeAreaId = area.storeAreaId || undefined;
        }
      });
    }, {weekday: dayOfWeek});

    toast.success("Area updated", {closeButton: true});
  }

  const onAreaDeleted = (area: ScheduleArea) => {

  }

  const onOpenEvent = (eventId: string) => {
    send({type: "viewEventDetails", eventId});
  }
  const onViewEventDetails = (event: ScheduleEventDto) => {
    if (!find(localRawEvents, e => e.id === event.id)) {
      onUpsertEvent(event, false);
    }
  }

  const upsertScheduleEventMutation = api.user.upsertScheduleEvent.useMutation();
  const deleteScheduleEventMutation = api.user.deleteScheduleEvent.useMutation();

  const onUpsertEvent = (event: ScheduleEventDto, toastMeBro = true) => {
    upsertScheduleEventMutation.mutate({
      id: event.id,
      storeId: props.storeId,
      title: event.title,
      description: event.description,
      range: event.range,
      eventType: event.eventType,
      visibilityLevel: event.visibilityLevel,
      isTimeOffRestricted: event.isTimeOffRestricted ?? false,
    }, {
      onError: () => {
        alert("An error occurred trying to save your event update to the server. Please try again later.");
      }
    });

    setLocalRawEvents(events => {
      return produce(events, draft => {
        const existingEvent = find(draft, e => e.id === event.id);
        if (existingEvent) {
          existingEvent.title = event.title;
          existingEvent.description = event.description;
          existingEvent.range = event.range;
          existingEvent.eventType = event.eventType;
          existingEvent.visibilityLevel = event.visibilityLevel;
          existingEvent.isTimeOffRestricted = event.isTimeOffRestricted;
        } else {
          draft.push(event);
        }
      });
    })

    if (toastMeBro) {
      toast.success("Event updated", {
        closeButton: true
      });
    }
  }

  const onDeleteEvent = (event: ScheduleEventDto) => {
    deleteScheduleEventMutation.mutate({
      id: event.id
    }, {
      onError: () => {
        alert("An error occurred trying to delete the event on the server. Please try again later.");
      }
    });

    setLocalRawEvents(events => {
      return filter(events, e => e.id !== event.id);
    })
    toast.success("Event deleted", {
      closeButton: true
    });
  }

  const {
    ignoredMessages,
    isMessageIgnored,
    ignoreMessage, unignoreMessage,
    unignoreAllMessages
  } = useIgnoreScheduleValidationMessages({
    scheduleId: props.scheduleId,
    storeId: props.storeId,
  });

  const onApplyTemplateToToday = (template: DraftSchedule, areaIds: string[], shouldFilterAreas: boolean) => {
    onScheduleChange(sched => {
      const templateDay = find(template.days, d => d.dayOfWeek === dayOfWeek);
      const templateAreas = filter(templateDay?.areas, a => includes(areaIds, a.id));
      const templateAreaTitles = map(templateAreas, a => a.title);
      return applyTemplateToSchedule({
        schedule: sched,
        template: template,
        dayOfWeek: dayOfWeek,
        shouldFilterAreas,
        copyShiftAssignments: true,
        includeAreaTitles: templateAreaTitles
      });
    }, {weekday: dayOfWeek})

    toast.success(`Template "${template?.title}" applied to ${dayOfWeekObj?.name}`, {closeButton: true});
  }

  const doZoom = (out: boolean) => setZoom(w => clamp(out ? w - .1 : w + .1, .5, 1.5));
  const zoomPercentage = (zoom * 100);

  const dayPartsBgStyle = {
    background: getDayPartsCssBackground({
      dayParts: schedule.dayParts,
      storeHours: schedule.storeHours,
      incrementWidth,
      offsetX: 16 // offsetX corresponds to the mx-4 that is applied to the timeline container. This makes
      // the day parts line up with the timeline
    })
  };

  const onUpdatePeriods = (dayParts: DayPart[]) => {
    onScheduleChange(sched => {
      return {
        ...sched,
        dayParts: dayParts
      }
    }, {weekday: undefined})
    toast.success("Daily periods updated", {closeButton: true});
    editPeriods.onClose();
  }

  const onUpdateStoreHours = (storeHours: DailyTimeRange) => {
    onScheduleChange(sched => {
      return {
        ...sched,
        storeHours: storeHours
      }
    }, {weekday: undefined})
    toast.success("Store hours updated", {closeButton: true});
    editStoreHours.onClose();
  }

  const onUpdatePeakHours = (peakHours: DailyTimeRange[]) => {
    onScheduleChange(sched => {
      return {
        ...sched,
        peakHours: flatten(times(7, d => map(peakHours, ph => ({
          dayOfWeek: d + 1,
          start: ph.start,
          end: ph.end
        }))))
      }
    }, {weekday: undefined})
    toast.success("Peak hours updated", {closeButton: true});
    editPeakHours.onClose();
  }

  const onCopyDay = (value: {
    days: number[]
    copyShiftAssignments: boolean,
    includeAreaIds: string[],
    shouldFilterAreas: boolean;
  }) => {
    const {days, copyShiftAssignments} = value;
    copy.onClose();

    onScheduleChange(sched => {
      return produce(sched, draftSched => {
        const fromDay = find(draftSched.days, d => d.dayOfWeek === dayOfWeek);
        if (!fromDay) return;
        const areaTitles = map(filter(fromDay.areas, a => includes(value.includeAreaIds, a.id)), a => a.title);

        for (const day of days) {
          // don't allow copying from and to the same day
          if (day === dayOfWeek) {
            continue;
          }

          const toDayIdx = findIndex(draftSched.days, d => d.dayOfWeek === day);
          const toDay = draftSched.days[toDayIdx];
          if (toDayIdx === -1) {
            continue;
          }

          // copy over the data, but generate new IDs for all the things
          draftSched.days[toDayIdx] = copyDay({
            fromDay: fromDay,
            dayOfWeek: day,
            copyShiftAssignments: copyShiftAssignments,
            includeAreaTitles: areaTitles,
            toDay: toDay,
            shouldFilterAreas: value.shouldFilterAreas
          });
        }
      });
    }, {weekday: undefined})

    const dayTitles = map(days, d => {
      return find(daysOfWeek, dow => dow.dayOfWeek === Number(d))?.name;
    });
    toast.success(`Day copied to ${dayTitles.join(', ')}`, {closeButton: true});
  }

  const onWheel = useCallback((e: React.WheelEvent) => {
    if (e.ctrlKey) {
      setZoom(z => {
        return clamp(z + (e.deltaY > 0 ? -0.05 : 0.05), 0.5, 1.5);
      });
    }
  }, []);

  const warningsForToday = useMemo(() => {
    return filter(validationResult.messages, isMessageForWeekday(dayOfWeek));
  }, [validationResult, dayOfWeek]);
  const notIgnoredWarningsForToday = useMemo(() => {
    return filter(warningsForToday, (m) => {
      return !isMessageIgnored(m) && isMessageIgnorable(m);
    });
  }, [warningsForToday, ignoredMessages]);

  const validationPanel = useDisclosure();

  const onPublish = () => {
    publish.onOpen();
  }

  const onGoToValidationMessage = useCallback((week: IsoWeekDate, dayOfWeek: number, msg: ValidationMessage) => {
    if ("shift" in msg.info && msg.dayOfWeek) {
      publish.onClose();
      onChangeDayOfWeek(msg.dayOfWeek);
      onShiftOpened(msg.info.shift.shiftAreaId, msg.info.shift.id);
    }
  }, []);

  const onUpdateMetrics = (metrics: ScheduleMetricsInput) => {
    const sanitizedMetrics = omitBy(metrics, (v) => isNil(v));
    onScheduleChange(sched => {
      return produce(sched, draftSched => {
        const day = find(draftSched.days, d => d.dayOfWeek === dayOfWeek);
        if (day) {
          day.metricsInput = isEmpty(sanitizedMetrics) ? undefined : sanitizedMetrics;
        }
      });
    }, {weekday: dayOfWeek})
  }

  const metricsInputForToday = useMemo(() => find(schedule.days, d => d.dayOfWeek === dayOfWeek)?.metricsInput, [dayOfWeek, schedule.days]);

  const isShiftDetailsOpen = snapshot.matches({
    shiftSelected: "shiftDetailsOpen"
  });
  const areEditorHotkeysEnabled = !isShiftDetailsOpen && !editPeriods.isOpen && !editStoreHours.isOpen && !editPeakHours.isOpen && !copy.isOpen && !validationPanel.isOpen;

  const isUndoEnabled = undoStack.stackPointer > 0 && undoStack.stackLength > 0;
  useHotkeys("mod+z", onUndo, {
    enableOnFormTags: false,
    preventDefault: true,
    enabled: areEditorHotkeysEnabled && isUndoEnabled
  });

  const isRedoEnabled = undoStack.stackPointer < undoStack.stackLength - 1;
  useHotkeys("mod+shift+z", onRedo, {
    enableOnFormTags: false,
    preventDefault: true,
    enabled: areEditorHotkeysEnabled && isRedoEnabled
  });

  useHotkeys("ctrl+shift+p", positionCoverageView.onToggle, {
    enableOnFormTags: false,
    preventDefault: true,
    enabled: areEditorHotkeysEnabled
  });

  const today = find(schedule.days, d => d.dayOfWeek === dayOfWeek)!;
  const prevToday = usePrevious(today);

  // save day changes on the undo/redo stack for this day
  useEffect(() => {
    if (prevToday && prevToday.dayOfWeek === today.dayOfWeek && !isUndoRedoChange.current && !isEqual(today, prevToday)) {
      undoStack.onScheduleDayChange(today);
    }
    isUndoRedoChange.current = false;
  }, [today]);

  const publish = useDisclosure();
  const onPublished = useCallback((numShifts: number) => {
    toast.success(`${numShifts} shifts on ${dayOfWeekObj?.name} of week ${schedule.week.week} published. Team members will be notified.`, {closeButton: true});
    publish.onClose();
  }, []);

  useEffect(() => {
    if (props.shiftId) {
      onShiftOpened("", props.shiftId);
    }
  }, [props.shiftId]);

  useEffect(() => {
    scrollShiftIntoView(selectedShiftId);
  }, []);

  const getNotes = api.user.getScheduleNotes.useQuery({scheduleId: props.scheduleId}, {
    select: notes => {
      return filter(notes, n => n.dayOfWeek === props.dayOfWeek);
    }
  });

  const onSortShifts = () => {
    onScheduleChange(sched => {
      return produce(sched, draftSched => {
        const day = find(draftSched.days, d => d.dayOfWeek === dayOfWeek);
        if (!day) return;

        // sort the shifts by start time
        forEach(day.areas, area => {
          area.shifts = sortBy(area.shifts, s => s.range.start);
        })
      });
    }, {weekday: dayOfWeek})
  }
  useHotkeys("mod+shift+l", onSortShifts, {
    enableOnFormTags: false,
    preventDefault: true,
    enabled: areEditorHotkeysEnabled
  });

  const [selectedActivityId, setSelectedActivityId] = useState<string>();
  const currentDateTime = DateTime.fromObject({
    weekYear: sched.draft.week.year,
    weekNumber: sched.draft.week.week,
    weekday: dayOfWeek as WeekdayNumbers
  })

  const onToggleArea = (areaTitle: string) => {
    setAreaSettings(settings => ({
      ...settings, [areaTitle]: {
        isCollapsed: !settings[areaTitle]?.isCollapsed
      }
    }))
  }

  const onBack = () => {
    navigate({
      to: "/$businessId/$storeId/schedules/builder",
      params: {
        businessId: schedule.businessId,
        storeId: schedule.storeId,
      },
      search: {
        week: schedule.week.week,
        year: schedule.week.year,
      }
    });
  }

  const forecast = scheduleRev.forecast;
  const metrics = getMetrics({
    schedule: schedule,
    dayOfWeek: dayOfWeek,
    countOpenShiftsTowardsLabor: countOpenShifts,
    payRates: payRates,
    averagePayRate: forecast ? getAveragePayRate(forecast) : undefined,
  });

  return (
          <div className={`overflow-auto h-screen w-screen bg-[#F6F8FC] flex flex-col gioa-scroll items-stretch`}
               data-increment-width={incrementWidth}
               ref={scrollContainerRef}>
            <Helmet>
              <title>
                {dayOfWeekObj?.name} - Nation
              </title>
            </Helmet>
            {/*Topbar*/}
            <div className={"bg-white flex flex-wrap gap-2 items-center px-5 py-3 sticky left-0 border-b-2 border-slate-300"}>
              <div className={"flex items-stretch gap-1"}>
                <Button variant={"outline"} onClick={onBack}>
                  <ArrowLeftIcon size={16} className={"text-gray-500 mr-2"}/>
                  To week {schedule.week.week}
                </Button>

                <SegmentedControl options={["Day", "Week"]}
                                  value={"Day"} onChange={onChangeView}/>

                <ScheduleDayMenu dayOfWeek={dayOfWeek} scheduleWeekDate={schedule.week}
                                 timezone={timezone}
                                 onChangeDayOfWeek={onChangeDayOfWeek}/>

                <Button variant={"outline"} onClick={onGoPrevDay}
                        disabled={dayOfWeek === 1}
                        aria-label={"Previous day"}>
                  <ArrowLeftIcon size={16} className={"text-gray-500"}/>
                </Button>
                <Button variant={"outline"} onClick={onGoNextDay}
                        disabled={dayOfWeek === 7}
                        aria-label={"Next day"}>
                  <ArrowRightIcon size={16} className={"text-gray-500"}/>
                </Button>

                <div className={"w-[2px] h-8 bg-gray-300 mx-2 self-center"}/>

                <Menubar>
                  <MenubarMenu>
                    <MenubarTrigger className={"hover:bg-gray-100 cursor-pointer"}>
                      Edit
                    </MenubarTrigger>
                    <MenubarContent>
                      <MenubarItem onSelect={onUndo} disabled={!isUndoEnabled}>
                        Undo
                        <MenubarShortcut>
                          <HotkeyLabel label={"mod+z"}/>
                        </MenubarShortcut>
                      </MenubarItem>
                      <MenubarItem onSelect={onRedo} disabled={!isRedoEnabled}>
                        Redo
                        <MenubarShortcut>
                          <HotkeyLabel label={"mod+shift+z"}/>
                        </MenubarShortcut>
                      </MenubarItem>
                      <MenubarSeparator/>
                      <MenubarItem onSelect={onSortShifts}>
                        Sort shifts by start time
                        <MenubarShortcut className={"ml-2"}>
                          <HotkeyLabel label={"mod+shift+l"}/>
                        </MenubarShortcut>
                      </MenubarItem>
                      <MenubarSeparator/>
                      <MenubarItem onSelect={() => {
                        send({type: "listEvents"});
                      }}>
                        Events
                      </MenubarItem>
                      <MenubarItem onSelect={() => editPeriods.onOpen()}>
                        Daily periods
                      </MenubarItem>
                      <MenubarItem onSelect={() => editPeakHours.onOpen()}>
                        Peak hours
                      </MenubarItem>
                      <MenubarItem onSelect={() => editStoreHours.onOpen()}>
                        Store hours
                      </MenubarItem>
                      {isPublishedWithDraft &&
                              <MenubarSeparator/>}
                      {isPublishedWithDraft &&
                              <MenubarItem onSelect={onDiscardDraft}>
                                Discard draft
                              </MenubarItem>}
                    </MenubarContent>
                  </MenubarMenu>
                  <Separator orientation={"vertical"}/>
                  <MenubarMenu>
                    <MenubarTrigger className={"hover:bg-gray-100 cursor-pointer"}>
                      View
                      {zoomPercentage > 100
                              ? <ZoomInIcon size={16} className={"ml-2 text-gray-600"}/>
                              : zoomPercentage < 100
                                      ? <ZoomOutIcon size={16} className={"ml-2 text-gray-600"}/>
                                      : null}
                    </MenubarTrigger>
                    <MenubarContent>
                      <MenubarCheckboxItem onSelect={() => {
                        send({type: "listTeamMembers"});
                      }} checked={isTeamMembersMenuOpen}>
                        Team Members
                      </MenubarCheckboxItem>
                      {hasForecast && isForecastingEnabled ?
                              <MenubarCheckboxItem onSelect={() => setIsForecastOpen(o => !o)} checked={isForecastOpen}>
                                Forecast
                              </MenubarCheckboxItem> : null}
                      <MenubarCheckboxItem onSelect={() => setIsEventsOpen(o => !o)} checked={isEventsOpen}>
                        Event timeline
                      </MenubarCheckboxItem>
                      <MenubarCheckboxItem onSelect={() => setIsAvailabilityShown(o => !o)}
                                           checked={isAvailabilityShown}>
                        Availability
                      </MenubarCheckboxItem>
                      <MenubarCheckboxItem onSelect={() => setIsAreaHeatmapShown(o => !o)} checked={isAreaHeatmapShown}>
                        Area Heatmap
                      </MenubarCheckboxItem>
                      <MenubarCheckboxItem onSelect={() => setArePeriodsVisible(o => !o)} checked={arePeriodsVisible}>
                        Daily periods
                      </MenubarCheckboxItem>
                      <MenubarCheckboxItem onSelect={() => setIsPeakHoursOpen(o => !o)} checked={isPeakHoursOpen}>
                        Peak hours
                      </MenubarCheckboxItem>
                      <MenubarCheckboxItem onSelect={validationPanel.onToggle} checked={validationPanel.isOpen}>
                        Issues ({warningsForToday.length})
                      </MenubarCheckboxItem>

                      <MenubarSeparator/>

                      <MenubarCheckboxItem onSelect={positionCoverageView.onToggle}
                                           checked={positionCoverageView.isOpen}>
                        Position Coverage
                        <MenubarShortcut>
                          <HotkeyLabel label={"ctrl+shift+p"}/>
                        </MenubarShortcut>
                      </MenubarCheckboxItem>

                      <MenubarCheckboxItem onSelect={activitiesView.onToggle}
                                           checked={activitiesView.isOpen}>
                        Activity Titles
                      </MenubarCheckboxItem>

                      <MenubarSeparator/>
                      <div className={"text-sm pl-8 py-1.5 pr-2 flex gap-2 items-center"}>
                        <div className={"pr-6"} title={"ctrl + mouse wheel"}>
                          Zoom
                        </div>
                        <Button onClick={() => doZoom(true)} className={"rounded-full"}
                                aria-label={"zoom out"}
                                variant={"secondary"} size={"iconSm"}>
                          <MinusIcon size={16}/>
                        </Button>
                        <div className={"w-10 text-center"}>
                          {zoomPercentage.toFixed(0)}%
                        </div>
                        <Button onClick={() => doZoom(false)} className={"rounded-full"}
                                aria-label={"zoom in"}
                                variant={"secondary"} size={"iconSm"}>
                          <PlusIcon size={16}/>
                        </Button>
                        {zoom !== 1 ?
                                <Button onClick={() => setZoom(1)} className={"rounded-full"}
                                        aria-label={"reset zoom"}
                                        variant={"secondary"} size={"xs"}>
                                  Reset
                                </Button> : null}
                      </div>
                    </MenubarContent>
                  </MenubarMenu>
                  <Separator orientation={"vertical"}/>
                  <MenubarMenu>
                    <MenubarTrigger className={"hover:bg-gray-100 cursor-pointer"}>
                      Tools
                    </MenubarTrigger>
                    <MenubarContent>
                      <MenubarItem onSelect={() => copy.onOpen()}>
                        Copy Day
                      </MenubarItem>
                      <MenubarItem onSelect={() => editNotes.onOpen()}>
                        Notes {!isEmpty(getNotes.data) ? `(${getNotes.data?.length})` : ""}
                      </MenubarItem>
                      <MenubarItem>
                        <Link from={props.routeFullPath as any}
                              search={prev => ({...prev, selectedDate: currentDateTime.toISO()}) as never}
                              to={"../../reports/availability" as any}>
                          Availability Report
                        </Link>
                      </MenubarItem>
                      <MenubarItem onSelect={() => {
                        send({type: "listMetrics"});
                      }}>
                        Metrics Calculator
                      </MenubarItem>
                    </MenubarContent>
                  </MenubarMenu>
                </Menubar>
                <Button variant={"outline"} onClick={() => setIsTemplateSheetOpen(true)}>
                  Templates
                </Button>
              </div>
              <div className={"flex items-center gap-2 justify-end flex-1"}>
                {hasDraft ?
                        <div className={"pr-1"}>
                          <DocumentSaveStatus status={savingStatus}/>
                        </div> :
                        null}
                {props.isPresenceEnabled ?
                        <SchedulePresenceIndicator scheduleId={props.scheduleId}
                                                   currentPersonId={currentPersonId}
                                                   people={people}/> : null}
                <Toggle aria-label={"Show issues"}
                        variant={"outline"} title={"Show issues"}
                        onPressedChange={(pressed) => {
                          validationPanel.setOpen(pressed);
                        }}
                        pressed={validationPanel.isOpen}
                        className={cn("text-gray-700 whitespace-nowrap", {"border-amber-400": !isEmpty(notIgnoredWarningsForToday)})}>
                  Issues {notIgnoredWarningsForToday.length > 0 ? `(${notIgnoredWarningsForToday.length})` : ""}
                </Toggle>
                {hasDraft ?
                        <Button onClick={onPublish}>
                          Publish Day
                        </Button> : null}
              </div>
            </div>

            <div className={"flex grow"} style={{minWidth: incrementsContainerWidth}}>
              {/*Left sidebar*/}
              {positionCoverageView.isOpen
                      ? <div className={"bg-white sticky left-0 z-20 border-r border-slate-300 py-3"}
                             ref={leftSidebarRef}>
                        <div style={{height: topbarHeight}} className={" px-4 flex items-center"}>
                          Position Coverage
                        </div>
                        {isForecastOpen && forecast
                                ? <ScheduleForecastSide metrics={metrics} canViewPayRates={canViewPayRates}
                                                        forecast={forecast} weekday={dayOfWeek}/>
                                : null}
                        <div className={"pt-6"}>
                          {map(setupPositionsByArea, (setupPositions, areaTitle) => {
                            const isCollapsed = areaSettings[areaTitle]?.isCollapsed;

                            return [
                              <div style={{height: rowHeight}} key={areaTitle + "_area"} className={"py-1 px-4"}>
                                <div
                                        className={"bg-primary-600 text-white rounded-lg flex h-full w-full text-left items-stretch overflow-hidden "}>
                                  <div className={"flex items-center grow text-left pl-3 pr-2 hover:bg-primary-700"}>
                          <span className={"grow truncate pr-2"}>
                          {areaTitle}
                          </span>
                                  </div>
                                  <button title={isCollapsed ? "Expand area" : "Collapse area"}
                                          onClick={() => onToggleArea(areaTitle)}
                                          className={"bg-primary-600 px-1 hover:bg-primary-700"}>
                                    <ChevronDownIcon size={16}
                                                     className={cn("text-gray-100 transition-transform duration-200", {
                                                       "rotate-180": !isCollapsed
                                                     })}/>
                                  </button>
                                </div>
                              </div>,
                              ...map(setupPositions, setupPosition => {
                                const activities = setupPositionToActivities[setupPosition.title];
                                const totalHours = sumBy(activities, a => getRangeDurationHours(a.range));

                                return <div style={{height: rowHeight}} key={setupPosition.title + "row"}
                                            className={cn("w-full py-1 px-4")}>
                                  <div className={cn("w-full justify-between h-full px-4 py-2 gap-4 border border-gray-200 rounded-md inline-flex items-center whitespace-nowrap text-sm font-medium")}>

                                   <span>
                                    <span className={"max-w-[16rem] overflow-ellipsis overflow-hidden text-left"}
                                          style={{minWidth: "14ch"}}>
                                      {setupPosition.title}
                                      {setupPosition.start && setupPosition.end ? <span className={"text-muted-foreground text-sm ml-2"}>{to12HourTime(setupPosition.start, true)} - {to12HourTime(setupPosition.end, true)} ({getRangeDurationHours({start: setupPosition.start, end: setupPosition.end}).toFixed(0)}hr)</span> : null}
                                    </span>
                                     {totalHours ?
                                             <span className={cn("text-muted-foreground text-sm", {
                                               "ml-2": !(setupPosition.start && setupPosition.end)
                                             })}>{setupPosition.start && setupPosition.end ? " | " : ""}{Math.floor(totalHours)}hr</span> : null}
                                  </span>
                                  </div>
                                </div>;
                              })

                            ]
                          })}
                        </div>
                        <div className={"border-t border-gray-200 px-4 flex gap-2 py-4 mt-4"}>
                          <Link from={props.routeFullPath as any} className={buttonVariants({variant: "outline"})}
                                to={"../../positions" as any}>
                            Edit Setup Positions
                          </Link>
                        </div>
                      </div>
                      : <div className={"bg-white sticky left-0 z-20 border-r border-slate-300 py-3"}
                             ref={leftSidebarRef}>
                        <div style={{height: topbarHeight}} className={" px-4 flex items-center"}>
                          <div>
                            <label className={"flex flex-row items-center gap-2 border border-gray-200 px-2 py-1 rounded-md"}>
                              <Checkbox checked={countOpenShifts}
                                        onCheckedChange={s => setCountOpenShifts(Boolean(s))}/>
                              Count open shifts towards labor
                            </label>
                          </div>
                        </div>
                        {isForecastOpen && forecast
                                ? <ScheduleForecastSide metrics={metrics} canViewPayRates={canViewPayRates}
                                                        forecast={forecast} weekday={dayOfWeek}/>
                                : null}
                        <div className={"pt-6"}>
                          {map(shifts, (shift, rowIndex) => {
                            const isRowSelected = selectedShiftId === shift.id;
                            const teamMemberNumber = shift.shiftNum ?? 1;
                            return <ScheduleSidebarRow isHighlighted={isRowSelected}
                                                       storeHours={schedule.storeHours}
                                                       onClickArea={onClickAreaSidebarRow}
                                                       shifts={shifts} onToggleArea={onToggleArea}
                                                       rowHeight={rowHeight} teamMemberNumber={teamMemberNumber}
                                                       onClickTeamMember={onClickTeamMember}
                                                       onClickAddShift={onShiftAdded} rowIndex={rowIndex}
                                                       isSelected={isRowSelected} key={shift.id + "_sidebar"}/>
                          })}
                        </div>
                        <div className={"border-t border-gray-200 px-4 flex gap-2 py-4 mt-4"}>
                          <ScheduleAddArea onAddArea={onAddArea} {...addArea}
                                           storeAreas={storeAreas}
                                           dayOfWeek={dayOfWeek}/>
                          <ScheduleEditAreas areas={dayAreas} onSave={onSaveAreas}
                                             storeAreas={storeAreas}
                                             storeId={props.storeId}
                                             onAreaEdited={onAreaEdited}
                                             dayOfWeek={dayOfWeek}/>
                        </div>
                      </div>}

              {/*Timeline and shift rows*/}
              <div className={"pt-3 grow"} style={arePeriodsVisible ? dayPartsBgStyle : undefined}>
                {/*Timeline*/}
                <ScheduleTimeline width={incrementsContainerWidth} topbarHeight={topbarHeight}
                                  numIncrements={numIncrements}
                                  storeHours={schedule.storeHours} peakHours={schedule.peakHours}
                                  onOpenEvent={onOpenEvent} selectedEventId={selectedEventId}
                                  isPeakHoursOpen={isPeakHoursOpen}
                                  dayOfWeek={dayOfWeek} scheduleWeek={schedule.week}
                                  incrementWidth={incrementWidth} timelineHeight={timelineHeight}
                                  isEventsOpen={isEventsOpen} events={eventsForToday} shifts={shifts}
                                  leftSidebarRef={leftSidebarRef}/>

                {isForecastOpen && scheduleRev.forecast && isForecastingEnabled
                        ? <ScheduleForecastTimeline incrementWidth={incrementWidth}
                                                    countOpenShiftsTowardsLabor={countOpenShifts}
                                                    numIncrements={numIncrements} payRates={payRates}
                                                    schedule={schedule} weekday={dayOfWeek}
                                                    forecast={scheduleRev.forecast}
                                                    width={incrementsContainerWidth}/>
                        : null}

                {/*Shifts*/}
                {positionCoverageView.isOpen
                        ? <div style={bgStyle} className="relative pt-6 mx-4" onWheel={onWheel}>
                          {map(setupPositionsByArea, (setupPositions, areaTitle, rowIndex) => {
                            const isCollapsed = areaSettings[areaTitle]?.isCollapsed;

                            return [
                              <BlankRow rowHeight={rowHeight} key={areaTitle + "blank"}/>,
                              ...map(isCollapsed ? [] : setupPositions, setupPosition => {
                                const activities = setupPositionToActivities[setupPosition.title];

                                return <SetupActivitiesRow rowHeight={rowHeight} activities={activities}
                                                           setupPositionStart={setupPosition.start}
                                                           setupPositionEnd={setupPosition.end}
                                                           key={setupPosition.title + areaTitle + "row"}
                                                           incrementWidth={incrementWidth} onOpen={onShiftOpened}
                                                           selectedShiftId={selectedShiftId} storeHours={schedule.storeHours}
                                />
                              })

                            ]
                          })}

                        </div>
                        : <div style={bgStyle} className="relative pt-6 mx-4" onWheel={onWheel}>
                          {map(shifts, (shift, rowIndex) => {
                            if (shift.type === "area") {
                              return <AreaRow rowHeight={rowHeight} rowIndex={rowIndex} key={shift.id + "_area"}
                                              numShiftsInArea={shift.numShiftsInArea}
                                              isAreaHeatmapShown={isAreaHeatmapShown}
                                              incrementMetrics={shift.incrementMetrics ?? []}
                                              incrementWidth={incrementWidth}/>
                            } else if (shift.type === "shift") {
                              const isSelected = selectedShiftId === shift.id;
                              return (
                                      <ShiftRow rowHeight={rowHeight} rowIndex={rowIndex} isActivitiesView={activitiesView.isOpen}
                                                selectedActivityId={selectedActivityId}
                                                dayOfWeek={dayOfWeek} showAvail={isAvailabilityShown}
                                                onShiftOpened={onShiftOpened}
                                                isSelected={isSelected} storeHours={schedule.storeHours}
                                                key={shift.id + "row"} onShiftEndChange={onShiftEndChange}
                                                onShiftStartChange={onShiftStartChange}
                                                onShiftMoved={onShiftMoved}
                                                shift={shift} incrementWidth={incrementWidth}/>
                              );
                            } else if (shift.type === "add") {
                              return <BlankRow rowHeight={rowHeight} key={shift.id + "_add"}/>
                            }
                          })}
                        </div>}
              </div>
            </div>

            <Sheet modal={false} open={isShiftDetailsOpen}
                   onOpenChange={onShiftDetailsOpenChanged}>
              <SheetContent side="right" hideCloseButton={true}
                            onInteractOutside={onInteractOutside}
                            className="flex flex-col overflow-auto px-0">
                {selectedShift
                        ? <ShiftDetailsSheetContent shift={selectedShift} storeHours={schedule.storeHours}
                                                    storeId={props.storeId}
                                                    onDeleteShift={onDeleteShift} schedule={schedule}
                                                    onChangeView={onChangeView} settings={settings}
                                                    latestServerVersion={latestServerVersion}
                                                    currentView={"Day"}
                                                    canChangeView={Boolean(selectedShift.assignedTo)}
                                                    onDuplicateShift={onDuplicateShift}
                                                    onSelectedActivityChange={setSelectedActivityId}
                                                    timezone={timezone} routeFullPath={props.routeFullPath}
                                                    people={people} storeAreas={storeAreas}
                                                    validationResult={validationResult}
                                                    onShiftActivitiesUpdated={onShiftActivitiesUpdated}
                                                    onShiftUpdated={onShiftUpdated}
                                                    weeksHoursMap={weeksHoursMap}
                                                    storeState={storeState}/>
                        : null}

              </SheetContent>
            </Sheet>

            <ScheduleTemplateSheet isOpen={isTemplateSheetOpen} storeId={props.storeId}
                                   scheduleId={schedule.id}
                                   dayCreatedFromId={today?.createdFromTemplateId}
                                   dayOfWeek={dayOfWeek} onApplyTemplate={onApplyTemplateToToday}
                                   onOpenChange={onTemplateSheetOpenChange}/>

            <FloatingPanel timezone={timezone}
                           hasForecast={hasForecast} countOpenShiftsTowardsLabor={countOpenShifts}
                           schedule={schedule} onUpsertEvent={onUpsertEvent}
                           initMetricsInput={metricsInputForToday} storeId={props.storeId}
                           onUpdateMetrics={onUpdateMetrics}
                           isoWeek={schedule.week} snapshot={snapshot} send={send}
                           storeAreas={storeAreas} dayOfWeek={dayOfWeek}
                           events={scheduleEvents} settings={settings}
                           onDeleteEvent={onDeleteEvent}
                           shift={selectedShift} people={people}
                           onViewEventDetails={onViewEventDetails}
                           onSelectTeamMember={onTeamMemberSelected}/>

            <EditPeriodsDialog dayParts={schedule.dayParts} {...editPeriods}
                               storeHours={schedule.storeHours}
                               dayOfWeek={dayOfWeek} onSave={onUpdatePeriods}/>

            <EditStoreHoursDialog {...editStoreHours} storeHours={schedule.storeHours}
                                  onSave={onUpdateStoreHours}/>

            <EditPeakHoursDialog {...editPeakHours} peakHours={schedule.peakHours}
                                 onSave={onUpdatePeakHours}
                                 dayOfWeek={dayOfWeek}/>

            <CopyDayDialog {...copy} onCopy={onCopyDay} schedule={schedule}
                           dayOfWeek={dayOfWeek}/>

            <EditScheduleNotesDialog {...editNotes} scheduleId={schedule.id}
                                     week={schedule.week.week}
                                     dayOfWeek={dayOfWeek}/>

            <ScheduleValidationSheet isOpen={validationPanel.isOpen} onIgnoreMessage={ignoreMessage}
                                     storeHours={schedule.storeHours} onUnignoreMessage={unignoreMessage}
                                     scheduleRev={scheduleRev} people={people}
                                     validationResult={validationResult}
                                     onOpenChange={validationPanel.setOpen}
                                     dayOfWeek={dayOfWeek} storeId={props.storeId}
                                     onGoToMessage={onGoToValidationMessage}/>

            {editingArea &&
                    <ScheduleEditAreaDialog area={editingArea} isOpen={true}
                                            onOpenChange={onScheduleEditAreaDialogOpenChange}
                                            dayOfWeek={dayOfWeek}
                                            storeAreas={storeAreas}
                                            onSave={onAreaEdited}
                                            onDelete={onAreaDeleted}/>}

            <PublishDayDialog isOpen={publish.isOpen} dayOfWeek={dayOfWeek} draftVersion={latestServerVersion}
                              onOpenChange={publish.setOpen} published={scheduleRev.published}
                              routeFullPath={props.routeFullPath}
                              onGoToMessage={onGoToValidationMessage} onIgnoreMessage={ignoreMessage}
                              validationResult={validationResult} onUnignoreMessage={unignoreMessage}
                              schedule={schedule}
                              people={people} timezone={timezone}
                              storeHours={schedule.storeHours}
                              onPublished={onPublished}/>


          </div>
  );
}

