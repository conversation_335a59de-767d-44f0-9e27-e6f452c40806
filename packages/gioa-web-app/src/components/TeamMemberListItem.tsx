import React, {useEffect, useRef, useState} from 'react';
import {cn} from "@/src/util.ts";
import {ChevronRightIcon} from "lucide-react";
import {ProficiencyRating} from "@/src/components/ProficiencyRating.tsx";
import {getPersonLaborStatus} from "../../../api/src/personMetrics.ts";
import {LaborStatusIcon} from "@/src/components/LaborStatusIcon.tsx";
import {SuggestionReason} from "../../../api/src/shiftSuggestion.types.ts";
import {TeamMemberSelectButton} from "@/src/components/TeamMemberSelectButton.tsx";
import {PersonAvatar} from "@/src/components/PersonAvatar.tsx";
import {DailyTimeRange} from '@gioa/api/src/timeSchemas.ts';
import {createPortal} from 'react-dom';
import {useUnmount} from "usehooks-ts";
import {SchedulePersonClientDto} from "../../../api/src/schedulePersonDto.ts";
import {ShiftRowAvailabilityOverlay} from "@/src/components/ShiftRowAvailabilityOverlay.tsx";

export interface TeamMemberListItemProps {
  person: SchedulePersonClientDto;
  onSelectTeamMember: (person: SchedulePersonClientDto) => void;
  isSelecting: boolean;
  onViewDetails: (person: SchedulePersonClientDto) => void;
  score: number;
  reasons: SuggestionReason[];
  shiftId?: string;
  dayOfWeek: number;
  personTotalWeekHours: number;
  storeHours: DailyTimeRange;
}

export const TeamMemberListItem: React.FC<TeamMemberListItemProps> = React.memo(({
                                                                                   onSelectTeamMember,
                                                                                   isSelecting,
                                                                                   person,
                                                                                   onViewDetails, dayOfWeek,
                                                                                   score, shiftId, storeHours,
                                                                                   reasons, personTotalWeekHours
                                                                                 }) => {
  const laborStatus = getPersonLaborStatus(person.age);
  const shiftElem = useRef<HTMLDivElement | null>(null);
  const [incrementWidth, setIncrementWidth] = useState(0);

  useEffect(() => {
    if (!shiftId) return;

    shiftElem.current = document.querySelector(`[data-shift-id="${shiftId}"]`);

    const incrementWidthElem = document.querySelector("div[data-increment-width]");
    if (!incrementWidthElem) {
      return;
    }
    const incrementWidth = Number(incrementWidthElem.getAttribute("data-increment-width"));
    setIncrementWidth(incrementWidth);
  }, [shiftId])

  const [isHovering, setIsHovering] = useState(false);
  useUnmount(() => {
    setIsHovering(false);
  });
  useEffect(() => {
    setIsHovering(false);
  }, [shiftId])

  const showAvail = isSelecting && isHovering && shiftId && shiftElem.current?.dataset.shiftId === shiftId;

  return (
    <li key={person.id} //onMouseEnter={onMouseEnter} onMouseLeave={onMouseLeave}
        onMouseEnter={() => setIsHovering(true)}
        onMouseLeave={() => setIsHovering(false)}
        className={cn("flex items-center gap-2 border-b")}>
      {isSelecting ? (
        <TeamMemberSelectButton reasons={reasons} score={score}
                                person={person}
                                className={"basis-auto flex-shrink-0 ml-2"}
                                onClick={onSelectTeamMember}/>
      ) : null}
      <button className="flex items-center gap-2 py-4 pr-2 pl-3 grow hover:bg-gray-50"
              onClick={() => onViewDetails(person)}>
        <PersonAvatar size="sm" person={person}/>
        <div className="grow text-sm text-left break-words hyphens-auto flex items-center gap-2">
          {person.firstName} {person.lastName}
        </div>
        <div>
          <LaborStatusIcon laborStatus={laborStatus} size={24}/>
          <div className={"text-muted-foreground text-sm"}>
            {Math.floor(personTotalWeekHours)}hr
          </div>
        </div>
        <ProficiencyRating rank={person.proficiencyRanking ?? 0} colorScheme="light-bg"/>
        <div>
          <ChevronRightIcon size={24} className="text-gray-500"/>
        </div>
      </button>
      {shiftElem.current && showAvail ? createPortal(
        <ShiftRowAvailabilityOverlay person={person} storeHours={storeHours}
                                     dayOfWeek={dayOfWeek} showAvail={showAvail}
                                     incrementWidth={incrementWidth}/>,
        shiftElem.current
      ) : null}
    </li>
  );
});

TeamMemberListItem.displayName = "TeamMemberListItem";
