import React, {useEffect, useRef, useState} from 'react';
import {Check, Edit, X} from 'lucide-react';
import {Button} from "@/src/components/ui/button.tsx";

type InlineEditProps<T> = {
  value: T;
  onChange: (value: T) => void;
  displayFormatter?: (value: T) => string;
  valueFormatter?: (value: T) => string;
  parseValue?: (value: string) => T;
  inputType?: string;
  placeholder?: string;
  className?: string;
  inputClassName?: string;
  displayClassName?: string;
  showEditIcon?: boolean;
  multiline?: boolean;
  rows?: number;
  minHeight?: number;
  saveOnEnter?: boolean;
  saveOnCtrlEnter?: boolean;
  renderDisplayContent?: (value: T, placeholder: string) => React.ReactNode;
  initialIsEditing?: boolean;
};

/**
 * A generic inline editing component that transforms a displayed value
 * into an editable input when clicked.
 */
export function InlineEdit<T>({
                                value,
                                onChange,
                                displayFormatter = (val: T) => String(val),
                                valueFormatter = (val: T) => String(val),
                                parseValue = (val: string) => val as unknown as T,
                                inputType = 'text',
                                placeholder = 'Enter value',
                                className = '',
                                inputClassName = '',
                                displayClassName = '',
                                showEditIcon = true,
                                multiline = false,
                                rows = 3,
                                minHeight = 100,
                                saveOnEnter = true,
                                saveOnCtrlEnter = false,
                                renderDisplayContent,
                                initialIsEditing = false,
                              }: InlineEditProps<T>) {
  const [isEditing, setIsEditing] = useState(initialIsEditing);
  const [inputValue, setInputValue] = useState<string>(valueFormatter(value));
  const inputRef = useRef<HTMLInputElement | HTMLTextAreaElement>(null);
  const displayRef = useRef<HTMLDivElement>(null);
  const [width, setWidth] = useState<number | undefined>(undefined);

  // update input value when prop value changes
  useEffect(() => {
    setInputValue(valueFormatter(value));
  }, [value]);

  // focus the input when entering edit mode
  useEffect(() => {
    if (isEditing && inputRef.current) {
      setInputValue(valueFormatter(value));
      inputRef.current.focus();
    }
  }, [isEditing]);

  // measure the display width before switching to edit mode
  useEffect(() => {
    if (!isEditing && displayRef.current) {
      const newWidth = displayRef.current.offsetWidth;
      // add a bit of extra space for comfortable editing
      setWidth(Math.max(newWidth + 10, 60)); // At least 60px wide
    }
  }, [isEditing]);

  const handleValueClick = () => {
    setIsEditing(true);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    setInputValue(e.target.value);
  };

  const handleSave = () => {
    try {
      const newValue = parseValue(inputValue);
      onChange(newValue);
      setIsEditing(false);
    } catch (error) {
      console.error('Error parsing value:', error);
    }
  };

  const handleCancel = () => {
    setInputValue(valueFormatter(value));
    setIsEditing(false);
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    if (e.key === 'Escape') {
      handleCancel();
    } else if (e.key === 'Enter') {
      if (multiline) {
        if ((saveOnCtrlEnter && e.ctrlKey) || (!saveOnCtrlEnter && !saveOnEnter)) {
          handleSave();
          e.preventDefault();
        }
      } else if (saveOnEnter) {
        handleSave();
        e.preventDefault();
      }
    }
  };

  const handleBlur = () => {
    // Optional: you could save on blur, but in this implementation we don't
    // to prevent unexpected saves when clicking outside
  };

  return (
    <div className={`inline-flex ${multiline ? 'items-start' : 'items-center'} ${className}`}>
      {isEditing ? (
        <div className={`${multiline ? 'flex flex-col' : 'flex items-stretch'}`}>
          {multiline ? (
            <textarea
              ref={inputRef as React.RefObject<HTMLTextAreaElement>}
              value={inputValue}
              onChange={handleInputChange}
              onKeyDown={handleKeyDown}
              placeholder={placeholder}
              rows={rows}
              style={{
                width: width ? `${width}px` : '100%',
                minHeight: `${minHeight}px`
              }}
              className={`px-2 py-1 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500 ${inputClassName}`}
            />
          ) : (
            <input
              ref={inputRef as React.RefObject<HTMLInputElement>}
              type={inputType}
              value={inputValue}
              onChange={handleInputChange}
              onKeyDown={handleKeyDown}
              onBlur={handleBlur}
              placeholder={placeholder}
              style={{width: width ? `${width}px` : 'auto'}}
              className={`px-2 py-1 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500 ${inputClassName}`}
            />
          )}

          {multiline ? (
            <div className="flex mt-2 justify-end gap-2">
              <Button variant={"secondary"} leftIcon={<X size={16}/>}
                      type="button" size={"sm"}
                      onClick={handleCancel}>

                Cancel
              </Button>
              <Button leftIcon={<Check size={16}/>}
                      type="button" size={"sm"}
                      onClick={handleSave}>

                Save
              </Button>

            </div>
          ) : (
            <div className="flex">
              <button
                type="button"
                onClick={handleSave}
                className="ml-2 p-1 w-8 text-green-600 hover:text-green-800 flex items-center hover:bg-gray-100 justify-center rounded-md"
                title="Save"
              >
                <Check size={16}/>
              </button>
              <button
                type="button"
                onClick={handleCancel}
                className="ml-1 p-1 w-8 text-red-600 hover:text-red-800 flex text-center items-center hover:bg-gray-100 justify-center rounded-md"
                title="Cancel"
              >
                <X size={16}/>
              </button>
            </div>
          )}
        </div>
      ) : (
        <div
          ref={displayRef}
          role="button"
          tabIndex={0}
          className={`${multiline ? 'w-full' : 'flex items-center gap-2'} rounded hover:bg-gray-100 cursor-pointer ${displayClassName}`}
          onClick={handleValueClick}
          onKeyDown={(e) => {
            if (e.key === 'Enter' || e.key === ' ') {
              e.preventDefault(); // Prevent page scroll on space
              handleValueClick();
            }
          }}
        >
          {renderDisplayContent ? (
            renderDisplayContent(value, placeholder)
          ) : (
            <div className="flex items-center gap-2">
              <span className={multiline ? 'whitespace-pre-wrap' : ''}>
                {value ? displayFormatter(value) : <span className="text-gray-400">{placeholder}</span>}
              </span>
              {showEditIcon && <Edit size={14} className="text-gray-400 flex-shrink-0"/>}
            </div>
          )}
        </div>
      )}
    </div>
  );
}
