import React from 'react';
import { FileIcon, FileTextIcon, ImageIcon, VideoIcon } from 'lucide-react';
import { cn } from '@/src/util';

export interface FileThumbnailIconProps {
  mimeType: string;
  className?: string;
}

export const FileThumbnailIcon: React.FC<FileThumbnailIconProps> = ({ mimeType, className }) => {
  // Determine the file type based on the MIME type
  const isImage = mimeType.startsWith('image/');
  const isVideo = mimeType.startsWith('video/');
  const isPdf = mimeType === 'application/pdf';
  const isText = mimeType.startsWith('text/') ||
                mimeType === 'application/msword' ||
                mimeType.includes('document') ||
                mimeType.includes('spreadsheet');

  // Choose the appropriate icon based on file type
  const renderIcon = () => {
    if (isImage) {
      return <ImageIcon className={cn("text-blue-500", className)} />;
    } else if (isVideo) {
      return <VideoIcon className={cn("text-purple-500", className)} />;
    } else if (isPdf) {
      return (
        <div className={cn("relative border border-gray-200 rounded-md bg-white", className)}>
          <div className="w-full h-full flex items-center justify-center p-4">
            <svg width="48" height="48" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M19 3H5C3.9 3 3 3.9 3 5V19C3 20.1 3.9 21 5 21H19C20.1 21 21 20.1 21 19V5C21 3.9 20.1 3 19 3Z" fill="#F5F5F5" stroke="#E0E0E0" />
              <path d="M7 7H17V9H7V7Z" fill="#E53935" />
              <path d="M7 11H17V13H7V11Z" fill="#E53935" />
              <path d="M7 15H13V17H7V15Z" fill="#E53935" />
            </svg>
          </div>
        </div>
      );
    } else if (isText) {
      return <FileTextIcon className={cn("text-blue-500", className)} />;
    } else {
      return <FileIcon className={cn("text-gray-500", className)} />;
    }
  };

  return (
    <div className={cn("flex items-center justify-center", className)}>
      {renderIcon()}
    </div>
  );
};
