import React from 'react';
import {CalendarX2, <PERSON>CircleIcon, TriangleAlertIcon} from "lucide-react";
import {Button, buttonVariants} from "@/src/components/ui/button.tsx";
import {negativePersonSuggestionThreshold, SuggestionReason} from "../../../api/src/shiftSuggestion.types.ts";
import {Popover, PopoverContent, PopoverTrigger} from './ui/popover';
import {Text} from './Text';
import {filter, isEmpty, map, sortBy, some} from "lodash";
import {PopoverArrow, PopoverClose} from "@radix-ui/react-popover";
import {SchedulePersonClientDto} from "../../../api/src/schedulePersonDto.ts";
import {getReasonLabel} from "../../../api/src/scheduleBuilder.util.ts";
import {VariantProps} from "class-variance-authority";

export interface TeamMemberSelectButtonProps {
  onClick: (person: SchedulePersonClientDto) => void;
  person: SchedulePersonClientDto;
  score: number;
  reasons: SuggestionReason[];
  className?: string;
}

function getSeverityTheme(score: number, personName: string, negativeReasons: SuggestionReason[]): {
  icon: React.ReactNode,
  buttonColorScheme: VariantProps<typeof buttonVariants>["colorScheme"];
  helpText: string;
} {
  if (some(negativeReasons, reason => reason.type === "conflictsWithTimeOff")) {
    return {
      icon: <CalendarX2 className="text-red-700"/>,
      buttonColorScheme: "red",
      helpText: `${personName} is not suggested for this shift because:`
    }
  }
  if (score < negativePersonSuggestionThreshold) {
    return {
      icon: <TriangleAlertIcon className="text-amber-700"/>,
      buttonColorScheme: "amber",
      helpText: `${personName} is not suggested for this shift because:`
    }
  }
  return {
    icon: <PlusCircleIcon className="text-blue-700"/>,
    buttonColorScheme: "blue",
    helpText: `Caution: `
  }
}

export const TeamMemberSelectButton: React.FC<TeamMemberSelectButtonProps> = ({
                                                                                onClick,
                                                                                className,
                                                                                score,
                                                                                reasons,
                                                                                person
                                                                              }) => {
  const _onClick = () => {
    onClick(person);
  }

  const negativeReasons = sortBy(filter(reasons, reason => reason.score < 0), r => r.score);
  if (!isEmpty(negativeReasons)) {
    const {icon, buttonColorScheme, helpText} = getSeverityTheme(score, person.firstName + " " + person.lastName, negativeReasons);

    return <Popover>
      <PopoverTrigger asChild>
        <Button size="icon" colorScheme={buttonColorScheme}
                className={className}>
          {icon}
        </Button>
      </PopoverTrigger>
      <PopoverContent
        // prevent the trigger from regaining focus when the popover closes. If the trigger regains focus,
        // it scrolls the team member list to the trigger after selecting a team member. It should rather stay
        // at the current scroll position, to avoid annoying and confusing the user.
        onCloseAutoFocus={e => e.preventDefault()}
                      side={"left"}>
        <PopoverArrow width={12} height={8}/>
        <Text size={"sm"}>
          {helpText}
        </Text>
        <ul className="list-disc list-inside mt-2">
          {map(negativeReasons, (reason, index) => (
            <li key={index} className="text-sm text-gray-800">
              {getReasonLabel({reason: reason, person: person})}
            </li>
          ))}
        </ul>

        <PopoverClose asChild>
          <Button size="sm" leftIcon={<PlusCircleIcon size={16}/>}
                  variant={"secondary"} onClick={_onClick} className="mt-3 w-full">
            Assign anyway
          </Button>
        </PopoverClose>
      </PopoverContent>
    </Popover>
  } else {
    return (
      <Button size="icon" colorScheme="green" onClick={_onClick}
              className={className}>
        <PlusCircleIcon className="text-green-700"/>
      </Button>
    );
  }
}
