import React, { use<PERSON><PERSON>back, useEffect, useMemo, useState } from "react";
import { Heading } from "@/src/components/Heading";
import { api } from "@/src/api";
import { find, map, reduce } from "lodash";
import { Button } from "@/src/components/ui/button";
import { Text } from "@/src/components/Text";
import { Card } from "@/src/components/ui/card";
import { ArrowLeft, Edit, GripVertical, PlusCircle } from "lucide-react";
import { toast } from "sonner";
import { genPositionId } from "@gioa/api/src/schemas";
import { useNavigate } from "@tanstack/react-router";
import {
  closestCenter,
  DndContext,
  DragEndEvent,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
} from "@dnd-kit/core";
import {
  SortableContext,
  sortableKeyboardCoordinates,
  useSortable,
  verticalListSortingStrategy,
} from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import { restrictToVerticalAxis } from "@dnd-kit/modifiers";
import { PositionDialog } from "@/src/components/PositionDialog";
import { Switch } from "@/src/components/ui/switch.tsx";

type SelectedPositionIds = { [id: string]: boolean };

export interface PositionItem {
  id: string;
  title: string;
  isReadonly?: boolean;
  isActive: boolean;
}

interface SortablePositionItemProps {
  position: PositionItem;
}

const SortablePositionItem = ({ position }: SortablePositionItemProps) => {
  const { attributes, listeners, setNodeRef, transform, transition } = useSortable({ id: position.id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  };

  return (
    <div ref={setNodeRef} style={style} {...attributes} {...listeners} className="mb-3">
      <Card className="flex items-center hover:bg-gray-50">
        <div className="p-3 cursor-grab active:cursor-grabbing">
          <GripVertical size={20} className="text-gray-400" />
        </div>
        <div className="flex-1 py-3">
          <Text>{position.title}</Text>
        </div>
      </Card>
    </div>
  );
};

export interface WorkAreaPositionsProps {
  businessId: string;
  storeId: string;
  areaId: string;
}

export const WorkAreaPositions: React.FC<WorkAreaPositionsProps> = ({ businessId, storeId, areaId }) => {
  const [business] = api.user.getBusiness.useSuspenseQuery(undefined, {
    staleTime: 1000 * 60 * 60, // 1 hour
  });
  const { areas } = find(business.stores, (s) => s.id === storeId)!;
  const navigate = useNavigate();
  const apiUtil = api.useUtils();
  const area = find(areas, (a) => a.id === areaId);

  const updatePositionsActiveState = api.user.updatePositionsActiveState.useMutation();

  // Initialize all state hooks first, before any conditional returns
  const [positions, setPositions] = useState<PositionItem[]>([]);
  const [reorderedPositions, setReorderedPositions] = useState<PositionItem[]>([]);
  const [isReordering, setIsReordering] = useState(false);
  const [dialogPosition, setDialogPosition] = useState<PositionItem | null>(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isEditMode, setIsEditMode] = useState(false);

  // Calculate position items from area data
  const positionItems: PositionItem[] = useMemo(() => {
    if (!area) return [];
    const sortedPositions = area.positions.sort((a, b) => (a?.order ?? 0) - (b?.order ?? 0));
    return map(sortedPositions, (position) => ({
      id: position.id,
      title: position.title,
      isReadonly: position.isReadonly,
      isActive: position.isActive,
    }));
  }, [area]);

  const initSelectedIds = useMemo(() => {
    return reduce(
      positionItems,
      (acc, p) => {
        acc[p.id] = p.isActive;
        return acc;
      },
      {} as SelectedPositionIds,
    );
  }, [positionItems]);

  const [selectedPositionIds, setSelectedPositionIds] = useState<SelectedPositionIds>(initSelectedIds);

  // Update positions when area data changes
  useEffect(() => {
    setPositions(positionItems);
  }, [positionItems]);

  // Update selected position IDs when initial data changes
  useEffect(() => {
    setSelectedPositionIds(initSelectedIds);
  }, [initSelectedIds]);

  const navBackToAreas = () => {
    navigate({
      to: "/$businessId/$storeId/store/settings/area/work-areas",
      params: {
        businessId,
        storeId,
      },
    });
  };

  const onSetSelectedPositions = (id: string) => {
    setSelectedPositionIds((ids) => {
      return {
        ...ids,
        [id]: !ids[id],
      };
    });
  };

  useEffect(() => {
    if (!area) return;

    updatePositionsActiveState.mutate(
      {
        areaId: area.id,
        positions: selectedPositionIds,
      },
      {
        onSuccess: async () => {
          toast.success("Position active state updated");
        },
      },
    );
  }, [selectedPositionIds, area]);

  const setPositionsOrder = api.user.setStoreAreaPositionsOrder.useMutation({
    onSuccess: async () => {
      await apiUtil.user.getBusiness.invalidate();
      toast.success("Positions reordered successfully");
    },
    onError: (error) => {
      toast.error(`Failed to reorder positions: ${error.message}`);
    },
  });

  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    }),
  );

  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;

    if (active.id !== over?.id) {
      const oldIndex = reorderedPositions.findIndex((item) => item.id === active.id);
      const newIndex = reorderedPositions.findIndex((item) => item.id === over?.id);

      const newItems = [...reorderedPositions];
      const [removed] = newItems.splice(oldIndex, 1);
      newItems.splice(newIndex, 0, removed);

      setReorderedPositions(newItems);
    }
  };

  const saveReordering = () => {
    const positionIdsInOrder = map(reorderedPositions, (position) => position.id);
    setPositionsOrder.mutate(
      {
        areaId,
        positionIdsInOrder,
      },
      {
        onSuccess: () => {
          setPositions(reorderedPositions);
          setIsReordering(false);
        },
      },
    );
  };

  const cancelReordering = () => {
    setReorderedPositions([]);
    setIsReordering(false);
  };

  const upsertPosition = api.user.upsertPosition.useMutation({
    onSuccess: async () => {
      await apiUtil.user.getBusiness.invalidate();
      toast.success("Position updated successfully");
    },
    onError: (error) => {
      toast.error(`Failed to update position: ${error.message}`);
    },
  });

  const handleEditPosition = useCallback((position: PositionItem) => {
    if (position.isReadonly) {
      toast.error("This position cannot be edited");
      return;
    }
    setDialogPosition(position);
    setIsEditMode(true);
    setIsDialogOpen(true);
  }, []);

  const handleAddPosition = useCallback(() => {
    setDialogPosition(null);
    setIsEditMode(false);
    setIsDialogOpen(true);
  }, []);

  const handlePositionDialogSubmit = useCallback(
    async (title: string) => {
      if (title.trim() === "") {
        toast.error("Position name cannot be empty");
        return;
      }

      const id = isEditMode && dialogPosition ? dialogPosition.id : genPositionId();
      await upsertPosition.mutateAsync({
        id,
        areaId,
        storeId,
        title,
      });

      setIsDialogOpen(false);
    },
    [areaId, storeId, upsertPosition, isEditMode, dialogPosition],
  );

  const startReordering = () => {
    setReorderedPositions([...positions]);
    setIsReordering(true);
  };

  if (!area) {
    return (
            <div className="container py-6">
              <Text>Work area not found.</Text>
              <Button className="mt-4" onClick={navBackToAreas} leftIcon={<ArrowLeft size={16} />}>
                Back to Work Areas
              </Button>
            </div>
    );
  }

  return (
    <div className="pl-8 sm:max-w-lg bg-white border-l border-gray-200">
      <div className="flex items-center mb-6">
        <Button variant="ghost" size="icon" className="mr-2" onClick={navBackToAreas}>
          <ArrowLeft size={20} />
        </Button>
        <Heading level={1} size={"xs"} className={"mb-1"}>
          {area.title}
        </Heading>
      </div>

      <div className="flex justify-between items-center mb-3">
        {isReordering ? (
          <div className={"flex-1 flex flex-row justify-between items-center"}>
            <div className="flex gap-2">
              <Button variant="outline" onClick={cancelReordering} disabled={setPositionsOrder.isPending}>
                Cancel
              </Button>
              <Button
                onClick={saveReordering}
                disabled={setPositionsOrder.isPending}
                isLoading={setPositionsOrder.isPending}
              >
                Save Order
              </Button>
            </div>
            <Text className="flex-1 text-right">Drag and drop to reorder positions.</Text>
          </div>
        ) : (
          <div className="flex gap-2">
            <Button variant="outline" onClick={startReordering} leftIcon={<GripVertical size={16} />}>
              Reorder Positions
            </Button>
            <Button onClick={handleAddPosition} leftIcon={<PlusCircle size={16} />}>
              Add Position
            </Button>
            <PositionDialog
              isOpen={isDialogOpen}
              onClose={() => setIsDialogOpen(false)}
              onSubmit={handlePositionDialogSubmit}
              initialTitle={dialogPosition?.title || ""}
              dialogTitle={isEditMode ? "Edit Position" : "Add Position"}
              submitButtonText={isEditMode ? "Save Changes" : "Create Position"}
            />
          </div>
        )}
      </div>

      {isReordering ? (
        <div className={"flex flex-col"}>
          <DndContext
            sensors={sensors}
            collisionDetection={closestCenter}
            onDragEnd={handleDragEnd}
            modifiers={[restrictToVerticalAxis]}
          >
            <SortableContext
              items={reorderedPositions.map((position) => position.id)}
              strategy={verticalListSortingStrategy}
            >
              {reorderedPositions.length > 0 ? (
                reorderedPositions.map((position) => (
                  <SortablePositionItem key={position.id} position={position} />
                ))
              ) : (
                <Text className="text-center text-gray-500 py-8">
                  No positions found. Click "Add Position" to create one.
                </Text>
              )}
            </SortableContext>
          </DndContext>
        </div>
      ) : (
        <div className={"flex flex-col gap-2"}>
          {positions.length > 0 ? (
            positions.map((position) => (
              <Card
                key={position.id}
                className="px-4 py-2 flex flex-row gap-5 justify-between items-center hover:bg-gray-50"
              >
                <Switch
                  checked={selectedPositionIds[position.id]}
                  disabled={position.isReadonly}
                  onCheckedChange={() => {
                    onSetSelectedPositions(position.id);
                  }}
                />
                <div className="flex-1">
                  <Text>{position.title}</Text>
                </div>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => handleEditPosition(position)}
                  disabled={position.isReadonly}
                >
                  <Edit size={18} className="text-gray-500" />
                </Button>
              </Card>
            ))
          ) : (
            <Text className="text-center text-gray-500 py-8">
              No positions found. Click "Add Position" to create one.
            </Text>
          )}
        </div>
      )}
    </div>
  );
};
