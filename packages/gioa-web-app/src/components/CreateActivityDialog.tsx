import React, {useMemo} from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger
} from "@/src/components/ui/dialog.tsx";
import {But<PERSON>} from "@/src/components/ui/button.tsx";
import {
  incrementTo24HourTime,
  incrementToFriendlyTime,
  ScheduleRowInfo,
  ShiftActivity,
  storeTimeToIncrement
} from "../../../api/src/scheduleBuilder.util.ts";
import {DailyTimeRange} from '@gioa/api/src/timeSchemas.ts';
import {useDisclosure} from "@/src/hooks/useDisclosure.tsx";
import {ActivityDialogForm, ActivityDialogFormValues} from "@/src/components/ActivityDialogForm.tsx";
import {genShiftActivityId} from "../../../api/src/schemas.ts";
import {PlusIcon} from 'lucide-react';
import {addToTime, clamp24HourTime} from "../../../api/src/date.util.ts";
import {activityToLaborCounting} from "@/src/components/ShiftActivityTypeChooser.tsx";
import {maxBy} from "lodash";

export interface CreateActivityDialogProps {
  shift: ScheduleRowInfo;
  onSubmit: (activity: ShiftActivity) => void;
  storeHours: DailyTimeRange;
  storeId: string;
}

export const CreateActivityDialog: React.FC<CreateActivityDialogProps> = ({
                                                                            storeId,
                                                                            onSubmit,
                                                                            shift,
                                                                            storeHours,
                                                                          }) => {


  const dialog = useDisclosure()
  const onFormSubmit = (value: ActivityDialogFormValues) => {
    if (!value.activityType) return;

    const countsTowardsLaborConfig = value.activityType ? activityToLaborCounting[value.activityType] : "configurable";
    const countsTowardsLabor = countsTowardsLaborConfig === "configurable"
            ? value.countsTowardsLabor
            : countsTowardsLaborConfig === "counts";

    onSubmit({
      id: genShiftActivityId(),
      activityType: value.activityType,
      start: storeTimeToIncrement(storeHours, value.start),
      end: storeTimeToIncrement(storeHours, value.end),
      title: value.title,
      description: value.description,
      countsTowardsLabor: countsTowardsLabor,
      range: {
        start: value.start,
        end: value.end
      },
      setupPositionTitle: value.setupPositionTitle ?? undefined,
      payStatus: value.payStatus ?? undefined,
    })
    dialog.onClose();
  }

  const _onCancel = () => {
    dialog.onClose();
  }

  const latestActivity = useMemo(() => {
    return maxBy(shift.activities, a => a.range.end);
  }, [shift.activities]);
  const shiftStart24Hr = incrementTo24HourTime(storeHours, shift.start);
  const shiftEnd24Hr = incrementTo24HourTime(storeHours, shift.end);
  const defaultStart = clamp24HourTime(latestActivity && latestActivity.range.end < shiftEnd24Hr ? latestActivity.range.end : shiftStart24Hr, shiftStart24Hr, shiftEnd24Hr);
  const defaultEnd = clamp24HourTime(shiftEnd24Hr, addToTime(defaultStart, 5, "minutes"), shiftEnd24Hr);

  return <Dialog open={dialog.isOpen} onOpenChange={dialog.setOpen}>
    <DialogTrigger asChild>
      <Button variant={"outline"} size={"sm"} leftIcon={<PlusIcon size={16}/>}>
        Add Activity
      </Button>
    </DialogTrigger>
    <DialogContent size={"xl"}>
      <DialogHeader>
        <DialogTitle>Add Activity</DialogTitle>
        <DialogDescription>
          This shift goes
          from {incrementToFriendlyTime(storeHours, shift.start)} to {incrementToFriendlyTime(storeHours, shift.end)}.
          Choose a timeframe for this activity.
        </DialogDescription>
      </DialogHeader>

      <ActivityDialogForm defaultValues={{
        start: defaultStart,
        end: defaultEnd,
        title: "",
        description: "",
        countsTowardsLabor: false,
        activityType: undefined,
        setupPositionTitle: undefined,
        payStatus: "paid"
      }} onSubmit={onFormSubmit} storeId={storeId}
                          storeHours={storeHours}
                          shift={shift}
                          onCancel={_onCancel}/>
    </DialogContent>
  </Dialog>
}
