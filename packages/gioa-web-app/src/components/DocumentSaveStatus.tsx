import React from 'react';
import {AnimatePresence, motion} from 'framer-motion';
import {Check, Loader} from 'lucide-react';

export type SaveStatus = 'saved' | 'saving' | 'pending';

interface DocumentSaveStatusProps {
  status: SaveStatus;
}

export const DocumentSaveStatus: React.FC<DocumentSaveStatusProps> = ({status}) => {
  return (
    <AnimatePresence mode="wait">
      {status === 'saved' && (
        <motion.div
          key="saved"
          initial={{opacity: 0, y: 10}}
          animate={{opacity: 1, y: 0}}
          exit={{opacity: 0, y: -10}}
          className="flex items-center gap-1 text-green-600"
        >
          <Check size={16}/>
          <span className="text-sm">Draft saved</span>
        </motion.div>
      )}
      {status === 'saving' && (
        <motion.div
          key="saving"
          initial={{opacity: 0, y: 10}}
          animate={{opacity: 1, y: 0}}
          exit={{opacity: 0, y: -10}}
          className="flex items-center gap-1 text-blue-600"
        >
          <motion.div
            animate={{rotate: 360}}
            transition={{duration: 1, repeat: Infinity, ease: "linear"}}
          >
            <Loader size={16}/>
          </motion.div>
          <span className="text-sm">Saving...</span>
        </motion.div>
      )}
      {status === 'pending' && (
        <motion.div
          key="pending"
          initial={{opacity: 0, y: 10}}
          animate={{opacity: 1, y: 0}}
          exit={{opacity: 0, y: -10}}
          className="flex items-center gap-1 text-yellow-600"
        >
          <span className="text-sm">Changes pending</span>
        </motion.div>
      )}
    </AnimatePresence>
  );
};
