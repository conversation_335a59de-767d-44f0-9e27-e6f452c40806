import React, {useMemo} from "react";
import {Area, AreaChart, CartesianGrid, Tooltip, XAxis, YAxis} from "recharts";
import {ChartConfig, ChartContainer} from "@/src/components/ui/chart";
import {TrendingUp} from "lucide-react";
import {DateTime, WeekdayNumbers} from "luxon";
import {
  ForecastDataPoints
} from "@gioa/api/src/scheduling/metrics/scheduleHourlySalesForecast/scheduleHourlySalesForecastTypes";
import {formatCurrency} from "../../../common/src/dataFormatters.ts";
import {dollars, Dollars} from "../../../api/src/scheduling/metrics/dollars.ts";
import {toDollars} from "../../../api/src/scheduling/metrics/cents.ts";

// Chart configuration
const chartConfig = {
  forecast: {
    label: "Forecast",
    color: "hsl(var(--chart-1))",
    icon: TrendingUp,
  },
} satisfies ChartConfig;

export interface ForecastGraphProps {
  forecast: ForecastDataPoints;
  dataRangeEnd: Date;
  timezone: string;
  height?: number;
}

export function ForecastGraph({forecast, dataRangeEnd, timezone, height = 400}: ForecastGraphProps) {
  // Process forecast data for chart
  const chartData = useMemo(() => {
    if (!forecast) return [];

    const dataPoints: Array<{hour: number, time: string, forecast: Dollars, weekday: string, formattedTime: string}> = [];

    // Convert Map entries to array for chart
    forecast.forEach((dataPoint, weekHour) => {
      // Calculate day and hour from weekHour (0-167)
      const day = Math.floor(weekHour / 24) + 1; // 1-7 (Monday-Sunday)
      const hour = weekHour % 24; // 0-23

      // Create a date object for this point
      const pointDate = DateTime.fromObject({
        weekYear: DateTime.fromJSDate(dataRangeEnd).weekYear,
        weekNumber: DateTime.fromJSDate(dataRangeEnd).weekNumber,
        weekday: day as WeekdayNumbers,
        hour: hour,
        minute: 0,
      }, { zone: timezone });

      if (!pointDate.isValid) return;

      dataPoints.push({
        hour: weekHour,
        time: pointDate.toISO(),
        forecast: toDollars(dataPoint.amountCents),
        weekday: pointDate.toFormat('ccc'),
        formattedTime: pointDate.toFormat('ccc t')
      });
    });

    // Sort by time
    return dataPoints.sort((a, b) => a.hour - b.hour);
  }, [forecast, dataRangeEnd, timezone]);

  if (chartData.length === 0) {
    return (
      <div className="text-center p-6">
        <p>No forecast data available.</p>
      </div>
    );
  }

  return (
    <ChartContainer config={chartConfig} className={"max-h-[600px] aspect-[4]"}>
      <AreaChart
        data={chartData}
        margin={{
          top: 10,
          right: 30,
          left: 0,
          bottom: 0,
        }}
        height={height}
      >
        <CartesianGrid strokeDasharray="3 3"/>
        <XAxis
          dataKey="formattedTime"
          tickLine={false}
          axisLine={true}
          tickMargin={8}
        />
        <YAxis
          width={72}
          tickFormatter={(value) => formatCurrency(value)}
          tickLine={false}
          axisLine={true}
          tickMargin={8}
        />
        <Tooltip
          formatter={(value) => [formatCurrency(dollars(value as number)), "Forecast"]}
          labelFormatter={(label) => {
            return label;
          }}
        />
        <Area
          type="monotone"
          dataKey="forecast"
          stroke="var(--color-forecast)"
          fill="var(--color-forecast)"
          fillOpacity={0.3}
        />
      </AreaChart>
    </ChartContainer>
  );
}

