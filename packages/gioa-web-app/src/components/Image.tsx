import React from 'react';

export interface ImageProps extends React.ImgHTMLAttributes<HTMLImageElement> {
  src: string;
  alt: string;
  width?: number;
  height?: number;
  className?: string;
  onLoad?: () => void;
  onLoadStart?: () => void;
}

export const Image: React.FC<ImageProps> = ({ src, alt, width, height, className, onLoad, onLoadStart, ...rest }) => {
  const handleLoad = () => {
    if (onLoad) onLoad();
  };

  return (
    <img
      src={src}
      alt={alt}
      width={width}
      height={height}
      className={className}
      onLoad={handleLoad}
      {...rest}
    />
  );
}
