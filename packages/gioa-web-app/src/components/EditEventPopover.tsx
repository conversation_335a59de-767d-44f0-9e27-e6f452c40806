import React from 'react';
import {Popover, PopoverContent, PopoverTrigger} from "./ui/popover.tsx";
import {useDisclosure} from "@/src/hooks/useDisclosure.tsx";
import {ScheduleEventDto} from '@gioa/api/src/scheduleEventSchemas.ts';
import {EventDetailsPanelContent} from "@/src/components/EventDetailsPanelContent.tsx";

export interface EditEventPopoverProps {
  event: ScheduleEventDto;
  storeId: string;
  timezone: string | null;
  onUpsertEvent: (event: ScheduleEventDto) => void;
  onDeleteEvent: (event: ScheduleEventDto) => void;
  children: React.ReactNode;
  side?: string;
}

export const EditEventPopover: React.FC<EditEventPopoverProps> = ({
                                                                    event,
                                                                    side = "top",
                                                                    timezone, storeId,
                                                                    onUpsertEvent,
                                                                    onDeleteEvent,
                                                                    children
                                                                  }) => {
  const popover = useDisclosure();

  const handleUpsertEvent = (event: ScheduleEventDto) => {
    onUpsertEvent(event);
    popover.setOpen(false); // Close the popover
  };

  const handleDeleteEvent = (event: ScheduleEventDto) => {
    onDeleteEvent(event);
    popover.setOpen(false); // Close the popover
  };

  return (
    <Popover open={popover.isOpen} onOpenChange={popover.setOpen}>
      <PopoverTrigger asChild>
        {children}
      </PopoverTrigger>
      <PopoverContent className="p-4 w-[360px] max-h-[480px] overflow-auto" side={side as any}>
        <EventDetailsPanelContent event={event} storeId={storeId}
                                  onUpdateEvent={handleUpsertEvent}
                                  onDeleteEvent={handleDeleteEvent}
                                  timezone={timezone}/>
      </PopoverContent>
    </Popover>
  );
}
