import React, {useCallback, useState} from "react";
import {Collapsible, CollapsibleContent} from "@/src/components/ui/collapsible.tsx";
import {map, some} from "lodash";
import {Badge} from "@/src/components/ui/badge.tsx";
import {Route} from "@/src/routes/_signedin/$businessId/$storeId/_nav.tsx";
import {Link, useNavigate} from '@tanstack/react-router'
import {ChevronDown, ChevronUp} from "lucide-react";
import {AnimatePresence, motion} from "framer-motion";

export interface SidebarIconProps {
  icon: () => React.JSX.Element;
  label: string;
  to?: string;
  isNavExpanded?: boolean;
  notificationCount?: number;
  activeOptions?: {
    exact?: boolean
  };

  subItems?: {
    label: string;
    href: string;
    notificationCount?: number;
    activeOptions?: {
      exact?: boolean
    };
  }[];
}

export const SidebarIcon = ({icon: Icon, label, to, isNavExpanded, notificationCount = 0, activeOptions, subItems}: SidebarIconProps) => {
  const [isSubNavExpanded, setIsSubNavExpanded] = useState(false);
  const toggleSubNav = useCallback(() => {
    setIsSubNavExpanded(!isSubNavExpanded);
  }, [isSubNavExpanded]);

  const hasSubItems = subItems && subItems.length > 0;
  const hasSubNotifications = some(subItems, s => s.notificationCount) || (notificationCount ?? 0) > 0;

  const navigate = useNavigate();

  return (
          <div className="flex flex-col">
            <Link from={Route.fullPath} to={to}
                  onClick={(e) => {
                    e.preventDefault();

                    if (hasSubItems) {
                      toggleSubNav();
                    } else {
                      navigate({
                        to
                      })
                    }
                  }}
                  activeProps={{className: "bg-gioaBlue text-white"}}
                  inactiveProps={{className: "text-muted-foreground hover:bg-gioaBlue/20"}}
                  className={"rounded-xl p-2"}
                  activeOptions={{exact: activeOptions?.exact}}>
              <div className="flex items-center gap-3">
                <div className="w-6 h-6 flex items-center justify-center flex-shrink-0 relative">
                  <Icon/>
                  {hasSubNotifications && <div className="rounded-full h-2 w-2 bg-red-600 absolute top-0 right-0"/>}
                </div>

                <div className="w-full text-sm whitespace-nowrap overflow-hidden transition-all duration-200 flex flex-row justify-between items-center">
                  <span>{label}</span>
                  {hasSubItems ? (
                          <motion.div
                                  animate={{rotate: isSubNavExpanded ? 0 : 180}}
                                  transition={{duration: 0.15, ease: "easeInOut"}}
                          >
                            <ChevronUp size={16}/>
                          </motion.div>
                  ) : null}
                </div>
              </div>
            </Link>
            {hasSubItems && isNavExpanded && (
                    <AnimatePresence initial={false}>
                      {isSubNavExpanded && (
                              <motion.div
                                      key="content"
                                      initial={{height: 0, opacity: 0, overflow: "hidden"}}
                                      animate={{
                                        height: "auto",
                                        opacity: 1,
                                        transition: {
                                          height: {duration: 0.15, ease: "easeOut"},
                                          opacity: {duration: 0.15, ease: "easeOut"}
                                        }
                                      }}
                                      exit={{
                                        height: 0,
                                        opacity: 0,
                                        transition: {
                                          height: {duration: 0.15, ease: "easeIn"},
                                          opacity: {duration: 0.1, ease: "easeIn"}
                                        }
                                      }}
                              >
                                <div className={"flex-col gap-2 items-center relative"}>
                                  {map(subItems, ({label, href, notificationCount, activeOptions}) => {
                                    return <Link key={href} from={Route.fullPath} to={href}
                                                 activeOptions={{exact: activeOptions?.exact ?? false}}
                                                 activeProps={{className: "text-muted-foreground bg-gioaBlue/20 hover:bg-gioaBlue/40 hover:text-white"}}
                                                 inactiveProps={{className: "text-muted-foreground hover:bg-gioaBlue/40 hover:text-white"}}
                                                 className={"ml-8 mt-1 rounded-xl p-2 flex items-center gap-3 justify-between"}>
                                    <span className="text-sm whitespace-nowrap overflow-hidden transition-all duration-200">
                                        {label}
                                    </span>
                                      {notificationCount ? <Badge size={"sm"} colorScheme={"destructive"}
                                                                  className={"px-1"}>
                                        {notificationCount}
                                      </Badge> : null}
                                    </Link>
                                  })}
                                </div>
                              </motion.div>
                      )}
                    </AnimatePresence>
            )}
          </div>
  );
};
