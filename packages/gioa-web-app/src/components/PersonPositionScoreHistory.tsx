import React from 'react';
import {isEmpty, map} from 'lodash';
import {MetricDataPoint} from '@gioa/api/src/schemas';
import {Text} from "@/src/components/Text.tsx";
import {MetricDataPointNote} from "@/src/components/MetricDataPointNote.tsx";

export interface PersonPositionScoreHistoryProps {
  dataPoints: MetricDataPoint[];
  aggregateValue?: number;
  className?: string;
}

export const PersonPositionScoreHistory: React.FC<PersonPositionScoreHistoryProps> = ({
                                                                                        dataPoints,
                                                                                        aggregateValue,
                                                                                        className
                                                                                      }) => {
  return (
          <div className={`flex-1 overflow-auto ${className || ''}`}>
            {isEmpty(dataPoints) ? (
                    <Text muted>
                      No scores have been recorded yet.
                    </Text>
            ) : (
                    <>
                      <div className="mb-3">
                        <Text size="lg" semibold>
                          Score History
                        </Text>
                      </div>
                      <div className="mb-1">
                        <Text bold>
                          Aggregate score: {(aggregateValue ?? 0).toFixed(0)}%
                        </Text>
                      </div>
                      <Text size="sm" muted className="mb-6">
                        The aggregate score is the average of the most recent score given by each leader.
                      </Text>

                      <div className="flex flex-col gap-1">
                        {map(dataPoints, dataPoint => (
                                <MetricDataPointNote dataPoint={dataPoint} key={dataPoint.id}/>
                        ))}
                      </div>
                    </>
            )}
          </div>
  );
};
