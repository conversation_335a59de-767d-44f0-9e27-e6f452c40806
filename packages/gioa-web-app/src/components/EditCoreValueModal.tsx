import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/src/components/ui/dialog";
import { Text } from "@/src/components/Text";
import { Button } from "@/src/components/ui/button";
import { api } from "@/src/api";
import { Label } from "@/src/components/ui/label.tsx";
import { FormInput } from "@/src/components/form/FormInput.tsx";
import { FieldInfo } from "@/src/components/form/FieldInfo.tsx";
import { FormControl } from "@/src/components/form/FormControl.tsx";
import { useForm } from "@tanstack/react-form";
import { find } from "lodash";
import { toast } from "sonner";
import { z } from "zod";
import { zodValidator } from "@tanstack/zod-form-adapter";
import { ErrorAlert } from "@/src/components/ErrorAlert.tsx";
import { useEffect } from "react";

export function EditCoreValueModal({
  coreValueId,
  isOpen,
  onOpenChange,
}: {
  coreValueId: string;
  isOpen: boolean;
  onOpenChange: (isOpen: boolean) => void;
}) {
  const apiUtil = api.useUtils();

  const [business] = api.user.getBusiness.useSuspenseQuery();

  const coreValue = find(business.coreValues, (a) => a.id === coreValueId);
  const isNewCoreValue = !coreValue;

  const upsertCoreValue = api.user.upsertCoreValue.useMutation({
    onSuccess: async () => {
      await apiUtil.user.getBusiness.invalidate();
      toast.success(isNewCoreValue ? "Core value created successfully" : "Core value updated successfully");
      onOpenChange(false);
    },
    onError: (error) => {
      toast.error(`Failed to ${isNewCoreValue ? "create" : "update"} core value: ${error.message}`);
    },
  });

  const deleteCoreValue = api.user.deleteCoreValue.useMutation({
    onSuccess: async () => {
      await apiUtil.user.getBusiness.invalidate();
      toast.success("Core value deleted successfully");
      onOpenChange(false);
    },
    onError: (error) => {
      toast.error(`Failed to delete core value: ${error.message}`);
    },
  });

  const handleDelete = () => {
    if (confirm("Are you sure you want to delete this core value?")) {
      deleteCoreValue.mutate({ id: coreValueId });
    }
  };

  const form = useForm({
    defaultValues: {
      title: coreValue?.title || "",
    },
    onSubmit: ({ value }) => {
      upsertCoreValue.mutate(
        {
          id: coreValueId,
          title: value.title,
        },
        {
          onSuccess: async () => {
            await apiUtil.user.getBusiness.invalidate();
            onOpenChange(false);
          },
        },
      );
    },
    validatorAdapter: zodValidator(),
  });

  useEffect(() => {
    if (isOpen) {
      form.reset({
        title: coreValue?.title || "",
      });
    }
  }, [coreValue, isOpen]);

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>
            <Text size="lg" semibold>
              {isNewCoreValue ? "Create Core Value" : "Edit Core Value"}
            </Text>
          </DialogTitle>
        </DialogHeader>

        {upsertCoreValue.isError ? <ErrorAlert error={upsertCoreValue.error} /> : null}

        <div className="space-y-4 ">
          <form
            onSubmit={(e) => {
              e.preventDefault();
              e.stopPropagation();
              form.handleSubmit();
            }}
            className="space-y-4"
          >
            <form.Field
              name="title"
              validators={{
                onSubmit: z.string().min(1, "Required"),
              }}
            >
              {(field) => (
                <FormControl>
                  <Label htmlFor={field.name}>Title</Label>
                  <FormInput field={field} placeholder="Add title" />
                  <FieldInfo field={field} />
                </FormControl>
              )}
            </form.Field>

            <div className="flex justify-between pt-4">
              <div>
                {!isNewCoreValue && (
                  <Button type="button" variant="destructive" onClick={handleDelete}>
                    Delete
                  </Button>
                )}
              </div>
              <div className="flex gap-2">
                <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
                  Cancel
                </Button>
                <Button type="submit" disabled={form.state.isSubmitting} isLoading={form.state.isSubmitting}>
                  {isNewCoreValue ? "Create" : "Save Changes"}
                </Button>
              </div>
            </div>
          </form>
        </div>
      </DialogContent>
    </Dialog>
  );
}
