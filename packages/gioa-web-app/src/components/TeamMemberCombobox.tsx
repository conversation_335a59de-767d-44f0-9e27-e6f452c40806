import * as React from "react";
import {ChevronsUpDown, Check} from "lucide-react";
import {Input} from "@/src/components/ui/input";
import {Button} from "@/src/components/ui/button";
import {Popover, PopoverContent, PopoverTrigger} from "@/src/components/ui/popover";
import {PersonAvatar} from "@/src/components/PersonAvatar";
import {PersonDto} from "../../../api/src/schemas";
import {cn} from "@/src/util";

export interface TeamMemberComboboxProps {
  people: PersonDto[];
  value: string; // the person ID
  onValueChange: (value: string) => void;
  className?: string;
  listClassName?: string;
}

export function TeamMemberCombobox({people, onValueChange, value, className, listClassName}: TeamMemberComboboxProps) {
  const [open, setOpen] = React.useState(false);
  const [search, setSearch] = React.useState("");
  const selectedPerson = people.find((person) => person.id === value);

  const filteredPeople = React.useMemo(() => {
    const term = search.toLowerCase();
    return people.filter((p) =>
            `${p.firstName} ${p.lastName}`.toLowerCase().includes(term)
    );
  }, [people, search]);

  return (
          <Popover open={open} onOpenChange={setOpen}>
            <PopoverTrigger asChild>
              <Button
                      variant="outline"
                      role="combobox"
                      aria-expanded={open}
                      className={cn("justify-between gap-2", className)}
              >
                {selectedPerson ? (
                        <>
                          <PersonAvatar person={selectedPerson} size="xs"/>
                          {selectedPerson.firstName} {selectedPerson.lastName}
                        </>
                ) : (
                        "Select team member..."
                )}
                <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50"/>
              </Button>
            </PopoverTrigger>

            <PopoverContent
                    className="w-[300px] p-2"
                    align="start"
                    onWheel={(e) => e.stopPropagation()} // prevent parent components from handling scroll events
            >
              <Input
                      placeholder="Search team members..."
                      value={search}
                      onChange={(e) => setSearch(e.target.value)}
                      className="mb-2"
              />

              <div className={cn("max-h-[40vh] overflow-y-auto", listClassName)}>
                {filteredPeople.length > 0 ? (
                        filteredPeople.map((person) => (
                                <div
                                        key={person.id}
                                        onClick={() => {
                                          onValueChange(person.id!);
                                          setOpen(false);
                                        }}
                                        className={cn(
                                                "flex items-center px-2 py-2 rounded hover:bg-accent cursor-pointer",
                                                value === person.id && "bg-accent text-accent-foreground"
                                        )}
                                >
                                  <Check
                                          className={cn(
                                                  "mr-2 h-4 w-4",
                                                  value === person.id ? "opacity-100" : "opacity-0"
                                          )}
                                  />
                                  <PersonAvatar person={person} className="mr-2"/>
                                  <div>
                                    {person.firstName} {person.lastName}
                                    <div className="text-muted-foreground text-sm">
                                      {person.jobTitle}
                                    </div>
                                  </div>
                                </div>
                        ))
                ) : (
                        <div className="text-sm text-muted-foreground px-2 py-4 text-center">
                          No team member found.
                        </div>
                )}
              </div>
            </PopoverContent>
          </Popover>
  );
}
