import React from 'react';
import {Card} from '@/src/components/ui/card';
import {Text} from '@/src/components/Text';
import {ChevronRightIcon, XIcon} from 'lucide-react';

interface ChecklistItemFormRowProps {
  icon: React.ReactNode;
  label: string;
  onPress?: () => void;
  onRemove?: () => void;
}

export function ChecklistItemFormRow({
  icon,
  label,
  onPress,
  onRemove
}: ChecklistItemFormRowProps) {
  return (
    <Card className="p-4 mb-3 border border-gray-200">
      <div className="flex items-center justify-between">
        <div
          className="flex items-center flex-1"
          onClick={onPress}
          style={{ cursor: onPress ? 'pointer' : 'default' }}
        >
          <div className="mr-3">{icon}</div>
          <Text className="flex-1">{label}</Text>
        </div>

        <div className="flex items-center gap-3">
          {onPress && (
            <ChevronRightIcon size={20} className="text-gray-400" />
          )}

          {onRemove && (
            <button
              onClick={onRemove}
              className="text-gray-400 hover:text-gray-600"
              type="button"
            >
              <XIcon size={18} />
            </button>
          )}
        </div>
      </div>
    </Card>
  );
}
