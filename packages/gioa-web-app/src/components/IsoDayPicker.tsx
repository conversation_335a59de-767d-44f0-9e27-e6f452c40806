import * as React from "react"
import {getISODay, getISOWeek, getISOWeekYear} from "date-fns"
import {IsoWeekDate} from "../../../api/src/timeSchemas.ts";
import {getDateTimeFromWeekDayTime} from "../../../api/src/date.util.ts";
import {DateTime} from "luxon";
import {DatePicker} from "@/src/components/DatePicker.tsx";

export interface DayPickerProps extends Omit<React.HTMLAttributes<HTMLDivElement>, "value" | "onChange"> {
  value: IsoWeekDate;
  onChange: (date: IsoWeekDate) => void;
  today: DateTime;
}

export function IsoDayPicker({value, onChange, today}: DayPickerProps) {

  const onSelect = (date: DateTime | undefined) => {
    if (!date) {
      return;
    }
    const day = date.toJSDate();
    if (day) {
      onChange({
        week: getISOWeek(day),
        year: getISOWeekYear(day),
        day: getISODay(day)
      });
    }
  }

  const selectedDay = value ? getDateTimeFromWeekDayTime({
    year: value.year,
    week: value.week,
    day: value.day || 1,
    time: "00:00",
    timezone: "local"
  }) : undefined;

  return (
    <DatePicker value={selectedDay} onChange={onSelect} today={today}/>
  );
}
