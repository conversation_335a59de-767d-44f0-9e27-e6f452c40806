import React, {useEffect} from 'react';
import {Alert, AlertDescription} from "@/src/components/ui/alert.tsx";
import {AlertCircle, X} from "lucide-react";
import {capitalize, isEmpty, isObjectLike, map} from "lodash";
import {useDisclosure} from '../hooks/useDisclosure';
import {Button} from "@/src/components/ui/button.tsx";
import {cn} from "@/src/util.ts";

export interface ErrorAlertProps {
  error?: any;
  className?: string;
}

export function getHumanReadableErrorMessageString(error: any): string {
  if (!error) {
    return "";
  }

  if (error.data?.zodError) {
    const formErrors = !isEmpty(error.data.zodError.formErrors)
      ? error.data.zodError.formErrors.join(", ")
      : "";

    const fieldErrors = !isEmpty(error.data.zodError.fieldErrors)
      ? Object.entries(error.data.zodError.fieldErrors)
        .map(([k, v]) => `${capitalize(k)}: ${(v as string[]).join(", ")}`)
        .join("\n")
      : "";

    return [formErrors, fieldErrors].filter(Boolean).join("\n");
  }

  if (error.response) {
    const response = error.response.data as any;
    if (isObjectLike(response?.errors)) {
      return map(response.errors, (v, k) => `${k}: ${v}`).join("\n");
    }

    return String(response);
  } else if (error.message) {
    return error.message;
  } else {
    try {
      const errJson = JSON.parse(error);
      return getHumanReadableErrorMessage(errJson);
    } catch (e) {
      return "An unknown error occurred";
    }
  }
}

export function getHumanReadableErrorMessage(error: any) {
  if (!error) {
    return "";
  }

  if (error.data?.zodError) {
    return <div className={"flex flex-col gap-3"}>
      {!isEmpty(error.data.zodError.formErrors) ? <div>
        {map(error.data.zodError.formErrors, (v, k) => (
          <p key={k}>{v}</p>
        ))}
      </div> : null}
      {!isEmpty(error.data.zodError.fieldErrors) ?
        map(error.data.zodError.fieldErrors, (v, k) => (
          <p key={k}><span className={"font-bold"}>{capitalize(k)}</span>: {v.join(", ")}</p>
        )) : null}
    </div>
  }

  if (error.response) {
    const response = error.response.data as any;
    // if this is a .NET validation error
    if (isObjectLike(response?.errors)) {
      return map(response.errors, (v, k) => `${k}: ${v}`).join("\n");
    }

    return response;
  } else if (error.message) {
    return error.message;
  } else {
    return "An unknown error occurred";
  }
}

function sanitize(error: any) {
  if (typeof error === "string") {
    return error;
  }
  if (React.isValidElement(error)) {
    return error;
  }
  return JSON.stringify(error);
}


export const ErrorAlert: React.FC<ErrorAlertProps> = ({
                                                        className,
                                                        error,
                                                      }) => {
  const errorDetail = typeof error === "string" ? error : sanitize(getHumanReadableErrorMessage(error));
  const {isOpen, onClose, onOpen} = useDisclosure(true);

  useEffect(() => {
    onOpen();
  }, [error]);

  if (!isOpen) {
    return null;
  }

  return (
    <Alert variant="destructive" className={cn("px-4 py-2", className)}>
      <AlertCircle className="h-4 w-4"/>
      <AlertDescription>
        {errorDetail}
      </AlertDescription>

      <Button onClick={onClose} variant="ghost" size={"iconSm"} className="ml-auto" aria-label={"Close"}>
        <X size={20}/>
      </Button>
    </Alert>
  );
}
