import React from 'react';
import {<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON>eader, DialogTitle} from "@/src/components/ui/dialog.tsx";
import {Button} from "@/src/components/ui/button.tsx";
import {Text} from "@/src/components/Text.tsx";
import {api} from "@/src/api.ts";
import {getHumanReadableErrorMessageString} from "@/src/components/ErrorAlert.tsx";
import warningAnimation from "@/src/assets/warning.lottie";
import {DotLottieReact} from '@lottiefiles/dotlottie-react';

interface PastDueDialogProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  gracePeriodDaysRemaining: number;
  gracePeriodEndsAt: Date | undefined;
  storeId: string;
}

export function PastDueDialog({
                                isOpen,
                                onOpenChange, gracePeriodEndsAt,
                                gracePeriodDaysRemaining,
                                storeId,
                              }: PastDueDialogProps) {
  const startCustomerPortalSession = api.payment.startCustomerPortalSession.useMutation();

  const handleManageSubscription = () => {
    startCustomerPortalSession.mutate({
      storeId: storeId,
    }, {
      onSuccess: (data) => {
        window.location.href = data.url;
      },
      onError: (error) => {
        alert("Failed to start customer portal: " + getHumanReadableErrorMessageString(error));
      }
    });
  };

  const handleClose = () => {
    onOpenChange(false);
  };

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent size="lg">
        <DialogHeader>
          <DialogTitle>Subscription Past Due</DialogTitle>
        </DialogHeader>

        <DotLottieReact className={"h-[140px]"}
                        src={warningAnimation}
                        speed={1}
                        autoplay
        />

        <div className="space-y-3">
          <Text>
            Your Nation subscription payment is past due.
          </Text>

          {gracePeriodDaysRemaining > 0 ? (
            <>
              <Text className="font-medium text-orange-700">
                You have {gracePeriodDaysRemaining} day{gracePeriodDaysRemaining !== 1 ? 's' : ''} left in your grace
                period.
              </Text>
              <Text>
                To avoid any interruption to your Nation service, please update your payment method as soon as possible.
                If payment is not received within the grace period, your access may be suspended.
              </Text>
            </>
          ) : (
            <>
              <Text className="font-medium text-red-700">
                Your grace period ended on {gracePeriodEndsAt?.toLocaleDateString()}.
                Your access to Nation may be suspended at any time.
              </Text>
              <Text>
                Please update your payment method immediately to restore your subscription and avoid service interruption.
              </Text>
            </>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={handleClose} disabled={startCustomerPortalSession.isPending}>
            Close
          </Button>
          <Button
            onClick={handleManageSubscription}
            isLoading={startCustomerPortalSession.isPending}
            disabled={startCustomerPortalSession.isPending}
            className="bg-orange-600 hover:bg-orange-700"
          >
            Manage Subscription
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
