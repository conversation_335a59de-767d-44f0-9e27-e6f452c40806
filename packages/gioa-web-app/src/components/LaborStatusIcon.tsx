import React from 'react';
import {LaborStatus} from "../../../api/src/personMetrics.ts";
import {cn} from "@/src/util.ts";

export interface LaborStatusIconProps {
  laborStatus: LaborStatus;
  size?: number;
  showFullLabel?: boolean;
  className?: string;
}

export const LaborStatusIcon: React.FC<LaborStatusIconProps> = ({laborStatus, size = 24, showFullLabel, className}) => {
  return laborStatus === "14/15 yr old"
    ? <span className={cn("bg-red-100 text-red-700 rounded-md px-1 py-0.5 text-xs", className)}>
      14/15 {showFullLabel ? "yr old" : ""}
    </span>
    : laborStatus === "16/17 yr old"
      ? <span className={cn("bg-yellow-100 text-yellow-800 rounded-md px-1 py-0.5 text-xs", className)}>
        16/17 {showFullLabel ? "yr old" : ""}
      </span>
      : null
}
