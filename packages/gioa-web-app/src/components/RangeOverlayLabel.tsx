import React from 'react';
import {cn} from "@/src/util.ts";
import {to12HourTime} from "../../../api/src/date.util.ts";
import {DailyTimeRange} from "../../../api/src/timeSchemas.ts";

export interface RangeOverlayLabelProps {
  range: DailyTimeRange;
  showAvail: boolean;
  isAllDay: boolean;
  className?: string;
  labelClassName?: string;
  showClassName?: string;
  label?: string;
}

export const RangeOverlayLabel: React.FC<RangeOverlayLabelProps> = ({
                                                                      range, isAllDay, showAvail,
                                                                      label = "Avail.", showClassName = "opacity-70",
                                                                      className, labelClassName
                                                                    }) => {
  return <div className={cn("absolute -top-1 left-0 right-0 flex items-center justify-center", className)}>
    <div
      className={cn("bg-white rounded-md px-1 py-0.5 text-xs border border-gray-200 transition-opacity", showAvail ? showClassName : "opacity-0", labelClassName)}>
      {label} {isAllDay ? "all day" :
      `${to12HourTime(range.start, true)} - ${to12HourTime(range.end, true)}`}
    </div>
  </div>
}
