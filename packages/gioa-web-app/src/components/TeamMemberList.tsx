import React, {useEffect, useMemo, useState} from 'react';
import {filter, isEmpty, map} from "lodash";
import {TeamMemberListItem} from "@/src/components/TeamMemberListItem.tsx";
import {BaseSchedule, HawaiiACAPersonHours, Shift} from "../../../api/src/scheduleSchemas.ts";
import {DailyTimeRange, DayOfWeek} from "../../../api/src/timeSchemas.ts";
import {StoreAreaDto} from "../../../api/src/schemas.ts";
import {ShiftSuggestion} from "../../../api/src/shiftSuggestion.ts";
import {SchedulePersonClientDto} from "../../../api/src/schedulePersonDto.ts";
import {getPersonTotalWeekHours} from "../../../api/src/scheduleBuilder.util.ts";
import {filterPeopleByStoreAreaTraining} from "@/src/availabilityReport.ts";
import {ScheduleValidationSettings} from "../../../api/src/scheduleValidation.types.ts";

export interface TeamMemberListProps {
  schedule: BaseSchedule;
  people: SchedulePersonClientDto[];
  dayOfWeek: DayOfWeek;
  selectedShiftId: string | null;
  filterAreaId: string;
  searchInput: string;
  storeAreas: StoreAreaDto[];
  storeHours: DailyTimeRange;
  onSelectTeamMember: (person: SchedulePersonClientDto) => void;
  onViewDetails: (person: SchedulePersonClientDto) => void;
  isSelecting: boolean;
  shift?: Shift;
  timezone: string;
  settings: ScheduleValidationSettings;
  weeksHoursMap: HawaiiACAPersonHours;
  storeState?: string;
}

export const TeamMemberList = React.memo(({
                                            people,
                                            dayOfWeek,
                                            selectedShiftId,
                                            schedule, filterAreaId,
                                            searchInput, storeHours,
                                            storeAreas,
                                            onSelectTeamMember, settings,
                                            isSelecting, shift,
                                            onViewDetails, timezone,
                                            weeksHoursMap,
                                            storeState = 'Unknown'
                                          }: TeamMemberListProps) => {
  const relevantPeople = useMemo(() => {
    return filterPeopleByStoreAreaTraining({people, filterAreas: [filterAreaId], storeAreas});
  }, [people, filterAreaId]);

  const filteredPeople = useMemo(() => {
    return filter(relevantPeople, person => {
      return person.firstName.toLowerCase().includes(searchInput.toLowerCase()) || person.lastName.toLowerCase().includes(searchInput.toLowerCase());
    });
  }, [relevantPeople, searchInput]);

  const [scoredPeople, setScoredPeople] = useState<ShiftSuggestion[]>([]);
  const peopleToRender = useMemo(() => {
    if (!selectedShiftId) {
      return map(filteredPeople, p => ({person: p, score: 0, reasons: []}));
    }
    return scoredPeople;
  }, [scoredPeople, filteredPeople, selectedShiftId]);

  const [worker, setWorker] = useState<Worker | null>(null);

  useEffect(() => {
    // Initialize worker
    const w = new Worker(new URL('../shiftSuggestionWorker.ts', import.meta.url), {
      type: "module"
    });
    setWorker(w);

    return () => {
      w.terminate();
    };
  }, []);

  useEffect(() => {
    if (!worker || !selectedShiftId) {
      return;
    }

    // run the shift suggestion on a web worker to avoid blocking the UI
    worker.postMessage({
      schedule,
      dayOfWeek,
      storeAreas,
      selectedShiftId,
      people: filteredPeople,
      timezone,
      settings,
      weeksHoursMap,
      storeState
    });

    const messageHandler = (e: MessageEvent) => {
      setScoredPeople(e.data);
    };
    worker.addEventListener('message', messageHandler);

    return () => {
      worker.removeEventListener('message', messageHandler);
    };
  }, [filteredPeople, selectedShiftId, schedule, dayOfWeek, storeAreas, timezone, worker, settings, weeksHoursMap]);

  return (
    <ul className="overflow-y-auto flex-grow">
      {isEmpty(peopleToRender) ?
        <div className={"text-muted-foreground text-center text-sm py-6"}>No team members match your
          filters.</div> : null}
      {map(peopleToRender, ({person, score, reasons}) => {
        const personTotalWeekHours = getPersonTotalWeekHours(schedule, person);

        return (
          <TeamMemberListItem key={person.id} person={person} onSelectTeamMember={onSelectTeamMember}
                              dayOfWeek={dayOfWeek} storeHours={storeHours}
                              reasons={reasons} score={score} shiftId={shift?.id}
                              personTotalWeekHours={personTotalWeekHours}
                              isSelecting={isSelecting} onViewDetails={onViewDetails}/>
        );
      })}
    </ul>
  );
});

TeamMemberList.displayName = "TeamMemberList";
