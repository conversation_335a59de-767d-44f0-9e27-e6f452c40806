import React, {use<PERSON>emo} from 'react';
import {ReactFormExtendedApi, Validator} from '@tanstack/react-form';
import {z, ZodType} from 'zod';
import {ShiftActivityType} from "../../../api/src/scheduleSchemas.ts";
import {FormControl} from "@/src/components/form/FormControl.tsx";
import {Label} from "@/src/components/ui/label.tsx";
import {FormInput} from "@/src/components/form/FormInput.tsx";
import {FieldInfo} from "@/src/components/form/FieldInfo.tsx";
import {api} from "@/src/api.ts";
import * as SP from "../../../api/src/setupSheets/storeSetupPositions/storeSetupPositions"
import {FormSelect} from "@/src/components/form/FormSelect.tsx";
import {groupBy, map} from "lodash";
import {SelectGroup, SelectItem, SelectLabel} from "@/src/components/ui/select.tsx";

export interface ShiftActivityTypeFormProps {
  form: ReactFormExtendedApi<any, Validator<unknown, ZodType>>;
  storeId: string;
}

/**
 * Holds the form fields specific to a specific shift activity type. E.g. "admin" type will have a title field, whereas "setups" type will have a setup position chooser.
 * @param props
 * @constructor
 */
export const ShiftActivityTypeForm: React.FC<ShiftActivityTypeFormProps> = ({form, storeId}) => {
  const [storeSetupPositions] = api.setups.getStoreSetupPositions.useSuspenseQuery({storeId})
  const [store] = api.user.getStoreAdmin.useSuspenseQuery({storeId: storeId}, {staleTime: 1000 * 60 * 60});
  const activityType = form.useStore(s => s.values.activityType as ShiftActivityType | undefined);

  const setupPositionOptions = useMemo(() => {
    const positions = SP.toArray({
      storeSetupPositions: storeSetupPositions,
      storeAreas: store.areas
    });

    return groupBy(positions, pos => pos.areaTitle || "(no area)");
  }, [storeSetupPositions]);

  if (!activityType) {
    return null;
  }

  switch (activityType) {
    case "training":
    case "custom":
    case "admin":
      return <>
        <form.Field name={`title`}

                    children={(field) => {
                      return <FormControl>
                        <Label htmlFor={field.name}>Title</Label>
                        <FormInput field={field}
                                   placeholder="Title visible to the Team Member..."/>
                        <FieldInfo field={field}/>
                      </FormControl>;
                    }}/>
      </>
    case "setups":
      return <>
        <form.Field name={"setupPositionTitle"}
                    validators={{
                      onSubmit: z.string("Required").min(1, "Required")
                    }}
                    children={(field) => {
                      return <FormControl>
                        <Label htmlFor={field.name}>Setup Position</Label>
                        <FormSelect field={field}
                                    isClearable
                                    placeholder={"Select a position..."}>
                          {map(setupPositionOptions, (positions, areaTitle) => {
                            return <SelectGroup key={areaTitle}>
                              <SelectLabel>{areaTitle}</SelectLabel>
                              {map(positions, pos =>
                                      <SelectItem value={pos.title}
                                                  key={pos.title}>
                                        {pos.title}
                                      </SelectItem>)}
                            </SelectGroup>
                          })}
                        </FormSelect>
                        <FieldInfo field={field}/>
                      </FormControl>
                    }}
        />
      </>
    case "breaks":
      return <>
        <form.Field name={`title`}
                    validators={{
                      onSubmit: z.string().min(1, "Required")
                    }}
                    children={(field) => {
                      return <FormControl>
                        <Label htmlFor={field.name}>Title</Label>
                        <FormInput field={field}
                                   placeholder="Title visible to the Team Member..."/>
                        <FieldInfo field={field}/>
                      </FormControl>;
                    }}/>
        <form.Field name={"payStatus"}
                    validators={{
                      onSubmit: z.string("Required").min(1, "Required")
                    }}
                    children={(field) => {
                      return <FormControl>
                        <Label htmlFor={field.name}>Break Pay</Label>
                        <FormSelect field={field}>
                          <SelectGroup>
                            <SelectItem value={"paid"}>Paid</SelectItem>
                            <SelectItem value={"unpaid"}>Unpaid</SelectItem>
                          </SelectGroup>
                        </FormSelect>
                        <FieldInfo field={field}/>
                      </FormControl>
                    }}
        />
      </>
  }
}
