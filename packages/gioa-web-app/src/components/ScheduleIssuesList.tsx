import React from 'react';
import {find, groupBy, isEmpty, map, sortBy} from "lodash";
import {ChevronRightIcon, SmileIcon} from "lucide-react";
import {Accordion, AccordionContent, AccordionItem, AccordionTrigger} from "@/src/components/ui/accordion.tsx";
import {ValidationMessageIcon} from "@/src/components/ValidationMessageIcon.tsx";
import {ValidationMessage} from "../../../api/src/scheduleValidation.types.ts";
import {PersonAvatar} from "@/src/components/PersonAvatar.tsx";
import {BaseSchedule} from '@gioa/api/src/scheduleSchemas.ts';
import {DailyTimeRange, IsoWeekDate} from "../../../api/src/timeSchemas.ts";
import {useIgnoreScheduleValidationMessages} from "@/src/hooks/useIgnoreScheduleValidationMessages.tsx";
import {daysOfWeek} from "@/src/components/AvailabilityWeek.tsx";
import {cn} from "@/src/util.ts";
import {useAutoAnimate} from "@formkit/auto-animate/react";
import {AnimatePresence, motion} from "framer-motion";
import {SchedulePersonDto} from "../../../api/src/schedulePersonDto.ts";
import {
  getHumanReadableMessageCodeSummary,
  incrementToFriendlyTime,
  messageSeverityToLabel,
  storeTimeToIncrement
} from "../../../api/src/scheduleBuilder.util.ts";
import {
  getMessageDayOfWeek,
  getMessagePersonId,
  getMessageShift,
  sortBySeverity
} from "../../../api/src/scheduleValidation/scheduleValidationMessagesL1.ts";
import {Button} from "@/src/components/ui/button.tsx";
import {messageToHumanReadable} from "@/src/validationMessageDisplay.tsx";

export interface ScheduleDayIssuesProps {
  warnings: ValidationMessage[];
  schedule: BaseSchedule;
  people: SchedulePersonDto[];
  storeHours: DailyTimeRange;
  onGoToMessage: (week: IsoWeekDate, dayOfWeek: number, msg: ValidationMessage) => void;
  onIgnoreMessage: (msg: ValidationMessage) => void;
  onUnignoreMessage: (msg: ValidationMessage) => void;
  showDay?: boolean;
  className?: string;
  NoIssuesComponent?: React.ReactNode;
  noIssuesLabel?: string;
  showTimes?: boolean;
}

export const ScheduleIssuesList: React.FC<ScheduleDayIssuesProps> = ({
                                                                       schedule,
                                                                       people,
                                                                       className,
                                                                       warnings,
                                                                       noIssuesLabel = "There are no issues on this day. Nice work!",
                                                                       NoIssuesComponent,
                                                                       onGoToMessage, showTimes,
                                                                       storeHours, onUnignoreMessage,
                                                                       showDay, onIgnoreMessage
                                                                     }) => {

  const sortedWarnings = sortBy(warnings ?? [], sortBySeverity, m => m.dayOfWeek, m => {
    const shift = "shift" in m.info ? m.info.shift : null;
    return shift ? shift.range.start : 0;
  });
  const codeToMessages = groupBy(sortedWarnings, w => w.code);
  const {isMessageIgnored} = useIgnoreScheduleValidationMessages({
    scheduleId: schedule.id,
    storeId: schedule.storeId,
  });
  const [ref] = useAutoAnimate();
  const getMsg = messageToHumanReadable({
    activeDay: undefined,
    routeFullPath: undefined,
    people: people,
  })

  return <div className={cn("bg-gray-100 px-3 py-4", className)}>
    <AnimatePresence mode={"popLayout"} initial={false}>
      {isEmpty(sortedWarnings)
        ? <motion.div initial={{opacity: 0, scale: 0.75}} key={"no-issues"}
                      animate={{opacity: 1, scale: 1}}
                      exit={{opacity: 0, scale: 0}}>
          {NoIssuesComponent ? NoIssuesComponent :
            <div className={"flex justify-center items-center flex-col gap-6 px-6 pb-4 pt-10"} style={{
              backgroundImage: `repeating-linear-gradient(
    -45deg,
    transparent,
    transparent 10px,
    rgba(0, 0, 0, 0.05) 10px,
    rgba(0, 0, 0, 0.05) 11px
  )`,
              backgroundSize: "16px 16px",
            }}>
              <SmileIcon size={78} className={"text-gray-500"}/>
              <div>
                {noIssuesLabel}
              </div>
            </div>}
        </motion.div>
        : <motion.div initial={{opacity: 0, scale: 0.75}} key={"issues"}
                      animate={{opacity: 1, scale: 1}}
                      exit={{opacity: 0, scale: 0}}>
          <Accordion type="multiple">
            <div ref={ref} className={"space-y-2"}>
              {map(codeToMessages, (warnings, code) => {
                const groupSeverity = codeToMessages[code][0].severity;
                return <AccordionItem value={code} key={code} className={"border-b-0"}>
                  <AccordionTrigger
                    className={"flex items-center text-left gap-2 bg-white rounded-md px-4 py-2 [&[data-state=open]]:rounded-b-none [&[data-state=open]]:border-b"}>
                    <div className={"flex items-center gap-3"}>
                      <ValidationMessageIcon severity={groupSeverity}/>
                      <div>
                        <div className={"text-md font-normal"}>
                          {getHumanReadableMessageCodeSummary(code as ValidationMessage["code"])}
                        </div>
                        <div className={"text-sm text-muted-foreground"}>
                          {warnings.length} {messageSeverityToLabel(groupSeverity)}{warnings.length > 1 ? "s" : ""}
                        </div>
                      </div>
                    </div>
                  </AccordionTrigger>
                  <AccordionContent className={"pb-0"}>
                    {map(warnings, msg => {
                      const shift = getMessageShift(msg);
                      const personId = getMessagePersonId(msg)
                      const canIgnoreInline = Boolean(personId) && !shift;
                      const person = find(people, p => p.id === personId);
                      const isIgnored = isMessageIgnored(msg);
                      const dayOfWeek = getMessageDayOfWeek(msg)
                      const dayOfWeekObj = find(daysOfWeek, d => d.dayOfWeek === dayOfWeek);

                      return <button onClick={() => dayOfWeek ? onGoToMessage(schedule.week, dayOfWeek, msg) : null}
                                     className={" text-left w-full hover:bg-gray-50 bg-white last:rounded-b-lg border-b last:border-b-0 pl-4 pr-3 py-2 flex gap-2 items-center"}
                                     key={msg.id}>
                        {showDay && dayOfWeekObj?.name ? <div className={"text-sm text-muted-foreground min-w-[5rem]"}>
                          {dayOfWeekObj?.name}
                        </div> : null}

                        {shift && showTimes ? <div className={"min-w-[6rem]"}>
                          {incrementToFriendlyTime(storeHours, storeTimeToIncrement(storeHours, shift.range.start))} -{' '}
                          {incrementToFriendlyTime(storeHours, storeTimeToIncrement(storeHours, shift.range.end))}
                        </div> : null}

                        {person ? <>
                          <PersonAvatar size={"sm"} person={person}/>
                          <div className={"grow"}>
                            <div>
                              {person.firstName} {person.lastName}
                            </div>
                            <div className={"text-sm text-muted-foreground"}>
                              {getMsg(msg)}
                            </div>
                          </div>
                        </> : shift && !showTimes ? <div className={"grow py-1"}>
                            {incrementToFriendlyTime(storeHours, storeTimeToIncrement(storeHours, shift.range.start))} - {incrementToFriendlyTime(storeHours, storeTimeToIncrement(storeHours, shift.range.end))}
                          </div>
                          : null}

                        {canIgnoreInline
                          ? isIgnored
                            ? <Button variant={"outline"} size={"sm"} title={"Unignore"}
                                      onClick={() => onUnignoreMessage(msg)}
                                      className={"flex items-center justify-center text-yellow-700 bg-yellow-100 rounded-md px-2 py-1"}>
                              Ignored
                            </Button>
                            : <Button variant={"outline"} onClick={() => onIgnoreMessage(msg)}
                                      size={"sm"}>
                              Ignore
                            </Button>
                          : isIgnored
                            ? <div
                              className={"flex items-center justify-center text-yellow-700 bg-yellow-100 rounded-md px-2 py-1"}>
                              Ignored
                            </div>
                            : null}
                        {dayOfWeek ?
                          <ChevronRightIcon size={20} className={"text-gray-500"}/> : null}
                      </button>;
                    })}
                  </AccordionContent>
                </AccordionItem>
              })}
            </div>
          </Accordion>
        </motion.div>}

    </AnimatePresence>
  </div>
}
