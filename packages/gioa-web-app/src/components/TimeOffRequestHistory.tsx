import React from 'react';
import {map} from "lodash";
import {PersonTimeOffRequestStatusBadge} from "@/src/components/PersonTimeOffRequestStatusBadge.tsx";
import {formatTimeOffDate} from "@/src/date.util.ts";
import {TimeOffAdminDto} from "../../../api/src/personTimeOff.schemas.ts";
import {Link} from "@/src/components/Link.tsx";
import {ArrowRightIcon, CalendarRangeIcon, NotebookPenIcon} from 'lucide-react';

export interface TimeOffRequestHistoryProps {
  lastRequests: TimeOffAdminDto[];
  linkFrom?: string;
  linkTo: string;
  linkSearch?: any;
  timezone: string;
}

export const TimeOffRequestHistory: React.FC<TimeOffRequestHistoryProps> = ({
                                                                              linkSearch,
                                                                              lastRequests, linkFrom, linkTo, timezone
                                                                            }) => {
  return <div className={"bg-white rounded-lg py-3"}>
    <h4 className={"font-bold mb-2 mx-4"}>
      Last 10 requests
    </h4>
    {map(lastRequests, req => {
      return <Link from={linkFrom as any}
                   search={linkSearch}
                   to={linkTo + req.id} key={req.id}
                   className={"last:border-b-0 border-b px-4 py-3 flex flex-col gap-2 items-stretch"}>
        <div className={"text-sm font-medium flex gap-2 items-center whitespace-nowrap"}>
          <PersonTimeOffRequestStatusBadge status={req.status} size={"sm"}/>
          <div className={"grow"}/>
          <ArrowRightIcon size={16} className={"text-muted-foreground"}/>
        </div>
        <div className={"text-sm flex items-center gap-2"}>
          <CalendarRangeIcon size={16} className={"text-muted-foreground"}/>
          {formatTimeOffDate(req.range.start, timezone ?? null)} - {formatTimeOffDate(req.range.end, timezone ?? null)}
        </div>
        {req.submittedReason ?
                <div className={"text-sm flex items-start gap-2"}>
                  <NotebookPenIcon size={16} className={"mt-0.5 text-muted-foreground flex-shrink-0"}/>
                  <div className={"overflow-hidden"}>
                    <span className={"line-clamp-2"}>{req.submittedReason}</span>
                  </div>
                </div> : null}
      </Link>
    })}
  </div>
}
