import React from 'react';
import {CheckIcon} from "lucide-react";

export interface ScheduleDayStatusBadgeProps {
  warningsCount: number;
  isComplete: boolean;
  isPast: boolean;
}

export const ScheduleDayStatusBadge: React.FC<ScheduleDayStatusBadgeProps> = ({warningsCount, isComplete, isPast}) => {
  return isPast ?
    <div className={"text-muted-foreground text-sm flex items-center gap-1"}>
    </div>
    : warningsCount > 0
      ? <div className={"text-red-700 text-sm flex items-center gap-1"}>
        Issues ({warningsCount})
      </div>
      : isComplete
        ? <div className={"text-green-700 text-sm flex items-center gap-1"}>
          <CheckIcon size={16}/>
          Complete
        </div> : <div className={"text-muted-foreground text-sm flex items-center gap-1"}>
          Incomplete
        </div>
}
