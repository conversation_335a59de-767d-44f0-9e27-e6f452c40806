import React from "react";
import {useForm} from "@tanstack/react-form";
import {zodValidator} from '@tanstack/zod-form-adapter';
import {z} from "zod";
import {api} from "@/src/api.ts";
import {Text} from "@/src/components/Text.tsx";
import {Button} from "@/src/components/ui/button.tsx";
import {FormControl} from "@/src/components/form/FormControl.tsx";
import {Label} from "@/src/components/ui/label.tsx";
import {FormInput} from "@/src/components/form/FormInput.tsx";
import {FieldInfo} from "@/src/components/form/FieldInfo.tsx";
import {FormDropDown} from "@/src/components/form/FormDropDown.tsx";
import {CirclePlus, Trash} from "lucide-react";
import {map} from "lodash";
import {genScheduleBreakRuleId} from "../../../api/src/schemas.ts";
import {ErrorAlert} from "@/src/components/ErrorAlert.tsx";

interface BreakRulesFormProps {
  storeId: string;
  onContinue: () => void;
  setIsLoading: (isLoading: boolean) => void;
}

export const BreakRulesForm = React.forwardRef<{ submit: () => Promise<void> }, BreakRulesFormProps>(
  ({storeId, onContinue, setIsLoading}, ref) => {
    const [defaultRequiredBreaks] = api.user.getStoreScheduleBreakRules.useSuspenseQuery({storeId});
    const updateScheduleBreakRules = api.user.updateScheduleBreakRules.useMutation({
      onSuccess: () => {
        setIsLoading(false);
      },
      onError: (error) => {
        setIsLoading(false);
      }
    });

    const defaultValues = map(defaultRequiredBreaks, (requiredBreak) => ({
      requiredStatus: requiredBreak.isRequiredByLaw ? "Required by Law" : "Preferred",
      ageGroup: requiredBreak.ageGroup,
      shiftLengthHours: requiredBreak.shiftLengthHours,
      breakLengthMinutes: requiredBreak.breakLengthMinutes,
      paidStatus: requiredBreak.isPaid ? "Paid" : "Unpaid"
    }));

    const form = useForm({
      defaultValues: {
        breakRules: defaultValues
      },
      onSubmit: async ({value, formApi}) => {
        if (formApi.state.isDirty) {
          setIsLoading(true);
          await updateScheduleBreakRules.mutateAsync({
            storeId,
            breakRules: map(value.breakRules, (breakRule) => ({
              id: genScheduleBreakRuleId(),
              isRequiredByLaw: breakRule.requiredStatus === "Required by Law",
              ageGroup: breakRule.ageGroup,
              shiftLengthHours: breakRule.shiftLengthHours,
              breakLengthMinutes: breakRule.breakLengthMinutes,
              isPaid: breakRule.paidStatus === "Paid"
            }))
          });
        }
        onContinue();
      },
      validatorAdapter: zodValidator(),
    });

    React.useImperativeHandle(ref, () => ({
      submit: form.handleSubmit,
    }));

    return (
      <div className={"flex flex-col gap-3 border-2 border-gray-100 p-3 shadow-md rounded-md"}>
        <Text size={"xl"}>Break Rules</Text>
        {updateScheduleBreakRules.error ? <div className={"my-2"}>
            <ErrorAlert error={updateScheduleBreakRules.error}/>
        </div> : null}
        <div>
          <form.Field name={"breakRules"} mode={"array"}
                      validators={{}}
                      children={arrField => {
                        return <div className={"flex flex-col gap-3 items-start"}>
                          {arrField.state.value.length === 0
                            ? <Text muted>Press "Add Break Rule" button to begin</Text>
                            : map(arrField.state.value, (requiredBreak, index) => {
                              return <div key={index} className={"flex flex-row gap-3"}>
                                <form.Field name={`breakRules[${index}].requiredStatus`}
                                            validators={{
                                              onSubmit: z.string().min(1)
                                            }}
                                            children={(field) => {
                                              return <FormControl>
                                                <Label htmlFor={field.name}>Requirement Status</Label>
                                                <FormDropDown field={field}
                                                              placeholder="Select group..."
                                                              options={[
                                                                {label: "Required by Law", value: "Required by Law"},
                                                                {label: "Preferred", value: "Preferred"},
                                                              ]}/>
                                                <FieldInfo field={field}/>
                                              </FormControl>;
                                            }}/>
                                <form.Field name={`breakRules[${index}].ageGroup`}
                                            validators={{
                                              onSubmit: z.string().min(1)
                                            }}
                                            children={(field) => {
                                              return <FormControl>
                                                <Label htmlFor={field.name}>Age</Label>
                                                <FormDropDown field={field}
                                                              placeholder="Select group..."
                                                              options={[
                                                                {label: "All Team Members", value: "All Team Members"},
                                                                {label: "18 and older", value: "18 and older"},
                                                                {label: "16 and older", value: "16 and older"},
                                                                {label: "Minors under 18", value: "Minors under 18"},
                                                                {label: "Minors under 16", value: "Minors under 16"}
                                                              ]}/>
                                                <FieldInfo field={field}/>
                                              </FormControl>;
                                            }}/>
                                <form.Field name={`breakRules[${index}].shiftLengthHours`}
                                            validators={{
                                              onSubmit: z.number().max(24).min(1)
                                            }}
                                            children={(field) => {
                                              return <FormControl>
                                                <Label htmlFor={field.name}>Shift Length (hrs)</Label>
                                                <FormInput field={field} type={"number"} convertToNumber
                                                           placeholder={"8 Hours"}/>
                                                <FieldInfo field={field}/>
                                              </FormControl>;
                                            }}/>
                                <form.Field name={`breakRules[${index}].breakLengthMinutes`}
                                            validators={{
                                              onSubmit: z.number().max(120).min(1)
                                            }}
                                            children={(field) => {
                                              return <FormControl>
                                                <Label htmlFor={field.name}>Break Length (mins)</Label>
                                                <FormInput field={field} type={"number"} convertToNumber
                                                           placeholder={"30 Minutes"}/>
                                                <FieldInfo field={field}/>
                                              </FormControl>;
                                            }}/>
                                <form.Field name={`breakRules[${index}].paidStatus`}
                                            children={(field) => {
                                              return <FormControl>
                                                <Label htmlFor={field.name}>Paid Status</Label>
                                                <FormDropDown field={field}
                                                              placeholder="Select Status..."
                                                              options={[
                                                                {label: "Paid", value: "Paid"},
                                                                {label: "Unpaid", value: "Unpaid"},
                                                              ]}/>
                                                <FieldInfo field={field}/>
                                              </FormControl>;
                                            }}/>
                                <div>
                                  <Label>Remove</Label>
                                  <Button variant={"outline"} className={"flex flex-col justify-center items-center"}
                                          onClick={() => arrField.removeValue(index)}>
                                    <Trash color={"red"} size={16} className={"mr-2 text-gray-500"}/>
                                  </Button>
                                </div>
                              </div>
                            })}
                          <Button variant={"outline"}
                                  isLoading={updateScheduleBreakRules.isPending}
                                  onClick={() => {
                                    arrField.pushValue({
                                      requiredStatus: "Required by Law",
                                      ageGroup: "All Team Members",
                                      shiftLengthHours: 0,
                                      breakLengthMinutes: 0,
                                      paidStatus: "Paid"
                                    })
                                  }}>
                            <CirclePlus size={16} className={"mr-2 text-gray-500"}/>
                            <Text size="sm" muted>Add Break Rule</Text>
                          </Button>
                        </div>
                      }}/>
        </div>
      </div>
    );
  });