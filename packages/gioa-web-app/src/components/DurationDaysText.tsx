import React from 'react';
import {Text} from "@/src/components/Text.tsx";

export interface DurationDaysTextProps {
  durationHours: number;
  className?: string;
  prefix?: string;
  size?: "xs" | "sm" | "md" | "lg";
}

export const DurationDaysText: React.FC<DurationDaysTextProps> = ({durationHours, className, prefix, size = "sm"}) => {
  const totalMinutes = Math.floor(durationHours * 60);

  const roundUpIfCloseToNextDay = (minutes: number) => {
    const daysExact = minutes / (24 * 60);
    const daysFloor = Math.floor(daysExact);
    const remainingMinutes = minutes - (daysFloor * 24 * 60);

    if (remainingMinutes >= 1438) { // Within 2 minutes of next day
      return daysFloor + 1;
    }
    return daysFloor;
  };

  const days = roundUpIfCloseToNextDay(totalMinutes);
  const remainingMinutes = totalMinutes - (days * 24 * 60);
  const hours = Math.floor(remainingMinutes / 60);
  const minutes = remainingMinutes % 60;

  const mainTextSize = size;
  const secondaryTextSize = size === "sm"
    ? "xs"
    : size === "md"
      ? "sm"
      : size === "xs"
        ? "xs"
        : "md";

  return (
    <Text size={mainTextSize} className={className} center asChild={"span"}>
      {prefix ?? ""}
      {days > 0 && (
        <>
          {days}<Text size={secondaryTextSize} className={className} asChild={"span"}>d</Text>
        </>
      )}
      {hours > 0 && (
        <>
          {days > 0 ? " " : ""}
          {hours}<Text size={secondaryTextSize} className={className} asChild={"span"}>h</Text>
        </>
      )}
      {minutes > 0 && (
        <>
          {" "}
          {minutes}<Text size={secondaryTextSize} className={className} asChild={"span"}>m</Text>
        </>
      )}
    </Text>
  );
}
