import React, {useState} from 'react';
import {Text} from "@/src/components/Text.tsx";
import {Input} from "@/src/components/ui/input.tsx";
import {Button} from "@/src/components/ui/button.tsx";
import {Checkbox} from "@/src/components/ui/checkbox.tsx";
import {X} from "lucide-react";
import {Heading} from "@/src/components/Heading.tsx";
import {useForm} from "@tanstack/react-form";
import {map} from "lodash";
import {api} from "@/src/api.ts";
import {zodValidator} from '@tanstack/zod-form-adapter'
import {z} from "zod";
import {FormInput} from "@/src/components/form/FormInput.tsx";
import {FormCheckbox} from "@/src/components/form/FormCheckbox.tsx";
import {Clock, Trash, CirclePlus} from "lucide-react";
import {ErrorAlert} from "@/src/components/ErrorAlert.tsx";

export interface SettingsActivityLogProps {
  storeId: string;
}

export const SettingsActivityLog: React.FC<SettingsActivityLogProps> = ({storeId}) => {
  const apiUtil = api.useUtils();
  return (
    <div className="">
      <Heading level={1}>SettingsActivityLog</Heading>
      <div className="flex flex-col">
        <Text semibold>SettingsActivityLog :)</Text>
      </div>
    </div>
  );
}