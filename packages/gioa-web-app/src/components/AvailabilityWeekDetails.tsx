import {Text} from "@/src/components/Text.tsx";
import {Calendar as CalendarIcon} from "lucide-react";
import {useDaysOfWeek} from "../../../api/src/daysOfWeek.ts";
import {filter, isEmpty, map, reduce} from "lodash";
import React from "react";
import {to12HourTime} from "../../../api/src/date.util.ts";
import {getDurationHours} from "../../../api/src/timeSchemas.util.ts";
import {weekDayTimeRange} from "../../../api/src/timeSchemas.ts";
import {z} from "zod";

export function AvailabilityWeekDetails({
                                          availabilityData,
                                          onDayClick,
                                        }: {
  availabilityData: {ranges: z.infer<typeof weekDayTimeRange>[]};
  onDayClick?: (dayOfWeek: number) => void;
}) {
  const daysOfWeek = useDaysOfWeek();

  return (
          <div className="flex flex-col gap-2">
            <div className="flex flex-row gap-2">
              <div className="h-10 w-14 border border-gray-300 flex flex-row rounded-lg justify-center items-center">
                <CalendarIcon/>
              </div>
              <div className="h-10 flex-1 border border-gray-300 flex flex-row rounded-lg justify-center items-center">
                <Text center>Availability</Text>
              </div>
            </div>

            {map(daysOfWeek, (day) => {
              const ranges = filter(availabilityData?.ranges, (r) => r.dayOfWeek === day.dayOfWeek);
              const noAvailability = isEmpty(ranges);

              const totalHours = reduce(
                      ranges,
                      (acc, range) => acc + getDurationHours(range),
                      0
              );
              const totalHoursRounded = totalHours ? Math.round(totalHours * 10) / 10 : null;

              return (
                      <div key={day.abbr} className="flex flex-row gap-2">
                        <div className="h-14 w-14 border border-gray-300 flex flex-row rounded-lg justify-center items-center">
                          <Text>{day.abbr}</Text>
                        </div>

                        <button
                                type="button"
                                onClick={() => onDayClick?.(day.dayOfWeek)}
                                className={`
                h-14 flex-1 border border-gray-300 flex flex-row rounded-lg justify-start items-center
                border-l-8 ${noAvailability ? 'border-l-gray-300' : 'border-l-green-500'}
                px-2 overflow-x-auto hover:bg-gray-50
                text-left
              `}
                        >
                          {noAvailability ? (
                                  <Text className="ml-4 text-sm">
                                    No Availability
                                  </Text>
                          ) : (
                                  <div className="flex flex-col ml-4">
                                    <div className="flex flex-row gap-2 items-center">
                                      {map(ranges, (range, idx) => (
                                              <Text key={idx} className={`text-sm ${idx > 0 ? 'border-l border-gray-300 pl-2' : ''}`}>
                                                {to12HourTime(range.start)} - {to12HourTime(range.end)}
                                              </Text>
                                      ))}
                                    </div>
                                    {totalHoursRounded && (
                                            <Text className="text-sm">
                                              {totalHoursRounded}h
                                            </Text>
                                    )}
                                  </div>
                          )}
                        </button>
                      </div>
              );
            })}


          </div>
  );
}
