import React from 'react';
import {VariantProps, cva} from "class-variance-authority";
import {cn} from "@/src/util.ts";

export interface HeadingProps extends VariantProps<typeof headingVariants>{
  level: 1 | 2 | 3 | 4 | 5 | 6;
  children: React.ReactNode;
  className?: string;
}

const headingVariants = cva("font-medium leading-tight", {
  variants: {
    size: {
      xl: 'text-3xl sm:text-4xl md:text-5xl lg:text-6xl mb-4',
      lg: 'text-2xl sm:text-3xl md:text-4xl lg:text-5xl mb-3.5',
      md: 'text-xl sm:text-2xl md:text-3xl lg:text-4xl mb-3',
      sm: 'text-lg sm:text-xl md:text-2xl lg:text-3xl mb-2.5',
      xs: 'text-base sm:text-lg md:text-xl lg:text-2xl mb-2',
      xxs: 'text-sm sm:text-base md:text-lg lg:text-xl mb-1.5',
    },
  },
  defaultVariants: {
    size: 'md',
  },
})

export const Heading: React.FC<HeadingProps> = ({level, children, className, size}) => {
  const Tag = `h${level}` as keyof JSX.IntrinsicElements;
  const classes = headingVariants({size});
  return <Tag className={cn(classes, className)}>{children}</Tag>;
}
