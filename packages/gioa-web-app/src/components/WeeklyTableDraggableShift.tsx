import React, {useRef, useState} from 'react';
import {WeeklyTableShift} from "@/src/components/WeeklyTableTypes.ts";
import Draggable, {DraggableData, DraggableEvent} from 'react-draggable';
import {ClassValue} from 'clsx';
import {cn} from "@/src/util.ts";
import {sanitizeStoreAreaTitle} from "../../../api/src/util.ts";
import {getRangeDurationHours, to12HourTime} from "../../../api/src/date.util.ts";
import {DurationText} from "@/src/components/DurationText.tsx";
import {getChangeStatusThickBorder} from "@/src/components/ScheduleBuilder.util.tsx";
import {CrownIcon} from 'lucide-react';

export interface WeeklyTableDraggableShiftProps {
  shift: WeeklyTableShift;
  onDragStart: (shiftId: string, boxHeight: number) => void;
  onDragStop: (shiftId: string, data: DraggableData, e: DraggableEvent) => void;
  onClick: (shift: WeeklyTableShift) => void;

  isDragging: boolean;
  setIsDragging: (shiftId: string) => void;
  setIsNotDragging: () => void;
}

export const DragPlaceholder = ({className, height}: {
  className?: ClassValue;
  height?: number;
}) => {
  return (
    <div style={height ? {height: height} : undefined}
         className={cn("bg-blue-100 border-2 border-dashed min-h-16 border-blue-400 p-2 rounded-lg w-full drag-placeholder", className)}>
    </div>
  );
};

export const WeeklyTableDraggableShift = React.memo(({
                                                       onClick,
                                                       onDragStart,
                                                       onDragStop, isDragging, setIsDragging, setIsNotDragging,
                                                       shift
                                                     }: WeeklyTableDraggableShiftProps) => {
  const nodeRef = useRef<any>(null);
  // Use a state to control the position of the box
  const [position, setPosition] = useState({x: 0, y: 0});
  // Track the start position to determine if a drag has occurred
  const startPosition = useRef({x: 0, y: 0});

  const handleDragStart = (e: DraggableEvent, data: DraggableData) => {
    setIsNotDragging();
    startPosition.current.x = data.x
    startPosition.current.y = data.y
  };

  const handleDrag = (e: DraggableEvent, data: DraggableData) => {
    // Check if the box has moved more than a few pixels
    const dx = Math.abs(data.x - startPosition.current.x);
    const dy = Math.abs(data.y - startPosition.current.y);
    if (dx > 15 || dy > 15) {
      onDragStart(shift.id, nodeRef.current?.clientHeight ?? 0);
      setIsDragging(shift.id);
    }
  };

  const handleDragStop = (e: DraggableEvent, data: DraggableData) => {
    // We'll reset the position in the parent component if needed
    onDragStop(shift.id, data, e);

    // Apply transition class and then reset position
    setIsNotDragging();

    // Small delay to ensure the transition class is applied before position change
    setTimeout(() => {
      setPosition({x: 0, y: 0});
    }, 0);
  };

  const handleClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    // Only trigger click if no drag has occurred
    if (!isDragging) {
      onClick(shift);
    }
    setIsNotDragging();
  };

  const changeStatus = shift.changeStatus;
  const {borderClass, color} = getChangeStatusThickBorder(changeStatus);
  const shiftPosition = shift.storePositionTitle;

  return <div className={"relative"}>
    <DragPlaceholder
      className={["transition-opacity duration-300 absolute top-0 right-0 bottom-0 left-0 border-slate-500 bg-slate-200", {
        "opacity-0": !isDragging,
        "opacity-100": isDragging
      }]}/>
    <Draggable defaultClassName={"gioa-shift"}
               nodeRef={nodeRef}
               position={position}
               onStart={handleDragStart}
               onDrag={handleDrag}
               onStop={handleDragStop}
    >
      <button aria-label={"Open shift details"} key={shift.id} data-id={shift.id}
              ref={nodeRef} data-shift-id={shift.id}
              onClick={handleClick}
              className={cn("bg-white border rounded-lg pl-3 pr-2 py-2 text-left w-full", borderClass,
                {
                  "transition-transform duration-300": !isDragging,
                  "pointer-events-none shadow-lg outline-2 outline-gray-900 z-10 relative": isDragging
                })}
              style={{borderLeftColor: color}}>
        <div className={"flex flex-row gap-1 flex-wrap items-center mb-1"}>
          {shift.areaTitle ?
            <div className={"px-2 py-0.5 text-sm bg-blue-100 rounded-md whitespace-nowrap"}>
              {sanitizeStoreAreaTitle(shift.areaTitle)}
            </div> : null}
          {shiftPosition ? <div className={"px-2 py-0.5 text-sm bg-blue-100 rounded-md"}>
            {shiftPosition}
          </div> : null}
          {shift.isShiftLead ? <div><CrownIcon className={"text-gray-700"} size={16}/></div> : null}
        </div>
        {shift.range ?
          <div className={"text-sm whitespace-nowrap"}>
            {to12HourTime(shift.range.start, true)} - {to12HourTime(shift.range.end, true)}{' '}
            (<DurationText durationHours={getRangeDurationHours(shift.range)}/>)
          </div> : null}
      </button>
    </Draggable>
  </div>;
});

WeeklyTableDraggableShift.displayName = "WeeklyTableDraggableShift";
