import React from 'react';
import { format } from 'date-fns';
import { MetricDataPoint } from '@gioa/api/src/schemas';
import { Progress } from "@/src/components/ui/progress.tsx";
import { Text } from "@/src/components/Text.tsx";

export interface MetricDataPointNoteProps {
  dataPoint: MetricDataPoint;
}

export const MetricDataPointNote: React.FC<MetricDataPointNoteProps> = ({ dataPoint }) => {
  return (
    <div key={dataPoint.id} className="border border-gray-200 rounded-lg p-4">
      <div className="flex items-center justify-between mx-2 mt-2">
        <Text muted size="sm" className="flex-1">
          By {dataPoint.recorderPerson?.firstName} {dataPoint.recorderPerson?.lastName}
        </Text>
        <Text muted size="sm">{format(new Date(dataPoint.timestamp), 'PP')}</Text>
      </div>

      {dataPoint.notes ? (
        <Text className="mb-2">
          {dataPoint.notes}
        </Text>
      ) : null}

      <div className="flex items-center gap-2 mb-2">
        <Progress value={dataPoint.value ?? 0} className="flex-1" max={100} />
        <span className="text-sm">{(dataPoint.value ?? 0).toFixed(0)}%</span>
      </div>
    </div>
  );
};
