import { findIndex } from "lodash";
import { CSSProperties } from "react";
import { cn } from "@/src/util";

export interface SegmentedControlProps {
  options: string[];
  value: string;
  onChange: (value: string) => void;
  className?: string;
  buttonClassName?: string;
  style?: CSSProperties;
}

export const SegmentedControl = ({
  options,
  value,
  onChange,
  className,
  buttonClassName,
  style,
}: SegmentedControlProps) => {
  const selectedIndex = findIndex(options, (option) => option === value);

  return (
    <div className={cn("inline-flex items-stretch justify-start rounded-lg bg-gray-100 p-1", className)} style={style}>
      {options.map((option, index) => (
        <button
          key={index}
          type="button"
          aria-disabled="false"
          onClick={() => onChange(option)}
          className={cn(
            "group inline-flex items-center justify-center whitespace-nowrap py-1 align-middle font-semibold",
            "transition-all duration-300 ease-in-out disabled:cursor-not-allowed min-w-[32px]",
            "gap-1.5 disabled:stroke-slate-400 disabled:text-slate-400 px-3 rounded-md text-sm",
            selectedIndex === index
              ? "stroke-blue-700 text-slate-950 bg-white drop-shadow hover:stroke-blue-950 hover:text-blue-950"
              : "bg-transparent text-slate-600 hover:stroke-blue-950 hover:text-blue-950",
            buttonClassName,
          )}
        >
          <div>{option}</div>
        </button>
      ))}
    </div>
  );
};
