import { Button } from "@/src/components/ui/button.tsx";
import React from "react";
import { Edit, GripVertical } from "lucide-react";
import { Card } from "@/src/components/ui/card.tsx";
import { Text } from "@/src/components/Text.tsx";
import { useSortable } from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";

export interface JobItem {
  id: string;
  title: string;
  description: string;
  isDisabled: boolean;
}

export const SortableJobItem = ({ job, onEditClick }: { job: JobItem; onEditClick?: (jobId: string) => void }) => {
  const { attributes, listeners, setNodeRef, transform, transition } = useSortable({ id: job.id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  };

  return (
    <div ref={setNodeRef} style={style} {...attributes} className="mb-3">
      <Card className="flex items-center hover:bg-gray-50">
        <div className="p-3 cursor-grab active:cursor-grabbing" {...listeners}>
          <GripVertical size={20} className="text-gray-400" />
        </div>
        <div className="flex-1 py-3 pr-3">
          <Text semibold>{job.title}</Text>
          {job.description && (
            <Text size="sm" className="text-muted-foreground">
              {job.description}
            </Text>
          )}
        </div>
        {onEditClick && !job.isDisabled ? (
          <Button
            variant="ghost"
            size="icon"
            onClick={(e) => {
              e.stopPropagation();
              onEditClick(job.id);
            }}
            className="mr-2"
          >
            <Edit size={18} className="text-gray-500" />
          </Button>
        ) : null}
      </Card>
    </div>
  );
};
