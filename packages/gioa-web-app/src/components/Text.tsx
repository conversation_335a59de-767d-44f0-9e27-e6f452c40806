import React from 'react';
import {cva, type VariantProps} from "class-variance-authority"
import {cn} from "@/src/util.ts";

export const textVariants = cva(
  "text-base",
  {
    variants: {
      colorScheme: {
        default: `text-black`,
        primary: `text-primary-700`,
        light: `text-white`,
        danger: `text-red-600`,
        dark: `text-gioaBlue`
      },
      size: {
        xs: "text-xs",
        sm: "text-sm",
        md: "text-base",
        lg: "text-lg",
        xl: "text-xl"
      },
      bold: {
        true: "font-bold",
        false: undefined
      },
      semibold: {
        true: "font-semibold",
        false: undefined
      },
      medium: {
        true: "font-medium",
        false: undefined
      },
      center: {
        true: "text-center",
        false: undefined
      },
      muted: {
        true: "text-muted-foreground",
        false: undefined
      }
    },
    defaultVariants: {
      colorScheme: "default",
      size: "md",
      bold: false,
      center: false,
      muted: false
    },
  }
)

export interface TextProps extends VariantProps<typeof textVariants> {
  children?: any;
  className?: string;
  asChild?: React.ElementType;
  style?: React.CSSProperties;
}

export const Text: React.FC<TextProps> = ({
                                            children,
                                            colorScheme,
                                            size,
                                            bold, semibold, medium,
                                            center,
                                            muted,
                                            className,
                                            asChild,
                                            style,
                                            ...textProps
                                          }) => {

  const Tag = asChild ?? "p";

  return (
    <Tag className={cn(textVariants({
      colorScheme,
      size,
      bold, semibold, medium,
      center,
      muted
    }), className)} style={style} {...textProps}>
      {children}
    </Tag>
  );
}
