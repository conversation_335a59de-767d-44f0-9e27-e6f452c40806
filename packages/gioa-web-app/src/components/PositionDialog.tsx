import React, { useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON>Title, DialogFooter } from '@/src/components/ui/dialog';
import { Button } from '@/src/components/ui/button';
import { Input } from '@/src/components/ui/input';
import { Label } from '@/src/components/ui/label';
import { useForm } from '@tanstack/react-form';
import { zodValidator } from '@tanstack/zod-form-adapter';
import { z } from 'zod';
import { Text } from '@/src/components/Text';

interface PositionDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (title: string) => void;
  initialTitle?: string;
  dialogTitle?: string;
  submitButtonText?: string;
}

export const PositionDialog: React.FC<PositionDialogProps> = ({ 
  isOpen, 
  onClose, 
  onSubmit, 
  initialTitle = '', 
  dialogTitle = 'Add Position',
  submitButtonText = 'Create Position'
}) => {
  const form = useForm({
    defaultValues: {
      title: initialTitle,
    },
    validatorAdapter: zodValidator(),
    onSubmit: async ({ value }) => {
      onSubmit(value.title);
      form.reset();
    }
  });

  // Update form values when initialTitle changes
  useEffect(() => {
    if (isOpen) {
      form.setFieldValue('title', initialTitle);
    }
  }, [initialTitle, isOpen]);

  const handleClose = () => {
    form.reset();
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>{dialogTitle}</DialogTitle>
        </DialogHeader>
        <form
          onSubmit={(e) => {
            e.preventDefault();
            e.stopPropagation();
            form.handleSubmit();
          }}
        >
          <div className="space-y-4 py-4">
            <div>
              <Label htmlFor="title">Position Name</Label>
              <form.Field
                name="title"
                validators={{
                  onChange: z.string().min(1, 'Position name is required'),
                }}
              >
                {(field) => (
                  <div>
                    <Input
                      id="title"
                      placeholder="Enter position name..."
                      value={field.state.value}
                      onBlur={field.handleBlur}
                      onChange={(e) => field.handleChange(e.target.value)}
                      autoFocus
                    />
                    {field.state.meta.errors ? (
                      <Text className="text-red-500 text-sm mt-1">{field.state.meta.errors.join(', ')}</Text>
                    ) : null}
                  </div>
                )}
              </form.Field>
            </div>
          </div>
          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={handleClose}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={form.state.isSubmitting}
              isLoading={form.state.isSubmitting}
            >
              {submitButtonText}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};
