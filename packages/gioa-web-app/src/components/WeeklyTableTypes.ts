import {DailyTimeRange, IsoWeekDateWithDay} from "../../../api/src/timeSchemas.ts";
import {ScheduleShiftChangeStatus} from "../../../api/src/scheduleBuilder.util.ts";
import {Activity} from "../../../api/src/scheduleSchemas.ts";

export interface WeeklyTableShift {
  range: DailyTimeRange;
  title: string;
  description?: string;
  id: string;
  assignedPersonId?: string;
  // This is the schedule area, not the store area
  areaId: string;
  areaTitle: string;
  storeAreaId: string | undefined;
  isShiftLead?: boolean;

  storePositionId?: string;
  storePositionTitle: string | undefined;
  changeStatus: ScheduleShiftChangeStatus;

  activities: Activity[];

  isoWeek: IsoWeekDateWithDay;
}
