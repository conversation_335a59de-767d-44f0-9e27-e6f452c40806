import {describe, expect, it} from 'vitest';
import {getTimeOffRangeFromFormInputs} from './timeOff.util';

describe('getTimeOffRangeFromFormInputs', () => {
  it('should handle all day, single day time off', () => {
    const result = getTimeOffRangeFromFormInputs({
      isAllDay: true,
      isMultipleDays: false,
      startDate: '2024-09-05',
      startTime: undefined,
      endDate: undefined,
      endTime: undefined
    }, "America/Denver");

    expect(result).toEqual({
      start: new Date('2024-09-05T06:00:00.000Z'),
      end: new Date('2024-09-06T05:59:59.000Z')
    });
  });

  it('should handle all day, multiple days time off', () => {
    const result = getTimeOffRangeFromFormInputs({
      isAllDay: true,
      isMultipleDays: true,
      startDate: '2024-09-05',
      startTime: undefined,
      endDate: '2024-09-06',
      endTime: undefined
    }, "America/Denver");

    expect(result).toEqual({
      start: new Date('2024-09-05T06:00:00.000Z'),
      end: new Date('2024-09-07T05:59:59.000Z')
    });
  });

  it('should handle specific time, single day time off', () => {
    const result = getTimeOffRangeFromFormInputs({
      isAllDay: false,
      isMultipleDays: false,
      startDate: '2024-09-05',
      startTime: '08:00',
      endDate: '2024-09-05',
      endTime: '17:00'
    }, "America/Denver");

    expect(result).toEqual({
      start: new Date('2024-09-05T14:00:00.000Z'),
      end: new Date('2024-09-05T23:00:00.000Z')
    });
  });

  it('should handle specific time, multiple days time off', () => {
    const result = getTimeOffRangeFromFormInputs({
      isAllDay: false,
      isMultipleDays: true,
      startDate: '2024-09-05',
      startTime: '08:00',
      endDate: '2024-09-06',
      endTime: '17:00'
    }, "America/Denver");

    expect(result).toEqual({
      start: new Date('2024-09-05T14:00:00.000Z'),
      end: new Date('2024-09-06T23:00:00.000Z')
    });
  });

  it('should return undefined if start time is missing for specific time request', () => {
    const result = getTimeOffRangeFromFormInputs({
      isAllDay: false,
      isMultipleDays: false,
      startDate: '2024-09-05',
      startTime: undefined,
      endDate: '2024-09-05',
      endTime: '17:00'
    }, "America/Denver");

    expect(result).toBeUndefined();
  });

  it('should return undefined if end time is missing for specific time request', () => {
    const result = getTimeOffRangeFromFormInputs({
      isAllDay: false,
      isMultipleDays: false,
      startDate: '2024-09-05',
      startTime: '08:00',
      endDate: '2024-09-05',
      endTime: undefined
    }, "America/Denver");

    expect(result).toBeUndefined();
  });
});
