import React from 'react';
import {Document, Page, PDFViewer, StyleSheet, Text, View} from '@react-pdf/renderer';
import {map, sortBy} from 'lodash';
import {SchedulePersonDto} from "../../../api/src/schedulePersonDto.ts";
import {DateTimeRange} from "../../../api/src/timeSchemas.ts";
import {Table, TD, TR} from '@ag-media/react-pdf-table';
import {formatDateRangeByTimeFrame} from "@/src/date.util.ts";

const styles = StyleSheet.create({
  page: {
    padding: 30,
    fontSize: 8,
  },
  header: {
    marginBottom: 8,
    fontSize: 18,
    color: '#374151',
    fontWeight: 'bold',
  },
  dateRange: {
    marginBottom: 15,
    fontSize: 12,
    color: '#6B7280',
  },
  statsContainer: {
    flexDirection: 'row',
    marginBottom: 8,
    gap: 6,
  },
  statBox: {
    flex: 1,
    border: '1px solid #D1D5DB',
    borderRadius: 4,
    padding: 8,
    backgroundColor: '#F9FAFB',
  },
  statTitle: {
    fontSize: 8,
    color: '#6B7280',
    marginBottom: 4,
  },
  statValue: {
    fontSize: 12,
    color: '#374151',
    fontWeight: 'bold',
  },
  tableContainer: {
    marginTop: 10,
    borderRadius: 4,
  },
  cell: {
    borderCollapse: 'collapse',
    paddingVertical: 4,
    paddingHorizontal: 6,
    borderColor: '#E5E7EB',
    fontSize: 8,
  },
  headerRow: {
    backgroundColor: '#F3F4F6',
    fontWeight: 'bold',
  },
  nameCell: {
    flexDirection: 'column',
    gap: 2,
  },
  personName: {
    fontSize: 8,
    fontWeight: 'bold',
    color: '#374151',
  },
  proficiencyDots: {
    flexDirection: 'row',
    gap: 1,
  },
  proficiencyDot: {
    width: 4,
    height: 4,
    borderRadius: 2,
  },
  swapOfferCell: {
    flexDirection: 'column',
    gap: 1,
  },
  swapOfferText: {
    fontSize: 7,
    color: '#374151',
  },
  swapOfferSubtext: {
    fontSize: 6,
    color: '#6B7280',
  },
});

// Rounds number and removes trailing zeros
const cleanNumber = (hours: number) => parseFloat(hours.toFixed(2));

interface PersonData {
  person: SchedulePersonDto;
  totalAvailability: number;
  shiftStats: {
    hours: number;
    shifts: number;
    adminNonOps: number;
  };
  swapStats: {
    offered: number;
    received: number;
  };
  offerStats: {
    offered: number;
    received: number;
  };
  timeOffHours: number;
}

interface ShiftStat {
  title: string;
  count: number;
}

interface PDFSchedulingInsightsTableProps {
  storeTitle: string;
  peopleData: PersonData[];
  shiftStats: ShiftStat[];
  dateRange: DateTimeRange;
  timeFrame: string;
  timezone: string;
  exportOptions?: {
    availability: boolean;
    scheduled: boolean;
    adminNonOps: boolean;
    shifts: boolean;
    shiftSwaps: boolean;
    shiftOffers: boolean;
    timeOff: boolean;
    itemizeShifts: boolean;
  };
}

const ProficiencyRating: React.FC<{rank: number}> = ({rank}) => {
  const dots = [];
  for (let i = 1; i <= 3; i++) {
    dots.push(
      <View
        key={i}
        style={[
          styles.proficiencyDot,
          {
            backgroundColor: i <= rank ? '#10B981' : '#E5E7EB'
          }
        ]}
      />
    );
  }
  return <View style={styles.proficiencyDots}>{dots}</View>;
};

export const PDFSchedulingInsightsTable: React.FC<PDFSchedulingInsightsTableProps> = ({
  storeTitle,
  peopleData,
  shiftStats,
  dateRange,
  timeFrame,
  timezone,
  exportOptions,
}) => {
  const sortedPeopleData = sortBy(peopleData, [
    p => p.person.lastName.toLowerCase(),
    p => p.person.firstName.toLowerCase()
  ]);

  const dateRangeText = formatDateRangeByTimeFrame({range: dateRange, timeFrame, timezone});

  return (
    <PDFViewer style={{width: '100%', height: '100vh'}}>
      <Document title={`Scheduling Insights - ${storeTitle}`}>
        <Page size="LETTER" style={styles.page}>
          <Text style={styles.header}>
            Scheduling Insights
          </Text>

          <Text style={styles.dateRange}>
            {storeTitle} • {dateRangeText}
          </Text>

          {/* Summary Statistics */}
          <View style={styles.statsContainer}>
            {map(shiftStats, (stat, index) => {
              // Conditionally render statistics based on export options
              if (stat.title === "Total Scheduled Hours" && exportOptions?.scheduled === false) return null;
              if (stat.title === "Total Shifts" && exportOptions?.shifts === false) return null;
              if (stat.title === "Total Swaps" && exportOptions?.shiftSwaps === false) return null;
              if (stat.title === "Total Offers" && exportOptions?.shiftOffers === false) return null;
              if (stat.title === "Total Time Off Requests" && exportOptions?.timeOff === false) return null;

              return (
                <View key={index} style={styles.statBox}>
                  <Text style={styles.statTitle}>{stat.title}</Text>
                  <Text style={styles.statValue}>{cleanNumber(stat.count)}</Text>
                </View>
              );
            })}
          </View>

          {/* Main Table */}
          <View style={styles.tableContainer}>
            <Table>
              {/* Header Row */}
              <TR style={styles.headerRow}>
                <TD style={styles.cell}>
                  <Text>Team Member</Text>
                </TD>
                {exportOptions?.availability !== false && (
                  <TD style={styles.cell}>
                    <Text>Availability</Text>
                  </TD>
                )}
                {exportOptions?.scheduled !== false && (
                  <TD style={styles.cell}>
                    <Text>Scheduled</Text>
                  </TD>
                )}
                {exportOptions?.adminNonOps !== false && (
                  <TD style={styles.cell}>
                    <Text>Admin/Non-ops</Text>
                  </TD>
                )}
                {exportOptions?.shifts !== false && (
                  <TD style={styles.cell}>
                    <Text>Shifts</Text>
                  </TD>
                )}
                {exportOptions?.shiftSwaps !== false && (
                  <TD style={styles.cell}>
                    <Text>Swaps</Text>
                  </TD>
                )}
                {exportOptions?.shiftOffers !== false && (
                  <TD style={styles.cell}>
                    <Text>Offers</Text>
                  </TD>
                )}
                {exportOptions?.timeOff !== false && (
                  <TD style={styles.cell}>
                    <Text>Time Off</Text>
                  </TD>
                )}
              </TR>

              {/* Data Rows */}
              {map(sortedPeopleData, (personData) => (
                <TR key={personData.person.id} wrap={false}>
                  <TD style={styles.cell}>
                    <View style={styles.nameCell}>
                      <Text style={styles.personName}>
                        {personData.person.firstName} {personData.person.lastName}
                      </Text>
                      <ProficiencyRating rank={personData.person.proficiencyRanking ?? 0} />
                    </View>
                  </TD>
                  {exportOptions?.availability !== false && (
                    <TD style={styles.cell}>
                      <Text>{cleanNumber(personData.totalAvailability)} hrs</Text>
                    </TD>
                  )}
                  {exportOptions?.scheduled !== false && (
                    <TD style={styles.cell}>
                      <Text>{cleanNumber(personData.shiftStats.hours)} hrs</Text>
                    </TD>
                  )}
                  {exportOptions?.adminNonOps !== false && (
                    <TD style={styles.cell}>
                      <Text>{cleanNumber(personData.shiftStats.adminNonOps)} hrs</Text>
                    </TD>
                  )}
                  {exportOptions?.shifts !== false && (
                    <TD style={styles.cell}>
                      <Text>{cleanNumber(personData.shiftStats.shifts)}</Text>
                    </TD>
                  )}
                  {exportOptions?.shiftSwaps !== false && (
                    <TD style={styles.cell}>
                      <View style={styles.swapOfferCell}>
                        <Text style={styles.swapOfferText}>{personData.swapStats.offered} Offered</Text>
                        <Text style={styles.swapOfferSubtext}>{personData.swapStats.received} Pick-ups</Text>
                      </View>
                    </TD>
                  )}
                  {exportOptions?.shiftOffers !== false && (
                    <TD style={styles.cell}>
                      <View style={styles.swapOfferCell}>
                        <Text style={styles.swapOfferText}>{personData.offerStats.offered} Offered</Text>
                        <Text style={styles.swapOfferSubtext}>{personData.offerStats.received} Pick-ups</Text>
                      </View>
                    </TD>
                  )}
                  {exportOptions?.timeOff !== false && (
                    <TD style={styles.cell}>
                      <Text>{cleanNumber(personData.timeOffHours)} hrs</Text>
                    </TD>
                  )}
                </TR>
              ))}
            </Table>
          </View>
        </Page>
      </Document>
    </PDFViewer>
  );
};
