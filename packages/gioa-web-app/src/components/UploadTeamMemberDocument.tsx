import React, { use<PERSON>emo, useState } from "react";
import { useDropzone } from "react-dropzone";
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from "@/src/components/ui/dialog.tsx";
import { Button } from "@/src/components/ui/button.tsx";
import { Text } from "@/src/components/Text.tsx";
import {
  CircleHelp,
  CloudUploadIcon,
  ExternalLinkIcon,
  FileImageIcon,
  FileTextIcon,
  FileVideoIcon,
  MoreHorizontal,
  PlusIcon,
  Trash2Icon,
} from "lucide-react";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/src/components/ui/alert-dialog.tsx";
import { formatFileSize } from "@/src/utils/formatters.ts";
import { api } from "@/src/api.ts";
import { Error<PERSON>lert, getHumanReadableErrorMessage } from "@/src/components/ErrorAlert.tsx";
import { useForm } from "@tanstack/react-form";
import { zodValidator } from "@tanstack/zod-form-adapter";
import { z } from "zod";
import { toast } from "sonner";
import { useFormAttachmentUpload } from "@/src/hooks/useFormAttachmentUpload.ts";
import { FileAttachmentDto } from "@gioa/api/src/fileAttachment.dto";
import { Progress } from "@/src/components/ui/progress.tsx";
import { Label } from "@/src/components/ui/label.tsx";
import { FormInput } from "@/src/components/form/FormInput.tsx";
import { FieldInfo } from "@/src/components/form/FieldInfo.tsx";
import { FormDropDown } from "@/src/components/form/FormDropDown.tsx";
import { Popover, PopoverContent, PopoverTrigger } from "@/src/components/ui/popover.tsx";
import { DataTable } from "@/src/components/ui/data-table.tsx";
import {
  createColumnHelper,
  getCoreRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  SortingState,
  useReactTable,
} from "@tanstack/react-table";
import { DataTableColumnHeader } from "@/src/components/DataTableColumnHeader.tsx";
import { format } from "date-fns";
import { DateTime } from "luxon";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/src/components/ui/dropdown-menu.tsx";
import { imageUrl } from "@/src/images.ts";

interface TeamMemberDocument {
  id: string;
  title?: string;
  documentType?: string;
  expiresAt?: Date;
  url?: string;
  filename?: string;
  mediaType: "image" | "video" | "document" | "file";
  mimeType?: string;
}

interface UploadTeamMemberDocumentProps {
  storeId: string;
  personId: string;
  title?: string;
  subtitle?: string;
  onSuccess?: () => void;
}

interface DocumentFile {
  file: File;
  name: string;
  size: number;
  type: string;
}

export function UploadTeamMemberDocument({
  storeId,
  personId,
  title = "Upload Document",
  subtitle,
  onSuccess,
}: UploadTeamMemberDocumentProps) {
  const [isFilePickerOpen, setIsFilePickerOpen] = useState(false);
  const [sorting, setSorting] = useState<SortingState>([]);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [documentToDelete, setDocumentToDelete] = useState<string | null>(null);

  const getPresignedPost = api.user.getPresignedPostForTeamMemberDocument.useMutation();
  const uploadTeamMemberDocument = api.user.uploadTeamMemberDocument.useMutation({
    onSuccess: () => {
      toast.success("Document uploaded successfully", {
        position: "top-center",
      });
      apiUtil.user.getTeamMemberDocuments.invalidate({
        storeId,
        personId,
      });
      onSuccess?.();
      form.reset();
      setIsFilePickerOpen(false);
    },
    onError: (error) => {
      toast.error("Failed to upload document: " + getHumanReadableErrorMessage(error), {
        position: "top-center",
      });
    },
  });

  const apiUtil = api.useUtils();

  const [{ items: documents }] = api.user.getTeamMemberDocuments.useSuspenseQuery({ storeId, personId });
  const [{ timezone }] = api.user.getStore.useSuspenseQuery(
    { storeId: storeId! },
    {
      staleTime: 1000 * 60 * 60, // 1 hour
    },
  );

  const { toDto: uploadAttachment, status: uploadStatus } = useFormAttachmentUpload(({ contentType, mediaType }) =>
    getPresignedPost.mutateAsync({
      storeId,
      personId,
      contentType,
      mediaType: mediaType || "file",
    }),
  );

  const columnHelper = createColumnHelper<TeamMemberDocument>();

  const handleOpenDocument = (document: TeamMemberDocument) => {
    if (!document.url) return;
    const href = document.mediaType === "image" ? imageUrl(document.url, { width: 1280 }) : document.url;
    window.open(href, "_blank");
  };

  const confirmDeleteDocument = (documentId: string) => {
    setDocumentToDelete(documentId);
    setIsDeleteDialogOpen(true);
  };

  const deleteTeamMemberDocument = api.user.deleteTeamMemberDocument.useMutation();

  const handleDeleteDocument = async () => {
    if (!documentToDelete) return;

    try {
      await deleteTeamMemberDocument.mutateAsync({
        storeId,
        personId,
        id: documentToDelete,
      });
      toast.success("Document deleted successfully", {
        position: "top-center",
      });
      apiUtil.user.getTeamMemberDocuments.invalidate({
        storeId,
        personId,
      });
      setIsDeleteDialogOpen(false);
      setDocumentToDelete(null);
    } catch (error) {
      toast.error("Failed to delete document: " + getHumanReadableErrorMessage(error), {
        position: "top-center",
      });
    }
  };

  const columns = [
    columnHelper.accessor("title", {
      header: ({ column }) => <DataTableColumnHeader column={column} title="Name" />,
      cell: ({ row }) => (
        <div className="flex flex-row items-center gap-4">
          <div>
            {row.original.mediaType === "file" || row.original.mediaType === "document" ? (
              <FileTextIcon size={24} className={"text-primary-600"} />
            ) : null}
            {row.original.mediaType === "image" ? <FileImageIcon size={24} className={"text-orange-600"} /> : null}
            {row.original.mediaType === "video" ? <FileVideoIcon size={24} className={"text-green-600"} /> : null}
          </div>
          <Text size={"sm"}>{row.original.title}</Text>
        </div>
      ),
    }),
    columnHelper.accessor("documentType", {
      header: ({ column }) => <DataTableColumnHeader column={column} title="Type" />,
      cell: ({ row }) => <Text size={"sm"}>{row.original.documentType || "-"}</Text>,
    }),
    columnHelper.accessor("expiresAt", {
      header: ({ column }) => <DataTableColumnHeader column={column} title="Expiration Date" />,
      cell: ({ row }) => {
        const expiresAt = row.original.expiresAt;
        return <Text size={"sm"}>{expiresAt ? format(expiresAt, "MM/dd/yyyy") : "-"}</Text>;
      },
    }),
    columnHelper.display({
      id: "actions",
      header: () => <div className="text-right">Actions</div>,
      cell: ({ row }) => {
        const document = row.original;
        return (
          <div className="flex justify-end">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="icon" className="h-8 w-8 p-0">
                  <span className="sr-only">Open menu</span>
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                {document.url && (
                  <DropdownMenuItem onClick={() => handleOpenDocument(document)}>
                    <ExternalLinkIcon className="mr-2 h-4 w-4" />
                    <Text>Open</Text>
                  </DropdownMenuItem>
                )}
                <DropdownMenuItem onClick={() => confirmDeleteDocument(document.id)} className="text-red-600">
                  <Trash2Icon className="mr-2 h-4 w-4" />
                  <Text colorScheme={"danger"}>Delete</Text>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        );
      },
    }),
  ];

  const table = useReactTable({
    data: documents,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    onSortingChange: setSorting,
    getPaginationRowModel: getPaginationRowModel(),
    initialState: {
      pagination: {
        pageSize: 20,
      },
    },
    state: {
      sorting,
    },
  });

  const form = useForm({
    defaultValues: {
      document: null as DocumentFile | null,
      fileName: "",
      type: undefined as string | undefined,
      expiration: undefined,
    },
    validatorAdapter: zodValidator(),
    onSubmit: async ({ value }) => {
      try {
        let attachment: FileAttachmentDto | undefined;
        if (value.document) {
          attachment = await uploadAttachment(value.document.file);
          if (!attachment) {
            toast.error("Failed to upload attachment");
            return;
          }
        }

        if (!attachment) {
          toast.error("Failed to upload attachment");
          return;
        }

        uploadTeamMemberDocument.mutate({
          storeId,
          personId,
          resourceType: "file",
          resourceTitle: value.fileName,
          documentType: value.type,
          expiresAt: value.expiration
            ? DateTime.fromFormat(value.expiration, "yyyy-MM-dd", { zone: timezone }).toJSDate()
            : undefined,
          attachment: attachment,
        });
      } catch (error) {
        console.error("Error uploading document:", error);
        toast.error("Error uploading document: " + getHumanReadableErrorMessage(error), {
          position: "top-center",
        });
      }
    },
  });

  const formatFileName = (filename: string): string => {
    const nameWithoutExtension = filename.replace(/\.[^/.]+$/, "");
    const nameWithSpaces = nameWithoutExtension.replace(/[_\-.]+/g, " ");

    const titleCased = nameWithSpaces
      .split(" ")
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join(" ");

    const maxLength = 50;
    return titleCased.length > maxLength ? titleCased.substring(0, maxLength) : titleCased;
  };

  const onDrop = React.useCallback(
    (acceptedFiles: File[]) => {
      const file = acceptedFiles[0];
      if (file) {
        form.setFieldValue("document", {
          file,
          name: file.name,
          size: file.size,
          type: file.type,
        });

        form.setFieldValue("fileName", formatFileName(file.name));
      }
    },
    [form],
  );

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      // All images, videos, PDFs, and office documents
      "image/*": [],
      "video/*": [],
      "text/plain": [],
      "application/pdf": [],
      "application/msword": [],
      "application/vnd.openxmlformats-officedocument.wordprocessingml.document": [],
      "application/vnd.ms-excel": [],
      "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet": [],
    },
    multiple: false,
    maxSize: 100 * 1024 * 1024, // 100MB max size
  });

  const onClose = () => {
    form.reset();
    setIsFilePickerOpen(false);
  };

  const isLoading = uploadTeamMemberDocument.isPending || uploadStatus.isLoading;

  const ListEmptyComponent = useMemo(
    () => (
      <div className={"text-muted-foreground text-center"}>
        <Text>No documents uploaded yet</Text>
      </div>
    ),
    [],
  );

  return (
    <>
      <div className="space-y-4">
        <div
          className="border border-gray-300 rounded-lg p-3 flex flex-row justify-between items-center cursor-pointer"
          onClick={() => setIsFilePickerOpen(true)}
        >
          <Text muted>Upload New Document</Text>
          <PlusIcon size={20} color={"#000"} />
        </div>

        {documents.length > 0 ? (
          <div className="overflow-hidden">
            <DataTable
              table={table}
              EmptyComponent={ListEmptyComponent}
              rowDetailLinkFrom=""
              getRowDetailTo={() => ""}
            />
          </div>
        ) : (
          <div className="py-4 text-center border border-dashed rounded-lg">
            <Text muted>No documents uploaded yet</Text>
          </div>
        )}
      </div>

      <Dialog open={isFilePickerOpen} onOpenChange={(open) => !open && onClose()}>
        <DialogContent className="sm:max-w-3xl w-full max-w-[95vw] overflow-hidden">
          <DialogHeader>
            <DialogTitle>{title}</DialogTitle>
            {subtitle && (
              <Text size="sm" muted>
                {subtitle}
              </Text>
            )}
          </DialogHeader>

          {uploadStatus.isError && <ErrorAlert error={uploadStatus.error} className="my-2" />}
          {uploadTeamMemberDocument.isError && <ErrorAlert error={uploadTeamMemberDocument.error} className="my-2" />}

          <div className="space-y-4 py-4 w-full overflow-hidden">
            <form.Field
              name="document"
              validators={{
                onSubmit: z.any().refine((val) => val !== null, {
                  message: "Please select a document to upload",
                }),
              }}
              children={(field) => (
                <>
                  <div
                    {...getRootProps()}
                    className={`flex flex-col gap-4 border-2 border-dashed rounded-lg
                      p-12 text-center cursor-pointer transition-colors justify-center items-center
                      ${isDragActive ? "border-primary bg-primary/10" : "border-gray-300 hover:border-primary/50"}`}
                  >
                    <input {...getInputProps()} />
                    <CloudUploadIcon className="mx-auto h-12 w-12 text-gray-400" />
                    <Text className="mt-2">Choose a file or drag & drop it here</Text>
                    <Button variant="outline" type="button" className="mt-2 border-gray-400">
                      Browse
                    </Button>
                  </div>

                  {field.state.value && (
                    <div className={"space-y-3"}>
                      <div className="bg-gray-100 p-4 rounded-lg mt-4 w-full max-w-full overflow-hidden">
                        <div className="flex flex-row items-center gap-6 w-full overflow-hidden">
                          <div>
                            <FileTextIcon size={36} className="text-primary-600" />
                          </div>
                          <div className="flex-1 flex flex-col overflow-hidden min-w-0">
                            <Text size="sm" className="truncate w-full">
                              {field.state.value.name}
                            </Text>
                            <Text size="xs" muted>
                              {formatFileSize(field.state.value.size)}
                            </Text>
                          </div>
                          <div>
                            <Button variant="ghost" size="sm" onClick={() => field.handleChange(null)} type="button">
                              <Trash2Icon size={24} className="text-red-500" />
                            </Button>
                          </div>
                        </div>
                      </div>
                      <div className={"px-1 flex gap-1 justify-between"}>
                        <form.Field
                          name={"fileName"}
                          validators={{
                            onSubmit: z.string().min(1, "Please enter a file name"),
                          }}
                          children={(field) => (
                            <div className={"flex-1 flex flex-col gap-2"}>
                              <div className={"flex justify-start items-center h-4"}>
                                <Label>File Name</Label>
                              </div>
                              <FormInput field={field} placeholder="Enter file name..." />
                              <FieldInfo field={field} />
                            </div>
                          )}
                        />
                        <form.Field
                          name={"type"}
                          children={(field) => (
                            <div className={"flex-1 flex flex-col gap-2"}>
                              <div className={"flex justify-start items-center h-4"}>
                                <Label>File Type (optional)</Label>
                              </div>
                              <FormDropDown
                                field={field}
                                options={[
                                  {
                                    value: "Certification",
                                    label: "Certification",
                                  },
                                  { value: "Training", label: "Training" },
                                  { value: "HR", label: "HR" },
                                  { value: "General", label: "General" },
                                ]}
                              />
                              <FieldInfo field={field} />
                            </div>
                          )}
                        />
                        <form.Field
                          name={"expiration"}
                          children={(field) => (
                            <div className={"flex-1 flex flex-col gap-2"}>
                              <div className={"h-4 flex justify-between items-center"}>
                                <Label>Expiration Date (optional)</Label>
                                <Popover>
                                  <PopoverTrigger asChild>
                                    <CircleHelp size={18} className="cursor-help" />
                                  </PopoverTrigger>
                                  <PopoverContent>
                                    <Text size={"sm"}>
                                      Does this document, certification, etc have an expiration date?
                                    </Text>
                                  </PopoverContent>
                                </Popover>
                              </div>
                              {/*<FormDatePicker field={field} />*/}
                              <FormInput field={field} type={"date"} />
                              <FieldInfo field={field} />
                            </div>
                          )}
                        />
                      </div>
                    </div>
                  )}

                  {uploadStatus.isLoading && (
                    <div>
                      <Progress value={uploadStatus.progress} max={100} className={"h-3"} />
                      <div className={"flex flex-row justify-center"}>
                        <Text size={"sm"} muted>
                          Uploading...
                        </Text>
                      </div>
                    </div>
                  )}
                </>
              )}
            />
          </div>

          <div className="flex justify-between gap-2">
            <Button variant="outline" onClick={onClose} type="button">
              Cancel
            </Button>
            <Button disabled={!form.getFieldValue("document") || isLoading} onClick={form.handleSubmit} type="button">
              {isLoading ? "Uploading..." : "Upload"}
            </Button>
          </div>
        </DialogContent>
      </Dialog>
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Document</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete this document? This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={() => setDocumentToDelete(null)}>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={handleDeleteDocument} className="bg-red-600 hover:bg-red-700">
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
