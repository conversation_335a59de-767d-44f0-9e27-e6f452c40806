import React, {useMemo} from 'react';
import {ReactFormExtendedApi, Validator} from "@tanstack/react-form";
import {ZodType} from 'zod';
import {DailyTimeRange} from "../../../api/src/timeSchemas.ts";
import {getDateTimeFromWeekDayTime, parse24HourTime} from "../../../api/src/date.util.ts";
import {times} from "lodash";
import {adjustHourlyValuesToMatchTotal, convertFormForecastToMap} from "../metrics.util";
import {CurrencyInlineEdit} from "@/src/components/CurrencyInlineEdit.tsx";
import {Link} from "@tanstack/react-router";
import {
  getForecastTotalProjectedRevenueForDay
} from '@gioa/api/src/scheduling/metrics/scheduleHourlySalesForecast/scheduleHourlySalesForecast.ts';
import {Route} from "@/src/routes/_signedin/$businessId/$storeId/_nav/schedules/builder.tsx";

export interface WeekMetricsDayRowProps {
  timezone: string;
  year: number;
  weekNumber: number;
  weekday: number;
  form: ReactFormExtendedApi<any, Validator<unknown, ZodType>>;
  storeHours: DailyTimeRange;
  scheduleId: string;
}

export const WeekMetricsDayRow: React.FC<WeekMetricsDayRowProps> = ({form, storeHours, scheduleId, ...props}) => {
  const [startHour] = parse24HourTime(storeHours.start);
  const [endHour] = parse24HourTime(storeHours.end);
  const numHours = endHour - startHour;
  const date = getDateTimeFromWeekDayTime({
    year: props.year,
    week: props.weekNumber,
    day: props.weekday,
    timezone: props.timezone,
    time: "00:00",
  })
  const hours = form.useStore(s => s.values.hours);
  const forecast = useMemo(() => {
    return convertFormForecastToMap(hours);
  }, [hours]);

  const totalProjectedRevenue = useMemo(() => {
    return getForecastTotalProjectedRevenueForDay(forecast, props.weekday);
  }, [forecast])

  const onTotalChange = (newTotal: number) => {
    // Get the current hourly values for this day
    const weekday = props.weekday;
    const startHourIndex = (weekday - 1) * 24;
    const endHourIndex = weekday * 24;

    // Extract the current hourly values for this day
    const currentHourlyValues: number[] = [];
    for (let hour = startHourIndex; hour < endHourIndex; hour++) {
      const hourValue = Number(form.getFieldValue(`hours[${hour}]`) || 0);
      currentHourlyValues.push(hourValue);
    }

    // Use the adjustHourlyValuesToMatchTotal function to calculate the new values
    const adjustedValues = adjustHourlyValuesToMatchTotal(
      currentHourlyValues,
      totalProjectedRevenue,
      newTotal
    );

    // Update the form with the adjusted values
    adjustedValues.forEach((value, index) => {
      const hour = startHourIndex + index;
      form.setFieldValue(`hours[${hour}]`, value.toFixed(2));
    });
  }

  return (
    <tr>
      <td className={"whitespace-nowrap bg-gioaBlue text-white sticky left-0 z-10"}>
        <Link from={Route.fullPath}
              to={`../../schedules/$scheduleId`}
              params={{scheduleId: scheduleId}}
              search={{dayOfWeek: props.weekday, w: true, week: false}}
              className={"px-2 w-full h-full hover:underline"}>
          {date.toLocaleString({
            weekday: "long",
            month: "numeric",
            day: "numeric",
          })}
        </Link>
      </td>
      {times(numHours, i => {
        const hour = (props.weekday - 1) * 24 + startHour + i;
        return <td key={hour}>
          <form.Field name={`hours[${hour}]`}
                      children={field =>
                        <input value={field.state.value} className={"w-24 px-2 py-1"}
                               onBlur={field.handleBlur} type={"number"}
                               onChange={(e) => field.handleChange(e.target.value)}/>}
          />
        </td>
      })}
      <td className={"sticky right-0 bg-white border"}>
        <CurrencyInlineEdit value={totalProjectedRevenue} onChange={onTotalChange}/>
        <div className={"absolute w-8 -left-8 top-0 bottom-0 bg-gradient-to-r from-transparent to-[#b3bdd04d]"}/>
      </td>
    </tr>
  );
}
