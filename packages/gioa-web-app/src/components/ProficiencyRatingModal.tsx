import { useState, useEffect } from "react";
import { Button } from "@/src/components/ui/button.tsx";
import {
  Dialog,
  DialogContent,
} from "@/src/components/ui/dialog.tsx";
import { Text } from "@/src/components/Text.tsx";
import { Star } from "lucide-react";

interface ProficiencyRatingModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (rating: number) => Promise<void>;
  personName: string;
  currentRating: number;
  isLoading?: boolean;
}

export function ProficiencyRatingModal({
  isOpen,
  onClose,
  onSave,
  personName,
  currentRating,
  isLoading = false,
}: ProficiencyRatingModalProps) {
  const [rating, setRating] = useState(currentRating);

  // Reset rating when modal opens with new data
  useEffect(() => {
    if (isOpen) {
      setRating(currentRating);
    }
  }, [isOpen, currentRating]);

  const handleSave = async () => {
    await onSave(rating);
  };

  const handleCancel = () => {
    setRating(currentRating); // Reset to original rating
    onClose();
  };

  const getRatingDescription = (ratingValue: number) => {
    switch (ratingValue) {
      case 1:
        return "A 1-star Team Member is not trained or not proficient at most positions";
      case 2:
        return "A 2-star Team Member is trained on all positions, but not proficient on all positions";
      case 3:
        return "A 3-star Team Member is trained and proficient at all positions";
      default:
        return "";
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md rounded-3xl p-6">
        {/* Close button */}
        <button
          onClick={onClose}
          className="absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none"
          disabled={isLoading}
        >
          <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>

        <div className="space-y-6">
          {/* Header */}
          <div>
            <h2 className="text-lg font-semibold text-gray-900 mb-2">Proficiency</h2>
            <div className="text-sm text-gray-600">
              <span className="text-gray-500">Team Member: </span>
              <span className="font-medium text-gray-900">{personName}</span>
            </div>
          </div>

          {/* Star Rating */}
          <div className="flex justify-center space-x-2">
            {[1, 2, 3].map((starValue) => (
              <button
                key={starValue}
                onClick={() => setRating(starValue)}
                disabled={isLoading}
                className="p-2 rounded-lg hover:bg-gray-50 transition-colors disabled:opacity-50"
              >
                <Star
                  size={48}
                  className={`transition-colors ${
                    rating >= starValue
                      ? 'fill-yellow-400 text-yellow-400'
                      : 'fill-none text-gray-300'
                  }`}
                />
              </button>
            ))}
          </div>

          {/* Rating Description */}
          <div className="text-center">
            <Text className="text-sm text-gray-600 italic">
              {getRatingDescription(rating)}
            </Text>
          </div>

          {/* Action Buttons */}
          <div className="flex space-x-3 pt-2">
            <Button
              variant="outline"
              onClick={handleCancel}
              disabled={isLoading}
              className="flex-1"
            >
              Cancel
            </Button>
            <Button
              onClick={handleSave}
              disabled={isLoading}
              className="flex-1"
            >
              {isLoading ? 'Saving...' : 'Save'}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
