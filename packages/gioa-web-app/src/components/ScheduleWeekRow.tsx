import React, {useMemo, useState} from 'react';
import {ScheduleRevision} from "../../../api/src/scheduleSchemas.ts";
import {find, findIndex, map, times} from "lodash";
import {ScheduleWeekDay} from "@/src/components/ScheduleWeekDay.tsx";
import {getDateFromWeekDayTime} from "../../../api/src/date.util.ts";
import {IsoWeekDate} from "../../../api/src/timeSchemas.ts";
import {addDays} from "date-fns";
import {Button, buttonVariants} from "@/src/components/ui/button";
import {Link, useNavigate} from "@tanstack/react-router";
import {cn} from "@/src/util.ts";
import {api} from "@/src/api.ts";
import {validateSchedule} from "../../../api/src/scheduleValidation.ts";
import {scheduleAndPeopleToValidationRequest} from "../../../api/src/scheduleValidation.util.ts";
import {getEffectiveAvailability} from "../../../api/src/getEffectiveAvailability.ts";
import {PublishWeekDialog} from "@/src/components/PublishWeekDialog.tsx";
import {useDisclosure} from "@/src/hooks/useDisclosure.tsx";
import {Route} from "@/src/routes/_signedin/$businessId/$storeId/_nav/schedules/builder.tsx";
import {ValidationMessage} from '@gioa/api/src/scheduleValidation.types.ts';
import {toast} from "sonner";
import {ScheduleEventDto} from '@gioa/api/src/scheduleEventSchemas.ts';
import {StoreAreaDto} from '@gioa/api/src/schemas.ts';
import {useAutoAnimate} from "@formkit/auto-animate/react";
import {SchedulePersonClientDto, SchedulePersonDto} from "../../../api/src/schedulePersonDto.ts";
import {CopyWeekDialog} from "@/src/components/CopyWeekDialog.tsx";
import {generateIsoWeekDates, takeFromGenerator} from "../../../api/src/scheduleBuilder.util.ts";
import {getHumanReadableErrorMessage} from './ErrorAlert.tsx';
import {copyDay} from "../../../api/src/scheduleTemplates.ts";
import {produce} from 'immer';
import {useIgnoreScheduleValidationMessages} from '../hooks/useIgnoreScheduleValidationMessages.tsx';
import {getFriendlyIsoWeekString} from "@/src/schedule/schedule.ts";
import {
  ValidScheduleHourlySalesForecast
} from "../../../api/src/scheduling/metrics/scheduleHourlySalesForecast/scheduleHourlySalesForecastTypes.ts";
import {formatCurrency, formatFixed, formatHours} from "../../../common/src/dataFormatters.ts";
import {fromSchedulePeople} from "../../../api/src/scheduling/metrics/payRates/payRates.ts";
import {ScheduleValidationSettings} from "../../../api/src/scheduleValidation.types.ts";
import {calculateWeekMetrics} from "../../../api/src/scheduling/metrics/metrics.util.ts";

export interface ScheduleWeekRowProps {
  isForecastingEnabled: boolean;
  isSelected: boolean;
  year: number;
  week: number;
  storeId: string;
  schedule?: ScheduleRevision;
  forecast: ValidScheduleHourlySalesForecast | undefined;
  routeToScheduleFrom: string;
  routeToScheduleTo?: string;
  onSelect: (week: IsoWeekDate) => void;
  people: SchedulePersonDto[];
  events: ScheduleEventDto[];
  onViewNotes: (scheduleId: string, dayOfWeek: number) => void;
  timezone: string;
  storeAreas: StoreAreaDto[];
  settings: ScheduleValidationSettings;
}

export const
        ScheduleWeekRow = React.memo(function ScheduleWeekRow({
                                                                     isSelected, isForecastingEnabled,
                                                                     year, week: weekNum, storeId,
                                                                     schedule, forecast,
                                                                     routeToScheduleFrom,
                                                                     routeToScheduleTo, settings,
                                                                     onSelect, people, events,
                                                                     onViewNotes, timezone, storeAreas
                                                                   }: ScheduleWeekRowProps) {
  const payRates = useMemo(() => fromSchedulePeople(people), [people]);
  const week = {year, week: weekNum};
  const weekStartDate = getDateFromWeekDayTime({...week, day: 1, time: "00:00", timezone: "local"});
  const weekEndDate = getDateFromWeekDayTime({...week, day: 7, time: "23:59", timezone: "local"});
  const [now] = useState(new Date());
  const sched = schedule?.draft ?? schedule?.published;
  const hasSchedule = Boolean(sched);
  const isPastSchedule = weekEndDate < now;
  const isPublished = Boolean(schedule?.published);
  const hasDraft = Boolean(schedule?.draft);
  const {ignoreMessage, unignoreMessage} = useIgnoreScheduleValidationMessages({
    scheduleId: sched?.id,
    storeId: storeId,
  });
  const onSelectWeek = () => {
    onSelect(week);
  }

  const createSched = api.user.createSchedule.useMutation();
  const notes = api.user.getScheduleNotes.useQuery({
    scheduleId: sched?.id!
  }, {
    enabled: Boolean(sched)
  });

  function getNotesForDay(dayOfWeek: number) {
    if (!notes.data) return [];
    return notes.data.filter((note) => note.dayOfWeek === dayOfWeek);
  }

  const onCreateSchedule = () => {
    createSched.mutate({
      storeId: storeId,
      week: week
    }, {
      onSuccess: (result) => {
        navigate({
          from: routeToScheduleFrom as any,
          to: `../${result.scheduleId}`,
          search: {
            w: true,
          }
        });
      },
      onError: (error) => {
        alert("Failed to create schedule: " + error.message);
      }
    });
  }

  // Fetch store address for location-based validations
  const [storeAddress] = api.user.getStoreAddress.useSuspenseQuery({
    storeId: storeId
  });

  // Fetch multi-week hours for Hawaii ACA validation
  const currentSched = schedule?.draft ?? schedule?.published;
  const [weeksHoursMap] = api.user.get3PrecedingAnd3FollowingWeeksHours.useSuspenseQuery({
    storeId: storeId,
    currentWeek: currentSched ? currentSched.week : week,
    state: storeAddress.state
  }, {
    staleTime: 1000 * 60 * 60 // 1 hour
  });

  const validationResult = useMemo(() => {
    const sched = schedule?.draft ?? schedule?.published;
    if (!sched) return null;

    return validateSchedule(scheduleAndPeopleToValidationRequest({
      schedule: sched,
      people: map(people, (p): SchedulePersonClientDto => {
        const {
          availability: weekAvailability,
          preferences: weekPreferences
        } = getEffectiveAvailability(p.availability, sched.week, timezone);
        return {
          ...p,
          weekAvailability: weekAvailability,
          weekPreferences: weekPreferences
        }
      }),
      timezone: timezone,
      storeAreas: storeAreas,
      settings: settings,
      weeksHoursMap: weeksHoursMap,
      storeState: storeAddress.state
    }));
  }, [schedule, people, settings, weeksHoursMap]);

  const weekMetrics = useMemo(() => {
    return calculateWeekMetrics({
      schedule: sched,
      payRates: payRates,
      isForecastingEnabled: isForecastingEnabled,
      forecast: forecast
    })
  }, [sched, payRates, isForecastingEnabled, forecast]);

  const publish = useDisclosure();
  const copyWeek = useDisclosure();

  const navigate = useNavigate();
  const onGoToDay = (week: IsoWeekDate, dayOfWeek: number, shiftId?: string) => {
    publish.onClose();
    const sched = schedule?.draft ?? schedule?.published;

    navigate({
      from: Route.fullPath,
      to: `${sched?.id}?dayOfWeek=${dayOfWeek}` + (shiftId ? `&shiftId=${shiftId}` : ""),
    })
  }

  const onGoToValidationMessage = (week: IsoWeekDate, dayOfWeek: number, msg: ValidationMessage) => {
    const shiftId = "shift" in msg.info ? msg.info.shift.id : null;
    if (shiftId) {
      onGoToDay(week, dayOfWeek, shiftId);
    }
  }

  const onPublished = () => {
    toast.success("Schedule published. Team members will be notified.", {closeButton: true});
    publish.onClose();
  }


  const [autoContainerRef] = useAutoAnimate();

  const apiUtil = api.useUtils();
  const updateSchedule = api.user.updateSchedule.useMutation();
  const [isCopyNextWeekLoading, setIsCopyNextWeekLoading] = useState(false);

  const onCopyWeek = async (value: {
    days: number[]
    copyShiftAssignments: boolean;
    includeAreaTitles: string[];
    shouldFilterAreas: boolean;
  }) => {
    try {
      setIsCopyNextWeekLoading(true);
      const weeks = takeFromGenerator(generateIsoWeekDates({
        week: weekNum,
        year: year
      }, timezone), 2);

      const nextWeek = weeks[1];

      // get the schedule for next week
      const nextWeekSchedResult = await apiUtil.user.getScheduleForWeek.fetch({
        storeId: storeId,
        week: nextWeek
      });
      let nextWeekScheduleRev = nextWeekSchedResult.schedule;

      // if it doesn't exist, create it
      if (!nextWeekScheduleRev) {
        const {scheduleId: nextWeekScheduleId} = await createSched.mutateAsync({
          storeId: storeId,
          week: nextWeek
        });

        nextWeekScheduleRev = await apiUtil.user.getSchedule.fetch({
          id: nextWeekScheduleId
        });
      }

      const fromSchedule = schedule?.draft ?? schedule?.published;
      if (!nextWeekScheduleRev?.draft || !fromSchedule) {
        throw new Error("Could not find next week's schedule");
      }

      const nextWeekSchedule = nextWeekScheduleRev.draft;

      // copy the selected days from the current week's schedule to the next week's schedule
      const newNextWeekSchedule = produce(nextWeekSchedule, nextWeekDraft => {
        for (const fromWeekDay of value.days) {
          const fromDay = find(fromSchedule.days, d => d.dayOfWeek === fromWeekDay);
          if (!fromDay) return;

          const toDayIdx = findIndex(nextWeekDraft.days, d => d.dayOfWeek === fromWeekDay);
          const toDay = nextWeekDraft.days[toDayIdx];
          if (toDayIdx === -1) {
            continue;
          }

          // copy over the data, but generate new IDs for all the things
          nextWeekDraft.days[toDayIdx] = copyDay({
            fromDay: fromDay,
            dayOfWeek: fromWeekDay,
            copyShiftAssignments: value.copyShiftAssignments,
            includeAreaTitles: value.includeAreaTitles,
            toDay: toDay,
            shouldFilterAreas: value.shouldFilterAreas
          });
        }
      })

      // update next week's schedule draft
      await updateSchedule.mutateAsync({
        id: nextWeekSchedule.id,
        version: nextWeekScheduleRev.draftVersion,
        draft: newNextWeekSchedule
      })
      setIsCopyNextWeekLoading(false);

      toast(`Copied week successfully`, {
        position: "top-center",
        dismissible: true,
      });
      apiUtil.user.getSchedule.invalidate();
      apiUtil.user.getScheduleWeeks.invalidate();
      apiUtil.user.getScheduleForWeek.invalidate();
      copyWeek.onClose();

    } catch (e) {
      alert("Error copying week: " + getHumanReadableErrorMessage(e));
    }
  }

  const areaStats = useDisclosure();
  const scheduleToCopy = schedule?.draft || schedule?.published;

  return (
    <section key={week.year + "-" + week.week}
             className={cn("rounded-lg", {
               "bg-white shadow-md": isSelected,
             })}>
      <div className={"flex flex-wrap justify-between gap-3 pb-4 px-4 pt-3 items-center"}>
        <div className={'grow basis-[300px]'}>
          <div className={"flex flex-row items-baseline gap-3 mb-2 flex-wrap"}>
            <h2 role={!isSelected ? "button" : undefined}
                tabIndex={!isSelected ? 0 : undefined}
                onClick={onSelectWeek}
                className={cn("font-bold text-xl flex items-center gap-2", {
                  "text-muted-foreground": !isSelected,
                  "hover:text-gioaBlue cursor-pointer": !isSelected
                })}>
              {getFriendlyIsoWeekString(week)}
              {/*{isCurrentWeek && <CircleIcon size={20} className={"text-blue-300 fill-blue-300"}/>}*/}
            </h2>

            <div className={"text-muted-foreground text-sm"}>
              {weekMetrics.percentageShiftsAssigned.toFixed(0)}% complete
            </div>
          </div>

          <div className={"flex flex-row gap-2 items-center flex-wrap"}>
            <div
              className={"border border-gray-200 rounded-md px-3 py-1 flex flex-row gap-2 items-center justify-between"}>
              <span className={"text-gray-700 text-sm"}>
                Total Shifts
              </span>
              <span className={"font-bold"}>
                {formatFixed(weekMetrics.assignedShiftCount, 0)}
              </span>
            </div>

            <div
              className={"border border-gray-200 rounded-md px-3 py-1 flex flex-row gap-2 items-center justify-between"}>
              <span className={"text-gray-700 text-sm"}>
                Total Hours
              </span>
              <span className={"font-bold"}>
                {formatHours(weekMetrics.totalLaborHours)}
              </span>
            </div>

            <div
              className={"border border-gray-200 rounded-md px-3 py-1 flex flex-row gap-2 items-center justify-between"}>
              <span className={"text-gray-700 text-sm"}>
                Productivity
              </span>
              <span className={"font-bold"}>
                {formatCurrency(weekMetrics.averageActualProductivity)}
              </span>
            </div>

            <div
              className={"border border-gray-200 rounded-md px-3 py-1 flex flex-row gap-2 items-center justify-between"}>
              <span className={"text-gray-700 text-sm"}>
                Revenue
              </span>
              <span className={"font-bold"}>
                {formatCurrency(weekMetrics.projectedRevenue)}
              </span>
            </div>
          </div>
        </div>

        {isPastSchedule
          ? hasSchedule ?
            <div className={"flex items-center gap-2 flex-wrap justify-end"}>
              {isForecastingEnabled ?
                <Link to={"."} search={(prev: any) => ({...prev, metrics: true})}
                     disabled={!hasSchedule}
                     className={buttonVariants({variant: "outline"})}>
                Metrics
              </Link> : null}
              <Button variant={"outline"} isLoading={isCopyNextWeekLoading}
                      onClick={copyWeek.onOpen}>
                Copy
              </Button>
              <Link from={routeToScheduleFrom as any} to={routeToScheduleTo}
                    disabled={!hasSchedule}
                    className={buttonVariants({variant: isSelected ? "default" : "secondary"})}>
                View Schedule
              </Link>
            </div>
            : null
          : hasSchedule
            ?
            <div className={"text-right"}>
              <div className={"text-right mb-2 "}>
                Status: <strong>{isPublished
                ? hasDraft ? "Published with draft" : "Published"
                : "Draft"}</strong>
              </div>
              <div className={"flex items-center gap-2 flex-wrap justify-end"}>
                {isForecastingEnabled ?
                  <Link to={"."} search={(prev: any) => ({...prev, metrics: true})}
                        disabled={!hasSchedule}
                        className={buttonVariants({variant: "outline"})}>
                    Metrics
                  </Link> : null}
                <Button variant={"outline"} isLoading={isCopyNextWeekLoading}
                        onClick={copyWeek.onOpen}>
                  Copy
                </Button>
                <Button onClick={publish.onOpen}>
                  Publish Week
                </Button>
              </div>
            </div>
            : <Button onClick={onCreateSchedule} variant={isSelected ? "default" : "secondary"}
                      isLoading={createSched.isPending}>
              Create Schedule
            </Button>
        }
      </div>

      <div ref={autoContainerRef}>
        <div className="border-t rounded-b-lg overflow-hidden">
          <div className="flex overflow-x-auto bg-gray-200 gap-px">
            {times(7, i => {
              const dayOfWeek = (i + 1);
              // const numNotesForDay = filter(notes.data, n => n.dayOfWeek === dayOfWeek).length;
              const numNotesForDay = getNotesForDay(dayOfWeek)?.length ?? 0;

              return <div key={dayOfWeek} className="flex-grow min-w-60">
                <ScheduleWeekDay isWeekSelected={isSelected} storeId={storeId}
                                forecast={forecast} payRates={payRates}
                                isFirst={i === 0} warnings={validationResult?.messages ?? []}
                                isLast={i === 6} week={week} timezone={timezone}
                                events={events} routeFrom={routeToScheduleFrom}
                                date={addDays(weekStartDate, i)} isForecastingEnabled={isForecastingEnabled}
                                areaStatsExpanded={areaStats.isOpen}
                                onToggleAreaStats={areaStats.onToggle}
                                schedule={schedule} onViewNotes={onViewNotes} numNotes={numNotesForDay}
                                dayOfWeek={dayOfWeek}
                />
              </div>
            })}
          </div>
        </div>
      </div>

      {isSelected && schedule && schedule?.draft && validationResult ?
        <PublishWeekDialog isOpen={publish.isOpen} draftVersion={schedule.draftVersion ?? 1}
                           onOpenChange={publish.setOpen} onUnignoreMessage={unignoreMessage}
                           onIgnoreMessage={ignoreMessage}
                           onGoToMessage={onGoToValidationMessage}
                           validationResult={validationResult}
                           schedule={schedule.draft}
                           people={people}
                           onPublished={onPublished}/> : null}

      {scheduleToCopy ?
        <CopyWeekDialog isOpen={copyWeek.isOpen} onClose={copyWeek.onClose} timezone={timezone}
                        isLoading={isCopyNextWeekLoading}
                        onCopy={onCopyWeek} onOpen={copyWeek.onOpen} schedule={scheduleToCopy}/> : null}
    </section>);
});
