import React from 'react';
import {SheetDes<PERSON>, SheetHeader, SheetTitle} from "@/src/components/ui/sheet.tsx";


import {ScheduleRowInfo} from "../../../api/src/scheduleBuilder.util.ts";

export interface ViewTeamMembersProps {
}

export const ViewTeamMemberSheetContent: React.FC<ViewTeamMembersProps> = (props) => {
    return (
      <SheetHeader>
        <SheetTitle>Team Members</SheetTitle>
        <SheetDescription>
            View all your team members info here
        </SheetDescription>
      </SheetHeader>
    );
}
