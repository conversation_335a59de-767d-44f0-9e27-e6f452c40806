import React from 'react';
import {PersonAvatar} from "@/src/components/PersonAvatar.tsx";
import {LaborStatusIcon} from "@/src/components/LaborStatusIcon.tsx";
import {getPersonLaborStatus} from "../../../api/src/personMetrics.ts";
import {ProficiencyRating} from "@/src/components/ProficiencyRating.tsx";

export interface TeamMemberInfoProps {
  person: {
    firstName?: string;
    lastName?: string;
    age?: number;
    proficiencyRanking?: number;
  };
}

export const TeamMemberInfo: React.FC<TeamMemberInfoProps> = ({person}) => {
    return (
      <>
        <PersonAvatar size="sm" person={person}/>
        <div className="text-sm text-left">
          {person.firstName} {person.lastName}
        </div>
        <div className={"grow"}>
          {person.age ? <LaborStatusIcon laborStatus={getPersonLaborStatus(person.age)}/> : null}
        </div>
        <ProficiencyRating rank={person.proficiencyRanking ?? 0} colorScheme="light-bg"/>
      </>
    );
}
