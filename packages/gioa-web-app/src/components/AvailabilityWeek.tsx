import React from 'react';
import {Clock} from 'lucide-react';
import {PersonAvailabilityDto} from "../../../api/src/availabilitySchemas.ts";
import {Text} from "@/src/components/Text.tsx";
import {filter, find, isEmpty, map} from "lodash";
import {cn} from "@/src/util.ts";
import {to12HourTime} from '@gioa/api/src/date.util.ts';

export interface AvailabilityWeekProps {
  prevPersonAvailability?: PersonAvailabilityDto;
  personAvailability: PersonAvailabilityDto;
  title: string;
  color: string;
}

export type DayOfWeek = {
  name: string,
  abbr: string,
  dayOfWeek: number,
  isDisabled: boolean | undefined
}

export const daysOfWeek: DayOfWeek[] = [
  {name: "Sunday", abbr: "Sun", dayOfWeek: 7, isDisabled: true},
  {name: "Monday", abbr: "Mon", dayOfWeek: 1, isDisabled: false},
  {name: "Tuesday", abbr: "Tu<PERSON>", dayOfWeek: 2, isDisabled: false},
  {name: "Wednesday", abbr: "Wed", dayOfWeek: 3, isDisabled: false},
  {name: "Thursday", abbr: "Thu", dayOfWeek: 4, isDisabled: false},
  {name: "Friday", abbr: "Fri", dayOfWeek: 5, isDisabled: false},
  {name: "Saturday", abbr: "Sat", dayOfWeek: 6, isDisabled: false},
];

export const AvailabilityWeek: React.FC<AvailabilityWeekProps> = (props) => {
  const pa = props.personAvailability;

  return (
    <div className={"shadow-lg shadow-slate-100 rounded-lg border border-slate-200"}>
      <div className={cn(props.color, "rounded-t-lg flex items-center px-4 py-3 flex-wrap")}>
        <Clock className="w-5 h-5 mr-2 text-gray-500"/>
        <Text className={"grow"}>
          {props.title}
        </Text>
        <div>
          Max hours preferred: {pa.maxHoursPreferred || "(none entered)"}
        </div>
      </div>
      <div className={"flex flex-col sm:flex-row"}>
        {map(daysOfWeek, (day, idx, days) => {
          const rangesForDay = filter(pa.ranges, range => range.dayOfWeek === day.dayOfWeek);
          const isLast = idx === days.length - 1;
          return <div key={day.dayOfWeek} className={cn("flex-1 p-2 border-gray-200", {
            "sm:border-r border-b": !isLast,
          })}>
            <div className={"text-center mb-3"}>
              <Text center muted size={"sm"}>
                {day.name}
              </Text>
            </div>

            <div className={"flex flex-col gap-2"}>
              {isEmpty(rangesForDay) ? <Text size={"sm"} muted center>(Zero hours)</Text> : null}
              {map(rangesForDay, range => {
                const rangeChanged = props.prevPersonAvailability && find(props.prevPersonAvailability?.ranges, r => r.start === range.start && r.end === range.end) === undefined;
                return <div key={range.start} className={cn("rounded-sm px-3 py-2", {
                  "bg-green-100": !rangeChanged,
                  "bg-red-100": rangeChanged
                })}>
                  <Text asChild={"span"}>{to12HourTime(range.start)}</Text><br/>
                  <Text asChild={"span"}>{to12HourTime(range.end)}</Text>
                </div>
              })}
            </div>
          </div>
        })}
      </div>
    </div>

  );
}

