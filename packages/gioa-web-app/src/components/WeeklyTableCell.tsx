import React from 'react';
import {WeeklyTableShift} from "@/src/components/WeeklyTableTypes.ts";
import {CellPosition, WeeklyTableCellContent} from "@/src/components/WeeklyTableCellContent.tsx";
import {DraggableData, DraggableEvent} from 'react-draggable';

export interface WeeklyTableDayCellProps {
  personId: string;
  weekday: number;
  shifts: WeeklyTableShift[];
  onAddShift: (personId: string, day: number) => void;
  onDragStart: (shiftId: string, cellPosition: CellPosition) => void;
  onDragStop: (shiftId: string, data: DraggableData, e: DraggableEvent, cellPosition: CellPosition) => void;
  onShiftClick: (shift: WeeklyTableShift) => void;
}

export const WeeklyTableCell: React.FC<WeeklyTableDayCellProps> = React.memo(({
                                                                                shifts,
                                                                                personId,
                                                                                weekday,
                                                                                onAddShift,
                                                                                onDragStart,
                                                                                onDragStop,
                                                                                onShiftClick
                                                                              }) => {
  return (
    <td className={"h-full px-2 even:bg-slate-100 odd:bg-slate-50 align-top group"}
        style={{width: `${100/7}%`}}
        data-person-id={personId}
        data-day={weekday}>
      <WeeklyTableCellContent
        personId={personId}
        day={weekday}
        shifts={shifts}
        onAddShift={onAddShift}
        onDragStart={onDragStart}
        onDragStop={onDragStop}
        onShiftClick={onShiftClick}
      />
    </td>
  );
});

WeeklyTableCell.displayName = "WeeklyTableCell";
