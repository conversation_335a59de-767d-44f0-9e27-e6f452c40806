import React from 'react';
import {Document, Page, PDFViewer, StyleSheet, Text, View} from '@react-pdf/renderer';
import {map, sortBy} from 'lodash';
import {SchedulePersonDto} from "../../../api/src/schedulePersonDto.ts";
import {DateTimeRange} from "../../../api/src/timeSchemas.ts";
import {Table, TD, TR} from '@ag-media/react-pdf-table';
import {DateTime} from "luxon";
import {formatDateRangeByTimeFrame} from "@/src/date.util.ts";

const styles = StyleSheet.create({
  page: {
    padding: 30,
    paddingBottom: 50, // Add extra bottom padding for footer
    fontSize: 8,
  },
  header: {
    marginBottom: 8,
    fontSize: 18,
    color: '#374151',
    fontWeight: 'bold',
  },
  subHeader: {
    marginBottom: 4,
    fontSize: 12,
    color: '#6B7280',
  },
  exportInfo: {
    marginBottom: 16,
    fontSize: 10,
    color: '#6B7280',
  },
  personSection: {
    marginBottom: 16,
    pageBreakInside: 'avoid',
  },
  sectionHeader: {
    marginTop: 10,
  },
  sectionTitle: {
    fontSize: 12,
    color: '#374151',
    fontWeight: 'bold',
  },
  tableContainer: {
    marginTop: 4,
    borderRadius: 4,
  },
  cell: {
    borderCollapse: 'collapse',
    paddingVertical: 3,
    paddingHorizontal: 4,
    borderColor: '#E5E7EB',
    fontSize: 7,
  },
  headerRow: {
    backgroundColor: '#F3F4F6',
    fontWeight: 'bold',
  },
  footer: {
    position: 'absolute',
    bottom: 20,
    left: 30,
    right: 30,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderTop: 1,
    borderTopColor: '#E5E7EB',
    paddingTop: 8,
    fontSize: 8,
    color: '#6B7280',
  },
});

// Rounds number and removes trailing zeros
const cleanNumber = (hours: number) => parseFloat(hours.toFixed(2));

interface OfferedShift {
  date: DateTime;
  type: "Offered" | "Picked-up";
  range: { start: Date; end: Date };
  durationHours: number;
  approved: boolean;
  teamMemberName: string;
}

interface ShiftSwap {
  date: DateTime;
  type: "Shift Swap";
  range: { start: Date; end: Date };
  durationHours: number;
  approved: boolean;
  teamMemberName: string;
}

interface TimeOffEntry {
  date: DateTime;
  range: { start: Date; end: Date };
  durationHours: number;
  approved: boolean;
}

interface PersonShiftData {
  person: SchedulePersonDto;
  totalAvailabilityHours: number;
  totalWorkedHours: number;
  totalAdminNonOpsHours: number;
  totalShifts: number;
  swapStats: {
    offered: number;
    received: number;
  };
  offerStats: {
    offered: number;
    received: number;
  };
  timeOffRequests: number;
  shifts: Array<{
    date: DateTime;
    range: { start: Date; end: Date };
    workedHours: number;
    adminNonOpsHours: number;
    dailyAvailabilityHours: number;
  }>;
  offeredShifts: OfferedShift[];
  shiftSwaps: ShiftSwap[];
  timeOff: TimeOffEntry[];
}

interface PDFSchedulingInsightsShiftsProps {
  storeTitle: string;
  timezone: string;
  peopleShiftData: PersonShiftData[];
  dateRange: DateTimeRange;
  timeFrame: string;
  exportedBy: string;
  exportOptions?: {
    availability: boolean;
    scheduled: boolean;
    adminNonOps: boolean;
    shifts: boolean;
    shiftSwaps: boolean;
    shiftOffers: boolean;
    timeOff: boolean;
    itemizeShifts: boolean;
  };
}

// Re-exported for other components using this file as a source of truth
export const formatDateRange = (dateRange: DateTimeRange, timeFrame: string, timezone: string): string => {
  return formatDateRangeByTimeFrame({range: dateRange, timeFrame, timezone});
};

const formatTime = (date: Date, timezone: string): string => {
  return DateTime.fromJSDate(date, {zone: timezone}).toFormat('h:mm a');
};

const formatDate = (date: DateTime): string => {
  return date.toFormat('M/d/yy');
};

// Helper function to format date range for time off
const formatTimeOffDateRange = (start: Date, end: Date, timezone: string): string => {
  const startDateTime = DateTime.fromJSDate(start, {zone: timezone});
  const endDateTime = DateTime.fromJSDate(end, {zone: timezone});

  const startDate = startDateTime.toFormat('M/d/yy');
  const endDate = endDateTime.toFormat('M/d/yy');

  if (startDateTime.hasSame(endDateTime, 'day')) {
    return startDate;
  }

  return `${startDate} - ${endDate}`;
};

// Helper function to format time range for time off
const formatTimeOffTimeRange = (start: Date, end: Date, timezone: string): string => {
  const startDateTime = DateTime.fromJSDate(start, {zone: timezone});
  const endDateTime = DateTime.fromJSDate(end, {zone: timezone});

  const isAllDay = startDateTime.hour === 0 && startDateTime.minute === 0 &&
                   ((endDateTime.hour === 23 && endDateTime.minute >= 59) ||
                    (endDateTime.hour === 0 && endDateTime.minute === 0));

  if (isAllDay) {
    return "All day";
  }

  const startTime = startDateTime.toFormat('h:mm a');
  const endTime = endDateTime.toFormat('h:mm a');

  return `${startTime} - ${endTime}`;
};

// Footer component for each page
const Footer: React.FC<{ personName?: string; dateRangeText: string }> = ({
  personName,
  dateRangeText
}) => {
  return (
    <View style={styles.footer}>
      <Text>
        {personName ? `${personName} | ${dateRangeText}` : dateRangeText}
      </Text>
      <Text
        render={({ pageNumber, totalPages }) =>
          `Page ${pageNumber} of ${totalPages}`
        }
      />
    </View>
  );
};

const PersonSection: React.FC<{
  personData: PersonShiftData;
  dateRangeText: string;
  timezone: string;
  exportOptions?: {
    availability: boolean;
    scheduled: boolean;
    adminNonOps: boolean;
    shifts: boolean;
    shiftSwaps: boolean;
    shiftOffers: boolean;
    timeOff: boolean;
    itemizeShifts: boolean;
  };
}> = ({
  personData,
  dateRangeText,
  timezone,
  exportOptions
}) => {
  const {
    person,
    totalAvailabilityHours,
    totalWorkedHours,
    totalAdminNonOpsHours,
    totalShifts,
    swapStats,
    offerStats,
    timeOffRequests,
    shifts,
    offeredShifts,
    shiftSwaps,
    timeOff
  } = personData;

  return (
          <View style={styles.personSection}>
            <View style={{flexDirection: 'row', gap: 6, alignItems: 'flex-end'}}>
              <Text style={{...styles.sectionTitle, fontSize: 14}}>
                {person.firstName} {person.lastName}
              </Text>
              <View style={{width: 1, height: 10, backgroundColor: '#cececf'}}/>
              <View style={{marginBottom: 1, color: '#6B7280'}}>
                <Text style={{fontSize: 10}}>{dateRangeText}</Text>
              </View>
            </View>


            {/* Summary Section */}
            <View style={styles.sectionHeader}>
              <Text style={styles.sectionTitle}>Summary</Text>
              <View style={styles.tableContainer}>
                <Table>
                  {/* Header Row */}
                  <TR style={styles.headerRow}>
                    {exportOptions?.availability !== false && (
                      <TD style={styles.cell}>
                        <Text>Availability</Text>
                      </TD>
                    )}
                    {exportOptions?.scheduled !== false && (
                      <TD style={styles.cell}>
                        <Text>Scheduled</Text>
                      </TD>
                    )}
                    {exportOptions?.adminNonOps !== false && (
                      <TD style={styles.cell}>
                        <Text>Admin/Non-Ops</Text>
                      </TD>
                    )}
                    {exportOptions?.shifts !== false && (
                      <TD style={styles.cell}>
                        <Text>Shifts</Text>
                      </TD>
                    )}
                    {exportOptions?.shiftSwaps !== false && (
                      <TD style={styles.cell}>
                        <Text>Swaps</Text>
                      </TD>
                    )}
                    {exportOptions?.shiftOffers !== false && (
                      <TD style={styles.cell}>
                        <Text>Offers</Text>
                      </TD>
                    )}
                    {exportOptions?.timeOff !== false && (
                      <TD style={styles.cell}>
                        <Text>Time Off</Text>
                      </TD>
                    )}
                  </TR>

                  {/* Data Row */}
                  <TR>
                    {exportOptions?.availability !== false && (
                      <TD style={styles.cell}>
                        <Text>{cleanNumber(totalAvailabilityHours)} hrs</Text>
                      </TD>
                    )}
                    {exportOptions?.scheduled !== false && (
                      <TD style={styles.cell}>
                        <Text>{cleanNumber(totalWorkedHours)} hrs</Text>
                      </TD>
                    )}
                    {exportOptions?.adminNonOps !== false && (
                      <TD style={styles.cell}>
                        <Text>{cleanNumber(totalAdminNonOpsHours)} hrs</Text>
                      </TD>
                    )}
                    {exportOptions?.shifts !== false && (
                      <TD style={styles.cell}>
                        <Text>{totalShifts}</Text>
                      </TD>
                    )}
                    {exportOptions?.shiftSwaps !== false && (
                      <TD style={styles.cell}>
                        <View>
                          <Text style={{fontSize: 8, fontWeight: 'bold'}}>{swapStats.offered} Offered</Text>
                          <Text style={{fontSize: 7, color: '#6B7280'}}>{swapStats.received} Pick-ups</Text>
                        </View>
                      </TD>
                    )}
                    {exportOptions?.shiftOffers !== false && (
                      <TD style={styles.cell}>
                        <View>
                          <Text style={{fontSize: 8, fontWeight: 'bold'}}>{offerStats.offered} Offered</Text>
                          <Text style={{fontSize: 7, color: '#6B7280'}}>{offerStats.received} Pick-ups</Text>
                        </View>
                      </TD>
                    )}
                    {exportOptions?.timeOff !== false && (
                      <TD style={styles.cell}>
                        <Text>{timeOffRequests}</Text>
                      </TD>
                    )}
                  </TR>
                </Table>
              </View>
            </View>

            {/* Scheduled Shifts Section */}
            {exportOptions?.shifts !== false && (
              <View style={styles.sectionHeader}>
              <View style={{flexDirection: 'row', gap: 6, alignItems: 'flex-end'}}>
                <Text style={styles.sectionTitle}>
                  Scheduled Shifts
                </Text>
                <View style={{width: 1, height: 10, backgroundColor: '#cececf'}}/>
                <View style={{marginBottom: 1, color: '#6B7280'}}>
                  <Text>{totalShifts} Shifts, {cleanNumber(totalWorkedHours)} hrs</Text>
                </View>
              </View>

              {shifts.length > 0 ? (
                      <View style={styles.tableContainer}>
                        <Table>
                          {/* Header Row */}
                          <TR style={styles.headerRow}>
                            <TD style={styles.cell}>
                              <Text>Date</Text>
                            </TD>
                            <TD style={styles.cell}>
                              <Text>Time</Text>
                            </TD>
                            {exportOptions?.scheduled !== false && (
                              <TD style={styles.cell}>
                                <Text>Scheduled</Text>
                              </TD>
                            )}
                            {exportOptions?.adminNonOps !== false && (
                              <TD style={styles.cell}>
                                <Text>Admin / Non-Ops</Text>
                              </TD>
                            )}
                            {exportOptions?.availability !== false && (
                              <TD style={styles.cell}>
                                <Text>Availability</Text>
                              </TD>
                            )}
                          </TR>

                          {/* Data Rows */}
                          {map(shifts, (shift, index) => (
                                  <TR key={index} wrap={false}>
                                    <TD style={styles.cell}>
                                      <Text>{formatDate(shift.date)}</Text>
                                    </TD>
                                    <TD style={styles.cell}>
                                      <Text>{formatTime(shift.range.start, timezone)} - {formatTime(shift.range.end, timezone)}</Text>
                                    </TD>
                                    {exportOptions?.scheduled !== false && (
                                      <TD style={styles.cell}>
                                        <Text>{cleanNumber(shift.workedHours)} hrs</Text>
                                      </TD>
                                    )}
                                    {exportOptions?.adminNonOps !== false && (
                                      <TD style={styles.cell}>
                                        <Text>{shift.adminNonOpsHours > 0 ? `${cleanNumber(shift.adminNonOpsHours)} hrs` : '-'}</Text>
                                      </TD>
                                    )}
                                    {exportOptions?.availability !== false && (
                                      <TD style={styles.cell}>
                                        <Text>{cleanNumber(shift.dailyAvailabilityHours)} hrs</Text>
                                      </TD>
                                    )}
                                  </TR>
                          ))}
                        </Table>
                      </View>
              ): null}
              </View>
            )}

            {/* Offered Shifts Section */}
            {exportOptions?.shiftOffers !== false && (
              <View style={styles.sectionHeader}>
              <View style={{flexDirection: 'row', gap: 6, alignItems: 'flex-end'}}>
                <Text style={styles.sectionTitle}>
                  Offered Shifts
                </Text>
                <View style={{width: 1, height: 10, backgroundColor: '#cececf'}}/>
                <View style={{marginBottom: 1, color: '#6B7280'}}>
                  <Text>{offeredShifts.length} Offers</Text>
                </View>
              </View>

              {offeredShifts.length > 0 ? (
                      <View style={styles.tableContainer}>
                        <Table>
                          {/* Header Row */}
                          <TR style={styles.headerRow}>
                            <TD style={styles.cell}>
                              <Text>Date</Text>
                            </TD>
                            <TD style={styles.cell}>
                              <Text>Type</Text>
                            </TD>
                            <TD style={styles.cell}>
                              <Text>Time</Text>
                            </TD>
                            <TD style={styles.cell}>
                              <Text>Length</Text>
                            </TD>
                            <TD style={styles.cell}>
                              <Text>Approved</Text>
                            </TD>
                            <TD style={styles.cell}>
                              <Text>Team Member</Text>
                            </TD>
                          </TR>

                          {/* Data Rows */}
                          {map(offeredShifts, (offeredShift, index) => (
                                  <TR key={index} wrap={false}>
                                    <TD style={styles.cell}>
                                      <Text>{formatDate(offeredShift.date)}</Text>
                                    </TD>
                                    <TD style={styles.cell}>
                                      <Text>{offeredShift.type}</Text>
                                    </TD>
                                    <TD style={styles.cell}>
                                      <Text>{formatTime(offeredShift.range.start, timezone)} - {formatTime(offeredShift.range.end, timezone)}</Text>
                                    </TD>
                                    <TD style={styles.cell}>
                                      <Text>{cleanNumber(offeredShift.durationHours)} hrs</Text>
                                    </TD>
                                    <TD style={styles.cell}>
                                      <Text>{offeredShift.approved ? 'Yes' : 'No'}</Text>
                                    </TD>
                                    <TD style={styles.cell}>
                                      <Text>{offeredShift.teamMemberName}</Text>
                                    </TD>
                                  </TR>
                          ))}
                        </Table>
                      </View>
              ) : null}
              </View>
            )}

            {/* Shift Swaps Section */}
            {exportOptions?.shiftSwaps !== false && (
              <View style={styles.sectionHeader}>
              <View style={{flexDirection: 'row', gap: 6, alignItems: 'flex-end'}}>
                <Text style={styles.sectionTitle}>
                  Shift Swaps
                </Text>
                <View style={{width: 1, height: 10, backgroundColor: '#cececf'}}/>
                <View style={{marginBottom: 1, color: '#6B7280'}}>
                  <Text>{shiftSwaps.length} Swaps</Text>
                </View>
              </View>

              {shiftSwaps.length > 0 ? (
                      <View style={styles.tableContainer}>
                        <Table>
                          {/* Header Row */}
                          <TR style={styles.headerRow}>
                            <TD style={styles.cell}>
                              <Text>Date</Text>
                            </TD>
                            <TD style={styles.cell}>
                              <Text>Type</Text>
                            </TD>
                            <TD style={styles.cell}>
                              <Text>Time</Text>
                            </TD>
                            <TD style={styles.cell}>
                              <Text>Length</Text>
                            </TD>
                            <TD style={styles.cell}>
                              <Text>Approved</Text>
                            </TD>
                            <TD style={styles.cell}>
                              <Text>Team Member</Text>
                            </TD>
                          </TR>

                          {/* Data Rows */}
                          {map(shiftSwaps, (shiftSwap, index) => (
                                  <TR key={index} wrap={false}>
                                    <TD style={styles.cell}>
                                      <Text>{formatDate(shiftSwap.date)}</Text>
                                    </TD>
                                    <TD style={styles.cell}>
                                      <Text>{shiftSwap.type}</Text>
                                    </TD>
                                    <TD style={styles.cell}>
                                      <Text>{formatTime(shiftSwap.range.start, timezone)} - {formatTime(shiftSwap.range.end, timezone)}</Text>
                                    </TD>
                                    <TD style={styles.cell}>
                                      <Text>{cleanNumber(shiftSwap.durationHours)} hrs</Text>
                                    </TD>
                                    <TD style={styles.cell}>
                                      <Text>{shiftSwap.approved ? 'Yes' : 'No'}</Text>
                                    </TD>
                                    <TD style={styles.cell}>
                                      <Text>{shiftSwap.teamMemberName}</Text>
                                    </TD>
                                  </TR>
                          ))}
                        </Table>
                      </View>
              ) : null}
              </View>
            )}

            {/* Time Off Section */}
            {exportOptions?.timeOff !== false && (
              <View style={styles.sectionHeader}>
              <View style={{flexDirection: 'row', gap: 6, alignItems: 'flex-end'}}>
                <Text style={styles.sectionTitle}>
                  Time Off
                </Text>
                <View style={{width: 1, height: 10, backgroundColor: '#cececf'}}/>
                <View style={{marginBottom: 1, color: '#6B7280'}}>
                  <Text>{timeOff.length} Requests</Text>
                </View>
              </View>

              {timeOff.length > 0 ? (
                      <View style={styles.tableContainer}>
                        <Table>
                          {/* Header Row */}
                          <TR style={styles.headerRow}>
                            <TD style={styles.cell}>
                              <Text>Date</Text>
                            </TD>
                            <TD style={styles.cell}>
                              <Text>Time</Text>
                            </TD>
                            <TD style={styles.cell}>
                              <Text>Length</Text>
                            </TD>
                            <TD style={styles.cell}>
                              <Text>Approved</Text>
                            </TD>
                          </TR>

                          {/* Data Rows */}
                          {map(timeOff, (timeOffEntry, index) => (
                                  <TR key={index} wrap={false}>
                                    <TD style={styles.cell}>
                                      <Text>{formatTimeOffDateRange(timeOffEntry.range.start, timeOffEntry.range.end, timezone)}</Text>
                                    </TD>
                                    <TD style={styles.cell}>
                                      <Text>{formatTimeOffTimeRange(timeOffEntry.range.start, timeOffEntry.range.end, timezone)}</Text>
                                    </TD>
                                    <TD style={styles.cell}>
                                      <Text>{cleanNumber(timeOffEntry.durationHours)} hrs</Text>
                                    </TD>
                                    <TD style={styles.cell}>
                                      <Text>{timeOffEntry.approved ? 'Yes' : 'No'}</Text>
                                    </TD>
                                  </TR>
                          ))}
                        </Table>
                      </View>
              ) : null}
              </View>
            )}
          </View>
  );
};

export const PDFSchedulingInsightsShifts: React.FC<PDFSchedulingInsightsShiftsProps> = ({
                                                                                          storeTitle,
                                                                                          timezone,
                                                                                          peopleShiftData,
                                                                                          dateRange,
                                                                                          timeFrame,
                                                                                          exportedBy,
                                                                                          exportOptions,
                                                                                        }) => {
  const sortedPeopleData = sortBy(peopleShiftData, [
    p => p.person.lastName.toLowerCase(),
    p => p.person.firstName.toLowerCase()
  ]);

  const dateRangeText = formatDateRange(dateRange, timeFrame, timezone);
  const exportedAt = DateTime.now().setZone(timezone).toFormat('M/d/yy h:mma');

  return (
          <PDFViewer style={{width: '100%', height: '100vh'}}>
            <Document title={`Scheduling Insights - Shifts - ${storeTitle}`}>
              {/* First page with header info */}
              <Page size="LETTER" style={styles.page}>
                <Text style={styles.header}>
                  Scheduling Insights
                </Text>

                <Text style={styles.subHeader}>
                  Date Range: {dateRangeText}
                </Text>

                <Text style={styles.subHeader}>
                  Location: {storeTitle}
                </Text>

                <Text style={styles.exportInfo}>
                  Exported: {exportedAt} by {exportedBy}
                </Text>

                {/* First person on the same page as header */}
                {sortedPeopleData.length > 0 && (
                        <PersonSection
                                personData={sortedPeopleData[0]}
                                dateRangeText={dateRangeText}
                                timezone={timezone}
                                exportOptions={exportOptions}
                        />
                )}

                <Footer
                  personName={sortedPeopleData.length > 0 ? `${sortedPeopleData[0].person.firstName} ${sortedPeopleData[0].person.lastName}` : undefined}
                  dateRangeText={dateRangeText}
                />
              </Page>

              {/* Each additional person gets their own page */}
              {map(sortedPeopleData.slice(1), (personData) => (
                      <Page key={personData.person.id} size="LETTER" style={styles.page}>
                        <PersonSection
                                personData={personData}
                                dateRangeText={dateRangeText}
                                timezone={timezone}
                                exportOptions={exportOptions}
                        />
                        <Footer
                          personName={`${personData.person.firstName} ${personData.person.lastName}`}
                          dateRangeText={dateRangeText}
                        />
                      </Page>
              ))}
            </Document>
          </PDFViewer>
  );
};
