import React from 'react';
import {PersonAvatar} from "@/src/components/PersonAvatar.tsx";
import {Text} from "@/src/components/Text.tsx";
import {formatDateMonthNoTime} from "@/src/date.util.ts";
import {RequestComment} from "@/src/components/RequestComment.tsx";
import {PersonDto} from "../../../api/src/schemas.ts";

export interface RequestCommentBoxProps {
  request: {
    submittedByPerson?: PersonDto;
    submittedAt?: Date;
    submittedReason?: string;
    approvedByPerson?: PersonDto;
    approvedAt?: Date;
    approvedReason?: string;
    declinedByPerson?: PersonDto;
    declinedAt?: Date;
    declinedReason?: string;
    cancelledByPerson?: PersonDto;
    cancelledAt?: Date;
    cancelledReason?: string;
  }
}

export const RequestCommentBox: React.FC<RequestCommentBoxProps> = ({request: req}) => {
  return (
    <div className={"border rounded-lg px-4 py-3 space-y-4"}>
      {req.approvedByPerson && req.approvedReason && req.approvedAt ?
        <RequestComment person={req.approvedByPerson} date={req.approvedAt}
                        comment={req.approvedReason}
                        actionLabel={"approved"}/> : null}
      {req.declinedByPerson && req.declinedReason && req.declinedAt ?
        <RequestComment person={req.declinedByPerson} date={req.declinedAt}
                        comment={req.declinedReason}
                        actionLabel={"declined"}/> : null}
      {req.cancelledByPerson && req.cancelledReason && req.cancelledAt ?
        <RequestComment person={req.cancelledByPerson} date={req.cancelledAt}
                        comment={req.cancelledReason}
                        actionLabel={"cancelled"}/> : null}
    </div>
  );
}
