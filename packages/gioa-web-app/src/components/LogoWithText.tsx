import * as React from "react"

interface LogoSvgProps {
  fill?: string;
  width?: number;
}

export function LogoWithText(props: LogoSvgProps) {
  // Default width
  const defaultWidth = 164;
  const defaultHeight = 147;
  const aspectRatio = defaultWidth / defaultHeight;

  // Set the width to the provided prop or default width
  const width = props.width ?? defaultWidth;
  const height = width / aspectRatio;

  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 164 147"
      fill="none"
    >
      <circle cx={81.9717} cy={14.5236} r={14.5236} fill="#fff"/>
      <circle cx={111.019} cy={31.6541} r={9.68242} fill="#E51636"/>
      <path
        d="M51.062 98.5a8.565 8.565 0 01-8.565-8.565V25.28a7.032 7.032 0 0111.911-5.064L98.004 62.23a1.858 1.858 0 003.146-1.338v-8.756a8.565 8.565 0 018.566-8.565h2.42a8.565 8.565 0 018.565 8.565v39.725a6.639 6.639 0 01-11.236 4.79L65.729 54.67a2.418 2.418 0 00-4.092 1.745v33.519a8.565 8.565 0 01-8.565 8.565h-2.01z"
        fill="#fff"
      />
      <path
        d="M101.15 57.241v-5.105a8.566 8.566 0 018.566-8.565h2.42a8.565 8.565 0 018.565 8.565v18.062c-8.565-9.24-10.986-3.32-19.551-12.957z"
        fill="#E51636"
      />
      <path
        d="M16.177 119.792c2.048 0 3.872.416 5.472 1.248 1.632.8 2.912 2.048 3.84 3.744.928 1.664 1.392 3.808 1.392 6.432V146h-7.488v-13.632c0-2.08-.464-3.616-1.392-4.608-.896-.992-2.176-1.488-3.84-1.488-1.184 0-2.256.256-3.216.768-.928.48-1.664 1.232-2.208 2.256-.512 1.024-.768 2.336-.768 3.936V146H.481v-25.824h7.152v7.152l-1.344-2.16a9.565 9.565 0 013.984-3.984c1.728-.928 3.696-1.392 5.904-1.392zM50.949 146v-5.04l-.48-1.104v-9.024c0-1.6-.497-2.848-1.489-3.744-.96-.896-2.447-1.344-4.463-1.344-1.377 0-2.736.224-4.08.672-1.313.416-2.432.992-3.36 1.728l-2.688-5.232c1.407-.992 3.103-1.76 5.087-2.304 1.985-.544 4-.816 6.049-.816 3.936 0 6.992.928 9.168 2.784 2.175 1.856 3.264 4.752 3.264 8.688V146h-7.008zm-7.872.384c-2.017 0-3.744-.336-5.184-1.008-1.44-.704-2.544-1.648-3.313-2.832-.767-1.184-1.151-2.512-1.151-3.984 0-1.536.367-2.88 1.103-4.032.769-1.152 1.969-2.048 3.6-2.688 1.633-.672 3.76-1.008 6.385-1.008h6.863v4.368h-6.047c-1.76 0-2.977.288-3.648.864-.64.576-.96 1.296-.96 2.16 0 .96.367 1.728 1.103 2.304.768.544 1.808.816 3.12.816 1.248 0 2.368-.288 3.36-.864.992-.608 1.712-1.488 2.16-2.64l1.152 3.456c-.543 1.664-1.535 2.928-2.975 3.792-1.44.864-3.297 1.296-5.569 1.296zm33.657 0c-3.04 0-5.408-.768-7.105-2.304-1.696-1.568-2.544-3.888-2.544-6.96v-22.656h7.489v22.56c0 1.088.288 1.936.864 2.544.575.576 1.36.864 2.352.864 1.183 0 2.191-.32 3.023-.96l2.016 5.28c-.767.544-1.695.96-2.783 1.248-1.056.256-2.16.384-3.313.384zm-13.632-19.872v-5.76h17.904v5.76H63.102zM88.472 146v-25.824h7.489V146h-7.488zm3.745-29.424c-1.376 0-2.496-.4-3.36-1.2-.864-.8-1.296-1.792-1.296-2.976s.432-2.176 1.296-2.976c.864-.8 1.984-1.2 3.36-1.2s2.496.384 3.36 1.152c.864.736 1.296 1.696 1.296 2.88 0 1.248-.432 2.288-1.296 3.12-.832.8-1.952 1.2-3.36 1.2zm24.421 29.808c-2.752 0-5.2-.576-7.344-1.728-2.112-1.152-3.792-2.72-5.04-4.704-1.216-2.016-1.824-4.304-1.824-6.864 0-2.592.608-4.88 1.824-6.864a12.963 12.963 0 015.04-4.704c2.144-1.152 4.592-1.728 7.344-1.728 2.72 0 5.152.576 7.296 1.728 2.144 1.12 3.824 2.672 5.04 4.656 1.216 1.984 1.824 4.288 1.824 6.912 0 2.56-.608 4.848-1.824 6.864-1.216 1.984-2.896 3.552-5.04 4.704-2.144 1.152-4.576 1.728-7.296 1.728zm0-6.144c1.248 0 2.368-.288 3.36-.864.992-.576 1.776-1.392 2.352-2.448.576-1.088.864-2.368.864-3.84 0-1.504-.288-2.784-.864-3.84-.576-1.056-1.36-1.872-2.352-2.448-.992-.576-2.112-.864-3.36-.864-1.248 0-2.368.288-3.36.864-.992.576-1.792 1.392-2.4 2.448-.576 1.056-.864 2.336-.864 3.84 0 1.472.288 2.752.864 3.84.608 1.056 1.408 1.872 2.4 2.448.992.576 2.112.864 3.36.864zm36.302-20.448c2.048 0 3.872.416 5.472 1.248 1.632.8 2.912 2.048 3.84 3.744.928 1.664 1.392 3.808 1.392 6.432V146h-7.488v-13.632c0-2.08-.464-3.616-1.392-4.608-.896-.992-2.176-1.488-3.84-1.488-1.184 0-2.256.256-3.216.768-.928.48-1.664 1.232-2.208 2.256-.512 1.024-.768 2.336-.768 3.936V146h-7.488v-25.824h7.152v7.152l-1.344-2.16a9.565 9.565 0 013.984-3.984c1.728-.928 3.696-1.392 5.904-1.392z"
        fill="#fff"
      />
    </svg>
  )
}
