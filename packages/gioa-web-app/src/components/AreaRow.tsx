import React from 'react';
import {cn} from "@/src/util.ts";
import {IncrementMetrics} from "../../../api/src/scheduleBuilder.util.ts";
import {map, maxBy} from 'lodash';

export interface AreaRowProps {
  rowHeight: number;
  rowIndex: number;
  incrementWidth: number;
  incrementMetrics: IncrementMetrics[];
  numShiftsInArea: number;
  isAreaHeatmapShown: boolean;
}

type MetricSpan = {
  start: number,
  end: number,
  metrics: IncrementMetrics,
};

function getDisplayNum(metrics?: IncrementMetrics) {
  return metrics?.numOpsShifts ?? 0;
}

export const AreaRow = React.memo(({
                                     rowHeight, incrementMetrics, incrementWidth, rowIndex,
                                     numShiftsInArea, isAreaHeatmapShown
                                   }: AreaRowProps) => {

  const maxNumShifts = getDisplayNum(maxBy(incrementMetrics, m => getDisplayNum(m)));
  const spans: Array<MetricSpan> = [];
  let currentSpan: MetricSpan = {
    start: 0,
    end: 1,
    metrics: incrementMetrics[0],
  };

  for (let i = 0; i < incrementMetrics.length; i++) {
    const metrics = incrementMetrics[i];
    const nextMetrics = incrementMetrics[i + 1];

    if (getDisplayNum(nextMetrics) !== getDisplayNum(metrics)) {
      currentSpan.end = i + 1;
      spans.push(currentSpan);
      currentSpan = {
        start: i + 1,
        end: i + 2,
        metrics: nextMetrics,
      };
    }
  }

  return (
    <div style={{height: rowHeight}}
         className={cn("relative w-full px-4 -ml-4 py-1 flex flex-row items-center",)}>
      {isAreaHeatmapShown ? map(spans, ({start, end, metrics}, idx, all) => {
        const prevSpan = all[idx - 1];
        const nextSpan = all[idx + 1];
        const opacity = maxNumShifts > 0 ? getDisplayNum(metrics) / maxNumShifts : 0;
        const numIncrements = end - start;

        return <div key={start + end} style={{width: incrementWidth * numIncrements, background: `rgba(62, 75, 126, ${opacity})`}} className={cn("h-full px-1 flex items-center", {
          "text-white": opacity > 0.6,
          "text-black": opacity < 0.6,
          "rounded-l-md": !prevSpan || getDisplayNum(prevSpan.metrics) === 0,
          "rounded-r-md": !nextSpan || getDisplayNum(nextSpan.metrics) === 0,
        })}>
          {getDisplayNum(metrics) > 0 ? getDisplayNum(metrics) : ""}
        </div>
      }) : null}
    </div>
  );
});
