import React from 'react';
import {Helmet} from "react-helmet";
import {<PERSON>} from "@tanstack/react-router";
import {api} from "@/src/api.ts";
import {Badge} from "@/src/components/ui/badge.tsx";

export interface RequestNavProps {
  storeId: string;
  routeFullPath: string;
}

export const RequestNav: React.FC<RequestNavProps> = ({storeId, routeFullPath}) => {
  const [store] = api.user.getStoreAdmin.useSuspenseQuery({
    storeId
  });

  const timeOffRequests = api.user.getStoreTimeOffRequests.useQuery({
    storeId,
    status: "pending"
  });
  const shiftOffers = api.user.getShiftOffers.useQuery({
    storeId: storeId,
    status: "pending",
    appVersion: "web"
  })
  const availabilityRequests = api.user.getAvailabilityRequests.useQuery({
    storeId: storeId,
    status: "pending"
  });
  const numTimeOffPending = (timeOffRequests.data?.items.length ?? 0);
  const numShiftOffersPending = (shiftOffers.data?.items.length ?? 0);
  const numAvailabilityPending = (availabilityRequests.data?.items.length ?? 0);

  return (
    <>
      <Helmet>
        <title>
          Requests - {store.title} - Nation
        </title>
      </Helmet>

      <nav className={"flex flex-wrap mb-6"}>
        <Link from={routeFullPath as any} className={"border-b px-6 py-2"}
              activeProps={{className: "border-b-4 border-blue-500 text-foreground"}}
              inactiveProps={{className: "border-gray-300 hover:text-foreground text-muted-foreground"}}
              to={"../availability" as any}>
          Availability
          {numAvailabilityPending > 0 ?
                  <Badge size={"sm"} colorScheme={"destructive"} className={"ml-2 px-1"}>
                    {numAvailabilityPending}
                  </Badge>
                  : null}
        </Link>

        <Link from={routeFullPath  as any} className={"border-b px-6 py-2"}
              activeProps={{className: "border-b-4 border-blue-500 text-foreground"}}
              inactiveProps={{className: "border-gray-300 hover:text-foreground text-muted-foreground"}}
              to={"../timeOff" as any}>
          Time off
          {numTimeOffPending > 0 ?
            <Badge size={"sm"} colorScheme={"destructive"} className={"ml-2 px-1"}>
              {numTimeOffPending}
            </Badge>
            : null}
        </Link>

        <Link from={routeFullPath as any} className={"border-b px-6 py-2"}
              activeProps={{className: "border-b-4 border-blue-500 text-foreground"}}
              inactiveProps={{className: "border-gray-300 hover:text-foreground text-muted-foreground"}}
              to={"../shiftOffers" as any}>
          Offers
          {numShiftOffersPending > 0 ?
            <Badge size={"sm"} colorScheme={"destructive"} className={"ml-2 px-1"}>
              {numShiftOffersPending}
            </Badge>
            : null}
        </Link>
      </nav>
    </>
  );
}
