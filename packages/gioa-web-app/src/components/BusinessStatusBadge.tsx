import React from 'react';
import {BusinessDto, BusinessInvitationDto} from "../../../api/src/routers/gioa-web-app/admin.schemas.ts";
import {Badge, BadgeProps} from "@/src/components/ui/badge.tsx";

export interface BusinessStatusBadgeProps {
  status: string;
}

export function getBusinessInvitationStatus(invitation?: BusinessInvitationDto): string {
  if (!invitation) {
    return "Not Sent";
  }
  return invitation.isAccepted ? "Accepted" : "Pending";
}

export function getBusinessStatus(business: BusinessDto): string {
  if (business.isArchived) {
    return "Archived";
  }
  return getBusinessInvitationStatus(business.invitation);
}

const statusToInfo: { [key: string]: { colorScheme: BadgeProps["colorScheme"], label: string } } = {
  Pending: {colorScheme: "default", label: "Pending"},
  Accepted: {colorScheme: "success", label: "Accepted"},
  "Not Sent": {colorScheme: "outline", label: "Not Sent"},
  Archived: {colorScheme: "destructive", label: "Archived"},
};

export const BusinessStatusBadge: React.FC<BusinessStatusBadgeProps> = ({status}) => {
  const {colorScheme, label} = statusToInfo[status];

  return (
    <Badge colorScheme={colorScheme}>
      {label}
    </Badge>
  );
}
