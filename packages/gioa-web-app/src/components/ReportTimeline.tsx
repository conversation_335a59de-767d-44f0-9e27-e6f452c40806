import React from 'react';
import {times} from "lodash";
import {DailyTimeRange} from "../../../api/src/timeSchemas.ts";
import {cn} from "@/src/util.ts";
import {incrementToFriendlyTime, ScheduleBuilderEvent} from "../../../api/src/scheduleBuilder.util.ts";
import {ScheduleTimelineEvents} from "@/src/components/ScheduleTimelineEvents.tsx";

export interface ReportTimelineProps {
  width: number;
  topbarHeight: number;
  numIncrements: number;
  incrementWidth: number;
  storeHours: DailyTimeRange;
  isEventsOpen: boolean;
  timelineHeight: number;
  events: ScheduleBuilderEvent[];
}

export const ReportTimeline: React.FC<ReportTimelineProps> = ({
                                                                width,
                                                                topbarHeight,
                                                                numIncrements,
                                                                incrementWidth,
                                                                storeHours, timelineHeight,
                                                                isEventsOpen, events
                                                              }) => {
  const onOpenEvent = (eventId: string) => {
    // TODO?
  }

  return (
    <div style={{
      minWidth: width,
      height: topbarHeight,
      background: `rgba(255, 255, 255, 0.8)`,
      backdropFilter: `blur(2px)`
    }}
         className={"z-10 rounded-lg sticky mx-4 border-b border-slate-200 shadow-sm top-0"}>
      <div className={"group h-full"}>
        {times(numIncrements / 4 + 1, (markIndex) => {
          const isLast = markIndex === numIncrements / 4;
          return <div key={markIndex} style={{width: incrementWidth * 4}}
                      className={cn("inline-block border-slate-300 h-full py-3 px-2", !isLast ? "border-r" : undefined)}>
            {incrementToFriendlyTime(storeHours, markIndex * 4, true)}
          </div>;
        })}
      </div>

      <ScheduleTimelineEvents isEventsOpen={isEventsOpen} events={events} onOpenEvent={onOpenEvent}
                              selectedEventId={undefined} numIncrements={numIncrements}
                              incrementWidth={incrementWidth} timelineHeight={timelineHeight}/>
    </div>
  );
}
