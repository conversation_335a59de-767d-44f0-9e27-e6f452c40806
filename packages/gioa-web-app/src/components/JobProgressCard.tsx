import React from 'react';
import {Card, CardContent, CardDescription, CardHeader, CardTitle} from "@/src/components/ui/card.tsx";
import {JobStatusBadge} from "@/src/components/JobStatusBadge.tsx";
import IndeterminateProgressBar from "@/src/components/IndeterminateProgressBar.tsx";

export interface JobProgressCardProps {
  title: string;
  jobStatus: string;
  errorMessage?: string;
  pendingLabel: string;
  startedLabel: string;
  successLabel: string;
  errorLabel: string;
  description?: string;
}

export const JobProgressBody: React.FC<Omit<JobProgressCardProps, "title" | "description">> = ({
                                                                   jobStatus,
                                                                   errorMessage,
                                                                   errorLabel,
                                                                   pendingLabel,
                                                                   startedLabel,
                                                                   successLabel,
                                                                 }) => {
  return <div>
    <JobStatusBadge status={jobStatus} className="mb-3" size={"md"}/>
    {jobStatus === "pending" && <>
        <p className={"text-muted-foreground mb-3 text-sm"}>{pendingLabel ?? "Pending"}...</p>
        <IndeterminateProgressBar/>
    </>}
    {jobStatus === "started" && <>
        <p className={"text-muted-foreground mb-3 text-sm"}>{startedLabel ?? "Started"}...</p>
        <IndeterminateProgressBar color={"secondary"}/>
    </>}
    {jobStatus === "success" && (
      <p>{successLabel ?? "Success"}</p>
    )}
    {jobStatus === "error" && (
      <div>
        <p>{errorLabel ?? "Error processing:"}</p>
        <p className="text-sm mt-1">{errorMessage}</p>
      </div>
    )}
  </div>
}

export const JobProgressCard: React.FC<JobProgressCardProps> = ({
                                                                  title,
                                                                  description,
                                                                  ...props
                                                                }) => {
  return (
    <Card>
      <CardHeader>
        <CardTitle>{title}</CardTitle>
        {description && <CardDescription>{description}</CardDescription>}
      </CardHeader>
      <CardContent>
        <JobProgressBody {...props}/>
      </CardContent>
    </Card>
  );
}
