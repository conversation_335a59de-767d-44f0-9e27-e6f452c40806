import React, {useEffect, useState} from 'react';
import {Popover, PopoverContent, PopoverTrigger} from "./ui/popover.tsx"
import {Button} from "@/src/components/ui/button.tsx";
import {CalendarDaysIcon} from "lucide-react";
import {EventsPanelContent} from "@/src/components/EventsPanelContent.tsx";
import {IsoWeekDate} from '@gioa/api/src/timeSchemas.ts';
import {useDisclosure} from "@/src/hooks/useDisclosure.tsx";
import {cn} from "@/src/util.ts";
import {filter, find} from "lodash";
import {api} from "@/src/api.ts";
import {produce} from "immer";
import {toast} from "sonner";
import {ScheduleEventDto} from '@gioa/api/src/scheduleEventSchemas.ts';

export interface EventsPopoverProps {
  events: ScheduleEventDto[];
  week: IsoWeekDate;
  dayOfWeek: number;
  storeId: string;
  timezone: string | null;
}

export const EventsPopover: React.FC<EventsPopoverProps> = ({events, week, dayOfWeek, storeId, timezone}) => {
  const defaultExpandedHeight = 500;
  const popover = useDisclosure();
  const onBack = () => {
    popover.onClose();
  }

  const [localEvents, setLocalEvents] = useState<ScheduleEventDto[]>(events);
  useEffect(() => {
    setLocalEvents(events);
  }, [events]);

  const upsertScheduleEventMutation = api.user.upsertScheduleEvent.useMutation();
  const deleteScheduleEventMutation = api.user.deleteScheduleEvent.useMutation();

  const onUpsertEvent = (event: ScheduleEventDto, toastMeBro = true) => {
    upsertScheduleEventMutation.mutate({
      id: event.id,
      storeId: storeId,
      title: event.title,
      description: event.description,
      range: event.range,
      eventType: event.eventType,
      visibilityLevel: event.visibilityLevel,
      isTimeOffRestricted: event.isTimeOffRestricted ?? false,
    }, {
      onError: () => {
        alert("An error occurred trying to save your event update to the server. Please try again later.");
      }
    });

    setLocalEvents(events => {
      return produce(events, draft => {
        const existingEvent = find(draft, e => e.id === event.id);
        if (existingEvent) {
          existingEvent.title = event.title;
          existingEvent.description = event.description;
          existingEvent.range = event.range;
          existingEvent.eventType = event.eventType;
          existingEvent.visibilityLevel = event.visibilityLevel;
          existingEvent.isTimeOffRestricted = event.isTimeOffRestricted;
        } else {
          draft.push(event);
        }
      });
    })

    if (toastMeBro) {
      toast.success("Event updated", {
        closeButton: true
      });
    }
  }

  const onDeleteEvent = (event: ScheduleEventDto) => {
    deleteScheduleEventMutation.mutate({
      id: event.id
    }, {
      onError: () => {
        alert("An error occurred trying to delete the event on the server. Please try again later.");
      }
    });

    setLocalEvents(events => {
      return filter(events, e => e.id !== event.id);
    })
    toast.success("Event deleted", {
      closeButton: true
    });
  }

  const onViewEventDetails = (event: ScheduleEventDto) => {
    if (!find(localEvents, e => e.id === event.id)) {
      console.log("Adding event to local events", event);
      onUpsertEvent(event, false);
    }
    setSelectedEventId(event.id);
  }

  const [selectedEventId, setSelectedEventId] = useState<string>();
  const onPanelBack = () => {
    setSelectedEventId(undefined);
  }

  return (
    <Popover open={popover.isOpen}
             onOpenChange={popover.setOpen}>
      <PopoverTrigger asChild>
        <Button size={"xs"} variant={"secondary"}
                leftIcon={<CalendarDaysIcon size={16} className={"text-gray-600"}/>}>
          Events ({localEvents.length})
        </Button>
      </PopoverTrigger>
      <PopoverContent className={cn("p-0 w-[380px] h-full overflow-auto")}
                      style={{height: defaultExpandedHeight, maxHeight: "50vh"}}>
        <EventsPanelContent onBack={onBack} isoWeek={week} dayOfWeek={dayOfWeek} events={localEvents}
                            timezone={timezone} onPanelBack={onPanelBack} storeId={storeId}
                            selectedEventId={selectedEventId} onViewEventDetails={onViewEventDetails}
                            onUpsertEvent={onUpsertEvent} onDeleteEvent={onDeleteEvent}/>
      </PopoverContent>
    </Popover>
  );
}
