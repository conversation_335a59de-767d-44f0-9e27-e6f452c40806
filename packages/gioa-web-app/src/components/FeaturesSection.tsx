import React from "react";
import {api} from "@/src/api.ts";
import {toast} from "sonner";
import {FeatureList} from "./FeatureList.tsx";

interface FeaturesSectionProps {
  businessId: string;
}

export function FeaturesSection({businessId}: FeaturesSectionProps) {
  const [features] = api.feature.getBusinessFeatures.useSuspenseQuery({businessId});
  const toggleFeature = api.feature.toggleBusinessFeature.useMutation();
  const apiUtil = api.useUtils();

  const handleToggle = (featureId: string, isEnabled: boolean) => {
    toggleFeature.mutate({
      businessId,
      featureId,
      isEnabled
    }, {
      onSuccess: () => {
        apiUtil.feature.invalidate();
        const feature = features.find(f => f.id === featureId);
        toast(`${feature?.name} ${isEnabled ? 'enabled' : 'disabled'} for this business`, {
          position: "top-center",
          dismissible: true
        });
      }
    });
  };

  return <FeatureList features={features} onToggle={handleToggle} />;
}
