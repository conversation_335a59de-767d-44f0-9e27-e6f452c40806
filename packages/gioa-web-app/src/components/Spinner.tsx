import React from 'react';
import {cn} from "@/src/util.ts";
import {Loader2} from "lucide-react";
import { cva, type VariantProps } from "class-variance-authority"


import {buttonVariants} from "@/src/components/ui/button.tsx";

export interface SpinnerProps extends VariantProps<typeof spinnerVariants> {
  className?: string;
}

const spinnerVariants = cva("mr-2 h-4 w-4 animate-spin", {
  variants: {
    size: {
      sm: "h-2 w-2",
      default: "h-4 w-4",
      lg: "h-6 w-6",
      xl: "h-8 w-8",
    },
  },
  defaultVariants: {
    size: "default",
  },
});

export const Spinner: React.FC<SpinnerProps> = ({className, size}) => {
    return (
      <Loader2 className={cn(spinnerVariants({ size, className }))} />
    );
}
