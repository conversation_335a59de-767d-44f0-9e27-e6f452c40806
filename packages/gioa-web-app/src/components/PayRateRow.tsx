import React from 'react';
import {TableCell, TableRow} from "@/src/components/ui/table.tsx";
import {Select, SelectContent, SelectItem, SelectTrigger, SelectValue} from "@/src/components/ui/select.tsx";
import {map} from "lodash";
import {Input} from "@/src/components/ui/input.tsx";
import {Button} from "@/src/components/ui/button.tsx";
import {PersonPayRate} from "@/src/payRates/payRatesParse.ts";
import {CheckIcon, TrashIcon, XIcon} from "lucide-react";
import {SchedulePersonDto} from "../../../api/src/schedulePersonDto.ts";

export interface PayRateRowProps {
  rate: MappedPayRate;
  onUpdatePayRate: (index: number, value: string) => void;
  onUpdatePersonId: (index: number, value: string | null) => void;
  removeMappedPayRate: (index: number) => void;
  people: SchedulePersonDto[];
  personAlreadyAssignedToRate: MappedPayRate | null;
  index: number;
}

export interface MappedPayRate {
  personPayRate: PersonPayRate;
  personId: string | null;
  payRate: string | undefined;
}

export function isPayRateValid(rate: MappedPayRate) {
  return Boolean(rate.payRate !== undefined && rate.personId);
}

export const PayRateRow: React.FC<PayRateRowProps> = React.memo(({
                                                                   rate,
                                                                   index,
                                                                   onUpdatePayRate,
                                                                   people,
                                                                   personAlreadyAssignedToRate,
                                                                   onUpdatePersonId,
                                                                   removeMappedPayRate
                                                                 }) => {
  return <TableRow>
    <TableCell>
      {isPayRateValid(rate) && !personAlreadyAssignedToRate
        ? <CheckIcon className="h-4 w-4 text-green-500"/>
        : <XIcon className="h-4 w-4 text-red-500"/>}
    </TableCell>
    <TableCell className={"p-1"}>{rate.personPayRate.fullName}</TableCell>
    <TableCell className={"p-1"}>
      <div className={"flex flex-row items-center"}>
        <Select
          value={rate.personId || ""}
          onValueChange={(value) => onUpdatePersonId(index, value)}
        >
          <SelectTrigger>
            <SelectValue placeholder="Select a Team Member"/>
          </SelectTrigger>
          <SelectContent>
            {map(people, (person) => (
              <SelectItem key={person.id} value={person.id || ""}>
                {person.firstName} {person.lastName}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        {rate.personId ?
          <Button variant="ghost" size="iconSm" onClick={() => onUpdatePersonId(index, null)}>
          <XIcon className="h-4 w-4 text-muted-foreground" size={16}/>
        </Button> : null}
      </div>
      {personAlreadyAssignedToRate ?
        <div className={"text-sm text-destructive"}>
          This person is already mapped to {personAlreadyAssignedToRate.personPayRate.fullName}
        </div> : null}
    </TableCell>
    <TableCell className={"p-1"}>
      <div className="flex items-center gap-2">
      <Input
        type="number"
        step="0.01"
        min="0"
        value={rate.payRate} placeholder={"$0.00"}
        onChange={(e) => onUpdatePayRate(index, e.target.value)}
        className="w-24 text-right"
      />
        <div>
          /hr
        </div>
      </div>
    </TableCell>
    <TableCell className={"p-1"}>
      <Button
        variant="ghost"
        size="icon"
        onClick={() => removeMappedPayRate(index)}
      >
        <TrashIcon className="h-4 w-4"/>
      </Button>
    </TableCell>
  </TableRow>
});
