import {use<PERSON><PERSON>back, useMemo, useState} from 'react'
import {<PERSON><PERSON>ircle2, <PERSON>ader2, XCircle} from 'lucide-react'
import {Button} from '@/src/components/ui/button.tsx'
import {Dialog, DialogContent, DialogFooter, <PERSON><PERSON>Header, DialogTitle,} from '@/src/components/ui/dialog.tsx'
import {Checkbox} from '@/src/components/ui/checkbox.tsx'
import {Input} from '@/src/components/ui/input.tsx'
import {toast} from 'sonner'
import {api} from '@/src/api.ts'
import {getHumanReadableErrorMessage} from '@/src/components/ErrorAlert.tsx'

interface Store {
  id: string
  title: string
  businessTitle: string
}

type CopyStatus = {
  [storeId: string]: {
    status: 'pending' | 'success' | 'error'
    error?: string
    copiedCount?: number
  }
}

interface CopyToDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  stores: Store[]
  businessId: string
  storeId: string
  templates: Array<{ id: string; title: string }>
  onSuccess: () => void
}

export function CopyToDialog({
                               open,
                               onOpenChange,
                               stores,
                               businessId,
                               storeId,
                               templates,
                               onSuccess,
                             }: CopyToDialogProps) {
  const [selectedStores, setSelectedStores] = useState<Set<string>>(new Set(stores.map(s => s.id)));
  const [searchQuery, setSearchQuery] = useState('');
  const [copyStatus, setCopyStatus] = useState<CopyStatus>({})
  const [isCopying, setIsCopying] = useState(false)
  const copyTemplates = api.admin.copyChecklistTemplates.useMutation()

  const resetState = useCallback(() => {
    setSelectedStores(new Set(stores.map(s => s.id)))
    setCopyStatus({})
    setIsCopying(false)
    setSearchQuery('')
  }, [stores])

  const filteredStores = useMemo(() => {
    if (!searchQuery) return stores;
    const query = searchQuery.toLowerCase();
    return stores.filter(store =>
      store.title.toLowerCase().includes(query) ||
      store.businessTitle.toLowerCase().includes(query)
    );
  }, [stores, searchQuery]);

  const handleConfirmCopy = async () => {
    const isConfirmed = window.confirm(`Are you sure you want to copy ${templates.length} template${templates.length === 1 ? '' : 's'} to ${selectedStores.size} store${selectedStores.size > 1 ? 's' : ''}?`);
    if (!isConfirmed) {
      return
    }

    if (!templates.length) {
      toast.error('No templates to copy')
      return
    }

    if (selectedStores.size === 0) {
      toast.error('Please select at least one store to copy to')
      return
    }

    setIsCopying(true)
    let hasError = false

    for (const targetStoreId of selectedStores) {
      // Set pending status
      setCopyStatus(prev => ({
        ...prev,
        [targetStoreId]: {status: 'pending'}
      }))

      try {
        const result = await copyTemplates.mutateAsync({
          from: {
            storeId,
            templateIds: templates.map(t => t.id),
          },
          to: {
            storeId: targetStoreId,
          },
        })

        // Set success status with count
        setCopyStatus(prev => ({
          ...prev,
          [targetStoreId]: {
            status: 'success',
            copiedCount: result.copied.length
          }
        }))
      } catch (error) {
        hasError = true
        // Set error status
        setCopyStatus(prev => ({
          ...prev,
          [targetStoreId]: {
            status: 'error',
            error: getHumanReadableErrorMessage(error)
          }
        }))
      }
    }

    setIsCopying(false)

    if (!hasError) {
      toast.success('Templates copied successfully')
      onSuccess()
    }
  }

  const toggleStore = (storeId: string) => {
    const newSelected = new Set(selectedStores)
    if (newSelected.has(storeId)) {
      newSelected.delete(storeId)
    } else {
      newSelected.add(storeId)
    }
    setSelectedStores(newSelected)
  }

  const toggleAll = () => {
    if (selectedStores.size === stores.length) {
      setSelectedStores(new Set())
    } else {
      setSelectedStores(new Set(stores.map(s => s.id)))
    }
  }

  return (
    <Dialog open={open} onOpenChange={() => {
      onOpenChange(false)
      resetState()
    }}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Copy {templates.length} Template{templates.length === 1 ? '' : 's'} To Other Stores</DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          <Input
            placeholder="Search stores..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
          <div className="flex items-center space-x-2">
            <Checkbox
              id="select-all"
              checked={selectedStores.size === filteredStores.length && filteredStores.length > 0}
              onCheckedChange={toggleAll}
              disabled={isCopying}
            />
            <label htmlFor="select-all"
                   className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
              Select All
            </label>
          </div>

          <div className="space-y-2 max-h-[400px] overflow-auto">
            {filteredStores.map(store => (
              <div key={store.id} className="flex items-center space-x-2">
                <Checkbox
                  id={store.id}
                  checked={selectedStores.has(store.id)}
                  onCheckedChange={() => toggleStore(store.id)}
                />
                <label htmlFor={store.id}
                       className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 grow">
                  {store.businessTitle} - {store.title}
                </label>
                {copyStatus[store.id]?.status === 'pending' && (
                  <Loader2 className="h-4 w-4 animate-spin text-blue-500"/>
                )}
                {copyStatus[store.id]?.status === 'success' && (
                  <div className="flex items-center gap-1">
                    <CheckCircle2 className="h-4 w-4 text-green-500"/>
                    <span className="text-xs text-green-600">
                      {copyStatus[store.id]?.copiedCount} copied
                    </span>
                  </div>
                )}
                {copyStatus[store.id]?.status === 'error' && (
                  <div title={copyStatus[store.id]?.error}>
                    <XCircle className="h-4 w-4 text-red-500"/>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => {
            onOpenChange(false)
            resetState()
          }} disabled={isCopying}>
            Cancel
          </Button>
          <Button onClick={handleConfirmCopy} disabled={isCopying || selectedStores.size === 0}>
            {isCopying ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin"/>
                Copying...
              </>
            ) : (
              `Copy ${templates.length} Template${templates.length === 1 ? '' : 's'} to ${selectedStores.size} Store${selectedStores.size > 1 ? 's' : ''}`
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
