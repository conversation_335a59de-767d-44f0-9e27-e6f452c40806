import React, { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/src/components/ui/dialog";
import { Button } from "@/src/components/ui/button";
import { Input } from "@/src/components/ui/input";
import { X } from "lucide-react";
import { api } from "@/src/api";
import { toast } from "sonner";
import { map } from "lodash";
import { Text } from "@/src/components/Text";

interface ChecklistTagsManagementModalProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  storeId: string;
}

export const ChecklistTagsManagementModal: React.FC<ChecklistTagsManagementModalProps> = ({ isOpen, onOpenChange, storeId }) => {
  const [newTagValue, setNewTagValue] = useState("");

  const [{ tags }] = api.checklist2.getChecklistTags.useSuspenseQuery({storeId});
  const apiUtils = api.useUtils();

  const scrollRef = React.useRef<HTMLDivElement>(null);

  const addTagMutation = api.checklist2.addChecklistTag.useMutation({
    onSuccess: async () => {
      await apiUtils.checklist2.getChecklistTags.invalidate();
      setNewTagValue("");
      toast.success("Tag added successfully");
    },
    onError: (error) => {
      toast.error(`Error adding tag: ${error.message}`);
    },
  });

  const removeTagMutation = api.checklist2.removeChecklistTag.useMutation({
    onSuccess: async () => {
      await apiUtils.checklist2.getChecklistTags.invalidate();
      toast.success("Tag removed successfully");
    },
    onError: (error) => {
      toast.error(`Error removing tag: ${error.message}`);
    },
  });

  const handleAddTag = async () => {
    if (!newTagValue.trim()) {
      toast.error("Please enter a tag name");
      return;
    }

    if (tags.includes(newTagValue.trim())) {
      toast.error("Tag already exists");
      return;
    }

    await addTagMutation.mutateAsync({ storeId, tag: newTagValue.trim() });

    setTimeout(() =>
    scrollRef.current?.scrollTo({
      top: scrollRef.current.scrollHeight,
      behavior: "smooth",
    }), 300);
  };

  const handleRemoveTag = (tag: string) => {
    removeTagMutation.mutate({ storeId, tag });
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      e.preventDefault();
      handleAddTag();
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent size={"lg"} className="flex flex-col h-[600px] max-h-[80vh]">
        <DialogHeader className="flex-shrink-0">
          <DialogTitle>Manage Checklist Tags</DialogTitle>
        </DialogHeader>

        <div ref={scrollRef} className="flex-1 overflow-y-auto space-y-1 min-h-0 pr-2">
          {tags.length === 0 ? (
            <div className="text-center text-muted-foreground py-8">No tags found. Add your first tag below.</div>
          ) : (
            map(tags, (tag) => (
              <div
                key={tag}
                className="flex items-center justify-between px-3 border border-gray-200 rounded-md bg-gray-50"
              >
                <Text size={"sm"}>{tag}</Text>
                <Button
                  variant="ghost"
                  size="iconSm"
                  onClick={() => handleRemoveTag(tag)}
                  disabled={removeTagMutation.isPending}
                  className="text-gray-500 hover:text-red-600"
                >
                  <X size={16} />
                </Button>
              </div>
            ))
          )}
        </div>

        <div className="border-t pt-4 space-y-3 flex-shrink-0">
          <div className="flex gap-2">
            <Input
              placeholder="Enter new tag name"
              value={newTagValue}
              onChange={(e) => setNewTagValue(e.target.value)}
              onKeyDown={handleKeyDown}
              disabled={addTagMutation.isPending}
              className="flex-1"
            />
            <Button
              onClick={handleAddTag}
              disabled={addTagMutation.isPending || !newTagValue.trim()}
              isLoading={addTagMutation.isPending}
            >
              Add Tag
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
