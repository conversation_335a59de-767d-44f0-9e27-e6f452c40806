import React from 'react';
import {<PERSON><PERSON>} from "@/src/components/ui/button.tsx"
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger
} from "@/src/components/ui/dialog.tsx"
import {useForm} from "@tanstack/react-form";
import {FormControl} from "@/src/components/form/FormControl.tsx"
import {FormInput} from "@/src/components/form/FormInput.tsx"
import {FormTextarea} from "@/src/components/form/FormTextarea.tsx"
import {Label} from "@/src/components/ui/label.tsx"
import {FieldInfo} from "@/src/components/form/FieldInfo.tsx"
import {zodValidator} from "@tanstack/zod-form-adapter";
import {z} from "zod"
import {PlusIcon} from "lucide-react"
import {useDisclosure} from "@/src/hooks/useDisclosure.tsx"
import {api} from "@/src/api.ts";
import {getHumanReadableErrorMessageString} from "@/src/components/ErrorAlert.tsx";
import {Route} from "@/src/routes/_signedin/$businessId/$storeId/_nav/checklists/_nav.tsx";
import { map } from "lodash";
import { Badge } from "@/src/components/ui/badge.tsx";

export interface CreateChecklistForm {
  title: string;
  description: string;
}

interface CreateChecklistDialogFormProps {
  storeId: string;
}

export const CreateChecklistDialogForm: React.FC<CreateChecklistDialogFormProps> = ({
                                                                                      storeId
                                                                                    }) => {
  const createChecklist = api.checklist2.createChecklist.useMutation();
  const navigate = Route.useNavigate();

  const [{ tags }] = api.checklist2.getChecklistTags.useSuspenseQuery({storeId});

  const dialog = useDisclosure();
  const form = useForm({
    defaultValues: {
      title: "",
      description: "",
      tags: [] as string[],
    },
    validatorAdapter: zodValidator(),
    onSubmit: async ({value}) => {
      createChecklist.mutate({
        storeId: storeId,
        title: value.title,
        description: value.description,
        tags: value.tags,
      }, {
        onSuccess: (data) => {
          navigate({
            to: `templates/$templateId`,
            params: {
              templateId: data.id
            }
          })
          dialog.onClose();
          form.reset();
        },
        onError: (error) => {
          alert("Error creating checklist: " + getHumanReadableErrorMessageString(error));
        }
      });
    },
  });

  return (
    <Dialog open={dialog.isOpen} onOpenChange={dialog.setOpen}>
      <DialogTrigger asChild>
        <Button leftIcon={<PlusIcon size={16}/>}>
          Create New
        </Button>
      </DialogTrigger>

      <DialogContent>
        <div className="space-y-4">
          <DialogHeader>
            <DialogTitle>Create Checklist</DialogTitle>

            <DialogDescription>
              Give your new checklist a title and a description
            </DialogDescription>
          </DialogHeader>

          <form onSubmit={(e) => {
            e.preventDefault()
            e.stopPropagation()
            form.handleSubmit()
          }}>
            <div className="space-y-4">
              <form.Field
                name="title"
                validators={{
                  onSubmit: z.string().min(1, "Title is required")
                }}
              >
                {(field) => (
                  <FormControl>
                    <Label htmlFor={field.name}>Title</Label>
                    <FormInput
                      field={field}
                      placeholder="Enter checklist title"
                    />
                    <FieldInfo field={field}/>
                  </FormControl>
                )}
              </form.Field>

              <form.Field
                name="description"
              >
                {(field) => (
                  <FormControl>
                    <Label htmlFor={field.name}>Description</Label>
                    <FormTextarea
                      field={field}
                      placeholder="Enter checklist description"
                    />
                    <FieldInfo field={field}/>
                  </FormControl>
                )}
              </form.Field>

              <form.Field
                name="tags"
              >
                {(field) => (
                  <FormControl>
                    <Label htmlFor={field.name}>Tags</Label>
                    <div className={"flex flex-wrap gap-1"}>
                      {map(tags, tag => {
                        const isSelected = field.state.value.includes(tag);
                        return <Badge key={tag} colorScheme={isSelected ? "default" : "outline"} size={"sm"}
                                      className={"cursor-pointer"}
                                       onClick={() => {
                                         field.handleChange(prev => {
                                           if (prev.includes(tag)) {
                                             return prev.filter(t => t !== tag);
                                           } else {
                                             return [...prev, tag];
                                           }
                                         });
                                       }}>
                          {tag}
                        </Badge>
                      })}
                    </div>
                    <FieldInfo field={field}/>
                  </FormControl>
                )}
              </form.Field>
            </div>

            <DialogFooter className="pt-4">
              <Button disabled={createChecklist.isPending}
                      type="button"
                      variant="outline"
                      onClick={() => dialog.onClose()}>
                Cancel
              </Button>

              <Button isLoading={createChecklist.isPending}
                      type="submit">
                Create
              </Button>
            </DialogFooter>
          </form>
        </div>
      </DialogContent>
    </Dialog>
  );
}
