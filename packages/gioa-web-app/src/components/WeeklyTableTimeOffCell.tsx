import React from 'react';
import {map, sortBy} from "lodash";
import {
  expandMultidayEvents,
  renderEventSubtitle,
  renderEventTitleSuffix
} from "../../../api/src/expandMultidayEvents.ts";
import {TableCell} from "@/src/components/ui/table.tsx";
import {getIsoWeekDayDtRangeInTimezone} from "../../../api/src/date.util.ts";
import {SchedulePersonTimeOffDto} from "../../../api/src/schedulePersonDto.ts";
import {cn} from "@/src/util.ts";

export interface WeeklyTableTimeOffCellProps {
  year: number;
  week: number;
  weekday: number;
  timezone: string;
  timeOff: SchedulePersonTimeOffDto[];
  pendingTimeOff: SchedulePersonTimeOffDto[];
  onTimeOffClick: (timeOff: SchedulePersonTimeOffDto) => void;
}

export const WeeklyTableTimeOffCell = React.memo(({
                                                    year,
                                                    week,
                                                    weekday,
                                                    timezone, onTimeOffClick,
                                                    timeOff, pendingTimeOff
                                                  }: WeeklyTableTimeOffCellProps) => {
  const isoWeekDate = {
    year,
    week,
    day: weekday
  }
  const isoWeekDayRange = getIsoWeekDayDtRangeInTimezone(isoWeekDate, timezone);
  const multidayTimeOff = expandMultidayEvents({
    events: map(timeOff, t => ({range: t})),
    range: isoWeekDayRange,
    timezone
  })

  const timeOffMiddles = sortBy(multidayTimeOff.middles, e => e.item.range.start);
  const timeOffEnds = multidayTimeOff.ends;
  const timeOffStarts = multidayTimeOff.startsAndEngulfed;

  const multidayPendingTimeOff = expandMultidayEvents({
    events: map(pendingTimeOff, t => ({range: t})),
    range: isoWeekDayRange,
    timezone
  })
  const pendingTimeOffMiddles = sortBy(multidayPendingTimeOff.middles, e => e.item.range.start);
  const pendingTimeOffEnds = multidayPendingTimeOff.ends;
  const pendingTimeOffStarts = multidayPendingTimeOff.startsAndEngulfed;

  return (
    <TableCell className={"h-full px-2 even:bg-slate-100 odd:bg-slate-200 border-t border-slate-300"} style={{width: `${100/7}%`}}>
      <div className={"h-full"}>
        {map([...timeOffMiddles, ...timeOffEnds, ...timeOffStarts, ...pendingTimeOffMiddles, ...pendingTimeOffEnds, ...pendingTimeOffStarts], (evt, idx) => {
          const isPending = evt.item.range.status === "pending";

          return <div key={idx} role={"button"} tabIndex={0}
                      onClick={() => onTimeOffClick(evt.item.range)}
                      className={cn("flex flex-row items-start gap-2 flex-wrap bg-white border border-gray-200 rounded-lg border-l-8 pl-3 pr-2 py-2 text-left w-full hover:bg-slate-50", {
                        "border-l-green-500": !isPending,
                        "border-l-yellow-500": isPending,
                      })}>
            <div>
              {isPending ? "Pending" : "Approved"} time off {renderEventTitleSuffix(evt)}
            </div>

            {renderEventSubtitle(evt)}
          </div>
        })}
      </div>
    </TableCell>
  );
})
