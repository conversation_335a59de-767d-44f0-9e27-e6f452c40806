import React from "react";
import {Text} from "./Text";
import {format} from "date-fns";
import {PersonNoteHeader} from "./PersonNoteHeader";
import {CorrectiveActionStatusBadge} from "./CorrectiveActionStatusBadge";
import {PersonGeneralNoteDto} from "../../../api/src/schemas.ts";

export interface PersonGeneralNoteProps {
  note: PersonGeneralNoteDto;
}

function getGeneralNoteStatus(note: PersonGeneralNoteDto): string | undefined {
  if (note.isArchived) {
    return "archived";
  }
  if (note.requiresAcknowledgement) {
    if (note.isAcknowledged) {
      return "acknowledged";
    } else {
      return "unacknowledged";
    }
  }
  return undefined;
}

export const PersonGeneralNote = React.memo((props: PersonGeneralNoteProps) => {
  const { note } = props;
  const noteStatus = getGeneralNoteStatus(note);

  return (
    <div className="px-4 py-3 mb-2 bg-white relative shadow-sm border border-gray-200 rounded-xl">
      <PersonNoteHeader note={note} />
      <div className="mt-2">
        <div className="flex flex-row items-center gap-1 mb-2">
          <Text muted className="flex-1 text-sm">
            {format(new Date(note.createdAt), "PP")} by {note.createdBy ? `${note.createdBy.firstName} ${note.createdBy.lastName}` : "System"}
          </Text>
        </div>
        <Text className={noteStatus ? "mb-2" : undefined}>{note.note}</Text>
        {noteStatus && (
          <div className="flex flex-row">
            <CorrectiveActionStatusBadge status={noteStatus} />
          </div>
        )}
      </div>
    </div>
  );
});
