import React from 'react';
import {Card, CardContent} from "@/src/components/ui/card.tsx";
import {Text} from "@/src/components/Text.tsx";
import {Heading} from "@/src/components/Heading.tsx";
import {formatCurrency, formatPercent} from "@gioa/common/src/dataFormatters";
import {CheckIcon} from "lucide-react";
import {cn} from "@/src/util";
import {percent} from "../../../api/src/scheduling/metrics/percent.ts";

import {PriceCommitmentType} from "../../../api/src/payments/priceSchemas.ts";
import {Dollars} from "../../../api/src/scheduling/metrics/dollars.ts";

interface PlanChooserProps {
  selectedPlan: PriceCommitmentType | undefined;
  onPlanChange: (plan: PriceCommitmentType) => void;
  pricesData?: {
    monthly: Dollars | null;
    yearly: Dollars | null;
  };
  className?: string;
}

export function PlanChooser({
  selectedPlan,
  onPlanChange,
  pricesData,
  className = ""
}: PlanChooserProps) {
  const annualSavingsPercentage = pricesData
    ? percent(((pricesData.monthly ?? 0) * 12 - (pricesData.yearly ?? 0) * 12) / ((pricesData.monthly ?? 0) * 12) * 100)
    : percent(0);

  return (
    <div className={cn("space-y-4", className)}>
      <Heading className={"text-center"} level={2} size={"sm"}>Choose Your Plan</Heading>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 px-6">
        {/* Monthly Plan */}
        <Card
          className={cn(
            "cursor-pointer transition-all duration-200 hover:shadow-md",
            selectedPlan === 'monthly'
              ? "ring-2 ring-blue-500 bg-blue-50"
              : "hover:bg-gray-50"
          )}
          onClick={() => onPlanChange('monthly')}
        >
          <CardContent className="p-6">
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <div className="flex items-center gap-2 mb-2">
                  <h3 className="text-lg font-semibold">Monthly</h3>
                  {selectedPlan === 'monthly' && (
                    <CheckIcon className="w-5 h-5 text-blue-500"/>
                  )}
                </div>
                <p className="text-2xl font-bold text-gray-900 mb-1">
                  {pricesData ? formatCurrency(pricesData.monthly ?? undefined) : '$--'}
                  <span className="text-sm font-normal text-gray-600">/month*</span>
                </p>
                <p className="text-sm text-gray-600">
                  <ol className={"list-disc list-outside ml-4"}>
                    <li>
                      All Nation features
                    </li>
                    <li>
                      No annual commitment
                    </li>
                  </ol>
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Annual Plan */}
        <Card
          className={cn(
            "cursor-pointer transition-all duration-200 hover:shadow-md relative",
            selectedPlan === 'annual'
              ? "ring-2 ring-blue-500 bg-blue-50"
              : "hover:bg-gray-50"
          )}
          onClick={() => onPlanChange('annual')}
        >
          <CardContent className="p-6">
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <div className="flex items-center gap-2 mb-2">
                  <h3 className="text-lg font-semibold">Annual</h3>
                  {selectedPlan === 'annual' && (
                    <CheckIcon className="w-5 h-5 text-blue-500"/>
                  )}
                </div>
                <p className="text-2xl font-bold text-gray-900 mb-1">
                  {pricesData ? formatCurrency(pricesData.yearly ?? undefined) : '$--'}
                  <span className="text-sm font-normal text-gray-600">/month*</span>
                </p>
                <p className="text-sm text-gray-600">
                  <ol className={"list-disc list-outside ml-4"}>
                    <li>
                      All Nation features
                    </li>
                    <li>
                      Billed monthly with a 12-month commitment
                    </li>
                    <li>
                      Save {formatPercent(annualSavingsPercentage, 1)} versus monthly
                    </li>
                  </ol>
                </p>
              </div>
            </div>
            {/* Popular badge */}
            <div
              className="absolute -top-2 -right-2 bg-gradient-to-r from-blue-500 to-purple-600 text-white text-xs font-medium px-3 py-1 rounded-full shadow-lg">
              Popular
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Tax indicator */}
      <div className="text-center">
        <Text className="text-xs text-gray-500">
          * Prices may include applicable taxes and fees
        </Text>
      </div>
    </div>
  );
}
