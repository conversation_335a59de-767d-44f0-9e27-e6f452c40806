import React from 'react';
import {ChevronRight} from "lucide-react";
import {cn} from "@/src/util.ts";
import {Link as RouterLink} from "@tanstack/react-router";

export interface PressableCardProps {
  className?: string;
  color: string;
  children: React.ReactNode;
  onClick?: () => void;
}

export const PressableCard: React.FC<PressableCardProps> = (props) => {
  return (
    <button onClick={props.onClick}
            className={cn("flex flex-row items-center border border-gray-200 rounded-lg border-l-8 pl-3 pr-2 py-2 text-left w-full", props.className)}
            style={{borderLeftColor: props.color}}>
      <div className={"flex-1"}>
        {props.children}
      </div>
      <ChevronRight className={"text-gray-500"}/>
    </button>
  );
}

export const LinkCard: React.FC<PressableCardProps & {
  from?: string;
  to: string;
  search: any;
}> = (props) => {
  return (
    <RouterLink from={props.from  as any} to={props.to  as any} search={props.search}
                className={cn("flex flex-row items-center border border-gray-200 rounded-lg border-l-8 pl-3 pr-2 py-2 text-left w-full", props.className)}
                style={{borderLeftColor: props.color}}>
      <div className={"flex-1"}>
        {props.children}
      </div>
      <ChevronRight className={"text-gray-500"}/>
    </RouterLink>
  );
}
