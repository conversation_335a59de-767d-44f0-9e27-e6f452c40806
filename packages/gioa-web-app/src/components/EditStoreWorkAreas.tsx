import React, {useCallback, useEffect, useState} from "react";
import { Heading } from "@/src/components/Heading";
import { api } from "@/src/api";
import { filter, map } from "lodash";
import { Button } from "@/src/components/ui/button";
import { Text } from "@/src/components/Text";
import { Card } from "@/src/components/ui/card";
import { Edit, GripVertical, ListChecks, PlusCircle } from "lucide-react";
import { toast } from "sonner";
import { genStoreAreaId } from "@gioa/api/src/schemas";
import { useNavigate } from "@tanstack/react-router";
import { closestCenter, DndContext, KeyboardSensor, PointerSensor, useSensor, useSensors } from "@dnd-kit/core";
import {
  SortableContext,
  sortableKeyboardCoordinates,
  useSortable,
  verticalListSortingStrategy,
} from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import { restrictToVerticalAxis } from "@dnd-kit/modifiers";

export interface AreaItem {
  id: string;
  title: string;
  isReadonly?: boolean;
}

interface SortableAreaItemProps {
  area: AreaItem;
}

const SortableAreaItem = ({ area }: SortableAreaItemProps) => {
  const { attributes, listeners, setNodeRef, transform, transition } = useSortable({ id: area.id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  };

  return (
          <div ref={setNodeRef} style={style} {...attributes} {...listeners} className="mb-3">
            <Card className="flex items-center hover:bg-gray-50">
              <div className="p-3 cursor-grab active:cursor-grabbing">
                <GripVertical size={20} className="text-gray-400" />
              </div>
              <div className="flex-1 py-3">
                <Text>{area.title}</Text>
              </div>
            </Card>
          </div>
  );
};

export interface EditStoreWorkAreasProps {
  businessId: string;
  storeId: string;
}

export const EditStoreWorkAreas: React.FC<EditStoreWorkAreasProps> = ({ businessId, storeId }) => {
  const [store] = api.user.getStoreAdmin.useSuspenseQuery(
          { storeId },
          {
            staleTime: 1000 * 60 * 60, // 1 hour
          },
  );

  const navigate = useNavigate();
  const apiUtil = api.useUtils();

  const activeAreas = filter(store.areas, (area) => area.isActive);
  const sortedAreas = [...activeAreas].sort((a, b) => a.order - b.order);

  const areaItems: AreaItem[] = map(sortedAreas, (area) => ({
    id: area.id,
    title: area.title,
    isReadonly: area.isReadonly,
  }));

  const [areas, setAreas] = useState<AreaItem[]>(areaItems);
  const [reorderedAreas, setReorderedAreas] = useState<AreaItem[]>([]);
  const [isReordering, setIsReordering] = useState(false);

  useEffect(() => {
    setAreas(areaItems);
  }, [store.areas]);

  const setAreaOrder = api.user.setStoreAreasOrder.useMutation({
    onSuccess: () => {
      apiUtil.user.getStoreAdmin.invalidate({ storeId });
      toast.success("Work areas reordered successfully");
    },
    onError: (error) => {
      toast.error(`Failed to reorder work areas: ${error.message}`);
    },
  });

  const sensors = useSensors(
          useSensor(PointerSensor),
          useSensor(KeyboardSensor, {
            coordinateGetter: sortableKeyboardCoordinates,
          }),
  );

  const handleDragEnd = (event: any) => {
    const { active, over } = event;

    if (active.id !== over.id) {
      const oldIndex = reorderedAreas.findIndex((item) => item.id === active.id);
      const newIndex = reorderedAreas.findIndex((item) => item.id === over.id);

      const newItems = [...reorderedAreas];
      const [removed] = newItems.splice(oldIndex, 1);
      newItems.splice(newIndex, 0, removed);

      setReorderedAreas(newItems);
    }
  };

  const saveReordering = () => {
    const areaIdsInOrder = map(reorderedAreas, (area) => area.id);
    setAreaOrder.mutate({
      storeId,
      areaIdsInOrder,
    }, {
      onSuccess: () => {
        setAreas(reorderedAreas);
        setIsReordering(false);
      }
    });
  };

  const cancelReordering = () => {
    setReorderedAreas([]);
    setIsReordering(false);
  };

  const handleEditArea = useCallback(
          (area: AreaItem) => {
            navigate({
              to: `/$businessId/$storeId/store/settings/area/$areaId/edit`,
              params: {
                businessId,
                storeId,
                areaId: area.id },
            });
          },
          [navigate],
  );

  const handleManagePositions = useCallback(
          (area: AreaItem) => {
            navigate({
              to: `/$businessId/$storeId/store/settings/area/$areaId/positions`,
              params: {
                businessId,
                storeId,
                areaId: area.id
              },
            });
          },
          [navigate],
  );

  const handleAddArea = useCallback(() => {
    const newAreaId = genStoreAreaId();
    navigate({
      to: `/$businessId/$storeId/store/settings/area/$areaId/edit`,
      params: {
        businessId,
        storeId,
        areaId: newAreaId
      },
    });
  }, [navigate]);

  const startReordering = () => {
    setReorderedAreas([...areas]);
    setIsReordering(true);
  };

  return (
          <div className="pl-8 sm:max-w-lg bg-white border-l border-gray-200 flex flex-col gap-3">
            <Heading level={3} size="xs">
              Work Areas
            </Heading>
            {isReordering ? (
                    <div className="flex gap-2">
                      <Button variant="outline" onClick={cancelReordering}>Cancel</Button>
                      <Button onClick={saveReordering} disabled={setAreaOrder.isPending}>Save Order</Button>
                    </div>
            ) : (
                    <div className="flex gap-2">
                      <Button variant="outline" onClick={startReordering} leftIcon={<GripVertical size={16} />}>
                        Reorder
                      </Button>
                      <Button variant="outline" onClick={handleAddArea} rightIcon={<PlusCircle size={16} />}>
                        Add Area
                      </Button>
                    </div>
            )}

            {isReordering ? (
                    <div>
                      <p className="mb-4">
                        Drag and drop to reorder items. Click Save when you're done.
                      </p>
                      <DndContext
                              sensors={sensors}
                              collisionDetection={closestCenter}
                              onDragEnd={handleDragEnd}
                              modifiers={[restrictToVerticalAxis]}
                      >
                        <SortableContext items={reorderedAreas.map((area) => area.id)} strategy={verticalListSortingStrategy}>
                          {reorderedAreas.length > 0 ? (
                                  reorderedAreas.map((area) => <SortableAreaItem key={area.id} area={area} />)
                          ) : (
                                  <Text className="text-center text-gray-500 py-8">
                                    No work areas found. Click "Add Work Area" to create one.
                                  </Text>
                          )}
                        </SortableContext>
                      </DndContext>
                    </div>
            ) : (
                    <div>
                      {areas.length > 0 ? (
                              areas.map((area) => (
                                      <Card key={area.id} className="px-4 py-3 flex justify-between items-center hover:bg-gray-50 mb-2">
                                        <div className="flex-1 cursor-pointer" onClick={() => handleEditArea(area)}>
                                          <Text>{area.title}</Text>
                                        </div>
                                        <div className="flex gap-2">
                                          <Button
                                                  variant="ghost"
                                                  size="icon"
                                                  onClick={() => handleManagePositions(area)}
                                                  title="Manage Positions"
                                          >
                                            <ListChecks size={18} className="text-gray-500" />
                                          </Button>
                                          <Button variant="ghost" size="icon" onClick={() => handleEditArea(area)} title="Edit Area">
                                            <Edit size={18} className="text-gray-500" />
                                          </Button>
                                        </div>
                                      </Card>
                              ))
                      ) : (
                              <Text className="text-center text-gray-500 py-8">
                                No work areas found. Click "Add Work Area" to create one.
                              </Text>
                      )}
                    </div>
            )}
          </div>
  );
};
