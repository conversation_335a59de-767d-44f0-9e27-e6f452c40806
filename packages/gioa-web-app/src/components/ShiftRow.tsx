import React from 'react';
import {ScheduleShift} from "@/src/components/ScheduleShift.tsx";
import {cn} from "@/src/util.ts";
import {DailyTimeRange, DayOfWeek} from "../../../api/src/timeSchemas.ts";
import {ScheduleRowInfo} from "../../../api/src/scheduleBuilder.util.ts";
import {ShiftRowAvailabilityOverlay} from "@/src/components/ShiftRowAvailabilityOverlay.tsx";

export interface ScheduleRowProps {
  rowHeight: number;
  rowIndex: number;
  shift: ScheduleRowInfo;
  incrementWidth: number;
  onShiftEndChange: (areaId: string, shiftId: string, duration: number) => void;
  onShiftStartChange: (areaId: string, shiftId: string, start: number) => void;
  onShiftMoved: (areaId: string, shiftId: string, start: number, end: number) => void;
  onShiftOpened: (areaId: string, shiftId: string) => void;
  isSelected: boolean;
  storeHours: DailyTimeRange;
  dayOfWeek: DayOfWeek;
  showAvail: boolean;
  selectedActivityId: string | undefined;
  isActivitiesView: boolean;
}

export const ShiftRow = React.memo(({
                                      rowIndex, storeHours, isActivitiesView,
                                      onShiftStartChange, onShiftEndChange, dayOfWeek, showAvail, selectedActivityId,
                                      rowHeight, shift, incrementWidth, onShiftMoved, onShiftOpened, isSelected,
                                    }: ScheduleRowProps) => {


  return (
    <div style={{height: rowHeight}} data-shift-id={shift.id}
         className={cn("relative w-full px-4 -ml-4", isSelected ? "bg-[#0034FF] bg-opacity-5" : undefined)}>
      {/*<div className={"sticky bg-red-400 left-0 inline-block z-20"} style={{width: 120}}>Test</div>*/}
      <ScheduleShift rowHeight={rowHeight} rowIndex={rowIndex} key={shift.id} storeHours={storeHours}
                     onShiftOpened={onShiftOpened} isSelected={isSelected} selectedActivityId={selectedActivityId}
                     onShiftEndChange={onShiftEndChange} onShiftStartChange={onShiftStartChange}
                     shift={shift} onShiftMoved={onShiftMoved} isActivitiesView={isActivitiesView}
                     incrementWidth={incrementWidth}/>

      {showAvail && shift.assignedTo
        ? <ShiftRowAvailabilityOverlay dayOfWeek={dayOfWeek}
                                       incrementWidth={incrementWidth}
                                       person={shift.assignedTo}
                                       showAvail={showAvail}
                                       storeHours={storeHours}/>
        : null}
    </div>
  );
});
