import React from 'react';
import {cn} from "@/src/util.ts";

export interface CardProps {
  className?: string;
  color: string;
  children: React.ReactNode;
}

export const Card: React.FC<CardProps> = (props) => {
  return (
    <div
      className={cn("flex flex-row items-center border border-gray-200 rounded-lg border-l-8 pl-3 pr-2 py-2 text-left w-full", props.className)}
      style={{borderLeftColor: props.color}}>
      {props.children}
    </div>
  );
}
