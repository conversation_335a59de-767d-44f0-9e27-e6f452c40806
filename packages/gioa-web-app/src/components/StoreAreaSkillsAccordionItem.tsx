import React from 'react';
import {filter, find, map} from 'lodash';
import {AccordionContent, AccordionItem, AccordionTrigger} from "@/src/components/ui/accordion.tsx";
import {StorePositionSkill} from "@/src/components/StorePositionSkill.tsx";
import type {StoreAreaDto, StorePositionDto} from '@gioa/api/src/schemas';
import {api} from "@/src/api.ts";
import {SchedulePersonDto} from "../../../api/src/schedulePersonDto.ts";
import {bohAreaId, diningRoomAreaId, fohAreaId, otherAreaId} from "../../../api/src/stockAreaTemplates.ts";
import {CookingPot, Store, User, Utensils} from "lucide-react";
import {cn} from "@/src/util.ts";
import {Text} from "@/src/components/Text.tsx";

interface StoreAreaSkillsProps {
  area: StoreAreaDto;
  person: SchedulePersonDto;
  storeId: string;
  onPositionTrainingEdit: (params: {
    positionTitle: string;
    positionId: string;
    isTrained: boolean;
  }) => void;
  onPositionClick?: (position: StorePositionDto) => void;
  canViewTraining: boolean;
  canEditTraining: boolean;
  canViewPositionScores: boolean;
}

// Map of createdFromTemplateId to style
const fallbackAreaStyle = {
  backgroundColor: "#dbe3e5",
  icon: <Store size={20} color={"#b5b3b9"}/>
};
const driveThroughAreaStyle = {
  backgroundColor: "#F3E8FF",
  icon: <Store size={20} color={"#b5b3b9"}/>
};
const storeAreaStyle: Record<string, { backgroundColor: string; icon: React.ReactNode }> = {
  [fohAreaId]: {backgroundColor: "#FFF4DE", icon: <User size={20} color={"#ffb216"}/>},
  [bohAreaId]: {backgroundColor: "#DCFCE7", icon: <CookingPot size={20} color={"#4fd37c"}/>},
  [diningRoomAreaId]: {
    backgroundColor: "#E0EFFF",
    // icon: <MaterialCommunityIcons name={"silverware"} size={24} color={"#007AFF"}/>
    icon: <Utensils size={20} color={"#87b9ef"}/>
  },
  [otherAreaId]: fallbackAreaStyle,
}

export const StoreAreaSkillsAccordionItem = React.memo<StoreAreaSkillsProps>(({
                                                                                area,
                                                                                person,
                                                                                storeId,
                                                                                onPositionTrainingEdit,
                                                                                onPositionClick,
                                                                                canViewTraining,
                                                                                canEditTraining,
                                                                                canViewPositionScores,
                                                                              }) => {

  const numPositions = area.positions.length;
  const numTrainedPositions = filter(area.positions, position => {
    return find(person.training, t => t.positionId === position.id)?.isCompleted;
  }).length;

  const getPersonPositionStatistics = api.user.getPersonPositionStatistics.useQuery({
    storeId: storeId,
    personId: person.id,
  })

  let areaStyle = area.createdFromTemplateId ? storeAreaStyle[area.createdFromTemplateId] ?? fallbackAreaStyle : fallbackAreaStyle;
  if (area.title.match(/drive/i)) {
    areaStyle = driveThroughAreaStyle;
  }

  return (
          <AccordionItem  value={area.id} className={"mt-2"}>
            <AccordionTrigger className={cn("border border-gray-200 rounded-lg px-4 mb-2")} style={{backgroundColor: areaStyle.backgroundColor}}>
              {/* display the icon from areaStyle.icon*/}
              <div className={cn("flex items-center justify-center mr-4 shrink-0 bg-white rounded-lg p-2")}>
                {areaStyle.icon}
              </div>
              <div className={cn("flex-1 flex flex-row gap-1 ")}>
                <div className={cn("flex flex-col gap-1 items-start")}>
                  <Text>{area.title}</Text>
                  <Text size={"sm"} className={"text-gray-500"}>
                    {numTrainedPositions} / {numPositions} Positions
                  </Text>
                </div>
              </div>
            </AccordionTrigger>
            <AccordionContent className={"pb-0 mt-2"}>
              {map(area.positions, position => {
                const statistic = find(getPersonPositionStatistics.data, s => s.positionId === position.id);
                const training = find(person.training, t => t.positionId === position.id);

                return (
                        <StorePositionSkill
                                key={position.id}
                                position={position}
                                training={training}
                                statistic={statistic}
                                canViewTraining={canViewTraining}
                                canEditTraining={canEditTraining}
                                canViewPositionScores={canViewPositionScores}
                                onPositionTrainingEdit={onPositionTrainingEdit}
                                onPositionClick={onPositionClick}
                        />
                );
              })}
            </AccordionContent>
          </AccordionItem>
  );
});
