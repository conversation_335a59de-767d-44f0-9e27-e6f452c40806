import React, { useState } from 'react';
import { map } from 'lodash';
import { imageUrl } from '@/src/images';
import { Image } from '@/src/components/Image';
import { ImageDto } from '../../../api/src/schemas';
import { Spinner } from '@/src/components/Spinner';

export interface ImageThumbRowProps {
  images: ImageDto[];
}

export const ImageThumbRow: React.FC<ImageThumbRowProps> = ({ images }) => {
  const [loadingStates, setLoadingStates] = useState<Record<string, boolean>>({});

  // Initialize loading states for all images
  React.useEffect(() => {
    const initialLoadingStates: Record<string, boolean> = {};
    images.forEach(image => {
      initialLoadingStates[image.url] = loadingStates[image.url] ?? true;
    });
    setLoadingStates(initialLoadingStates);
  }, [images]);

  const onImageClick = (url: string) => {
    window.open(imageUrl(url, { width: 1280 }), '_blank');
  };

  const handleImageLoad = (url: string) => {
    setLoadingStates((prev) => ({ ...prev, [url]: false }));
  };

  return (
    <>
      {map(images, (image) => {
        const isLoading = loadingStates[image.url];

        return (
          <div
            key={image.id}
            className="cursor-pointer relative"
            onClick={() => onImageClick(image.url)}
          >
            {isLoading && (
              <div className="absolute inset-0 flex items-center justify-center z-10 bg-gray-50 bg-opacity-50 rounded-md">
                <Spinner size="sm" />
              </div>
            )}
            <Image
              src={imageUrl(image.url, { width: 100, height: 100 }) || ''}
              alt="Attachment"
              width={100}
              height={100}
              className="object-contain rounded-md border border-gray-200"
              onLoad={() => handleImageLoad(image.url)}
            />
          </div>
        );
      })}
    </>
  );
};
