import React from 'react';
import {<PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>alogTitle, DialogTrigger} from "@/src/components/ui/dialog.tsx";
import {Button} from "@/src/components/ui/button.tsx";
import {EditAreaDialogForm} from "@/src/components/EditAreaDialogForm.tsx";
import {DialogDescription} from "@radix-ui/react-dialog";
import {find} from "lodash";
import {daysOfWeek} from "@/src/components/AvailabilityWeek.tsx";
import {StoreAreaDto} from "../../../api/src/schemas.ts";
import {ScheduleArea} from "../../../api/src/scheduleBuilder.util.ts";

export interface ScheduleAddAreaProps {
  onAddArea: (area: ScheduleArea) => void;
  dayOfWeek: number;
  isOpen: boolean;
  onClose: () => void;
  onOpen: () => void;
  storeAreas: StoreAreaDto[];
}

export const ScheduleAddArea: React.FC<ScheduleAddAreaProps> = (props) => {
  const dayObj = find(daysOfWeek, d => d.dayOfWeek === props.dayOfWeek)!;
  const onSubmit = (area: ScheduleArea) => {
    props.onAddArea(area);
  }

  const onCancel = () => {
    props.onClose();
  }

  return (
    <Dialog open={props.isOpen} onOpenChange={open => {
      if (open) {
        props.onOpen();
      } else {
        onCancel();
      }
    }}>
      <DialogTrigger asChild>
        <Button variant={"outline"}>
          Add area
        </Button>
      </DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Add Area</DialogTitle>
          <DialogDescription>
            Add a work area to the schedule for {dayObj.name}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          <EditAreaDialogForm onSubmit={onSubmit}
                              storeAreas={props.storeAreas}
                              onCancel={onCancel}/>
        </div>
      </DialogContent>
    </Dialog>
  );
}
