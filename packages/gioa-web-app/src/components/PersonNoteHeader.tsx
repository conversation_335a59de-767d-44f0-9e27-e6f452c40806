import React from "react";
import { Text } from "./Text";
import { EditNotificationIcon, SportsIcon, StylusNoteIcon, ThumbsUpIcon } from "@/src/feedbackIcons.tsx";
import { BlocksIcon, NotebookPenIcon } from "lucide-react";
import { PersonNoteDto } from "../../../api/src/schemas.ts";

export interface PersonNoteHeaderProps {
  note: PersonNoteDto;
}

export function getPersonNoteHeading(note: PersonNoteDto) {
  switch (note.type) {
    case "general":
      if (note.noteType === "general") {
        return "General Note";
      } else if (note.noteType === "positive-feedback") {
        return "Positive Feedback";
      } else if (note.noteType === "coaching") {
        return "Coaching Moment";
      }
      return "General Note";
    case "correctiveAction":
      return note.correctiveAction.isFormalized ? "Corrective Action" : "Actionable Item";
    case "positionPerformanceDataPoint":
      return "Position Score";
    default:
      return "Note";
  }
}

// Extracted function for getting note icons and background colors
export const getNoteIconAndBg = (noteType: string, noteSubType?: string) => {
  if (noteType === "correctiveAction") {
    return { icon: <EditNotificationIcon />, bgColor: "#FFF4DE" };
  } else if (noteType === "general") {
    if (noteSubType === "general") {
      return { icon: <StylusNoteIcon />, bgColor: "#E0EFFF" };
    } else if (noteSubType === "coaching") {
      return { icon: <SportsIcon />, bgColor: "#DCFCE7" };
    } else if (noteSubType === "positive-feedback") {
      return { icon: <ThumbsUpIcon />, bgColor: "#F3E8FF" };
    }
  } else if (noteType === "positionPerformanceDataPoint") {
    return { icon: <BlocksIcon />, bgColor: "#FFF6DF" };
  }

  return { icon: <NotebookPenIcon />, bgColor: "#F1F5F9" };
};

export const PersonNoteHeader: React.FC<PersonNoteHeaderProps> = ({ note }) => {
  const { icon, bgColor } = getNoteIconAndBg(note.type, note.type === "general" ? note.noteType : undefined);

  return (
    <div className="flex items-center justify-between">
      <Text semibold>{getPersonNoteHeading(note)}</Text>
      <div className="w-8 h-8 rounded-lg flex items-center justify-center" style={{ backgroundColor: bgColor }}>
        {icon}
      </div>
    </div>
  );
};
