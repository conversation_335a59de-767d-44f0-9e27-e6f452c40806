import React from 'react';
import {createLink} from "@tanstack/react-router"
import {cn} from "@/src/util.ts";
import {textVariants} from "@/src/components/Text.tsx";

export interface LinkProps extends React.AnchorHTMLAttributes<HTMLAnchorElement> {
  children?: any;
  className?: string;
}

const LinkInternal: React.FC<LinkProps> = (props) => {
  return (
    <a {...props} className={cn(textVariants(), "text-blue-600 hover:underline", props.className)}>
      {props.children}
    </a>
  );
}

export const Link = createLink(LinkInternal)

export const ExternalLink = (props: LinkProps) => {
  return <LinkInternal {...props} target={"_blank"} rel={"noreferrer noopener"}/>;
};

