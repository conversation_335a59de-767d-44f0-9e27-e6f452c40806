import React, {use<PERSON>emo} from 'react';
import {<PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ooter, <PERSON><PERSON><PERSON>eader, DialogTitle} from "@/src/components/ui/dialog.tsx";
import {Button} from "@/src/components/ui/button.tsx";
import {api} from "@/src/api.ts";
import {getHumanReadableErrorMessage} from "@/src/components/ErrorAlert.tsx";
import {toast} from 'sonner';
import {Table, TableBody, TableCell, TableHead, TableHeader, TableRow} from './ui/table';
import {Input} from './ui/input';
import {Cents, toDollars} from "../../../api/src/scheduling/metrics/cents.ts";
import {parseDollarsOrUndef} from "@/src/util.tsx";
import {SchedulePersonDto} from "../../../api/src/schedulePersonDto.ts";
import {forEach, isEmpty, map, orderBy, reduce} from 'lodash';
import {toCents} from "../../../api/src/scheduling/metrics/dollars.ts";
import {useForm} from '@tanstack/react-form';
import {zodValidator} from '@tanstack/zod-form-adapter';
import {FormControl} from './form/FormControl';
import {FieldInfo} from './form/FieldInfo';

export interface EditPayRatesDialogProps {
  isOpen: boolean;
  onClose: () => void;
  storeId: string;
  scheduleId: string;
  people: SchedulePersonDto[];
}

export function EditPayRatesDialog({isOpen, onClose, storeId, scheduleId, people}: EditPayRatesDialogProps) {
  const sortedPeople = useMemo(() => orderBy(people, [p => p.lastName, p => p.firstName]), [people]);

  const [{payRates}] = api.data.getPayRates.useSuspenseQuery({storeId}, {
    select: ({payRates}) => {
      const newPayRates = reduce(people, (result, person) => {
        const personId = person.id || '';
        const payRateCents = payRates.get(personId);
        const payRateDollars = payRateCents !== undefined ? toDollars(payRateCents) : undefined;

        if (payRateDollars !== undefined) {
          result[personId] = payRateDollars.toString();
        } else {
          result[personId] = '';
        }

        return result;
      }, {} as Record<string, string>);

      return {payRates: newPayRates};
    }
  });

  const updatePayRates = api.data.updatePayRates.useMutation();
  const apiUtil = api.useUtils();

  const form = useForm({
    defaultValues: {
      payRates: payRates
    },
    onSubmit: async ({value}) => {
      try {
        const payRatesMap = new Map<string, Cents>();

        forEach(value.payRates, (payRateStr, personId) => {
          if (!isEmpty(payRateStr)) {
            const dollars = parseDollarsOrUndef(payRateStr);
            if (dollars !== undefined) {
              payRatesMap.set(personId, toCents(dollars));
            }
          }
        });

        await updatePayRates.mutateAsync({
          storeId,
          scheduleId,
          payRates: payRatesMap
        });

        apiUtil.data.invalidate();
        apiUtil.user.getAllSchedulePeopleAtStore.invalidate();

        toast.success("Pay rates saved successfully");
        onClose();
      } catch (e) {
        alert("Error saving pay rates: " + getHumanReadableErrorMessage(e));
      }
    },
    validatorAdapter: zodValidator(),
  });

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent size="lg">
        <DialogHeader>
          <DialogTitle>Edit Pay Rates</DialogTitle>
        </DialogHeader>

        <form onSubmit={(e) => {
          e.preventDefault();
          e.stopPropagation();
          form.handleSubmit();
        }}>
          <div className="py-4">
            <p className="text-sm text-muted-foreground mb-4">
              Edit pay rates for your team members. Pay rates are used to calculate labor costs.
            </p>

            <div className="overflow-y-auto" style={{maxHeight: "calc(100vh - 300px)"}}>
              <Table className={"w-auto"}>
                <TableHeader>
                  <TableRow>
                    <TableHead>Name</TableHead>
                    <TableHead className="w-32">Pay Rate</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {map(sortedPeople, (person) => {
                    const personId = person.id || '';
                    return (
                      <TableRow key={personId}>
                        <TableCell className={"py-1"}>{person.firstName} {person.lastName}</TableCell>
                        <TableCell className={"py-1"}>
                          <form.Field
                            name={`payRates.${personId}`}
                            validators={{
                              onChange: ({value}) => {
                                if (isEmpty(value)) return;
                                return parseDollarsOrUndef(value) === undefined
                                  ? "Must be a valid dollar amount"
                                  : undefined;
                              }
                            }}
                          >
                            {(field) => (
                              <FormControl className="mb-0">
                                <div className="flex items-center gap-2">
                                  <Input
                                    type="number"
                                    step="0.01"
                                    min="0"
                                    value={field.state.value}
                                    onChange={(e) => field.handleChange(e.target.value)}
                                    onBlur={field.handleBlur}
                                    placeholder="$0.00"
                                    className="w-24 text-right"
                                    hasError={!isEmpty(field.state.meta.errors)}
                                  />
                                  <div>
                                    /hr
                                  </div>
                                </div>
                                <FieldInfo field={field}/>
                              </FormControl>
                            )}
                          </form.Field>
                        </TableCell>
                      </TableRow>
                    );
                  })}
                </TableBody>
              </Table>
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={onClose} type="button">
              Cancel
            </Button>
            <Button
              type="submit"
              isLoading={updatePayRates.isPending}
            >
              Save
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
