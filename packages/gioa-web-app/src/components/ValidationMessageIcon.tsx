import React from 'react';
import {cn} from "@/src/util.ts";
import {messageSeverityToIcon} from "@/src/components/ScheduleBuilder.util.tsx";
import {messageSeverityToColor} from "../../../api/src/scheduleBuilder.util.ts";

export interface ValidationMessageIconProps {
  severity: number;
}

export const ValidationMessageIcon: React.FC<ValidationMessageIconProps> = ({severity}) => {
  return (
    <div
      className={cn("flex items-center justify-center w-10 h-10 rounded-md shrink-0", messageSeverityToColor(severity))}>
      {messageSeverityToIcon(severity, 20)}
    </div>
  );
}
