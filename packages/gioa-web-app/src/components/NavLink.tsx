import React from 'react';
import {useLocation, useNavigate} from "@tanstack/react-router"
import {cn} from "@/src/util.ts";
import {textVariants} from "@/src/components/Text.tsx";

export interface NavLinkProps {
  from: any;
  to?: string;
  href?: string;
  urlMatch?: RegExp;
  activeProps?: Record<string, any>;
  inactiveProps?: Record<string, any>;
  activeOptions?: {
    exact?: boolean;
  };
  children?: React.ReactNode;
  className?: string;
  onClick?: (e: React.MouseEvent<HTMLAnchorElement>) => void;
}

export const NavLink: React.FC<NavLinkProps> = (props) => {
  const {
    to,
    from,
    href,
    urlMatch,
    activeProps,
    inactiveProps,
    activeOptions,
    onClick,
    ...restProps
  } = props;

  const location = useLocation();
  const navigate = useNavigate();

  // Determine the href to use
  const linkHref = to || href || '#';

  // Check if current URL matches the provided regex pattern
  let isActive = false;

  if (urlMatch) {
    if (activeOptions?.exact) {
      // For exact matching, the entire pathname must match the pattern
      // and must be the same length
      const match = location.pathname.match(urlMatch);
      isActive = match !== null && match[0] === location.pathname;
    } else {
      // For non-exact matching, just check if the pattern is contained in the pathname
      isActive = urlMatch.test(location.pathname);
    }
  }

  // Apply the appropriate props based on active state
  const stateProps = isActive ? activeProps : inactiveProps;

  // Handle click with navigation if 'to' is provided
  const handleClick = (e: React.MouseEvent<HTMLAnchorElement>) => {
    if (onClick) {
      onClick(e);
    }

    if (to && !e.defaultPrevented) {
      e.preventDefault();
      navigate({
        to,
        from
      });
    }
  };

  return (
    <a
      href={linkHref}
      onClick={handleClick}
      {...restProps}
      {...stateProps}
      className={cn(
        textVariants(),
        "text-gray-900",
        props.className,
        stateProps?.className
      )}
    >
      {props.children}
    </a>
  );
};

export const StyledNavLink: React.FC<NavLinkProps> = (props) => {
  return (
    <NavLink
      {...props}
      activeProps={{
        className: "bg-blue-50 hover:bg-blue-100",
        ...props.activeProps
      }}
      inactiveProps={{
        className: "hover:bg-blue-100",
        ...props.inactiveProps
      }}
      activeOptions={{
        exact: false,
        ...props.activeOptions
      }}
      className={cn(
        "rounded-lg px-4 p-2 flex items-center gap-3 justify-between",
        props.className
      )}
    />
  );
};
