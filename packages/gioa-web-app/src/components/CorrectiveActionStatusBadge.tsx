import React from 'react';
import { Badge } from './ui/badge';
import { VariantProps } from 'class-variance-authority';

export interface CorrectiveActionStatusBadgeProps {
  status: string;
  size?: "sm" | "md" | "lg";
  className?: string;
}

export const statusToLabel: { [key: string]: string } = {
  inReview: "Needs Review",
  reviewed: "Reviewed",
  unacknowledged: "Unacknowledged",
  acknowledged: "Acknowledged",
  archived: "Archived",
  unknown: "Unknown"
};

const statusToColorScheme: { [key: string]: string } = {
  inReview: "secondary",
  reviewed: "secondary",
  unacknowledged: "yellow",
  acknowledged: "success",
  archived: "default",
  unknown: "danger"
};

export const CorrectiveActionStatusBadge: React.FC<CorrectiveActionStatusBadgeProps> = ({
  status,
  size = "sm",
  className
}) => {
  const label = statusToLabel[status] || statusToLabel.unknown;
  const colorScheme = statusToColorScheme[status] || statusToColorScheme.unknown;

  return (
    <Badge
      colorScheme={colorScheme as any}
      size={size}
      className={className}
    >
      {label}
    </Badge>
  );
};
