import React from 'react';
import {edgesGradient} from "@/src/components/ShiftRowAvailabilityOverlay.tsx";
import {RangeOverlayLabel} from "@/src/components/RangeOverlayLabel.tsx";
import {DailyTimeRange} from "../../../api/src/timeSchemas.ts";

export interface AvailabilityRowSpanProps {
  rowIndex: number;
  isAllDay: boolean;
  range: DailyTimeRange;
}

export const AvailabilityRowSpan: React.FC<AvailabilityRowSpanProps> = React.memo((props) => {
  return (
    <div className={"h-full flex flex-row py-0.5"}>
      <div style={{
        background: edgesGradient({
          show: true,
          middleOpacity: 0.5,
          edgeOpacity: 0.7,
          rgb: [62, 75, 126] // gioaBlue
        })
      }} className={"rounded-md h-full grow"}>
        <RangeOverlayLabel showAvail={true} className={"top-1/2 transform -translate-y-1/2"}
                           labelClassName={"sticky right-1"}
                           showClassName={"opacity-90"}
                           isAllDay={props.isAllDay}
                           range={props.range}/>
      </div>
    </div>
  );
});
