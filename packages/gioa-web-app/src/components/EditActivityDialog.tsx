import React, {useMemo} from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger
} from "@/src/components/ui/dialog.tsx";
import {But<PERSON>} from "@/src/components/ui/button.tsx";
import {
  incrementTo24HourTime,
  incrementToFriendlyTime,
  ScheduleRowInfo,
  ShiftActivity,
  storeTimeToIncrement
} from "../../../api/src/scheduleBuilder.util.ts";
import {DailyTimeRange} from '@gioa/api/src/timeSchemas.ts';
import {useDisclosure} from "@/src/hooks/useDisclosure.tsx";
import {ActivityDialogForm, ActivityDialogFormValues} from "@/src/components/ActivityDialogForm.tsx";
import {activityToLaborCounting} from "@/src/components/ShiftActivityTypeChooser.tsx";

export interface EditActivityDialogProps {
  shift: ScheduleRowInfo;
  activity: ShiftActivity;
  onSubmit: (activity: ShiftActivity) => void;
  storeHours: DailyTimeRange;
  storeId: string;
}

export interface ActivityFormValues {
  start: string;
  end: string;
  title: string;
  description: string;
}

export const EditActivityDialog: React.FC<EditActivityDialogProps> = ({activity, storeId, onSubmit, shift, storeHours}) => {
  const onFormSubmit = (value: ActivityDialogFormValues) => {
    if (!value.activityType) return;

    const countsTowardsLaborConfig = value.activityType ? activityToLaborCounting[value.activityType] : "configurable";
    const countsTowardsLabor = countsTowardsLaborConfig === "configurable"
            ? value.countsTowardsLabor
            : countsTowardsLaborConfig === "counts";

    onSubmit({
      ...activity,
      activityType: value.activityType,
      start: storeTimeToIncrement(storeHours, value.start),
      end: storeTimeToIncrement(storeHours, value.end),
      title: value.title,
      description: value.description,
      countsTowardsLabor: countsTowardsLabor,
      setupPositionTitle: value.setupPositionTitle ?? undefined,
      payStatus: value.payStatus ?? undefined,
    })
    dialog.onClose();
  }

  const activityFormValues = useMemo((): ActivityDialogFormValues => {
    return {
      countsTowardsLabor: activity.countsTowardsLabor,
      description: activity.description || "",
      start: incrementTo24HourTime(storeHours, activity.start),
      end: incrementTo24HourTime(storeHours, activity.end),
      title: activity.title,
      activityType: activity.activityType,
      setupPositionTitle: activity.setupPositionTitle || undefined,
      payStatus: activity.payStatus || undefined
    }
  }, [storeHours, activity]);

  const dialog = useDisclosure()

  return <Dialog open={dialog.isOpen} onOpenChange={dialog.setOpen}>
    <DialogTrigger asChild>
      <Button>
        Edit
      </Button>
    </DialogTrigger>
    <DialogContent size={"xl"}>
      <DialogHeader>
        <DialogTitle>Edit Activity</DialogTitle>
        <DialogDescription>
          This shift goes
          from {incrementToFriendlyTime(storeHours, shift.start)} to {incrementToFriendlyTime(storeHours, shift.end)}.
          Choose a timeframe for this activity.
        </DialogDescription>
      </DialogHeader>

      <ActivityDialogForm defaultValues={{
        start: incrementTo24HourTime(storeHours, activity.start),
        end: incrementTo24HourTime(storeHours, activity.end),
        title: activity.title,
        description: activity.description || "",
        countsTowardsLabor: activity.countsTowardsLabor,
        activityType: activity.activityType,
        setupPositionTitle: activity.setupPositionTitle || undefined,
        payStatus: activity.payStatus || undefined
      }} onSubmit={onFormSubmit} onCancel={dialog.onClose}
                          shift={shift} storeId={storeId}
                          storeHours={storeHours}
                          newValues={activityFormValues}/>
    </DialogContent>
  </Dialog>
}
