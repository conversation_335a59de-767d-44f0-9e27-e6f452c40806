import React, {useState} from 'react';
import {find, includes, isEqual, map} from "lodash";
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuRadioGroup,
  DropdownMenuRadioItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger
} from "@/src/components/ui/dropdown-menu.tsx";
import {Button} from "@/src/components/ui/button.tsx";
import {ChevronDownIcon} from "lucide-react";
import {WeeklyViewFilter} from './WeeklyViewControlBar.types';

export interface WeeklyFilterControlProps {
  filter: WeeklyViewFilter;
  onDeleteFilter: (filterId: string) => void;
  setFilterValue: (filterId: string, value: any) => void;
  suppressDelete?: boolean;
  deleteLabel?: string;
}

export function toggleInArray<T>(arr: T[], item: T, equals: (i1: T, i2: T) => boolean = isEqual): T[] {
  if (Boolean(find(arr, i => equals(i, item)))) {
    return arr.filter(i => !equals(i, item));
  } else {
    return [...arr, item];
  }
}

export const isAddingWeeklyFilter = {
  current: false
}

export const WeeklyFilterControl: React.FC<WeeklyFilterControlProps> = ({filter, onDeleteFilter, suppressDelete,
                                                                          deleteLabel = "Delete Filter",
                                                                          setFilterValue}) => {
  const valueLabel = filter.controlType === "select"
    ? find(filter.options, o => o.value === filter.value)?.label
    : map(filter.value, v => find(filter.options, o => o.value === v)?.label).join(", ");
  const [isOpen, setIsOpen] = useState(isAddingWeeklyFilter.current);

  return <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
    <DropdownMenuTrigger asChild>
      <Button variant={"outline"} size={"sm"}
              rightIcon={<ChevronDownIcon className={"text-gray-700"} size={16}/>}>
        {filter.label ? <span className={"font-semibold"}>
          {filter.label}:&nbsp;
        </span> : null}
        {' '} {valueLabel}
      </Button>
    </DropdownMenuTrigger>
    <DropdownMenuContent align={"start"}>
      {filter.controlType === "select" ?
        <DropdownMenuRadioGroup value={filter.value} onValueChange={val => setFilterValue(filter.id, val)}>
          {map(filter.options, option => {
            return <DropdownMenuRadioItem key={option.value} value={option.value}>
              {option.label}
            </DropdownMenuRadioItem>
          })}
        </DropdownMenuRadioGroup>
        : map(filter.options, option => {
          return <DropdownMenuCheckboxItem key={option.value}
                                           onSelect={e => e.preventDefault()}
                                           onCheckedChange={val => {
                                             const newValue = toggleInArray(filter.value, option.value);
                                             setFilterValue(filter.id, newValue);
                                           }}
                                           checked={includes(filter.value, option.value)}>
            {option.label}
          </DropdownMenuCheckboxItem>
        })}

      {!suppressDelete ?
        <>
        <DropdownMenuSeparator/>
        <DropdownMenuItem onSelect={() => onDeleteFilter(filter.id)} className={"text-muted-foreground"}>
          {deleteLabel}
        </DropdownMenuItem>
      </> : null}
    </DropdownMenuContent>
  </DropdownMenu>
}
