const macKeyMap: { [key: string]: string } = {
  ctrl: '⌃',
  control: '⌃',
  cmd: '⌘',
  command: '⌘',
  mod: '⌘',
  meta: '⌘',
  alt: '⌥',
  option: '⌥',
  shift: '⇧',
  return: '↩',
  enter: '↩',
  delete: '⌫',
  backspace: '⌫',
  esc: '⎋',
  escape: '⎋',
  tab: '⇥',
  capslock: '⇪',
  space: '␣',
};

const windowsKeyMap: { [key: string]: string } = {
  ctrl: 'Ctrl',
  control: 'Ctrl',
  mod: 'Ctrl',
  alt: 'Alt',
  option: 'Alt',
  shift: 'Shift',
  meta: 'Win',
  command: 'Win',
  cmd: 'Win',
  return: 'Enter',
  enter: 'Enter',
  delete: 'Del',
  backspace: 'Backspace',
  esc: 'Esc',
  escape: 'Esc',
  tab: 'Tab',
  capslock: 'Caps',
  space: 'Space',
};

export function parseHotkeysForPlatform(isMac: boolean, isWin: boolean, hotkeyString: string): string {
  const hotkeyGroups = hotkeyString.split(',').map(group => group.trim());

  return hotkeyGroups.map(group => {
    const keys = group.split('+').map(key => key.trim().toLowerCase());

    let parsedKeys = keys.map(key => {
      if (isMac) {
        return macKeyMap[key] || key.charAt(0).toUpperCase() + key.slice(1);
      } else if (isWin) {
        return windowsKeyMap[key] || key.charAt(0).toUpperCase() + key.slice(1);
      } else {
        return key.charAt(0).toUpperCase() + key.slice(1);
      }
    });

    if (isMac) {
      // For Mac, join keys without '+' and move non-modifier keys to the end
      const modifiers = parsedKeys.filter(key => '⌃⌘⌥⇧'.includes(key));
      const nonModifiers = parsedKeys.filter(key => !'⌃⌘⌥⇧'.includes(key));
      return modifiers.join('') + nonModifiers.join('');
    } else {
      // For other platforms, join keys with '+'
      return parsedKeys.join('+');
    }
  }).join(', ');
}
