import React from 'react';
import {Popover, PopoverContent, PopoverTrigger} from "./ui/popover.tsx";
import {useForm} from "@tanstack/react-form";
import {useDisclosure} from "@/src/hooks/useDisclosure.tsx";
import {FormControl} from "@/src/components/form/FormControl.tsx";
import {Label} from "@/src/components/ui/label.tsx";
import {FieldInfo} from "@/src/components/form/FieldInfo.tsx";
import {Checkbox} from "@/src/components/ui/checkbox.tsx";
import {Text} from "@/src/components/Text.tsx";
import {map} from "lodash";
import {Separator} from "@/src/components/ui/separator.tsx";
import {Button} from "@/src/components/ui/button.tsx";

interface FilterChecklistTagsPopoverProps {
  initialValues: FilterChecklistTagsFormValues;
  defaultValues: FilterChecklistTagsFormValues;
  onSubmit: (values: FilterChecklistTagsFormValues) => void;
  children: React.ReactNode;
  allTags: string[];
}

export interface FilterChecklistTagsFormValues {
  tags: string[];
}

export const FilterChecklistTagsPopover: React.FC<FilterChecklistTagsPopoverProps> = ({
                                                                                        initialValues,
                                                                                        defaultValues,
                                                                                        onSubmit,
                                                                                        children,
                                                                                        allTags
                                                                                      }) => {
  const popover = useDisclosure();

  const resetForm = () => {
    form.store.setState((state) => ({
      ...state,
      values: defaultValues
    }));
    form.handleSubmit();
  }

  const form = useForm({
    defaultValues: {
      ...defaultValues,
      ...initialValues,
    },
    onSubmit: ({value}) => {
      onSubmit(value);
      popover.setOpen(false);
    }
  });

  return (
    <Popover open={popover.isOpen} onOpenChange={popover.setOpen}>
      <PopoverTrigger asChild>
        {children}
      </PopoverTrigger>
      <PopoverContent className="p-4 w-[300px]">
        <div className={"flex flex-row justify-between items-center"}>
          <Text size={"lg"}>Filter by Tags</Text>
          <Button variant="link" onClick={resetForm}>Reset</Button>
        </div>
        <Separator className={"my-3"}/>
        <form.Field name={"tags"}
                    children={(field) => {
                      return <FormControl>
                        <Label htmlFor={field.name}>Tags</Label>

                        <div className={"flex mt-3 flex-col gap-2 cursor-pointer"}>
                          {allTags.length === 0 ? (
                            <Text muted size="sm">No tags available</Text>
                          ) : (
                            map(allTags, (tag) => {
                              const isChecked = field.state.value.includes(tag);
                              return (
                                <div 
                                  key={tag}
                                  className={"flex flex-row gap-2 items-center"}
                                  onClick={() => {
                                    const newState = isChecked
                                      ? field.state.value.filter(v => v !== tag)
                                      : [...field.state.value, tag];
                                    field.handleChange(newState);
                                  }}
                                >
                                  <Checkbox checked={isChecked} />
                                  <Text>{tag}</Text>
                                </div>
                              );
                            })
                          )}
                        </div>
                        <FieldInfo field={field}/>
                      </FormControl>;
                    }}/>
        <div className={"flex flex-row gap-2 mt-4"}>
          <Button variant="outline" className={"flex-1 mt-4"} onClick={form.handleSubmit}>Filter</Button>
        </div>
      </PopoverContent>
    </Popover>
  );
};
