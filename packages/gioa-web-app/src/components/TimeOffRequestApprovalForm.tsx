import React from 'react';
import {formatTimeOffDate} from "@/src/date.util.ts";
import {cn} from "@/src/util.ts";
import {ShiftConflictAlert} from "@/src/components/ShiftConflictAlert.tsx";
import {Separator} from "@/src/components/ui/separator.tsx";
import {FormControl} from "@/src/components/form/FormControl.tsx";
import {Label} from "@/src/components/ui/label.tsx";
import {FormTextarea} from "@/src/components/form/FormTextarea.tsx";
import {FieldInfo} from "@/src/components/form/FieldInfo.tsx";
import {ShiftConflictDto, TimeOffAdminDto} from "../../../api/src/personTimeOff.schemas.ts";
import {ReactFormExtendedApi, Validator} from '@tanstack/react-form';
import {ZodType} from 'zod';
import {RequestCommentBox} from "@/src/components/RequestCommentBox.tsx";

export interface TimeOffRequestApprovalFormProps {
  businessId: string;
  storeId: string;
  request: TimeOffAdminDto;
  timezone: string;
  conflictingShifts: ShiftConflictDto[];
  form: ReactFormExtendedApi<any, Validator<unknown, ZodType>>
  children: React.ReactNode;
}

export const TimeOffRequestApprovalForm: React.FC<TimeOffRequestApprovalFormProps> = ({
                                                                                        businessId, storeId,
                                                                                        request: req,
                                                                                        timezone,
                                                                                        conflictingShifts,
                                                                                        form, children
                                                                                      }) => {
  return <>
    <h2 className={"text-lg font-medium mb-4"}>
      {formatTimeOffDate(req.range.start, timezone ?? null)} - {formatTimeOffDate(req.range.end, timezone ?? null)}
    </h2>

    <h2 className={"text-lg font-medium mb-1"}>
      Type
    </h2>
    <div className={cn("mb-6", {"text-muted-foreground": !req.timeOffType})}>
      {req.timeOffType || "(No type provided)"}
    </div>

    <h2 className={"text-lg font-medium mb-1"}>
      Justification
    </h2>
    <div className={cn("mb-6", {"text-muted-foreground": !req.submittedReason})}>
      {req.updatedReason || req.submittedReason || "(No justification provided)"}
    </div>

    <ShiftConflictAlert conflictingShifts={conflictingShifts}
                        timezone={timezone ?? null}
                        routeToSchedule={conflict => `/${businessId}/${storeId}/schedules/${conflict.scheduleId}`}/>

    <Separator className={"my-6"}/>

    {req.status === "pending" ? <>

      <form onSubmit={(e) => {
        e.preventDefault()
        e.stopPropagation()
      }} className={"mb-4"}>
        <form.Field name={"reason"}
                    children={field => <FormControl>
                      <Label className={"mb-1"}>Comments (optional)</Label>
                      <p className={"text-sm text-muted-foreground mb-2"}>
                        This will be visible to the team member and management.
                      </p>
                      <FormTextarea field={field}
                                    placeholder="Enter your reason for approving or declining this request..."/>
                      <FieldInfo field={field}/>
                    </FormControl>}/>
      </form>

      {children}

    </> : req.approvedReason || req.declinedReason || req.cancelledReason ? <>
      <h2 className={"text-lg font-medium mb-1"}>
        Comments
      </h2>
      <RequestCommentBox request={req}/>

    </> : null}
  </>
}
