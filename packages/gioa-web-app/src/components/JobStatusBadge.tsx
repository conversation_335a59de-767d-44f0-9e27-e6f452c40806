import React from "react";
import {Badge} from "@/src/components/ui/badge.tsx";
import {capitalize} from "lodash";

const statusToColorScheme = {
  pending: "outline",
  started: "secondary",
  success: "success",
  error: "destructive",
  default: "default"
} as const;

export interface JobStatusBadgeProps {
  status: string;
  className?: string;
  size?: "sm" | "md" | "lg";
}

export function JobStatusBadge({status, className, size}: JobStatusBadgeProps) {
  const colorScheme = statusToColorScheme[status as keyof typeof statusToColorScheme] || statusToColorScheme.default;
  return <Badge colorScheme={colorScheme} className={className} size={size}>
    {capitalize(status)}
  </Badge>;
}
