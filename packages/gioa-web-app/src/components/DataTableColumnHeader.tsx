import {
  ArrowDownIcon,
  ArrowUpIcon,
  SortAscIcon,
  EyeOffIcon, ArrowUpDownIcon,
  FilterIcon,
  XIcon
} from "lucide-react"
import {Column, FilterFn} from "@tanstack/react-table"
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger
} from "./ui/dropdown-menu"
import {Button} from "./ui/button"
import {cn} from "@/src/util.ts";
import { Checkbox } from "./ui/checkbox";
import { Label } from "./ui/label";
import {compact} from "lodash";

interface DataTableColumnHeaderProps<TData, TValue>
  extends React.HTMLAttributes<HTMLDivElement> {
  column: Column<TData, TValue>
  title: string
  /**
   * Determines whether this column can be faceted (filtered by multiple discrete values).
   *
   * If this column CAN be faceted, then its definition must include a filter function that operates over arrays,
   * like `arrIncludes`, `arrIncludesSome`, or `arrIncludesAll`.
   * Note that these may not provide the exact UX you want, and you may have to write your own.
   *
   * Read more about column faceting here:
   * https://tanstack.com/table/v8/docs/api/features/column-faceting
   */
  canBeFaceted?: boolean;
}

export function DataTableColumnHeader<TData, TValue>({
                                                       column,
                                                       title,
                                                       canBeFaceted = false,
                                                       className,
                                                     }: DataTableColumnHeaderProps<TData, TValue>) {
  if (!column.getCanSort()) {
    return <div className={cn(className)}>{title}</div>
  }

  return (
    <div className={cn("flex items-center space-x-2", className)}>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant="ghost"
            size="sm"
            className="-ml-3 h-8 data-[state=open]:bg-accent"
          >
            <span>{title}</span>
            {column.getIsSorted() === "desc" ? (
              <ArrowDownIcon className="ml-2 h-4 w-4"/>
            ) : column.getIsSorted() === "asc" ? (
              <ArrowUpIcon className="ml-2 h-4 w-4"/>
            ) : (
              <ArrowUpDownIcon className="ml-2 h-4 w-4"/>
            )}
            {column.getIsFiltered()
              ? <FilterIcon className="ml-2 h-4 w-4 text-green-700"/>
              : null}
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="start" className={"max-h-96 overflow-auto"}>
          <DropdownMenuLabel>
            Sort
          </DropdownMenuLabel>
          <DropdownMenuItem onClick={() => column.toggleSorting(false)}>
            <ArrowUpIcon className="mr-2 h-3.5 w-3.5 text-muted-foreground/70"/>
            Asc
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => column.toggleSorting(true)}>
            <ArrowDownIcon className="mr-2 h-3.5 w-3.5 text-muted-foreground/70"/>
            Desc
          </DropdownMenuItem>
          {column.getIsSorted() && (
            <DropdownMenuItem onClick={() => column.clearSorting()}>
              <XIcon className="mr-2 h-3.5 w-3.5 text-muted-foreground/70"/>
              Clear
            </DropdownMenuItem>
          )}
          {canBeFaceted && column.getFacetedUniqueValues().size > 0 && (
            (() => {
              const uniqueValuesMap = column.getFacetedUniqueValues();
              const entries = [...uniqueValuesMap];
              return <>
                <DropdownMenuSeparator/>
                <DropdownMenuLabel>
                  Filter
                </DropdownMenuLabel>
                {entries.map(([value, count]: [TValue, number]) => {
                  const getFilterVal = () => {
                    return ([] as TValue[]).concat(column.getFilterValue() as TValue);
                  }
                  const filterValue = getFilterVal();
                  const isChecked = filterValue.includes(value);
                  return (
                    <DropdownMenuCheckboxItem
                      key={String(value)}
                      checked={isChecked}
                      onClick={(e) => {
                        e.stopPropagation();
                        e.preventDefault();
                        const filterValue = compact(getFilterVal());
                        column.setFilterValue([...filterValue, value].filter(v => v === value ? !isChecked : true))
                      }}
                    >
                      {String(value)} ({count})
                    </DropdownMenuCheckboxItem>
                  );
                })}
              </>;
            })()
          )}
          {column.getCanHide() ?
            <>
              <DropdownMenuSeparator/>
              <DropdownMenuItem onClick={() => column.toggleVisibility(false)}>
                <EyeOffIcon className="mr-2 h-3.5 w-3.5 text-muted-foreground/70"/>
                Hide column
              </DropdownMenuItem>
            </> : null}
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  )
}
