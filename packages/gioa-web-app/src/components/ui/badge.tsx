import * as React from "react"
import { cva, type VariantProps } from "class-variance-authority"

import { cn } from "@/src/util"

const badgeVariants = cva(
  "inline-flex items-center rounded-lg border font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",
  {
    variants: {
      colorScheme: {
        default:
          "border-transparent bg-primary-700 text-primary-foreground hover:bg-primary/80",
        secondary:
          "border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",
        success: "border-transparent bg-green-100 text-green-500",
        destructive:
                "border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",
        danger:
                "border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",
        outline: "text-foreground",
        yellow: "border-transparent bg-yellow-100 text-yellow-500",
      },
      size: {
        sm: "text-xs px-2.5 py-0.5",
        md: "text-sm px-3 py-1",
        lg: "text-base px-4 py-2",
      }
    },
    defaultVariants: {
      size: "sm",
      colorScheme: "default",
    },
  }
)

export interface BadgeProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof badgeVariants> {}

function Badge({ className, colorScheme, size, ...props }: BadgeProps) {
  return (
    <div className={cn(badgeVariants({ colorScheme, size }), className)} {...props} />
  )
}

export { Badge, badgeVariants }
