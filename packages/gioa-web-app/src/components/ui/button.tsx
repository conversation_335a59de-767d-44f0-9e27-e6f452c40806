import * as React from "react"
import {Slot} from "@radix-ui/react-slot"
import {cva, type VariantProps} from "class-variance-authority"
import {cn} from "@/src/util"
import {Spinner} from "@/src/components/Spinner";

export const buttonVariants = cva(
  "inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",
  {
    variants: {
      variant: {
        default: "bg-primary-700 text-primary-foreground hover:bg-primary/90",
        destructive:
          "bg-destructive text-destructive-foreground hover:bg-destructive/90",
        outline:
          "border border-input bg-background hover:bg-accent hover:text-accent-foreground",
        secondary:
          "bg-secondary text-secondary-foreground hover:bg-secondary/80",
        ghost: "hover:bg-accent hover:text-accent-foreground",
        link: "text-primary underline-offset-4 hover:underline",
      },
      size: {
        default: "h-10 px-4 py-2",
        xs: "py-1 px-2 rounded-md",
        sm: "h-9 rounded-md px-3",
        lg: "h-11 rounded-md px-8",
        icon: "h-10 w-10",
        iconSm: "h-8 w-8",
        iconLg: "h-12 w-12",
      },
      colorScheme: {
        green: "bg-green-200 text-green-foreground hover:bg-green-300",
        red: "bg-red-200 text-red-foreground hover:bg-red-300",
        amber: "bg-amber-200 text-amber-foreground hover:bg-amber-300",
        blue: "bg-blue-100 text-blue-foreground hover:bg-blue-200",
        purple: "bg-purple-200 text-purple-foreground hover:bg-purple-300",
        violet: "bg-violet-200 text-violet-foreground hover:bg-violet-300",
        yellow: "bg-yellow-200 text-yellow-foreground hover:bg-yellow-300",
      }
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
)

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean;
  isLoading?: boolean;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({
     children,
     className,
     variant,
     size, colorScheme,
     isLoading = false,
     disabled,
     asChild = false,
     leftIcon,
     rightIcon,
     ...props
   }, ref) => {
    const Comp = asChild ? Slot : "button"
    const buttonClassName = cn(buttonVariants({ variant, size, className, colorScheme }));

    return (
      <Comp
        className={buttonClassName}
        ref={ref}
        disabled={isLoading || disabled}
        {...props}
      >
        {isLoading ? (
          <>
            <Spinner className="mr-2" />
            {children}
          </>
        ) : (
          <>
            {leftIcon && <span className="mr-2">{leftIcon}</span>}
            {children}
            {rightIcon && <span className="ml-2">{rightIcon}</span>}
          </>
        )}
      </Comp>
    )
  }
)
Button.displayName = "Button"

export { Button }
