import * as React from "react"
import * as TogglePrimitive from "@radix-ui/react-toggle"
import { cva, type VariantProps } from "class-variance-authority"

import { cn } from "@/src/util"

const toggleVariants = cva(
  "inline-flex items-center justify-center rounded-md text-sm ring-offset-background transition-colors hover:bg-muted hover:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",
  {
    variants: {
      variant: {
        default: "bg-transparent",
        outline:
          "border border-input bg-transparent hover:bg-accent hover:text-accent-foreground",
      },
      size: {
        default: "h-10 px-3",
        sm: "h-9 px-2.5",
        lg: "h-11 px-5",
      },
      colorScheme: {
        default: "data-[state=on]:bg-accent data-[state=on]:text-accent-foreground font-medium",
        primary: "data-[state=on]:bg-gray-700 data-[state=on]:text-white text-gray-700",
        amber: "data-[state=on]:bg-amber-200 data-[state=on]:text-amber-foreground text-amber-200 data-[state=on]:border-amber-400",
      }
    },
    defaultVariants: {
      variant: "default",
      size: "default",
      colorScheme: "default"
    },
  }
)

const Toggle = React.forwardRef<
  React.ElementRef<typeof TogglePrimitive.Root>,
  React.ComponentPropsWithoutRef<typeof TogglePrimitive.Root> &
    VariantProps<typeof toggleVariants>
>(({ className, variant, size, colorScheme, ...props }, ref) => (
  <TogglePrimitive.Root
    ref={ref}
    className={cn(toggleVariants({ variant, size, colorScheme, className }))}
    {...props}
  />
))

Toggle.displayName = TogglePrimitive.Root.displayName

export { Toggle, toggleVariants }
