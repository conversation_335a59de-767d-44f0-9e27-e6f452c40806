import * as React from "react"
import {cn} from "@/src/util"
import {LucideIcon} from "lucide-react"
import {cva, type VariantProps} from "class-variance-authority"

export interface InputProps
  extends React.InputHTMLAttributes<HTMLInputElement>, VariantProps<typeof inputVariants> {
  hasError?: boolean
  leftIcon?: LucideIcon
  rightIcon?: LucideIcon
  containerClassName?: string
  onRightIconClick?: () => void
}

export const inputVariants = cva("flex h-10 w-full rounded-md px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed", {
  variants: {
    variant: {
      outline: "bg-background border border-input placeholder:text-muted-foreground disabled:opacity-50",
      filled: "bg-gray-100 placeholder:text-gray-600",
    }
  },
  defaultVariants: {
    variant: "outline"
  }
})

const Input = React.forwardRef<HTMLInputElement, InputProps>(
  ({ className, containerClassName, type, hasError, leftIcon: LeftIcon, rightIcon: RightIcon, onRightIconClick, variant, ...props }, ref) => {
    const inputClassName = inputVariants({ className, variant })

    return (
      <div className={cn("relative w-full", containerClassName)}>
        {LeftIcon && (
          <LeftIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={18} />
        )}
        <input
          type={type}
          className={cn(
            inputClassName,
            hasError ? "border-red-600" : null,
            LeftIcon ? "pl-10" : null,
            RightIcon ? "pr-10" : null,
            className
          )}
          ref={ref}
          {...props}
        />
        {RightIcon && (
          <div
            onClick={onRightIconClick}
            className={cn(
              "absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400",
              onRightIconClick ? "cursor-pointer hover:text-gray-600" : ""
            )}
          >
            <RightIcon size={18} />
          </div>
        )}
      </div>
    )
  }
)
Input.displayName = "Input"

export { Input }
