import {flexR<PERSON>, Row, Table as TanstackTable,} from "@tanstack/react-table"
import {Table, TableBody, TableCell, TableHead, TableHeader, TableRow,} from "@/src/components/ui/table"
import {DataTablePagination} from "@/src/components/DataTablePagination.tsx";
import React from "react";
import {Link} from "@tanstack/react-router";
import {cn} from "@/src/util.ts";
import {map} from "lodash";

interface DataTableProps<TData> {
  table: TanstackTable<TData>
  getRowDetailTo?: (row: Row<TData>) => string;
  getRowDetailToParams?: (row: Row<TData>) => any;
  onRowClicked?: (row: Row<TData>) => void;
  rowDetailLinkFrom: string;
  className?: string;
  EmptyComponent?: React.ReactNode;
}

export function DataTable<TData>({
                                   table,
                                   getRowDetailTo,
                                   getRowDetailToParams,
                                   onRowClicked,
                                   className,
                                   EmptyComponent
                                 }: DataTableProps<TData>) {

  return (
    <div>
      <div className={cn("mb-3 bg-white", className)}>
        <Table >
          <TableHeader className="bg-blue-50">
            {map(table.getHeaderGroups(), (headerGroup) => (
              <TableRow key={headerGroup.id}>
                {map(headerGroup.headers, (header) => {
                  return (
                    <TableHead key={header.id} className="text-gioaBlue">
                      {header.isPlaceholder
                        ? null
                        : flexRender(
                          header.column.columnDef.header,
                          header.getContext()
                        )}
                    </TableHead>
                  )
                })}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody >
            {table.getRowModel().rows?.length ? (
              map(table.getRowModel().rows, (row) => {
                const rowDetailTo = getRowDetailTo?.(row);
                return (
                  <TableRow
                    className={onRowClicked ? "cursor-pointer" : undefined}
                    key={row.id}
                    data-state={row.getIsSelected() && "selected"}
                    onClick={() => onRowClicked?.(row)}
                  >
                    {map(row.getVisibleCells(), (cell) => {
                      const renderedRow = flexRender(cell.column.columnDef.cell, cell.getContext());
                      if (rowDetailTo) {
                        return (
                          <TableCell key={cell.id} className={"p-0"}>
                            <Link
                              to={rowDetailTo}
                              search={getRowDetailToParams?.(row) ?? {}}
                              className="block w-full h-full p-4"
                              style={{textDecoration: 'none', color: 'inherit'}}>
                            <span className="sr-only">
                              View details for this row
                            </span>
                              {renderedRow}
                            </Link>
                          </TableCell>
                        );
                      } else {
                        return <TableCell key={cell.id}>{renderedRow}</TableCell>;
                      }
                    })}
                  </TableRow>
                );
              })
            ) : (
              <TableRow>
                <TableCell colSpan={table.getAllColumns().length} className="h-24 text-center">
                  {EmptyComponent ?? "No results."}
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
      <DataTablePagination table={table}/>
    </div>
  )
}
