import React, {useEffect, useMemo, useState} from "react";
import {Link} from '@tanstack/react-router'
import {Heading} from "@/src/components/Heading.tsx";
import {Text} from "@/src/components/Text.tsx";
import {Select, SelectContent, SelectItem, SelectTrigger, SelectValue} from "@/src/components/ui/select.tsx";
import {IsoDayPicker} from "@/src/components/IsoDayPicker.tsx";
import {DateTime} from "luxon";
import {IsoWeekDate} from "../../../api/src/timeSchemas.ts";
import {api} from "@/src/api.ts";
import {
  getDateFromIsoCompleteDate,
  getDateRangeDurationHours,
  getDateTimeFromWeekDayTime,
  isTimeAllDay
} from "../../../api/src/date.util.ts";
import {map} from "lodash";
import {formatInTimeZone} from "date-fns-tz";
import {ArrowLeftIcon, ArrowRightIcon, ChevronRightIcon} from "lucide-react";
import {PersonTimeOffRequestStatusBadge} from "@/src/components/PersonTimeOffRequestStatusBadge.tsx";
import {TimeOffStatusFilter} from "../../../api/src/personTimeOff.schemas.ts";
import {DurationDaysText} from "@/src/components/DurationDaysText.tsx";
import {Button} from "@/src/components/ui/button.tsx";
import {Badge} from "@/src/components/ui/badge.tsx";
import {cn} from "@/src/util.ts";

export interface TimeOffRequestSearchProps {
  storeId: string;
  timeOffRequestId: string;
  timeOffRequestStatus: string;
  today: DateTime;
  initialDate: DateTime;
  timezone: string;
  routeFullPath: string;
  requestsSearchParams: any;
}

const dateTimeToIsoWeekDate = (date: DateTime): IsoWeekDate => ({
  year: date.weekYear,
  week: date.weekNumber,
  day: date.weekday ?? 1,
});

export const TimeOffRequestSearch: React.FC<TimeOffRequestSearchProps> = ({
                                                                            storeId, requestsSearchParams,
                                                                            timeOffRequestId,
                                                                            today, timeOffRequestStatus,
                                                                            initialDate,
                                                                            timezone,
                                                                            routeFullPath
                                                                          }) => {
  const [selectedStatus, setSelectedStatus] = useState<string>("all");
  const [selectedDate, setSelectedDate] = useState<IsoWeekDate>({
    year: initialDate.weekYear,
    week: initialDate.weekNumber,
    day: initialDate.weekday ?? 1,
  });

  // the calendar component doesn't update when the date changes, so we need to update it manually
  useEffect(() => setSelectedDate(dateTimeToIsoWeekDate(initialDate)), [initialDate])

  const dateRange = useMemo(() => {
    const date = getDateTimeFromWeekDayTime({
      year: selectedDate.year,
      week: selectedDate.week,
      day: selectedDate.day ?? 1,
      time: "00:00",
      timezone: timezone,
    });
    return {
      start: date.startOf("day").toJSDate(),
      end: date.endOf("day").toJSDate(),
    }
  }, [selectedDate]);

  const thisDay = getDateFromIsoCompleteDate(selectedDate, timezone);
  const nextDay = thisDay.plus({days: 1});
  const prevDay = thisDay.minus({days: 1});

  const handleStatusChange = (status: string) => {
    setSelectedStatus(status);
  };

  const requests = api.user.getStoreTimeOffRequestsAdvanced.useQuery({
    storeId,
    status: selectedStatus as TimeOffStatusFilter,
    dateRange: dateRange
  });

  return (
    <div className={cn("w-full")}>
      <Heading level={4} size={"xxs"}>
        Team Member Time Off Requests
      </Heading>
      <Text muted className={cn("block mb-4")}>View team members with time off requests during this timeframe</Text>

      {timeOffRequestStatus === "pending" || timeOffRequestStatus === "approved" ?
        <Link from={routeFullPath as any}
              to={`../../../../reports/availability` as any}
              search={{
                selectedDate: thisDay.toISODate(),
                toReqId: timeOffRequestId,
                timeOffFilter: ["approved", "pending"]
              } as any}
              className={cn("flex flex-row items-center gap-2 text-blue-600 hover:underline")}>
          View on Availability Report
          <Badge colorScheme={"secondary"}>NEW</Badge>
          <ArrowRightIcon size={16}/>
        </Link> : null}

      <div className={cn("flex flex-col gap-2 my-5")}>
        <div className={cn("flex flex-row items-center gap-3")}>
          <div className={cn("w-auto")}>
            <Select onValueChange={handleStatusChange} value={selectedStatus}>
              <SelectTrigger>
                <SelectValue placeholder="Select status"/>
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All</SelectItem>
                <SelectItem value="pending">Pending</SelectItem>
                <SelectItem value="approved">Approved</SelectItem>
                <SelectItem value="declined">Declined</SelectItem>
                <SelectItem value="cancelled">Cancelled</SelectItem>
                <SelectItem value="expired">Expired</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <Text className={cn("text-sm text-gray-500")}>
            Count: {requests.data?.items.length || 0}
          </Text>
        </div>

        <div className={cn("flex flex-row")}>
          <div className={cn("flex flex-row items-center justify-between gap-3")}>
            <IsoDayPicker value={selectedDate} onChange={setSelectedDate} today={today}/>
            <div className={cn("inline-flex rounded-md")} role="group">
              <Button title={"Go to the previous month"}
                      variant={"outline"} className={cn("rounded-r-none relative hover:z-10")}
                      onClick={() => setSelectedDate(dateTimeToIsoWeekDate(prevDay))}>
                <ArrowLeftIcon size={16} className={cn("mr-1")}/>
                Prev
              </Button>
              <Button title={"Go to the next month"}
                      variant={"outline"} className={cn("rounded-l-none border-l-0 relative hover:z-10")}
                      onClick={() => setSelectedDate(dateTimeToIsoWeekDate(nextDay))}>
                Next
                <ArrowRightIcon size={16} className={cn("ml-1")}/>
              </Button>
            </div>
          </div>
        </div>
      </div>

      <div className={cn("flex flex-col gap-2 max-w-[400px]")}>
        {requests.data?.items.length === 0
          ? <Text className={cn("text-center m-6")} size={"sm"} muted>No requests found for this day</Text>
          : map(requests.data?.items, req => {
            const isSameDay = formatInTimeZone(req.range.start, timezone, 'yyyy-MM-dd') === formatInTimeZone(req.range.end, timezone, 'yyyy-MM-dd');

            const isAllDay = isTimeAllDay({
              start: formatInTimeZone(req.range.start, timezone, 'HH:mm'),
              end: formatInTimeZone(req.range.end, timezone, 'HH:mm')
            });

            const isThisYear = formatInTimeZone(DateTime.now().toJSDate(), timezone, 'yyyy') === formatInTimeZone(req.range.end, timezone, 'yyyy');
            const isSameYear = formatInTimeZone(req.range.start, timezone, 'yyyy') === formatInTimeZone(req.range.end, timezone, 'yyyy');
            const dateFormat = `EEE MM/d${isSameYear && isThisYear ? '' : ', yyyy'}`
            const timeFormat = 'h:mmaaa'

            const startDate = formatInTimeZone(req.range.start, timezone, dateFormat);
            const endDate = formatInTimeZone(req.range.end, timezone, dateFormat);
            const startTime = formatInTimeZone(req.range.start, timezone, timeFormat);
            const endTime = formatInTimeZone(req.range.end, timezone, timeFormat);

            const dateRange =
              isSameDay
                ? `${startDate}`
                : `${startDate} - ${endDate}`
            const timeRange = isAllDay
              ? undefined
              : `${startTime} - ${endTime}`

            return <Link from={routeFullPath as any}
                         to={"../" + req.id} search={requestsSearchParams}
                         className={cn("flex flex-col gap-2")} key={req.id}>
              <div
                className={cn("flex flex-row items-center justify-between gap-2 border border-gray-200 rounded-lg px-3 py-2")}>
                <div className={cn("flex flex-col gap-2")}>
                  <Text>{req.person.firstName} {req.person.lastName}</Text>
                  <Text size={"sm"}>
                    {dateRange}
                  </Text>
                  {timeRange ? <div className={cn("items-start flex flex-row gap-1")}>
                    <Text size={"sm"} muted className={cn("border border-gray-200 rounded-md px-1")}>
                      {timeRange}
                    </Text>
                    <Text size={"sm"} muted className={cn("border border-gray-200 rounded-md px-1")}>
                      <DurationDaysText className={cn("text-gray-500")}
                                        durationHours={getDateRangeDurationHours(req.range)}/>
                    </Text>
                  </div> : null}
                </div>
                <div className={cn("flex flex-row items-center gap-5")}>
                  <PersonTimeOffRequestStatusBadge status={req.status} size={"sm"}/>
                  <ChevronRightIcon className={cn("h-4 w-4")}/>
                </div>
              </div>
            </Link>
          })}
      </div>
    </div>
  );
};
