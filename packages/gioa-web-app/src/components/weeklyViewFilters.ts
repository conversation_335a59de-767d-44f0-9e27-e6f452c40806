import {WeeklyViewFilter} from "@/src/components/WeeklyViewControlBar.types.ts";
import {every, filter, includes, isEmpty} from "lodash";
import {getPersonLaborStatus} from "../../../api/src/personMetrics.ts";
import {SchedulePersonDto} from "../../../api/src/schedulePersonDto.ts";
import {WeeklyTableShift} from "@/src/components/WeeklyTableTypes.ts";

export function filterPerson({filter, person, personShifts}: {
  filter: WeeklyViewFilter;
  person: SchedulePersonDto;
  personShifts: WeeklyTableShift[];
}): boolean {
  switch (filter.type) {
    case "isScheduled":
      return filter.value === "true" ? !isEmpty(personShifts) : isEmpty(personShifts);
    case "numDaysScheduled":
      const daysScheduled = new Set<number>();
      for (const shift of personShifts) {
        daysScheduled.add(shift.isoWeek.day);
      }
      return daysScheduled.size === parseInt(filter.value);
    case "area":
      return true; // area filters apply to shifts, not people
    case "laborStatus":
      const personLaborStatus = getPersonLaborStatus(person.age);
      return includes(filter.value, personLaborStatus);
    default:
      return false;
  }
}

export function filterWeeklyViewPeople<T extends SchedulePersonDto>({filters, people, personToShifts}: {
  filters: WeeklyViewFilter[];
  people: T[];
  personToShifts: { [personId: string]: WeeklyTableShift[] };
}): T[] {
  return filter(people, person => {
    return every(filters, f => filterPerson({filter: f, person, personShifts: personToShifts[person.id] ?? []}));
  });
}

export function filterShift({filter, shift}: {
  filter: WeeklyViewFilter;
  shift: WeeklyTableShift;
}): boolean {
  switch (filter.type) {
    case "area":
      return includes(filter.value, shift.storeAreaId);
    case "laborStatus":
      return true; // labor status filters apply to people, not shifts
    case "numDaysScheduled":
      return true; // num days scheduled filters apply to people, not shifts
    case "isScheduled":
      return true; // is scheduled filters apply to people, not shifts
    default:
      return false;
  }
}

export function filterWeeklyViewShifts({filters, shifts}: {
  filters: WeeklyViewFilter[];
  shifts: WeeklyTableShift[];
}): WeeklyTableShift[] {
  return filter(shifts, shift => {
    return every(filters, f => filterShift({filter: f, shift}));
  });
}
