import * as React from "react"
import {addDays, format, getISOWeek, getISOWeekYear, isWithinInterval, subDays} from "date-fns"
import {Calendar as CalendarIcon} from "lucide-react"
import {DateRange, SelectSingleEventHandler} from "react-day-picker"
import {<PERSON><PERSON>} from "@/src/components/ui/button"
import {Calendar} from "@/src/components/ui/calendar"
import {Popover, PopoverContent, PopoverTrigger,} from "@/src/components/ui/popover"
import {cn} from "@/src/util.ts";
import {IsoWeekDate} from "../../../api/src/timeSchemas.ts";
import {convertLocalTimeToTimezone, getDateFromWeekDayTime} from "../../../api/src/date.util.ts";
import {DateTime} from "luxon";

function isDateInRange(date: Date, selectedWeek: DateRange, {excludeEnds} = {excludeEnds: false}) {
  const start = selectedWeek.from;
  const end = selectedWeek.to;

  if (!start || !end) {
    return false;
  }

  if (excludeEnds) {
    return isWithinInterval(date, {
      start: addDays(start, 1),
      end: subDays(end, 1)
    });
  } else {
    return isWithinInterval(date, {start, end});
  }
}

export interface WeekPickerProps extends Omit<React.HTMLAttributes<HTMLDivElement>, "value" | "onChange"> {
  value: IsoWeekDate;
  onChange: (week: IsoWeekDate) => void;
  today: DateTime;
  timezone: string | null;
}

export function WeekPicker({className, value, onChange, today, timezone}: WeekPickerProps) {

  const onSelect: SelectSingleEventHandler = (day) => {
    if (day) {
      // the date picked from the calendar is in local time. We want it in the store's timezone
      const storeDay = convertLocalTimeToTimezone(day, timezone);

      onChange({
        week: getISOWeek(storeDay),
        year: getISOWeekYear(storeDay)
      });
    }
  }

  const selectedWeek = value ? {
    from: getDateFromWeekDayTime({
      year: value.year,
      week: value.week,
      day: 1,
      time: "00:00",
      timezone: "local"
    }),
    to: getDateFromWeekDayTime({
      year: value.year,
      week: value.week,
      day: 7,
      time: "23:59",
      timezone: "local"
    }),
  } : undefined;

  const onGoToCurrentWeek = () => {
    onChange({
      week: today.weekNumber,
      year: today.weekYear
    });
  }

  const onGoToNextWeek = () => {
    const nextWeek = today.plus({weeks: 1});
    onChange({
      week: nextWeek.weekNumber,
      year: nextWeek.weekYear
    });
  }

  return (
    <div className={cn("grid gap-2", className)}>
      <Popover>
        <PopoverTrigger asChild>
          <Button
            id="date"
            variant={"outline"}
            className={cn(
              "justify-start text-left font-normal",
              !value && "text-muted-foreground"
            )}
          >
            <CalendarIcon className="mr-2 h-4 w-4"/>
            {selectedWeek?.from ? (
              selectedWeek.to ? (
                <>
                  {format(selectedWeek.from, "LLL dd, y")} -{" "}
                  {format(selectedWeek.to, "LLL dd, y")}
                </>
              ) : (
                format(selectedWeek.from, "LLL dd, y")
              )
            ) : (
              <span>Pick a date</span>
            )}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-auto p-0" align="start">
          <div className={"flex gap-2 justify-start py-3 px-3 border-b items-center"}>
            Go to:
            <Button onClick={onGoToCurrentWeek}
                    variant={"secondary"} size={"sm"}>
              Current Week
            </Button>
            <Button onClick={onGoToNextWeek}
                    variant={"secondary"} size={"sm"}>
              Next Week
            </Button>
          </div>
          <Calendar
            initialFocus
            showOutsideDays ISOWeek={true}
            modifiers={{
              selected: selectedWeek ?? false,
              range_start: selectedWeek?.from ?? false,
              range_end: selectedWeek?.to ?? false,
              range_middle: (date: Date) =>
                selectedWeek
                  ? isDateInRange(date, selectedWeek, {excludeEnds: true})
                  : false
            }}
            mode="single"
            defaultMonth={selectedWeek?.from}
            onSelect={onSelect}
            numberOfMonths={2}
          />
        </PopoverContent>
      </Popover>
    </div>
  )
}
