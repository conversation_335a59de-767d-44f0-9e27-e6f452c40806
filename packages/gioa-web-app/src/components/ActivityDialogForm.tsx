import React, {useEffect} from 'react';
import {FormDailyTimeRange} from "@/src/components/form/FormDailyTimeRange.tsx";
import {FormControl} from "@/src/components/form/FormControl.tsx";
import {Label} from "@/src/components/ui/label.tsx";
import {FieldInfo} from "@/src/components/form/FieldInfo.tsx";
import {FormTextarea} from "@/src/components/form/FormTextarea.tsx";
import {Checkbox} from "@/src/components/ui/checkbox.tsx";
import {Text} from "@/src/components/Text.tsx";
import {DialogFooter} from "@/src/components/ui/dialog.tsx";
import {Button} from "@/src/components/ui/button.tsx";
import {incrementTo24HourTime, ScheduleRowInfo} from "../../../api/src/scheduleBuilder.util.ts";
import {useForm} from '@tanstack/react-form';
import {zodValidator} from "@tanstack/zod-form-adapter";
import {z} from "zod";
import {RangerVals, ShiftActivityRanger} from "@/src/components/ShiftActivityRanger.tsx";
import {DailyTimeRange} from "../../../api/src/timeSchemas.ts";
import {minutesTo24HourTime, timeToMinutes} from "../../../api/src/date.util.ts";
import {activityToLaborCounting, ShiftActivityTypeChooser} from "@/src/components/ShiftActivityTypeChooser.tsx";
import {ShiftActivityType} from "../../../api/src/scheduleSchemas.ts";
import {ShiftActivityTypeForm} from "@/src/components/ShiftActivityTypeForm.tsx";

export interface ActivityDialogFormValues {
  start: string,
  end: string,
  title: string,
  description: string,
  countsTowardsLabor: boolean,
  activityType: ShiftActivityType | undefined;
  setupPositionTitle: string | undefined;
  payStatus: "paid" | "unpaid" | undefined;
}

export interface ActivityDialogFormProps {
  defaultValues: ActivityDialogFormValues;
  newValues?: ActivityDialogFormValues;
  onSubmit: (values: ActivityDialogFormValues) => void;
  onCancel: () => void;
  storeHours: DailyTimeRange,
  shift: ScheduleRowInfo;
  storeId: string;
}

export const ActivityDialogForm: React.FC<ActivityDialogFormProps> = ({
                                                                        storeId,
                                                                        defaultValues,
                                                                        newValues,
                                                                        onSubmit,
                                                                        onCancel,
                                                                        storeHours,
                                                                        shift
                                                                      }) => {
  const form = useForm({
    defaultValues: defaultValues,
    validatorAdapter: zodValidator(),
    onSubmit: async ({value}) => {
      onSubmit(value)
    },
  })

  useEffect(() => {
    if (newValues) {
      form.setFieldValue("start", newValues.start ?? "");
      form.setFieldValue("end", newValues.end ?? "");
      form.setFieldValue("title", newValues.title ?? "");
      form.setFieldValue("description", newValues.description ?? "");
      form.setFieldValue("countsTowardsLabor", newValues.countsTowardsLabor ?? false);
      form.setFieldValue("activityType", newValues.activityType);
      form.setFieldValue("setupPositionTitle", newValues.setupPositionTitle);
      form.setFieldValue("payStatus", newValues.payStatus);
    }
  }, [newValues])

  const formStart = form.useStore(s => s.values.start);
  const formEnd = form.useStore(s => s.values.end);

  const rangerVals: RangerVals = [timeToMinutes(formStart), timeToMinutes(formEnd)];
  const shiftStart24Hr = incrementTo24HourTime(storeHours, shift.start);
  const shiftEnd24Hr = incrementTo24HourTime(storeHours, shift.end);
  const shiftRange: RangerVals = [timeToMinutes(shiftStart24Hr), timeToMinutes(shiftEnd24Hr)];

  const setRangerVals = (vals: RangerVals) => {
    form.setFieldValue("start", minutesTo24HourTime(vals[0]));
    form.setFieldValue("end", minutesTo24HourTime(vals[1]));
  }

  const activityType = form.useStore(s => s.values.activityType);
  const onActivityTypeChange = (activityType: ShiftActivityType | null) => {
    if (activityType === "admin") {
      form.setFieldValue("title", "Admin/Non-Ops");
    } else if (activityType === "breaks") {
      form.setFieldValue("title", "Break");
    } else {
      form.setFieldValue("title", "");
    }
  }

  return <form onSubmit={(e) => {
    e.preventDefault()
    e.stopPropagation()
    form.handleSubmit()
  }}>
    <div className="pb-6">
      <hr className={"pt-4"}/>
      <Label className={"mb-3 block"}>Activity Type</Label>

      <form.Field name={`activityType`}
                  validators={{
                    onSubmit: z.string("Choose an activity type").min(1, "Choose an activity type")
                  }}
                  children={field => <ShiftActivityTypeChooser onChange={onActivityTypeChange}
                                                               field={field}/>}>
      </form.Field>

      <ShiftActivityTypeForm form={form} storeId={storeId}/>

      <hr className={"my-4"}/>

      <ShiftActivityRanger storeHours={storeHours}
                           value={rangerVals}
                           onChange={setRangerVals}
                           shiftRange={shiftRange}/>

      <FormDailyTimeRange form={form}
                          min={shiftStart24Hr}
                          max={shiftEnd24Hr}/>

      <form.Field name={`description`}
                  children={(field) => {
                    return <FormControl>
                      <Label htmlFor={field.name}>Description (optional)</Label>
                      <FormTextarea field={field}
                                    placeholder="Enter description..."/>
                      <FieldInfo field={field}/>
                    </FormControl>;
                  }}/>

      {activityType && activityToLaborCounting[activityType] === "configurable" ?
              <form.Field name={"countsTowardsLabor"}
                          validators={{
                            onSubmit: z.boolean()
                          }}
                          children={(field) => {
                            return <div
                                    className={"flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4 bg-white mb-3"}>
                              <Checkbox id={field.name}
                                        onCheckedChange={isChecked => typeof isChecked === "boolean" ? field.handleChange(isChecked) : null}
                                        checked={field.state.value}>
                              </Checkbox>
                              <div className="space-y-1 leading-none">
                                <Label htmlFor={field.name} className={"block"}>
                                  Counts Towards Labor
                                </Label>
                                <Text size={"sm"} className={"block"}>
                                  Select if this activity counts towards labor hours
                                </Text>
                              </div>
                              <FieldInfo field={field}/>
                            </div>
                          }}/> : null}
    </div>

    <DialogFooter>
      <Button variant={"outline"} type={"button"}
              onClick={onCancel}>
        Cancel
      </Button>
      <Button type={"submit"}>
        Save
      </Button>
    </DialogFooter>
  </form>
}
