import React from 'react';
import {Document, Page, PDFViewer, StyleSheet, Text, View} from '@react-pdf/renderer';
import {filter, find, flatMap, map, sortBy} from 'lodash';
import {ScheduleDto} from "../../../api/src/scheduleSchemas.ts";
import {getDatesInRange, getIsoWeekDateTimeRangeInTimezone, to12HourTime} from "../../../api/src/date.util.ts";
import {SchedulePersonDto} from '@gioa/api/src/schedulePersonDto.ts';
import {Table, TD, TR} from '@ag-media/react-pdf-table';
import {DateTime} from "luxon";

const styles = StyleSheet.create({
  page: {
    padding: 30,
    fontSize: 8,
  },
  header: {
    marginBottom: 10,
    fontSize: 12,
    color: '#374151',
  },
  dayContainer: {},
  dayHeader: {
    fontSize: 9,
    fontWeight: 'bold',
    marginBottom: 3,
    backgroundColor: '#F3F4F6',
    padding: 3,
  },
  cell: {
    color: "#545454",
    borderCollapse: 'collapse',
    paddingVertical: 1,
    paddingHorizontal: 3,
    borderColor: '#e0e0e0',
  },
  headerRow: {
    backgroundColor: '#FFFFFF',
    fontWeight: 'bold',
  },
  shiftLead: {
    backgroundColor: "#b4b4b4", paddingVertical: 1, paddingHorizontal: 3, borderRadius: 3, fontSize: 7
  }
});

const DaySchedule: React.FC<{
  schedule: ScheduleDto;
  people: SchedulePersonDto[];
  dayOfWeek: number;
  date: DateTime;
  showHeader: boolean;
  storeTitle: string;
}> = ({schedule, people, dayOfWeek, date, showHeader, storeTitle}) => {
  const day = find(schedule.published!.days, d => d.dayOfWeek === dayOfWeek);
  if (!day) return null;

  const shifts = flatMap(day.areas, area => {
    return map(area.shifts, shift => ({
      ...shift,
      areaTitle: area.title,
      person: find(people, p => p.id === shift.assignedPersonId)
    }));
  });

  const sortedShifts = sortBy(shifts, shift => shift.range.start);

  return (
    <View>
      {showHeader && (
        <Text style={styles.header}>
          {storeTitle} - Week {schedule.published?.week.week}
        </Text>
      )}

      <Text style={styles.dayHeader}>
        {date.toFormat('EEEE, MMM d')}
      </Text>

      <Table>
        {sortedShifts.length === 0 ? (
          <TR>
            <TD style={{...styles.cell}}>
              <Text>No shifts scheduled</Text>
            </TD>
          </TR>
        ) : (
          map(sortedShifts, (shift, index) => (
            <TR key={index} wrap={false}>
              <TD style={{...styles.cell, flexDirection: "row", gap: 3}}>
                <Text>{shift.person?.firstName} {shift.person?.lastName}</Text>
                {shift.isShiftLead ? <View style={{...styles.shiftLead}}>
                  <Text style={{color: "white"}}>L</Text>
                </View> : null}
              </TD>
              <TD style={{...styles.cell}}>
                <Text>{to12HourTime(shift.range.start)} - {to12HourTime(shift.range.end)}</Text>
              </TD>
              <TD style={{...styles.cell}}>
                <Text>{shift.areaTitle}</Text>
              </TD>
            </TR>
          ))
        )}
      </Table>
    </View>
  );
};

export interface PDFScheduleProps {
  storeTitle: string;
  schedule: ScheduleDto;
  people: SchedulePersonDto[];
}

export const PDFSchedule: React.FC<PDFScheduleProps> = ({
                                                          storeTitle,
                                                          schedule,
                                                          people,
                                                        }) => {
  const {start, end} = getIsoWeekDateTimeRangeInTimezone({
    week: schedule.published!.week,
    timezone: schedule.timezone ?? null
  });

  const weekDates = filter(
    getDatesInRange(start, end),
    d => d.weekday !== 7
  );

  return (
    <PDFViewer style={{width: '100%', height: '100vh'}}>
      <Document title={`${storeTitle} - Week ${schedule.published?.week.week}`}>
        {map(weekDates, (date, index) => (
          <Page key={date.toFormat('yyyy-MM-dd')} size="LETTER" style={styles.page} wrap>
            <DaySchedule
              schedule={schedule}
              people={people}
              dayOfWeek={date.weekday}
              date={date}
              showHeader={index === 0}
              storeTitle={storeTitle}
            />
          </Page>
        ))}
      </Document>
    </PDFViewer>
  );
};
