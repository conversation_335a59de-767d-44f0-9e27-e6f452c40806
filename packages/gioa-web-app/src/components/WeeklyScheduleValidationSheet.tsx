import React, {useMemo} from 'react';
import {Sheet, SheetContent, SheetDescription, Sheet<PERSON>eader, SheetTitle} from "@/src/components/ui/sheet.tsx";
import {Button} from "@/src/components/ui/button.tsx";
import {chain} from "lodash";
import {ScheduleValidationResult, ValidationMessage} from "../../../api/src/scheduleValidation.types.ts";
import {ScheduleDto} from "../../../api/src/scheduleSchemas.ts";
import {DailyTimeRange, IsoWeekDate} from '@gioa/api/src/timeSchemas.ts';
import {ScheduleIssuesList} from "@/src/components/ScheduleIssuesList.tsx";
import {SchedulePersonDto} from "../../../api/src/schedulePersonDto.ts";
import {sortBySeverity} from "../../../api/src/scheduleValidation/scheduleValidationMessagesL1.ts";
import {getFriendlyIsoWeekString} from "@/src/schedule/schedule.ts";

export interface WeeklyScheduleValidationSheetProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  storeId: string;
  onGoToMessage: (week: IsoWeekDate, dayOfWeek: number, msg: ValidationMessage) => void;
  onIgnoreMessage: (msg: ValidationMessage) => void;
  onUnignoreMessage: (msg: ValidationMessage) => void;
  validationResult?: ScheduleValidationResult;
  scheduleRev: ScheduleDto;
  people: SchedulePersonDto[];
  storeHours: DailyTimeRange;
}

export const WeeklyScheduleValidationSheet: React.FC<WeeklyScheduleValidationSheetProps> = ({
                                                                                              isOpen,
                                                                                              onOpenChange,
                                                                                              storeId,
                                                                                              scheduleRev,
                                                                                              people,
                                                                                              onIgnoreMessage,
                                                                                              storeHours,
                                                                                              onGoToMessage,
                                                                                              onUnignoreMessage,
                                                                                              validationResult,
                                                                                            }) => {

  const warnings = useMemo(() =>
      chain(validationResult?.messages ?? [])
        .sortBy(sortBySeverity)
        .value(),
    [validationResult]);

  return (
    <Sheet modal={false} open={isOpen}
           onOpenChange={onOpenChange}>
      <SheetContent side="right"
                    className="flex flex-col gap-4 max-h-full overflow-auto px-0">
        <SheetHeader className={"mx-4"}>
          <SheetTitle>
            Issues
          </SheetTitle>
          <SheetDescription>
            Identify and resolve issues for {getFriendlyIsoWeekString(scheduleRev.draft.week)}
          </SheetDescription>
        </SheetHeader>

        <ScheduleIssuesList warnings={warnings} schedule={scheduleRev.draft}
                            onIgnoreMessage={onIgnoreMessage} onUnignoreMessage={onUnignoreMessage}
                            people={people} storeHours={scheduleRev.draft.storeHours}
                            onGoToMessage={onGoToMessage}/>

        <div className={"px-4 w-full"}>
          <Button variant={"outline"} type={"button"}
                  onClick={() => onOpenChange(false)}>
            Close
          </Button>
        </div>
      </SheetContent>
    </Sheet>
  );
}

