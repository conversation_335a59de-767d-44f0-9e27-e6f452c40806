import React from "react";
import {useForm} from "@tanstack/react-form";
import {api} from "@/src/api.ts";
import {Text} from "@/src/components/Text.tsx";
import {FormControl} from "@/src/components/form/FormControl.tsx";
import {Label} from "@/src/components/ui/label.tsx";
import {FormInput} from "@/src/components/form/FormInput.tsx";
import {FieldInfo} from "@/src/components/form/FieldInfo.tsx";
import FormMonthDayPicker from "@/src/components/form/FormMonthDayPicker.tsx";

interface LaborLawsFormProps {
  storeId: string;
  onContinue: () => void;
  setIsLoading: (isLoading: boolean) => void;
}

export const LaborLawsForm = React.forwardRef<{ submit: () => Promise<void> }, LaborLawsFormProps>(
  ({storeId, onContinue, setIsLoading}, ref) => {
    const [defaultChildLaborLaws] = api.user.getStoreChildLaborLaws.useSuspenseQuery({storeId});
    const updateStoreChildLaborLaws = api.user.updateStoreChildLaborLaws.useMutation()

    const {
      ageGroup14To15,
      ageGroup16To17,
      summerDates
    } = defaultChildLaborLaws

    const form = useForm({
      defaultValues: {
        ageGroup14To15: {
          schoolDays: {
            hoursPerDay: ageGroup14To15.schoolDays.hoursPerDay?.toString() ?? '',
            hoursPerWeek: ageGroup14To15.schoolDays.hoursPerWeek?.toString() ?? '',
          },
          nonSchoolDays: {
            hoursPerDay: ageGroup14To15.nonSchoolDays.hoursPerDay?.toString() ?? '',
            hoursPerWeek: ageGroup14To15.nonSchoolDays.hoursPerWeek?.toString() ?? '',
          },
          workHours: {
            start: ageGroup14To15.workHours.start ?? '',
            end: ageGroup14To15.workHours.end ?? '',
            summerStart: ageGroup14To15.workHours.summerStart ?? '',
            summerEnd: ageGroup14To15.workHours.summerEnd ?? '',
          },
        },
        ageGroup16To17: {
          schoolDays: {
            hoursPerDay: ageGroup16To17.schoolDays.hoursPerDay?.toString() ?? '',
            hoursPerWeek: ageGroup16To17.schoolDays.hoursPerWeek?.toString() ?? '',
          },
          nonSchoolDays: {
            hoursPerDay: ageGroup16To17.nonSchoolDays.hoursPerDay?.toString() ?? '',
            hoursPerWeek: ageGroup16To17.nonSchoolDays.hoursPerWeek?.toString() ?? '',
          },
        },
        summerDates: {
          start: summerDates.start ?? "06-01",
          end: summerDates.end ?? "09-01",
        },
      },
      // Going this route because it became much too complicated to validate start and end times, and then clearing
      // the validation errors after each change
      validators: {
        onChange: ({value}) => {
          let errors: string[] = []

          const validateNumber = (value: string, min: number, fieldName: string) => {
            const num = Number(value);
            if (value && (isNaN(num) || num < min)) {
              errors.push(`${fieldName}: must be at least ${min}`);
            }
          };

          const validateTimeRange = (start: string, end: string, fieldName: string) => {
            if ((!start && end) || (start && !end)) {
              errors.push(`${fieldName}: both start and end times must be provided`);
            } else if (start && end && start >= end) {
              errors.push(`${fieldName}: start time must be prior to end time`);
            }
          };

          validateNumber(value.ageGroup14To15.schoolDays.hoursPerDay, 0, 'Age 14-15 School Days Hours Per Day');
          validateNumber(value.ageGroup14To15.schoolDays.hoursPerWeek, 0, 'Age 14-15 School Days Hours Per Week');
          validateNumber(value.ageGroup14To15.nonSchoolDays.hoursPerDay, 0, 'Age 14-15 Non-School Days Hours Per Day');
          validateNumber(value.ageGroup14To15.nonSchoolDays.hoursPerWeek, 0, 'Age 14-15 Non-School Days Hours Per Week');

          validateNumber(value.ageGroup16To17.schoolDays.hoursPerDay, 1, 'Age 16-17 School Days Hours Per Day');
          validateNumber(value.ageGroup16To17.schoolDays.hoursPerWeek, 1, 'Age 16-17 School Days Hours Per Week');
          validateNumber(value.ageGroup16To17.nonSchoolDays.hoursPerDay, 1, 'Age 16-17 Non-School Days Hours Per Day');
          validateNumber(value.ageGroup16To17.nonSchoolDays.hoursPerWeek, 1, 'Age 16-17 Non-School Days Hours Per Week');

          validateTimeRange(value.ageGroup14To15.workHours.start, value.ageGroup14To15.workHours.end, 'Age 14-15 Work Hours');
          validateTimeRange(value.ageGroup14To15.workHours.summerStart, value.ageGroup14To15.workHours.summerEnd, 'Age 14-15 Summer Work Hours');

          if (value.summerDates.start && value.summerDates.end) {
            if (value.summerDates.start >= value.summerDates.end) {
              errors.push("Summer start date must be before end date");
            }
          }

          return errors.join(";");
        }
      },
      onSubmit: async ({value}) => {
        setIsLoading(true);
        await updateStoreChildLaborLaws.mutateAsync({
          storeId: storeId,
          childLaborLaws: {
            ageGroup14To15: {
              schoolDays: {
                hoursPerDay: value.ageGroup14To15.schoolDays.hoursPerDay?.trim() ? parseInt(value.ageGroup14To15.schoolDays.hoursPerDay) : undefined,
                hoursPerWeek: value.ageGroup14To15.schoolDays.hoursPerWeek?.trim() ? parseInt(value.ageGroup14To15.schoolDays.hoursPerWeek) : undefined,
              },
              nonSchoolDays: {
                hoursPerDay: value.ageGroup14To15.nonSchoolDays.hoursPerDay?.trim() ? parseInt(value.ageGroup14To15.nonSchoolDays.hoursPerDay) : undefined,
                hoursPerWeek: value.ageGroup14To15.nonSchoolDays.hoursPerWeek?.trim() ? parseInt(value.ageGroup14To15.nonSchoolDays.hoursPerWeek) : undefined,
              },
              workHours: {
                start: value.ageGroup14To15.workHours.start ?? undefined,
                end: value.ageGroup14To15.workHours.end ?? undefined,
                summerStart: value.ageGroup14To15.workHours.summerStart ?? undefined,
                summerEnd: value.ageGroup14To15.workHours.summerEnd ?? undefined,
              },
            },
            ageGroup16To17: {
              schoolDays: {
                hoursPerDay: value.ageGroup16To17.schoolDays.hoursPerDay?.trim() ? parseInt(value.ageGroup16To17.schoolDays.hoursPerDay) : undefined,
                hoursPerWeek: value.ageGroup16To17.schoolDays.hoursPerWeek?.trim() ? parseInt(value.ageGroup16To17.schoolDays.hoursPerWeek) : undefined,
              },
              nonSchoolDays: {
                hoursPerDay: value.ageGroup16To17.nonSchoolDays.hoursPerDay?.trim() ? parseInt(value.ageGroup16To17.nonSchoolDays.hoursPerDay) : undefined,
                hoursPerWeek: value.ageGroup16To17.nonSchoolDays.hoursPerWeek?.trim() ? parseInt(value.ageGroup16To17.nonSchoolDays.hoursPerWeek) : undefined,
              },
            },
            summerDates: {
              start: value.summerDates.start ?? undefined,
              end: value.summerDates.end ?? undefined,
            },
          }
        });
        setIsLoading(false);
        onContinue();
      },
    })

    React.useImperativeHandle(ref, () => ({
      submit: form.handleSubmit,
    }));

    const formErrorMap = form.useStore((state) => state.errorMap)

    return (
      <div>
        <div className={"flex flex-col gap-3"}>
          <div className="flex flex-col gap-3 border-2 border-gray-100 p-3 shadow-md rounded-md items-start">
            <Text size="xl" bold className="mb-3">Age 14 & 15</Text>
            <div className="mb-4">
              <div className={"flex flex-row gap-3 items-center"}>
                <div className={"w-40"}>
                  <Text>School Days</Text>
                </div>
                <form.Field name={`ageGroup14To15.schoolDays.hoursPerDay`}
                            children={(field) => {
                              return <FormControl className={"flex-1"}>
                                <Label htmlFor={field.name}>Hours Per Day</Label>
                                <FormInput field={field} type={"number"}
                                           placeholder={"0 Hours"}/>
                                <FieldInfo field={field}/>
                              </FormControl>;
                            }}/>
                <form.Field name={`ageGroup14To15.schoolDays.hoursPerWeek`}
                            children={(field) => {
                              return <FormControl className={"flex-1"}>
                                <Label htmlFor={field.name}>Hours Per Week</Label>
                                <FormInput field={field} type={"number"}
                                           placeholder={"0 Hours"}/>
                                <FieldInfo field={field}/>
                              </FormControl>;
                            }}/>
              </div>
              <div className={"flex flex-row gap-3 items-center"}>
                <div className={"w-40"}>
                  <Text>Non-School Days</Text>
                </div>
                <form.Field name={`ageGroup14To15.nonSchoolDays.hoursPerDay`}
                            children={(field) => {
                              return <FormControl className={"flex-1"}>
                                <Label htmlFor={field.name}>Hours Per Day</Label>
                                <FormInput field={field} type={"number"}
                                           placeholder={"0 Hours"}/>
                                <FieldInfo field={field}/>
                              </FormControl>;
                            }}/>
                <form.Field name={`ageGroup14To15.nonSchoolDays.hoursPerWeek`}
                            children={(field) => {
                              return <FormControl className={"flex-1"}>
                                <Label htmlFor={field.name}>Hours Per Week</Label>
                                <FormInput field={field} type={"number"}
                                           placeholder={"0 Hours"}/>
                                <FieldInfo field={field}/>
                              </FormControl>;
                            }}/>
              </div>
              <hr className={"my-3"}/>
              <div className={"flex flex-row gap-3 items-center"}>
                <div className={"w-40"}>
                  <Text>School Season Work Hours</Text>
                </div>
                <form.Field
                  name={`ageGroup14To15.workHours.start`}
                  children={(field) => {
                    return <FormControl className={"flex-1"}>
                      <Label htmlFor={field.name}>Shift Start</Label>
                      <FormInput
                        field={field}
                        type="time"
                      />
                      <FieldInfo field={field}/>
                    </FormControl>
                  }}/>
                <form.Field
                  name={`ageGroup14To15.workHours.end`}
                  children={(field) => {
                    return <FormControl className={"flex-1"}>
                      <Label htmlFor={field.name}>Shift End</Label>
                      <FormInput
                        field={field}
                        type="time"
                      />
                      <FieldInfo field={field}/>
                    </FormControl>
                  }}/>
              </div>
              <div className={"flex flex-row gap-3 items-center"}>
                <div className={"w-40"}>
                  <Text>Summer Work Hours</Text>
                </div>
                <form.Field
                  name={`ageGroup14To15.workHours.summerStart`}
                  children={(field) => {
                    return <FormControl className={"flex-1"}>
                      <Label htmlFor={field.name}>Shift Start</Label>
                      <FormInput
                        field={field}
                        type="time"
                      />
                      <FieldInfo field={field}/>
                    </FormControl>
                  }}/>
                <form.Field
                  name={`ageGroup14To15.workHours.summerEnd`}
                  children={(field) => {
                    return <FormControl className={"flex-1"}>
                      <Label htmlFor={field.name}>Shift End</Label>
                      <FormInput
                        field={field}
                        type="time"
                      />
                      <FieldInfo field={field}/>
                    </FormControl>
                  }}/>
              </div>
              <hr className={"my-3"}/>
              <div className={"flex flex-row gap-3 items-center"}>
                <div className={"w-40"}>
                  <Text>Summer Dates</Text>
                </div>
                <form.Field
                  name={`summerDates.start`}
                  children={(field) => {
                    return <FormControl className={"flex-1"}>
                      <Label htmlFor={field.name}>Summer Start</Label>
                      <FormMonthDayPicker field={field}/>
                    </FormControl>
                  }}/>
                <form.Field
                  name={`summerDates.end`}
                  children={(field) => {
                    return <FormControl className={"flex-1"}>
                      <Label htmlFor={field.name}>Summer End</Label>
                      <FormMonthDayPicker field={field}/>
                    </FormControl>
                  }}/>
              </div>
            </div>
          </div>
          <div className="flex flex-col gap-3 border-2 border-gray-100 p-3 shadow-md rounded-md items-start">
            <Text size="xl" bold className="mb-3">Age 16 & 17</Text>
            <div className="mb-4">
              <div className={"flex flex-row gap-3 items-center"}>
                <div className={"w-40"}>
                  <Text>School Days</Text>
                </div>
                <form.Field name={`ageGroup16To17.schoolDays.hoursPerDay`}
                            children={(field) => {
                              return <FormControl className={"flex-1"}>
                                <Label htmlFor={field.name}>Hours Per Day</Label>
                                <FormInput field={field} type={"number"}
                                           placeholder={"0 Hours"}/>
                                <FieldInfo field={field}/>
                              </FormControl>;
                            }}/>
                <form.Field name={`ageGroup16To17.schoolDays.hoursPerWeek`}
                            children={(field) => {
                              return <FormControl className={"flex-1"}>
                                <Label htmlFor={field.name}>Hours Per Week</Label>
                                <FormInput field={field} type={"number"}
                                           placeholder={"0 Hours"}/>
                                <FieldInfo field={field}/>
                              </FormControl>;
                            }}/>
              </div>
              <div className={"flex flex-row gap-3 items-center"}>
                <div className={"w-40"}>
                  <Text>Non-School Days</Text>
                </div>
                <form.Field name={`ageGroup16To17.nonSchoolDays.hoursPerDay`}
                            children={(field) => {
                              return <FormControl className={"flex-1"}>
                                <Label htmlFor={field.name}>Hours Per Day</Label>
                                <FormInput field={field} type={"number"}
                                           placeholder={"0 Hours"}/>
                                <FieldInfo field={field}/>
                              </FormControl>;
                            }}/>
                <form.Field name={`ageGroup16To17.nonSchoolDays.hoursPerWeek`}
                            children={(field) => {
                              return <FormControl className={"flex-1"}>
                                <Label htmlFor={field.name}>Hours Per Week</Label>
                                <FormInput field={field} type={"number"}
                                           placeholder={"0 Hours"}/>
                                <FieldInfo field={field}/>
                              </FormControl>;
                            }}/>
              </div>
            </div>
          </div>
        </div>
        <div>
          {formErrorMap.onChange && typeof formErrorMap.onChange === "string" ? (
            <div className={"flex flex-col items-center"}>
              <Text center colorScheme={"danger"} bold>There was an error on the form:</Text>
              {formErrorMap.onChange.split(";").map((x) => <Text center colorScheme={"danger"}>{x}</Text>)}
            </div>
          ) : null}
        </div>
      </div>
    )
  });
