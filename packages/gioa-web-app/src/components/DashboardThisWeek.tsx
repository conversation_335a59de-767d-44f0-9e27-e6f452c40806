import React, {use<PERSON>emo} from 'react';
import {Table, TableBody, TableCell, TableHead, TableHeader, TableRow} from '@/src/components/ui/table';
import {Text} from '@/src/components/Text';
import {ShiftLead} from "@/src/components/ShiftLead.tsx";
import {Badge} from "@/src/components/ui/badge.tsx";
import {api} from "@/src/api.ts";
import {Shift} from "../../../api/src/scheduleSchemas.ts";
import {DateTime} from "luxon";
import {filter, find, flatMap, map} from "lodash";
import {getIsoWeekDateTimeRangeInTimezone} from "../../../api/src/date.util.ts";
import {useNavigate} from '@tanstack/react-router';
import {SchedulePersonDto} from "../../../api/src/schedulePersonDto.ts";
import {formatCurrency, formatHours} from "../../../common/src/dataFormatters.ts";
import {fromSchedulePeople} from "../../../api/src/scheduling/metrics/payRates/payRates.ts";


import {calculateMetrics} from "../../../api/src/scheduling/metrics/metrics.util.ts";
import {getDay} from "../../../api/src/schedule.ts";

export interface DashboardWeekAheadProps {
  storeId: string;
  businessId: string;
  allPeopleAtStore: SchedulePersonDto[];
}

const DashboardThisWeek: React.FC<DashboardWeekAheadProps> = ({storeId, businessId, allPeopleAtStore}) => {
  const navigate = useNavigate();
  const [[store, {schedule, forecast}]] = api.useSuspenseQueries(t => {
    return [
      t.user.getStoreAdmin({
        storeId: storeId!
      }),
      t.user.getPublishedScheduleForThisWeek({
        storeId: storeId!
      }, {
        refetchInterval: 1000 * 60 * 15 // 15 minutes
      }),
    ]
  })
  const isForecastingEnabled = Boolean(store.permissions?.isForecastingEnabled);
  const payRates = useMemo(() => fromSchedulePeople(allPeopleAtStore), [allPeopleAtStore]);

  const {timezone} = store;
  const storeTimezone = timezone ?? "America/New_York";
  const [now] = React.useState(DateTime.now().setZone(storeTimezone));
  const currentWeek = {
    year: now.weekYear,
    week: now.weekNumber
  }

  const weatherForecast = api.user.getWeatherForecast.useQuery({zipCode: store.address?.zipCode ?? ""}, {
    enabled: Boolean(store.address?.zipCode)
  });

  const shiftOffers = api.user.getShiftOffers.useQuery({storeId, status: "pending", appVersion: "web"});

  const scheduleEvents = api.user.getScheduleEvents.useQuery({
    week: currentWeek,
    storeId: storeId
  }, {
    staleTime: 1000 * 60 * 15 // 15 minutes
  })

  const {start} = getIsoWeekDateTimeRangeInTimezone({
    week: currentWeek,
    timezone: timezone ?? null
  })

  const weekDates = Array.from({length: 7}, (_, i) => {
    return start.plus({days: i});
  });

  return (
    <div className="bg-white p-6 rounded-lg shadow">
      <div className="pb-4">
        <Text size="xl">This Week's Published Schedule</Text>
      </div>
      <div className={"w-full overflow-auto"}>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead></TableHead>
              <TableHead>Day</TableHead>
              <TableHead>Shifts</TableHead>
              <TableHead>Offers</TableHead>
              <TableHead>Events</TableHead>
              <TableHead>Proj. Revenue</TableHead>
              <TableHead>Shift Leads</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {map(weekDates, (startOfWeekDay, index) => {
              const weekday = startOfWeekDay.weekday;
              const scheduleDay = schedule ? getDay(schedule, weekday) : undefined;
              const endOfWeekDay = startOfWeekDay.plus({days: 1});
              const daysWeather = find(weatherForecast.data?.weather?.days, d => d.date === startOfWeekDay.toFormat("yyyy-MM-dd"));

              const daysShiftOffers = filter(shiftOffers.data?.items, o => {
                return o.shift!.startAbs >= startOfWeekDay.toJSDate()
                  && o.shift!.startAbs <= endOfWeekDay.toJSDate();
              });

              const isToday = startOfWeekDay.hasSame(now, "day");

              const currentDateEvents = filter(scheduleEvents.data, evt => {
                return (evt.range.start >= startOfWeekDay.toJSDate() && evt.range.start <= endOfWeekDay.toJSDate())
                  || (evt.range.end >= startOfWeekDay.toJSDate() && evt.range.end <= endOfWeekDay.toJSDate())
                  || (evt.range.start <= startOfWeekDay.toJSDate() && evt.range.end >= endOfWeekDay.toJSDate());
              });

              const metrics = calculateMetrics({
                isForecastingEnabled: isForecastingEnabled,
                payRates: payRates,
                schedule: schedule,
                weekday: weekday,
                forecast: forecast,
                metricsInput: scheduleDay?.metricsInput
              })

              const currentDateMetricsShiftLeaders = filter(flatMap(scheduleDay?.areas, a => a.shifts), {isShiftLead: true});
              const shiftLeadersShifts = map(currentDateMetricsShiftLeaders, (shift: Shift) => {
                return {
                  person: find(allPeopleAtStore, p => p.id === shift.assignedPersonId) ?? undefined,
                  range: shift.range
                }
              });

              const navigateToScheduleWeekday = () => {
                navigate({
                  to: `/${businessId}/${storeId}/schedules/${schedule?.id}`,
                  search: {dayOfWeek: weekday}
                })
              }
              const navigateToPendingOffers = () => {
                navigate({to: `/${businessId}/${storeId}/schedules/requests/shiftOffers`})
              }

              return <TableRow key={index} className={`cursor-pointer ${isToday ? "bg-blue-50" : ""}`}>
                <TableCell onClick={navigateToScheduleWeekday}>
                  {daysWeather
                    ? <div className="flex items-center flex-wrap">
                      <img src={daysWeather.weatherIcon} alt="Weather data by WeatherAPI.com" className={"w-12 h-12"}/>
                      <div className={"flex flex-col gap-0 items-center"}>
                        <Text size={"xs"} className={"whitespace-nowrap"}>{Math.round(daysWeather.highTempF)}°F
                          / {Math.round(daysWeather.lowTempF)}°F</Text>
                      </div>
                    </div>
                    : <div/>}
                </TableCell>
                <TableCell onClick={navigateToScheduleWeekday}>
                  <div className="ml-2 flex flex-col items-start">
                    <Text size="sm" semibold>{startOfWeekDay.toFormat("cccc")}</Text>
                    <Text size="sm" muted>{startOfWeekDay.toFormat('M/d')}</Text>
                  </div>
                </TableCell>
                <TableCell onClick={navigateToScheduleWeekday}>
                  <div className="ml-2 flex flex-col items-start">
                    <Text size="sm"
                          semibold>{metrics.assignedShiftCount} shift{metrics.assignedShiftCount !== 1 ? "s" : ""}</Text>
                    <Text size="sm" muted
                          className={"whitespace-nowrap"}>{formatHours(metrics?.totalLaborHours)} hrs</Text>
                  </div>
                </TableCell>
                <TableCell onClick={navigateToPendingOffers}>
                  {daysShiftOffers.length === 0
                    ? <Badge className={"bg-green-100 text-green-500"}>{daysShiftOffers.length}</Badge>
                    : <Badge className={"bg-red-100   text-red-500"}>{daysShiftOffers.length}</Badge>}
                </TableCell>
                <TableCell onClick={navigateToScheduleWeekday}>
                  <Badge className={"bg-purple-100 text-purple-500"}>{currentDateEvents.length}</Badge>
                </TableCell>
                <TableCell onClick={navigateToScheduleWeekday}>
                  <div className="ml-2 flex flex-col items-start">
                    <Text size="sm" semibold>{formatCurrency(metrics.projectedRevenue)}</Text>
                    <Text size="sm" muted>{formatCurrency(metrics.actualProductivity)} Productivity</Text>
                  </div>
                </TableCell>
                <TableCell onClick={navigateToScheduleWeekday}>
                  <div className="flex flex-col gap-1 items-start">
                    {shiftLeadersShifts.length === 0
                      ? <Text size={"sm"} muted>No shift leads assigned, yet</Text>
                      : shiftLeadersShifts.map((shiftLead, index) => (
                        <div key={`${shiftLead.person?.id}-${index ?? 0}`} className="flex items-center gap-2">
                          <ShiftLead key={shiftLead.person?.id ?? index} person={shiftLead.person}
                                     range={shiftLead.range}/>
                        </div>
                      ))}

                  </div>
                </TableCell>
              </TableRow>
            })}
          </TableBody>
        </Table>
      </div>
    </div>
  );
};

export default DashboardThisWeek;
