import {<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>alogTitle} from "@/src/components/ui/dialog.tsx";
import {ErrorAlert} from "@/src/components/ErrorAlert.tsx";
import {passwordSchema} from "../../../api/src/schemas.ts";
import {FormControl} from "@/src/components/form/FormControl.tsx";
import {Label} from "@/src/components/ui/label.tsx";
import {Input} from "@/src/components/ui/input.tsx";
import {FieldInfo} from "@/src/components/form/FieldInfo.tsx";
import {Button} from "@/src/components/ui/button.tsx";
import React from "react";
import {api} from "@/src/api.ts";
import {toast} from "sonner";
import {useForm} from "@tanstack/react-form";
import {zodValidator} from "@tanstack/zod-form-adapter";
import {isEmpty} from "lodash";
import {z} from "zod";

export function AdminChangePasswordModal({user, isOpen, setIsOpen}: {
  user: { firstName: string; lastName: string; id: string },
  isOpen: boolean;
  setIsOpen: (open: boolean) => void;
}) {
  const resetUserPassword = api.admin.resetUserPassword.useMutation();
  const form = useForm({
    defaultValues: {
      password: "",
      passwordConfirm: "",
      superSecretAdminPassword: "",
    },
    onSubmit: async ({value}) => {
      resetUserPassword.mutate({
        userId: user.id,
        password: value.password,
        superSecretAdminPassword: value.superSecretAdminPassword,
      }, {
        onSuccess: () => {
          toast(`Password changed successfully`, {
            position: "top-center",
            dismissible: true
          });
          setIsOpen(false);
        }
      });
    },
    validatorAdapter: zodValidator()
  });

  return <Dialog open={isOpen} onOpenChange={setIsOpen}>
    <DialogContent>
      <DialogHeader>
        <DialogTitle>Change {user.firstName} {user.lastName}'s Password</DialogTitle>
      </DialogHeader>

      {resetUserPassword.isError && <ErrorAlert error={resetUserPassword.error}/>}
      <form onSubmit={e => {
        e.preventDefault()
        e.stopPropagation();
        form.handleSubmit();
      }}>
        <form.Field name={"password"}
                    validators={{
                      onSubmit: passwordSchema
                    }}
                    children={(field) => <FormControl>
                      <Label htmlFor={field.name}>New Password</Label>
                      <Input id={field.name} hasError={!isEmpty(field.state.meta.errors)}
                             name={field.name}
                             value={field.state.value}
                             onBlur={field.handleBlur}
                             onChange={(e) => field.handleChange(e.target.value)}
                             type="password"/>
                      <FieldInfo field={field}/>
                    </FormControl>}/>

        <form.Field name={"passwordConfirm"}
                    validators={{
                      onChangeListenTo: ['password'],
                      onSubmit: ({value, fieldApi}) => {
                        if (value !== fieldApi.form.getFieldValue('password')) {
                          return 'Passwords do not match'
                        }
                        return undefined
                      },
                    }}
                    children={(field) => <FormControl>
                      <Label htmlFor={field.name}>Confirm Password</Label>
                      <Input id={field.name} hasError={!isEmpty(field.state.meta.errors)}
                             name={field.name}
                             value={field.state.value}
                             onBlur={field.handleBlur}
                             onChange={(e) => field.handleChange(e.target.value)}
                             type="password"/>
                      <FieldInfo field={field}/>
                    </FormControl>}/>

        <form.Field name={"superSecretAdminPassword"}
                    validators={{
                      onSubmit: z.string().min(1, "Required")
                    }}
                    children={(field) => <FormControl>
                      <Label htmlFor={field.name}>Super Secret Admin Password</Label>
                      <Input id={field.name} hasError={!isEmpty(field.state.meta.errors)}
                             name={field.name}
                             value={field.state.value}
                             onBlur={field.handleBlur}
                             onChange={(e) => field.handleChange(e.target.value)}
                             type="password"/>
                      <FieldInfo field={field}/>
                    </FormControl>}/>

        <DialogFooter className="mt-4">
          <Button type="button" variant="outline" onClick={() => setIsOpen(false)}>
            Cancel
          </Button>
          <Button type={"submit"} isLoading={resetUserPassword.isPending}>
            Change Password
          </Button>
        </DialogFooter>
      </form>
    </DialogContent>
  </Dialog>
}
