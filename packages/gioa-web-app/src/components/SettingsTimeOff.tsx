import React, {Suspense} from 'react';
import {Text} from "@/src/components/Text.tsx";
import {But<PERSON>} from "@/src/components/ui/button.tsx";
import {CirclePlus, Clock, Trash} from "lucide-react";
import {Heading} from "@/src/components/Heading.tsx";
import {useForm} from "@tanstack/react-form";
import {map} from "lodash";
import {api} from "@/src/api.ts";
import {zodValidator} from '@tanstack/zod-form-adapter'
import {z} from "zod";
import {FormInput} from "@/src/components/form/FormInput.tsx";
import {FormCheckbox} from "@/src/components/form/FormCheckbox.tsx";
import {ErrorAlert} from "@/src/components/ErrorAlert.tsx";
import {toast} from "sonner";
import {FieldInfo} from "@/src/components/form/FieldInfo.tsx";
import {SettingsRequestRestrictionForm} from "@/src/components/SettingsRequestRestrictionForm.tsx";
import {Spinner} from "@/src/components/Spinner.tsx";

export interface SettingsTimeOffProps {
  storeId: string;
}

export const SettingsTimeOff: React.FC<SettingsTimeOffProps> = ({storeId}) => {
  const apiUtil = api.useUtils();
  const [timeOffReasons] = api.user.getTimeOffTypes.useSuspenseQuery({storeId});
  const createTimeOffType = api.user.createTimeOffType.useMutation({
    onSuccess: () => {
      apiUtil.user.getTimeOffTypes.invalidate({storeId});
      form.reset();
      toast.success("Time off reason created", {
        position: "top-center",
      });
    }
  });
  const archiveTimeOffType = api.user.archiveTimeOffType.useMutation({
    onSuccess: () => {
      apiUtil.user.getTimeOffTypes.invalidate({storeId});
      toast.success("Time off reason archived", {
        position: "top-center",
      });
    }
  });

  const onArchive = (title: string) => {
    const result = window.confirm("Are you sure you want to archive this time off type?  Existing time off requests with this type will keep the association.")
    if (!result) {
      return;
    }
    archiveTimeOffType.mutate({
      storeId: storeId,
      title: title
    });
  }

  const form = useForm({
      defaultValues: {
        title: "",
        isPaid: true,
      },
      validatorAdapter: zodValidator(),
      onSubmit:
        ({value}) => {
          createTimeOffType.mutateAsync({
            storeId: storeId,
            title: value.title,
            isPaid: value.isPaid,
          })
        }
    }
  )

  return (
    <div>
      <Heading level={1}>Time Off</Heading>
      {createTimeOffType.isError && <ErrorAlert error={createTimeOffType.error}/>}
      {archiveTimeOffType.isError && <ErrorAlert error={archiveTimeOffType.error}/>}
      <div className="my-8">
        <div className={"flex flex-col"}>
          <Text semibold>Time Off Types</Text>
          <Text muted className="mb-4">Manage time off with various types</Text>
        </div>
        <div className={"flex flex-col gap-1"}>
          {timeOffReasons.types.length === 0
            ? <div className={"border border-gray-200 rounded-lg px-3 py-2 items-center"}>
              <Text muted>No time off types found. Being by adding a new type below.</Text>
            </div>
            : map(timeOffReasons.types, reason => {
              return <div className={"flex flex-row gap-4 items-center"} key={reason.title}>
                <div
                  className={"flex flex-row gap-4 border border-gray-200 rounded-lg px-3 py-2 items-center min-w-80"}>
                  <Clock className="h-4 w-4"/>
                  <Text>{reason.title}</Text>
                  <Text muted>{reason.isPaid ? "Paid" : "Unpaid"}</Text>
                </div>

                <Button variant={"ghost"} size={"sm"}
                        onClick={() => onArchive(reason.title)}
                        isLoading={archiveTimeOffType.isPending}>
                  <Trash className={"text-red-600"}/>
                </Button>
              </div>
            })}
        </div>

        <div className={"mt-6 p-4 shadow-md rounded-md border border-gray-100 mb-8"}>
          <div className={"mb-3"}>
            <Text semibold>Add New Type</Text>
          </div>
          <div className={"flex flex-col lg:flex-row gap-4 items-center"}>
            <form.Field name="title"
                        validators={{
                          onSubmit: z.string().min(3)
                        }}
                        children={field => (
                          <div className={"flex flex-col gap-3"}>
                            <FormInput field={field}
                                       className={"min-w-60"}
                                       placeholder="Reason for time off..."/>
                            <FieldInfo field={field}/>
                          </div>
                        )}/>
            <form.Field name="isPaid"
                        children={field => (
                          <div className={"flex flex-row gap-3"}>
                            <FormCheckbox field={field}
                                          label="Is Paid"/>
                          </div>
                        )}/>
            <Button leftIcon={<CirclePlus/>}
                    onClick={form.handleSubmit}
                    isLoading={createTimeOffType.isPending}>
              Add
            </Button>
          </div>
        </div>

        <div>
          <h2 className={"font-semibold"}>Time Off Request Restrictions</h2>
          <p className="mb-4 text-muted-foreground">Manage the restriction window for time off requests.</p>

          <Suspense fallback={<Spinner size={"lg"}/>}>
            <SettingsRequestRestrictionForm storeId={storeId} restrictionType='timeOff'/>
          </Suspense>
        </div>
      </div>
    </div>
  );
}
