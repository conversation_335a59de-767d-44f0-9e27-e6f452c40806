import React, {useEffect, useState} from 'react';
import {ArrowLeftIcon, LineChartIcon} from "lucide-react";
import {Button} from "@/src/components/ui/button.tsx";
import {Label} from "@/src/components/ui/label.tsx";
import {FormControl} from "@/src/components/form/FormControl.tsx";
import {Input} from "@/src/components/ui/input.tsx";
import {getDerivedMetrics, getMetrics} from "../../../api/src/scheduling/metrics/metrics.util.ts";
import {DayOfWeek} from "../../../api/src/timeSchemas.ts";
import {find, isNil} from "lodash";
import {daysOfWeek} from "@/src/components/AvailabilityWeek.tsx";
import {useDebounceCallback} from "usehooks-ts";
import {DraftSchedule, ScheduleMetricsInput} from "../../../api/src/scheduleSchemas.ts";
import {parseDollarsOrUndef, parseNumberOrUndef} from "@/src/util.tsx";
import {payRates} from "../../../api/src/scheduling/metrics/payRates/payRates.ts";
import {toCentsOrUndef} from "../../../api/src/scheduling/metrics/dollars.ts";

export interface MetricsPanelContentProps {
  initMetricsInput?: ScheduleMetricsInput;
  onBack: () => void;
  onUpdateMetrics: (metrics: ScheduleMetricsInput) => void;
  dayOfWeek: DayOfWeek;
  schedule: DraftSchedule;
  countOpenShiftsTowardsLabor: boolean;
}

export const MetricsPanelContent: React.FC<MetricsPanelContentProps> = ({
                                                                          initMetricsInput,
                                                                          onBack, schedule, countOpenShiftsTowardsLabor,
                                                                          onUpdateMetrics: _onUpdateMetrics,
                                                                          dayOfWeek
                                                                        }) => {
  const [projectedRevenue, setProjectedRevenue] = useState<string | undefined>(initMetricsInput?.projectedRevenue?.toString());
  const [productivityGoal, setProductivityGoal] = useState<string | undefined>(initMetricsInput?.productivityGoal?.toString());
  const [averagePayRate, setAveragePayRate] = useState<string | undefined>(initMetricsInput?.averagePayRate?.toString());

  const metrics = getMetrics({
    schedule: schedule,
    dayOfWeek: dayOfWeek,
    countOpenShiftsTowardsLabor,
    payRates: payRates(),
    averagePayRate: toCentsOrUndef(parseDollarsOrUndef(averagePayRate)),
  });

  useEffect(() => {
    setProjectedRevenue(initMetricsInput?.projectedRevenue?.toString());
    setProductivityGoal(initMetricsInput?.productivityGoal?.toString());
    setAveragePayRate(initMetricsInput?.averagePayRate?.toString());
  }, [dayOfWeek]);

  const onUpdateMetrics = useDebounceCallback(_onUpdateMetrics, 1000);
  useEffect(() => {
    if (!isNil(projectedRevenue) || !isNil(productivityGoal) || !isNil(averagePayRate)) {
      onUpdateMetrics({
        projectedRevenue: parseNumberOrUndef(projectedRevenue),
        productivityGoal: parseNumberOrUndef(productivityGoal),
        averagePayRate: parseNumberOrUndef(averagePayRate)
      });
    }
  }, [projectedRevenue, productivityGoal, averagePayRate]);

  const {
    estimatedLaborHours,
    actualProductivity,
    productivityPercentage,
    totalLaborHours,
    laborCost,
    totalNonOpsLaborHours,
    totalOpsLaborHours,
    shiftCount
  } = getDerivedMetrics({
    metrics,
    projectedRevenue: parseDollarsOrUndef(projectedRevenue),
    productivityGoal: parseDollarsOrUndef(productivityGoal),
  });

  const dayOfWeekObj = find(daysOfWeek, d => d.dayOfWeek === dayOfWeek);

  return (
    <div className={"h-full overflow-auto"}>
      <Button onClick={onBack} aria-label={"Back"} className={"mt-2 ms-1"}
              variant={"ghost"} size={"sm"}>
        <ArrowLeftIcon size={24} className={"text-gray-600"}/>
      </Button>
      <div className="px-4 pb-4 flex items-center gap-6 border-b">
        <div>
          <LineChartIcon size={36} className={"text-gray-600"}/>
        </div>
        <div className={"text-left"}>
          <p className={"font-medium mb-1"}>
            Metrics Calculator for {dayOfWeekObj?.name}
          </p>
          <p className={"text-sm"}>
            Calculate daily metrics.
          </p>
        </div>
      </div>
      <div className={"px-4 pt-4"}>
        <FormControl>
          <Label htmlFor={"gioa-projected-revenue"}>Projected Revenue ($)</Label>
          <Input id={"gioa-projected-revenue"}
                 value={projectedRevenue ?? ""} onChange={e => setProjectedRevenue(e.target.value)}
                 placeholder="Enter proj. revenue"/>
        </FormControl>
      </div>
      <div className={"flex gap-3 px-4"}>
        <div className={"w-1/2"}>
          <FormControl>
            <Label htmlFor={"gioa-productivity-goal"}>Productivity Goal ($/hr)</Label>
            <Input id={"gioa-productivity-goal"}
                   value={productivityGoal ?? ""} onChange={e => setProductivityGoal(e.target.value)}
                   placeholder="Enter $/hr goal"/>
          </FormControl>
        </div>
        <div className={"w-1/2"}>
          <FormControl>
            <Label htmlFor={"gioa-productivity-goal"}>Actual Productivity</Label>
            <Input id={"gioa-productivity-goal"} disabled variant={"filled"}
                   value={actualProductivity?.toFixed(1) ?? ""}
                   placeholder="—"/>
          </FormControl>
        </div>
      </div>
      <div className={"flex gap-3 px-4"}>
        <div className={"w-1/2"}>
          <FormControl>
            <Label htmlFor={"est-labor-hrs"}>Est. Labor Hours</Label>
            <Input id={"est-labor-hrs"} disabled variant={"filled"}
                   value={estimatedLaborHours?.toFixed(2) ?? ""}
                   placeholder="—"/>
          </FormControl>
        </div>
        <div className={"w-1/2"}>
          <FormControl>
            <Label htmlFor={"est-labor-shifts"}>Actual Labor Hours</Label>
            <Input id={"est-labor-shifts"} disabled variant={"filled"}
                   value={totalLaborHours?.toFixed(2) ?? ""}
                   placeholder="—"/>
          </FormControl>
        </div>
      </div>
      <div className={"flex gap-3 px-4"}>
        <div className={"w-1/2"}>
          <FormControl>
            <Label htmlFor={"est-labor-hrs"}>Average Pay Rate ($/hr)</Label>
            <Input id={"est-labor-hrs"}
                   value={averagePayRate ?? ""} onChange={e => setAveragePayRate(e.target.value)}
                   placeholder="Enter $/hr"/>
          </FormControl>
        </div>
        <div className={"w-1/2"}>
          <FormControl>
            <Label htmlFor={"est-labor-shifts"}>Labor Cost ($)</Label>
            <Input id={"est-labor-shifts"}  disabled variant={"filled"}
                   value={laborCost?.toFixed(1) ?? ""}
                   placeholder="—"/>
          </FormControl>
        </div>
      </div>
      <div className={"flex gap-3 px-4"}>
        <div className={"w-1/2"}>
          <FormControl>
            <Label htmlFor={"est-labor-hrs"}>Productivity Percentage</Label>
            <Input id={"est-labor-hrs"}  disabled variant={"filled"}
                   value={productivityPercentage?.toFixed(1) ?? ""}
                   placeholder="—"/>
          </FormControl>
        </div>
        <div className={"w-1/2"}>
          <FormControl>
            <Label htmlFor={"est-labor-shifts"}>Number of shifts</Label>
            <Input id={"est-labor-shifts"}  disabled variant={"filled"}
                   value={shiftCount ?? ""}
                   placeholder="—"/>
          </FormControl>
        </div>
      </div>
      <div className={"flex gap-3 px-4"}>
        <div className={"w-1/2"}>
          <FormControl>
            <Label htmlFor={"total-operational-hrs"}>Total Ops Hours</Label>
            <Input id={"total-operational-hrs"}  disabled variant={"filled"}
                   value={totalOpsLaborHours?.toFixed(1) ?? ""}
                   placeholder="—"/>
          </FormControl>
        </div>
        <div className={"w-1/2"}>
          <FormControl>
            <Label htmlFor={"total-admin-hrs"}>Total Non-Ops Hours</Label>
            <Input id={"total-admin-hrs"}  disabled variant={"filled"}
                   value={totalNonOpsLaborHours?.toFixed(1) ?? ""}
                   placeholder="—"/>
          </FormControl>
        </div>
      </div>
    </div>
  );
}
