import React from 'react';
import {PersonAvatar} from "@/src/components/PersonAvatar.tsx";
import {ProficiencyRating} from "@/src/components/ProficiencyRating.tsx";
import {Button} from "@/src/components/ui/button.tsx";
import {PersonDto} from "../../../api/src/schemas.ts";
import {XIcon} from 'lucide-react';

export interface SelectedTeamMemberRowProps {
  person: PersonDto;
  onRemove: () => void;
}

export const SelectedTeamMemberRow: React.FC<SelectedTeamMemberRowProps> = ({
                                                                              person,
                                                                              onRemove
                                                                            }) => {
  return <div className="flex items-center gap-2 justify-between pl-4 pr-2 py-3 bg-gray-50 rounded-md">
    <div className="flex items-center gap-2 grow">
      <PersonAvatar person={person}/>
      <div>
        <div>
          {person.firstName} {person.lastName}
        </div>
        <div className="text-sm text-muted-foreground">
          {person.jobTitle}
        </div>
      </div>

    </div>
    <div>
      <ProficiencyRating rank={person.proficiencyRanking ?? 0}
                         colorScheme={"light-bg"}/>
    </div>
    <Button
      variant="ghost"
      size="icon"
      onClick={onRemove}
    >
      <XIcon size={16}/>
    </Button>
  </div>
}
