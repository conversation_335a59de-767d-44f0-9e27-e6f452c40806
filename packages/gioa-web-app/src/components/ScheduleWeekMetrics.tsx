import React, {useState} from 'react';
import {But<PERSON>, buttonVariants} from "@/src/components/ui/button.tsx";
import {Link, useBlocker} from '@tanstack/react-router';
import {ArrowLeftIcon, ChevronDownIcon, PencilIcon, UploadIcon} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger
} from "@/src/components/ui/dropdown-menu.tsx";
import {api} from "@/src/api.ts";
import {isEmpty, map, times} from "lodash";
import {DateTime} from "luxon";
import {DataAgeBadge} from "@/src/components/DataAgeBadge.tsx";
import {WeekMetricsDayRow} from "@/src/components/WeekMetricsDayRow.tsx";
import {useForm} from '@tanstack/react-form';
import {zodValidator} from '@tanstack/zod-form-adapter'
import {parse24HourTime, to12HourTime} from "../../../api/src/date.util.ts";
import {WeekSalesUploadDialog} from "@/src/components/WeekSalesUploadDialog.tsx";
import {PayRatesUploadDialog} from "@/src/components/PayRatesUploadDialog.tsx";
import {EditPayRatesDialog} from "@/src/components/EditPayRatesDialog.tsx";
import {useDisclosure} from "@/src/hooks/useDisclosure.tsx";
import {SuccessSalesForecastJobDto} from "../../../api/src/scheduling/metrics/salesForecastJob/salesForecastJobDtos.ts";
import {ApplySavedForecastDialog} from './ApplySavedForecastDialog.tsx';
import {DailyTimeRange} from "../../../api/src/timeSchemas.ts";
import {toast} from 'sonner';
import {getHumanReadableErrorMessage} from "@/src/components/ErrorAlert.tsx";
import {
  convertCentsToDollars,
  convertForecastToForm,
  convertFormDollarValueToCents,
  convertFormForecastToMap
} from "@/src/metrics.util.ts";
import {Label} from "@/src/components/ui/label.tsx";
import {Input} from "@/src/components/ui/input.tsx";
import {FieldInfo} from "@/src/components/form/FieldInfo.tsx";
import {FormControl} from "@/src/components/form/FormControl.tsx";
import {SchedulePersonDto} from "../../../api/src/schedulePersonDto.ts";

export interface ScheduleWeekMetricsProps {
  storeId: string;
  timezone: string;
  storeHours: DailyTimeRange;
  scheduleId: string;
  year: number;
  week: number;
  people: SchedulePersonDto[];
}

export const ScheduleWeekMetrics: React.FC<ScheduleWeekMetricsProps> = ({
                                                                          storeId,
                                                                          timezone,
                                                                          storeHours,
                                                                          scheduleId,
                                                                          year,
                                                                          week, people
                                                                        }) => {
  const [{items: forecastJobs}] = api.data.getForecastJobs.useSuspenseQuery({
    storeId,
    limit: 5,
    status: "success",
    orderBy: "dataAge"
  });

  const [{forecast, averagePayRate, previousWeekProductivityGoal}] = api.data.getScheduleHourlySalesForecast.useSuspenseQuery({
    storeId,
    scheduleId,
  });

  const [store] = api.user.getStoreAdmin.useSuspenseQuery({storeId: storeId!}, {
    staleTime: 1000 * 60 * 60 // 1 hour
  });

  const apiUtil = api.useUtils();
  const saveForecast = api.data.saveScheduleHourlySalesForecast.useMutation();
  const form = useForm({
    defaultValues: {
      hours: convertForecastToForm(forecast?.dataPoints),
      productivityGoal: forecast?.productivityGoal
        ? convertCentsToDollars(forecast?.productivityGoal)
        : previousWeekProductivityGoal ? convertCentsToDollars(previousWeekProductivityGoal) : "",
      averagePayRate: forecast
        ? convertCentsToDollars(forecast.averagePayRate)
        : averagePayRate ? convertCentsToDollars(averagePayRate) : "",
    },
    onSubmit: async ({value}) => {
      saveForecast.mutate({
        scheduleId: scheduleId,
        storeId: storeId,
        dataPoints: convertFormForecastToMap(value.hours),
        productivityGoal: convertFormDollarValueToCents(value.productivityGoal),
        averagePayRate: convertFormDollarValueToCents(value.averagePayRate)
      }, {
        onSuccess: () => {
          toast.success("Forecast saved successfully", {closeButton: true});
          apiUtil.user.getSchedule.invalidate();
          apiUtil.data.getScheduleHourlySalesForecast.invalidate();
          form.reset(value);
        },
        onError: (error) => {
          alert(getHumanReadableErrorMessage(error));
        }
      })
    },
    validatorAdapter: zodValidator(),
  });

  const [startHour] = parse24HourTime(storeHours.start);
  const [endHour] = parse24HourTime(storeHours.end);
  const numHours = endHour - startHour;
  const upload = useDisclosure();
  const editPayRateDisclosure = useDisclosure();
  const uploadPayRateDisclosure = useDisclosure();

  const [forecastJobToApply, setForecastJobToApply] = useState<SuccessSalesForecastJobDto>();
  const apply = useDisclosure();
  const hasChanges = form.useStore(s => s.isDirty);
  useBlocker({
    blockerFn: () => {
      return window.confirm('You have unsaved changes. Are you sure you want to leave?');
    },
    condition: hasChanges,
  })

  return <>
    <form onSubmit={(e) => {
      e.preventDefault()
      e.stopPropagation()
      form.handleSubmit()
    }}>
      <div className={"flex flex-row gap-3 flex-wrap items-center"}>
        <Link to={"."} search={(prev: any) => ({...prev, metrics: false})}
              className={buttonVariants({variant: "outline"})}>
          <ArrowLeftIcon size={16} className={"mr-1"}/>
          Back
        </Link>
        <h1 className={"text-lg font-medium"}>
          Edit Metrics
        </h1>

        <div className={"grow"}/>

        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" rightIcon={<ChevronDownIcon size={16}/>}>
              Create Forecast
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent className="w-56">
            <DropdownMenuItem onClick={upload.onOpen}>
              <UploadIcon/>
              <span>Upload Sales Data</span>
            </DropdownMenuItem>
            {!isEmpty(forecastJobs) ? <>
              <DropdownMenuSeparator/>
              <DropdownMenuLabel className={"text-muted-foreground font-normal text-xs"}>
                Apply Saved Forecast
              </DropdownMenuLabel>
              <DropdownMenuGroup>
                {map(forecastJobs, job => {
                  if (job.status !== "success") return null;

                  return <DropdownMenuItem key={job.id} onClick={() => {
                    setForecastJobToApply(job);
                    apply.onOpen();
                  }}>
                  <span className={"grow"}>
                  {DateTime.fromJSDate(job.createdAt).toLocaleString(DateTime.DATE_SHORT)}
                  </span>
                    <DataAgeBadge dataRangeEnd={job.dataRangeEnd} timezone={timezone}/>
                  </DropdownMenuItem>
                })}
              </DropdownMenuGroup>
            </> : null}
          </DropdownMenuContent>
        </DropdownMenu>

        {store.permissions?.canViewPayRates && store.permissions?.canUpdatePayRates ?
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" type={"button"}
                      rightIcon={<ChevronDownIcon size={16}/>}>
                Manage Pay Rates
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent className="w-56">
              <DropdownMenuItem onClick={uploadPayRateDisclosure.onOpen}>
                <UploadIcon/>
                <span>Upload Pay Rates</span>
              </DropdownMenuItem>
              <DropdownMenuItem onClick={editPayRateDisclosure.onOpen}>
                <PencilIcon/>
                Edit Pay Rates
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
          : <Button variant="outline" type={"button"}
                    onClick={() => alert(`You do not have permission to manage pay rates. Contact your manager or Operator to request the "Manage Pay Rates" permission.`)}
                    rightIcon={<ChevronDownIcon size={16}/>}>
            Manage Pay Rates
          </Button>}

      </div>

      <p className={"my-3 text-muted-foreground text-sm"}>
        Input weekly projected revenue.
      </p>

      <div className={"overflow-auto "} style={{maxWidth: `calc(100vw - 180px)`}}>
        <table className={"border-collapse rounded-lg"}>
          <thead>
          <tr>
            <th>
            </th>
            {times(numHours, i => {
              const hour = startHour + i;
              return <th key={hour + "-hourth"}>
                {to12HourTime(hour + ":00", true, [" AM", " PM"])}
              </th>
            })}
            <th className={"sticky right-0 bg-white"}>
              Total
            </th>
          </tr>
          </thead>
          <tbody className={"[&>tr>td]:border [&>tr>td]:border-gray-300 [&>tr>td>input]:text-right"}>
          {times(7, i => {
            const weekday = i + 1;
            return <WeekMetricsDayRow key={weekday} scheduleId={scheduleId}
                                      storeHours={storeHours}
                                      year={year}
                                      weekNumber={week}
                                      form={form}
                                      timezone={timezone}
                                      weekday={weekday}

            />
          })}
          </tbody>
        </table>
      </div>

      <div className={"mt-4 flex flex-wrap flex-row gap-3"}>
        <form.Field name={"productivityGoal"}
                    validators={{
                      onSubmit: ({value}) => {
                        if (isEmpty(value)) return;
                        const cents = convertFormDollarValueToCents(value);
                        return Number.isInteger(cents) ? undefined : "Productivity goal must be a dollar amount";
                      }
                    }}
                    children={(field) => {
                      return <FormControl className={"mb-0"}>
                        <Label htmlFor={field.name}>Productivity Goal ($/hr)</Label>
                        <Input id={field.name} hasError={!isEmpty(field.state.meta.errors)}
                               name={field.name}
                               value={field.state.value}
                               onBlur={field.handleBlur}
                               onChange={(e) => field.handleChange(e.target.value)}
                               type="number"
                               placeholder="$0.00"/>
                        <FieldInfo field={field}/>
                      </FormControl>;
                    }}/>

        <form.Field name={"averagePayRate"}
                    validators={{
                      onSubmit: ({value}) => {
                        if (isEmpty(value)) return;
                        const cents = convertFormDollarValueToCents(value);
                        return Number.isInteger(cents) ? undefined : "Average pay rate must be a dollar amount";
                      }
                    }}
                    children={(field) => {
                      return <FormControl className={"mb-0"}>
                        <Label htmlFor={field.name}>Average Pay Rate ($/hr)</Label>
                        <Input id={field.name} hasError={!isEmpty(field.state.meta.errors)}
                               name={field.name}
                               value={field.state.value}
                               onBlur={field.handleBlur}
                               onChange={(e) => field.handleChange(e.target.value)}
                               type="number"
                               placeholder="$0.00"/>
                        <FieldInfo field={field}/>
                      </FormControl>;
                    }}/>

        <div className={"grow"}/>

        <div className={"self-end"}>
          <Button type={"submit"}
                  disabled={!hasChanges}
                  isLoading={saveForecast.isPending}>
            Save
          </Button>
        </div>
      </div>
    </form>
    <WeekSalesUploadDialog isOpen={upload.isOpen} onClose={upload.onClose}
                           storeId={storeId} scheduleId={scheduleId} timezone={timezone}/>

    {forecastJobToApply ?
      <ApplySavedForecastDialog isOpen={apply.isOpen} onOpenChange={apply.setOpen}
                                storeId={storeId} scheduleId={scheduleId} timezone={timezone}
                                forecastJob={forecastJobToApply}/> : null}

    <PayRatesUploadDialog isOpen={uploadPayRateDisclosure.isOpen} onClose={uploadPayRateDisclosure.onClose}
                          scheduleId={scheduleId}
                          storeId={storeId} people={people}/>

    <EditPayRatesDialog isOpen={editPayRateDisclosure.isOpen} onClose={editPayRateDisclosure.onClose}
                        scheduleId={scheduleId}
                        storeId={storeId} people={people}/>
  </>
}
