import { Text } from "@/src/components/Text.tsx";
import { api } from "@/src/api.ts";
import { flatMap, map, sortBy, uniq } from "lodash";
import { PersonNote } from "@/src/components/PersonNote";
import { ListFilterIcon, NotebookPenIcon, PlusIcon } from "lucide-react";
import React, { useCallback, useEffect, useMemo, useState } from "react";
import { FormInput } from "@/src/components/form/FormInput.tsx";
import { useForm } from "@tanstack/react-form";
import { zodValidator } from "@tanstack/zod-form-adapter";
import { PersonNoteDto, PersonPositionPerformanceDataPointNoteDto } from "../../../api/src/schemas.ts";
import { filterNotes, NotesFilterFormValues } from "../../../api/src/Notes.util.ts";
import { NotesFilterPopover } from "@/src/components/NotesFilterPopover.tsx";
import { Button } from "@/src/components/ui/button.tsx";
import { cn } from "@/src/util.ts";
import { findPosition } from "../../../api/src/playerCard.util.ts";
import { ViewPersonPositionModal } from "@/src/components/ViewPersonPositionModal.tsx";
import { EvaluatePersonPositionModal } from "@/src/components/EvaluatePersonPositionModal.tsx";
import { AddFeedBackSelectTypeModal } from "./AddFeedbackSelectTypeModal.tsx";
import { useGoTo } from "@/src/navigationUtils.ts";
import { Heading } from "@/src/components/Heading.tsx";
import { SearchIcon } from 'lucide-react';
import { useNavigate } from "@tanstack/react-router";
import { Route } from "@/src/routes/_signedin/$businessId/$storeId/_nav/team/directory/$personId/notes/index.tsx";
import { useDebounceCallback } from "usehooks-ts";

export function PersonNotesList({
  businessId,
  storeId,
  personId,
}: {
  businessId: string;
  storeId: string;
  personId: string;
}) {
  const [person] = api.user.getPersonDetail.useSuspenseQuery({
    personId: personId,
    storeId: storeId,
  });

  const canViewPositionScores = Boolean(person.requesterPermissionEvaluation?.canViewPositionScores);
  const canEditPositionScores = Boolean(person.requesterPermissionEvaluation?.canEditPositionScores);
  const canEditTraining = Boolean(person.requesterPermissionEvaluation?.canEditTraining);
  const [selectedPositionIdForModal, setSelectedPositionIdForModal] = useState<string | null>(null);
  const [isViewModalOpen, setIsViewModalOpen] = useState(false);
  const [isEvaluateModalOpen, setIsEvaluateModalOpen] = useState(false);
  const [isFeedbackModalOpen, setIsFeedbackModalOpen] = useState(false);

  const [{ timezone }] = api.user.getStore.useSuspenseQuery({ storeId: storeId! });

  const goTo = useGoTo();
  const onCreateActionableItem = () => {
    goTo.editActionableItem(businessId, storeId, personId);
  };

  const onCreateNote = (type: string) => {
    goTo.editPersonNote(businessId, storeId, personId, undefined, type);
  };

  const policyFilters = useMemo(() => {
    return uniq(
      flatMap(person.notes, (note) =>
        "policiesInAction" in note
          ? note.policiesInAction
          : "correctiveAction" in note
            ? note.correctiveAction.policiesInAction
            : [],
      ),
    );
  }, [person.notes]);

  const personNotes: PersonNoteDto[] = useMemo(
    () =>
      sortBy(
        [
          ...person.notes,
          ...flatMap(person.positionStatistics, (s) =>
            map(s.dataPoints, (dp): PersonPositionPerformanceDataPointNoteDto => {
              return {
                type: "positionPerformanceDataPoint",
                id: dp.id,
                note: dp.notes!,
                createdBy: dp.recorderPerson!,
                createdAt: dp.timestamp,
                dataPoint: dp,
                positionId: s.positionId,
                positionTitle: findPosition(person.storeAreas, s.positionId)?.title ?? "Unknown Position",
                isArchived: false,
              };
            }),
          ),
        ],
        (note) => -note.createdAt,
      ),
    [person],
  );

  const navigate = useNavigate();
  const searchParams = Route.useSearch();

  // Initialize form with search parameter from URL
  const form = useForm({
    defaultValues: { search: searchParams?.search || "" },
    validatorAdapter: zodValidator(),
  });

  // Define default values as a constant to ensure consistent comparison
  const DEFAULT_FILTER_VALUES = {
    orderBy: "recent" as const,
    showCorrectiveActions: true,
    showCoaching: true,
    showFeedback: true,
    showGeneral: true,
    showPositionScores: true,
    usePolicyFilter: false,
    includeArchived: false,
    pastDays: undefined,
  };

  // Create the complete default values with policies
  const defaultNotesFilterValues: NotesFilterFormValues = useMemo(() => ({
    ...DEFAULT_FILTER_VALUES,
    policies: policyFilters,
  }), [policyFilters]);

  // Initialize filter values from URL search parameters
  const initialFilterValues: NotesFilterFormValues = {
    orderBy: searchParams?.orderBy || defaultNotesFilterValues.orderBy,
    showCorrectiveActions: searchParams?.showCorrectiveActions ?? defaultNotesFilterValues.showCorrectiveActions,
    showCoaching: searchParams?.showCoaching ?? defaultNotesFilterValues.showCoaching,
    showFeedback: searchParams?.showFeedback ?? defaultNotesFilterValues.showFeedback,
    showGeneral: searchParams?.showGeneral ?? defaultNotesFilterValues.showGeneral,
    showPositionScores: searchParams?.showPositionScores ?? defaultNotesFilterValues.showPositionScores,
    usePolicyFilter: searchParams?.usePolicyFilter ?? defaultNotesFilterValues.usePolicyFilter,
    includeArchived: searchParams?.includeArchived ?? defaultNotesFilterValues.includeArchived,
    // If usePolicyFilter is true and policies are in URL, use those; otherwise use all policies
    policies: (searchParams?.usePolicyFilter && searchParams?.policies) ? searchParams.policies : policyFilters,
    pastDays: searchParams?.pastDays,
  };

  const [notesFilterValues, setNotesFilterValues] = useState<NotesFilterFormValues>(initialFilterValues);

  useEffect(() => {
    // Only update the policies when they change and policy filter is not active
    // This preserves user's selected policies when policy filter is active
    if (!notesFilterValues.usePolicyFilter) {
      setNotesFilterValues(prev => ({
        ...prev,
        policies: policyFilters,
      }));
    }
  }, [policyFilters, notesFilterValues.usePolicyFilter]);

  // Compare filter values with defaults to determine if filtering is active
  const isFilteringActive = React.useMemo(() => {
    // For policies, we need special handling since it's an array
    const isPoliciesDefault = !notesFilterValues.usePolicyFilter ||
      (notesFilterValues.policies.length === policyFilters.length);

    return (
      notesFilterValues.orderBy !== DEFAULT_FILTER_VALUES.orderBy ||
      !isPoliciesDefault ||
      notesFilterValues.usePolicyFilter !== DEFAULT_FILTER_VALUES.usePolicyFilter ||
      notesFilterValues.includeArchived !== DEFAULT_FILTER_VALUES.includeArchived ||
      notesFilterValues.pastDays !== DEFAULT_FILTER_VALUES.pastDays ||
      notesFilterValues.showCorrectiveActions !== DEFAULT_FILTER_VALUES.showCorrectiveActions ||
      notesFilterValues.showCoaching !== DEFAULT_FILTER_VALUES.showCoaching ||
      notesFilterValues.showFeedback !== DEFAULT_FILTER_VALUES.showFeedback ||
      notesFilterValues.showGeneral !== DEFAULT_FILTER_VALUES.showGeneral ||
      notesFilterValues.showPositionScores !== DEFAULT_FILTER_VALUES.showPositionScores ||
      form.state.values.search !== ""
    );
  }, [notesFilterValues, policyFilters, form.state.values.search]);

  const searchInput = form.useStore((state) => state.values.search);

  const debouncedUpdateSearchParam = useDebounceCallback(
    (value: string) => {
      navigate({
        // @ts-expect-error this is correct and working
        search: (prev) => ({ ...prev, search: value }),
        replace: true,
      });
    },
    500
  );

  useEffect(() => {
    debouncedUpdateSearchParam(searchInput);
  }, [searchInput, debouncedUpdateSearchParam]);

  // Use a debounced function for updating URL search parameters
  const debouncedUpdateFilterParams = useDebounceCallback(
    (values: NotesFilterFormValues) => {
      const selectedPolicies = values.usePolicyFilter &&
                              values.policies &&
                              values.policies.length > 0 &&
                              values.policies.length < policyFilters.length
                              ? values.policies
                              : undefined;

      navigate({
        search: {
          search: form.state.values.search,
          orderBy: values.orderBy,
          showCorrectiveActions: values.showCorrectiveActions,
          showCoaching: values.showCoaching,
          showFeedback: values.showFeedback,
          showGeneral: values.showGeneral,
          showPositionScores: values.showPositionScores,
          usePolicyFilter: values.usePolicyFilter,
          includeArchived: values.includeArchived,
          pastDays: values.pastDays,
          // Only include policies if they're a subset of all policies
          policies: selectedPolicies,
        } as any,
        replace: true,
      });
    },
    300
  );

  // Update URL when filter values change
  useEffect(() => {
    debouncedUpdateFilterParams(notesFilterValues);
  }, [notesFilterValues, debouncedUpdateFilterParams]);

  const filteredNotes = useMemo((): PersonNoteDto[] => {
    const searchString = searchInput.toLowerCase();
    return filterNotes({
      personNotes,
      filterValues: notesFilterValues,
      searchString,
      policyFilters,
      timezone,
    });
  }, [searchInput, personNotes, notesFilterValues, policyFilters, timezone]);

  const onClickNote = (note: PersonNoteDto) => {
    switch (note.type) {
      case "general":
        goTo.viewPersonNote(businessId, storeId, personId, note.id);
        break;
      case "correctiveAction":
        goTo.viewCorrectiveAction(businessId, storeId, personId, note.correctiveAction.id);
        break;
      case "positionPerformanceDataPoint":
        setSelectedPositionIdForModal(note.positionId);

        // Determine which modal to open based on permissions
        if (canEditPositionScores || canEditTraining) {
          setIsEvaluateModalOpen(true);
        } else if (canViewPositionScores) {
          setIsViewModalOpen(true);
        }
        break;
    }
  };

  return (
    <>
      <div className={"max-w-lg flex flex-col"}>
        <div className={"flex flex-row justify-between items-center"}>
          <Heading level={1} size={"xs"}>Notes</Heading>
        </div>
        <div className={"flex flex-row gap-2 mb-2"}>
          <div className={"flex-1"}>
            <form.Field
              name={"search"}
              children={(field) => <FormInput field={field} leftIcon={SearchIcon} placeholder={"Search notes..."} />}
            />
          </div>
          <NotesFilterPopover
            policies={policyFilters}
            initialValues={notesFilterValues}
            onSubmit={setNotesFilterValues}
            onReset={() => {
              // Reset form state first
              form.store.setState((state) => ({
                ...state,
                values: { search: "" },
              }));

              // Create a new reset values object with current policy filters
              const resetValues = {
                ...DEFAULT_FILTER_VALUES,
                policies: policyFilters,
              };

              // Reset filter values
              setNotesFilterValues(resetValues);

              // Reset URL search parameters
              // Use a direct object instead of a function to avoid dependency on previous state
              navigate({
                search: {
                  search: "",
                  orderBy: DEFAULT_FILTER_VALUES.orderBy,
                  showCorrectiveActions: DEFAULT_FILTER_VALUES.showCorrectiveActions,
                  showCoaching: DEFAULT_FILTER_VALUES.showCoaching,
                  showFeedback: DEFAULT_FILTER_VALUES.showFeedback,
                  showGeneral: DEFAULT_FILTER_VALUES.showGeneral,
                  showPositionScores: DEFAULT_FILTER_VALUES.showPositionScores,
                  usePolicyFilter: DEFAULT_FILTER_VALUES.usePolicyFilter,
                  includeArchived: DEFAULT_FILTER_VALUES.includeArchived,
                  // Don't include policies in URL when resetting
                  policies: undefined,
                  pastDays: undefined,
                } as any,
                replace: true,
              });
            }}
          >
            <Button
              leftIcon={
                <div className={"relative"}>
                  <ListFilterIcon size={18} color={"#000"} />
                  {isFilteringActive ? (
                    <div className={cn("absolute", "bottom-3", "left-3", "w-3", "h-3", "rounded-full", "bg-red-500")} />
                  ) : null}
                </div>}
              variant={"outline"}>
              Filters
            </Button>
          </NotesFilterPopover>
          <Button
            rightIcon={<PlusIcon size={18}/>}
            variant={"outline"}
            onClick={() => setIsFeedbackModalOpen(true)}
          >
            Add Feedback
          </Button>
        </div>
        <div className="">
          {filteredNotes.length === 0 ? (
            <div className="flex flex-col gap-5 items-center justify-center py-8 text-gray-500">
              <NotebookPenIcon size={42} color={"#333"} />
              <Text>No notes have been taken on this team member yet.</Text>
            </div>
          ) : (
            map(filteredNotes, (note) => <PersonNote key={note.id} note={note} onClickNote={onClickNote} />)
          )}
        </div>
      </div>

      {selectedPositionIdForModal && (
        <ViewPersonPositionModal
          isOpen={isViewModalOpen}
          onClose={() => setIsViewModalOpen(false)}
          personId={personId!}
          storeId={storeId!}
          positionId={selectedPositionIdForModal}
        />
      )}

      {selectedPositionIdForModal && (
        <EvaluatePersonPositionModal
          isOpen={isEvaluateModalOpen}
          onClose={() => setIsEvaluateModalOpen(false)}
          personId={personId!}
          storeId={storeId!}
          positionId={selectedPositionIdForModal}
        />
      )}

      <AddFeedBackSelectTypeModal
        isOpen={isFeedbackModalOpen}
        onOpenChange={setIsFeedbackModalOpen}
        personId={personId}
        storeId={storeId}
        onCreateActionableItem={onCreateActionableItem}
        onCreateNote={onCreateNote}
      />
    </>
  );
}
