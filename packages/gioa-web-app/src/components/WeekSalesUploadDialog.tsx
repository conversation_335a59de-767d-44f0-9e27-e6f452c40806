import React, {useCallback, useState} from 'react';
import <PERSON> from 'papa<PERSON><PERSON>';
import {DateTime} from 'luxon';
import {<PERSON><PERSON>, DialogContent, DialogFooter, DialogHeader, DialogTitle} from "@/src/components/ui/dialog.tsx";
import {But<PERSON>} from "@/src/components/ui/button.tsx";
import {api} from "@/src/api.ts";
import {useUploadDataFile} from "@/src/dataFile.util.tsx";
import {ForecastGraph} from "@/src/components/ForecastGraph.tsx";
import {getHumanReadableErrorMessage} from "@/src/components/ErrorAlert.tsx";
import {FileIcon, UploadIcon} from 'lucide-react';
import {useDropzone} from 'react-dropzone';
import {DataFileType} from '@gioa/api/src/scheduling/metrics/dataFile/dataFileDtos';
import {toast} from 'sonner';
import {calculateDaysAge} from "@/src/date.util.ts";
import {JobProgressBody, JobProgressCard} from "@/src/components/JobProgressCard.tsx";
import {trim} from "lodash";

enum WizardStep {
  UPLOAD_FILE = 0,
  FORECAST_JOB = 1,
  REVIEW_FORECAST = 2
}

interface SalesCSVRow {
  Restaurant_Number: string;
  Time: string;
  DAY: string;
  Hour: string;
  Sales_Total: string;
}

interface CSVValidationResult {
  isValid: boolean;
  errors: string[];
  sampleData?: SalesCSVRow[];
}

export interface WeekSalesUploadDialogProps {
  isOpen: boolean;
  onClose: () => void;
  storeId: string;
  scheduleId: string;
  timezone: string;
}

/**
 * Validates a CSV file to ensure it matches the expected format for sales data.
 * Uses PapaParse with streaming and worker options for efficient processing of large files.
 *
 * @param file - The CSV file to validate
 * @returns Promise that resolves to a validation result object
 */
function validateSalesCSV(file: File): Promise<CSVValidationResult> {
  return new Promise((resolve) => {
    const errors: string[] = [];
    const sampleData: SalesCSVRow[] = [];
    let headerValidated = false;
    let maxErrorCount = 1; // Limit the number of errors to collect

    // Define the required headers
    const requiredHeaders = [
      'Restaurant_Number',
      'Time',
      'DAY',
      'Hour',
      'Sales_Total'
    ];

    Papa.parse<SalesCSVRow>(file, {
      header: true,
      preview: 100,
      skipEmptyLines: true,
      fastMode: true, // Use fast mode for large files
      chunk: (results, parser) => {
        try {
          // Check for headers in the first chunk
          if (!headerValidated && results.data.length > 0) {
            const firstRow = results.data[0];
            const headers = Object.keys(firstRow);
            const missingHeaders = requiredHeaders.filter(h => !headers.includes(h));

            if (missingHeaders.length > 0) {
              errors.push(`Missing required headers: ${missingHeaders.join(', ')}`);
              parser.abort();
              resolve({
                isValid: false,
                errors
              });
              return;
            }

            headerValidated = true;
          }

          // Process each row in the chunk
          for (let rowCount = 0; rowCount < results.data.length; rowCount++) {
            const row = results.data[rowCount];

            // Collect sample data for the first few rows
            if (sampleData.length < 5) {
              sampleData.push(row);
            }

            // Validate row data
            // Check if Sales_Total is a valid number
            if (isNaN(parseFloat(row.Sales_Total))) {
              errors.push(`Row ${rowCount}: Sales_Total is not a valid number: ${row.Sales_Total}`);
            }

            // Check if Time has the correct format (yyyy-MM-dd HH:mm:ss.S) using Luxon
            const parsedDate = DateTime.fromFormat(trim(row.Time, '"'), 'yyyy-MM-dd HH:mm:ss.S');
            if (!parsedDate.isValid) {
              errors.push(`Row ${rowCount}: Time format is invalid: (${parsedDate.invalidReason}: ${parsedDate.invalidExplanation})`);
            }

            // If we've collected enough errors, stop processing
            if (errors.length >= maxErrorCount) {
              parser.abort();
              break;
            }
          }
        } catch (e) {
          console.error('Error in chunk processing:', e);
          errors.push(`Error processing CSV: ${e instanceof Error ? e.message : String(e)}`);
          parser.abort();
        }
      },
      complete: () => {
        resolve({
          isValid: errors.length === 0,
          errors,
          sampleData: sampleData.length > 0 ? sampleData : undefined
        });
      },
      error: (error) => {
        console.error('PapaParse error:', error);
        errors.push(`Error parsing CSV: ${error.message}`);
        resolve({
          isValid: false,
          errors
        });
      }
    });
  });
}

export function WeekSalesUploadDialog({isOpen, onClose, storeId, scheduleId, timezone}: WeekSalesUploadDialogProps) {
  const [currentStep, setCurrentStep] = useState<WizardStep>(WizardStep.UPLOAD_FILE);
  const [dataFileJobId, setDataFileJobId] = useState<string | null>(null);
  const [forecastJobId, setForecastJobId] = useState<string | null>(null);
  const [file, setFile] = useState<File | null>(null);
  const [validationResult, setValidationResult] = useState<CSVValidationResult | null>(null);
  const [isValidating, setIsValidating] = useState(false);

  // Reset state when dialog is closed
  const handleClose = () => {
    setCurrentStep(WizardStep.UPLOAD_FILE);
    setDataFileJobId(null);
    setForecastJobId(null);
    setFile(null);
    setValidationResult(null);
    setIsValidating(false);
    onClose();
  };

  // File upload hooks
  const upload = useUploadDataFile(api.data.getPresignedPostForDataFile.useMutation().mutateAsync);
  const createDataFile = api.data.createDataFile.useMutation();

  // Data file job polling - only poll when job is in a non-terminal state
  const dataFileJobQuery = api.data.getDataFileJob.useQuery(
    {
      storeId,
      dataFileJobId: dataFileJobId!
    },
    {
      refetchInterval: (query) => {
        const data = query.state.data;

        // Stop polling if job is in a terminal state (success or error)
        if (data?.status === "success" || data?.status === "error") {
          return false;
        }
        return 5000; // Poll every 5 seconds
      },
      enabled: Boolean(dataFileJobId) && currentStep === WizardStep.UPLOAD_FILE,
    }
  );

  // Forecast job hooks
  const startForecastJob = api.data.startForecastJob.useMutation();

  // Forecast job polling - only poll when job is in a non-terminal state
  const forecastJobQuery = api.data.getForecastJobById.useQuery(
    {
      storeId,
      forecastJobId: forecastJobId!
    },
    {
      refetchInterval: (query) => {
        const data = query.state.data;

        // Stop polling if job is in a terminal state (success or error)
        if (data?.status === "success" || data?.status === "error") {
          return false;
        }
        return 5000; // Poll every 5 seconds
      },
      enabled: Boolean(forecastJobId) && currentStep === WizardStep.FORECAST_JOB,
    }
  );

  // Watch for forecast job completion and automatically move to review step
  React.useEffect(() => {
    if (forecastJobQuery.data?.status === "success" && currentStep === WizardStep.FORECAST_JOB) {
      setCurrentStep(WizardStep.REVIEW_FORECAST);
    }
  }, [forecastJobQuery.data?.status, currentStep]);

  // Apply forecast hook
  const applyForecast = api.data.applySavedScheduleHourlySalesForecast.useMutation();

  // Set up dropzone
  const onDrop = useCallback(async (acceptedFiles: File[]) => {
    const selectedFile = acceptedFiles?.[0];
    if (selectedFile) {
      // Reset previous validation state
      setFile(selectedFile);
      setValidationResult(null);
      setIsValidating(true);

      try {
        // Validate the CSV file
        const result = await validateSalesCSV(selectedFile);
        setValidationResult(result);
      } catch (error) {
        setValidationResult({
          isValid: false,
          errors: ["Unexpected error validating file. Please try again or use a different file."]
        });
      } finally {
        setIsValidating(false);
      }
    }
  }, [isValidating]);

  const {getRootProps, getInputProps, isDragActive} = useDropzone({
    onDrop,
    accept: {
      'text/csv': ['.csv'],
      'application/vnd.ms-excel': ['.csv']
    },
    multiple: false,
    disabled: !!dataFileJobId || upload.isLoading || createDataFile.isPending || isValidating
  });

  // Handle file upload
  const handleFileUpload = async () => {
    if (!file) return;

    try {
      const uploadResult = await upload.upload({
        file,
        contentType: "text/csv",
        storeId: storeId
      });

      if (!uploadResult) {
        toast.error("Failed to upload data file. Please try again or contact Nation support.");
        return;
      }

      const result = await createDataFile.mutateAsync({
        storeId: storeId,
        newDataFileId: uploadResult.newDataFileId,
        dataType: "15-min sales" as DataFileType,
        fileName: file.name,
        mimeType: file.type,
        sizeInBytes: file.size,
      });

      setDataFileJobId(result.dataFileJobId);
    } catch (e) {
      toast.error("Error uploading file: " + getHumanReadableErrorMessage(e));
    }
  };


  // Start forecast job
  const handleStartForecastJob = async () => {
    try {
      const result = await startForecastJob.mutateAsync({
        storeId: storeId
      });

      setForecastJobId(result.salesForecastJobId);
      setCurrentStep(WizardStep.FORECAST_JOB);
    } catch (e) {
      toast.error("Error starting forecast job: " + getHumanReadableErrorMessage(e));
    }
  };

  // Watch for data file job completion and automatically start forecast job
  React.useEffect(() => {
    if (dataFileJobQuery.data?.status === "success" && currentStep === WizardStep.UPLOAD_FILE) {
      handleStartForecastJob();
    }
  }, [dataFileJobQuery.data?.status, currentStep]);

  // Apply forecast to schedule
  const apiUtil = api.useUtils();
  const handleApplyForecast = async () => {
    if (!forecastJobId) return;

    try {
      await applyForecast.mutateAsync({
        storeId: storeId,
        scheduleId: scheduleId,
        salesForecastJobId: forecastJobId
      });

      apiUtil.user.getSchedule.invalidate();
      apiUtil.data.getScheduleHourlySalesForecast.invalidate();
      toast.success("Forecast applied to schedule successfully");
      handleClose();
    } catch (e) {
      toast.error("Error applying forecast: " + getHumanReadableErrorMessage(e));
    }
  };

  // Render step content based on current step
  const renderStepContent = () => {
    switch (currentStep) {
      case WizardStep.UPLOAD_FILE:
        return (
          <div className="space-y-4">
            <div
              {...getRootProps()}
              className={`border-2 border-dashed rounded-lg p-8 text-center cursor-pointer ${isDragActive ? 'border-primary bg-primary/10' : validationResult && !validationResult.isValid ? 'border-red-300' : 'border-gray-300'}`}
            >
              <input {...getInputProps()} />
              <UploadIcon className="mx-auto h-12 w-12 text-gray-400"/>

              {file ? (
                <div className="mt-4 flex flex-col items-center">
                  <div className="flex items-center gap-2 text-primary">
                    <FileIcon size={16}/>
                    <span className="font-medium">{file.name}</span>
                  </div>
                  <p className="mt-2 text-sm text-gray-500">
                    Click or drag to replace
                  </p>
                </div>
              ) : (
                <>
                  <p className="mt-2 text-sm text-gray-600">
                    Drag and drop your CSV file here, or click to browse
                  </p>
                  <p className="mt-1 text-xs text-gray-500">
                    File must be a CSV containing 15-minute sales data
                  </p>
                </>
              )}
            </div>

            {validationResult && !validationResult.isValid && (
              <div className="p-4 bg-red-50 rounded-md">
                <h3 className="text-red-800 font-medium mb-2">CSV Format Issues:</h3>
                <ul className="list-disc pl-5 space-y-1">
                  {validationResult.errors.map((error, index) => (
                    <li key={index} className="text-red-700 text-sm">{error}</li>
                  ))}
                </ul>
                <p className="mt-2 text-sm text-red-700">
                  Please do not open the downloaded .csv file before using it here. Opening the file in Excel
                  or other tools can change the format of the file.
                </p>
              </div>
            )}

            {dataFileJobId && dataFileJobQuery.data && (
              <JobProgressCard title={"File Processing Status"}
                               jobStatus={dataFileJobQuery.data.status}
                               errorMessage={"errorMessage" in dataFileJobQuery.data ? dataFileJobQuery.data.errorMessage : undefined}
                               pendingLabel={"Waiting for import job to start..."}
                               startedLabel={"Importing sales data from your file..."}
                               successLabel={"Hourly sales data imported successfully!"}
                               errorLabel={"Error processing file:"}/>
            )}
          </div>
        );

      case WizardStep.FORECAST_JOB:
        return (
          <div>
            {forecastJobQuery.data && (
              <JobProgressBody
                jobStatus={forecastJobQuery.data.status}
                pendingLabel={"Waiting to start forecast generation..."}
                startedLabel={"Generating forecast..."}
                successLabel={"Forecast generated successfully!"}
                errorMessage={"errorMessage" in forecastJobQuery.data ? forecastJobQuery.data.errorMessage : undefined}
                errorLabel={"Error generating forecast:"}/>
            )}
          </div>
        );

      case WizardStep.REVIEW_FORECAST:
        if (forecastJobQuery.data && forecastJobQuery.data.status === "success") {
          return (
            <div className="space-y-4 mb-4">

              <p className="mb-4">
                This forecast is based on data that
                is <strong>{calculateDaysAge(forecastJobQuery.data.dataRangeEnd, timezone)} days old</strong>.
                Would you like to apply this forecast to your current schedule week?
              </p>

              <ForecastGraph
                forecast={forecastJobQuery.data.forecast}
                dataRangeEnd={forecastJobQuery.data.dataRangeEnd}
                timezone={timezone}
                height={300}
              />

            </div>
          );
        }

      default:
        return null;
    }
  };

  // Render footer buttons based on current step
  const renderFooterButtons = () => {
    switch (currentStep) {
      case WizardStep.UPLOAD_FILE:
        const isIngestionSuccessful = dataFileJobId && dataFileJobQuery.data?.status === "success";
        const noFileSelected = !file;
        const fileReadyForUpload = file && !dataFileJobId;
        const fileValidated = validationResult && validationResult.isValid;
        const fileInvalid = validationResult && !validationResult.isValid;
        const canProceed = isIngestionSuccessful || (fileReadyForUpload && fileValidated);

        return (
          <>
            <Button variant="outline" onClick={handleClose}>
              Cancel
            </Button>
            <Button
              onClick={() => {
                if (fileReadyForUpload) {
                  handleFileUpload();
                }
              }}
              disabled={Boolean(!canProceed || startForecastJob.isPending || upload.isLoading || createDataFile.isPending || isValidating || fileInvalid)}
              isLoading={startForecastJob.isPending || upload.isLoading || createDataFile.isPending || isValidating}
            >
              Upload & Generate Forecast
            </Button>
          </>
        );

      case WizardStep.FORECAST_JOB:
        return (
          <>
            <Button variant="outline" onClick={handleClose}>
              Cancel
            </Button>
          </>
        );

      case WizardStep.REVIEW_FORECAST:
        return (
          <>
            <Button variant="outline" onClick={handleClose}>
              Cancel
            </Button>
            <Button
              onClick={handleApplyForecast}
              isLoading={applyForecast.isPending}
              disabled={applyForecast.isPending}
            >
              Apply Forecast
            </Button>
          </>
        );

      default:
        return null;
    }
  };

  // Get step title
  const getStepTitle = () => {
    switch (currentStep) {
      case WizardStep.UPLOAD_FILE:
        return "Upload Sales Data";
      case WizardStep.FORECAST_JOB:
        return "Generate Forecast";
      case WizardStep.REVIEW_FORECAST:
        return "Review Forecast";
      default:
        return "";
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && handleClose()}>
      <DialogContent size={"3xl"} className="overflow-y-auto min-h-[400px]" style={{maxHeight: "calc(100vh - 100px)"}}>
        <DialogHeader>
          <DialogTitle className={"text-center"}>{getStepTitle()}</DialogTitle>
        </DialogHeader>

        <div className="flex justify-center my-3">
          <div className="flex items-center">
            <div
              className={`flex items-center justify-center w-8 h-8 rounded-full ${currentStep >= WizardStep.UPLOAD_FILE ? 'bg-primary text-white' : 'bg-gray-200'}`}>
              1
            </div>
            <div className={`w-12 h-1 ${currentStep > WizardStep.UPLOAD_FILE ? 'bg-primary' : 'bg-gray-200'}`}></div>
            <div
              className={`flex items-center justify-center w-8 h-8 rounded-full ${currentStep >= WizardStep.FORECAST_JOB ? 'bg-primary text-white' : 'bg-gray-200'}`}>
              2
            </div>
            <div className={`w-12 h-1 ${currentStep > WizardStep.FORECAST_JOB ? 'bg-primary' : 'bg-gray-200'}`}></div>
            <div
              className={`flex items-center justify-center w-8 h-8 rounded-full ${currentStep >= WizardStep.REVIEW_FORECAST ? 'bg-primary text-white' : 'bg-gray-200'}`}>
              3
            </div>
          </div>
        </div>

        {currentStep === WizardStep.UPLOAD_FILE && <>
            <p className="mb-2">
                To get started, download a CSV file of 15-minute sales data from cfahome.com.
            </p>

            <ol className="space-y-3 list-decimal list-outside ml-6">
                <li className="pl-2">
                    Log into {' '}
                    <a rel="noreferrer noopener" href={"https://backoffice.cfahome.com/df/pickFeed.do"}
                       className={"text-blue-600 hover:underline"} target={"_blank"}>cfahome.com
                    </a>
                </li>
                <li className="pl-2">
                    Go to <strong className="font-semibold text-gray-900">Data Feed</strong>.
                </li>
                <li className="pl-2">
                    Scroll down to <strong className="font-semibold text-gray-900">Sales</strong>.
                </li>
                <li className="pl-2">
                    Select <strong className={"font-semibold text-gray-900"}>15 min Sales</strong>. Select at least 1
                    year back.
                </li>
                <li className="pl-2">
                    Download the CSV file.
                </li>
                <li className="pl-2">
                    Upload the CSV file here.
                </li>
            </ol>
        </>}
        {currentStep === WizardStep.FORECAST_JOB && "We're analyzing your sales data to generate a forecast. This may take a few minutes."}
        {currentStep === WizardStep.REVIEW_FORECAST && "Review the generated forecast before applying it to your schedule."}

        <div className="py-3">
          {renderStepContent()}
        </div>

        <DialogFooter className={"justify-between"}>
          {renderFooterButtons()}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
