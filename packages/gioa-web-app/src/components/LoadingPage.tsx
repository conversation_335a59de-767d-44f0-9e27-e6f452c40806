import React from 'react';
import {Spinner} from "@/src/components/Spinner.tsx";

export interface LoadingPageProps {
}

export const LoadingPage: React.FC<LoadingPageProps> = (props) => {
    return (
      <div className="flex h-screen w-screen items-center justify-center bg-gray-100 dark:bg-gray-900">
        <div className="flex flex-col items-center space-y-4">
          <Spinner size={"xl"}/>
          <p className="text-lg font-medium text-gray-500 dark:text-gray-400">Loading content...</p>
        </div>
      </div>
    );
}
