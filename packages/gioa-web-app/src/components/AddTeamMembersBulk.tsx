import React, { useCallback, useState } from "react";
import { api } from "@/src/api.ts";
import { keys, map } from "lodash";
import { Text } from "@/src/components/Text.tsx";
import { Button } from "@/src/components/ui/button.tsx";
import { ErrorAlert } from "@/src/components/ErrorAlert.tsx";
import { useDropzone } from "react-dropzone";
import { toast } from "sonner";

export function AddTeamMembersBulk({
  storeId,
  onClose,
  onFinish,
}: {
  storeId: string;
  onClose: () => void;
  onFinish: () => void;
}) {
  const [step, setStep] = React.useState(1);
  const [invitesSent, setInvitesSent] = useState(false);
  const [reviewDetails, setReviewDetails] = useState<
    | {
        errors: string[];
        addedPersons: { firstName: string; lastName: string; id: string }[];
        addedPersonsIds: string[];
        existingPersons: { firstName: string; lastName: string; id: string }[];
      }
    | undefined
  >();
  const [isShowingNewMembers, setIsShowingNewMembers] = useState(false);
  const [isShowingExistingMembers, setIsShowingExistingMembers] = useState(false);

  const [file, setFile] = useState<File | null>(null);
  const uploadMutation = api.user.uploadEmployeeOnboardingExcel.useMutation();

  const handleSubmit = async (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    if (!file) return;

    const reader = new FileReader();
    reader.onload = async () => {
      const fileBuffer = reader.result as ArrayBuffer;
      const base64String = btoa(String.fromCharCode(...new Uint8Array(fileBuffer)));

      try {
        const { errors, addedPersons, existingPersons } = await uploadMutation.mutateAsync({
          storeId,
          file: base64String,
        });

        setReviewDetails({ errors, addedPersons, existingPersons, addedPersonsIds: map(addedPersons, (p) => p.id) });
        setStep(4);
        setFile(null);
      } catch (error) {
        console.error("File upload failed:", error);
      }
    };

    reader.readAsArrayBuffer(file);
  };

  const onDrop = useCallback((acceptedFiles: File[]) => {
    const selectedFile = acceptedFiles?.[0];
    if (selectedFile) {
      setFile(selectedFile);
    }
  }, []);

  const { getRootProps, getInputProps } = useDropzone({
    onDrop,
    accept: {
      "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet": [".xlsx"],
      "application/vnd.ms-excel": [".xls"],
    },
    multiple: false,
  });

  const invitePersons = api.user.invitePersons.useMutation({
    onSuccess: () => {
      setInvitesSent(true);
      toast("Invitations have been successfully sent!", {
        position: "top-center",
        dismissible: true,
      });
    },
  });
  const onInvitePersons = async () => {
    if (!reviewDetails) {
      return;
    }

    const allPersonIds = [...reviewDetails.addedPersonsIds, ...reviewDetails.existingPersons.map((p) => p.id)];

    invitePersons.mutate({
      storeId,
      personIds: allPersonIds,
    });
  };

  const step1 = (
    <div>
      <div className="flex flex-col items-center border border-dashed border-gray-300 p-10 rounded-lg">
        <div className={"flex flex-col items-center"}>
          <div className="text-blue-600 mb-4">
            <svg width="55" height="49" viewBox="0 0 92 82" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path
                d="M92 49.2V75.44C92 77.1798 91.3077 78.8484 90.0753 80.0786C88.8429 81.3089 87.1714 82 85.4286 82H6.57143C4.82858 82 3.15711 81.3089 1.92473 80.0786C0.692345 78.8484 0 77.1798 0 75.44V49.2C0 47.4602 0.692345 45.7916 1.92473 44.5614C3.15711 43.3311 4.82858 42.64 6.57143 42.64H23C23.8714 42.64 24.7072 42.9856 25.3234 43.6007C25.9395 44.2158 26.2857 45.0501 26.2857 45.92C26.2857 46.7899 25.9395 47.6242 25.3234 48.2393C24.7072 48.8544 23.8714 49.2 23 49.2H6.57143V75.44H85.4286V49.2H69C68.1286 49.2 67.2928 48.8544 66.6767 48.2393C66.0605 47.6242 65.7143 46.7899 65.7143 45.92C65.7143 45.0501 66.0605 44.2158 66.6767 43.6007C67.2928 42.9856 68.1286 42.64 69 42.64H85.4286C87.1714 42.64 88.8429 43.3311 90.0753 44.5614C91.3077 45.7916 92 47.4602 92 49.2ZM43.6754 48.2406C43.9805 48.5456 44.3429 48.7875 44.7418 48.9526C45.1406 49.1176 45.5682 49.2026 46 49.2026C46.4318 49.2026 46.8594 49.1176 47.2582 48.9526C47.6571 48.7875 48.0195 48.5456 48.3246 48.2406L68.0389 28.5606C68.6555 27.9451 69.0018 27.1104 69.0018 26.24C69.0018 25.3696 68.6555 24.5349 68.0389 23.9194C67.4224 23.3039 66.5862 22.9582 65.7143 22.9582C64.8424 22.9582 64.0062 23.3039 63.3896 23.9194L49.2857 38.0029V3.28C49.2857 2.41009 48.9395 1.57581 48.3234 0.96069C47.7072 0.34557 46.8714 0 46 0C45.1286 0 44.2928 0.34557 43.6767 0.96069C43.0605 1.57581 42.7143 2.41009 42.7143 3.28V38.0029L28.6104 23.9194C27.9938 23.3039 27.1576 22.9582 26.2857 22.9582C25.4138 22.9582 24.5776 23.3039 23.9611 23.9194C23.3445 24.5349 22.9982 25.3696 22.9982 26.24C22.9982 27.1104 23.3445 27.9451 23.9611 28.5606L43.6754 48.2406ZM75.5714 62.32C75.5714 61.3469 75.2824 60.3957 74.7408 59.5866C74.1993 58.7775 73.4295 58.1469 72.5289 57.7745C71.6284 57.4021 70.6374 57.3047 69.6813 57.4945C68.7253 57.6844 67.8471 58.153 67.1578 58.841C66.4686 59.5291 65.9992 60.4058 65.809 61.3602C65.6188 62.3145 65.7164 63.3038 66.0895 64.2028C66.4625 65.1018 67.0942 65.8702 67.9047 66.4108C68.7152 66.9514 69.6681 67.24 70.6429 67.24C71.95 67.24 73.2036 66.7216 74.1279 65.799C75.0522 64.8763 75.5714 63.6249 75.5714 62.32Z"
                fill="#3E4B7E"
              />
            </svg>
          </div>
          <span className="text-blue-800 text-lg mb-5">Download Spreadsheet from CFAHOME.com</span>
          <div className={"flex flex-col gap-1"}>
            <Text>
              1. Log into{" "}
              <a href={"https://cfahome.com/"} className={"text-blue-600"} target={"_blank"}>
                cfahome.com
              </a>
            </Text>
            <Text>2. Go to Reports on the far right.</Text>
            <Text>3. On the left side, click on HR Payroll.</Text>
            <Text>4. Click on Reports.</Text>
            <Text>5. Click on Telephone and Contact.</Text>
            <Text>6. In the top right, click on Excel Report.</Text>
            <Text>7. Open the Excel file, but DO NOT modify any rows or columns.</Text>
            <Text>8. The only exception: Update any phone numbers or add missing phone numbers.</Text>
          </div>
        </div>
      </div>
      <div className="flex justify-end my-8 space-x-4">
        {step === 1 && (
          <Button variant={"outline"} onClick={onClose}>
            Cancel
          </Button>
        )}
        <Button
          onClick={() => {
            setFile(null);
            setStep(2);
          }}
        >
          Next
        </Button>
      </div>
    </div>
  );

  const step2 = (
    <div>
      <div className="flex flex-col items-center border border-dashed border-gray-300 p-10 rounded-lg">
        <div className={"flex flex-col items-center"}>
          <div className="text-blue-600 mb-4">
            <svg xmlns="http://www.w3.org/2000/svg" width="50" height="50" viewBox="0 0 79 79" fill="none">
              <path
                d="M7.5547 79C5.44358 79 3.65668 78.27 2.19401 76.81C0.731337 75.35 0 73.5664 0 71.4592V15.6271C0 13.5198 0.731337 11.7362 2.19401 10.2762C3.65668 8.81625 5.44358 8.08625 7.5547 8.08625H36.1897C37.2344 8.08625 38.018 8.4137 38.5404 9.06861C39.0628 9.72422 39.324 10.4396 39.324 11.2148C39.324 11.99 39.052 12.7054 38.508 13.361C37.964 14.0159 37.1696 14.3433 36.1249 14.3433H7.5547C7.23292 14.3433 6.93829 14.4772 6.67083 14.7448C6.40268 15.0118 6.2686 15.3059 6.2686 15.6271V71.4592C6.2686 71.7804 6.40268 72.0744 6.67083 72.3414C6.93829 72.6091 7.23292 72.7429 7.5547 72.7429H63.4894C63.8112 72.7429 64.1058 72.6091 64.3733 72.3414C64.6414 72.0744 64.7755 71.7804 64.7755 71.4592V42.6765C64.7755 41.6337 65.1036 40.8515 65.7597 40.3301C66.4165 39.8087 67.1332 39.548 67.9098 39.548C68.6864 39.548 69.4031 39.8087 70.0599 40.3301C70.716 40.8515 71.0441 41.6337 71.0441 42.6765V71.4592C71.0441 73.5664 70.3128 75.35 68.8501 76.81C67.3874 78.27 65.6005 79 63.4894 79H7.5547ZM25.0744 50.2007V42.7735C25.0744 41.7675 25.2701 40.8008 25.6615 39.8733C26.0523 38.9452 26.5907 38.1391 27.2768 37.455L62.9357 1.86148C63.5834 1.21422 64.296 0.742157 65.0733 0.445292C65.8499 0.148428 66.6401 0 67.4438 0C68.2636 0 69.051 0.148428 69.8061 0.445292C70.5618 0.742157 71.2503 1.20066 71.8716 1.82081L77.1194 7.0434C77.7247 7.69066 78.1896 8.40467 78.5142 9.18541C78.8381 9.96616 79 10.7577 79 11.56C79 12.3623 78.8621 13.1402 78.5863 13.8939C78.3104 14.6482 77.8483 15.3487 77.1999 15.9952L41.4208 51.7097C40.7347 52.3938 39.9271 52.942 38.998 53.3543C38.0681 53.7658 37.0996 53.9716 36.0925 53.9716H28.8523C27.7748 53.9716 26.8759 53.6118 26.1557 52.8923C25.4348 52.1734 25.0744 51.2762 25.0744 50.2007ZM31.343 47.7145H36.5511L62.5972 21.7152L59.9936 19.1165L57.2051 16.4374L31.343 42.2521V47.7145Z"
                fill="#3E4B7E"
              />
            </svg>
          </div>
          <div className={"flex flex-col gap-4 justify-center"}>
            <span className="text-blue-800 text-lg text-center">Update Team Member Phone Numbers in Excel</span>
            <Text center semibold>
              Do not modify anything except phone numbers!
            </Text>
            <Text center>
              Only update phone numbers to ensure they are correct. If you don't know someone's phone number, you can
              add team members one at a time.
            </Text>
          </div>
        </div>
      </div>
      <div className="flex justify-end my-8 space-x-4">
        <Button variant={"outline"} onClick={() => setStep(1)}>
          Back
        </Button>
        <Button onClick={() => setStep(3)}>Next</Button>
      </div>
    </div>
  );

  const step3 = (
    <div>
      <form onSubmit={handleSubmit}>
        <div
          {...getRootProps()}
          className="flex flex-col items-center border border-dashed border-gray-300 p-10 rounded-lg cursor-pointer"
        >
          <input {...getInputProps()} />
          <div className="text-blue-600 mb-4">
            <svg width="55" height="50" viewBox="0 0 92 82" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path
                d="M92 49.201V75.4402C92 77.18 91.3077 78.8485 90.0753 80.0787C88.8429 81.3089 87.1714 82 85.4286 82H6.57143C4.82858 82 3.15711 81.3089 1.92473 80.0787C0.692345 78.8485 0 77.18 0 75.4402V49.201C0 47.4613 0.692345 45.7928 1.92473 44.5626C3.15711 43.3324 4.82858 42.6412 6.57143 42.6412H26.2857C27.1571 42.6412 27.9929 42.9868 28.6091 43.6019C29.2253 44.217 29.5714 45.0513 29.5714 45.9211C29.5714 46.791 29.2253 47.6253 28.6091 48.2404C27.9929 48.8555 27.1571 49.201 26.2857 49.201H6.57143V75.4402H85.4286V49.201H65.7143C64.8429 49.201 64.0071 48.8555 63.3909 48.2404C62.7747 47.6253 62.4286 46.791 62.4286 45.9211C62.4286 45.0513 62.7747 44.217 63.3909 43.6019C64.0071 42.9868 64.8429 42.6412 65.7143 42.6412H85.4286C87.1714 42.6412 88.8429 43.3324 90.0753 44.5626C91.3077 45.7928 92 47.4613 92 49.201ZM28.6104 25.2824L42.7143 11.1993V45.9211C42.7143 46.791 43.0605 47.6253 43.6767 48.2404C44.2928 48.8555 45.1286 49.201 46 49.201C46.8714 49.201 47.7072 48.8555 48.3234 48.2404C48.9395 47.6253 49.2857 46.791 49.2857 45.9211V11.1993L63.3896 25.2824C64.0062 25.8978 64.8424 26.2436 65.7143 26.2436C66.5862 26.2436 67.4224 25.8978 68.0389 25.2824C68.6555 24.6669 69.0018 23.8322 69.0018 22.9619C69.0018 22.0915 68.6555 21.2568 68.0389 20.6413L48.3246 0.96195C48.0195 0.656997 47.6571 0.415075 47.2582 0.250016C46.8594 0.0849577 46.4318 0 46 0C45.5682 0 45.1406 0.0849577 44.7418 0.250016C44.3429 0.415075 43.9805 0.656997 43.6754 0.96195L23.9611 20.6413C23.3445 21.2568 22.9982 22.0915 22.9982 22.9619C22.9982 23.8322 23.3445 24.6669 23.9611 25.2824C24.5776 25.8978 25.4138 26.2436 26.2857 26.2436C27.1576 26.2436 27.9938 25.8978 28.6104 25.2824ZM75.5714 62.3206C75.5714 61.3476 75.2824 60.3964 74.7408 59.5873C74.1993 58.7782 73.4295 58.1476 72.5289 57.7753C71.6284 57.4029 70.6374 57.3055 69.6813 57.4953C68.7253 57.6851 67.8471 58.1537 67.1578 58.8418C66.4686 59.5298 65.9992 60.4065 65.809 61.3608C65.6188 62.3152 65.7164 63.3044 66.0895 64.2034C66.4625 65.1023 67.0942 65.8707 67.9047 66.4113C68.7152 66.9519 69.6681 67.2405 70.6429 67.2405C71.95 67.2405 73.2036 66.7221 74.1279 65.7995C75.0522 64.8768 75.5714 63.6254 75.5714 62.3206Z"
                fill="#3E4B7E"
              />
            </svg>
          </div>
          <div className={"flex flex-col gap-4 justify-center"}>
            <span className="text-blue-800 text-lg text-center">Upload Team Member Excel File</span>
            <Text semibold>Please ensure that only team member phone numbers were modified.</Text>
            {file ? (
              <span className="text-red-600 text-center">Selected File: {file.name}</span>
            ) : (
              <span className="text-gray-500 text-center border-dashed border-2 border-gray-400 rounded-lg p-8 mt-4">
                Drag and Drop or <span className={"text-blue-400"}>Browse Files</span> to
                <br />
                upload Team Member list.
              </span>
            )}
          </div>
        </div>
        <div className={"my-3"}>{uploadMutation.isError && <ErrorAlert error={uploadMutation.error} />}</div>
        <div className="flex justify-end my-8 space-x-4">
          <Button variant={"outline"} onClick={() => setStep(2)}>
            Back
          </Button>
          <Button isLoading={uploadMutation.isPending} disabled={!file} type={"submit"}>
            Next
          </Button>
        </div>
      </form>
    </div>
  );

  const step4 = (
    <div>
      <div className="flex flex-col items-center border border-dashed border-gray-300 p-10 rounded-lg">
        <div className={"flex flex-col items-center"}>
          <div className="text-blue-600">
            <div className={"flex flex-col items-center justify-center gap-3 mb-8"}>
              <svg width="60" height="50" viewBox="0 0 95 85" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path
                  d="M9.03875 85C6.51292 85 4.375 84.125 2.625 82.375C0.875 80.625 0 78.4871 0 75.9613V9.03875C0 6.51292 0.875 4.375 2.625 2.625C4.375 0.875 6.51292 0 9.03875 0H85.9613C88.4871 0 90.625 0.875 92.375 2.625C94.125 4.375 95 6.51292 95 9.03875V75.9613C95 78.4871 94.125 80.625 92.375 82.375C90.625 84.125 88.4871 85 85.9613 85H9.03875ZM9.03875 77.5H85.9613C86.3463 77.5 86.6988 77.3396 87.0188 77.0188C87.3396 76.6988 87.5 76.3463 87.5 75.9613V9.03875C87.5 8.65375 87.3396 8.30125 87.0188 7.98125C86.6988 7.66041 86.3463 7.5 85.9613 7.5H9.03875C8.65375 7.5 8.30125 7.66041 7.98125 7.98125C7.66042 8.30125 7.5 8.65375 7.5 9.03875V75.9613C7.5 76.3463 7.66042 76.6988 7.98125 77.0188C8.30125 77.3396 8.65375 77.5 9.03875 77.5ZM32.5 66.25C33.5642 66.25 34.455 65.8908 35.1725 65.1725C35.8908 64.455 36.25 63.5642 36.25 62.5C36.25 61.4358 35.8908 60.545 35.1725 59.8275C34.455 59.1092 33.5642 58.75 32.5 58.75H17.5C16.4358 58.75 15.545 59.1092 14.8275 59.8275C14.1092 60.545 13.75 61.4358 13.75 62.5C13.75 63.5642 14.1092 64.455 14.8275 65.1725C15.545 65.8908 16.4358 66.25 17.5 66.25H32.5ZM60.25 45.1538L55.76 40.6638C55.0158 39.9204 54.1375 39.5533 53.125 39.5625C52.1125 39.5725 51.2342 39.965 50.49 40.74C49.7983 41.4842 49.4475 42.3625 49.4375 43.375C49.4283 44.3875 49.7954 45.2658 50.5387 46.01L57.0863 52.5575C57.9904 53.4617 59.045 53.9137 60.25 53.9137C61.455 53.9137 62.5096 53.4617 63.4137 52.5575L80.5863 35.385C81.3296 34.6408 81.7096 33.7704 81.7263 32.7738C81.7421 31.7771 81.3621 30.8908 80.5863 30.115C79.8104 29.34 78.9196 28.9525 77.9137 28.9525C76.9071 28.9525 76.0158 29.34 75.24 30.115L60.25 45.1538ZM32.5 46.25C33.5642 46.25 34.455 45.8908 35.1725 45.1725C35.8908 44.455 36.25 43.5642 36.25 42.5C36.25 41.4358 35.8908 40.545 35.1725 39.8275C34.455 39.1092 33.5642 38.75 32.5 38.75H17.5C16.4358 38.75 15.545 39.1092 14.8275 39.8275C14.1092 40.545 13.75 41.4358 13.75 42.5C13.75 43.5642 14.1092 44.455 14.8275 45.1725C15.545 45.8908 16.4358 46.25 17.5 46.25H32.5ZM32.5 26.25C33.5642 26.25 34.455 25.8908 35.1725 25.1725C35.8908 24.455 36.25 23.5642 36.25 22.5C36.25 21.4358 35.8908 20.545 35.1725 19.8275C34.455 19.1092 33.5642 18.75 32.5 18.75H17.5C16.4358 18.75 15.545 19.1092 14.8275 19.8275C14.1092 20.545 13.75 21.4358 13.75 22.5C13.75 23.5642 14.1092 24.455 14.8275 25.1725C15.545 25.8908 16.4358 26.25 17.5 26.25H32.5Z"
                  fill="#3E4B7E"
                />
              </svg>

              <div className={"flex flex-col gap-1 items-center mt-3"}>
                <div className={"flex flex-row items-center gap-3"}>
                  <span className={"text-black font-bold"}>
                    {reviewDetails?.addedPersonsIds.length} new member
                    {reviewDetails?.addedPersonsIds.length === 1 ? "" : "s"} uploaded
                  </span>
                  {(reviewDetails?.addedPersons?.length ?? 0) > 0 ? (
                    <Button
                      variant={"outline"}
                      onClick={() => {
                        setIsShowingExistingMembers(false);
                        setIsShowingNewMembers(!isShowingNewMembers);
                      }}
                    >
                      {isShowingNewMembers ? "Hide" : "Show"} newly added members
                    </Button>
                  ) : null}
                </div>
                {(reviewDetails?.existingPersons.length ?? 0 > 0) ? (
                  <div className={"flex flex-row items-center gap-3"}>
                    <Text>
                      {reviewDetails?.existingPersons.length} Existing member
                      {reviewDetails?.addedPersonsIds.length === 1 ? "" : "s"} were unchanged
                    </Text>
                    <Button
                      variant={"outline"}
                      onClick={() => {
                        setIsShowingNewMembers(false);
                        setIsShowingExistingMembers(!isShowingExistingMembers);
                      }}
                    >
                      {isShowingExistingMembers ? "Hide" : "Show"} existing members
                    </Button>
                  </div>
                ) : null}
                {isShowingExistingMembers ? (
                  <div className={"my-3 border-2 border-gray-400 rounded-lg py-1 px-8"}>
                    <ol>
                      {map(reviewDetails?.existingPersons, (person, index) => {
                        return (
                          <li className={"text-gray-800"} style={{ listStyleType: "decimal" }} key={index}>
                            <Text>
                              {person.firstName} {person.lastName}
                            </Text>
                          </li>
                        );
                      })}
                    </ol>
                  </div>
                ) : null}
                {isShowingNewMembers ? (
                  <div className={"my-3 border-2 border-gray-400 rounded-lg py-1 px-8"}>
                    <ol>
                      {map(reviewDetails?.addedPersons, (person, index) => {
                        return (
                          <li className={"text-gray-800"} style={{ listStyleType: "decimal" }} key={index}>
                            <Text>
                              {person.firstName} {person.lastName}
                            </Text>
                          </li>
                        );
                      })}
                    </ol>
                  </div>
                ) : null}
              </div>
            </div>
          </div>
          {reviewDetails?.errors.length ? (
            <div className={"flex flex-col items-center justify-center"}>
              <div className={"flex flex-col items-center justify-center border-2 border-red-200 rounded-lg p-8 mb-6"}>
                <div className={"mb-3"}>
                  <svg width="22" height="21" viewBox="0 0 22 21" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path
                      d="M10.9922 14.4996H11.0012M11.0002 11.4996V7.49963M4.32221 8.18262C6.73521 3.91162 7.94221 1.77663 9.59821 1.22663C10.5085 0.924458 11.492 0.924458 12.4022 1.22663C14.0582 1.77663 15.2652 3.91162 17.6782 8.18262C20.0922 12.4526 21.2982 14.5886 20.9372 16.3286C20.7372 17.2866 20.2472 18.1546 19.5352 18.8086C18.2412 19.9996 15.8272 19.9996 11.0002 19.9996C6.17321 19.9996 3.75921 19.9996 2.46521 18.8096C1.75045 18.1496 1.2602 17.2824 1.06321 16.3296C0.701208 14.5896 1.90821 12.4536 4.32221 8.18262Z"
                      stroke="#E51636"
                      strokeWidth="1.5"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                  </svg>
                </div>
                <div className={"flex flex-col"}>
                  {map(reviewDetails.errors, (error) => (
                    <Text size={"sm"} className={"text-gray-500"}>
                      {"•"} {error}
                    </Text>
                  ))}
                </div>
              </div>
              <span className={"text-center text-gray-500"}>
                Fix the errors and{" "}
                <Button
                  variant={"link"}
                  onClick={() => {
                    setFile(null);
                    setStep(3);
                  }}
                  className={"my-0 mx-1 p-0"}
                >
                  <span className={"text-blue-400 line-height-0"}>re-upload spreadsheet</span>
                </Button>
                to finish adding all your Team Members.
              </span>
            </div>
          ) : (
            <div>
              <div className={"flex flex-col gap-2 items-center justify-center"}>
                <Text center>Congrats! No errors.</Text>
                <Text center>Send your team members invites then sign into the app to see your roster!</Text>
                <Button onClick={onInvitePersons} disabled={invitesSent} isLoading={invitePersons.isPending}>
                  {invitesSent ? "Invites Sent!" : "Send Invites Now"}
                </Button>
              </div>
            </div>
          )}
        </div>
      </div>
      <div className="flex justify-end my-8 space-x-4">
        <Button
          variant={"outline"}
          onClick={() => {
            setInvitesSent(false);
            setStep(3);
          }}
        >
          Back
        </Button>
        <Button
          onClick={() => {
            setInvitesSent(false);
            setStep(5);
          }}
          disabled={(reviewDetails?.errors.length ?? 0) > 0 || !invitesSent}
        >
          Next
        </Button>
      </div>
    </div>
  );

  const step5 = (
    <div>
      <div className="flex flex-col items-center border border-dashed border-gray-300 p-10 rounded-lg">
        <span className="font-bold text-2xl text-center mb-6 text-blue-800">IMPORTANT</span>

        <div className={"flex flex-col gap-4"}>
          <div className={"flex flex-col"}>
            <span className={"font-bold"}>What's next?</span>
            <span className={"text-gray-500"}>
              Your team members will receive an SMS with links to download the app and a verification code. Once they
              fill out their profiles, you can quickly review their training, permissions, and give them a star rating
              for scheduling. This can all be done on the Team tab in the app.
            </span>
          </div>
          <div className={"flex flex-col gap-4"}>
            <span className={"font-bold"}>FAQ:</span>
            <div className={"flex flex-col"}>
              <span className={"font-bold"}>
                1. What happens if I upload a team member, but their phone number is incorrect?
              </span>
              <span className={"text-gray-500"}>
                Simply go to the Team tab in the app, click on Pending, edit their phone number, and resend the invite.
              </span>
            </div>
            <div className={"flex flex-col"}>
              <span className={"font-bold"}>2. What if I don't see the team member in my pending invites?</span>
              <span className={"text-gray-500"}>
                On the Team tab, in the top left corner, you will see a + symbol. From there, you can manually add the
                team member.
              </span>
            </div>
            <div className={"flex flex-col"}>
              <span className={"font-bold"}>
                3. What happens if we have more than 1 store, and a team member is on both HR payrolls?
              </span>
              <span className={"text-gray-500"}>
                Whoever uploads the Excel file first will have that team member initially assigned to their store. After
                their profile is created, they can be easily added to both stores or moved to the correct store by
                editing their profile on the player card.
              </span>
            </div>
            <div className={"flex flex-col"}>
              <span className={"font-bold"}>4. How are permissions handled?</span>
              <span className={"text-gray-500"}>
                Every job title has a set of permissions. When you assign a job title to a team member, it will
                automatically assign the relevant permission sets. After assigning their job title, you can give
                additional permissions to that team member or remove permissions as needed.
              </span>
            </div>
            <div className={"flex flex-col"}>
              <span className={"font-bold"}>5. How do I edit a job title's permissions?</span>
              <span className={"text-gray-500"}>
                On the Dashboard tab, click the three dashes in the top left corner, and select "Manage job titles."
                From there, you can set core permissions based on the job title. After the title is assigned to a team
                member, you can modify their permissions individually.
              </span>
            </div>

            <div className={"flex flex-col items-center mb-6"}>
              <Button onClick={onFinish}>Finish and Close</Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  return (
    <ImportSteps step={step}>
      {step === 1 ? step1 : null}
      {step === 2 ? step2 : null}
      {step === 3 ? step3 : null}
      {step === 4 ? step4 : null}
      {step === 5 ? step5 : null}
    </ImportSteps>
  );
}

interface ImportStepIndicatorProps {
  number: number;
  label: string;
  isActive: boolean;
}

const ImportStepIndicator: React.FC<ImportStepIndicatorProps> = ({ number, label, isActive }) => {
  return (
    <div className={`w-16 flex flex-col items-center space-y-2 ${isActive ? null : "text-gray-400"}`}>
      <Text
        size={"xs"}
        center
        className={`flex items-center justify-center w-8 h-8 border-2 rounded-full ${isActive ? "border-primary-600 text-primary-600" : "border-gray-300"}`}
      >
        {number}
      </Text>
      <div className={"h-10"}>
        <Text center size={"xs"}>
          {label}
        </Text>
      </div>
    </div>
  );
};

interface ImportStepsProps {
  step: number;
  children: any;
}

const steps: Record<number, { title: string; label: string }> = {
  1: {
    title: "Team Member Onboarding",
    label: "Download from CFA",
  },
  2: {
    title: "Update Team Member Phone Numbers ",
    label: "Update",
  },
  3: {
    title: "Upload Team Member File",
    label: "Upload",
  },
  4: {
    title: "Verify Team Member Upload",
    label: "Verify",
  },
  5: {
    title: "Next Steps",
    label: "What's Next",
  },
};

const ImportSteps: React.FC<ImportStepsProps> = ({ step, children }) => {
  const separatorLine = <div className="flex-grow border-t border-gray-300 mx-3"></div>;
  return (
    <div className="flex w-full h-full flex-col">
      <div className="flex justify-center w-1xl">
        <div className="w-full">
          <div className="flex items-center justify-between my-4 flex-1">
            {map([...Array(5)], (value, i) => (
              <React.Fragment key={i}>
                <ImportStepIndicator number={i + 1} label={steps[i + 1]?.label} isActive={step === i + 1} />
                {i < keys(steps).length - 1 ? separatorLine : null}
              </React.Fragment>
            ))}
          </div>
          {children}
        </div>
      </div>
    </div>
  );
};
