import React, {useMemo} from 'react';
import {
  hourFloatToFriendlyDuration,
  incrementTo24HourTime,
  incrementToFriendlyTime,
  ScheduleRowInfo,
  ShiftActivity
} from "../../../api/src/scheduleBuilder.util.ts";
import {DailyTimeRange} from "../../../api/src/timeSchemas.ts";
import {AccordionContent, AccordionItem, AccordionTrigger} from "@/src/components/ui/accordion.tsx";
import {EditActivityDialog} from "@/src/components/EditActivityDialog.tsx";
import {FormControl} from "@/src/components/form/FormControl.tsx";
import {Label} from "@/src/components/ui/label.tsx";
import {ValueBadge} from "@/src/components/ValueBadge.tsx";
import {getRangeDurationHours} from "../../../api/src/date.util.ts";
import {cn} from "@/src/util.ts";
import {Button} from "@/src/components/ui/button.tsx";

export interface ShiftActivityProps {
  value: ShiftActivity;
  shift: ScheduleRowInfo;
  storeHours: DailyTimeRange;
  storeId: string;
  onChange: (activity: ShiftActivity) => void;
  isSelected: boolean;
  onDelete: () => void;
}

export const ShiftActivityAccordionItem: React.FC<ShiftActivityProps> = ({
                                                                           value: activity,
                                                                           storeHours, shift,
                                                                           onChange, isSelected,
  onDelete, storeId
                                                                         }) => {

  const onSubmit = (values: ShiftActivity) => {
    onChange(values);
  }

  const friendlyDuration = useMemo(() => {
    const duration = getRangeDurationHours({
      start: incrementTo24HourTime(storeHours, activity.start),
      end: incrementTo24HourTime(storeHours, activity.end),
    });
    if (Number.isNaN(duration)) {
      return "---";
    }

    return hourFloatToFriendlyDuration(duration);
  }, [activity.start, activity.end]);

  const readonlyField = ({label, value, center}: {
    label: string;
    value: string;
    center?: boolean;
  }) => {
    return <FormControl>
      <Label>{label}</Label>
      <ValueBadge className={cn("py-1 px-2 flex  items-center", {
        "justify-center": center
      })}>
        {value}
      </ValueBadge>
    </FormControl>
  }

  return <>
    <AccordionItem
      value={activity.id} key={activity.id} className={cn({
      "outline outline-2 outline-blue-700 bg-blue-50": isSelected
    })}>
      <AccordionTrigger className={"px-6"}>
        <div key={activity.id}
             className={"flex flex-row items-center justify-between gap-2 pr-2 w-full font-normal"}>
          <div className={"whitespace-nowrap text-ellipsis overflow-hidden max-w-[12rem]"}>
            {activity.activityType === "setups"
                    ? activity.setupPositionTitle
                    : activity.title}
          </div>
          <div className={"whitespace-nowrap"}>
            {incrementToFriendlyTime(storeHours, activity.start)} - {incrementToFriendlyTime(storeHours, activity.end)}
          </div>
        </div>
      </AccordionTrigger>
      <AccordionContent className={"py-2 px-6"}>
        <div className={"flex flex-row items-center gap-2"}>
          {readonlyField({label: "Start", value: incrementToFriendlyTime(storeHours, activity.start), center: true})}
          {readonlyField({label: "End", value: incrementToFriendlyTime(storeHours, activity.end), center: true})}
          {readonlyField({label: "Duration", value: friendlyDuration, center: true})}
          {activity.activityType !== "breaks" ? readonlyField({label: "Labor", value: activity.countsTowardsLabor ? "Yes" : "No"}) : null}
          {activity.activityType === "breaks" ? readonlyField({label: "Pay", value: activity.payStatus === "paid" ? "Paid" : "Unpaid"}) : null}
        </div>

        {activity.title ? readonlyField({label: "Title", value: activity.title}) : null}
        {activity.description ? readonlyField({label: "Description", value: activity.description}) : null}

        <div className={"flex flex-row items-center justify-between gap-3"}>
          <EditActivityDialog shift={shift} activity={activity} storeId={storeId}
                              onSubmit={onSubmit} storeHours={storeHours}/>
          <Button variant={"outline"} type={"button"} onClick={onDelete}>
            Delete
          </Button>
        </div>
      </AccordionContent>
    </AccordionItem>


  </>;

}
