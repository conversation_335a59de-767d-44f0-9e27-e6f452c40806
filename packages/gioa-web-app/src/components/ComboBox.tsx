import * as React from "react"
import { Check, ChevronsUpDown } from "lucide-react"

import { cn } from "@/src/util"
import { Button } from "@/src/components/ui/button"
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/src/components/ui/command"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/src/components/ui/popover"

export interface ComboBoxItem {
  id: string;
  title: string;
  subtitle?: string;
}

interface ComboBoxProps {
  items: ComboBoxItem[];
  value: string;
  onValueChange: (value: string) => void;
  placeholder?: string;
  className?: string;
  searchPlaceholder?: string;
  emptyMessage?: string;
}

export function ComboBox({
  items,
  value,
  onValueChange,
  placeholder = "Select item...",
  className,
  searchPlaceholder = "Search...",
  emptyMessage = "No item found."
}: ComboBoxProps) {
  const [open, setOpen] = React.useState(false)

  const selectedItem = items.find((item) => item.id === value)

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className={cn(
            "w-full justify-between text-left font-normal",
            !selectedItem && "text-muted-foreground",
            className
          )}
        >
          <div className="flex items-center gap-2 min-w-0">
            {selectedItem ? (
              <div className="flex flex-col min-w-0">
                <div className="font-medium text-sm truncate">{selectedItem.title}</div>
                {selectedItem.subtitle && (
                  <div className="text-xs text-muted-foreground truncate">{selectedItem.subtitle}</div>
                )}
              </div>
            ) : (
              <span className="text-muted-foreground">{placeholder}</span>
            )}
          </div>
          <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-[--radix-popover-trigger-width] p-0" align="start">
        <Command>
          <CommandInput placeholder={searchPlaceholder} />
          <CommandList>
            <CommandEmpty>{emptyMessage}</CommandEmpty>
            <CommandGroup>
              {items.map((item) => (
                <CommandItem
                  key={item.id}
                  value={`${item.title} ${item.subtitle || ''}`}
                  onSelect={() => {
                    onValueChange(item.id === value ? "" : item.id)
                    setOpen(false)
                  }}
                >
                  <div className="flex items-center gap-2 min-w-0 flex-1">
                    <div className="flex flex-col min-w-0 flex-1">
                      <div className="font-medium text-sm truncate">{item.title}</div>
                      {item.subtitle && (
                        <div className="text-xs text-muted-foreground truncate">{item.subtitle}</div>
                      )}
                    </div>
                    <Check
                      className={cn(
                        "h-4 w-4 shrink-0",
                        value === item.id ? "opacity-100" : "opacity-0"
                      )}
                    />
                  </div>
                </CommandItem>
              ))}
            </CommandGroup>
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  )
}
