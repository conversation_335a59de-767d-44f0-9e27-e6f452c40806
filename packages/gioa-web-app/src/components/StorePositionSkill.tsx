import React, {useState} from 'react';
import {filter, isEmpty, map, some} from 'lodash';
import {CheckCircle, ChevronRightIcon, FileText, StarIcon, XCircle} from 'lucide-react';
import {MetricStatistic, PositionTrainingHistoryDto, StorePositionDto} from '@gioa/api/src/schemas';
import {Progress} from "@/src/components/ui/progress.tsx";
import {Button} from "@/src/components/ui/button.tsx";
import {Text} from "@/src/components/Text.tsx";
import {Dialog, DialogContent, DialogFooter, DialogHeader, DialogTitle} from "@/src/components/ui/dialog.tsx";
import {cn} from "@/src/util.ts";

interface StorePositionSkillProps {
  position: StorePositionDto;
  training?: PositionTrainingHistoryDto;
  statistic?: MetricStatistic;
  onPositionTrainingEdit: (params: {
    positionTitle: string;
    positionId: string;
    isTrained: boolean;
  }) => void;
  onPositionClick?: (position: StorePositionDto) => void;
  canViewTraining: boolean;
  canEditTraining: boolean;
  canViewPositionScores: boolean;
}

export const StorePositionSkill: React.FC<StorePositionSkillProps> = React.memo(({
                                                                                   position,
                                                                                   training,
                                                                                   statistic,
                                                                                   onPositionTrainingEdit,
                                                                                   onPositionClick,
                                                                                   canViewTraining,
                                                                                   canEditTraining,
                                                                                   canViewPositionScores
                                                                                 }) => {
  const hasNotes = some(statistic?.dataPoints, dp => !isEmpty(dp.notes));

  const handleClick = (e: React.MouseEvent) => {
    // Prevent click from bubbling up to parent elements
    e.stopPropagation();
    if (onPositionClick) {
      onPositionClick(position);
    }
  };

  const handleTrainingButtonClick = (e: React.MouseEvent) => {
    // Prevent click from bubbling up to parent elements
    e.stopPropagation();
    canEditTraining && onPositionTrainingEdit({
      positionTitle: position.title,
      positionId: position.id,
      isTrained: !!training?.isCompleted,
    });
  };

  const [isNotesDialogOpen, setIsNotesDialogOpen] = useState(false);

  const onOpenNotes = (e: React.MouseEvent) => {
    // Prevent click from bubbling up to parent elements
    e.stopPropagation();
    setIsNotesDialogOpen(true);
  }

  const showChevronRight = Boolean(onPositionClick);

  return (
          <>
            <div key={position.id}
                 role={"button"}
                 tabIndex={0}
                 onClick={handleClick}
                 className={cn("border border-gray-200 rounded-lg px-4 py-3 mb-1 flex flex-row", {
                   cursor: showChevronRight ? "pointer" : "default",
                 })}>
              <div className={"flex-1"}>
                <Text size={"sm"} className="mb-2">{position.title}</Text>
                <div className="flex items-center gap-2 mb-2">
                  <Progress value={statistic?.value ?? 0} max={100}/>
                  <span className="text-sm">{(statistic?.value ?? 0).toFixed(0)}%</span>
                </div>
                <div className="flex flex-wrap gap-2 items-center">
                  {training?.isPersonPreferred && (
                          <div className={"p-2 rounded-md bg-purple-200"}>
                            <StarIcon size={12} className={"text-purple-500 fill-purple-500"}/>
                          </div>
                  )}

                  {canViewTraining && (
                          <Button size={"xs"}
                                  onClick={handleTrainingButtonClick}
                                  colorScheme={training?.isCompleted ? 'green' : 'red'}
                                  leftIcon={training?.isCompleted ? <CheckCircle size={16}/> : <XCircle size={16}/>}
                          >
                            {training?.isCompleted ? 'Trained' : 'Not Trained'}
                          </Button>
                  )}

                  {canViewPositionScores && hasNotes && <Button onClick={onOpenNotes}
                                       leftIcon={<FileText size={16}/>}
                                       size="xs"
                                       colorScheme={"blue"}
                                       variant={"outline"}>
                    Notes
                  </Button>}
                </div>
              </div>
              {showChevronRight ?
                      <div className={"flex items-center justify-center pl-8"}>
                        <ChevronRightIcon size={24} className={"text-gray-500"}/>
                      </div> : null}
            </div>

            {/* Notes Dialog */}
            <Dialog open={isNotesDialogOpen} onOpenChange={setIsNotesDialogOpen}>
              <DialogContent className="sm:max-w-[500px]">
                <DialogHeader>
                  <DialogTitle>Position Notes - {position.title}</DialogTitle>
                </DialogHeader>

                <div className="py-4">
                  {statistic && statistic.dataPoints && statistic.dataPoints.length > 0 ? (
                          <div className="space-y-4">
                            {map(filter(statistic.dataPoints, dp => !isEmpty(dp.notes)), (dataPoint, index) => (
                                    <div key={index} className="border border-gray-200 rounded-lg p-3">
                                      <div className="flex justify-between items-center mb-2">
                                        <Text bold size="sm">{new Date(dataPoint.timestamp).toLocaleDateString()}</Text>
                                        <Text size="sm">{dataPoint.value}%</Text>
                                      </div>
                                      <Text size="sm">{dataPoint.notes}</Text>
                                    </div>
                            ))}
                          </div>
                  ) : (
                          <Text>No notes available for this position.</Text>
                  )}
                </div>

                <DialogFooter>
                  <Button onClick={() => setIsNotesDialogOpen(false)}>Close</Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </>
  );
});
