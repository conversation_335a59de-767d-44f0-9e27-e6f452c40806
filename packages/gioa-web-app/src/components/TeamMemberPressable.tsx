import React from 'react';
import {PersonDto} from "../../../api/src/schemas.ts";
import {getPersonLaborStatus} from "../../../api/src/personMetrics.ts";
import {cn} from "@/src/util.ts";
import {PersonAvatar} from "@/src/components/PersonAvatar.tsx";
import {Text} from "@/src/components/Text.tsx";
import {LaborStatusIcon} from "@/src/components/LaborStatusIcon.tsx";

type PressablePerson = Pick<PersonDto, "profileImageUrl" | "firstName" | "lastName" | "id" | "age">;

export interface TeamMemberPressableProps<TPerson extends PressablePerson> {
  person: TPerson;
  className?: string;
  Subtext: React.ReactNode | null;
  RightElem: React.ReactNode | null;
  currentPersonId?: string;
}

export const TeamMemberPressable = <TPerson extends PressablePerson>({
                                                                       person,
                                                                       className,
                                                                       Subtext, currentPersonId,
                                                                       RightElem
                                                                     }: TeamMemberPressableProps<TPerson>): React.ReactElement => {


  return <div key={person.id}
              className={cn("bg-white rounded-lg px-3 py-2 flex flex-row items-center flex-wrap gap-2 mb-2 border-b-2 border-gray-200 border-r-1 border-l-1", className)}>
    <PersonAvatar person={person}/>
    <div className={cn("flex-1 grow justify-center")}>
      <Text>
        {person?.firstName} {person?.lastName} {person.id === currentPersonId ? "(You)" : null}
        {' '}
        {person.age ? <LaborStatusIcon laborStatus={getPersonLaborStatus(person.age)}/> : null}
      </Text>
      {Subtext}
    </div>
    {RightElem}
  </div>
}
