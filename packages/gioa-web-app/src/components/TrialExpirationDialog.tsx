import React, {useState} from 'react';
import {<PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>Header, DialogTitle} from "@/src/components/ui/dialog.tsx";
import {Button} from "@/src/components/ui/button.tsx";
import {Text} from "@/src/components/Text.tsx";
import {api} from "@/src/api.ts";
import {getHumanReadableErrorMessageString} from "@/src/components/ErrorAlert.tsx";
import {PlanChooser} from "@/src/components/PlanChooser.tsx";

import {PriceCommitmentType} from "../../../api/src/payments/priceSchemas.ts";

interface TrialExpirationDialogProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  daysRemaining: number;
  storeId: string;
}

export function TrialExpirationDialog({
                                        isOpen,
                                        onOpenChange,
                                        daysRemaining,
                                        storeId,
                                      }: TrialExpirationDialogProps) {
  const [store] = api.user.getStoreAdmin.useSuspenseQuery({storeId: storeId}, {
    staleTime: 1000 * 60 * 60 // 1 hour
  });
  const pricesQuery = api.payment.getPrices.useQuery();
  const startSetupSession = api.payment.startSetupPaymentSession.useMutation();
  const startCheckoutSession = api.payment.startCheckoutSession.useMutation();
  const [selectedPlan, setSelectedPlan] = useState<PriceCommitmentType>();

  const handleContinue = () => {
    if (!selectedPlan) {
      return;
    }

    // if they already have a trial Stripe Subscription, then we only need a payment method
    if (store.subscriptionStatus === 'trialing') {
      startSetupSession.mutate({
        storeId: storeId,
        commitmentType: selectedPlan,
      }, {
        onSuccess: (data) => {
          window.location.href = data.url;
        },
        onError: (error) => {
          alert("Failed to start payment setup: " + getHumanReadableErrorMessageString(error));
        }
      });

      // else we assume they have no subscription at all, so we need a full checkout session
    } else {
      startCheckoutSession.mutate({
        storeId: storeId,
        commitmentType: selectedPlan,
      }, {
        onSuccess: (data) => {
          window.location.href = data.url;
        },
        onError: (error) => {
          alert("Failed to start checkout: " + getHumanReadableErrorMessageString(error));
        }
      });
    }

  };

  const handleClose = () => {
    onOpenChange(false);
  };


  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent size="2xl">
        <DialogHeader>
          <DialogTitle className={"text-center"}>Your Trial is Ending Soon</DialogTitle>
        </DialogHeader>

        <Text center>
          Your Nation trial ends in {daysRemaining} day{daysRemaining !== 1 ? 's' : ''}.<br/>
          Choose your plan and add a payment method to continue using Nation.
        </Text>

        <PlanChooser
          selectedPlan={selectedPlan}
          onPlanChange={setSelectedPlan}
          pricesData={pricesQuery.data}
        />

        <Text center>
          Adding a payment method now ensures uninterrupted access to Nation.<br/>
          You won't be charged until your trial period ends.
        </Text>

        <div className="justify-center flex-1 pt-6 pb-4 flex">
          <Button size={"lg"}
                  onClick={handleContinue}
                  isLoading={startSetupSession.isPending}
                  disabled={startSetupSession.isPending || !selectedPlan}
                  className="bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700"
          >
            Continue with {selectedPlan === 'monthly'
            ? 'Monthly'
            : selectedPlan === 'annual' ?
              'Annual' : "Selected"} Plan
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
