import {useState} from 'react'
import {Loader2} from 'lucide-react'
import {Button} from '@/src/components/ui/button.tsx'
import {Dialog, DialogContent, DialogFooter, DialogHeader, DialogTitle} from '@/src/components/ui/dialog.tsx'
import {Checkbox} from '@/src/components/ui/checkbox.tsx'
import {toast} from 'sonner'
import {api} from '@/src/api.ts'
import {getHumanReadableErrorMessage} from '@/src/components/ErrorAlert.tsx'

interface Template {
  id: string
  title: string
}

interface CopyFromDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  businessId: string
  storeId: string
  fromStoreId: string
  fromTemplates: Template[]
  selectedStore?: { title: string }
  onSuccess: () => void
}

export function CopyFromDialog({
  open,
  onOpenChange,
  businessId,
  storeId,
  fromStoreId,
  fromTemplates,
  selectedStore,
  onSuccess,
}: CopyFromDialogProps) {
  const [selectedTemplates, setSelectedTemplates] = useState<Set<string>>(new Set(fromTemplates.map(t => t.id)))
  const copyTemplates = api.admin.copyChecklistTemplates.useMutation()

  const handleConfirmCopy = async () => {
    if (!selectedTemplates.size) {
      toast.error('Please select at least one template to copy')
      return
    }

    copyTemplates.mutate({
      from: {
        storeId: fromStoreId,
        templateIds: Array.from(selectedTemplates),
      },
      to: {
        storeId,
      },
    }, {
      onSuccess: () => {
        toast.success('Templates copied successfully')
        onSuccess()
        onOpenChange(false)
      },
      onError: (error) => {
        toast.error(getHumanReadableErrorMessage(error))
      }
    })
  }

  const toggleTemplate = (templateId: string) => {
    const newSelected = new Set(selectedTemplates)
    if (newSelected.has(templateId)) {
      newSelected.delete(templateId)
    } else {
      newSelected.add(templateId)
    }
    setSelectedTemplates(newSelected)
  }

  const toggleAll = () => {
    if (selectedTemplates.size === fromTemplates.length) {
      setSelectedTemplates(new Set())
    } else {
      setSelectedTemplates(new Set(fromTemplates.map(t => t.id)))
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Copy Templates from {selectedStore?.title}</DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Checkbox
                id="select-all"
                checked={selectedTemplates.size === fromTemplates.length}
                onCheckedChange={toggleAll}
                disabled={copyTemplates.isPending}
              />
              <label htmlFor="select-all" className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
                Select All
              </label>
            </div>
            <span className="text-sm text-muted-foreground">
              {selectedTemplates.size} of {fromTemplates.length} selected
            </span>
          </div>

          <div className="space-y-2 max-h-[400px] overflow-auto">
            {fromTemplates.map(template => (
              <div key={template.id} className="flex items-center space-x-2">
                <Checkbox
                  id={template.id}
                  checked={selectedTemplates.has(template.id)}
                  onCheckedChange={() => toggleTemplate(template.id)}
                  disabled={copyTemplates.isPending}
                />
                <label htmlFor={template.id} className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 grow">
                  {template.title}
                </label>
              </div>
            ))}
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)} disabled={copyTemplates.isPending}>
            Cancel
          </Button>
          <Button onClick={handleConfirmCopy} disabled={copyTemplates.isPending || selectedTemplates.size === 0}>
            {copyTemplates.isPending ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Copying...
              </>
            ) : (
              `Copy ${selectedTemplates.size} Template${selectedTemplates.size === 1 ? '' : 's'}`
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
