import React from "react";
import { Heading } from "@/src/components/Heading";
import { api } from "@/src/api";
import { Button } from "@/src/components/ui/button";
import { Text } from "@/src/components/Text";
import { toast } from "sonner";
import { find } from "lodash";
import { Card } from "@/src/components/ui/card";
import { Input } from "@/src/components/ui/input";
import { Label } from "@/src/components/ui/label";
import { useForm } from "@tanstack/react-form";
import { zodValidator } from "@tanstack/zod-form-adapter";
import { z } from "zod";
import { ArrowLeft, ListChecks } from "lucide-react";

export interface EditWorkAreaProps {
  storeId: string;
  areaId: string;
  onBackToAreas: () => void;
  onManagePositions: () => void;
}

export const EditWorkArea: React.FC<EditWorkAreaProps> = ({ storeId, areaId, onBackToAreas, onManagePositions }) => {
  const [store] = api.user.getStoreAdmin.useSuspenseQuery(
    { storeId },
    {
      staleTime: 1000 * 60 * 60, // 1 hour
    },
  );

  const apiUtil = api.useUtils();

  const area = find(store.areas, (a) => a.id === areaId);
  const isNewArea = !area;

  const upsertArea = api.user.upsertArea.useMutation({
    onSuccess: () => {
      apiUtil.user.getBusiness.invalidate();
      apiUtil.user.getStoreAdmin.invalidate({ storeId });
      toast.success(isNewArea ? "Work area created successfully" : "Work area updated successfully");
      onBackToAreas();
    },
    onError: (error) => {
      toast.error(`Failed to ${isNewArea ? "create" : "update"} work area: ${error.message}`);
    },
  });

  const deleteArea = api.user.deleteArea.useMutation({
    onSuccess: () => {
      apiUtil.user.getBusiness.invalidate();
      apiUtil.user.getStoreAdmin.invalidate({ storeId });
      toast.success("Work area deleted successfully");
      onBackToAreas();
    },
    onError: (error) => {
      toast.error(`Failed to delete work area: ${error.message}`);
    },
  });

  const form = useForm({
    defaultValues: {
      title: area?.title || "",
    },
    validatorAdapter: zodValidator(),
    onSubmit: async ({ value }) => {
      upsertArea.mutate({
        id: areaId,
        storeId,
        title: value.title,
      });
    },
  });

  const handleDelete = () => {
    if (confirm("Are you sure you want to delete this work area?")) {
      deleteArea.mutate({ id: areaId });
    }
  };

  const isReadonly = area?.isReadonly;

  return (
    <div className="pl-8 sm:max-w-lg bg-white border-l border-gray-200">
      <div className="flex items-center mb-6">
        <Button variant="ghost" size="icon" className="mr-2" onClick={onBackToAreas}>
          <ArrowLeft size={20} />
        </Button>
        <Heading level={1} size={"xs"} className={"mb-1"}>
          Edit Work Area
        </Heading>
      </div>

      {!isNewArea && (
        <div className="mb-6">
          <Button onClick={onManagePositions} leftIcon={<ListChecks size={16} />}>
            Manage Positions
          </Button>
        </div>
      )}

      <Card className="p-6">
        <form
          onSubmit={(e) => {
            e.preventDefault();
            e.stopPropagation();
            form.handleSubmit();
          }}
        >
          <div className="space-y-4">
            <div>
              <Label htmlFor="title">Area Name</Label>
              <form.Field
                name="title"
                validators={{
                  onChange: z.string().min(1, "Area name is required"),
                }}
              >
                {(field) => (
                  <div>
                    <Input
                      id="title"
                      placeholder="Enter area name..."
                      disabled={isReadonly}
                      value={field.state.value}
                      onBlur={field.handleBlur}
                      onChange={(e) => field.handleChange(e.target.value)}
                    />
                    {field.state.meta.errors ? (
                      <Text className="text-red-500 text-sm mt-1">{field.state.meta.errors.join(", ")}</Text>
                    ) : null}
                  </div>
                )}
              </form.Field>
            </div>

            <div className="flex justify-between pt-4">
              <div>
                {!isNewArea && !isReadonly && (
                  <Button
                    type="button"
                    variant="destructive"
                    onClick={handleDelete}
                    disabled={deleteArea.isPending || upsertArea.isPending}
                    isLoading={deleteArea.isPending}
                  >
                    Delete Area
                  </Button>
                )}
              </div>
              <div className="flex gap-2">
                <Button type="button" variant="outline" onClick={onBackToAreas}>
                  Cancel
                </Button>
                <Button
                  type="submit"
                  disabled={isReadonly || upsertArea.isPending || deleteArea.isPending}
                  isLoading={upsertArea.isPending}
                >
                  {isNewArea ? "Create Area" : "Save Changes"}
                </Button>
              </div>
            </div>
          </div>
        </form>
      </Card>
    </div>
  );
};
