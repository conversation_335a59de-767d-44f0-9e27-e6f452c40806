import React from "react";
import {PositionWithAreaTitle} from "@/src/hooks/useStorePositionsWithAreas";
import {StorePositionCounts} from "../../../api/src/setupSheets/storeSetupPositions/storeSetupPositionSchemas.ts";
import {Text} from "@/src/components/Text.tsx";
import {Badge} from "@/src/components/ui/badge.tsx";
import {NumberStepper} from "@/src/components/NumberStepper.tsx";
import {sanitizeStoreAreaTitle} from "../../../api/src/util.ts";
import {map} from "lodash";

export interface StorePositionCountPickerProps {
  storePositions: PositionWithAreaTitle[];
  value: StorePositionCounts;
  onChange: (value: StorePositionCounts) => void;
}

export const StorePositionCountPicker: React.FC<StorePositionCountPickerProps> = (props) => {

  return <div className="flex flex-col h-full">
    <div className="flex-1 overflow-y-auto py-4">
      {map(props.storePositions, (item) => {
        const count = props.value[item.id] ?? 0;
        const handleCountChange = (newCount: number) => {
          props.onChange({...props.value, [item.id]: newCount});
        };

        return <div key={item.id} className="flex flex-row items-center justify-between py-3 px-4 border-b border-gray-200">
            <div className="flex flex-row items-center gap-2">
              <Text semibold>{item.title}</Text>
              <Badge colorScheme={"secondary"} size="sm">{sanitizeStoreAreaTitle(item.areaTitle)}</Badge>
            </div>
            <NumberStepper value={count} onChange={handleCountChange} min={0} max={100} />
          </div>;
      })}
    </div>
  </div>;
};

