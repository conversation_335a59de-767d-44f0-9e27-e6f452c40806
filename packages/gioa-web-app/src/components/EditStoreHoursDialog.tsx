import React, {useEffect} from 'react';
import {<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ooter, <PERSON><PERSON><PERSON>eader, DialogTitle} from "@/src/components/ui/dialog.tsx";
import {Button} from "@/src/components/ui/button.tsx";
import {DialogDescription} from "@radix-ui/react-dialog";
import {zodValidator} from "@tanstack/zod-form-adapter";
import {useForm} from "@tanstack/react-form";
import {FormControl} from "@/src/components/form/FormControl.tsx";
import {Label} from "@/src/components/ui/label.tsx";
import {FormInput} from "@/src/components/form/FormInput.tsx";
import {FieldInfo} from "@/src/components/form/FieldInfo.tsx";
import {compareTimeOfDay} from "../../../api/src/date.util.ts";
import {DailyTimeRange} from "../../../api/src/timeSchemas.ts";

import {incrementDurationMinutes} from "../../../api/src/scheduleBuilder.util.ts";

export interface EditStoreHoursDialogProps {
  onSave: (range: DailyTimeRange) => void;
  isOpen: boolean;
  onClose: () => void;
  onOpen: () => void;
  storeHours: DailyTimeRange;
}

export const EditStoreHoursDialog: React.FC<EditStoreHoursDialogProps> = (props) => {
  const form = useForm({
    defaultValues: {
      start: props.storeHours.start,
      end: props.storeHours.end
    },
    onSubmit: async ({value}) => {
      props.onSave({
        start: value.start,
        end: value.end
      });
    },
    validatorAdapter: zodValidator(),
  });

  useEffect(() => {
    if (!props.isOpen) {
      // give a bit of time for the modal to animate out
      setTimeout(() =>
        form.reset(), 100);
    }
  }, [props.isOpen]);

  const onCancel = () => {
    props.onClose();
  }

  return (
    <Dialog open={props.isOpen} onOpenChange={open => {
      if (open) {
        props.onOpen();
      } else {
        onCancel();
      }
    }}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Edit Store Hours</DialogTitle>
          <DialogDescription>
            Edit the store hours for this week's schedule. This will change the shift start or end time of any shift that falls outside of the new store hours.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          <form onSubmit={(e) => {
            e.preventDefault()
            e.stopPropagation()
            form.handleSubmit()
          }}>
            <div className={"flex gap-2 mb-3"}>
              <form.Field name={`start`}
                          validators={{
                            onChange: ({value, fieldApi}) => {
                              const comparison = compareTimeOfDay(value, fieldApi.form.getFieldValue(`end`));
                              if (comparison >= 0) {
                                return "Start time must be before end time";
                              }
                              return undefined;
                            },
                            onChangeListenTo: [`end`],
                          }}
                          children={(field) => {
                            return <FormControl className={"w-1/2"}>
                              <Label htmlFor={field.name}>Start Time</Label>
                              <FormInput field={field} type={"time"}
                                         step={incrementDurationMinutes * 60}
                                         placeholder="Choose start time..."/>
                              <FieldInfo field={field}/>
                            </FormControl>;
                          }}/>
              <form.Field name={`end`}
                          validators={{
                            onChange: ({value, fieldApi}) => {
                              const comparison = compareTimeOfDay(value, fieldApi.form.getFieldValue(`start`));
                              if (comparison <= 0) {
                                return "End time must be after start time";
                              }
                              return undefined;
                            },
                            onChangeListenTo: [`start`],
                          }}
                          children={(field) => {
                            return <FormControl className={"w-1/2"}>
                              <Label htmlFor={field.name}>End Time</Label>
                              <FormInput field={field} type={"time"}
                                         step={incrementDurationMinutes * 60}
                                         placeholder="Choose end time..."/>
                              <FieldInfo field={field}/>
                            </FormControl>;
                          }}/>
            </div>

            <DialogFooter>
              <Button variant={"outline"} type={"button"}
                      onClick={onCancel}>
                Cancel
              </Button>
              <Button type={"submit"}>
                Save
              </Button>
            </DialogFooter>
          </form>
        </div>
      </DialogContent>
    </Dialog>
  );
}
