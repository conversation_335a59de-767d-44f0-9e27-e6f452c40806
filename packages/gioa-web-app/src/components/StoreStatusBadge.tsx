import React from 'react';
import {Badge, BadgeProps} from "@/src/components/ui/badge.tsx";

export type StripeSubscriptionStatus = "trialing" | "active" | "incomplete" | "incomplete_expired" | "past_due" | "canceled" | "unpaid" | "paused";

export interface StoreStatusBadgeProps {
  status: StripeSubscriptionStatus | null;
}

export function getStoreStatus(status: StripeSubscriptionStatus | null): string {
  return status ?? "no_subscription";
}

const statusToInfo: { [key: string]: { colorScheme: BadgeProps["colorScheme"], label: string } } = {
  trialing: {colorScheme: "default", label: "Trial"},
  active: {colorScheme: "success", label: "Active"},
  incomplete: {colorScheme: "yellow", label: "Incomplete"},
  incomplete_expired: {colorScheme: "destructive", label: "Incomplete Expired"},
  past_due: {colorScheme: "yellow", label: "Past Due"},
  canceled: {colorScheme: "outline", label: "Canceled"},
  unpaid: {colorScheme: "destructive", label: "Unpaid"},
  paused: {colorScheme: "secondary", label: "Paused"},
  no_subscription: {colorScheme: "outline", label: "No Subscription"},
};

export const StoreStatusBadge: React.FC<StoreStatusBadgeProps> = ({status}) => {
  const statusKey = status ?? "no_subscription";
  const {colorScheme, label} = statusToInfo[statusKey];

  return (
    <Badge colorScheme={colorScheme}>
      {label}
    </Badge>
  );
}
