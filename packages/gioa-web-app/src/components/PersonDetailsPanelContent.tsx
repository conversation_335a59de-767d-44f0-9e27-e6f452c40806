import React, {useMemo, useState} from 'react';
import {ProficiencyRating} from "@/src/components/ProficiencyRating.tsx";
import {BaseSchedule} from "../../../api/src/scheduleSchemas.ts";
import {Text} from "@/src/components/Text.tsx";
import {getPersonMetrics} from "../../../api/src/personMetrics.ts";
import {filter, find, flatMap, isEmpty, last, map, times} from "lodash";
import {Accordion} from "@/src/components/ui/accordion.tsx";
import {StoreAreaDto} from "../../../api/src/schemas.ts";
import {StoreAreaSkillsAccordionItem} from "@/src/components/StoreAreaSkillsAccordionItem.tsx";
import {ValueBadge} from "@/src/components/ValueBadge.tsx";
import {LaborStatusIcon} from "@/src/components/LaborStatusIcon.tsx";
import {daysOfWeek} from "@/src/components/AvailabilityWeek.tsx";
import {cn} from "@/src/util.ts";
import {
  getDateTimeFromWeekDayTime,
  getIsoWeekDateTimeRangeInTimezone,
  isTimeAllDay,
  to12HourTime
} from "../../../api/src/date.util.ts";
import {PersonAvatar} from "@/src/components/PersonAvatar.tsx";
import {DurationText} from "@/src/components/DurationText.tsx";
import {Progress} from "@/src/components/ui/progress.tsx";
import {getShiftsWithIsoWeek} from "../../../api/src/getShiftsWithIsoWeek.ts";
import {Card} from "@/src/components/Card.tsx";
import {twConfig} from "@/src/styles.tsx";
import {ShiftCardContent} from "@/src/components/ShiftCardContent.tsx";
import {ShiftWithIsoWeek} from "../../../api/src/scheduleNotifications.types.ts";
import {IsoWeekDate} from "../../../api/src/timeSchemas.ts";
import {Button} from "@/src/components/ui/button.tsx";
import {ChevronLeftIcon, ChevronRightIcon, CrownIcon, SunIcon} from "lucide-react";
import {DateTime} from "luxon";
import {api} from "@/src/api.ts";
import {getEffectiveAvailability} from "../../../api/src/getEffectiveAvailability.ts";
import {LoadingOverlay} from "@/src/components/LoadingOverlay.tsx";
import {getDayScheduleRows, ShiftOrTimeOff} from '@gioa/api/src/scheduleBuilder.util.ts';
import {SchedulePersonClientDto} from "../../../api/src/schedulePersonDto.ts";
import {constructShift} from "../../../api/src/shift.ts";
import {LinkCard} from "@/src/components/PressableCard.tsx";

export interface PersonDetailsPanelContentProps {
  person: SchedulePersonClientDto;
  schedule: BaseSchedule;
  storeAreas: StoreAreaDto[];
  selectedShiftId: string | null;
  timezone: string | null;
}

export const PersonDetailsPanelContent: React.FC<PersonDetailsPanelContentProps> = ({
                                                                                      person,
                                                                                      selectedShiftId,
                                                                                      storeAreas,
                                                                                      schedule,
                                                                                      timezone
                                                                                    }) => {
  const areas = filter(storeAreas, a => !isEmpty(a.positions));
  const onPositionTrainingEdit = () => {
  }

  const [isoWeekDisplayed, setIsoWeekDisplayed] = useState<IsoWeekDate>(schedule.week);
  const weekRange = getIsoWeekDateTimeRangeInTimezone({
    timezone,
    week: isoWeekDisplayed
  })

  const shiftsQuery = api.user.getPersonPublishedShifts.useQuery({
    storeId: schedule.storeId,
    start: weekRange.start.toJSDate(),
    end: weekRange.end.toJSDate(),
    personId: person.id,
  }, {
    staleTime: 1000 * 60 * 15 // 15 minutes
  });
  const isViewingCurrentWeek = isoWeekDisplayed.week === schedule.week.week && isoWeekDisplayed.year === schedule.week.year;
  const shiftsForWeek = useMemo(() => isViewingCurrentWeek
    ? getShiftsWithIsoWeek(schedule)
    : map(shiftsQuery.data?.shifts, (s): ShiftWithIsoWeek => {
      return {
        ...constructShift({
          range: s.range,
          shiftAreaId: s.area.id,
          id: s.id,
          assignedPersonId: s.assignedPersonId,
          title: s.title,
          description: s.description,
          isShiftLead: s.isShiftLead,
          storePositionId: s.position?.id,
        }),
        isoWeek: {
          year: s.year,
          week: s.week,
          day: s.day
        },
      }
    }), [schedule, shiftsQuery.data]);

  const {
    hoursProjected,
    daysWorked,
    hoursScheduled,
    wage,
    laborStatus,
    daysRemaining
  } = getPersonMetrics({selectedShiftId, person, shifts: shiftsForWeek});

  const effectiveAvailability = getEffectiveAvailability(person.availability, isoWeekDisplayed, timezone);
  const weekAvailability = isViewingCurrentWeek
    ? person.weekAvailability
    : effectiveAvailability.availability

  const schedRows = useMemo(() => {
    return flatMap([1, 2, 3, 4, 5, 6], dayOfWeek => {
      const dayShifts = filter(shiftsForWeek, s =>
        s.isoWeek.day === dayOfWeek
        && s.isoWeek.week === isoWeekDisplayed.week
        && s.isoWeek.year === isoWeekDisplayed.year
        && s.assignedPersonId === person.id);

      const isoWeek = {
        ...isoWeekDisplayed,
        day: dayOfWeek
      };
      const dayStart = getDateTimeFromWeekDayTime({
        ...isoWeek,
        time: "00:00",
        timezone: timezone
      });
      const rows = getDayScheduleRows({
        isoWeek: isoWeek,
        shifts: dayShifts,
        timeOff: person.timeOff,
        timezone: timezone ?? null
      });
      const availRangesForDay = filter(weekAvailability, range => range.dayOfWeek === dayOfWeek);

      const dayOfWeekObj = find(daysOfWeek, d => d.dayOfWeek === dayOfWeek);
      const getRowCard = (item: ShiftOrTimeOff) => {
        if ("type" in item) {
          if (item.type === "timeOffEnd" || item.type === "timeOffStart") {
            return <Card color={twConfig.theme.colors.yellow[500]}>
              <div className={"text-sm"}>
                <div>
                  Time Off - {item.type === "timeOffStart" ? "Start" : "End"}
                </div>
                <div>
                  {to12HourTime(item.time)}
                </div>
              </div>
            </Card>;
          } else {
            return <div
              className={"bg-gray-100 border border-gray-200 text-gray-700 flex flex-row items-center rounded-lg pl-5 pr-3 py-2 text-left w-full text-sm"}>
              {isEmpty(availRangesForDay) ? "No availability" : "No scheduled shifts"}
            </div>;
          }
        } else {
          return <LinkCard
            to={'.'}
            search={{shiftId: item.id, dayOfWeek: dayOfWeek}}
            color={twConfig.theme.colors.green[500]}>
            <ShiftCardContent shift={item} size={"sm"}>
              <div className={"text-sm flex flex-row items-center gap-1"}>
                Scheduled Shift {item.isShiftLead ? <span><CrownIcon size={16}/></span> : null}
              </div>
            </ShiftCardContent>
          </LinkCard>;
        }
      }

      return map(rows, (item, idx) => {
        const isFirst = idx === 0;
        return <React.Fragment key={dayOfWeek + "_" + idx}>
          {isFirst ?
            <div className={"rounded-md border px-2 py-2 text-sm text-center"}>
              <div>
                {dayOfWeekObj?.abbr}
              </div>
              <div className={"text-muted-foreground"}>
                {dayStart.toLocaleString({month: 'numeric', day: 'numeric'})}
              </div>
            </div> : <div/>}
          {getRowCard(item)}
        </React.Fragment>;
      });

    });
  }, [schedule, person, isoWeekDisplayed, shiftsForWeek, weekAvailability]);

  const onGoToPrevWeek = () => {
    const currWeek = DateTime.fromObject({
      weekYear: isoWeekDisplayed.year,
      weekNumber: isoWeekDisplayed.week,
      weekday: 1
    })

    const prevWeek = currWeek.minus({weeks: 1})
    setIsoWeekDisplayed({
      year: prevWeek.weekYear,
      week: prevWeek.weekNumber,
    })
  }
  const onGoToNextWeek = () => {
    const currWeek = DateTime.fromObject({
      weekYear: isoWeekDisplayed.year,
      weekNumber: isoWeekDisplayed.week,
      weekday: 1
    })

    const nextWeek = currWeek.plus({weeks: 1})
    setIsoWeekDisplayed({
      year: nextWeek.weekYear,
      week: nextWeek.weekNumber,
    })
  }
  const onGoToCurrentWeek = () => {
    setIsoWeekDisplayed({
      year: schedule.week.year,
      week: schedule.week.week,
    })
  }

  // Take the max hours preferred from the last day of the week. The assumption here is that if a person changes
  // their preferences sometime in this week, then the scheduler wants to work with the most up-to-date preferences.
  // Even if, e.g., the person's preference change doesn't take effect until Wednesday.
  const maxHoursPreferred = last(effectiveAvailability.preferences)?.maxHoursPreferred;

  return (
    <>
      <div className={"flex flex-col items-center border-b pb-6 gap-2 pt-6 px-3"}>
        <PersonAvatar size="3xl" person={person}/>
        <div className={"flex flex-row gap-2 items-center"}>
          <h2 className="text-xl">
            {person.firstName} {person.lastName}
          </h2>
          <LaborStatusIcon laborStatus={laborStatus}/>
        </div>
        <ProficiencyRating rank={person.proficiencyRanking ?? 0} colorScheme="light-bg"/>
      </div>

      <div className={"border-b px-3 py-3 flex flex-row gap-2 sticky top-0 bg-white z-50"}>
        <Button variant={"secondary"} onClick={onGoToPrevWeek}
                title={"Previous week"} size={"iconSm"}>
          <ChevronLeftIcon size={20} className={"text-gray-600"}/>
        </Button>
        <div className={"grow flex flex-row gap-1 justify-center items-center"}>
          <Text medium>
            W{isoWeekDisplayed.week}:{' '}
            {weekRange.start.toLocaleString({month: 'numeric', day: 'numeric'})}{' '}-{' '}
            {weekRange.end.toLocaleString({month: 'numeric', day: 'numeric'})}
          </Text>
          <Button onClick={onGoToCurrentWeek}
                  variant={"ghost"} size={"iconSm"} title={`Go to this schedule's week W${schedule.week.week}`}>
            <SunIcon size={20} className={"text-gioaBlue"}/>
          </Button>
        </div>
        <Button variant={"secondary"} onClick={onGoToNextWeek}
                title={"Next week"} size={"iconSm"}>
          <ChevronRightIcon size={20} className={"text-gray-600"}/>
        </Button>
      </div>

      <div className={"border-b px-3 py-3"}>
        <div className={"mb-2"}>
          <Text medium>
            Weekly Schedule
          </Text>
        </div>

        <div className={"flex items-center justify-between"}>
          <div>
            <span className={"pr-2"}>
              Scheduled
            </span>
            <span className={"bg-blue-100 text-blue-700 rounded-md px-1 py-0.5"}>
              <DurationText durationHours={hoursScheduled ?? 0} size={"md"}/>
            </span>
          </div>

          {maxHoursPreferred ?
            <div>
              <span className={"pr-2"}>
                Preferred
              </span>
              <span className={"bg-blue-100 text-blue-700 rounded-md px-1 py-0.5"}>
                <DurationText durationHours={maxHoursPreferred ?? 0} size={"md"}/>
              </span>
            </div> : null}
        </div>

        {maxHoursPreferred ?
          <Progress value={hoursScheduled ?? 0} className={"mt-2 mb-4"} max={maxHoursPreferred}/> : null}

        <div className={"grid grid-cols-[auto_1fr] gap-2 my-3 relative"}>
          {schedRows}
          <LoadingOverlay isLoading={isViewingCurrentWeek ? false : shiftsQuery.isLoading}/>
        </div>
      </div>

      {/*Availability*/}
      <div className={"flex flex-col gap-2 pr-4 pl-3 py-3 border-b"}>
        <Text medium>
          Availability
        </Text>

        {maxHoursPreferred ? <div className={"text-sm"}>
          Max hours preferred: {maxHoursPreferred}
        </div> : null}

        <table className={"text-sm"}>
          <tbody>
          {times(6, dayIndex => {
            const dayOfWeek = dayIndex + 1;
            const dayObj = find(daysOfWeek, d => d.dayOfWeek === dayOfWeek);
            const date = getDateTimeFromWeekDayTime({
              year: isoWeekDisplayed.year,
              week: isoWeekDisplayed.week,
              day: dayOfWeek,
              time: "00:00",
              timezone
            });

            const rangesForDay = filter(weekAvailability, range => range.dayOfWeek === dayOfWeek);
            const isLast = dayOfWeek === 6;
            const rowClassName = cn("py-1", {"border-b": !isLast});

            return <tr key={dayIndex}>
              <td className={rowClassName}>
                {dayObj?.abbr} <span className={"text-muted-foreground"}>{date.toLocaleString({
                month: 'numeric',
                day: 'numeric'
              })}</span>
              </td>
              <td className={rowClassName}>
                {isEmpty(rangesForDay) ? <Text muted size={"sm"}>Not available</Text> : null}
                <div className={"flex flex-wrap gap-1"}>
                  {map(rangesForDay, range => {
                    const isAllDay = isTimeAllDay(range);
                    return <ValueBadge key={range.start}>
                      {isAllDay ? "All Day" : `${to12HourTime(range.start, true)} - ${to12HourTime(range.end, true)}`}
                    </ValueBadge>
                  })}
                </div>
              </td>
            </tr>
          })}
          </tbody>
        </table>
      </div>

      <div className={"border-b"}>
        <div className={"flex flex-col gap-2 w-1/2 pr-4 pl-3 py-3"}>
          <Text medium>
            Labor Info
          </Text>
          <div>
            <span className={"text-sm pr-3"}>
            Status
            </span>
            <LaborStatusIcon laborStatus={laborStatus} showFullLabel/>
          </div>
        </div>
      </div>

      {/*Areas, positions, and training*/}
      <div>
        <Accordion type="single" collapsible className="w-full p-2">
          {map(areas, area => {
            return <StoreAreaSkillsAccordionItem key={area.id}
                                                 storeId={schedule.storeId}
                                                 canEditTraining={false}
                                                 canViewTraining={true}
                                                 canViewPositionScores={true}
                                                 onPositionTrainingEdit={onPositionTrainingEdit}
                                                 area={area} person={person}/>
          })}
        </Accordion>
      </div>
    </>
  );
}
