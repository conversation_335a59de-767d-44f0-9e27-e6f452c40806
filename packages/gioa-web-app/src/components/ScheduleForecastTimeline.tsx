import React, {useMemo} from 'react';
import {times} from "lodash";
import {DayOfWeek} from "../../../api/src/timeSchemas.ts";
import {cn} from "@/src/util.ts";
import {DraftSchedule} from "../../../api/src/scheduleSchemas.ts";
import {incrementTo24HourTime} from "../../../api/src/scheduleBuilder.util.ts";
import {
  ValidScheduleHourlySalesForecast
} from "../../../api/src/scheduling/metrics/scheduleHourlySalesForecast/scheduleHourlySalesForecastTypes.ts";
import {parse24HourTime} from "../../../api/src/date.util.ts";
import {getDerivedMetrics, getShiftsMetrics} from "../../../api/src/scheduling/metrics/metrics.util.ts";
import {getDay, getDayAreas, getDayShifts} from "../../../api/src/schedule.ts";
import {ForecastTimelineColumn} from "@/src/components/ForecastTimelineColumn.tsx";
import {toDollars} from "../../../api/src/scheduling/metrics/cents.ts";
import {
  getAveragePayRate,
  getForecastProductivityGoalDollars
} from "../../../api/src/scheduling/metrics/scheduleHourlySalesForecast/scheduleHourlySalesForecast.ts";
import {dollars} from "../../../api/src/scheduling/metrics/dollars.ts";
import {PayRates} from "../../../api/src/scheduling/metrics/payRates/payRatesDto.ts";

export interface ScheduleForecastTimelineProps {
  width: number;
  numIncrements: number;
  incrementWidth: number;
  schedule: DraftSchedule;
  forecast: ValidScheduleHourlySalesForecast;
  payRates: PayRates;
  weekday: DayOfWeek;
  countOpenShiftsTowardsLabor: boolean;
}

export const ScheduleForecastTimeline: React.FC<ScheduleForecastTimelineProps> = ({
                                                                                    width, payRates,
                                                                                    numIncrements,
                                                                                    incrementWidth, weekday,
                                                                                    schedule, forecast,
                                                                                    countOpenShiftsTowardsLabor
                                                                                  }) => {

  const storeHours = schedule.storeHours;
  const dayInfo = useMemo(() => {
    const day = getDay(schedule, weekday);
    if (!day) return {shifts: [], areas: []};
    const shifts = getDayShifts(schedule, weekday);
    const areas = getDayAreas(schedule, weekday);
    return {
      shifts,
      areas
    }
  }, [schedule, weekday]);

  return (
    <div id={"schedule-forecast-timeline"}
         style={{
           minWidth: width,
         }}
         className={"rounded-lg mx-4 border-b border-slate-200 shadow-sm mt-1 bg-opacity-60 bg-white backdrop-blur-2xl"}>
      <div className={"group h-full"}>
        {times(numIncrements / 4, (markIndex) => {
          const isLast = markIndex === numIncrements / 4;
          const increment = markIndex * 4;
          const startHour24 = incrementTo24HourTime(storeHours, increment);
          const incrementDailyTimeRange = { // a one hour range
            start: startHour24,
            end: incrementTo24HourTime(storeHours, increment + 4) // adding 4 15 minute increments for one hour
          }

          const [hour] = parse24HourTime(startHour24);
          const weekHour = hour + (weekday - 1) * 24;
          const sales = forecast.dataPoints.get(weekHour);
          const salesDollars = sales ? toDollars(sales.amountCents) : dollars(0);
          const metrics = getDerivedMetrics({
            metrics: getShiftsMetrics({
              shifts: dayInfo.shifts,
              areas: dayInfo.areas,
              range: incrementDailyTimeRange,
              countOpenShiftsTowardsLabor: countOpenShiftsTowardsLabor,
              averagePayRate: getAveragePayRate(forecast),
              payRates: payRates
            }),
            projectedRevenue: salesDollars,
            productivityGoal: getForecastProductivityGoalDollars(forecast)
          })

          return <div key={markIndex} style={{width: incrementWidth * 4}}
                      className={cn("inline-block border-slate-300 h-full text-sm relative", !isLast ? "border-r" : undefined)}>
            <ForecastTimelineColumn metrics={metrics} hour24={startHour24}
                                    rounding={incrementWidth < 20}/>
          </div>;
        })}
      </div>
    </div>
  );
}
