import React, {SetStateAction, useCallback, useEffect, useMemo, useState} from 'react';
import {cn} from "@/src/util";
import {api} from "@/src/api.ts";
import {StorePositionCountPicker} from "@/src/components/StorePositionCountPicker";
import {
  StorePositionCounts,
  StoreSetupPositions
} from "../../../api/src/setupSheets/storeSetupPositions/storeSetupPositionSchemas.ts";
import * as SP from "../../../api/src/setupSheets/storeSetupPositions/storeSetupPositions.ts";
import {addPositions, renamePosition} from "../../../api/src/setupSheets/storeSetupPositions/storeSetupPositions.ts";
import {Badge} from "@/src/components/ui/badge.tsx";
import {sanitizeStoreAreaTitle} from "../../../api/src/util.ts";
import {Text} from "@/src/components/Text.tsx";
import {Button} from "@/src/components/ui/button.tsx";
import {TimePicker} from "@/src/components/TimePicker.tsx";
import {TimeOfDay} from "../../../api/src/timeSchemas.ts";
import {useStorePositionsWithAreas} from "@/src/hooks/useStorePositionsWithAreas.tsx";
import {map, mapValues} from "lodash";
import {X} from "lucide-react";
import {AddCustomPositionDialog} from "@/src/components/AddCustomPositionDialog.tsx";
import {RenamePositionModal} from "@/src/components/RenamePositionModal.tsx";
import {useDisclosure} from "@/src/hooks/useDisclosure.tsx";
import {Dialog, DialogContent, DialogFooter, DialogHeader, DialogTitle} from "@/src/components/ui/dialog.tsx";
import {isValid24HourTimeString} from "../../../api/src/date.util.ts";

export interface SetupPositionsEditorDialogProps {
  storeId: string;
  value: StoreSetupPositions;
  onChange: (value: SetStateAction<StoreSetupPositions>) => void;
  onSave: () => void;
  onCancel: () => void;
}

export const SetupPositionsEditor: React.FC<SetupPositionsEditorDialogProps> = (props) => {
  const {storeId} = props;
  const [store] = api.user.getStore.useSuspenseQuery({storeId: storeId});
  const {positionMap, storePositions} = useStorePositionsWithAreas(store.storeAreas);
  const [localValue, setLocalValue] = useState<StoreSetupPositions>(props.value);
  useEffect(() => {
    setLocalValue(props.value);
  }, [props.value]);
  const localPositionCounts = SP.toStorePositionCounts(localValue);

  const onSave = useCallback(() => {
    props.onChange(localValue);
  }, [localValue, props.onChange]);

  const positions = useMemo(() => {
    return SP.toArray({storeSetupPositions: localValue, storeAreas: store.storeAreas});
  }, [localValue, store.storeAreas]);

  const [positionToRename, setPositionToRename] = useState<string>("");
  const positionCountsPicker = useDisclosure();
  const addCustomPositionModal = useDisclosure();
  const renamePositionModal = useDisclosure();

  const onRemovePosition = useCallback((positionTitle: string) => {
    setLocalValue(prev => SP.removePosition(prev, positionTitle));
  }, []);

  const onPositionPress = useCallback((positionTitle: string) => {
    setPositionToRename(positionTitle);
    renamePositionModal.onOpen();
  }, []);

  const onChangeStartTime = useCallback((positionTitle: string, newTime: TimeOfDay) => {
    if (isValid24HourTimeString(newTime)) {
      setLocalValue(prev => SP.updatePosition(prev, positionTitle, {start: newTime}));
    }
  }, []);

  const onChangeEndTime = useCallback((positionTitle: string, newTime: TimeOfDay) => {
    if (isValid24HourTimeString(newTime)) {
      setLocalValue(prev => SP.updatePosition(prev, positionTitle, {end: newTime}));
    }
  }, []);

  const onUpdateCounts = useCallback((value: StorePositionCounts) => {
    setLocalValue(prev => SP.updatePositionCounts(prev, mapValues(positionMap, p => p.title), value));
  }, [positionMap]);

  const onAddCustomPosition = useCallback((title: string, count: number) => {
    setLocalValue(prev => addPositions(prev, { baseTitle: title, storePositionId: undefined, count }));
  }, []);

  const onRenamePosition = useCallback((oldTitle: string, newTitle: string) => {
    try {
      setLocalValue(prev => renamePosition(prev, oldTitle, newTitle));
    } catch (error) {
      console.error('Failed to rename position:', error);
    }
  }, []);

  return (
    <div className={cn("flex-1 items-center bg-gradient-to-b from-gray-100 via-white to-[#f6f2f2]")}>
      <div className={cn("px-8 mb-4 pt-6 max-w-[680px] w-full mx-auto")}>
        <Text center className={cn("mb-6")}>
          Configure the maximum setup sheet positions available in your store on a fully-staffed, busy day. Tap a position to rename it.
        </Text>

        <div className={cn("flex flex-row items-center justify-center gap-3")}>
          <Button onClick={positionCountsPicker.onOpen}>
            Add From Store Positions
          </Button>
          <Button variant="outline" onClick={addCustomPositionModal.onOpen}>
            Add Custom
          </Button>
          <div className={"h-10 w-2 bg-gray-300"}/>

        </div>
      </div>

      <div className={cn("flex-1 w-full max-w-[680px] mx-auto pb-12 pt-6")}>
        {map(positions, (item) => (
          <div key={item.title} className={cn("flex flex-row items-center justify-between bg-white border-b border-gray-200", item.areaTitle ? "py-2" : "py-4")}>
            <button className={cn("flex-1 px-6 py-2 text-left")} onClick={() => onPositionPress(item.title)} aria-label={`Tap to rename ${item.title}`}>
              <div className={cn("flex flex-col gap-1 items-start")}>
                <Text semibold>{item.title}</Text>
                <div className={cn("flex flex-row gap-2 items-center")}>
                  {item.areaTitle ? <Badge colorScheme={"secondary"}>{sanitizeStoreAreaTitle(item.areaTitle)}</Badge> : null}
                  {item.storePositionId ? <Text size="sm">{positionMap[item.storePositionId]?.title}</Text> : null}
                </div>
              </div>
            </button>

            <div className={cn("px-6 flex flex-row items-center gap-3")}>
              <TimePicker value={item.start} placeholder="Start" minuteInterval={5} onChange={(newTime) => onChangeStartTime(item.title, newTime)} />
              <TimePicker value={item.end} placeholder="End" minuteInterval={5} onChange={(newTime) => onChangeEndTime(item.title, newTime)} />
              <Button variant="outline" aria-label="Remove position" onClick={() => onRemovePosition(item.title)}>
                <X className="w-5 h-5" />
              </Button>
            </div>
          </div>
        ))}
      </div>

      <AddCustomPositionDialog isOpen={addCustomPositionModal.isOpen} onClose={addCustomPositionModal.onClose} onSave={onAddCustomPosition} />
      <RenamePositionModal isOpen={renamePositionModal.isOpen} onClose={() => { setPositionToRename(""); renamePositionModal.onClose(); }} onSave={onRenamePosition} currentTitle={positionToRename} existingTitles={map(positions, p => p.title)} />

      <Dialog open={positionCountsPicker.isOpen} onOpenChange={(open) => open ? positionCountsPicker.onOpen() : positionCountsPicker.onClose()}>
        <DialogContent className={cn("max-w-[720px]")}>
          <DialogHeader>
            <DialogTitle>Select Store Positions</DialogTitle>
          </DialogHeader>
          <div className="max-h-[60vh] overflow-auto">
            <StorePositionCountPicker storePositions={storePositions} value={localPositionCounts} onChange={(value) => {
              onUpdateCounts(value);
            }} />
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={positionCountsPicker.onClose}>
              Cancel
            </Button>
            <Button onClick={() => {
              onSave()
              positionCountsPicker.onClose();
            }}>
              Save
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
