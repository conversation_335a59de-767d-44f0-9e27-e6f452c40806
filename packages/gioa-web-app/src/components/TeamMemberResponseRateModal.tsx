import React, { use<PERSON><PERSON>back, useMemo, useState } from "react";
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from "@/src/components/ui/dialog";
import { Text } from "@/src/components/Text";
import { Badge } from "@/src/components/ui/badge";
import { ScrollArea } from "@/src/components/ui/scroll-area";
import { TeamMemberPressable } from "@/src/components/TeamMemberPressable";
import { ScheduleEventDto } from "../../../api/src/scheduleEventSchemas";
import {filter, map} from "lodash";
import { ProficiencyRating } from "@/src/components/ProficiencyRating.tsx";
import { useGoTo } from "@/src/navigationUtils.ts";
import { Input } from "@/src/components/ui/input";
import { SearchIcon } from "lucide-react";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/src/components/ui/select";

interface TeamMemberResponseRateModalProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  scheduleEvent: ScheduleEventDto;
  storeId: string;
  businessId: string;
}

type AcknowledgmentFilter = "all" | "acknowledged" | "unacknowledged";

export const TeamMemberResponseRateModal: React.FC<TeamMemberResponseRateModalProps> = ({
  isOpen,
  onOpenChange,
  scheduleEvent,
  storeId,
  businessId,
}) => {
  const goTo = useGoTo();
  const [searchInput, setSearchInput] = useState("");
  const [acknowledgmentFilter, setAcknowledgmentFilter] = useState<AcknowledgmentFilter>("all");

  const handlePersonClick = useCallback(
    (personId: string) => {
      onOpenChange(false);
      goTo.viewPerson(businessId, storeId, personId);
    },
    [businessId, storeId, onOpenChange, goTo],
  );

  // Combine acknowledged and unacknowledged persons
  const allPersons = useMemo(() => {
    const acknowledged = scheduleEvent.personsAcknowledged || [];
    const unacknowledged = scheduleEvent.personsUnacknowledged || [];

    return [
      ...map(acknowledged, (person) => ({ ...person, isAcknowledged: true })),
      ...map(unacknowledged, (person) => ({ ...person, isAcknowledged: false })),
    ];
  }, [scheduleEvent.personsAcknowledged, scheduleEvent.personsUnacknowledged]);

  // Filter persons based on search and acknowledgment status
  const filteredPersons = useMemo(() => {
    let filtered = allPersons;

    // Filter by search input
    if (searchInput.trim()) {
      const searchLower = searchInput.toLowerCase();
      filtered = filtered.filter(
        (person) =>
          person.firstName?.toLowerCase().includes(searchLower) ||
          person.lastName?.toLowerCase().includes(searchLower) ||
          person.jobTitle?.toLowerCase().includes(searchLower),
      );
    }

    // Filter by acknowledgment status
    if (acknowledgmentFilter === "acknowledged") {
      filtered = filter(filtered, (person) => person.isAcknowledged);
    } else if (acknowledgmentFilter === "unacknowledged") {
      filtered = filter(filtered, (person) => !person.isAcknowledged);
    }

    return filtered;
  }, [allPersons, searchInput, acknowledgmentFilter]);

  const acknowledgedCount = allPersons.filter((p) => p.isAcknowledged).length;
  const totalCount = allPersons.length;

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent size={"xl"} className="overflow-y-auto min-h-[400px]" style={{ maxHeight: "calc(100vh - 100px)" }}>
        <DialogHeader>
          <DialogTitle>Response Rate</DialogTitle>
          <Text size="sm" muted>
            {acknowledgedCount}/{totalCount} team members have acknowledged this Announcement
          </Text>
        </DialogHeader>

        {/* Search and Filter Controls */}
        <div className="flex flex-row gap-3 mb-4">
          <div className="flex-1">
            <Input
              placeholder="Search"
              value={searchInput}
              onChange={(e) => setSearchInput(e.target.value)}
              leftIcon={SearchIcon}
            />
          </div>
          <div className="w-48">
            <Select
              value={acknowledgmentFilter}
              onValueChange={(value: AcknowledgmentFilter) => setAcknowledgmentFilter(value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Filter" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All</SelectItem>
                <SelectItem value="acknowledged">Acknowledged</SelectItem>
                <SelectItem value="unacknowledged">Unacknowledged</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        <ScrollArea className="max-h-[60vh]">
          <div className="space-y-2 pr-4">
            {map(filteredPersons, (person) => (
              <div onClick={() => handlePersonClick(person.id!)} key={person.id}>
                <TeamMemberPressable
                  person={person}
                  Subtext={
                    <Text size={"sm"} muted>
                      {person.jobTitle}
                    </Text>
                  }
                  RightElem={
                    <div className="flex-1 flex flex-row justify-between items-center gap-2">
                      <ProficiencyRating rank={person.proficiencyRanking ?? 0} colorScheme={"light-bg"} size={16} />
                      <Badge colorScheme={person.isAcknowledged ? "success" : "yellow"} size="sm">
                        {person.isAcknowledged ? "Acknowledged" : "Unacknowledged"}
                      </Badge>
                    </div>
                  }
                />
              </div>
            ))}

            {filteredPersons.length === 0 && (
              <Text className="text-center py-4 text-muted-foreground">
                {searchInput.trim() || acknowledgmentFilter !== "all"
                  ? "No team members match the current filters."
                  : "No team members found for this announcement."}
              </Text>
            )}
          </div>
        </ScrollArea>
      </DialogContent>
    </Dialog>
  );
};
