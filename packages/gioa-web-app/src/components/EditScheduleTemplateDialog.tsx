import React from 'react';
import {<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON>Title} from "@/src/components/ui/dialog.tsx";
import {Button} from "@/src/components/ui/button.tsx";
import {DialogDescription} from "@radix-ui/react-dialog";
import {zodValidator} from "@tanstack/zod-form-adapter";
import {z} from "zod";
import {useForm} from "@tanstack/react-form";
import {DraftSchedule} from "../../../api/src/scheduleSchemas.ts";
import {getMetrics} from "../../../api/src/scheduling/metrics/metrics.util.ts";
import {FormControl} from "@/src/components/form/FormControl.tsx";
import {Label} from "@/src/components/ui/label.tsx";
import {FormInput} from "@/src/components/form/FormInput.tsx";
import {FieldInfo} from "@/src/components/form/FieldInfo.tsx";
import {FormTextarea} from "@/src/components/form/FormTextarea.tsx";
import {Text} from "@/src/components/Text.tsx";
import {ValueBadge} from "@/src/components/ValueBadge.tsx";
import {api} from "@/src/api.ts";
import {toast} from "sonner";
import {to12HourTime} from '@gioa/api/src/date.util.ts';
import {payRates} from "../../../api/src/scheduling/metrics/payRates/payRates.ts";

export interface EditScheduleTemplateDialogProps {
  dayOfWeek: number;
  isOpen: boolean;
  onClose: () => void;
  onOpen: () => void;
  template: DraftSchedule;
}

export const EditScheduleTemplateDialog: React.FC<EditScheduleTemplateDialogProps> = (props) => {

  const updateScheduleTemplateMeta = api.user.updateScheduleTemplateMeta.useMutation();
  const templ = props.template;
  const metrics = getMetrics({
    schedule: templ,
    dayOfWeek: props.dayOfWeek,
    countOpenShiftsTowardsLabor: true,
    payRates: payRates(),
    averagePayRate: undefined
  });
  const apiUtil = api.useUtils();

  const form = useForm({
    defaultValues: {
      title: templ.title ?? '',
      description: templ?.description ?? '',
    },
    onSubmit: async ({value}) => {
      updateScheduleTemplateMeta.mutate({
        id: templ.id,
        title: value.title,
        description: value.description,
      }, {
        onSuccess: () => {
          toast.success(`Template "${templ?.title}" details were saved.`);
          props.onClose();
          apiUtil.user.getScheduleTemplates.invalidate();
        }
      });
    },
    validatorAdapter: zodValidator(),
  });

  const onCancel = () => {
    if (updateScheduleTemplateMeta.isPending) {
      return;
    }
    props.onClose();
  }

  return (
    <Dialog open={props.isOpen} onOpenChange={open => {
      if (open) {
        props.onOpen();
      } else {
        onCancel();
      }
    }}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Edit Template</DialogTitle>
          <DialogDescription>
            View and edit template details.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          <form onSubmit={(e) => {
            e.preventDefault()
            e.stopPropagation()
            form.handleSubmit()
          }}>
            <form.Field name={"title"}
                        validators={{
                          onSubmit: z.string().min(1)
                        }}
                        children={(field) => {
                          return <FormControl>
                            <Label htmlFor={field.name}>Title</Label>
                            <FormInput field={field}
                                       placeholder="Enter title..."/>
                            <FieldInfo field={field}/>
                          </FormControl>;
                        }}/>

            <div className={"grid grid-cols-2 gap-3 mb-3"}>
              <div>
                <Text size={"sm"}>Start time</Text>
                <ValueBadge>
                  {to12HourTime(templ.storeHours.start)}
                </ValueBadge>
              </div>
              <div>
                <Text size={"sm"}>End time</Text>
                <ValueBadge>
                  {to12HourTime(templ.storeHours.end)}
                </ValueBadge>
              </div>
              <div>
                <Text size={"sm"}>Total labor hours</Text>
                <ValueBadge>
                  {metrics.totalLaborHours}
                </ValueBadge>
              </div>
              <div>
                <Text size={"sm"}>Shift count</Text>
                <ValueBadge>
                  {metrics.shiftCount}
                </ValueBadge>
              </div>
            </div>

            <form.Field name={"description"}
                        validators={{
                          onSubmit: z.string().optional()
                        }}
                        children={(field) => {
                          return <FormControl>
                            <Label htmlFor={field.name}>Description (optional)</Label>
                            <FormTextarea field={field}
                                          placeholder="Enter description of this template..."/>
                            <FieldInfo field={field}/>
                          </FormControl>;
                        }}/>

            <DialogFooter>
              <Button variant={"outline"} type={"button"} disabled={updateScheduleTemplateMeta.isPending}
                      onClick={onCancel}>
                Cancel
              </Button>
              <Button type={"submit"} disabled={updateScheduleTemplateMeta.isPending}
                      isLoading={updateScheduleTemplateMeta.isPending}>
                Save
              </Button>
            </DialogFooter>
          </form>
        </div>
      </DialogContent>
    </Dialog>
  );
}
