import React, {MutableRefObject, useState} from 'react';
import {<PERSON><PERSON>, <PERSON>alogContent, Di<PERSON>Footer, DialogHeader, DialogTitle} from "@/src/components/ui/dialog.tsx";
import {But<PERSON>} from "@/src/components/ui/button.tsx";
import {DialogDescription} from "@radix-ui/react-dialog";
import {useForm} from "@tanstack/react-form";
import {DraftSchedule, PublishedSchedule, Shift} from "../../../api/src/scheduleSchemas.ts";
import {FormControl} from "@/src/components/form/FormControl.tsx";
import {Label} from "@/src/components/ui/label.tsx";
import {FieldInfo} from "@/src/components/form/FieldInfo.tsx";
import {capitalize, chain, filter, find, flatMap, isEmpty, map, reduce, some, values} from "lodash";
import {Text} from "@/src/components/Text.tsx";
import {
  ScheduleValidationResult,
  severityCritical,
  ValidationMessage
} from "../../../api/src/scheduleValidation.types.ts";
import {DailyTimeRange, IsoWeekDate} from "../../../api/src/timeSchemas.ts";
import {api} from "@/src/api.ts";
import {getHumanReadableErrorMessage} from "@/src/components/ErrorAlert.tsx";
import {FormTextarea} from "@/src/components/form/FormTextarea.tsx";
import {Checkbox} from './ui/checkbox.tsx';
import {ScheduleIssuesList} from "@/src/components/ScheduleIssuesList.tsx";
import {ChevronDown, TriangleAlertIcon} from "lucide-react";
import {daysOfWeek} from './AvailabilityWeek.tsx';
import {Table, TableBody, TableCell, TableRow} from "@/src/components/ui/table.tsx";
import {Accordion, AccordionContent, AccordionItem} from "@/src/components/ui/accordion.tsx";
import * as AccordionPrimitive from "@radix-ui/react-accordion";
import {cn} from "@/src/util.ts";
import {PersonAvatar} from "@/src/components/PersonAvatar.tsx";
import {CheckedState} from "@radix-ui/react-checkbox";
import {SchedulePersonDto} from "../../../api/src/schedulePersonDto.ts";
import {
  getChangeStatusColorMuted,
  getChangeStatusTextColor,
  getShiftChangeStatus,
  incrementToFriendlyTime,
  ScheduleShiftChangeStatus,
  shiftChangeStatusToLabel,
  storeTimeToIncrement
} from "../../../api/src/scheduleBuilder.util.ts";
import {getShift} from "../../../api/src/scheduling.util.ts";
import {getMessageShift, sortBySeverity} from "../../../api/src/scheduleValidation/scheduleValidationMessagesL1.ts";
import {
  isMessageForShiftorPerson,
  isMessageForWeekday,
  isMessageIgnorable
} from "../../../api/src/scheduleValidation/scheduleValidationMessagesL2.ts";
import {messageToHumanReadable} from "@/src/validationMessageDisplay.tsx";
import {getDateTimeFromWeekDayTime} from "../../../api/src/date.util.ts";
import {getShiftWeekday} from "../../../api/src/schedule.ts";

export interface PublishDayDialogProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  onGoToMessage: (week: IsoWeekDate, dayOfWeek: number, msg: ValidationMessage) => void;
  onIgnoreMessage: (msg: ValidationMessage) => void;
  onUnignoreMessage: (msg: ValidationMessage) => void;
  validationResult?: ScheduleValidationResult;
  schedule: DraftSchedule;
  draftVersion: MutableRefObject<number>;
  published?: PublishedSchedule;
  people: SchedulePersonDto[];
  storeHours: DailyTimeRange;
  onPublished: (numShifts: number) => void;
  timezone: string;
  dayOfWeek: number;
  routeFullPath: string;
}

export const PublishDayDialog: React.FC<PublishDayDialogProps> = ({
                                                                    isOpen,
                                                                    onOpenChange,
                                                                    onGoToMessage, draftVersion,
                                                                    onIgnoreMessage,
                                                                    validationResult,
                                                                    schedule, published,
                                                                    people, onUnignoreMessage,
                                                                    storeHours, dayOfWeek,
                                                                    onPublished, timezone, routeFullPath
                                                                  }) => {
  const [isOverriden, setIsOverriden] = useState(false);
  const getMsg = messageToHumanReadable({
    people: people,
    activeDay: getDateTimeFromWeekDayTime({
      year: schedule.week.year,
      week: schedule.week.week,
      day: dayOfWeek,
      time: "00:00",
      timezone
    }),
    routeFullPath: routeFullPath
  });
  const apiUtil = api.useUtils();

  const publish = api.user.publishPartialDraftSchedule.useMutation();
  const form = useForm({
    defaultValues: {
      changeReason: ""
    },
    onSubmit: async ({value}) => {
      // if there are any critical errors, don't publish
      const criticalErrors = filter(validationResult?.messages, m => m.severity >= severityCritical);
      if (!isEmpty(criticalErrors)) {
        alert("There are critical errors in your schedule. Please fix them before publishing: " + map(criticalErrors, (m, idx) => (idx + 1) + ". " + getMsg(m)).join(", "));
        return;
      }

      const day = find(schedule.days, d => d.dayOfWeek === dayOfWeek);
      if (!day) {
        return;
      }
      const allShifts = flatMap(day.areas, area => area.shifts);
      const shiftsToExclude = filter(allShifts, s => !Boolean(selectedShifts[s.id]));

      publish.mutate({
        scheduleId: schedule.id,
        changeReason: value.changeReason || undefined,
        dayOfWeek: dayOfWeek,
        excludeShiftIds: map(shiftsToExclude, s => s.id),
        draftVersion: draftVersion.current
      }, {
        onSuccess: async () => {
          apiUtil.user.getSchedule.invalidate();
          apiUtil.user.getScheduleWeeks.invalidate();
          onPublished(numSelectedPublishableShifts);
        },
        onError: (error) => {
          console.error("Error publishing schedule:", error);
          alert("Error publishing schedule. Please refresh the page and try again: " + getHumanReadableErrorMessage(error));
        }
      });
    },
  })

  const day = find(schedule.days, d => d.dayOfWeek === dayOfWeek);
  const dayOfWeekObj = find(daysOfWeek, d => d.dayOfWeek === dayOfWeek);

  const [selectedShifts, setSelectedShifts] = useState<{ [shiftId: string]: boolean }>({});
  const onShiftChecked = (shiftId: string, checked: CheckedState) => {
    setSelectedShifts(prev => ({...prev, [shiftId]: Boolean(checked)}));
  }
  const onShiftToggled = (shiftId: string) => {
    setSelectedShifts(prev => ({...prev, [shiftId]: !prev[shiftId]}));
  }

  const anyShiftsSelected = some(values(selectedShifts), v => v);
  const isShiftPublishableImpl = (shift: Shift, status: ScheduleShiftChangeStatus) => {
    const isPublished = status === "unchanged";
    const isEdited = status === "updated";
    const isAssigned = Boolean(shift.assignedPersonId);
    // if the shift is assigned, it can be published to that person
    // if the shift is not assigned and edited, then it must have been unassigned in the draft
    return (isAssigned && !isPublished) || (!isAssigned && isEdited);
  }
  const isShiftPublishable = (shift: Shift) => {
    const publishedShift = published ? getShift(published, shift.id) : undefined;
    const shiftChangeStatus = getShiftChangeStatus({
      draftShift: shift,
      draftShiftDay: getShiftWeekday(schedule, shift.id),
      publishedShift: publishedShift,
      publishedShiftDay: published ? getShiftWeekday(published, shift.id) : undefined
    });
    return isShiftPublishableImpl(shift, shiftChangeStatus);
  }
  const weekdayMessages = filter(validationResult?.messages ?? [], isMessageForWeekday(dayOfWeek));
  const messages = filter(weekdayMessages, msg => {
    const shift = getMessageShift(msg);
    return (!anyShiftsSelected || (!shift || (selectedShifts[shift.id] && isShiftPublishable(shift))));
  });
  const allShifts = flatMap(day?.areas, area => area.shifts);
  const numSelectedPublishableShifts = filter(allShifts, shift => {
    return selectedShifts[shift.id] && isShiftPublishable(shift);
  }).length;

  const selectAll = () => {
    const allPublishableShifts = filter(allShifts, shift => isShiftPublishable(shift));

    setSelectedShifts(prev => {
      return reduce(allPublishableShifts, (acc, shift) => {
        acc[shift.id] = true;
        return acc;
      }, {} as Record<string, boolean>);
    })
  }

  // A lot of calculation and rendering activity happens in this modal even if isOpen = false. So save CPU and battery usage by not rendering the modal until it's open.
  if (!isOpen) {
    return null;
  }

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent size={"3xl"} className={"overflow-auto !block"} style={{height: "calc(100vh - 50px)"}}>
        <DialogHeader className={"mb-4"}>
          <DialogTitle>Publish {dayOfWeekObj?.name} of Week {schedule.week.week}</DialogTitle>
          <DialogDescription>
            Select areas and shifts to publish.
          </DialogDescription>
        </DialogHeader>

        <Button onClick={selectAll} variant={"outline"} className={"mb-2"} size={"sm"}>
          Select All
        </Button>

        <Accordion type="multiple">
          {map(day?.areas, area => {
            const publishableShifts = filter(area.shifts, isShiftPublishable);
            const checkedShifts = filter(publishableShifts, s => Boolean(selectedShifts[s.id]));
            const isChecked = checkedShifts.length > 0 && checkedShifts.length === publishableShifts.length;
            const isIntermediate = checkedShifts.length > 0 && checkedShifts.length < publishableShifts.length;
            const checked = isChecked ? true : isIntermediate ? "indeterminate" : false;
            const disabled = publishableShifts.length === 0;

            return <AccordionItem value={area.id} key={area.id}>
              <div className={"flex items-center gap-3"}>

                <label htmlFor={"checkbox-area-" + area.id}
                       aria-label={"Select area " + area.title}
                       className={cn("pr-6 py-4 leading-none", {"cursor-pointer": !disabled})}>
                  <Checkbox disabled={disabled}
                            checked={checked} className={cn({"!opacity-30": disabled})}
                            onCheckedChange={checked => {
                              setSelectedShifts(prev => {
                                return reduce(area.shifts, (acc, shift) => {
                                  return {
                                    ...acc,
                                    [shift.id]: Boolean(checked) && isShiftPublishable(shift)
                                  }
                                }, prev);
                              });
                            }}
                            id={"checkbox-area-" + area.id}/>
                </label>
                <AccordionPrimitive.Header className="flex grow">
                  <AccordionPrimitive.Trigger className={cn(
                    "flex flex-1 items-center justify-between py-4 font-medium transition-all hover:bg-gray-50 [&[data-state=open]>svg]:rotate-180",
                  )}>
                    <span className={"align-middle"}>
                    {area.title}

                      <span className={"text-sm text-muted-foreground ml-3"}>
                    {area.shifts.length} shifts
                    </span>
                    </span>
                    <ChevronDown className="h-4 w-4 shrink-0 transition-transform duration-200"/>
                  </AccordionPrimitive.Trigger>
                </AccordionPrimitive.Header>
              </div>
              <AccordionContent>
                <Table className={"w-auto overflow-hidden"}>
                  <TableBody>
                    {map(area.shifts, shift => {
                      const publishedShift = published ? getShift(published, shift.id) : undefined;
                      const person = find(people, p => p.id === shift.assignedPersonId);
                      const shiftChangeStatus = getShiftChangeStatus({
                        draftShift: shift,
                        draftShiftDay: day?.dayOfWeek ?? 1,
                        publishedShift: publishedShift,
                        publishedShiftDay: published ? getShiftWeekday(published, shift.id) : undefined
                      });
                      const shiftIssues = chain(validationResult?.messages ?? [])
                        .filter(isMessageForShiftorPerson({
                          id: shift.id,
                          assignedPersonId: shift.assignedPersonId ?? undefined
                        }))
                        .filter(isMessageIgnorable)
                        .sortBy(sortBySeverity)
                        .value();
                      const isSelected = Boolean(selectedShifts[shift.id]);
                      const onToggled = () => onShiftToggled(shift.id);
                      const isPublishable = isShiftPublishableImpl(shift, shiftChangeStatus);
                      const disabled = !isPublishable;
                      const cursor = disabled ? "cursor-not-allowed" : "cursor-pointer";

                      return <TableRow key={shift.id}>
                        <TableCell className={cn("py-2", cursor)}>
                          <label htmlFor={"checkbox-shift-" + shift.id}
                                 aria-label={"Select shift"}
                                 className={cn("p-4 leading-none", {"cursor-pointer": !disabled})}>
                            <Checkbox id={"checkbox-shift-" + shift.id} className={cn({"!opacity-30": disabled})}
                                      checked={isSelected} disabled={disabled}
                                      onCheckedChange={checked => onShiftChecked(shift.id, checked)}/>
                          </label>
                        </TableCell>
                        <TableCell className={cn(" py-2", cursor)} onClick={() => {
                          if (!disabled) {
                            onToggled();
                          }
                        }}>
                          {incrementToFriendlyTime(storeHours, storeTimeToIncrement(storeHours, shift.range.start))}{' '}-{' '}
                          {incrementToFriendlyTime(storeHours, storeTimeToIncrement(storeHours, shift.range.end))}
                        </TableCell>
                        <TableCell className={cn(" py-2", cursor)}
                                   onClick={() => {
                                     if (!disabled) {
                                       onToggled();
                                     }
                                   }}>
                          {person ?
                            <div className={"flex items-center gap-2"}>
                              <PersonAvatar size={"xs"} person={person}/>
                              <span>
                            {person?.firstName} {person?.lastName}
                          </span>
                            </div> : <span>Open shift</span>}
                        </TableCell>
                        <TableCell className={cn(" py-2", cursor)} onClick={() => {
                          if (!disabled) {
                            onToggled();
                          }
                        }}>
                          {shiftIssues.length > 0 ? <TriangleAlertIcon size={16} className={"text-amber-500"}/> : null}
                        </TableCell>
                        <TableCell className={cn(" py-2", cursor)} onClick={() => {
                          if (!disabled) {
                            onToggled();
                          }
                        }}>
                          <div style={{backgroundColor: getChangeStatusColorMuted(shiftChangeStatus)}}
                               className={cn("text-sm px-2 py-0.5 inline-flex items-center rounded-full border",
                                 {
                                   "border-gray-200": shiftChangeStatus === "created",
                                   "border-transparent": shiftChangeStatus !== "created"
                                 },
                                 getChangeStatusTextColor(shiftChangeStatus))}>
                            {capitalize(shiftChangeStatusToLabel(shiftChangeStatus))}
                          </div>
                        </TableCell>
                      </TableRow>
                    })}
                  </TableBody>
                </Table>
              </AccordionContent>
            </AccordionItem>;
          })}
        </Accordion>

        <h2 className={"font-semibold text-lg pt-6"}>
          Issues
        </h2>

        {messages.length > 0 ?
          <div>
            {numSelectedPublishableShifts > 0 ?
              `Showing issues for the ${numSelectedPublishableShifts} selected shifts to publish.`
              : "Showing issues for all shifts."}
          </div> : null}

        <ScheduleIssuesList warnings={messages} schedule={schedule} showTimes
                            noIssuesLabel={numSelectedPublishableShifts > 0 ? "No issues found for the selected shifts." : "No issues found for this day."}
                            people={people} storeHours={schedule.storeHours} className={"mt-2 rounded-lg"}
                            onIgnoreMessage={onIgnoreMessage} onUnignoreMessage={onUnignoreMessage}
                            onGoToMessage={onGoToMessage}/>

        <form className={"mt-6"} onSubmit={(e) => {
          e.preventDefault()
          e.stopPropagation()
          form.handleSubmit()
        }}>
          <fieldset disabled={publish.isPending}>
            {!isEmpty(messages) ?
              <label htmlFor={"gioa-override-errors"}
                     className={"mt-2 mb-4 flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4 bg-white"}>
                <Checkbox checked={isOverriden}
                          id={"gioa-override-errors"}
                          onCheckedChange={state => setIsOverriden(typeof state === "boolean" ? state : false)}/>
                <div className="space-y-1 leading-none">
                  <div className={"block"}>
                    Ignore issues
                  </div>
                  <Text size={"sm"} className={"block"}>
                    Select to allow publishing the schedule despite issues.
                  </Text>
                </div>
              </label> : null}

            <form.Field name={"changeReason"}
                        children={field => <FormControl className={"mb-6"}>
                          <Label>Comments (optional)</Label>
                          <FormTextarea field={field} rows={1}
                                        placeholder="Leave a note about what changed"/>
                          <FieldInfo field={field}/>
                        </FormControl>}/>

            <DialogFooter className={"sm:justify-between"}>
              <Button variant={"outline"} type={"button"}
                      onClick={() => onOpenChange(false)}>
                Close
              </Button>
              <Button type={"submit"} isLoading={publish.isPending}
                      disabled={(!isEmpty(messages) && !isOverriden)}>
                Publish {numSelectedPublishableShifts > 0 ? `${numSelectedPublishableShifts} shift${numSelectedPublishableShifts > 1 ? "s" : ""}` : ""}
              </Button>
            </DialogFooter>
          </fieldset>
        </form>
      </DialogContent>
    </Dialog>
  );
}
