import React from 'react';
import {getRangeDurationHours, to12HourTime} from "../../../api/src/date.util";
import {DailyTimeRange} from "../../../api/src/timeSchemas";
import {DurationText} from "@/src/components/DurationText.tsx";

export interface ShiftCardContentProps {
  shift: { range: DailyTimeRange };
  children?: React.ReactNode;
  className?: string;
  size?: "sm" | "md" | "lg";
}

export const ShiftCardContent: React.FC<ShiftCardContentProps> = ({shift, children, className, size = "md"}) => {
  const durationHours = getRangeDurationHours(shift.range);

  return (
    <div>
      <div className={className}>
        {children}
      </div>

      <div className={className}>
        {to12HourTime(shift.range.start)}{' '}-{' '}
        {to12HourTime(shift.range.end)}{' '}
        (<DurationText durationHours={durationHours} size={size}/>)
      </div>
    </div>
  );
}
