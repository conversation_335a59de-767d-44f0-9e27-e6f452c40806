import React from "react";
import { TimePunchUploadWizard } from "./TimePunchUploadWizard.tsx";

interface TimePunchUploadModalProps {
  isOpen: boolean;
  onClose: () => void;
  storeId: string;
  onSuccess?: () => void;
}

export function TimePunchUploadModal({ isOpen, onClose, storeId, onSuccess }: TimePunchUploadModalProps) {
  return (
    <TimePunchUploadWizard
      isOpen={isOpen}
      onClose={onClose}
      storeId={storeId}
      onSuccess={onSuccess}
    />
  );
}
