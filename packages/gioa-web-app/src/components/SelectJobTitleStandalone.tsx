import React, {useState} from "react";
import {find, keys, pickBy} from "lodash";
import { cn } from "@/src/util.ts";
import {Briefcase} from "lucide-react";
import { Button } from "@/src/components/ui/button.tsx";
import { api } from "@/src/api.ts";
import {CheckedIds} from "@/src/components/PolicyEditor.tsx";
import { SelectJobTitle } from "./SelectJobTitle.tsx";
import {toast} from "sonner";
import {PackageId} from "@gioa/api/src/permission.types";

interface SelectJobTitleStandaloneProps {
  personId: string;
  storeId: string;
  placeholder?: string;
  className?: string;
  skipPermissions?: boolean;
  onJobUpdated?: () => void;
}

/**
 * This is a version of FormSelectJobTitle that is not intended to be a form component.  It is self-contained
 * and will update the team member's job title directly.
 */
export const SelectJobTitleStandalone: React.FC<SelectJobTitleStandaloneProps> = ({
  personId,
  storeId,
  placeholder = "Select Job Title...",
  className,
  skipPermissions = false,
  onJobUpdated,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const apiUtil = api.useUtils();

  const { data: business } = api.user.getBusiness.useQuery();
  const { data: person } = api.user.getPersonDetail.useQuery(
    { personId, storeId },
    { enabled: !!personId && !!storeId },
  );

  const editEmployeeJob = api.user.editEmployeeJob.useMutation({
    onSuccess: async () => {
      await apiUtil.user.getPersonDetail.invalidate({ personId, storeId });
      onJobUpdated?.();
    },
    onError: (error) => {
      toast.error(`Error updating job title: ${error.message}`, {
        position: "top-center",
      });
    },
  });

  const selectedJobId = person?.jobId;
  const selectedJob = business?.jobs ? find(business.jobs, (job) => job.id === selectedJobId) : undefined;

  const handleSelectJob = (jobId: string, permissionPackages: CheckedIds) => {
    editEmployeeJob.mutateAsync(
      {
        personId,
        jobId,
        permissionPackages: keys(pickBy(permissionPackages, (v) => v)) as PackageId[],
      },
      {
        onSuccess: () => setIsOpen(false),
      },
    );
  };

  return (
    <div className="space-y-2">
      <Button
        type="button"
        variant="outline"
        className={cn("w-full justify-between", className)}
        onClick={() => setIsOpen(true)}
      >
        <div className="flex items-center">
          <Briefcase className="mr-2 h-4 w-4" />
          <span>{selectedJob?.title || placeholder}</span>
        </div>
      </Button>

      {isOpen && (
        <SelectJobTitle
          initialJobId={selectedJobId}
          personName={person ? `${person.firstName} ${person.lastName}` : undefined}
          skipPermissions={skipPermissions}
          onSelect={handleSelectJob}
          onCancel={() => setIsOpen(false)}
          isPending={editEmployeeJob.isPending}
        />
      )}
    </div>
  );
};

export default SelectJobTitleStandalone;
