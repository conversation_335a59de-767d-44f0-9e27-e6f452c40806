import React, {useRef} from 'react';
import {useRanger} from '@tanstack/react-ranger';
import {DailyTimeRange} from "../../../api/src/timeSchemas.ts";
import {cn} from "@/src/util.ts";
import {minutesTo24HourTime, to12HourTime} from "../../../api/src/date.util.ts";
import {Label} from "@/src/components/ui/label.tsx";

interface IncrementRange {
  start: number;
  end: number;
}

export type RangerVals = [start: number, end: number];

export interface ShiftActivityRangerProps {
  shiftRange: RangerVals;
  value: RangerVals;
  onChange: (value: RangerVals) => void;
  storeHours: DailyTimeRange;
}

export const ShiftActivityRanger = (props: ShiftActivityRangerProps) => {
  const rangerRef = useRef(null);

  const rangerInstance = useRanger({
    getRangerElement: () => rangerRef.current,
    values: props.value,
    min: props.shiftRange[0],
    max: props.shiftRange[1],
    stepSize: 5,
    onChange: (instance) => props.onChange(instance.sortedValues as RangerVals),
  });

  const shiftDurationMinutes = props.shiftRange[1] - props.shiftRange[0];
  const tickDuration = shiftDurationMinutes > 4 * 60 ? 60 : 30;

  return (
    <div className={"mb-16"}>
      <Label className={"mb-6 block"}>Time Range</Label>

      <div className={"px-4"}>
        {/* Track */}
        <div
          ref={rangerRef}
          className="relative h-1 bg-gray-300 shadow-inner rounded-sm select-none"
        >
          {/* Ticks */}
          {rangerInstance.getTicks().map(({value, key, percentage}) => {
            const relativeValue = value - props.shiftRange[0];
            if (relativeValue % tickDuration !== 0) {
              return null;
            }

            return (
              <div
                key={key}
                className="absolute top-1"
                style={{left: `${percentage}%`, transform: 'translateX(-50%)'}}
              >
                {/* Tick Mark */}
                <div
                  className="absolute left-0 w-0.5 h-1 bg-black/20"
                  style={{transform: 'translate(-50%, 0.7rem)'}}
                />
                {/* Tick Label */}
                <div
                  className="absolute text-xs text-gray-500 whitespace-nowrap"
                  style={{transform: 'translate(-50%, 1.2rem)'}}
                >
                  {to12HourTime(minutesTo24HourTime(value), true)}
                </div>
              </div>
            );
          })}

          {/* Segments */}
          {rangerInstance.getSteps().map(({left, width}, i) => (
            <div
              key={i}
              className={cn("absolute h-full", {
                "bg-slate-500": i === 0 || i === 2,
                "bg-blue-500": i === 1,
              })}
              style={{
                left: `${left}%`,
                width: `${width}%`,
              }}
            />
          ))}

          {/* Handles */}
          {rangerInstance.handles().map(
            ({value, onKeyDownHandler, onMouseDownHandler, onTouchStart, isActive}, i) => (
              <button type={"button"}
                      key={i}
                      onKeyDown={onKeyDownHandler}
                      onMouseDown={onMouseDownHandler}
                      onTouchStart={onTouchStart}
                      role="slider"
                      aria-valuemin={rangerInstance.options.min}
                      aria-valuemax={rangerInstance.options.max}
                      aria-valuenow={value}
                      className={`absolute flex items-center justify-center px-1 py-1 text-xs text-white font-normal rounded-full bg-gioaBlue outline-none appearance-none whitespace-nowrap transition-transform ${
                        isActive ? 'z-10' : 'z-0'
                      }`}
                      style={{
                        left: `${rangerInstance.getPercentageForValue(value)}%`,
                        transform: isActive
                          ? 'translate(-50%, -100%) scale(1.3)'
                          : 'translate(-50%, -50%) scale(1)'
                      }}
              >
                {to12HourTime(minutesTo24HourTime(value))}
              </button>
            )
          )}
        </div>
      </div>
    </div>
  );
};

