import React, { useCallback } from "react";
import { <PERSON><PERSON>, Dialog<PERSON>ontent, DialogHeader, DialogTitle } from "@/src/components/ui/dialog";
import { Text } from "@/src/components/Text";
import { Badge } from "@/src/components/ui/badge";
import { ScrollArea } from "@/src/components/ui/scroll-area";
import { TeamMemberPressable } from "@/src/components/TeamMemberPressable";
import { PersonDto } from "../../../api/src/schemas";
import { map } from "lodash";
import { ProficiencyRating } from "@/src/components/ProficiencyRating.tsx";
import { useGoTo } from "@/src/navigationUtils.ts";

interface UnacknowledgedTeamMembersModalProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  unacknowledgedPersons: PersonDto[];
  storeId: string;
  businessId: string;
}

export const UnacknowledgedTeamMembersModal: React.FC<UnacknowledgedTeamMembersModalProps> = ({
  isOpen,
  onOpenChange,
  unacknowledgedPersons,
  storeId,
  businessId,
}) => {
  const goTo = useGoTo();
  const handlePersonClick = useCallback(
    (personId: string) => {
      onOpenChange(false);
      goTo.viewPerson(businessId, storeId, personId);
    },
    [businessId, storeId],
  );

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent size={"xl"} className="overflow-y-auto min-h-[400px]" style={{ maxHeight: "calc(100vh - 100px)" }}>
        <DialogHeader>
          <DialogTitle>Team Members</DialogTitle>
        </DialogHeader>

        <div className="mb-2">
          <Badge colorScheme="yellow">Unacknowledged</Badge>
        </div>

        <ScrollArea className="max-h-[60vh]">
          <div className="space-y-2 pr-4">
            {map(unacknowledgedPersons, (person) => (
              <div onClick={() => handlePersonClick(person.id!)} key={person.id}>
                <TeamMemberPressable
                  person={person}
                  Subtext={
                    <Text size={"sm"} muted>
                      {person.jobTitle}
                    </Text>
                  }
                  RightElem={
                    <ProficiencyRating rank={person.proficiencyRanking ?? 0} colorScheme={"light-bg"} size={16} />
                  }
                />
              </div>
            ))}

            {unacknowledgedPersons.length === 0 && (
              <Text className="text-center py-4 text-muted-foreground">
                All team members have acknowledged this announcement.
              </Text>
            )}
          </div>
        </ScrollArea>
      </DialogContent>
    </Dialog>
  );
};
