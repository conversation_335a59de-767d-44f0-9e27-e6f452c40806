import {Dialog, DialogDescription} from "@radix-ui/react-dialog";
import {<PERSON><PERSON><PERSON>ontent, Di<PERSON>Header, DialogTitle} from "@/src/components/ui/dialog.tsx";
import React, {useEffect} from "react";
import {useForm} from "@tanstack/react-form";
import {Label} from "@/src/components/ui/label.tsx";
import {FieldInfo} from "@/src/components/form/FieldInfo.tsx";
import {Text} from "@/src/components/Text.tsx";
import {Button} from "@/src/components/ui/button.tsx";
import {FormSwitch} from "@/src/components/form/FormSwitch.tsx";
import {FormCheckbox} from "@/src/components/form/FormCheckbox.tsx";

export function ExportScheduleDataSelectorDialog({isOpen, onOpenChange, onExport}: {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  onExport: (options: {
    availability: boolean,
    scheduled: boolean,
    adminNonOps: boolean,
    shifts: boolean,
    shiftSwaps: boolean,
    shiftOffers: boolean,
    timeOff: boolean,
    itemizeShifts: boolean,
  }) => void;
}) {
  const form = useForm({
    defaultValues: {
      availability: true,
      scheduled: true,
      adminNonOps: true,
      shifts: true,
      shiftSwaps: true,
      shiftOffers: true,
      timeOff: true,
      itemizeShifts: false,
    },
    onSubmit: async ({value}) => {
      onExport(value);
    }
  })

  const isItemizeShiftsEnabled = form.useStore(s => s.values.itemizeShifts);

  // Watch form values to determine if any insights are selected
  const formValues = form.useStore(s => s.values);
  const hasInsightsSelected = formValues.availability || formValues.scheduled || formValues.adminNonOps ||
                              formValues.shifts || formValues.shiftSwaps || formValues.shiftOffers || formValues.timeOff;

  // Reset form to default values when modal opens
  useEffect(() => {
    if (isOpen) {
      form.reset();
    }
  }, [isOpen, form]);

  return (
          <Dialog open={isOpen} onOpenChange={onOpenChange}>
            <DialogContent className="max-w-sm">
              <DialogHeader>
                <DialogTitle>Export Insights</DialogTitle>
                <DialogDescription>
                  <Text muted size={"sm"}>
                    Select the areas you wish to export from Scheduling Insights
                  </Text>
                </DialogDescription>
              </DialogHeader>

              <div className="space-y-5">
                <div className={"space-y-2"}>
                  <form.Field name={"availability"} children={(field) => {
                    return <div className={"rounded-md border py-2 px-3"}>
                      <FormCheckbox field={field} label="Availability" />
                      <FieldInfo field={field}/>
                    </div>
                  }}/>

                  <form.Field name={"scheduled"} children={(field) => {
                    return <div className={"rounded-md border py-2 px-3"}>
                      <FormCheckbox field={field} label="Scheduled Hrs" />
                      <FieldInfo field={field}/>
                    </div>
                  }}/>

                  <form.Field name={"adminNonOps"} children={(field) => {
                    return <div className={"rounded-md border py-2 px-3"}>
                      <FormCheckbox field={field} label="Admin/Non-Ops Hrs" />
                      <FieldInfo field={field}/>
                    </div>
                  }}/>

                  <form.Field name={"shifts"} children={(field) => {
                    return <div className={"rounded-md border py-2 px-3"}>
                      <FormCheckbox field={field} label="Shifts" />
                      <FieldInfo field={field}/>
                    </div>
                  }}/>

                  <form.Field name={"shiftSwaps"} children={(field) => {
                    return <div className={"rounded-md border py-2 px-3"}>
                      <FormCheckbox field={field} label="Shift Swaps" />
                      <FieldInfo field={field}/>
                    </div>
                  }}/>

                  <form.Field name={"shiftOffers"} children={(field) => {
                    return <div className={"rounded-md border py-2 px-3"}>
                      <FormCheckbox field={field} label="Shift Offers" />
                      <FieldInfo field={field}/>
                    </div>
                  }}/>

                  <form.Field name={"timeOff"} children={(field) => {
                    return <div className={"rounded-md border py-2 px-3"}>
                      <FormCheckbox field={field} label="Time Off" />
                      <FieldInfo field={field}/>
                    </div>
                  }}/>

                </div>

                <form.Field name={"itemizeShifts"} children={(field) => {
                  return <div className={"space-y-2"}>
                    <div className={"flex items-center justify-between rounded-md border py-2 px-3"}>
                      <Label htmlFor={field.name} className={"block"}>
                        {"Export Itemized Shifts"}
                      </Label>
                      <FormSwitch field={field}/>
                    </div>
                    {isItemizeShiftsEnabled ? <Text size={"xs"} colorScheme={"danger"}>
                      WARNING: Exporting itemized shifts for large date ranges can result in potentially very large
                      files.
                    </Text> : null}
                    <FieldInfo field={field}/>
                    <Text size={"xs"} muted>
                      Toggling this on will include itemized individual shifts for every selected section and for every
                      team member within the date range. This may significantly increase the export size.
                    </Text>
                  </div>
                }}/>
              </div>

              {/* Show hint when no insights are selected */}
              {!hasInsightsSelected && (
                <div className={"text-sm text-muted-foreground text-center py-2"}>
                  Please select at least one insight to export.
                </div>
              )}

              <div className={"space-x-3 flex justify-end"}>
                <Button variant={"outline"} onClick={() => onOpenChange(false)}>
                  Cancel
                </Button>
                <Button
                  onClick={form.handleSubmit}
                  disabled={!hasInsightsSelected}
                >
                  Export
                </Button>
              </div>
            </DialogContent>
          </Dialog>
  )
}
