import React from "react";
import {
  ChecklistTemplateItemDto,
  ItemInstructionDto,
  ItemRequirementDto,
  RequirementConditionalDto
} from "../../../api/src/checklists/checklistTemplate/checklistTemplateDtos.ts";
import {Accordion, AccordionContent, AccordionItem, AccordionTrigger,} from "@/src/components/ui/accordion";
import {Text} from "@/src/components/Text";
import {CheckIcon, FileIcon, FileTextIcon, HashIcon, ImageIcon, MessageSquareIcon, VideoIcon} from "lucide-react";
import {capitalize, map} from "lodash";
import {imageUrl} from "@/src/images";
import {FileAttachmentDto} from "../../../api/src/fileAttachment.dto.ts";

interface ChecklistItemReadOnlyViewProps {
  item: ChecklistTemplateItemDto;
}

function getRequirementIcon(requirement: ItemRequirementDto) {
  switch (requirement.type) {
    case 'addImage':
      return <ImageIcon size={16} className="text-blue-500" />;
    case 'writeComment':
      return <MessageSquareIcon size={16} className="text-green-500" />;
    case 'inputBoolean':
      return <CheckIcon size={16} className="text-purple-500" />;
    case 'inputNumber':
      return <HashIcon size={16} className="text-orange-500" />;
    default:
      return <FileIcon size={16} className="text-gray-500" />;
  }
}

function getRequirementLabel(requirement: ItemRequirementDto): string {
  switch (requirement.type) {
    case 'addImage':
      return 'Add Image';
    case 'writeComment':
      return 'Write Comment';
    case 'inputBoolean':
      return 'Yes/No Input';
    case 'inputNumber':
      return 'Number Input';
    default:
      return 'Unknown Requirement';
  }
}

function RequirementDisplay({ requirement }: { requirement: ItemRequirementDto }) {
  const label = getRequirementLabel(requirement);
  const icon = getRequirementIcon(requirement);

  return (
    <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
      {icon}
      <div className="flex-1">
        <Text className="font-medium">
          {label}
          {requirement.type === "inputNumber" && (
            <Text size="sm" className="text-muted-foreground font-normal" asChild="span">
              {" "}({capitalize((requirement as any).numberType || "number")})
            </Text>
          )}
        </Text>
        <Text size="xs" muted>
          {requirement.isRequired ? "Required" : "Optional"}
        </Text>
      </div>
    </div>
  );
}

function ConditionalRequirementDisplay({ requirement }: { requirement: ItemRequirementDto }) {
  if (requirement.type !== "inputBoolean") {
    return <RequirementDisplay requirement={requirement} />;
  }

  // Type assertion since we know it's a boolean requirement
  const booleanReq = requirement as ItemRequirementDto & { conditionals: RequirementConditionalDto[] };

  if (!booleanReq.conditionals?.length) {
    return <RequirementDisplay requirement={requirement} />;
  }

  return (
    <div className="space-y-3">
      <RequirementDisplay requirement={requirement} />

      {map(booleanReq.conditionals, (conditional, index) => (
        <div key={index} className="ml-4 pl-4 border-l-2 border-gray-200 space-y-2">
          <div className="flex items-center gap-2">
            <Text size="sm" className="font-semibold text-gray-800">
              If {conditional.if ? "Yes" : "No"}:
            </Text>
            <Text size="sm" className="text-gray-700">
              {conditional.thenText}
            </Text>
          </div>
          {map(conditional.thenRequirements, (req, reqIndex) => (
            <div key={reqIndex} className="ml-2">
              <RequirementDisplay requirement={req} />
            </div>
          ))}
        </div>
      ))}
    </div>
  );
}

function AttachmentDisplay({ attachment }: { attachment: FileAttachmentDto }) {
  if (!attachment) {
    return (
      <div className="flex items-center gap-2 p-3 bg-red-50 rounded-lg border border-red-200">
        <FileIcon size={16} className="text-red-500" />
        <Text size="sm" colorScheme={"danger"}>Invalid attachment</Text>
      </div>
    );
  }

  const renderFile = (label: string, filename?: string, icon?: React.ReactNode) => {
    return <div className="flex items-center gap-2 p-3 bg-gray-50 rounded-lg border">
      {icon || <FileIcon size={16} className="text-gray-500"/>}
      <Text size="sm" className="font-medium">
        {label} {filename || 'Unnamed file'}
      </Text>
    </div>
  };

  const renderImage = (attachment: FileAttachmentDto) => {
    if (!attachment.url) {
      return (
        <div className="flex items-center gap-2 p-3 bg-gray-50 rounded-lg border">
          <ImageIcon size={16} className="text-gray-500" />
          <Text size="sm" muted>Image: {attachment.filename || 'Unnamed file'} (No URL)</Text>
        </div>
      );
    }

    const imgUrl = imageUrl(attachment.url, { width: 200 });
    const fullImgUrl = imageUrl(attachment.url, { width: 1280 });

    return (
      <div className="border rounded-lg overflow-hidden bg-white">
        <a
          href={fullImgUrl}
          target="_blank"
          rel="noopener noreferrer"
          className="block hover:opacity-90 transition-opacity"
        >
          <img
            src={imgUrl}
            alt={attachment.filename || 'Attachment'}
            className="object-cover w-full max-w-xs h-32 mx-auto"
          />
        </a>
        {attachment.filename && (
          <div className="p-2 bg-gray-50">
            <Text size="xs" className="text-gray-600">{attachment.filename}</Text>
          </div>
        )}
      </div>
    );
  };

  switch (attachment.mediaType) {
    case "image":
      return renderImage(attachment);
    case "video":
      return renderFile("Video:", attachment.filename, <VideoIcon size={16} className="text-blue-500" />);
    case "document":
      return renderFile("Document:", attachment.filename, <FileTextIcon size={16} className="text-red-500" />);
    case "file":
      return renderFile("File:", attachment.filename);
    default:
      return renderFile("Unknown:", attachment.filename);
  }
}

function InstructionDisplay({ instruction }: { instruction: ItemInstructionDto }) {
  return (
    <div className="p-3 bg-blue-50 rounded-lg space-y-2">
      {instruction.text && (
        <div className="flex items-start gap-2">
          <FileTextIcon size={16} className="text-blue-500 mt-0.5 flex-shrink-0" />
          <Text size="sm" className="text-gray-700">
            {instruction.text}
          </Text>
        </div>
      )}

      {instruction.attachment && (
        <div className="ml-6">
          <AttachmentDisplay attachment={instruction.attachment} />
        </div>
      )}
    </div>
  );
}

export function ChecklistItemReadOnlyView({ item }: ChecklistItemReadOnlyViewProps) {
  const hasContent = (item.attachments?.length ?? 0) > 0 ||
                    (item.requirements?.length ?? 0) > 0 ||
                    (item.instructions?.length ?? 0) > 0;

  if (!hasContent) {
    return (
      <div className="py-2 px-3 border border-gray-200 rounded-lg shadow-sm">
        <Text>{item.title}</Text>
        {item.description && (
          <Text size="sm" muted>{item.description}</Text>
        )}
      </div>
    );
  }

  return (
    <Accordion type="single" collapsible className="w-full">
      <AccordionItem value={item.id} className="border border-gray-200 rounded-lg shadow-sm">
        <AccordionTrigger className="px-3 py-2 hover:no-underline font-normal">
          <div className="text-left">
            <Text>{item.title}</Text>
            {item.description && (
                    <Text size="sm" muted>{item.description}</Text>
            )}
          </div>
        </AccordionTrigger>

        <AccordionContent className="px-3 pb-3">
          <div className="space-y-4">
            {/* Main Attachments */}
            {item.attachments && item.attachments.length > 0 && (
              <div className="space-y-3 mt-1">
                <div className="flex items-center gap-2">
                  <ImageIcon size={16} className="text-blue-500" />
                  <Text size="sm" semibold className="text-gray-700">Attachments ({item.attachments.length})</Text>
                </div>
                <div className="space-y-3">
                  {map(item.attachments, (attachment, index) => (
                    <AttachmentDisplay key={index} attachment={attachment} />
                  ))}
                </div>
              </div>
            )}

            {/* Requirements */}
            {item.requirements && item.requirements.length > 0 && (
              <div className="space-y-2 mt-1">
                <Text size="sm" semibold className="text-gray-700">Requirements</Text>
                <div className="space-y-3">
                  {map(item.requirements, (requirement, index) => (
                    <ConditionalRequirementDisplay key={index} requirement={requirement} />
                  ))}
                </div>
              </div>
            )}

            {/* Instructions */}
            {item.instructions && item.instructions.length > 0 && (
              <div className="space-y-2 mt-1">
                <Text size="sm" semibold className="text-gray-700">Instructions</Text>
                <div className="space-y-2">
                  {map(item.instructions, (instruction, index) => (
                    <InstructionDisplay key={index} instruction={instruction} />
                  ))}
                </div>
              </div>
            )}
          </div>
        </AccordionContent>
      </AccordionItem>
    </Accordion>
  );
}
