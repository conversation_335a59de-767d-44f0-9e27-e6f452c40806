import {useForm} from '@tanstack/react-form';
import React, {useState} from 'react';
import {zodValidator} from '@tanstack/zod-form-adapter'
import {z} from "zod";
import {api} from "@/src/api.ts";
import {FormControl} from "@/src/components/form/FormControl.tsx";
import {Label} from "@/src/components/ui/label.tsx";
import {FieldInfo} from "@/src/components/form/FieldInfo.tsx";
import {find, map, times} from "lodash";
import {FormSelect} from "@/src/components/form/FormSelect.tsx";
import {to12HourTime, to24HourTime} from "@gioa/api/src/date.util.ts";
import {ExampleCalendar} from "@/src/ExampleCalendar.tsx";
import {Button} from "@/src/components/ui/button.tsx";
import {getHumanReadableErrorMessage} from "@/src/components/ErrorAlert.tsx";

import {toast} from "sonner";

export interface SettingsRequestRestrictionFormProps {
  storeId: string;
  restrictionType: "timeOff" | "availability";
}

const generateTimeOptions = () => {
  const options = [];
  for (let i = 0; i < 24; i++) {
    const hour = i.toString().padStart(2, '0');
    options.push(`${hour}:00`);
  }
  return options;
};

const timeOptions = map(generateTimeOptions(), time => {
  return {
    label: to12HourTime(time, false, ["AM", "PM"]),
    value: time
  };
});

const monthDayOptions = times(32, n => {
  const dayOfMonth = (n).toString();
  return {label: dayOfMonth + " day" + (n === 1 ? "" : "s"), value: dayOfMonth}
});

const weekdayOptions = [
  {label: "Monday", value: "1"},
  {label: "Tuesday", value: "2"},
  {label: "Wednesday", value: "3"},
  {label: "Thursday", value: "4"},
  {label: "Friday", value: "5"},
  {label: "Saturday", value: "6"},
  {label: "Sunday", value: "7"}
];

function getHighlightedRange({weekday, hour, windowDuration}: {
  weekday: number,
  hour: string,
  windowDuration: number
}): number[] {
  const start = weekday;
  return times(windowDuration + 1, d => {
    return start + d;
  })
}

export const SettingsRequestRestrictionForm: React.FC<SettingsRequestRestrictionFormProps> = ({storeId, restrictionType}) => {
  const { restrictionData, save, title } = (() => {
    switch (restrictionType) {
      case "timeOff":
        return {
          restrictionData: api.settings.getTimeOffRestrictions.useSuspenseQuery({ storeId: storeId }),
          save: api.settings.updateTimeOffRestrictions.useMutation(),
          title: "Time off"
        };
      case "availability":
        return {
          restrictionData: api.settings.getAvailabilityRestrictions.useSuspenseQuery({ storeId: storeId }),
          save: api.settings.updateAvailabilityRestrictions.useMutation(),
          title: "Availability"
        };
    }
  })();
  const [restrictions] = restrictionData;

  const apiUtil = api.useUtils();
  const form = useForm({
    defaultValues: {
      weekday: restrictions.weekday.toString(),
      hour: restrictions.hour,
      windowDuration: restrictions.windowDuration.toString()
    },
    validatorAdapter: zodValidator(),
    onSubmit: async ({value}) => {
      save.mutate({
        storeId,
        restrictions: {
          weekday: parseInt(value.weekday),
          hour: to24HourTime(parseInt(value.hour), 0),
          windowDuration: parseInt(value.windowDuration)
        }
      }, {
        onSuccess: () => {
          apiUtil.settings.invalidate();
          toast(`${title} restrictions updated`, {
            position: "top-center",
            dismissible: true
          })
        },
        onError: (error) => {
          alert(getHumanReadableErrorMessage(error));
        }
      })
    }
  });

  const weekday = form.useStore(state => state.values.weekday);
  const hour = form.useStore(state => state.values.hour);
  const windowDuration = form.useStore(state => state.values.windowDuration);
  const weekdayObj = find(weekdayOptions, o => o.value === weekday);

  const [testDate, setTestDate] = useState<Date>();

  return <div>
    <form onSubmit={e => {
      e.preventDefault();
      e.stopPropagation();
      form.handleSubmit();
    }}>
      <div className={"flex flex-row gap-2 flex-wrap"}>
        <form.Field name={"weekday"}
                    validators={{
                      onSubmit: z.string().min(1, "Required")
                    }}
                    children={field => {
                      return <FormControl>
                        <Label htmlFor={field.name}>Day</Label>
                        <FormSelect field={field}
                                    placeholder="Select week day..."
                                    options={weekdayOptions}/>
                        <FieldInfo field={field}/>
                      </FormControl>
                    }}/>

        <form.Field name={"hour"}
                    validators={{
                      onSubmit: z.string().min(1, "Required")
                    }}
                    children={field => {
                      return <FormControl>
                        <Label htmlFor={field.name}>Time</Label>
                        <FormSelect
                          field={field}
                          placeholder="Choose time..."
                          options={timeOptions}
                        />
                      </FormControl>
                    }}/>

        <form.Field name={"windowDuration"}
                    validators={{
                      onSubmit: z.string().min(1, "Required")
                    }}
                    children={field => {
                      return <FormControl>
                        <Label htmlFor={field.name}>Restriction Window</Label>
                        <div className={"flex flex-row gap-2"}>
                          <FormSelect field={field} className={"min-w-[10rem]"}
                                      placeholder="Select number of days..."
                                      options={monthDayOptions}/>
                          <Button type={"submit"} isLoading={save.isPending}>
                            Save
                          </Button>
                        </div>
                        <FieldInfo field={field}/>
                      </FormControl>
                    }}/>
      </div>

      <div className={"mb-3"}>
        <p className="mb-4">
          {title} requests must be submitted by{' '}
          <span className={"font-semibold underline"}>
          {weekdayObj?.label}
        </span>
          {' '}at{' '}
          <span className={"font-semibold underline"}>
        {to12HourTime(hour, false, ["AM", "PM"])}
      </span>
          {' '}at least{' '}
          <span className={"font-semibold underline"}>
          {windowDuration} day{parseInt(windowDuration) === 1 ? "" : "s"}
        </span>
          {' '}in advance.
        </p>

        <ExampleCalendar highlightedRange={getHighlightedRange({
          weekday: parseInt(weekday),
          hour,
          windowDuration: parseInt(windowDuration)
        })}/>

      </div>
    </form>
  </div>
}
