import React from 'react';
import { cn } from '../util';
import { Text } from './Text';
import { format } from 'date-fns';
import { Badge } from './ui/badge';
import { PersonNoteHeader } from './PersonNoteHeader';
import {PersonPositionPerformanceDataPointNoteDto} from "../../../api/src/schemas.ts";

export interface PersonPositionPerformanceDataPointNoteProps {
  note: PersonPositionPerformanceDataPointNoteDto;
}

export const PositionScoreBadgeAndPosition = ({ value, positionTitle, className }: {
  value: number;
  positionTitle: string;
  className?: string;
}) => {
  const scoreColor =
    value >= 4
      ? "bg-green-100 text-green-800"
      : value >= 3
        ? "bg-blue-100 text-blue-800"
        : value >= 2
          ? "bg-yellow-100 text-yellow-800"
          : "bg-red-100 text-red-800";

  return (
    <div className={cn("flex flex-row gap-2 items-center mt-2", className)}>
      <Badge className={scoreColor}>Score: {value}</Badge>
      <Text size="sm" muted>
        {positionTitle}
      </Text>
    </div>
  );
};

export const PersonPositionPerformanceDataPointNote = React.memo((props: PersonPositionPerformanceDataPointNoteProps) => {
  const { note } = props;

  return (
    <div className="px-4 py-3 mb-2 bg-white relative shadow-sm border border-gray-200 rounded-xl">
      <PersonNoteHeader note={note} />
      <div className="mt-2">
        <div className="flex flex-row items-center gap-1 mb-2">
          <Text muted className="flex-1 text-sm">
            {format(new Date(note.createdAt), "PP")} by {note.createdBy.firstName} {note.createdBy.lastName}
          </Text>
        </div>
        {note.note && <Text>{note.note}</Text>}
        {note.dataPoint && (
          <PositionScoreBadgeAndPosition
            value={note.dataPoint.value}
            positionTitle={note.positionTitle}
          />
        )}
      </div>
    </div>
  );
});
