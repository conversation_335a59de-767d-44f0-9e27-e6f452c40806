import React, {useCallback} from 'react';
import {<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON>Title} from "@/src/components/ui/dialog.tsx";
import {Button} from "@/src/components/ui/button.tsx";
import {useForm} from "@tanstack/react-form";
import {zodValidator} from "@tanstack/zod-form-adapter";
import {z} from "zod";
import {FormInput} from "@/src/components/form/FormInput.tsx";
import {Label} from "@/src/components/ui/label.tsx";
import {FieldInfo} from "@/src/components/form/FieldInfo.tsx";
import {NumberStepper} from "@/src/components/NumberStepper.tsx";

export interface AddCustomPositionModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (title: string, count: number) => void;
}

const addCustomPositionSchema = z.object({
  title: z.string().min(1, "Position title is required").max(512, "Position title is too long"),
  count: z.number().int().min(1, "Count must be at least 1").max(10, "Count cannot exceed 10")
});

export const AddCustomPositionDialog: React.FC<AddCustomPositionModalProps> = ({isOpen, onClose, onSave}) => {
  const form = useForm({
    defaultValues: {
      title: "",
      count: 1
    },
    onSubmit: async ({value}) => {
      onSave(value.title.trim(), value.count);
      form.reset();
      onClose();
    },
    validatorAdapter: zodValidator(),
    validators: {
      onSubmit: addCustomPositionSchema
    }
  });

  const handleCancel = useCallback(() => {
    form.reset();
    onClose();
  }, [form, onClose]);

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && handleCancel()}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Add Custom Position</DialogTitle>
        </DialogHeader>

        <div className="flex flex-col gap-6">
          <form.Field name="title">
            {(field) => (
              <div>
                <Label>Position Title</Label>
                <FormInput field={field} placeholder="Enter position name..." autoFocus/>
                <FieldInfo field={field}/>
              </div>
            )}
          </form.Field>

          <form.Field name="count">
            {(field) => (
              <div>
                <Label>Count</Label>
                <div className="py-2">
                  <NumberStepper value={field.state.value} onChange={field.handleChange} min={1} max={10}/>
                </div>
                <FieldInfo field={field}/>
              </div>
            )}
          </form.Field>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={handleCancel}>Cancel</Button>
          <Button onClick={form.handleSubmit}>Add</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

