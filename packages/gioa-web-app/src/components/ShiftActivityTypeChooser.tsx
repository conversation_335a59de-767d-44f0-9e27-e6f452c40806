import React from 'react';
import {FieldA<PERSON>} from "@tanstack/react-form";
import {ShiftActivityType} from "../../../api/src/scheduleSchemas.ts";
import {capitalize, keys, map} from "lodash";
import {cn} from "@/src/util.ts";
import {X} from "lucide-react";
import {useAutoAnimate} from '@formkit/auto-animate/react'
import {FieldInfo} from "@/src/components/form/FieldInfo.tsx";
import {edgesGradient} from "@/src/components/ShiftRowAvailabilityOverlay.tsx";
import {useFeatureFlag} from "@/src/hooks/useFeatureFlag.tsx";

export interface ShiftActivityTypeChooserProps {
  field: FieldApi<any, any, any, any>;
  onChange?: (activityType: ShiftActivityType | null) => void;
}

export const activityTypeToColorScheme: Record<ShiftActivityType, {
  containerClassName: string;
  shiftRowBg: string;
}> = {
  admin: {
    containerClassName: "bg-green-100 border-green-200 hover:bg-green-200",
    shiftRowBg: edgesGradient({
      show: true,
      rgb: [0, 155, 0],
      edgeOpacity: 0.15,
      middleOpacity: 0.15
    })
  },
  setups: {
    containerClassName: "bg-blue-100 border-blue-200 hover:bg-blue-200",
    shiftRowBg: edgesGradient({
      show: true,
      rgb: [0, 0, 255],
      edgeOpacity: 0.15,
      middleOpacity: 0.15
    })
  },
  breaks: {
    containerClassName: "bg-purple-100 border-purple-200 hover:bg-purple-200",
    shiftRowBg: edgesGradient({
      show: true,
      rgb: [155, 0, 155],
      edgeOpacity: 0.18,
      middleOpacity: 0.18
    })
  },
  training: {
    containerClassName: "bg-orange-100 border-orange-200 hover:bg-orange-200",
    shiftRowBg: edgesGradient({
      show: true,
      rgb: [255, 155, 0],
      edgeOpacity: 0.15,
      middleOpacity: 0.15
    })
  },
  custom: {
    containerClassName: "bg-gray-100 border-gray-200 hover:bg-gray-200",
    shiftRowBg: edgesGradient({
      show: true,
      rgb: [155, 155, 155],
      edgeOpacity: 0.20,
      middleOpacity: 0.20
    })
  },
}

const shiftActivityTypes = keys(activityTypeToColorScheme) as ShiftActivityType[];

export type LaborCountingConfig = "counts" | "doesNotCount" | "configurable"
export const activityToLaborCounting: Record<ShiftActivityType, LaborCountingConfig> = {
  admin: "configurable",
  setups: "counts",
  breaks: "doesNotCount",
  custom: "configurable",
  training: "configurable"
}

export const ShiftActivityTypeChooser: React.FC<ShiftActivityTypeChooserProps> = ({field, onChange}) => {
  const [autoContainerRef] = useAutoAnimate()
  const isActivitiesEnabled = useFeatureFlag("scheduleSetupActivities");
  const enabledActivities = isActivitiesEnabled ? shiftActivityTypes : ["admin", "custom"] as const;

  return <div ref={autoContainerRef} className={"mb-2"}>
    {!field.state.value
            ? <div className={"flex flex-row gap-4"}>
              {map(enabledActivities, activityType => {
                return <ActivityButton onClick={(activityType) => {
                  field.handleChange(activityType);
                  onChange?.(activityType);
                }} key={activityType}
                                       activityType={activityType}
                                       isToggledOn={false}/>
              })}
            </div>
            : <ActivityButton onClick={() => {
              field.handleChange(null);
              onChange?.(null);
            }}
                              activityType={field.state.value}
                              isToggledOn={true}/>}
    <FieldInfo field={field}/>
  </div>
}

function ActivityButton({activityType, onClick, isToggledOn}: {
  activityType: ShiftActivityType;
  isToggledOn: boolean;
  onClick: (activityType: ShiftActivityType) => void;
}) {
  return <button onClick={() => onClick(activityType)} type={"button"}
                 className={cn("flex-1 text-center border rounded-full py-2 px-1 flex flex-row gap-1 items-center justify-center",
                         {
                           "px-4": isToggledOn
                         },
                         activityTypeToColorScheme[activityType]?.containerClassName)}>
    {capitalize(activityType)}
    {isToggledOn ? <X size={16} className={"text-gray-700"}/> : null}
  </button>
}
