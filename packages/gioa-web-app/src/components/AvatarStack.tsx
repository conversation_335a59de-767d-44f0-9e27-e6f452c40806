import React from 'react';
import {PersonDto} from "../../../api/src/schemas";
import {map} from 'lodash';
import {cn} from "@/src/util";
import {Text} from "@/src/components/Text";
import {PersonAvatar} from "@/src/components/PersonAvatar";
import {Tooltip, TooltipContent, TooltipProvider, TooltipTrigger} from './ui/tooltip';
import {TooltipPortal} from '@radix-ui/react-tooltip';

type AvatarSize = "xs" | "sm" | "default" | "lg" | "xl" | "2xl" | "3xl" | "4xl";

type AvatarStackProps = {
  people: PersonDto[];
  size?: AvatarSize;
  overlap?: number;
  className?: string;
  title?: string;
  showLabel?: boolean;
  titleClassName?: string;
  labelTextSize?: "xs" | "sm" | "md" | "lg" | "xl";
};

function formatName(fullName: string): string {
  const [firstName, ...rest] = fullName.split(' ');
  if (rest.length === 0) return firstName;
  const lastInitial = rest[rest.length - 1][0];
  return `${firstName} ${lastInitial}.`;
}

export function AvatarGroup({people, size, overlap = 16, avatarClassName}: {
  people: PersonDto[],
  size?: AvatarSize,
  overlap: number;
  avatarClassName?: string;
}) {
  return (
    <TooltipProvider delayDuration={0}>
      <div className="relative flex">
        {map(people, (person, i) => {
          return <Tooltip key={person.id}>
            <TooltipTrigger asChild>
              <div
                key={person.id! + i}
                className="relative"
                style={{
                  marginLeft: i === 0 ? 0 : -overlap,
                  zIndex: people.length - i
                }}
              >
                <PersonAvatar
                  person={person}
                  size={size}
                  className={cn("border-2 border-white", avatarClassName)}
                />
              </div>
            </TooltipTrigger>
            <TooltipPortal>
              <TooltipContent>
                <p>
                  {person.firstName} {person.lastName}
                </p>
              </TooltipContent>
            </TooltipPortal>
          </Tooltip>
        })}
      </div>
    </TooltipProvider>
  );
}

export function AvatarStack({
                              people, titleClassName,
                              size = "default", labelTextSize = "sm",
                              overlap = 16,
                              className,
                              title,
                              showLabel = true,
                            }: AvatarStackProps) {
  if (!people.length) return null;

  // Single person case
  if (people.length === 1) {
    return (
      <div className={cn('flex items-center gap-1', className)}>
        <PersonAvatar person={people[0]} size={size}/>
        <div>
          {title && (
            <Text size="sm" className={cn("text-gray-700", titleClassName)}>
              {title}
            </Text>
          )}
          {showLabel
            ? <Text size={labelTextSize}>
              {people[0].firstName} {people[0].lastName}
            </Text> : null}
        </div>
      </div>
    );
  }

  // 2-3 people case
  if (people.length <= 3) {
    return (
      <div className={cn('flex items-center gap-1', className)}>
        <AvatarGroup people={people} size={size} overlap={overlap}/>
        <div>
          {title && (
            <Text size="sm" className={cn("text-gray-700", titleClassName)}>
              {title}
            </Text>
          )}
          {showLabel ?
            <Text size={labelTextSize}>
              {map(people, p => formatName(
                p.firstName + ' ' + p.lastName
              )).join(', ')}
            </Text> : null}
        </div>
      </div>
    );
  }

  // 3+ people case - show first 3 and count
  const visiblePeople = people.slice(0, 3);

  return (
    <div className={cn('flex items-center gap-1', className)}>
      <AvatarGroup people={visiblePeople} size={size} overlap={overlap}/>
      <div>
        {title && (
          <Text size="sm" className={cn("text-gray-700", titleClassName)}>
            {title}
          </Text>
        )}
        {showLabel ? <Text size={labelTextSize}>
          {people.length} Team Members
        </Text> : null}
      </div>
    </div>
  );
}
