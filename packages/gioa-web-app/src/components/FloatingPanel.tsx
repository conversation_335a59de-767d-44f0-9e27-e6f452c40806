import React, {useEffect, useRef, useState} from 'react';
import {Popover, PopoverContent} from "@/src/components/ui/popover";
import {AnimatePresence, motion} from "framer-motion";
import {DraftSchedule, ScheduleMetricsInput} from "../../../api/src/scheduleSchemas.ts";
import {api} from "@/src/api.ts";
import {debounce} from "lodash";
import Draggable, {DraggableEvent, DraggableEventHandler} from 'react-draggable';
import {cn} from "@/src/util.ts";
import {CalendarPlusIcon, LineChartIcon, UserCircleIcon, XIcon} from "lucide-react";
import {TeamMembersPanelContent} from "@/src/components/TeamMembersPanelContent.tsx";
import {MetricsPanelContent} from "@/src/components/MetricsPanelContent.tsx";
import {EventsPanelContent} from "@/src/components/EventsPanelContent.tsx";
import {PopoverAnchor} from "@radix-ui/react-popover";
import {StoreAreaDto} from "../../../api/src/schemas.ts";
import {DayOfWeek, IsoWeekDate} from "../../../api/src/timeSchemas.ts";
import {ScheduleEventDto} from "../../../api/src/scheduleEventSchemas.ts";
import {ScheduleBuilderMachineSnapshot, SendFn} from "@/src/components/ScheduleBuilder.machine.tsx";
import {useScheduleBuilderMachineUIState} from "@/src/hooks/useScheduleBuilderMachineUIState.tsx";
import {SchedulePersonClientDto} from "../../../api/src/schedulePersonDto.ts";
import {
  FocusOutsideEvent,
  PointerDownOutsideEvent,
  ScheduleBuilderEvent,
  ScheduleRowInfo
} from "../../../api/src/scheduleBuilder.util.ts";
import {ScheduleValidationSettings} from "../../../api/src/scheduleValidation.types.ts";

interface FloatingPanelProps {
  people: SchedulePersonClientDto[]
  shift?: ScheduleRowInfo;
  onSelectTeamMember: (person: SchedulePersonClientDto) => void;
  schedule: DraftSchedule;
  storeAreas: StoreAreaDto[];
  settings: ScheduleValidationSettings;
  events: ScheduleBuilderEvent[];
  dayOfWeek: DayOfWeek;
  isoWeek: IsoWeekDate;
  onUpsertEvent: (event: ScheduleEventDto) => void;
  onDeleteEvent: (event: ScheduleEventDto) => void;
  onViewEventDetails: (event: ScheduleEventDto) => void;
  snapshot: ScheduleBuilderMachineSnapshot;
  onUpdateMetrics: (metrics: ScheduleMetricsInput) => void;
  initMetricsInput?: ScheduleMetricsInput;
  timezone: string;
  send: SendFn;
  storeId: string;
  hasForecast: boolean;
  countOpenShiftsTowardsLabor: boolean;
}

export type ContentType = 'teamMembers' | 'metrics' | 'events' | null;

export const FloatingPanel: React.FC<FloatingPanelProps> = ({
                                                              dayOfWeek, storeId,
                                                              isoWeek,
                                                              people, settings,
                                                              events, countOpenShiftsTowardsLabor,
                                                              onDeleteEvent,
                                                              initMetricsInput,
                                                              onSelectTeamMember,
                                                              onUpsertEvent,
                                                              onViewEventDetails,
                                                              onUpdateMetrics,
                                                              shift, snapshot, send,
                                                              schedule, hasForecast,
                                                              storeAreas, timezone
                                                            }) => {
  // Fetch store address for location-based validations
  const [storeAddress] = api.user.getStoreAddress.useSuspenseQuery({
    storeId: storeId
  });

  // Fetch multi-week hours for Hawaii ACA validation
  const [weeksHoursMap] = api.user.get3PrecedingAnd3FollowingWeeksHours.useSuspenseQuery({
    storeId: storeId,
    currentWeek: schedule.week,
    state: storeAddress.state
  }, {
    staleTime: 1000 * 60 * 60 // 1 hour
  });

  const draggableRef = useRef<HTMLButtonElement>(null);
  const defaultExpandedHeight = 580;
  const heightPadding = 120;
  const [expandedHeight, setExpandedHeight] = useState(Math.min(defaultExpandedHeight, window.innerHeight - heightPadding));
  const {
    isEventsMenuOpen,
    isTeamMembersMenuOpen,
    isMetricsMenuOpen,
    isMenuOpen,
    selectedPersonId,
    selectedShiftId,
    selectedEventId,
    isFloatingPanelOpen: isOpen,
    isQuickFillingShift
  } = useScheduleBuilderMachineUIState(snapshot);

  const handleContentMenuSelect = (content: ContentType) => {
    switch (content) {
      case "teamMembers":
        send({type: "listTeamMembers"});
        break;
      case "metrics":
        send({type: "listMetrics"});
        break;
      case "events":
        send({type: "listEvents"});
        break;
    }
  };

  const handleBack = () => {
    send({type: "panelBack"});
  };

  const onOpenChange = (open: boolean) => {
    if (open) {
      send({type: "panelOpened"});
    } else {
      send({type: "panelClosed"});
    }
  }

  const [windowWidth, setWindowWidth] = useState(window.innerWidth);

  useEffect(() => {
    const handleResize = debounce(() => {
      setWindowWidth(window.innerWidth);
      setExpandedHeight(Math.min(defaultExpandedHeight, window.innerHeight - heightPadding));
      setDragPos({x: 0, y: 0});
    }, 500);

    window.addEventListener('resize', handleResize);

    // Clean up the event listener when the component unmounts
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []); // Empty dependency array means this effect runs once on mount and clean up on unmount

  const bounds = {
    left: -(windowWidth - 400),
    right: 0,
    top: 0,
    bottom: 0
  }

  const cumulativeDeltaX = useRef<number>(0);
  const [isDragging, setIsDragging] = useState(false);
  const onDragStart = () => {
    cumulativeDeltaX.current = 0;
  }
  const onDrag: DraggableEventHandler = (e, dragData) => {
    cumulativeDeltaX.current += dragData.deltaX;
    if (Math.abs(cumulativeDeltaX.current) > 15) {
      setIsDragging(true);
    }
  }
  const onDragStop = (e: DraggableEvent, dragData: { x: number }) => {
    // give some time for the onClick handler to fire, so that we don't fire
    // onClick after a dragging session has stopped
    setTimeout(() => {
      setIsDragging(false);
    });
    setDragPos({x: dragData.x, y: 0});
    cumulativeDeltaX.current = 0;
  };

  const [dragPos, setDragPos] = useState<{ x: number; y: number }>({x: 0, y: 0});
  const buttonsRef = useRef<HTMLDivElement>(null);
  const alignment = dragPos.x < -windowWidth / 2 ? "start" : "end";

  const onPopoverTriggerClick = () => {
    if (!isDragging) {
      onOpenChange(!isOpen);
    }
  }

  const onInteractOutside = (e: PointerDownOutsideEvent | FocusOutsideEvent) => {
    const target = e.target as HTMLElement;
    if (target.closest(".gioa-popover-btn, .gioa-shift, .gioa-team-member-select, .gioa-sched-event")) {
      e.preventDefault();
      return;
    }

    if (!isQuickFillingShift && !isMenuOpen) {
      e.preventDefault();
    }
  };

  const handleOpenEventChange = (event: ScheduleEventDto) => {
    send({type: "viewEventDetails", eventId: event.id});
    onViewEventDetails(event);
  }

  return (
    <Popover open={isOpen} onOpenChange={onOpenChange}>
      <Draggable nodeRef={draggableRef}
                 onStart={onDragStart} onDrag={onDrag}
                 onStop={onDragStop} position={dragPos}
                 axis="x"
                 bounds={bounds}>
        <PopoverAnchor asChild>
          <button onClick={onPopoverTriggerClick}
                  className={cn("gioa-popover-btn group rounded-full w-16 h-16 fixed bottom-8 right-8 flex items-center justify-center bg-white hover:bg-gray-100 active:bg-gray-200 transition-opacity", isDragging ? "opacity-70 cursor-move" : undefined, isOpen ? "bg-primary-700 text-white hover:bg-primary-700 hover:text-gray-100 active:bg-primary-700" : undefined)}
                  ref={draggableRef}>
            {isOpen ?
              <XIcon size={48}/>
              :
              <svg width="60" height="60" viewBox="0 0 60 60" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path
                  fillRule="evenodd"
                  clipRule="evenodd"
                  className="group-hover:fill-primary-700 fill-primary-600 group-active:fill-primary-800"
                  d="M30 0C13.431 0 0 13.431 0 30C0 46.569 13.431 60 30 60C46.569 60 60 46.569 60 30C60 13.431 46.569 0 30 0ZM19.5 22.5C19.5 21.1211 19.7716 19.7557 20.2993 18.4818C20.8269 17.2079 21.6004 16.0504 22.5754 15.0754C23.5504 14.1004 24.7079 13.3269 25.9818 12.7993C27.2557 12.2716 28.6211 12 30 12C31.3789 12 32.7443 12.2716 34.0182 12.7993C35.2921 13.3269 36.4496 14.1004 37.4246 15.0754C38.3996 16.0504 39.1731 17.2079 39.7007 18.4818C40.2284 19.7557 40.5 21.1211 40.5 22.5C40.5 25.2848 39.3938 27.9555 37.4246 29.9246C35.4555 31.8938 32.7848 33 30 33C27.2152 33 24.5445 31.8938 22.5754 29.9246C20.6062 27.9555 19.5 25.2848 19.5 22.5ZM48.774 44.952C46.5276 47.776 43.6725 50.0565 40.4218 51.6231C37.1711 53.1898 33.6086 54.0023 30 54C26.3914 54.0023 22.8289 53.1898 19.5782 51.6231C16.3275 50.0565 13.4724 47.776 11.226 44.952C16.089 41.463 22.725 39 30 39C37.275 39 43.911 41.463 48.774 44.952Z"
                />
              </svg>}
          </button>
        </PopoverAnchor>
      </Draggable>

      <PopoverContent className={cn("p-0 w-[380px] transition-opacity", isDragging ? "opacity-60" : undefined)}
                      align={alignment} onInteractOutside={onInteractOutside}>
        <AnimatePresence mode="wait" initial={false}>
          {isMenuOpen ? (
            <motion.div key="buttons" ref={buttonsRef}
                        initial={{opacity: 0}}
                        animate={{opacity: 1}}
                        exit={{opacity: 0}}
                        transition={{duration: 0.0}}>
              <button
                className="px-4 py-3 flex items-center gap-6 border-b border-gray-200 hover:bg-gray-100 rounded-t-md"
                onClick={() => handleContentMenuSelect("teamMembers")}>
                <div>
                  <UserCircleIcon size={36} className={"text-gray-600"}/>
                </div>
                <div className={"text-left"}>
                  <p className={"font-medium mb-1"}>
                    Team Members
                  </p>
                  <p className={"text-sm"}>
                    Select and filter team members for open shifts.
                  </p>
                </div>
              </button>
              <button className="px-4 py-3 flex items-center gap-6 border-b border-gray-200 hover:bg-gray-100 w-full"
                      onClick={() => handleContentMenuSelect("events")}>
                <div>
                  <CalendarPlusIcon size={36} className={"text-gray-600"}/>
                </div>
                <div className={"text-left"}>
                  <p className={"font-medium mb-1"}>
                    Events
                  </p>
                  <p className={"text-sm"}>
                    Add or edit events happening at your store.
                  </p>
                </div>
              </button>
              <button className="px-4 py-3 flex items-center gap-6 hover:bg-gray-100 rounded-b-md w-full"
                      onClick={() => handleContentMenuSelect("metrics")}>
                <div>
                  <LineChartIcon size={36} className={"text-gray-600"}/>
                </div>
                <div className={"text-left"}>
                  <p className={"font-medium mb-1"}>
                    Metrics
                  </p>
                  <p className={"text-sm"}>
                    Calculate daily metrics.
                  </p>
                </div>
              </button>
            </motion.div>
          ) : (
            <motion.div key="content" className={"h-full"}
                        initial={{opacity: 0, height: buttonsRef.current?.clientHeight}}
                        animate={{opacity: 1, height: expandedHeight}}
                        exit={{opacity: 0, height: buttonsRef.current?.clientHeight}}
                        transition={{duration: 0.2}}>
              {isEventsMenuOpen &&
                  <EventsPanelContent onBack={handleBack} dayOfWeek={dayOfWeek} storeId={storeId}
                                      onDeleteEvent={onDeleteEvent} isoWeek={isoWeek}
                                      onUpsertEvent={onUpsertEvent} timezone={timezone}
                                      onViewEventDetails={handleOpenEventChange}
                                      selectedEventId={selectedEventId} onPanelBack={handleBack}
                                      events={events}/>}

              {isTeamMembersMenuOpen &&
                  <TeamMembersPanelContent openPersonId={selectedPersonId} send={send}
                                           onBack={handleBack} dayOfWeek={dayOfWeek}
                                           selectedShiftId={shift?.id ?? null}
                                           schedule={schedule} timezone={timezone}
                                           storeAreas={storeAreas} settings={settings}
                                           onSelectTeamMember={onSelectTeamMember}
                                           people={people}
                                           weeksHoursMap={weeksHoursMap}
                                           storeState={storeAddress.state}/>}
              {isMetricsMenuOpen &&
                  <MetricsPanelContent onBack={handleBack} schedule={schedule}
                                       initMetricsInput={initMetricsInput}
                                       onUpdateMetrics={onUpdateMetrics}
                                       countOpenShiftsTowardsLabor={countOpenShiftsTowardsLabor}
                                       dayOfWeek={dayOfWeek}/>}
            </motion.div>
          )}
        </AnimatePresence>
      </PopoverContent>
    </Popover>
  );
};

export default FloatingPanel;
