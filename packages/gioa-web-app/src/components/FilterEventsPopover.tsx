import React from 'react';
import {Popover, PopoverContent, PopoverTrigger} from "./ui/popover.tsx";
import {ScheduleCalendars, ScheduleEventType} from '../../../api/src/scheduleCalendars.ts';
import {useForm} from "@tanstack/react-form";
import {useDisclosure} from "@/src/hooks/useDisclosure.tsx";
import {FormControl} from "@/src/components/form/FormControl.tsx";
import {Label} from "@/src/components/ui/label.tsx";
import {FieldInfo} from "@/src/components/form/FieldInfo.tsx";
import {Checkbox} from "@/src/components/ui/checkbox.tsx";
import {Text} from "@/src/components/Text.tsx";
import {keys, map} from "lodash";
import {Separator} from "@/src/components/ui/separator.tsx";
import {Button} from "@/src/components/ui/button.tsx";

interface FilterEventTypesPopoverProps {
  initialValues: FilterEventsFormValues;
  defaultValues: FilterEventsFormValues;
  onSubmit: (values: FilterEventsFormValues) => void;
  children: React.ReactNode;
}

export interface FilterEventsFormValues {
  eventTypes: ScheduleEventType[];
}

export const FilterEventTypesPopover: React.FC<FilterEventTypesPopoverProps> = ({
                                                                                  initialValues,
                                                                                  defaultValues,
                                                                                  onSubmit,
                                                                                  children
                                                                                }) => {
  const popover = useDisclosure();


  const resetForm = () => {
    form.store.setState((state) => ({
      ...state,
      values: defaultValues
    }));
    form.handleSubmit();
  }

  const form = useForm({
    defaultValues: {
      ...defaultValues,
      ...initialValues,
    },
    onSubmit: ({value}) => {
      onSubmit(value);
      popover.setOpen(false);
    }
  });

  return (
    <Popover open={popover.isOpen} onOpenChange={popover.setOpen}>
      <PopoverTrigger asChild>
        {children}
      </PopoverTrigger>
      <PopoverContent className="p-4 w-[300px]">
        <div className={"flex flex-row justify-between items-center"}>
          <Text size={"lg"}>Filter Events</Text>
          <Button variant="link" onClick={resetForm}>Reset</Button>
        </div>
        <Separator className={"my-3"}/>
        <form.Field name={"eventTypes"}
                    children={(field) => {
                      return <FormControl>

                        <Label htmlFor={field.name}>Event Types</Label>

                        <div className={"flex mt-3 flex-col gap-2 cursor-pointer"}>
                          {map(keys(ScheduleCalendars), (eventType: ScheduleEventType) => {
                            const calendar = ScheduleCalendars[eventType];
                            const isChecked = field.state.value.includes(calendar.value as ScheduleEventType);
                            return <div className={"flex flex-row gap-2 items-center"}
                                        onClick={() => {
                                          const newState = isChecked
                                            ? field.state.value.filter(v => v !== calendar.value as ScheduleEventType)
                                            : [
                                              ...field.state.value,
                                              calendar.value as ScheduleEventType
                                            ];
                                          field.handleChange(newState);
                                        }}>
                              <Checkbox style={{
                                backgroundColor: isChecked ? calendar.color : "transparent",
                                borderColor: calendar.color,
                              }} color={calendar.color}
                                        checked={field.state.value.includes(calendar.value as ScheduleEventType)}/>
                              <Text>{calendar.title}</Text>
                            </div>
                          })}
                        </div>
                        <FieldInfo field={field}/>
                      </FormControl>;
                    }}/>
        <div className={"flex flex-row gap-2 mt-4"}>
          <Button variant="outline" className={"flex-1 mt-4"} onClick={form.handleSubmit}>Filter</Button>
        </div>
      </PopoverContent>
    </Popover>
  );
};