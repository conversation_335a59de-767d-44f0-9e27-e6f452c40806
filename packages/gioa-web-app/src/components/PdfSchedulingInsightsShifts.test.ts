import { describe, it, expect } from 'vitest';
import { DateTime } from 'luxon';

// Test the interfaces and data structures for shift swaps
describe('PdfSchedulingInsightsShifts - Shift Swaps', () => {
  it('should have correct ShiftSwap interface structure', () => {
    // Mock data that matches the ShiftSwap interface
    const mockShiftSwap = {
      date: DateTime.now(),
      type: "Shift Swap" as const,
      range: { 
        start: new Date('2024-01-01T09:00:00Z'), 
        end: new Date('2024-01-01T17:00:00Z') 
      },
      durationHours: 8,
      approved: true,
      teamMemberName: "<PERSON> Do<PERSON>"
    };

    // Verify the structure matches our interface
    expect(mockShiftSwap.type).toBe("Shift Swap");
    expect(mockShiftSwap.approved).toBe(true);
    expect(mockShiftSwap.durationHours).toBe(8);
    expect(mockShiftSwap.teamMemberName).toBe("John Doe");
    expect(mockShiftSwap.range.start).toBeInstanceOf(Date);
    expect(mockShiftSwap.range.end).toBeInstanceOf(Date);
    expect(mockShiftSwap.date).toBeInstanceOf(DateTime);
  });

  it('should have PersonShiftData interface with shiftSwaps array', () => {
    // Mock PersonShiftData with shiftSwaps
    const mockPersonShiftData = {
      person: {
        id: "person1",
        firstName: "Jane",
        lastName: "Smith",
        jobTitle: "Manager"
      },
      totalAvailabilityHours: 40,
      totalWorkedHours: 35,
      totalAdminNonOpsHours: 2,
      totalShifts: 5,
      swapStats: {
        offered: 2,
        received: 1
      },
      offerStats: {
        offered: 1,
        received: 0
      },
      timeOffRequests: 0,
      shifts: [],
      offeredShifts: [],
      shiftSwaps: [
        {
          date: DateTime.now(),
          type: "Shift Swap" as const,
          range: { 
            start: new Date('2024-01-01T09:00:00Z'), 
            end: new Date('2024-01-01T17:00:00Z') 
          },
          durationHours: 8,
          approved: true,
          teamMemberName: "John Doe"
        }
      ]
    };

    // Verify the structure
    expect(mockPersonShiftData.shiftSwaps).toHaveLength(1);
    expect(mockPersonShiftData.shiftSwaps[0].type).toBe("Shift Swap");
    expect(mockPersonShiftData.shiftSwaps[0].approved).toBe(true);
  });

  it('should handle empty shiftSwaps array', () => {
    const mockPersonShiftData = {
      person: {
        id: "person1",
        firstName: "Jane",
        lastName: "Smith",
        jobTitle: "Manager"
      },
      totalAvailabilityHours: 40,
      totalWorkedHours: 35,
      totalAdminNonOpsHours: 2,
      totalShifts: 5,
      swapStats: {
        offered: 0,
        received: 0
      },
      offerStats: {
        offered: 0,
        received: 0
      },
      timeOffRequests: 0,
      shifts: [],
      offeredShifts: [],
      shiftSwaps: []
    };

    expect(mockPersonShiftData.shiftSwaps).toHaveLength(0);
    expect(Array.isArray(mockPersonShiftData.shiftSwaps)).toBe(true);
  });
});
