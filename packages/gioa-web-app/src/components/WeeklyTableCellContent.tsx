import React, {useCallback, useState} from 'react';
import {DraggableData, DraggableEvent} from 'react-draggable';
import {DragPlaceholder, WeeklyTableDraggableShift} from "@/src/components/WeeklyTableDraggableShift.tsx";
import {WeeklyTableShift} from "@/src/components/WeeklyTableTypes.ts";
import {cn} from '../util';
import {map} from "lodash";
import {Button} from "@/src/components/ui/button.tsx";
import {PlusIcon} from "lucide-react";

export interface CellPosition {
  personId: string;
  day: number;
}

export interface WeeklyTableCellContentProps {
  personId: string;
  day: number;
  shifts: WeeklyTableShift[];
  onAddShift: (personId: string, day: number) => void;
  onDragStart: (shiftId: string, cellPosition: CellPosition) => void;
  onDragStop: (shiftId: string, data: DraggableData, e: DraggableEvent, cellPosition: CellPosition) => void;
  onShiftClick: (shift: WeeklyTableShift) => void;
}

let placeholderHeight: number | undefined;

export const WeeklyTableCellContent = React.memo(({
                                                    personId,
                                                    day,
                                                    shifts,
                                                    onAddShift,
                                                    onDragStart,
                                                    onDragStop,
                                                    onShiftClick
                                                  }: WeeklyTableCellContentProps) => {
  const _onDragStart = useCallback((shiftId: string, boxHeight: number) => {
    placeholderHeight = boxHeight;
    onDragStart(shiftId, {personId, day});
  }, [onDragStart, personId, day]);

  const _onDragStop = useCallback((shiftId: string, data: DraggableData, e: DraggableEvent) => {
    onDragStop(shiftId, data, e, {personId, day});
  }, [onDragStop, personId, day]);

  const [isShiftDragging, setIsShiftDragging] = useState<string>();

  return (
    <div className="flex flex-col p-2 gap-2" data-cell-id={`${personId}-${day}`}>
      {map(shifts, shift => (
        <WeeklyTableDraggableShift
          isDragging={shift.id === isShiftDragging}
          setIsDragging={setIsShiftDragging}
          setIsNotDragging={() => setIsShiftDragging(undefined)}
          key={shift.id}
          shift={shift}
          onDragStart={_onDragStart}
          onDragStop={_onDragStop}
          onClick={onShiftClick}
        />
      ))}

      {/* Show placeholder when dragging and hovering over this cell (but not the source cell) */}
      {!isShiftDragging ?
        <div className={cn("hidden opacity-0 drag-show group-hover:opacity-100")}>
          <DragPlaceholder height={placeholderHeight}/>
        </div> : null}

      <Button variant={"outline"} size={"sm"}
              className={cn("opacity-0 transition-opacity duration-300 ease-in-out drag-hide group-hover:opacity-100", {
                // "group-hover:opacity-100": !showDragPlaceholder,
              })}
              onClick={() => onAddShift(personId, day)}
      >
        <PlusIcon size={16} className={"text-gray-600"}/>
      </Button>
    </div>
  );
});

WeeklyTableCellContent.displayName = "WeeklyTableCellContent";
