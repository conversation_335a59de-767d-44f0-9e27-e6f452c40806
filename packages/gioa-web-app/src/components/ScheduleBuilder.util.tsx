import {
  severityCritical,
  severityError,
  severityInfo,
  severityWarning
} from "../../../api/src/scheduleValidation.types.ts";
import {FileWarningIcon, OctagonX, SkullIcon, TriangleAlertIcon} from "lucide-react";
import React from "react";
import {SchedulePersonClientDto} from "../../../api/src/schedulePersonDto.ts";
import {availabilityRanges} from "../../../api/src/availabilitySchemas.ts";
import {getDateFromWeekDayTime} from "../../../api/src/date.util.ts";
import {ScheduleDto} from "../../../api/src/scheduleSchemas.ts";
import {map, times} from "lodash";
import {genScheduleAreaId, genShiftId} from "../../../api/src/schemas.ts";
import {getChangeStatusColor, ScheduleShiftChangeStatus} from "../../../api/src/scheduleBuilder.util.ts";
import {constructShift} from "../../../api/src/shift.ts";

export function messageSeverityToIcon(severity: number, size: number): React.ReactNode {
  if (severity >= severityCritical) {
    return <SkullIcon size={size} className={"text-red-600"}/>
  } else if (severity >= severityError) {
    return <OctagonX size={size} className={"text-red-600"}/>
  } else if (severity >= severityWarning) {
    return <TriangleAlertIcon size={size} className={"text-orange-600"}/>
  } else if (severity >= severityInfo) {
    return <FileWarningIcon size={size} className={"text-yellow-500"}/>
  }
}

export const rowHeight = 40;
export const rightPanelWidth = 380;
export const testSched: ScheduleDto = {
  draftVersion: 1,
  hasDraft: true,
  timezone: "America/New_York",
  draft: {
    id: "testSched",
    storeId: "testStore",
    businessId: "testBusiness",
    createdFromId: undefined,
    dayParts: [{
      title: "Morning",
      range: {
        start: "05:00",
        end: "12:00"
      },
      color: "#ddfcff"
    }, {
      title: "Afternoon",
      range: {
        start: "12:00",
        end: "17:00"
      },
      color: "#fef1dd"
    }, {
      title: "Evening",
      range: {
        start: "17:00",
        end: "22:00"
      },
      color: "#ffd8d8"
    }],
    isPublished: false,
    isTemplate: false,
    publishedAt: undefined,
    storeHours: {
      start: "05:00",
      end: "22:00"
    },
    peakHours: times(7, d => ({
      dayOfWeek: d + 1,
      start: "12:00",
      end: "14:00",
    })),
    days: times(6, d => {
      const areaId = genScheduleAreaId();
      return ({
        dayOfWeek: d + 1,
        areas: [{
          id: areaId,
          title: "Team Lead",
          countsTowardsLabor: true,
          shifts: map([{
            title: "",
            id: genShiftId(),
            range: {
              start: "05:00",
              end: "12:00"
            },
            shiftAreaId: areaId,
            storePositionId: undefined,
            assignedPersonId: "test6",
            isShiftLead: false
          }], s => constructShift(s))
        }]
      });
    }),
    week: {
      year: 2024,
      week: 31
    }
  },
  shiftOffers: []
}
export const testPeople: SchedulePersonClientDto[] = [
  {
    id: "test1", firstName: "John", lastName: "Doe",
    availability: availabilityRanges.parse([
      {dayOfWeek: 1, start: '05:00', end: '17:00'},
      {dayOfWeek: 1, start: '18:00', end: '22:00'},
      {dayOfWeek: 2, start: '05:00', end: '17:00'},
      {dayOfWeek: 3, start: '05:00', end: '17:00'},
      {dayOfWeek: 4, start: '05:00', end: '17:00'},
      {dayOfWeek: 5, start: '05:00', end: '17:00'},
      {dayOfWeek: 6, start: '05:00', end: '17:00'},
    ]),
    weekAvailability: availabilityRanges.parse([
      {dayOfWeek: 1, start: '05:00', end: '17:00'},
      {dayOfWeek: 1, start: '18:00', end: '22:00'},
      {dayOfWeek: 2, start: '05:00', end: '17:00'},
      {dayOfWeek: 3, start: '05:00', end: '17:00'},
      {dayOfWeek: 4, start: '05:00', end: '17:00'},
      {dayOfWeek: 5, start: '05:00', end: '17:00'},
      {dayOfWeek: 6, start: '05:00', end: '17:00'},
    ]),
    proficiencyRanking: 3, jobTitle: "Team Member"
  } as any,
  {
    id: "test2", firstName: "Jane", lastName: "Brown",
    profileImageUrl: "https://randomuser.me/api/portraits/women/12.jpg",
    proficiencyRanking: 3, jobTitle: "Team Member", age: 16,
    availability: availabilityRanges.parse([
      {dayOfWeek: 1, start: '05:00', end: '17:00'},
      {dayOfWeek: 1, start: '18:00', end: '22:00'},
      {dayOfWeek: 2, start: '05:00', end: '17:00'},
      {dayOfWeek: 3, start: '05:00', end: '17:00'},
      {dayOfWeek: 4, start: '05:00', end: '17:00'},
      {dayOfWeek: 5, start: '05:00', end: '17:00'},
      {dayOfWeek: 6, start: '05:00', end: '17:00'},
    ]),
    weekAvailability: availabilityRanges.parse([
      {dayOfWeek: 1, start: '05:00', end: '17:00'},
      {dayOfWeek: 1, start: '18:00', end: '22:00'},
      {dayOfWeek: 2, start: '05:00', end: '17:00'},
      {dayOfWeek: 3, start: '05:00', end: '17:00'},
      {dayOfWeek: 4, start: '05:00', end: '17:00'},
      {dayOfWeek: 5, start: '05:00', end: '17:00'},
      {dayOfWeek: 6, start: '05:00', end: '17:00'},
    ]),
  },
  {
    id: "test3", firstName: "Jane", lastName: "Doe",

    availability: availabilityRanges.parse([
      {dayOfWeek: 1, start: '05:00', end: '17:00'},
      {dayOfWeek: 1, start: '18:00', end: '22:00'},
      {dayOfWeek: 2, start: '13:00', end: '22:00'},
      {dayOfWeek: 3, start: '05:00', end: '17:00'},
      {dayOfWeek: 5, start: '05:00', end: '17:00'},
      {dayOfWeek: 6, start: '13:00', end: '22:00'},
    ]),
    weekAvailability: availabilityRanges.parse([
      {dayOfWeek: 1, start: '05:00', end: '17:00'},
      {dayOfWeek: 1, start: '18:00', end: '22:00'},
      {dayOfWeek: 2, start: '13:00', end: '22:00'},
      {dayOfWeek: 3, start: '05:00', end: '17:00'},
      {dayOfWeek: 5, start: '05:00', end: '17:00'},
      {dayOfWeek: 6, start: '13:00', end: '22:00'},
    ]),
    proficiencyRanking: 3, jobTitle: "Team Member"
  },
  {
    id: "test4", firstName: "John", lastName: "Smith",
    availability: availabilityRanges.parse([
      {dayOfWeek: 1, start: '05:00', end: '22:00'},
      {dayOfWeek: 2, start: '18:00', end: '22:00'},
      {dayOfWeek: 3, start: '05:00', end: '22:00'},
      {dayOfWeek: 5, start: '10:00', end: '22:00'},
      {dayOfWeek: 6, start: '13:00', end: '22:00'},
    ]),
    weekAvailability: availabilityRanges.parse([
      {dayOfWeek: 1, start: '05:00', end: '22:00'},
      {dayOfWeek: 2, start: '18:00', end: '22:00'},
      {dayOfWeek: 3, start: '05:00', end: '22:00'},
      {dayOfWeek: 5, start: '10:00', end: '22:00'},
      {dayOfWeek: 6, start: '13:00', end: '22:00'},
    ]),
    proficiencyRanking: 3, jobTitle: "Director"
  },
  {
    id: "test5", firstName: "Jane", lastName: "Smith",
    availability: availabilityRanges.parse([
      {dayOfWeek: 1, start: '05:00', end: '17:00'},
      {dayOfWeek: 2, start: '10:00', end: '22:00'},
      {dayOfWeek: 3, start: '05:00', end: '17:00'},
      {dayOfWeek: 5, start: '05:00', end: '17:00'},
    ]),
    weekAvailability: availabilityRanges.parse([
      {dayOfWeek: 1, start: '05:00', end: '17:00'},
      {dayOfWeek: 2, start: '10:00', end: '22:00'},
      {dayOfWeek: 3, start: '05:00', end: '17:00'},
      {dayOfWeek: 5, start: '05:00', end: '17:00'},
    ]),
    proficiencyRanking: 2, jobTitle: "Team Member"
  },
  {
    id: "test6", firstName: "John", lastName: "Brown",
    proficiencyRanking: 1, jobTitle: "Team Leader",
    training: [{
      id: "testTraining",
      isCompleted: true,
      positionId: "pos_GtWpNVMUPnqwfTgyXbBYM",
      isPersonPreferred: true,
    }, {
      id: "testTraining2",
      isCompleted: true,
      positionId: "pos_QUk2OFXt3Ee6AIDpP6zGy",
      isPersonPreferred: false,
    }],
    profileImageUrl: "https://randomuser.me/api/portraits/men/31.jpg",
    age: 15,
    positionStatistics: [{
      positionId: "pos_GtWpNVMUPnqwfTgyXbBYM",
      value: 80,
      dataPoints: [{
        notes: "This person is great",
        date: new Date(),
        value: 80
      }]
    }],
    availability: availabilityRanges.parse([
      {dayOfWeek: 1, start: '05:00', end: '17:00'},
      {dayOfWeek: 1, start: '18:00', end: '22:00'},
      {dayOfWeek: 3, start: '05:00', end: '17:00'},
      {dayOfWeek: 5, start: '05:00', end: '17:00'},
    ]),
    weekAvailability: availabilityRanges.parse([
      {dayOfWeek: 1, start: '05:00', end: '17:00'},
      {dayOfWeek: 1, start: '18:00', end: '22:00'},
      {dayOfWeek: 3, start: '05:00', end: '17:00'},
      {dayOfWeek: 5, start: '05:00', end: '17:00'},
    ]),
    timeOff: [{
      start: getDateFromWeekDayTime({
        week: 31,
        year: 2024,
        day: 1,
        time: "10:00",
        timezone: "America/Denver"
      }),
      end: getDateFromWeekDayTime({
        week: 31,
        year: 2024,
        day: 1,
        time: "16:00",
        timezone: "America/Denver"
      }),
    }]
  },
  {id: "test7", firstName: "John", lastName: "Johnson", proficiencyRanking: 1, jobTitle: "Team Member"},
  {id: "test8", firstName: "Jane", lastName: "Johnson", proficiencyRanking: 1, jobTitle: "Trainee"},
];

export function getChangeStatusBorder(status?: ScheduleShiftChangeStatus): {
  borderClass: string,
  color: string | undefined
} {
  const borderLeftColor = status && status !== "created" ? getChangeStatusColor(status) : undefined;
  const borderLeft = status && status !== "created" ? "border-l-4" : "";

  return {
    borderClass: borderLeft,
    color: borderLeftColor
  }
}

export function getChangeStatusThickBorder(status?: ScheduleShiftChangeStatus): {
  borderClass: string,
  color: string | undefined
} {
  const borderLeftColor = status && status !== "created" ? getChangeStatusColor(status) : undefined;
  const borderLeft = status && status !== "created" ? "border-l-8" : "";

  return {
    borderClass: borderLeft,
    color: borderLeftColor
  }
}

