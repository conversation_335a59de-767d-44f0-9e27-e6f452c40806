import React from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON>Title, DialogFooter } from "@/src/components/ui/dialog.tsx";
import { Button } from "@/src/components/ui/button.tsx";
import { PersonPositionScoreHistory } from "@/src/components/PersonPositionScoreHistory.tsx";
import { find, flatMap } from 'lodash';
import { api } from '@/src/api.ts';
import { StorePositionDto } from '@gioa/api/src/schemas';

interface ViewPersonPositionModalProps {
  isOpen: boolean;
  onClose: () => void;
  personId: string;
  storeId: string;
  positionId: string;
}

export const ViewPersonPositionModal: React.FC<ViewPersonPositionModalProps> = ({
  isOpen,
  onClose,
  personId,
  storeId,
  positionId
}) => {
  // Fetch person details
  const { data: person, isLoading } = api.user.getPersonDetail.useQuery({
    personId,
    storeId
  }, {
    staleTime: 5 * 60 * 1000, // 5 minutes
    enabled: isOpen // Only fetch when modal is open
  });

  // Find position and statistics
  const positions = person ? flatMap(person.storeAreas, a => a.positions) : [];
  const position = find(positions, p => p.id === positionId) as StorePositionDto | undefined;
  const statistic = person ? find(person.positionStatistics, s => s.positionId === positionId) : undefined;
  const dataPoints = statistic?.dataPoints ?? [];

  if (isLoading) {
    return (
      <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Loading...</DialogTitle>
          </DialogHeader>
        </DialogContent>
      </Dialog>
    );
  }

  if (!person || !position) {
    return null;
  }

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="sm:max-w-[600px] max-h-[80vh] overflow-hidden flex flex-col">
        <DialogHeader>
          <DialogTitle>{position.title}</DialogTitle>
        </DialogHeader>

        <div className="flex-1 overflow-auto py-4">
          <PersonPositionScoreHistory
            dataPoints={dataPoints}
            aggregateValue={statistic?.value ?? 0}
            className="px-2"
          />
        </div>

        <DialogFooter>
          <Button onClick={onClose}>
            Close
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
