import React, {useMemo} from 'react';
import {Sheet, SheetContent, SheetDescription, Sheet<PERSON>eader, SheetTitle} from "@/src/components/ui/sheet.tsx";
import {Button} from "@/src/components/ui/button.tsx";
import {chain, find} from "lodash";
import {daysOfWeek} from './AvailabilityWeek.tsx';
import {ScheduleValidationResult, ValidationMessage} from "../../../api/src/scheduleValidation.types.ts";
import {ScheduleDto} from "../../../api/src/scheduleSchemas.ts";
import {DailyTimeRange, IsoWeekDate} from '@gioa/api/src/timeSchemas.ts';
import {ScheduleIssuesList} from "@/src/components/ScheduleIssuesList.tsx";
import {SchedulePersonDto} from "../../../api/src/schedulePersonDto.ts";

import {isMessageForWeekday} from "../../../api/src/scheduleValidation/scheduleValidationMessagesL2.ts";
import {sortBySeverity} from "../../../api/src/scheduleValidation/scheduleValidationMessagesL1.ts";

export interface ScheduleValidationSheetProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  storeId: string;
  dayOfWeek: number;
  onGoToMessage: (week: IsoWeekDate, dayOfWeek: number, msg: ValidationMessage) => void;
  onIgnoreMessage: (msg: ValidationMessage) => void;
  onUnignoreMessage: (msg: ValidationMessage) => void;
  validationResult?: ScheduleValidationResult;
  scheduleRev: ScheduleDto;
  people: SchedulePersonDto[];
  storeHours: DailyTimeRange;
}

export const ScheduleValidationSheet: React.FC<ScheduleValidationSheetProps> = ({
                                                                                  isOpen,
                                                                                  onOpenChange,
                                                                                  storeId,
                                                                                  scheduleRev,
                                                                                  people, onIgnoreMessage,
                                                                                  dayOfWeek, storeHours,
                                                                                  onGoToMessage, onUnignoreMessage,
                                                                                  validationResult,
                                                                                }) => {

  const dayOfWeekObj = find(daysOfWeek, d => d.dayOfWeek === dayOfWeek);
  const warningsForToday = useMemo(() =>
      chain(validationResult?.messages ?? [])
        .filter(isMessageForWeekday(dayOfWeek))
        .sortBy(sortBySeverity)
        .value(),
    [validationResult, dayOfWeek]);

  return (
    <Sheet modal={false} open={isOpen}
           onOpenChange={onOpenChange}>
      <SheetContent side="right"
                    className="flex flex-col gap-4 max-h-full overflow-auto px-0">
        <SheetHeader className={"mx-4"}>
          <SheetTitle>
            Issues
          </SheetTitle>
          <SheetDescription>
            Identify and resolve issues for {dayOfWeekObj?.name}
          </SheetDescription>
        </SheetHeader>

        <ScheduleIssuesList warnings={warningsForToday} schedule={scheduleRev.draft}
                            onIgnoreMessage={onIgnoreMessage} onUnignoreMessage={onUnignoreMessage}
                            people={people} storeHours={scheduleRev.draft.storeHours}
                            onGoToMessage={onGoToMessage}/>

        <div className={"px-4 w-full"}>
          <Button variant={"outline"} type={"button"}
                  onClick={() => onOpenChange(false)}>
            Close
          </Button>
        </div>
      </SheetContent>
    </Sheet>
  );
}

