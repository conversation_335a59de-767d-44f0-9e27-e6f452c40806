import React from 'react';
import {Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle} from "@/src/components/ui/dialog.tsx";
import {IsoWeekDate} from "../../../api/src/timeSchemas.ts";

export interface ScheduleMetricsEditDialogProps {
  week: IsoWeekDate;
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
}

export const ScheduleMetricsEditDialog: React.FC<ScheduleMetricsEditDialogProps> = (props) => {
    return (
      <Dialog open={props.isOpen} onOpenChange={props.onOpenChange}>
        <DialogContent className={"overflow-auto"} style={{maxHeight: "calc(100vh - 50px)"}}>
          <DialogHeader>
            <DialogTitle>Edit Metrics</DialogTitle>
            <DialogDescription>
              Input metrics for this week.
            </DialogDescription>
          </DialogHeader>


        </DialogContent>
      </Dialog>
    );
}
