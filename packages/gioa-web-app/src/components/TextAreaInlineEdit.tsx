import React from 'react';
import {InlineEdit} from '@/src/components/InlineEdit';
import {Edit} from 'lucide-react';

type TextAreaInlineEditProps = {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  className?: string;
  textareaClassName?: string;
  displayClassName?: string;
  showEditIcon?: boolean;
  rows?: number;
  minHeight?: number;
};

export const TextAreaInlineEdit: React.FC<TextAreaInlineEditProps> = ({
  value,
  onChange,
  placeholder = 'Enter text',
  className = '',
  textareaClassName = '',
  displayClassName = '',
  showEditIcon = true,
  rows = 3,
  minHeight = 100,
}) => {
  return (
    <InlineEdit<string>
      value={value}
      onChange={onChange}
      placeholder={placeholder}
      className={className}
      inputClassName={textareaClassName}
      displayClassName={displayClassName}
      showEditIcon={showEditIcon}
      multiline={true}
      rows={rows}
      minHeight={minHeight}
      saveOnEnter={false}
      saveOnCtrlEnter={true}
      renderDisplayContent={(value, placeholder) => (
        <div className="flex items-center gap-2">
          <span className="whitespace-pre-wrap">
            {value || <span className="text-gray-400">{placeholder}</span>}
          </span>
          {showEditIcon && <Edit size={14} className="text-gray-400 flex-shrink-0" />}
        </div>
      )}
    />
  );
};
