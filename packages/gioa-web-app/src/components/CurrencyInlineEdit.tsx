import {InlineEdit} from "@/src/components/InlineEdit.tsx";

import {formatCurrency} from "../../../common/src/dataFormatters.ts";
import {dollars, Dollars} from "../../../api/src/scheduling/metrics/dollars.ts";
import {parseDollarsOrUndef} from "@/src/util.tsx";

type NumberInlineEditProps = {
  value: Dollars;
  onChange: (value: Dollars) => void;
  placeholder?: string;
  className?: string;
  inputClassName?: string;
  displayClassName?: string;
  showEditIcon?: boolean;
  formatter?: (value: Dollars) => string;
};

export const CurrencyInlineEdit: React.FC<NumberInlineEditProps> = ({
                                                                    value,
                                                                    onChange,
                                                                    placeholder = '$0.00',
                                                                    className = '',
                                                                    inputClassName = '',
                                                                    displayClassName = '',
                                                                    showEditIcon = true,
                                                                    formatter = (val) => formatCurrency(val),
                                                                  }) => {
  return (
    <InlineEdit<Dollars>
      value={value}
      onChange={onChange}
      displayFormatter={formatter}
      parseValue={(val) => {
        return parseDollarsOrUndef(val) ?? dollars(0);
      }}
      inputType="number"
      placeholder={placeholder}
      className={className}
      inputClassName={inputClassName}
      displayClassName={displayClassName}
      valueFormatter={num => num.toString()}
      showEditIcon={showEditIcon}
    />
  );
};
