import React from 'react';
import {PersonAvatar} from "@/src/components/PersonAvatar.tsx";
import {ProficiencyRating} from "@/src/components/ProficiencyRating.tsx";
import {LaborStatusIcon} from "@/src/components/LaborStatusIcon.tsx";
import {getPersonLaborStatus} from "../../../api/src/personMetrics.ts";
import {SchedulePersonClientDto} from "../../../api/src/schedulePersonDto.ts";

export interface WeeklyTablePersonCellProps {
  person: SchedulePersonClientDto;
  rowSpan: number;
  filteredWeekHours: number;
  totalWeekHours: number;
}

export const WeeklyTablePersonCell = React.memo(({
                                                   person,
                                                   rowSpan,
                                                   filteredWeekHours,
                                                   totalWeekHours
                                                 }: WeeklyTablePersonCellProps) => {
  return (
    <td rowSpan={rowSpan} className={"p-4 sticky left-0 bg-white z-10 team-member-cell"}>
      <div className={"flex flex-row gap-2 items-center border-b pb-3 mb-3"}>
        <PersonAvatar person={person}/>
        <div>
          <div className={"text-sm font-semibold mb-1"}>
            {person.firstName} {person.lastName.charAt(0)}.
          </div>
          <div className={"flex gap-2 flex-wrap"}>
            <ProficiencyRating rank={person.proficiencyRanking ?? 0} colorScheme={"light-bg"} size={14}/>
            <LaborStatusIcon laborStatus={getPersonLaborStatus(person.age)} size={14}/>
          </div>
        </div>
      </div>
      <div className={"text-sm"}>
        <div className={"whitespace-nowrap flex gap-2 justify-between items-center mb-2"}>
          <span className={'text-muted-foreground'}>Scheduled</span>
          <span
            className={"px-2 py-0.5 bg-blue-50 rounded-md"}>{filteredWeekHours === totalWeekHours ? totalWeekHours : `${filteredWeekHours}/${totalWeekHours}`} hr</span>
        </div>
        <div className={"whitespace-nowrap flex gap-2 justify-between items-center"}>
          <span className={'text-muted-foreground'}>Preferred</span>
          <span
            className={"px-2 py-0.5 bg-blue-50 rounded-md"}>{person.maxHoursPreferred ? person.maxHoursPreferred + " hr" : 'n/a'}</span>
        </div>
      </div>
    </td>
  );
})
