import React from 'react';
import {isEmpty, map, reject} from "lodash";
import {isAddingWeeklyFilter, WeeklyFilterControl} from "@/src/components/WeeklyFilterControl.tsx";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from "@/src/components/ui/dropdown-menu.tsx";
import {Button} from "@/src/components/ui/button.tsx";
import {
  createAreaFilter,
  createIsScheduledFilter,
  createLaborStatusFilter,
  createNumDaysFilter,
  WeeklyViewFilter
} from "@/src/components/WeeklyViewControlBar.types.ts";
import {StoreAreaDto} from "../../../api/src/schemas.ts";
import {useAutoAnimate} from "@formkit/auto-animate/react";
import {PlusIcon} from 'lucide-react';
import {cn} from "@/src/util.ts";

export interface WeeklyFilterBarProps {
  storeAreas: StoreAreaDto[];
  setFilters: (filters: WeeklyViewFilter[]) => void;
  filters: WeeklyViewFilter[];
  onReset: () => void;
  className?: string;
}

export const WeeklyFilterBar: React.FC<WeeklyFilterBarProps> = ({
                                                                  className,
                                                                  filters, setFilters, storeAreas, onReset
                                                                }) => {
  const onAddFilter = (filter: WeeklyViewFilter) => {
    isAddingWeeklyFilter.current = true;
    setFilters([...filters, filter]);
    setTimeout(() => isAddingWeeklyFilter.current = false, 100);
  }

  const onAddAreaFilter = () => {
    onAddFilter(createAreaFilter(filters.length, storeAreas, []));
  }
  const onAddLaborStatusFilter = () => {
    onAddFilter(createLaborStatusFilter(filters.length, ["14/15 yr old", "16/17 yr old"]));
  }
  const onAddNumDaysFilter = () => {
    onAddFilter(createNumDaysFilter(filters.length, 1));
  }
  const onAddIsScheduledFilter = () => {
    onAddFilter(createIsScheduledFilter(filters.length, true));
  }

  const onDeleteFilter = (filterId: string) => {
    setFilters(reject(filters, f => f.id === filterId));
  }

  const setFilterValue = (filterId: string, value: any) => {
    setFilters(map(filters, f => {
      if (f.id === filterId) {
        return {
          ...f,
          value: value
        }
      }
      return f;
    }));
  }

  const onResetFilters = () => {
    onReset();
  }

  const [autoContainerRef] = useAutoAnimate()

  return (
    <div className={cn("flex flex-row flex-wrap gap-2 grow items-center", className)}
         ref={autoContainerRef}>
      {map(filters, (filter) => {
        return <WeeklyFilterControl key={filter.id} filter={filter}
                                    onDeleteFilter={onDeleteFilter}
                                    setFilterValue={setFilterValue}/>
      })}

      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant={"link"} size={"sm"} leftIcon={<PlusIcon className={"text-gray-700"} size={16}/>}>
            Add Filter
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent>
          <DropdownMenuItem onSelect={onAddIsScheduledFilter}>Is scheduled</DropdownMenuItem>
          <DropdownMenuItem onSelect={onAddNumDaysFilter}>Num. days scheduled</DropdownMenuItem>
          <DropdownMenuItem onSelect={onAddAreaFilter}>Area</DropdownMenuItem>
          <DropdownMenuItem onSelect={onAddLaborStatusFilter}>Labor status</DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
      {!isEmpty(filters) ?
        <Button onClick={onResetFilters}
                variant={"link"} size={"sm"}>
          Reset
        </Button> : null}
    </div>
  );
}
