import React, { useState, use<PERSON><PERSON>back, useMemo, memo } from "react";
import { useDropzone } from "react-dropzone";
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "@/src/components/ui/dialog.tsx";
import { Button } from "@/src/components/ui/button.tsx";
import { Text } from "@/src/components/Text.tsx";
import {
  CloudUploadIcon,
  FileTextIcon,
  Trash2Icon,
  CheckIcon,
  XIcon,
  ChevronDownIcon,
} from "lucide-react";
import { formatFileSize } from "@/src/utils/formatters.ts";
import { api } from "@/src/api.ts";
import { ErrorAlert, getHumanReadableErrorMessage } from "@/src/components/ErrorAlert.tsx";
import { useForm } from "@tanstack/react-form";
import { zodValidator } from "@tanstack/zod-form-adapter";
import { z } from "zod";
import { toast } from "sonner";
import { useFormAttachmentUpload } from "@/src/hooks/useFormAttachmentUpload.ts";
import { FileAttachmentDto } from "@gioa/api/src/fileAttachment.dto";
import { Progress } from "@/src/components/ui/progress.tsx";
import { Label } from "@/src/components/ui/label.tsx";
import { FormInput } from "@/src/components/form/FormInput.tsx";
import { FieldInfo } from "@/src/components/form/FieldInfo.tsx";
import {genStoreResourceId, PersonDto} from "@gioa/api/src/schemas";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/src/components/ui/table.tsx";
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from "@/src/components/ui/command.tsx";
import { Popover, PopoverContent, PopoverTrigger } from "@/src/components/ui/popover.tsx";
import { map, find, filter, flatMap, forEach, groupBy, sumBy, isEmpty, sortBy } from "lodash";
import { matchPeople } from "@/src/payRates/payRates.ts";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/src/components/ui/alert-dialog";
import { Checkbox } from "@/src/components/ui/checkbox";
import { ScrollArea } from "@/src/components/ui/scroll-area";

enum WizardStep {
  UPLOAD_FILE = 0,
  MAP_EMPLOYEES = 1,
  REVIEW_RESULTS = 2
}

interface DocumentFile {
  file: File;
  name: string;
  size: number;
  type: string;
}

interface TimePunchEntry {
  employeeName: string;
  dayOfWeek?: string;
  date: string;
  timeIn?: string;
  timeOut?: string;
  totalHours?: number;
  payType?: string;
  weekHours?: number;
  regularHours?: number;
  overtimeHours?: number;
  wageRate?: number;
  regularWages?: number;
  overtimeWages?: number;
  totalWages?: number;
}

interface ParsedTimePunchResult {
  rawText: string;
  timePunchEntries: TimePunchEntry[];
  parseErrors: string[];
  validationErrors: string[];
  savedEntries?: number;
  deletedEntries?: number;
  dateRange?: {
    startDate: Date;
    endDate: Date;
  };
}

interface MappedEmployee {
  pdfEmployeeName: string;
  personId: string | null;
  entries: TimePunchEntry[];
}

interface TimePunchUploadWizardProps {
  isOpen: boolean;
  onClose: () => void;
  storeId: string;
  onSuccess?: () => void;
}

// Row component for employee mapping with memoization
const EmployeeMappingRow = memo(({
  employee,
  employeeIndex,
  sortedPeople,
  conflictingEmployeeName,
  isComboboxOpen,
  onComboboxOpenChange,
  onPersonMappingUpdate,
  onClearMapping
}: {
  employee: MappedEmployee;
  employeeIndex: number;
  sortedPeople: PersonDto[];
  conflictingEmployeeName: string | null;
  isComboboxOpen: boolean;
  onComboboxOpenChange: (index: number, open: boolean) => void;
  onPersonMappingUpdate: (index: number, personId: string) => void;
  onClearMapping: (index: number) => void;
}) => {
  // Calculate validation state inside the component
  const isValid = employee.personId !== null;
  const hasConflict = conflictingEmployeeName !== null;

  // Create stable callbacks for this specific row
  const handleComboboxChange = useCallback((open: boolean) => {
    onComboboxOpenChange(employeeIndex, open);
  }, [employeeIndex, onComboboxOpenChange]);

  const handlePersonMappingChange = useCallback((personId: string) => {
    onPersonMappingUpdate(employeeIndex, personId);
  }, [employeeIndex, onPersonMappingUpdate]);

  const handleClearMapping = useCallback(() => {
    onClearMapping(employeeIndex);
  }, [employeeIndex, onClearMapping]);

  return (
    <TableRow>
      <TableCell>
        {isValid && !hasConflict ? (
          <CheckIcon className="h-4 w-4 text-green-500" />
        ) : (
          <XIcon className="h-4 w-4 text-red-500" />
        )}
      </TableCell>
      <TableCell className="font-medium">
        {employee.pdfEmployeeName}
      </TableCell>
      <TableCell>
        <div className="flex items-center gap-2">
          <Popover open={isComboboxOpen} onOpenChange={handleComboboxChange}>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                role="combobox"
                aria-expanded={isComboboxOpen}
                className="w-full justify-between"
              >
                {employee.personId
                  ? (() => {
                      const selectedPerson = find(sortedPeople, p => p.id === employee.personId);
                      return selectedPerson ? `${selectedPerson.lastName || ''}, ${selectedPerson.firstName || ''}` : "Select employee";
                    })()
                  : "Select employee"
                }
                <ChevronDownIcon className="ml-2 h-4 w-4 shrink-0 opacity-50" />
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-[300px] p-2"
                            align="start"
                            onWheel={(e) => e.stopPropagation()}>
              <Command>
                <CommandInput placeholder="Search employees..." />
                <CommandList>
                  <CommandEmpty>No employee found.</CommandEmpty>
                  <CommandGroup>
                    {map(sortedPeople, (person) => (
                      <CommandItem
                        key={person.id}
                        value={`${person.lastName || ''}, ${person.firstName || ''}`}
                        onSelect={() => {
                          if (person.id) {
                            handlePersonMappingChange(person.id);
                            handleComboboxChange(false);
                          }
                        }}
                      >
                        {person.lastName || ''}, {person.firstName || ''}
                      </CommandItem>
                    ))}
                  </CommandGroup>
                </CommandList>
              </Command>
            </PopoverContent>
          </Popover>
          {employee.personId && (
            <Button
              variant="ghost"
              size="sm"
              onClick={handleClearMapping}
            >
              <XIcon className="h-4 w-4" />
            </Button>
          )}
        </div>
        {hasConflict && conflictingEmployeeName && (
          <Text size="sm" className="text-destructive mt-1">
            This person is already mapped to {conflictingEmployeeName}
          </Text>
        )}
      </TableCell>
      <TableCell className="text-center">
        <span className="bg-gray-100 text-gray-800 px-2 py-1 rounded text-sm">
          {employee.entries.length}
        </span>
      </TableCell>
    </TableRow>
  );
});

EmployeeMappingRow.displayName = 'EmployeeMappingRow';

export function TimePunchUploadWizard({ isOpen, onClose, storeId, onSuccess }: TimePunchUploadWizardProps) {
  const [currentStep, setCurrentStep] = useState<WizardStep>(WizardStep.UPLOAD_FILE);
  const [uploadedAttachment, setUploadedAttachment] = useState<FileAttachmentDto | null>(null);
  const [parsedResult, setParsedResult] = useState<ParsedTimePunchResult | null>(null);
  const [mappedEmployees, setMappedEmployees] = useState<MappedEmployee[]>([]);
  const [openComboboxes, setOpenComboboxes] = useState<Record<number, boolean>>({});
  const [savedResults, setSavedResults] = useState<ParsedTimePunchResult | null>(null);
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  const [confirmProceed, setConfirmProceed] = useState(false);

  // API mutations
  const getPresignedPost = api.user.getPresignedPostForStoreResource.useMutation();
  const parseTimePunchPdf = api.user.parseTimePunchPdfPreview.useMutation();
  const saveTimePunchEntries = api.user.saveTimePunchEntries.useMutation();

  // API utils for cache invalidation
  const apiUtils = api.useUtils();

  // Get store people for mapping (include archived and suspended for time punch mapping)
  // Enable query when dialog opens so data is ready for matching algorithm
  const { data: storeData } = api.user.getAllSchedulePeopleAtStore.useQuery(
    {
      storeId,
      includeArchived: true,
      includeSuspended: true
    },
    { enabled: isOpen }
  );

  const people = storeData?.people || [];

  // Memoize sorted people list for dropdown to avoid re-sorting on every render
  const sortedPeople = useMemo(() =>
    sortBy(people, ['lastName', 'firstName']),
    [people]
  );

  // No need to re-sort on every render - mappedEmployees is already sorted when created

  // Pre-compute duplicate mappings for O(N)
  const duplicateMap = useMemo(() => {
    const personIdToEmployees = new Map<string, string[]>();

    // Group employees by their assigned personId
    forEach(mappedEmployees, (employee) => {
      if (employee.personId) {
        const existing = personIdToEmployees.get(employee.personId) || [];
        existing.push(employee.pdfEmployeeName);
        personIdToEmployees.set(employee.personId, existing);
      }
    });

    // Create final map: employeeName → conflicting employee name (if any)
    const duplicateMap = new Map<string, string | null>();

    forEach(mappedEmployees, (employee) => {
      if (employee.personId) {
        const employeesWithSamePersonId = personIdToEmployees.get(employee.personId) || [];
        // If more than one employee mapped to same person, find the conflicting one
        if (employeesWithSamePersonId.length > 1) {
          const conflictingEmployee = employeesWithSamePersonId.find(name => name !== employee.pdfEmployeeName);
          duplicateMap.set(employee.pdfEmployeeName, conflictingEmployee || null);
        } else {
          duplicateMap.set(employee.pdfEmployeeName, null);
        }
      } else {
        duplicateMap.set(employee.pdfEmployeeName, null);
      }
    });

    return duplicateMap;
  }, [mappedEmployees]);

  const uploadTimePunchFile = api.user.uploadTimePunchFile.useMutation({
    onSuccess: async () => {
      // After successful upload, parse the PDF (but don't save to database yet)
      if (!uploadedAttachment) {
        toast.error("No file attachment found");
        return;
      }

      try {
        // Parse PDF without saving to database
        const parseResult = await parseTimePunchPdf.mutateAsync({
          storeId,
          fileId: uploadedAttachment.fileId,
        });

        // Add savedEntries field for compatibility
        const resultWithSavedEntries: ParsedTimePunchResult = {
          ...parseResult,
          savedEntries: undefined, // Will be set later when saving
        };

        setParsedResult(resultWithSavedEntries);

        // Group entries by employee name and create initial mappings
        const entriesByEmployee = groupBy(parseResult.timePunchEntries, entry => entry.employeeName);

        // Create unique employee names from PDF
        const pdfEmployees = Object.keys(entriesByEmployee).map(name => ({
          firstName: name.split(', ')[1] || name.split(' ')[0] || '',
          lastName: name.split(', ')[0] || name.split(' ')[1] || '',
          fullName: name
        }));

        // Use existing matching logic to suggest matches
        const matches = matchPeople(people, pdfEmployees);

        // Create mapped employees array
        const initialMappedEmployees: MappedEmployee[] = map(Object.keys(entriesByEmployee), pdfName => {
          const pdfEmployee = find(pdfEmployees, p => p.fullName === pdfName);
          const matchedPerson = pdfEmployee ? matches.get(pdfEmployee) : null;

          return {
            pdfEmployeeName: pdfName,
            personId: matchedPerson?.id || null,
            entries: entriesByEmployee[pdfName]
          };
        });

        // Sort employees once when initially created: unmatched first, then alphabetically
        const sortedInitialEmployees = sortBy(initialMappedEmployees, [
          // First sort by match status (unmatched first)
          (emp) => emp.personId !== null ? 1 : 0,
          // Then sort alphabetically by PDF employee name
          'pdfEmployeeName'
        ]);

        setMappedEmployees(sortedInitialEmployees);
        setCurrentStep(WizardStep.MAP_EMPLOYEES);

      } catch (parseError) {
        console.error('Failed to parse PDF:', parseError);
        toast.error(
          "File uploaded but failed to parse PDF content: " + getHumanReadableErrorMessage(parseError),
          { position: "top-center", duration: 5000 }
        );
      }
    },
    onError: (error) => {
      toast.error("Failed to upload file: " + getHumanReadableErrorMessage(error), {
        position: "top-center",
      });
    },
  });

  const { toDto: uploadAttachment, status: uploadStatus } = useFormAttachmentUpload(({ contentType, mediaType }) =>
    getPresignedPost.mutateAsync({
      storeId,
      contentType,
      mediaType: mediaType || "file",
    }),
  );

  const form = useForm({
    defaultValues: {
      document: null as DocumentFile | null,
      fileName: "",
    },
    validatorAdapter: zodValidator(),
    onSubmit: async ({ value }) => {
      try {
        let attachment: FileAttachmentDto | undefined;
        if (value.document) {
          attachment = await uploadAttachment(value.document.file);
          if (!attachment) {
            toast.error("Failed to upload attachment");
            return;
          }
        }

        if (!attachment) {
          toast.error("Failed to upload attachment");
          return;
        }

        // Store the attachment for later use in parsing
        setUploadedAttachment(attachment);

        uploadTimePunchFile.mutate({
          id: genStoreResourceId(),
          storeId,
          resourceTitle: value.fileName || value.document?.name || "Time Punch Data",
          attachment: attachment,
        });
      } catch (error) {
        console.error("Error uploading file:", error);
        toast.error("Error uploading file: " + getHumanReadableErrorMessage(error), {
          position: "top-center",
        });
      }
    },
  });

  const onDrop = (acceptedFiles: File[]) => {
    if (acceptedFiles.length > 0) {
      const file = acceptedFiles[0];
      const documentFile: DocumentFile = {
        file,
        name: file.name,
        size: file.size,
        type: file.type,
      };

      form.setFieldValue("document", documentFile);

      // Auto-populate filename if not already set
      if (!form.getFieldValue("fileName")) {
        const fileNameWithoutExtension = file.name.replace(/\.[^/.]+$/, ""); // Remove extension
        form.setFieldValue("fileName", fileNameWithoutExtension);
        // Trigger validation for the fileName field
        form.validateField("fileName", "change");
      }
    }
  };

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      "application/pdf": [".pdf"],
    },
    multiple: false,
    maxSize: 100 * 1024 * 1024, // 100MB max size
  });

  const handleClose = () => {
    form.reset();
    setUploadedAttachment(null);
    setParsedResult(null);
    setMappedEmployees([]);
    setSavedResults(null);
    setCurrentStep(WizardStep.UPLOAD_FILE);
    onClose();
  };

  const handleUpdatePersonMapping = useCallback((index: number, personId: string | null) => {
    setMappedEmployees(prev =>
      map(prev, (emp, i) =>
        i === index ? { ...emp, personId } : emp
      )
    );
  }, []);

  // Memoized callback functions for employee mapping
  const handleComboboxOpenChange = useCallback((index: number, open: boolean) => {
    setOpenComboboxes(prev => ({ ...prev, [index]: open }));
  }, []);

  const handlePersonMappingUpdateCallback = useCallback((index: number, personId: string) => {
    handleUpdatePersonMapping(index, personId);
  }, [handleUpdatePersonMapping]);

  const handleClearMappingCallback = useCallback((index: number) => {
    handleUpdatePersonMapping(index, null);
  }, [handleUpdatePersonMapping]);

  const handleSaveTimePunchData = useCallback(async () => {
    if (!parsedResult || !uploadedAttachment) return;

    // Check if all employees are mapped
    const unmappedEmployees = filter(mappedEmployees, emp => emp.personId === null);
    const hasUnmappedEmployees = !isEmpty(unmappedEmployees);

    // If there are unmapped employees, show confirmation dialog
    if (hasUnmappedEmployees) {
      setShowConfirmDialog(true);
      return;
    }

    // Proceed with saving
    await proceedWithSave();
  }, [mappedEmployees, parsedResult, uploadedAttachment]);

  const proceedWithSave = useCallback(async () => {
    if (!parsedResult || !uploadedAttachment) return;

    try {
      // Filter out entries for employees that weren't mapped to store employees and add personId
      const mappedEntries = flatMap(
        filter(mappedEmployees, emp => emp.personId !== null),
        emp => map(emp.entries, entry => ({
          ...entry,
          personId: emp.personId || undefined // Convert null to undefined for API compatibility
        }))
      );

      if (isEmpty(mappedEntries)) {
        toast.error("No employees mapped. Please map at least one employee to save data.");
        return;
      }

      // Save the mapped entries to database
      const saveResult = await saveTimePunchEntries.mutateAsync({
        storeId,
        fileId: uploadedAttachment.fileId,
        mappedEntries,
        dateRange: parsedResult.dateRange,
      });

      // Create a result object that matches the expected structure
      const finalResult: ParsedTimePunchResult = {
        rawText: parsedResult.rawText,
        timePunchEntries: mappedEntries,
        parseErrors: parsedResult.parseErrors,
        validationErrors: saveResult.validationErrors,
        savedEntries: saveResult.savedEntries,
        deletedEntries: saveResult.deletedEntries,
        dateRange: parsedResult.dateRange,
      };

      setSavedResults(finalResult);
      setCurrentStep(WizardStep.REVIEW_RESULTS);

      // Invalidate variance queries to refresh the table data
      await apiUtils.user.getTimePunchVarianceEntries.invalidate();

      // Call onSuccess callback to refresh other pages immediately after saving
      onSuccess?.();

      const mappedCount = filter(mappedEmployees, emp => emp.personId !== null).length;
      const totalEntries = mappedEntries.length;

      toast.success(
        `Success! Mapped ${mappedCount} employees and saved ${totalEntries} time punch entries to the database.`,
        { position: "top-center", duration: 6000 }
      );

    } catch (error) {
      console.error('Failed to save time punch data:', error);
      toast.error(
        "Failed to save time punch data: " + getHumanReadableErrorMessage(error),
        { position: "top-center", duration: 5000 }
      );
    }
  }, [mappedEmployees, parsedResult, uploadedAttachment, saveTimePunchEntries, storeId]);

  const handleConfirmSave = useCallback(async () => {
    if (!confirmProceed) {
      toast.error("Please confirm that you want to proceed by checking the checkbox.");
      return;
    }

    setShowConfirmDialog(false);
    setConfirmProceed(false);
    await proceedWithSave();
  }, [confirmProceed, proceedWithSave]);

  const handleCancelConfirm = useCallback(() => {
    setShowConfirmDialog(false);
    setConfirmProceed(false);
  }, []);

  const getStepTitle = () => {
    switch (currentStep) {
      case WizardStep.UPLOAD_FILE:
        return "Upload Time Punch Data";
      case WizardStep.MAP_EMPLOYEES:
        return "Map Employees";
      case WizardStep.REVIEW_RESULTS:
        return "Review Results";
      default:
        return "Upload Time Punch Data";
    }
  };

  const isLoading = uploadTimePunchFile.isPending || uploadStatus.isLoading || parseTimePunchPdf.isPending || saveTimePunchEntries.isPending;
  const document = form.getFieldValue("document");
  const fileName = form.getFieldValue("fileName");
  const canSubmit = document && fileName.trim().length > 0;
  const validMappings = filter(mappedEmployees, emp => emp.personId !== null);

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent
        size={"3xl"}
        className="max-h-[95vh] flex flex-col"
        onInteractOutside={(e) => {
          // Prevent closing when clicking on AlertDialog
          if (showConfirmDialog) {
            e.preventDefault();
          }
        }}
      >
        <DialogHeader className="flex-shrink-0">
          {/* Progress Indicator - moved to top and made more compact */}
          <div className="flex items-center justify-center space-x-4 mb-2 flex-shrink-0">
            {[1, 2, 3].map((step) => (
              <div key={step} className="flex items-center">
                <div
                  className={`
                    w-6 h-6 rounded-full flex items-center justify-center text-xs font-medium
                    ${step - 1 <= currentStep
                      ? "bg-primary text-primary-foreground"
                      : "bg-muted text-muted-foreground"
                    }
                  `}
                >
                  {step}
                </div>
                {step < 3 && (
                  <div
                    className={`
                      w-8 h-0.5 mx-1
                      ${step - 1 < currentStep ? "bg-primary" : "bg-muted"}
                    `}
                  />
                )}
              </div>
            ))}
          </div>

          {/* Only show title for non-MAP_EMPLOYEES steps */}
          {currentStep !== WizardStep.MAP_EMPLOYEES && (
            <DialogTitle className="mt-2">{getStepTitle()}</DialogTitle>
          )}
        </DialogHeader>

        {/* Step Content */}
        <div className={`flex-1 overflow-y-auto min-h-0 px-6 pb-4 ${currentStep === WizardStep.MAP_EMPLOYEES ? 'flex flex-col' : ''}`}>
          {currentStep === WizardStep.UPLOAD_FILE && (
            <div className="space-y-6">
              {/* Instructional Text */}
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <Text size="sm" className="font-medium text-blue-800 mb-3">
                  Follow these steps to upload your time punch data:
                </Text>
                <div className="space-y-2 text-sm text-blue-700">
                  <div>
                    <strong>Step 1:</strong> Log into TimePunch, <a
                      href="https://cfahome.okta.com/"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-blue-600 underline hover:text-blue-800"
                    >
                      CFA HOME
                    </a>
                  </div>
                  <div>
                    <strong>Step 2:</strong> Download the "Time Punch Detail Report" (unselect wages)
                  </div>
                  <div>
                    <strong>Step 3:</strong> Upload the file
                  </div>
                </div>
              </div>

              <form
                onSubmit={(e) => {
                  e.preventDefault();
                  form.handleSubmit();
                }}
                className="space-y-6"
              >
                <div className="max-w-lg mx-auto">
                <Label htmlFor="fileName">File Name</Label>
                <form.Field
                  name="fileName"
                  validators={{
                    onChange: z.string().min(1, "File name is required"),
                  }}
                  children={(field) => (
                    <div>
                      <FormInput
                        field={field}
                        placeholder="Enter a name for this file"
                        disabled={isLoading}
                      />
                      <FieldInfo field={field} />
                    </div>
                  )}
                />
              </div>

              <div>
                <Label>PDF Document</Label>
                <div
                  {...getRootProps()}
                  className={`
                    border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors
                    ${isDragActive ? "border-primary bg-primary/5" : "border-gray-300 hover:border-gray-400"}
                    ${document ? "bg-green-50 border-green-300" : ""}
                  `}
                >
                  <input {...getInputProps()} />

                  {document ? (
                    <div className="space-y-4">
                      <div className="flex items-center justify-center space-x-3">
                        <FileTextIcon className="w-8 h-8 text-green-600" />
                        <div className="text-left">
                          <p className="font-medium text-green-800">{document.name}</p>
                          <p className="text-sm text-green-600">{formatFileSize(document.size)}</p>
                        </div>
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation();
                            form.setFieldValue("document", null);
                          }}
                          disabled={isLoading}
                        >
                          <Trash2Icon className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>
                  ) : (
                    <div className="space-y-4">
                      <CloudUploadIcon className="w-12 h-12 mx-auto text-gray-400" />
                      <div>
                        <p className="text-lg font-medium text-gray-700">
                          {isDragActive ? "Drop your PDF here" : "Upload PDF Document"}
                        </p>
                        <p className="text-sm text-gray-500 mt-1">
                          Drag and drop a PDF file here, or click to browse
                        </p>
                        <p className="text-xs text-gray-400 mt-2">
                          Maximum file size: 100MB
                        </p>
                      </div>
                    </div>
                  )}
                </div>
              </div>

              {(uploadStatus.isLoading || parseTimePunchPdf.isPending) && (
                <div className="space-y-2">
                  <Label>Progress</Label>
                  <Progress value={parseTimePunchPdf.isPending ? 90 : 75} className="w-full" />
                  <Text size="sm" muted>
                    {parseTimePunchPdf.isPending ? "Parsing PDF content..." : "Uploading file..."}
                  </Text>
                </div>
              )}

              {(uploadTimePunchFile.isError || uploadStatus.isError || parseTimePunchPdf.isError) && (
                <ErrorAlert
                  error={uploadTimePunchFile.error || uploadStatus.error || parseTimePunchPdf.error}
                />
              )}
            </form>
            </div>
          )}

          {currentStep === WizardStep.MAP_EMPLOYEES && (
            <div className="space-y-3">
              {/* Instruction text */}
              <div className="text-center">
                <Text size="sm" muted className="mb-1">
                  Match the employee names from the PDF to actual employees in your store
                </Text>
                <Text size="xs" className="text-amber-700">
                  Saving will replace all existing time punch entries
                  {parsedResult?.dateRange && (
                    <>
                      {" "}in the date range {parsedResult.dateRange.startDate.toLocaleDateString()} - {parsedResult.dateRange.endDate.toLocaleDateString()}
                    </>
                  )}
                </Text>
              </div>

              {parsedResult?.parseErrors && parsedResult.parseErrors.length > 0 && (
                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-2">
                  <Text size="xs" className="font-medium text-yellow-800 mb-1">
                    Parse Warnings:
                  </Text>
                  <ul className="text-xs text-yellow-700 space-y-0.5">
                    {parsedResult.parseErrors.map((error, index) => (
                      <li key={index}>• {error}</li>
                    ))}
                  </ul>
                </div>
              )}

              <div className="border rounded-lg overflow-hidden flex-1">
                <div className="h-[500px] overflow-y-auto">
                  <Table>
                    <TableHeader className="sticky top-0 bg-white z-10">
                      <TableRow>
                        <TableHead className="w-12">Status</TableHead>
                        <TableHead className="w-48">PDF Employee Name</TableHead>
                        <TableHead className="w-48">Store Employee</TableHead>
                        <TableHead className="w-20">Entries</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {map(mappedEmployees, (employee, index) => {
                        const conflictingEmployeeName = duplicateMap.get(employee.pdfEmployeeName) || null;

                        return (
                          <EmployeeMappingRow
                            key={employee.pdfEmployeeName}
                            employee={employee}
                            employeeIndex={index}
                            sortedPeople={sortedPeople}
                            conflictingEmployeeName={conflictingEmployeeName}
                            isComboboxOpen={openComboboxes[index] || false}
                            onComboboxOpenChange={handleComboboxOpenChange}
                            onPersonMappingUpdate={handlePersonMappingUpdateCallback}
                            onClearMapping={handleClearMappingCallback}
                          />
                        );
                      })}
                    </TableBody>
                  </Table>
                </div>
              </div>

              <div className="bg-gray-50 border border-gray-200 rounded-lg p-2">
                <Text size="xs" className="text-gray-700">
                  <strong>Summary:</strong> {validMappings.length} of {mappedEmployees.length} employees mapped •{" "}
                  {sumBy(validMappings, emp => emp.entries.length)} time entries will be saved
                </Text>
              </div>
            </div>
          )}

          {currentStep === WizardStep.REVIEW_RESULTS && savedResults && (
            <div className="space-y-6">
              <div className="text-center">
                <CheckIcon className="w-16 h-16 text-green-500 mx-auto mb-4" />
                <Text size="xl" className="font-semibold text-green-800">
                  Time Punch Data Saved Successfully!
                </Text>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="bg-green-50 border border-green-200 rounded-lg p-4 text-center">
                  <Text size="xl" className="font-bold text-green-800">
                    {savedResults.savedEntries || 0}
                  </Text>
                  <Text size="sm" className="text-green-600">
                    Entries Saved
                  </Text>
                </div>
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 text-center">
                  <Text size="xl" className="font-bold text-blue-800">
                    {validMappings.length}
                  </Text>
                  <Text size="sm" className="text-blue-600">
                    Employees Mapped
                  </Text>
                </div>
                {savedResults.deletedEntries && savedResults.deletedEntries > 0 && (
                  <div className="bg-orange-50 border border-orange-200 rounded-lg p-4 text-center">
                    <Text size="xl" className="font-bold text-orange-800">
                      {savedResults.deletedEntries}
                    </Text>
                    <Text size="sm" className="text-orange-600">
                      Previous Entries Replaced
                    </Text>
                  </div>
                )}
              </div>

              {savedResults.validationErrors && savedResults.validationErrors.length > 0 && (
                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                  <Text size="sm" className="font-medium text-yellow-800 mb-2">
                    Validation Warnings:
                  </Text>
                  <ul className="text-sm text-yellow-700 space-y-1">
                    {savedResults.validationErrors.map((error, index) => (
                      <li key={index}>• {error}</li>
                    ))}
                  </ul>
                </div>
              )}

              {savedResults.dateRange && (
                <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
                  <Text size="sm" className="text-gray-700">
                    <strong>Date Range:</strong>{" "}
                    {savedResults.dateRange.startDate.toLocaleDateString()} -{" "}
                    {savedResults.dateRange.endDate.toLocaleDateString()}
                  </Text>
                </div>
              )}
            </div>
          )}
        </div>

        <DialogFooter className="flex-shrink-0">
          <div className="flex justify-between w-full">
          <div>
            {currentStep === WizardStep.MAP_EMPLOYEES && (
              <Button
                type="button"
                variant="outline"
                onClick={() => setCurrentStep(WizardStep.UPLOAD_FILE)}
                disabled={isLoading}
              >
                Back
              </Button>
            )}
          </div>

          <div className="flex space-x-3">
            <Button
              type="button"
              variant="outline"
              onClick={handleClose}
              disabled={isLoading}
            >
              {currentStep === WizardStep.REVIEW_RESULTS ? "Close" : "Cancel"}
            </Button>

            {currentStep === WizardStep.UPLOAD_FILE && (
              <Button
                type="submit"
                disabled={!canSubmit || isLoading}
                isLoading={isLoading}
                onClick={() => form.handleSubmit()}
              >
                Upload & Parse File
              </Button>
            )}

            {currentStep === WizardStep.MAP_EMPLOYEES && (
              <Button
                type="button"
                disabled={validMappings.length === 0 || isLoading}
                isLoading={saveTimePunchEntries.isPending}
                onClick={handleSaveTimePunchData}
              >
                Save {validMappings.length} Mapped Employee{validMappings.length !== 1 ? 's' : ''}
                ({sumBy(validMappings, emp => emp.entries.length)} entries)
              </Button>
            )}

            {currentStep === WizardStep.REVIEW_RESULTS && (
              <Button
                type="button"
                onClick={handleClose}
              >
                Done
              </Button>
            )}
          </div>
          </div>
        </DialogFooter>
      </DialogContent>

      {/* Confirmation Dialog for Unmapped Employees */}
      <AlertDialog open={showConfirmDialog} onOpenChange={(open) => {
        if (!open) {
          handleCancelConfirm();
        }
      }}>
        <AlertDialogContent
          className="max-w-md max-h-[80vh] flex flex-col"
          onEscapeKeyDown={(e) => {
            e.preventDefault();
            handleCancelConfirm();
          }}
        >
          <AlertDialogHeader className="flex-shrink-0">
            <AlertDialogTitle>Not All Employees Mapped</AlertDialogTitle>
            <AlertDialogDescription className="space-y-3">
              <div>
                {filter(mappedEmployees, emp => emp.personId === null).length} out of {mappedEmployees.length} PDF employees have not been mapped to store employees.
              </div>
            </AlertDialogDescription>
          </AlertDialogHeader>

          <div className="flex-1 min-h-0 space-y-3">
            <div>
              <strong className="text-sm">Unmapped employees:</strong>
            </div>
            <div className="border rounded bg-gray-50">
              <ScrollArea className="h-32 w-full" type="always">
                <div className="p-2">
                  {map(
                    filter(mappedEmployees, emp => emp.personId === null),
                    (emp, index) => (
                      <div key={index} className="text-sm py-1 border-b border-gray-200 last:border-b-0">
                        • {emp.pdfEmployeeName} ({emp.entries.length} entries)
                      </div>
                    )
                  )}
                </div>
              </ScrollArea>
            </div>
            <div className="text-sm text-gray-600">
              Time punch entries for unmapped employees will not be saved to the database.
            </div>
            <div className="flex items-start space-x-2 pt-2">
              <Checkbox
                id="confirm-proceed"
                checked={confirmProceed}
                onCheckedChange={(checked) => setConfirmProceed(checked === true)}
                className="mt-0.5"
              />
              <label
                htmlFor="confirm-proceed"
                className="text-sm font-medium leading-tight peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
              >
                I understand and want to proceed with saving only the mapped employees
              </label>
            </div>
          </div>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={handleCancelConfirm}>
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={handleConfirmSave}
              disabled={!confirmProceed}
            >
              Save Mapped Employees
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </Dialog>
  );
}
