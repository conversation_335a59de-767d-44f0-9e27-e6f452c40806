import React from "react";
import {api} from "@/src/api.ts";
import {toast} from "sonner";
import {FeatureList} from "./FeatureList.tsx";

export function GlobalFeaturesSection() {
  const [features] = api.feature.getAllFeatures.useSuspenseQuery();
  const toggleFeature = api.feature.toggleFeature.useMutation();
  const apiUtil = api.useUtils();

  const handleToggle = (featureId: string, isEnabled: boolean) => {
    toggleFeature.mutate({
      featureId,
      isEnabled
    }, {
      onSuccess: () => {
        apiUtil.feature.invalidate();
        const feature = features.find(f => f.id === featureId);
        toast(`${feature?.name} globally ${isEnabled ? 'enabled' : 'disabled'}`, {
          position: "top-center",
          dismissible: true
        });
      }
    });
  };

  return <FeatureList features={features} onToggle={handleToggle} />;
}
