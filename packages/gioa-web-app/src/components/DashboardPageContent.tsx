import DashboardWelcomeHeader from "@/src/components/DashboardWelcomeHeader.tsx";
import TodaysSnapshot from "@/src/components/DashboardTodaysSnapshot.tsx";
import DashboardThisWeek from "@/src/components/DashboardThisWeek.tsx";
import FeedbackForm from "@/src/components/FeedbackForm.tsx";
import React, {Suspense} from "react";
import {api} from "@/src/api.ts";
import CalendarStoreEvents from "@/src/components/CalendarStoreEvents.tsx";
import {Spinner} from "@/src/components/Spinner.tsx";

export interface DashboardPageContentProps {
  storeId: string;
  businessId: string;
}

export const DashboardPageContent: React.FC<DashboardPageContentProps> = ({storeId, businessId}) => {
  const [{people, timezone}] = api.user.getAllSchedulePeopleAtStore.useSuspenseQuery({storeId: storeId}, {
    staleTime: 1000 * 60 * 60 // 1 hour
  });

  return (
    <div className="flex flex-col lg:flex-row bg-gray-50">
      <div className="flex-grow p-8">
        <div className="flex flex-col space-y-6 w-full">
          <DashboardWelcomeHeader businessId={businessId} storeId={storeId}/>
          <TodaysSnapshot businessId={businessId} storeId={storeId}
                          allPeopleAtStore={people}/>
          <DashboardThisWeek businessId={businessId} storeId={storeId} allPeopleAtStore={people}/>
          <FeedbackForm businessId={businessId} storeId={storeId}/>
        </div>
      </div>
      <div className="w-full lg:w-96 p-8 lg:pl-0 bg-gray-50 shrink-0">
        <Suspense fallback={<Spinner size={"lg"}/>}>
          <CalendarStoreEvents storeId={storeId} timezone={timezone ?? null}/>
        </Suspense>
      </div>
    </div>
  );
}
