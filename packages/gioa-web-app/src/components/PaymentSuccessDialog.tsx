import React from 'react';
import {<PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON>eader, DialogTitle} from "@/src/components/ui/dialog.tsx";
import {Button} from "@/src/components/ui/button.tsx";
import {Text} from "@/src/components/Text.tsx";
import {api} from "@/src/api.ts";
import {getHumanReadableErrorMessageString} from "@/src/components/ErrorAlert.tsx";
import {DotLottieReact} from '@lottiefiles/dotlottie-react';
import successAnimation from "@/src/assets/success.lottie"

interface PaymentSuccessDialogProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  daysRemaining: number;
  storeId: string;
}

export function PaymentSuccessDialog({
                                       isOpen,
                                       onOpenChange, daysRemaining,
                                       storeId,
                                     }: PaymentSuccessDialogProps) {
  const startCustomerPortalSession = api.payment.startCustomerPortalSession.useMutation();

  const handleManageSubscription = () => {
    startCustomerPortalSession.mutate({
      storeId: storeId,
    }, {
      onSuccess: (data) => {
        window.location.href = data.url;
      },
      onError: (error) => {
        alert("Failed to start customer portal: " + getHumanReadableErrorMessageString(error));
      }
    });
  };

  const handleClose = () => {
    onOpenChange(false);
  };

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent size="lg">
        <DialogHeader>
          <DialogTitle>Payment Method Added Successfully!</DialogTitle>
        </DialogHeader>

        <DotLottieReact className={"h-[140px]"}
          src={successAnimation}
          speed={1}
          autoplay
        />

        <Text>
          Your payment method has been added to your account.
        </Text>

        <Text>
          Your payment method will be automatically charged when your trial ends in {daysRemaining} day{daysRemaining !== 1 ? 's' : ''}.
          You can manage your subscription and billing details at any time.
        </Text>

        <DialogFooter>
          <Button variant="outline" onClick={handleClose} disabled={startCustomerPortalSession.isPending}>
            Close
          </Button>
          <Button
            onClick={handleManageSubscription}
            isLoading={startCustomerPortalSession.isPending}
            disabled={startCustomerPortalSession.isPending}
          >
            Manage Subscription
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
