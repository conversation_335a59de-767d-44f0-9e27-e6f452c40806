import { createRouter } from "@tanstack/react-router"
import { routeTree } from "./routeTree.gen"
import {clientUtils, queryClient, trpcClient} from "./api.ts";

// Create a new router instance
export const router = createRouter({
  routeTree,
  defaultPreload: 'intent',
  // Since we're using React Query, we don't want loader calls to ever be stale
  // This will ensure that the loader is always called when the route is preloaded or visited
  defaultPreloadStaleTime: 0,
  context: {
    trpc: trpcClient,
    queryClient: queryClient,
    clientUtils: clientUtils
  }
})

// Register the router instance for type safety
declare module '@tanstack/react-router' {
  interface Register {
    router: typeof router
  }
}
