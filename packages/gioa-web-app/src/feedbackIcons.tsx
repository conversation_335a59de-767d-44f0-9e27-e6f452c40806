import React from "react";

export const SportsIcon: React.FC<React.SVGProps<SVGSVGElement>> = () => {
  return (
          <svg width="20" height="12" viewBox="0 0 20 12" fill="none">
            <path
                    d="M8.51314 11.4433C7.00681 11.4433 5.72322 10.9128 4.66239 9.85201C3.60139 8.79101 3.07089 7.50735 3.07089 6.00101C3.07089 5.79418 3.08406 5.58735 3.11039 5.38051C3.13672 5.17368 3.16972 4.98818 3.20939 4.82401C3.13256 4.85735 3.04539 4.87918 2.94789 4.88951C2.85039 4.89968 2.76481 4.90476 2.69114 4.90476C2.07131 4.90476 1.55014 4.69285 1.12764 4.26901C0.705307 3.84518 0.494141 3.32635 0.494141 2.71251C0.494141 2.09868 0.696057 1.57985 1.09989 1.15601C1.50356 0.73218 2.01381 0.520264 2.63064 0.520264C3.13581 0.520264 3.58514 0.664846 3.97864 0.954013C4.37231 1.24301 4.63514 1.61001 4.76714 2.05501C5.25297 1.59335 5.82281 1.2286 6.47664 0.960764C7.13047 0.692764 7.80931 0.558763 8.51314 0.558763H19.5131V3.55876H13.9554V6.00101C13.9554 7.51268 13.4263 8.79768 12.3681 9.85601C11.31 10.9142 10.025 11.4433 8.51314 11.4433ZM2.68639 3.71251C2.96972 3.71251 3.20722 3.61668 3.39889 3.42501C3.59056 3.23335 3.68639 2.99585 3.68639 2.71251C3.68639 2.42918 3.59056 2.19168 3.39889 2.00001C3.20722 1.80835 2.96972 1.71251 2.68639 1.71251C2.40306 1.71251 2.16556 1.80835 1.97389 2.00001C1.78222 2.19168 1.68639 2.42918 1.68639 2.71251C1.68639 2.99585 1.78222 3.23335 1.97389 3.42501C2.16556 3.61668 2.40306 3.71251 2.68639 3.71251ZM8.51489 7.80876C9.01256 7.80876 9.43797 7.6316 9.79114 7.27726C10.1443 6.92293 10.3209 6.49693 10.3209 5.99926C10.3209 5.50176 10.1437 5.07643 9.78939 4.72326C9.43506 4.36993 9.00914 4.19326 8.51164 4.19326C8.01397 4.19326 7.58856 4.37043 7.23539 4.72476C6.88222 5.0791 6.70564 5.5051 6.70564 6.00276C6.70564 6.50026 6.88281 6.9256 7.23714 7.27876C7.59147 7.6321 8.01739 7.80876 8.51489 7.80876Z"
                    fill="#3CD856"/>
          </svg>
  )
}

export const EditNotificationIcon: React.FC<React.SVGProps<SVGSVGElement>> = () => {
  return (
          <svg width="15" height="18" viewBox="0 0 15 18" fill="none">
            <path
                    d="M6.9984 17.8854C6.54235 17.8854 6.15247 17.7231 5.82873 17.3986C5.50484 17.0741 5.3429 16.684 5.3429 16.2283H8.6571C8.6571 16.6855 8.4947 17.076 8.1699 17.3998C7.84509 17.7235 7.45459 17.8854 6.9984 17.8854ZM13.1875 15.3116H0.8125C0.617708 15.3116 0.454465 15.2458 0.322771 15.1141C0.190924 14.9822 0.125 14.8188 0.125 14.6239C0.125 14.4291 0.190924 14.2658 0.322771 14.1341C0.454465 14.0026 0.617708 13.9368 0.8125 13.9368H1.7821V7.0969C1.7821 5.86413 2.1626 4.77414 2.92358 3.82692C3.68442 2.8797 4.66128 2.27386 5.85417 2.0094V1.43831C5.85417 1.12008 5.96547 0.849509 6.18806 0.626606C6.41066 0.403856 6.68092 0.29248 6.99885 0.29248C7.31694 0.29248 7.58758 0.403856 7.81079 0.626606C8.03415 0.849509 8.14583 1.12008 8.14583 1.43831V2.0094C8.16936 2.01536 8.19289 2.02124 8.21642 2.02704C8.23979 2.033 8.26324 2.03888 8.28677 2.04469C8.5277 2.12108 8.66551 2.28768 8.70019 2.5445C8.73487 2.80132 8.6529 3.02904 8.45429 3.22765L5.59315 6.06565C5.44128 6.21674 5.32051 6.39526 5.23083 6.60121C5.141 6.80715 5.09608 7.01997 5.09608 7.23967V10.1643C5.09608 10.62 5.25833 11.0101 5.58283 11.3346C5.90733 11.6591 6.29745 11.8214 6.75319 11.8214H9.67781C9.89766 11.8214 10.1107 11.7785 10.317 11.6926C10.5231 11.6069 10.7013 11.4888 10.8518 11.3383L11.1637 11.0262C11.36 10.83 11.5848 10.7842 11.8379 10.8887C12.0912 10.9932 12.2179 11.1848 12.2179 11.4634V13.9368H13.1875C13.3823 13.9368 13.5455 14.0027 13.6772 14.1344C13.8091 14.2662 13.875 14.4296 13.875 14.6246C13.875 14.8194 13.8091 14.9826 13.6772 15.1143C13.5455 15.2458 13.3823 15.3116 13.1875 15.3116ZM7.10588 8.98317V7.73513C7.10588 7.62467 7.12581 7.51711 7.16569 7.41246C7.20572 7.30796 7.26858 7.21751 7.35429 7.14113L12.0822 2.4361C12.1869 2.33741 12.3049 2.26102 12.4363 2.20694C12.5678 2.15285 12.7053 2.12581 12.8488 2.12581C12.9923 2.12581 13.1344 2.15583 13.2753 2.21588C13.4161 2.27592 13.5355 2.35406 13.6335 2.45031L14.4814 3.30694C14.5801 3.42091 14.6565 3.5413 14.7105 3.66811C14.7646 3.79491 14.7917 3.93073 14.7917 4.07556C14.7917 4.22055 14.7646 4.36256 14.7105 4.50158C14.6565 4.64061 14.5801 4.75947 14.4814 4.85817L9.77635 9.56319C9.69065 9.6489 9.59722 9.71177 9.49608 9.75179C9.39479 9.79167 9.29022 9.81161 9.18235 9.81161H7.93431C7.69949 9.81161 7.50272 9.73224 7.34398 9.5735C7.18524 9.41477 7.10588 9.21799 7.10588 8.98317ZM12.8509 4.96038L13.6988 4.07556L12.8509 3.21871L11.98 4.08954L12.8509 4.96038Z"
                    fill="#FF947A"/>
          </svg>
  )
}

export const StylusNoteIcon: React.FC<React.SVGProps<SVGSVGElement>> = () => {
  return (
          <svg width="20" height="13" viewBox="0 0 20 13" fill="none">
            <path
                    d="M1.69678 9.01104C1.69678 9.50711 1.8672 9.89494 2.20805 10.1745C2.5489 10.4543 3.09424 10.6412 3.84407 10.7353C4.02969 10.7599 4.17888 10.8441 4.29163 10.9881C4.40453 11.1321 4.45625 11.297 4.44678 11.4826C4.43746 11.6777 4.36787 11.8387 4.23801 11.9657C4.10815 12.0925 3.95331 12.1436 3.77349 12.119C2.62994 11.978 1.76881 11.6478 1.19009 11.1283C0.611215 10.6089 0.321777 9.90311 0.321777 9.01104C0.321777 8.08276 0.711361 7.3298 1.49053 6.75215C2.26969 6.17449 3.35266 5.82456 4.73942 5.70233C5.40584 5.64474 5.90565 5.52427 6.23886 5.34094C6.57192 5.1576 6.73844 4.90376 6.73844 4.57942C6.73844 4.17868 6.56481 3.86373 6.21755 3.63456C5.87028 3.4054 5.30065 3.23146 4.50865 3.11275C4.32287 3.088 4.17063 3.00283 4.05192 2.85723C3.93321 2.71148 3.88624 2.54572 3.91099 2.35994C3.93558 2.16499 4.02045 2.00641 4.16559 1.88419C4.31073 1.76197 4.47909 1.71613 4.67067 1.74669C5.83881 1.92406 6.70468 2.2449 7.26828 2.70919C7.83172 3.17333 8.11344 3.79674 8.11344 4.57942C8.11344 5.30679 7.83409 5.88353 7.27538 6.30963C6.71652 6.73557 5.90527 6.99147 4.84163 7.07733C3.79342 7.16549 3.00723 7.36998 2.48305 7.69081C1.95887 8.01165 1.69678 8.45172 1.69678 9.01104ZM11.0839 11.5443L7.94592 8.4065L15.8082 0.553188C16.0784 0.282924 16.3943 0.149243 16.7558 0.152146C17.1171 0.155049 17.4329 0.288729 17.7032 0.553188L18.9372 1.78702C19.2075 2.05744 19.3426 2.37476 19.3426 2.73898C19.3426 3.10335 19.2075 3.42067 18.9372 3.69094L11.0839 11.5443ZM7.32374 12.8753C7.11688 12.9246 6.93476 12.8705 6.7774 12.713C6.61989 12.5555 6.56581 12.3734 6.61515 12.1667L7.21969 9.21752L10.2589 12.2565L7.32374 12.8753Z"
                    fill="#007AFF"/>
          </svg>
  )
}

export const ThumbsUpIcon: React.FC<React.SVGProps<SVGSVGElement>> = () => {
  return (
          <svg width="16" height="15" viewBox="0 0 16 15" fill="none">
            <path
                    d="M12.2981 14.3747H4.87025V5.37474L9.84613 0.427734L10.4806 1.06223C10.5586 1.14011 10.6233 1.24323 10.6747 1.37161C10.7262 1.49998 10.7519 1.62092 10.7519 1.73442V1.92473L9.95563 5.37474H14.5192C14.8759 5.37474 15.1911 5.51155 15.4646 5.78517C15.7382 6.05867 15.875 6.3738 15.875 6.73055V7.94199C15.875 8.01986 15.8668 8.10399 15.8504 8.19436C15.8341 8.28474 15.8096 8.36892 15.7769 8.44692L13.6278 13.518C13.5202 13.7584 13.3399 13.9613 13.0871 14.1267C12.8342 14.292 12.5712 14.3747 12.2981 14.3747ZM3.74525 5.37474V14.3747H0.875V5.37474H3.74525Z"
                    fill="#BF83FF"/>
          </svg>
  )
}
