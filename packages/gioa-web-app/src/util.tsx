import {dollars, Dollars} from "../../api/src/scheduling/metrics/dollars.ts";

export function parseNumberOrUndef(str: string | undefined): number | undefined {
  if (str === undefined) return undefined;

  if (str.trim() === '') return undefined;

  const num = Number(str);

  if (isNaN(num) || !isFinite(num)) {
    return undefined;
  }

  return num;
}

export function parseDollarsOrUndef(str: string | undefined): Dollars | undefined {
  const d = parseNumberOrUndef(str);
  if (d === undefined) return undefined;
  return dollars(d);
}

export function getStripeLink(stripeCustomerId: string): string {
  const isDev = import.meta.env.VITE_GIOA_ENV === "development" || import.meta.env.VITE_GIOA_ENV === "beta";
  const testMode = isDev ? "test/" : "";
  return `https://dashboard.stripe.com/${testMode}customers/${stripeCustomerId}`;
}
