import {SchedulePersonDto} from "../../api/src/schedulePersonDto.ts";
import {ValidationMessage} from "../../api/src/scheduleValidation.types.ts";
import {messageToHumanReadable as getMsgStr} from "../../api/src/scheduleBuilder.util.ts";
import React from "react";
import {Link} from "@/src/components/Link.tsx";
import {ArrowRightIcon} from "lucide-react";
import {DateTime} from "luxon";

/**
 * Wraps the raw string with extras for the web app. Like links to time off requests and such.
 */
export const messageToHumanReadable = ({people, routeFullPath, activeDay}: {
  people: SchedulePersonDto[];
  routeFullPath: string | undefined;
  activeDay: DateTime | undefined;
}) => (msg?: ValidationMessage): string | React.ReactNode => {
  const messageStr = getMsgStr(people)(msg);

  switch (msg?.code) {
    case "TimeOffRequestConflict":
      if (routeFullPath && activeDay) {
        return <span>
        {messageStr}.{' '}
          <Link className={"inline-flex flex-row items-center gap-1 text-sm"}
                from={routeFullPath as any}
                search={{
                  selectedDate: activeDay.toISO(),
                  toReqId: msg.info.timeOffRequestId,
                  timeOffFilter: ["approved", "pending"]
                } as any}
                to={"../../reports/availability" as any}>
          View Request <ArrowRightIcon size={12}/>
        </Link>
      </span>
      }
  }

  return messageStr;
}
