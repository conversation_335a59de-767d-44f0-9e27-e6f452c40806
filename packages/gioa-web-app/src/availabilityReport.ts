import {RowData, RowSpan} from "@/src/components/TimelineGrid.tsx";
import {SchedulePersonDto, SchedulePersonTimeOffDto} from "../../api/src/schedulePersonDto.ts";
import {DailyTimeRange, IsoWeekDateWithDay} from "../../api/src/timeSchemas.ts";
import {compact, filter, find, flatMap, includes, intersectionWith, map, orderBy, some, sumBy, uniq} from "lodash";
import {getEffectiveAvailability} from "../../api/src/getEffectiveAvailability.ts";
import {
  dateRangeToDtRange,
  doIncrementRangesOverlap,
  formatDurationFriendly,
  getDurationMinutes,
  IncrementTimeRange,
  isoWeekDateToDateTime,
  isTimeAllDay
} from "../../api/src/date.util.ts";
import {
  dailyTimeRangeToIncrementRange,
  dateRangeToIncrementRange,
  incrementRangeToDailyTimeRange
} from "../../api/src/scheduleBuilder.util.ts";
import {shiftLeaderStorePositionTemplateId} from "../../api/src/stockPositionTemplates.ts";
import {StoreAreaDto} from "@gioa/api/src/schemas.ts";
import {getPersonTimeOffOnDay, isTimeOffAllDay} from "../../api/src/personTimeOff.ts";

export interface AvailabilitySpanMeta {
  availability: DailyTimeRange;
  isAllDay: boolean;
}

export interface HeatmapSpanMeta {
  numAvailable: number;
  numTimeOff: number;
  numTimeOffPending: number;
}

export interface TimeOffSpanMeta {
  timeOff: SchedulePersonTimeOffDto;
  range: DailyTimeRange;
  isAllDay: boolean;
}

export type ReportSpanMeta = AvailabilitySpanMeta | HeatmapSpanMeta | TimeOffSpanMeta;

function isAvailabilityRow(row: RowData<SchedulePersonDto | {}, ReportSpanMeta>): row is RowData<SchedulePersonDto, AvailabilitySpanMeta> {
  return "availability" in row.metadata;
}

function isAvailabilitySpan(span: RowSpan<ReportSpanMeta>): span is RowSpan<AvailabilitySpanMeta> {
  return "availability" in span.metadata;
}

function isHeatmapSpan(span: RowSpan<ReportSpanMeta>): span is RowSpan<HeatmapSpanMeta> {
  return "numAvailable" in span.metadata;
}

function isTimeOffSpan(span: RowSpan<ReportSpanMeta>): span is RowSpan<TimeOffSpanMeta> {
  return "timeOff" in span.metadata;
}

function pointsToRanges(points: number[]): IncrementTimeRange[] {
  if (points.length < 2) {
    return [];
  }

  const sortedPoints = orderBy(points, p => p, ['asc']);
  const ranges: IncrementTimeRange[] = [];

  for (let i = 0; i < sortedPoints.length - 1; i++) {
    ranges.push({
      start: sortedPoints[i],
      end: sortedPoints[i + 1]
    })
  }

  return ranges;
}

function buildHeatmap(spans: RowSpan<AvailabilitySpanMeta | TimeOffSpanMeta>[]): RowSpan<HeatmapSpanMeta>[] {
  const points = uniq(flatMap(spans, range => [range.start, range.end]));
  const heatmap = map(pointsToRanges(points), (r): RowSpan<HeatmapSpanMeta> => ({
    ...r,
    metadata: {
      numAvailable: 0,
      numTimeOff: 0,
      numTimeOffPending: 0,
    }
  }));

  for (let n = 0; n < spans.length; n++) {
    const span = spans[n];
    for (let h = 0; h < heatmap.length; h++) {
      const heatmapSpan = heatmap[h];
      if (doIncrementRangesOverlap(span, heatmapSpan)) {
        if (isAvailabilitySpan(span)) {
          heatmapSpan.metadata.numAvailable++;
        } else if (isTimeOffSpan(span)) {
          if (span.metadata.timeOff.status === "pending") {
            heatmapSpan.metadata.numTimeOffPending++;
          } else {
            heatmapSpan.metadata.numTimeOff++;
          }
        }
      }
    }
  }

  return heatmap
}

/**
 * Given list of people and their availability, generate rows with spans for the availability report.
 * The first row will be a heatmap row
 */
function getRowsForTeamMemberAvailability({isoWeek, people, timezone, storeHours, includeApprovedTimeOff, includePendingTimeOff}: {
  isoWeek: IsoWeekDateWithDay;
  people: SchedulePersonDto[];
  timezone: string;
  storeHours: DailyTimeRange;
  includeApprovedTimeOff: boolean;
  includePendingTimeOff: boolean;
}): Array<RowData<SchedulePersonDto | {}, ReportSpanMeta>> {
  const activeDate = isoWeekDateToDateTime(isoWeek, timezone);

  const peopleRows = map(people, (person): RowData<SchedulePersonDto, AvailabilitySpanMeta | TimeOffSpanMeta> => {
    const {availability} = getEffectiveAvailability(person.availability, isoWeek, timezone);
    const availabilityToday = filter(availability, a => a.dayOfWeek === isoWeek.day);
    const timeOffToday = getPersonTimeOffOnDay({
      person,
      day: activeDate,
      timezone
    })

    const availabilitySpans = map(availabilityToday, avail => {
      const isAllDay = isTimeAllDay(avail)

      return {
        ...dailyTimeRangeToIncrementRange(avail, storeHours),
        metadata: {
          availability: avail,
          isAllDay
        }
      }
    })

    const timeOffSpans = compact(map(timeOffToday, timeOff => {
      if (!includePendingTimeOff && timeOff.status === "pending") return;
      if (!includeApprovedTimeOff && timeOff.status === "approved") return;

      const timeOffIncRange = dateRangeToIncrementRange({
        range: timeOff,
        storeHours,
        dayOfWeek: isoWeek.day,
        isoWeek,
        timezone,
      });
      if (!timeOffIncRange) {
        return;
      }

      return {
        ...timeOffIncRange,
        metadata: {
          timeOff,
          isAllDay: isTimeOffAllDay(dateRangeToDtRange(timeOff, timezone), activeDate),
          range: incrementRangeToDailyTimeRange(timeOffIncRange, storeHours)
        }
      }
    }));

    const spans = [...availabilitySpans, ...timeOffSpans];

    return {
      id: person.id,
      metadata: person,
      spans: spans
    }
  });

  const availabilityRanges = flatMap(peopleRows, row => row.spans);
  const heatmapSpans = buildHeatmap(availabilityRanges)
  const totalsRow: RowData<{}, HeatmapSpanMeta> = {
    id: "totals",
    metadata: {},
    spans: heatmapSpans
  }

  return [totalsRow, ...orderBy(peopleRows, row => row.spans[0]?.start, ["asc"])];
}

function getDurationAvailable(row: RowData<SchedulePersonDto, AvailabilitySpanMeta>): string {
  if (some(row.spans, s => s.metadata.isAllDay)) {
    return "all day";
  }

  const durationMins = sumBy(row.spans, s => s.metadata.availability ? getDurationMinutes(s.metadata.availability) : 0);
  return formatDurationFriendly(durationMins);
}

function filterPeopleByStoreAreaTraining<TPerson extends SchedulePersonDto>({people, filterAreas, storeAreas}: {
  people: TPerson[],
  storeAreas: StoreAreaDto[],
  filterAreas: string[],
}): TPerson[] {
  if (includes(filterAreas, "all")) {
    return people;
  }

  if (includes(filterAreas, "shiftLeaders")) {
    // filter to people who have training in a shift lead position
    const allPositions = flatMap(storeAreas, sa => sa.positions);
    const shiftLeadPositions = filter(allPositions, position =>
      position.createdFromTemplateId === shiftLeaderStorePositionTemplateId
      // for backwards compat (BOH shift leader position used to have "leader" ID)
      || position.createdFromTemplateId === "leader");

    return filter(people, person => {
      return intersectionWith(person.training, shiftLeadPositions,
        (training, position) => Boolean(training.positionId === position.id && training.isCompleted)).length > 0;
    });
  }

  const storeArea = find(storeAreas, sa => includes(filterAreas, sa.id));
  if (!storeArea) {
    return people;
  }

  const storeAreaPositions = storeArea.positions;

  // filter to people who have at least one training in a position in the store area
  return filter(people, person => {
    return intersectionWith(person.training, storeAreaPositions,
      (training, position) => Boolean(training.positionId === position.id && training.isCompleted)).length > 0;
  });
}

export {
  getRowsForTeamMemberAvailability,
  getDurationAvailable,
  isAvailabilityRow,
  filterPeopleByStoreAreaTraining,
  isAvailabilitySpan,
  isHeatmapSpan,
  isTimeOffSpan
}
