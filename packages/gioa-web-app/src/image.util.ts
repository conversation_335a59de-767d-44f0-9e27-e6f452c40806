import { last } from "lodash";

export type MediaType = 'image' | 'video' | 'document' | 'unknown';

export function inferMimeTypeFromFile(file: File): string {
  // If the file already has a type, use it
  if (file.type) {
    return file.type;
  }

  // Otherwise, try to infer from the extension
  return inferMimeTypeFromFilename(file.name);
}

export function inferMimeTypeFromFilename(filename: string): string {
  const extension = last(filename.split("."))?.toLowerCase();

  if (!extension) return 'application/octet-stream';

  // Common image types
  const imageTypes: Record<string, string> = {
    'jpg': 'image/jpeg',
    'jpeg': 'image/jpeg',
    'png': 'image/png',
    'gif': 'image/gif',
    'webp': 'image/webp',
    'svg': 'image/svg+xml',
    'tiff': 'image/tiff',
    'tif': 'image/tiff',
    'bmp': 'image/bmp',
    'ico': 'image/x-icon',
    'heic': 'image/heic',
    'heif': 'image/heif',
    'avif': 'image/avif',
  };

  // Common video types
  const videoTypes: Record<string, string> = {
    'mp4': 'video/mp4',
    'mov': 'video/quicktime',
    'avi': 'video/x-msvideo',
    'wmv': 'video/x-ms-wmv',
    'flv': 'video/x-flv',
    'webm': 'video/webm',
    'm4v': 'video/x-m4v',
    'mkv': 'video/x-matroska',
    '3gp': 'video/3gpp',
    '3g2': 'video/3gpp2',
    'ts': 'video/mp2t',
    'ogv': 'video/ogg',
  };

  // Document types
  const documentTypes: Record<string, string> = {
    'pdf': 'application/pdf',
    'doc': 'application/msword',
    'docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'xls': 'application/vnd.ms-excel',
    'xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'ppt': 'application/vnd.ms-powerpoint',
    'pptx': 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
    'txt': 'text/plain',
    'rtf': 'application/rtf',
    'csv': 'text/csv',
  };

  return (
    imageTypes[extension] ||
    videoTypes[extension] ||
    documentTypes[extension] ||
    'application/octet-stream'
  );
}
