import React from 'react';
import {find, first, includes, last, times} from "lodash";
import {daysOfWeek} from "@/src/components/AvailabilityWeek.tsx";
import {cn} from "@/src/util.ts";

export interface ExampleCalendarProps {
  highlightedRange: number[];
}

const dayIdxToLabel: { [key: number]: string } = {
  0: "S",
  1: "M",
  2: "T",
  3: "W",
  4: "Th",
  5: "F",
  6: "S"
}

export const ExampleCalendar: React.FC<ExampleCalendarProps> = ({highlightedRange}) => {
  const firstInRange = first(highlightedRange);
  const lastInRange = last(highlightedRange);
  console.log(highlightedRange);

  return (
    <div className={"py-4 px-2 border rounded-lg inline-block"}>
      <div className="inline-grid gap-y-1 grid-cols-7">
        {times(6, w => {
          return times(7, d => {
            const dayOfMonth = d + w * 7;
            const isHighlighted = includes(highlightedRange, dayOfMonth);
            const isFirstInRange = dayOfMonth === firstInRange;
            const isLastInRange = dayOfMonth === lastInRange;
            return <div key={`${w}-${d}`} className={cn("text-center px-2 py-1",
              isHighlighted ? "bg-black text-white" : undefined,
              isFirstInRange ? "rounded-l-full" : undefined,
              isLastInRange ? "rounded-r-full" : undefined,
            )}>
              {dayIdxToLabel[d]}
            </div>
          })
        })}
      </div>
    </div>
  );
}
