import {map, reduce, times} from 'lodash';
import currency from 'currency.js';
import {
  ForecastDataPoints
} from "../../api/src/scheduling/metrics/scheduleHourlySalesForecast/scheduleHourlySalesForecastTypes.ts";
import {cents, Cents} from "../../api/src/scheduling/metrics/cents.ts";
import {toCents} from "../../api/src/scheduling/metrics/dollars.ts";
import {parseDollarsOrUndef} from "@/src/util.tsx";

function sumCurrencies(values: currency[]): currency {
  return reduce(values, (acc, value) => acc.add(value), currency(0));
}

/**
 * Adjusts hourly values to match a new total while maintaining the same proportions.
 *
 * @param hourlyValues - Array of current hourly values (in dollars)
 * @param currentTotal - Current total of all hourly values
 * @param newTotal - New desired total
 * @returns Array of adjusted hourly values that sum to the new total
 */
export function adjustHourlyValuesToMatchTotal(
  hourlyValues: number[],
  currentTotal: number,
  newTotal: number
): number[] {
  const currencyTotal = currency(currentTotal);
  const currencyNewTotal = currency(newTotal);

  // if current total is 0, distribute the new total evenly
  if (currencyTotal.value === 0) {
    return map(currencyNewTotal.distribute(hourlyValues.length), c => c.value);
  }

  const adjustedValues = map(hourlyValues, hourValue => {
    // calculate what percentage of the current total this hour represents
    const percentage = hourValue / currentTotal;

    // calculate that percentage of the new total
    return currency(currencyNewTotal).multiply(percentage);
  });

  return map(adjustedValues, curr => Number(curr));
}

export function convertCentsToDollars(amountCents: number): string {
  return (amountCents / 100).toFixed(2);
}

// we'll receive an array of 168 strings, each representing the dollars for an hour of the week
// convert it to a map of hours to cents
export function convertFormForecastToMap(hourToDollarNumStr: string[]): Map<number, { amountCents: Cents }> {
  const forecast = new Map<number, { amountCents: Cents }>();
  for (let hour = 0; hour < hourToDollarNumStr.length; hour++) {
    const amountDollars = parseDollarsOrUndef(hourToDollarNumStr[hour]);
    if (Number.isFinite(amountDollars)) {
      const amountCents = toCents(amountDollars!);
      forecast.set(hour, {amountCents});
    } else {
      // invalid inputs just get set to 0
      forecast.set(hour, {amountCents: cents(0)});
    }
  }

  return forecast;
}

export function convertForecastToForm(forecast: ForecastDataPoints | undefined): string[] {
  const form = times(168, () => "0");
  if (!forecast) return form;

  for (const [hour, dataPoint] of forecast.entries()) {
    // convert the amountCents to dollars
    form[hour] = convertCentsToDollars(dataPoint.amountCents);
  }

  return form;
}

export function convertFormDollarValueToCents(value: string): number {
  return Math.round(Number(value) * 100);
}

