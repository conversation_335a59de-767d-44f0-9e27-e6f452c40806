import {describe, expect, it} from 'vitest';
import {PublishedSchedule, Shift} from '../../api/src/scheduleSchemas';
import {ApprovedPersonAvailability} from "../../api/src/availabilitySchemas.ts";
import {DateTimeRange} from "../../api/src/timeSchemas.ts";
import {getPeopleAvailableForShift} from "../../api/src/schedulePeople.ts";
import {SchedulePersonDto} from "../../api/src/schedulePersonDto.ts";

function createMockSchedule(shift: Shift): PublishedSchedule {
  return {
    id: 'schedule1',
    isPublished: true,
    publishedAt: new Date(),
    week: { year: 2024, week: 1 },
    days: [
      {
        dayOfWeek: 1,
        areas: [
          {
            id: 'area1',
            title: 'Area 1',
            shifts: [shift],
            countsTowardsLabor: true,
          },
        ],
      },
    ],
  } as PublishedSchedule;
}

function createMockPerson(id: string, availability: ApprovedPersonAvailability[], timeOff: DateTimeRange[]): SchedulePersonDto {
  return {
    id,
    firstName: 'John',
    lastName: 'Doe',
    availability,
    timeOff,
  } as SchedulePersonDto;
}

describe('getPeopleAvailableForShift', () => {
  it('should return an empty array when the shift is not found', () => {
    const schedule = createMockSchedule({
      id: 'shift1',
      range: { start: '09:00', end: '17:00' },
    } as Shift);
    const people = [createMockPerson('person1', [], [])];

    const result = getPeopleAvailableForShift({
      schedule,
      people,
      shiftId: 'non-existent-shift',
      timezone: "America/Denver"
    });

    expect(result).toEqual([]);
  });

  it('should return people available for the shift', () => {
    const shift = {
      id: 'shift1',
      range: { start: '09:00', end: '17:00' },
    } as Shift;
    const schedule = createMockSchedule(shift);
    const availablePerson = createMockPerson('person1', [
      { effectiveAt: '2024-01-01', ranges: [{ dayOfWeek: 1, start: '08:00', end: '18:00' }] },
    ] as ApprovedPersonAvailability[], []);
    const unavailablePerson = createMockPerson('person2', [
      { effectiveAt: '2024-01-01', ranges: [{ dayOfWeek: 1, start: '12:00', end: '20:00' }] },
    ] as ApprovedPersonAvailability[], []);
    const people = [availablePerson, unavailablePerson];

    const result = getPeopleAvailableForShift({
      schedule,
      people,
      shiftId: 'shift1',
      timezone: "America/Denver"
    });

    expect(result).toEqual([availablePerson]);
  });

  it('should exclude people with time off during the shift', () => {
    const shift = {
      id: 'shift1',
      range: { start: '09:00', end: '17:00' },
    } as Shift
    const schedule = createMockSchedule(shift);
    const availablePerson = createMockPerson('person1', [
      { effectiveAt: '2024-01-01', ranges: [{ dayOfWeek: 1, start: '08:00', end: '18:00' }] },
    ] as ApprovedPersonAvailability[], []);
    const personWithTimeOff = createMockPerson('person2', [
      { effectiveAt: '2024-01-01', ranges: [{ dayOfWeek: 1, start: '08:00', end: '18:00' }] },
    ] as ApprovedPersonAvailability[], [{ start: new Date('2024-01-01T16:00:00Z'), end: new Date('2024-01-01T20:00:00Z') }]);
    const people = [availablePerson, personWithTimeOff];

    const result = getPeopleAvailableForShift({
      schedule,
      people,
      shiftId: 'shift1',
      timezone: "America/Denver"
    });

    expect(result).toEqual([availablePerson]);
  });

  it('should handle multiple availability ranges', () => {
    const shift = {
      id: 'shift1',
      range: { start: '14:00', end: '18:00' },
    } as Shift
    const schedule = createMockSchedule(shift);
    const personWithMultipleRanges = createMockPerson('person1', [
      {
        effectiveAt: '2024-01-01',
        ranges: [
          { dayOfWeek: 1, start: '08:00', end: '12:00' },
          { dayOfWeek: 1, start: '13:00', end: '19:00' },
        ],
      },
    ] as ApprovedPersonAvailability[], []);
    const people = [personWithMultipleRanges];

    const result = getPeopleAvailableForShift({
      schedule,
      people,
      shiftId: 'shift1',
      timezone: "America/Denver"
    });

    expect(result).toEqual([personWithMultipleRanges]);
  });

  it('should handle availability changes over time', () => {
    const shift = {
      id: 'shift1',
      range: { start: '09:00', end: '17:00' },
    } as Shift
    const schedule = createMockSchedule(shift);
    const personWithChangingAvailability = createMockPerson('person1', [
      {
        effectiveAt: '2023-12-31',
        ranges: [{ dayOfWeek: 1, start: '08:00', end: '12:00' }],
      },
      {
        effectiveAt: '2024-01-01',
        ranges: [{ dayOfWeek: 1, start: '08:00', end: '18:00' }],
      },
    ] as ApprovedPersonAvailability[], []);
    const people = [personWithChangingAvailability];

    const result = getPeopleAvailableForShift({
      schedule,
      people,
      shiftId: 'shift1',
      timezone: "America/Denver"
    });

    expect(result).toEqual([personWithChangingAvailability]);
  });

  it('should handle edge cases for availability and shift times', () => {
    const shift = {
      id: 'shift1',
      range: { start: '00:00', end: '23:59' },
    } as Shift
    const schedule = createMockSchedule(shift);
    const personAvailableAllDay = createMockPerson('person1', [
      {
        effectiveAt: '2024-01-01',
        ranges: [{ dayOfWeek: 1, start: '00:00', end: '23:59' }],
      },
    ] as ApprovedPersonAvailability[], []);
    const personAvailablePartDay = createMockPerson('person2', [
      {
        effectiveAt: '2024-01-01',
        ranges: [{ dayOfWeek: 1, start: '00:00', end: '12:00' }],
      },
    ] as ApprovedPersonAvailability[], []);
    const people = [personAvailableAllDay, personAvailablePartDay];

    const result = getPeopleAvailableForShift({
      schedule,
      people,
      shiftId: 'shift1',
      timezone: "America/Denver"
    });

    expect(result).toEqual([personAvailableAllDay]);
  });
});
