import {useNavigate} from "@tanstack/react-router";

export const useGoTo = () => {
  const navigate = useNavigate();

  return {
    teamDirectory: (businessId: string, storeId: string) => {
      navigate({
        to: `/${businessId}/${storeId}/team/directory`,
      });
    },
    viewPerson: (businessId: string, storeId: string, personId: string) => {
      navigate({
        to: `/${businessId}/${storeId}/team/directory/${personId}/about/personal`,
      });
    },
    viewPersonNotes: (businessId: string, storeId: string, personId: string) => {
      navigate({
        to: `/${businessId}/${storeId}/team/directory/${personId}/notes`,
      });
    },
    viewCorrectiveAction: (businessId: string, storeId: string, personId: string, correctiveActionId: string) => {
      navigate({
        to: `/${businessId}/${storeId}/team/directory/${personId}/notes/correctiveAction/${correctiveActionId}/view`,
      });
    },
    viewPersonNote: (businessId: string, storeId: string, personId: string, noteId: string) => {
      navigate({
        to: `/${businessId}/${storeId}/team/directory/${personId}/notes/note/${noteId}`,
      });
    },
    editPersonNote: (businessId: string, storeId: string, personId: string, noteId?: string, type?: string) => {
      navigate({
        to: `/${businessId}/${storeId}/team/directory/${personId}/notes/note/edit`,
        search: {
          noteId,
          type,
        },
      });
    },
    editActionableItem: (businessId: string, storeId: string, personId: string, correctiveActionId?: string) => {
      navigate({
        to: `/${businessId}/${storeId}/team/directory/${personId}/notes/correctiveAction/edit`,
        search: {
          noteId: correctiveActionId,
        },
      });
    },
    formalizeCorrectiveAction: (businessId: string, storeId: string, personId: string, correctiveActionId: string) => {
      navigate({
        to: `/${businessId}/${storeId}/team/directory/${personId}/notes/correctiveAction/${correctiveActionId}/formalize`,
      });
    },
    createCoachingMomentFromActionableItem: (businessId: string, storeId: string, personId: string, correctiveActionId: string) => {
      navigate({
        to: `/${businessId}/${storeId}/team/directory/${personId}/notes/note/cmFromAi/${correctiveActionId}`,
      });
    },
    viewJobTitles: (businessId: string, storeId: string) => {
      navigate({
        to: `/${businessId}/${storeId}/store/settings/job-titles`,
      });
    },
    editJobTitle: (businessId: string, storeId: string, jobId: string) => {
      navigate({
        to: `/${businessId}/${storeId}/store/settings/job-titles/${jobId}/edit`,
      });
    },
    viewAnnouncement: (businessId: string, storeId: string, announcementId: string) => {
      navigate({
        to: `/${businessId}/${storeId}/store/announcements/view`,
        search: {
          announcementId,
        },
      });
    },
    viewAnnouncements: (businessId: string, storeId: string) => {
      navigate({
        to: `/${businessId}/${storeId}/store/announcements`,
      });
    },
    editAnnouncement: (businessId: string, storeId: string, announcementId?: string) => {
      navigate({
        to: `/${businessId}/${storeId}/store/announcements/edit`,
        search: {
          announcementId,
        },
      });
    },
    viewStoreFiles: (businessId: string, storeId: string) => {
      navigate({
        to: `/${businessId}/${storeId}/store/files`,
      });
    },
    viewStoreFile: (businessId: string, storeId: string, fileId: string) => {
      navigate({
        to: `/${businessId}/${storeId}/store/files/${fileId}`,
      });
    },
    viewHRDocuments: (businessId: string, storeId: string) => {
      navigate({
        to: `/${businessId}/${storeId}/hr/documents`,
      });
    },
    viewHRDocument: (businessId: string, storeId: string, hrDocumentId: string) => {
      navigate({
        to: `/${businessId}/${storeId}/hr/documents/view`,
        search: {
          hrDocumentId,
        },
      });
    },
    editHRDocument: (businessId: string, storeId: string, hrDocumentId?: string) => {
      navigate({
        to: `/${businessId}/${storeId}/hr/documents/edit`,
        search: {
          hrDocumentId,
        },
      });
    },
  }
}
