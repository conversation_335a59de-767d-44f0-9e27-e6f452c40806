import {Link, Outlet} from "@tanstack/react-router";
import React from "react";
import {Route} from "@/src/routes/_signedin/$businessId/$storeId/_nav.tsx";

export const TeamMemberDirectoryScreen = () => {
  return (
          <div className="w-full min-h-screen  bg-gray-50 p-6 flex flex-col gap-6">
            <div className="rounded-xl border border-gray-200 bg-white p-4 w-full flex flex-row justify-start gap-4">
              <Link from={Route.fullPath} to={"./team/directory"} activeOptions={{exact: false}}
                    activeProps={{className: "bg-blue-50 hover:bg-gioaBlue/40 hover:text-white"}}
                    inactiveProps={{className: "hover:bg-gioaBlue/40 hover:text-white"}}
                    className={"rounded-lg px-4 p-2 flex items-center gap-3 justify-between"}>
                          <span className="text-sm whitespace-nowrap overflow-hidden transition-all duration-200">
                            Directory
                          </span>
              </Link>
              <Link from={Route.fullPath} to={"./team/reports"} activeOptions={{exact: false}}
                    activeProps={{className: "bg-blue-50 hover:bg-gioaBlue/40 hover:text-white"}}
                    inactiveProps={{className: "hover:bg-gioaBlue/40 hover:text-white"}}
                    className={"rounded-lg px-4 p-2 flex items-center gap-3 justify-between"}>
                          <span className="text-sm whitespace-nowrap overflow-hidden transition-all duration-200">
                            Reports
                          </span>
              </Link>
            </div>
            <div className="rounded-xl border border-gray-200 bg-white p-4 w-full flex flex-row justify-start gap-4">
              <span>Testing</span>
              <Outlet/>
            </div>
          </div>
  )
}
