import {createFileRoute, redirect} from '@tanstack/react-router'
import {queryClient} from "@/src/api.ts";
import {LoadingPage} from "@/src/components/LoadingPage.tsx";
import * as Sentry from "@sentry/react";

export const Route = createFileRoute('/_signedin')({
  // component: LayoutComponent,
  pendingComponent: LoadingPage,
  beforeLoad: async ({context, location}) => {
    const validate = await context.clientUtils.auth.validateSession.ensureData();
    // If the user is logged out, redirect them to the login page
    if (!validate.valid) {
      queryClient.clear();
      Sentry.setUser(null);
      throw redirect({
        to: '/',
        search: {
          // Use the current location to power a redirect after login
          // (Do not use `router.state.resolvedLocation` as it can
          // potentially lag behind the actual current location)
          redirect: location.pathname,
        },
      })
    }
  },
})

