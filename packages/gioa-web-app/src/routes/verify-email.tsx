import {createFileRoute, redirect, useNavigate} from '@tanstack/react-router'
import {trim} from "lodash";
import {api} from "@/src/api.ts";
import {ErrorAlert} from "@/src/components/ErrorAlert.tsx";
import {Button} from '../components/ui/button';
import {useForm} from "@tanstack/react-form";
import React from "react";
import {InputOTP, InputOTPGroup, InputOTPSeparator, InputOTPSlot} from "@/src/components/ui/input-otp.tsx";
import {REGEXP_ONLY_DIGITS} from "input-otp";
import {toast} from "sonner";

export const Route = createFileRoute('/verify-email')({
  component: VerifyEmailComponent,
  beforeLoad: async ({context, location}) => {
    const validate = await context.clientUtils.auth.validateSession.ensureData();

    // If the user is logged in with validated email, redirect them to the dashboard
    if (validate.valid) {
      throw redirect({
        replace: true,
        to: '/admin/dashboard',
      });
    }
  }
})

function VerifyEmailComponent() {
  const util = api.useUtils();
  const form = useForm({
    defaultValues: {
      code: ""
    },
    onSubmit: async ({value}) => {
      verifyEmailMutation.mutate({
        code: trim(value.code)
      }, {
        onSuccess: async (data) => {
          await util.auth.validateSession.reset();
          if (data.success) {
            toast.success("Email verified. Welcome to Nation!", {
              position: "top-center",
              dismissible: true
            });
            navigate({
              replace: true,
              to: "/admin/dashboard"
            })
          }
        }
      });
    }
  })

  const navigate = useNavigate();
  const verifyEmailMutation = api.auth.verifyEmail.useMutation();


  const resendCodeMutation = api.auth.resendVerificationCode.useMutation();
  const resendCode = () => {
    resendCodeMutation.mutate(undefined, {
      onSuccess: () => {
        toast.success("Verification code resent.", {
          position: "top-center",
          dismissible: true
        });
      }
    });
  }

  return (
    <div className="mx-auto flex max-w-md flex-col items-center justify-center space-y-6 py-12">
      <div className="space-y-2 text-center">
        <h1 className="text-3xl font-bold">Verify Your Email</h1>
        <p className="text-gray-500 dark:text-gray-400">
          Enter the 8-digit verification code sent to your email address.
        </p>
      </div>
      {verifyEmailMutation.isError && <ErrorAlert error={verifyEmailMutation.error}/>}
      {resendCodeMutation.isError && <ErrorAlert error={resendCodeMutation.error}/>}
      <form onSubmit={e => {
        e.preventDefault();
        e.stopPropagation();
        form.handleSubmit();
      }}>
        <div className="w-full space-y-4">
          <form.Field name={"code"}
                      children={field =>
                        <InputOTP maxLength={8}
                                  pattern={REGEXP_ONLY_DIGITS}
                                  name={field.name}
                                  value={field.state.value}
                                  onBlur={field.handleBlur}
                                  onChange={(value) => field.handleChange(value)}
                                  id={field.name}>
                          <InputOTPGroup>
                            <InputOTPSlot index={0}/>
                            <InputOTPSlot index={1}/>
                            <InputOTPSlot index={2}/>
                            <InputOTPSlot index={3}/>
                          </InputOTPGroup>
                          <InputOTPSeparator/>
                          <InputOTPGroup>
                            <InputOTPSlot index={4}/>
                            <InputOTPSlot index={5}/>
                            <InputOTPSlot index={6}/>
                            <InputOTPSlot index={7}/>
                          </InputOTPGroup>
                        </InputOTP>
                      }/>
          <Button type={"submit"} className={"w-full"} isLoading={verifyEmailMutation.isPending}>
            Verify
          </Button>
          <div className="text-center text-sm text-gray-500 dark:text-gray-400">
            Didn't receive the code?
            <Button variant={"link"} type={"button"} onClick={resendCode} isLoading={resendCodeMutation.isPending}>
              Resend code
            </Button>
          </div>
        </div>
      </form>
    </div>);
}
