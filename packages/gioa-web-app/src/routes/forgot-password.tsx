import {createFileRoute, useNavigate} from '@tanstack/react-router'
import {Label} from "@/src/components/ui/label"
import {Input} from "@/src/components/ui/input"
import {Button} from "@/src/components/ui/button"
import {api} from "@/src/api.ts";
import {useForm} from "@tanstack/react-form";
import {emailSchema} from "@gioa/api/src/schemas";
import {isEmpty} from "lodash";
import {FieldInfo} from "@/src/components/form/FieldInfo.tsx";
import React from "react";
import {zodValidator} from '@tanstack/zod-form-adapter'

export const Route = createFileRoute('/forgot-password')({
  component: Component,
})

export default function Component() {
  const navigate = useNavigate();
  const sendPasswordResetMutation = api.auth.sendPasswordReset.useMutation();
  const form = useForm({
    defaultValues: {
      email: ''
    },
    onSubmit: async ({value}) => {
      sendPasswordResetMutation.mutate({
        email: value.email
      }, {
        onSuccess: (data) => {
          navigate({
            to: '/forgot-password-code'
          })
        }
      })
    },
    validatorAdapter: zodValidator(),
  })

  return (
    <div className="mx-auto max-w-md space-y-6 py-12">
      <div className="space-y-2 text-center">
        <h1 className="text-3xl font-bold">Forgot Password</h1>
        <p className="text-gray-500 dark:text-gray-400">
          Enter your email address and we'll send you a code to reset your password.
        </p>
      </div>
      <form className="space-y-4" onSubmit={e => {
        e.preventDefault()
        e.stopPropagation()
        form.handleSubmit()
      }}>
        <div className="space-y-2">
          <form.Field name={"email"}
                      validators={{
                        onSubmit: emailSchema
                      }}
                      children={(field) => {
                        return <>
                          <Label htmlFor={field.name}>Email</Label>
                          <Input id={field.name} hasError={!isEmpty(field.state.meta.errors)}
                                 name={field.name}
                                 value={field.state.value}
                                 onBlur={field.handleBlur}
                                 onChange={(e) => field.handleChange(e.target.value)}
                                 type="email"
                                 placeholder="<EMAIL>"/>
                          <FieldInfo field={field}/>
                        </>;
                      }}/>
        </div>
        <Button className="w-full" type="submit">
          Reset Password
        </Button>
      </form>
    </div>
  )
}
