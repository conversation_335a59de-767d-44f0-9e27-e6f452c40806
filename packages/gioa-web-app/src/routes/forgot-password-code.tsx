import {create<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, useNavigate} from '@tanstack/react-router'
import {useForm} from "@tanstack/react-form";
import {isEmpty, trim} from "lodash";
import {toast} from "sonner";
import {api} from "@/src/api.ts";
import {ErrorAlert} from "@/src/components/ErrorAlert.tsx";
import {InputOTP, InputOTPGroup, InputOTPSeparator, InputOTPSlot} from "@/src/components/ui/input-otp.tsx";
import {REGEXP_ONLY_DIGITS} from "input-otp";
import {Button} from "@/src/components/ui/button.tsx";
import React from "react";
import {Label} from "@/src/components/ui/label.tsx";
import {Input} from "@/src/components/ui/input.tsx";
import {FieldInfo} from "@/src/components/form/FieldInfo.tsx";
import {zodValidator} from '@tanstack/zod-form-adapter'
import {passwordSchema} from "@gioa/api/src/schemas.ts";
import {FormControl} from "@/src/components/form/FormControl.tsx";


export const Route = createFileRoute('/forgot-password-code')({
  component: ForgotPassword
})

function ForgotPassword() {
  const resetPasswordMutation = api.auth.resetPassword.useMutation();
  const navigate = useNavigate();
  const form = useForm({
    defaultValues: {
      code: "",
      password: "",
      passwordConfirm: ""
    },
    onSubmit: async ({value}) => {
      resetPasswordMutation.mutate({
        password: value.password,
        code: value.code
      }, {
        onSuccess: (data) => {
          toast("Password reset successfully. Please log in with your new password.", {
            position: "top-center",
            dismissible: true
          })

          // Go to login screen
          navigate({
            replace: true,
            to: '/'
          })
        }
      });
    },
    validatorAdapter: zodValidator()
  });

  return (<div className="mx-auto flex max-w-md flex-col items-center justify-center space-y-6 py-12">
    <div className="space-y-2 text-center">
      <h1 className="text-3xl font-bold">Reset Password</h1>
      <p className="text-gray-500 dark:text-gray-400">
        Enter the code we sent you and your new password.
      </p>
    </div>
    {resetPasswordMutation.isError && <ErrorAlert error={resetPasswordMutation.error}/>}
    <form onSubmit={e => {
      e.preventDefault();
      e.stopPropagation();
      form.handleSubmit();
    }}>
      <div className="w-full space-y-4">
        <form.Field name={"code"}
                    children={field =>
                      <InputOTP maxLength={8}
                                pattern={REGEXP_ONLY_DIGITS}
                                name={field.name}
                                value={field.state.value}
                                onBlur={field.handleBlur}
                                onChange={(value) => field.handleChange(value)}
                                id={field.name}>
                        <InputOTPGroup>
                          <InputOTPSlot index={0}/>
                          <InputOTPSlot index={1}/>
                          <InputOTPSlot index={2}/>
                          <InputOTPSlot index={3}/>
                        </InputOTPGroup>
                        <InputOTPSeparator/>
                        <InputOTPGroup>
                          <InputOTPSlot index={4}/>
                          <InputOTPSlot index={5}/>
                          <InputOTPSlot index={6}/>
                          <InputOTPSlot index={7}/>
                        </InputOTPGroup>
                      </InputOTP>
                    }/>

        <form.Field name={"password"}
                    validators={{
                      onSubmit: passwordSchema
                    }}
                    children={(field) => <FormControl>
                      <Label htmlFor={field.name}>New Password</Label>
                      <Input id={field.name} hasError={!isEmpty(field.state.meta.errors)}
                             name={field.name}
                             value={field.state.value}
                             onBlur={field.handleBlur}
                             onChange={(e) => field.handleChange(e.target.value)}
                             type="password"/>
                      <FieldInfo field={field}/>
                    </FormControl>}/>

        <form.Field name={"passwordConfirm"}
                    validators={{
                      onChangeListenTo: ['password'],
                      onSubmit: ({ value, fieldApi }) => {
                        if (value !== fieldApi.form.getFieldValue('password')) {
                          return 'Passwords do not match'
                        }
                        return undefined
                      },
                    }}
                    children={(field) => <FormControl>
                      <Label htmlFor={field.name}>Confirm Password</Label>
                      <Input id={field.name} hasError={!isEmpty(field.state.meta.errors)}
                             name={field.name}
                             value={field.state.value}
                             onBlur={field.handleBlur}
                             onChange={(e) => field.handleChange(e.target.value)}
                             type="password"/>
                      <FieldInfo field={field}/>
                    </FormControl>}/>
        <Button type={"submit"} className={"w-full"} isLoading={resetPasswordMutation.isPending}>
          Reset Password
        </Button>
      </div>
    </form>
  </div>)
}
