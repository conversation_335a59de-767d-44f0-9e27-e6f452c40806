import {createFileRoute, SearchSchemaInput} from '@tanstack/react-router'
import {api} from "@/src/api.ts";
import {PDFMatrixSchedule} from "@/src/components/PdfWeeklyTeamMemberSchedule.tsx";
import {
  PrintScheduleSearchSchema,
  printSchedulesParams
} from "@/src/routes/_signedin/$businessId/$storeId/print/$scheduleId/daily.tsx";

export const Route = createFileRoute('/_signedin/$businessId/$storeId/print/$scheduleId/team')({
  component: Component,
  validateSearch: (search: unknown & SearchSchemaInput): PrintScheduleSearchSchema | undefined => {
    if (printSchedulesParams.safeParse(search).success) {
      return search as any;
    }
  },
})

function Component() {
  const {scheduleId, storeId} = Route.useParams();

  const [[store, schedule, {people: peopleRaw}]] = api.useSuspenseQueries(t => {
    return [
      t.user.getStoreAdmin({storeId: storeId}),
      t.user.getSchedule({id: scheduleId}, {
        gcTime: 0,
        staleTime: 0
      }),
      t.user.getAllSchedulePeopleAtStore({storeId: storeId}, {
        staleTime: 1000 * 60 * 60
      }),
    ];
  })

  return (
    <div className="w-full h-screen">
      <PDFMatrixSchedule storeTitle={store.title} schedule={schedule} people={peopleRaw}/>
    </div>
  )
}
