import {createFileRoute, SearchSchemaInput} from '@tanstack/react-router'
import {api} from "@/src/api.ts";
import {PDFSchedule} from "@/src/components/PdfWeeklySchedule.tsx";
import {z} from "zod";

export const printSchedulesParams = z.object({
  scheduleId: z.string(),
  storeId: z.string(),
})

export type PrintScheduleSearchSchema = z.infer<typeof printSchedulesParams>;

export const Route = createFileRoute('/_signedin/$businessId/$storeId/print/$scheduleId/daily')({
  component: Component,
  validateSearch: (search: unknown & SearchSchemaInput): PrintScheduleSearchSchema | undefined => {
    if (printSchedulesParams.safeParse(search).success) {
      return search as any;
    }
  },
})

function Component() {
  const {scheduleId, storeId} = Route.useParams();

  const [[store, schedule, {people: peopleRaw}]] = api.useSuspenseQueries(t => {
    return [
      t.user.getStoreAdmin({storeId: storeId}),
      t.user.getSchedule({id: scheduleId}, {
        gcTime: 0,
        staleTime: 0
      }),
      t.user.getAllSchedulePeopleAtStore({storeId: storeId}, {
        staleTime: 1000 * 60 * 60
      }),
    ];
  })

  return (
    <div className="w-full h-screen">
      <PDFSchedule
        storeTitle={store.title}
        schedule={schedule}
        people={peopleRaw}
      />
    </div>
  )
}
