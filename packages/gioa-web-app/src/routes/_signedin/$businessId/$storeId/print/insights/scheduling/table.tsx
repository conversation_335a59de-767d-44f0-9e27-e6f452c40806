import {createFileRoute, SearchSchemaInput} from '@tanstack/react-router'
import {api} from "@/src/api.ts";
import {PDFSchedulingInsightsTable} from "@/src/components/PdfSchedulingInsightsTable.tsx";
import {z} from "zod";
import {DateTime} from "luxon";
import {getDateRangeForTimeFrame} from "@/src/utils/dateRange.util.ts";
import {TimeFrameFilter} from "@/src/components/CustomDateFilter.tsx";
import {filter, map, forEach} from "lodash";
import {useMemo, Suspense} from "react";
import {calculateAvailableHours, filterPersonByName} from "../../../../../../../../../api/src/insights/scheduling.ts";
import {IsoCompleteDate} from "../../../../../../../../../api/src/timeSchemas.ts";

const schedulingInsightsDateFilterSchema = z.object({
  week: z.number().int().min(1).max(53).optional(),
  year: z.number().int().min(2000).max(9999).optional(),
  day: z.number().int().min(1).max(7).optional(),
  month: z.number().int().min(1).max(12).optional(),
  timeFrame: z.enum(['day', 'week', 'month', 'custom']).optional(),
  startDate: z.string().optional(), // ISO date string for custom range
  endDate: z.string().optional(),   // ISO date string for custom range
  search: z.string().optional(),    // Team member search
  // Filter parameters
  includeArchived: z.boolean().optional(),
  // Column filtering parameters
  columnFilters: z.string().optional().transform((val) => {
    if (!val) return [];
    try {
      return JSON.parse(val);
    } catch {
      return [];
    }
  }),
  // Export options for conditional column rendering
  exportOptions: z.string().optional().transform((val) => {
    if (!val) return {
      availability: true,
      scheduled: true,
      adminNonOps: true,
      shifts: true,
      shiftSwaps: true,
      shiftOffers: true,
      timeOff: true,
      itemizeShifts: false,
    };
    try {
      return JSON.parse(val);
    } catch {
      return {
        availability: true,
        scheduled: true,
        adminNonOps: true,
        shifts: true,
        shiftSwaps: true,
        shiftOffers: true,
        timeOff: true,
        itemizeShifts: false,
      };
    }
  }),
}).optional();

type InsightsSearch = z.infer<typeof schedulingInsightsDateFilterSchema>;

export const Route = createFileRoute(
  '/_signedin/$businessId/$storeId/print/insights/scheduling/table',
)({
  component: () => (
    <Suspense fallback={<div>Loading...</div>}>
      <RouteComponent/>
    </Suspense>
  ),
  validateSearch: (search: unknown & SearchSchemaInput): InsightsSearch | undefined => {
    if (schedulingInsightsDateFilterSchema.safeParse(search).success) {
      return search as never
    }
  },
})

function RouteComponent() {
  const {storeId} = Route.useParams();
  const searchParams = Route.useSearch();

  const [[store, {people}]] = api.useSuspenseQueries(t => [
    t.user.getStoreAdmin({storeId: storeId!}),
    t.user.getAllSchedulePeopleAtStore({
      storeId: storeId,
      includeArchived: true,
      includeSuspended: true
    }, {
      staleTime: 1000 * 60 * 60
    })
  ]);

  const schedulePeople = useMemo(() => {
    if (searchParams?.includeArchived) {
      return people;
    }
    return filter(people, p => p.status === "Active");
  }, [people, searchParams]);

  const timezone = store.timezone ?? "America/New_York";

  const today = DateTime.now().setZone(timezone);

  // Date filter logic with default to weekly view for current week
  const timeFrame: TimeFrameFilter = (searchParams?.timeFrame as TimeFrameFilter) ?? 'week';
  const isoDateSearch: IsoCompleteDate = searchParams?.week && searchParams?.year ? {
    week: searchParams.week,
    year: searchParams.year,
    day: searchParams.day,
    month: searchParams.month
  } : {week: today.weekNumber, year: today.weekYear};

  // Custom date range logic
  let customStartDate: IsoCompleteDate | null = null;
  let customEndDate: IsoCompleteDate | null = null;

  if (searchParams?.startDate) {
    const [year, month, day] = map(searchParams.startDate.split('-'), Number);
    const dt = DateTime.fromObject({year, month, day}, {zone: timezone});
    customStartDate = {year: dt.weekYear, month: month, day: dt.weekday, week: dt.weekNumber};
  }

  if (searchParams?.endDate) {
    const [year, month, day] = map(searchParams.endDate.split('-'), Number);
    const dt = DateTime.fromObject({year, month, day}, {zone: timezone});
    customEndDate = {year: dt.weekYear, month: month, day: dt.weekday, week: dt.weekNumber};
  }

  const dateRange = getDateRangeForTimeFrame(isoDateSearch, timeFrame, timezone, customStartDate, customEndDate);

  const {data} = api.user.getSchedulingInsightsData.useQuery({
    storeId: storeId!,
    personFilter: searchParams?.search?.trim() || '',
    range: dateRange,
    includeArchived: searchParams?.includeArchived ?? false,
  });

  const {peopleData, shiftStats} = useMemo(() => {
    const filteredPeople = filter(schedulePeople, person => filterPersonByName(person, searchParams?.search?.trim() || ''));

    const peopleData = map(filteredPeople, person => {
      const availability = person.availability;
      const hours = calculateAvailableHours(availability, dateRange, timezone);

      const stats = data?.insights ?? {
        personShiftStats: {
          [person.id]: {
            hours: 0,
            shifts: 0,
            adminNonOps: 0,
          }
        },
        personSwapStats: {
          [person.id]: {
            offered: 0,
            received: 0,
          }
        },
        personOfferStats: {
          [person.id]: {
            offered: 0,
            received: 0,
          }
        },
        personTimeOffHours: {
          [person.id]: 0
        },
        totalShifts: 0,
        totalScheduledHours: 0,
      }
      const shiftStats = stats.personShiftStats[person.id] ?? {
        hours: 0,
        shifts: 0,
        adminNonOps: 0,
      };
      const swapStats = stats.personSwapStats[person.id] ?? {
        offered: 0,
        received: 0,
      };
      const offerStats = stats.personOfferStats[person.id] ?? {
        offered: 0,
        received: 0,
      };
      const timeOffHours = stats.personTimeOffHours[person.id] ?? 0;

      return {
        person: person,
        totalAvailability: hours,
        shiftStats: {
          hours: shiftStats.hours,
          shifts: shiftStats.shifts,
          adminNonOps: shiftStats.adminNonOps,
        },
        swapStats: {
          offered: swapStats.offered,
          received: swapStats.received,
        },
        offerStats: {
          offered: offerStats.offered,
          received: offerStats.received,
        },
        timeOffHours,
      };
    });
    return {
      peopleData,
      shiftStats: [
        {title: "Total Scheduled Hours", count: data?.insights.totalScheduledHours ?? 0},
        {title: "Total Shifts", count: data?.insights.totalShifts ?? 0},
        {title: "Total Swaps", count: data?.insights.totalSwaps ?? 0},
        {title: "Total Offers", count: data?.insights.totalOffers ?? 0},
        {title: "Total Time Off Requests", count: data?.insights.totalTimeOffRequests ?? 0},
      ]
    };
  }, [data, schedulePeople, dateRange]);

  // Apply additional column filters from URL parameters if any
  const finalPeopleData = useMemo(() => {
    let result = peopleData;

    // Apply column filters if they exist
    const columnFilters = searchParams?.columnFilters ?? [];

    forEach(columnFilters, columnFilter => {
      if (columnFilter.id === 'fullName' && columnFilter.value) {
        const searchValue = columnFilter.value.toLowerCase();
        result = filter(result, row =>
          `${row.person.firstName} ${row.person.lastName}`.toLowerCase().includes(searchValue)
        );
      }
      // Add other column filters as needed
    });

    return result;
  }, [peopleData, searchParams?.columnFilters]);

  return (
    <div className="w-full h-screen">
      <PDFSchedulingInsightsTable
        storeTitle={store.title}
        peopleData={finalPeopleData}
        shiftStats={shiftStats}
        dateRange={dateRange}
        timeFrame={timeFrame}
        timezone={timezone}
        exportOptions={searchParams?.exportOptions}
      />
    </div>
  )
}
