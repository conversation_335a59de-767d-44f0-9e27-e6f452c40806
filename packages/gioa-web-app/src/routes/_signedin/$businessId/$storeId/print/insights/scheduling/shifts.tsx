import {createFileRoute, SearchSchemaInput} from '@tanstack/react-router'
import {api} from "@/src/api.ts";
import {PDFSchedulingInsightsShifts} from "@/src/components/PdfSchedulingInsightsShifts.tsx";
import {z} from "zod";
import {DateTime} from "luxon";
import {getDateRangeForTimeFrame} from "@/src/utils/dateRange.util.ts";
import {TimeFrameFilter} from "@/src/components/CustomDateFilter.tsx";
import {filter, find, forEach, map, sumBy} from "lodash";
import {Suspense, useMemo} from "react";
import {calculateAvailableHours, filterPersonByName} from "../../../../../../../../../api/src/insights/scheduling.ts";
import {IsoCompleteDate} from "../../../../../../../../../api/src/timeSchemas.ts";
import {getDateRangeDurationHours} from "../../../../../../../../../api/src/date.util.ts";
import {Spinner} from "@/src/components/Spinner.tsx";
import {Text} from "@/src/components/Text.tsx";

const schedulingInsightsDateFilterSchema = z.object({
  week: z.number().int().min(1).max(53).optional(),
  year: z.number().int().min(2000).max(9999).optional(),
  day: z.number().int().min(1).max(7).optional(),
  month: z.number().int().min(1).max(12).optional(),
  timeFrame: z.enum(['day', 'week', 'month', 'custom']).optional(),
  startDate: z.string().optional(), // ISO date string for custom range
  endDate: z.string().optional(),   // ISO date string for custom range
  search: z.string().optional(),    // Team member search
  // Filter parameters
  includeArchived: z.boolean().optional(),
  // Column filtering parameters
  columnFilters: z.string().optional().transform((val) => {
    if (!val) return [];
    try {
      return JSON.parse(val);
    } catch {
      return [];
    }
  }),
  // Export options for conditional column rendering
  exportOptions: z.string().optional().transform((val) => {
    if (!val) return {
      availability: true,
      scheduled: true,
      adminNonOps: true,
      shifts: true,
      shiftSwaps: true,
      shiftOffers: true,
      timeOff: true,
      itemizeShifts: true,
    };
    try {
      return JSON.parse(val);
    } catch {
      return {
        availability: true,
        scheduled: true,
        adminNonOps: true,
        shifts: true,
        shiftSwaps: true,
        shiftOffers: true,
        timeOff: true,
        itemizeShifts: true,
      };
    }
  }),
}).optional();

type InsightsSearch = z.infer<typeof schedulingInsightsDateFilterSchema>;

export const Route = createFileRoute(
        '/_signedin/$businessId/$storeId/print/insights/scheduling/shifts',
)({
  component: () => (
          <Suspense fallback={<div>Loading...</div>}>
            <RouteComponent/>
          </Suspense>
  ),
  validateSearch: (search: unknown & SearchSchemaInput): InsightsSearch | undefined => {
    if (schedulingInsightsDateFilterSchema.safeParse(search).success) {
      return search as never
    }
  },
})

function RouteComponent() {
  const {storeId} = Route.useParams();
  const searchParams = Route.useSearch();

  const [[store, {people}]] = api.useSuspenseQueries(t => [
    t.user.getStoreAdmin({storeId: storeId!}),
    t.user.getAllSchedulePeopleAtStore({
      storeId: storeId,
      includeArchived: true,
      includeSuspended: true
    }, {
      staleTime: 1000 * 60 * 60
    })
  ]);

  const schedulePeople = useMemo(() => {
    if (searchParams?.includeArchived) {
      return people;
    }
    return filter(people, p => p.status === "Active");
  }, [people, searchParams]);

  const timezone = store.timezone ?? "America/New_York";

  const today = DateTime.now().setZone(timezone);

  // Date filter logic with default to weekly view for current week
  const timeFrame: TimeFrameFilter = (searchParams?.timeFrame as TimeFrameFilter) ?? 'week';
  const isoDateSearch: IsoCompleteDate = searchParams?.week && searchParams?.year ? {
    week: searchParams.week,
    year: searchParams.year,
    day: searchParams.day,
    month: searchParams.month
  } : {week: today.weekNumber, year: today.weekYear};

  // Custom date range logic
  let customStartDate: IsoCompleteDate | null = null;
  let customEndDate: IsoCompleteDate | null = null;

  if (searchParams?.startDate) {
    const [year, month, day] = map(searchParams.startDate.split('-'), Number);
    const dt = DateTime.fromObject({year, month, day}, {zone: timezone});
    customStartDate = {year: dt.weekYear, month: month, day: dt.weekday, week: dt.weekNumber};
  }

  if (searchParams?.endDate) {
    const [year, month, day] = map(searchParams.endDate.split('-'), Number);
    const dt = DateTime.fromObject({year, month, day}, {zone: timezone});
    customEndDate = {year: dt.weekYear, month: month, day: dt.weekday, week: dt.weekNumber};
  }

  const dateRange = getDateRangeForTimeFrame(isoDateSearch, timeFrame, timezone, customStartDate, customEndDate);

  const {data} = api.user.getSchedulingInsightsShiftData.useQuery({
    storeId: storeId!,
    personFilter: searchParams?.search?.trim() || '',
    range: dateRange,
    includeArchived: searchParams?.includeArchived ?? false,
  });

  const {data: currentUser} = api.user.getUserProfile.useQuery();

  const peopleShiftData = useMemo(() => {
    if (!data?.insights) return [];

    const filteredPeople = filter(schedulePeople, person => filterPersonByName(person, searchParams?.search?.trim() || ''));

    return map(filteredPeople, person => {
      const availability = person.availability;
      const totalAvailabilityHours = calculateAvailableHours(availability, dateRange, timezone);

      // Get shifts for this person
      const personShifts = filter(data.insights.scheduledShifts, s => s.personId === person.id);

      // Calculate summary stats
      const totalWorkedHours = sumBy(personShifts, s => getDateRangeDurationHours(s.range));
      const totalAdminNonOpsHours = sumBy(personShifts, s => s.adminNonOpsHours);
      const totalShifts = personShifts.length;

      // Get swaps, offers, and time off for this person
      const swapStats = {
        offered: filter(data.insights.shiftSwaps, s => s.personId === person.id).length,
        received: filter(data.insights.shiftSwaps, s => s.offereePersonId === person.id).length,
      };

      const offerStats = {
        offered: filter(data.insights.shiftOffers, o => o.personId === person.id).length,
        received: filter(data.insights.shiftOffers, o => o.offereePersonId === person.id).length,
      };

      const timeOffRequests = filter(data.insights.timeOff, t => t.personId === person.id).length;

      // Process time off entries
      const personTimeOffEntries = filter(data.insights.timeOff, t => t.personId === person.id);
      const timeOffEntries = map(personTimeOffEntries, entry => ({
        date: DateTime.fromJSDate(entry.range.start).setZone(timezone),
        range: entry.range,
        durationHours: getDateRangeDurationHours(entry.range),
        approved: entry.status === "approved",
      })).sort((a, b) => a.date.toMillis() - b.date.toMillis());

      // Process offered shifts
      const offeredShifts = [];

      // Add "Offered" shifts (where this person is the offeror)
      const offeredByPerson = filter(data.insights.shiftOffers, o => o.personId === person.id);
      for (const offer of offeredByPerson) {

        const teamMember = offer.offereePersonId ?
          find(people, p => p.id === offer.offereePersonId) : null;
        const teamMemberName = teamMember ?
          `${teamMember.firstName} ${teamMember.lastName}` :
          '-';

        offeredShifts.push({
          date: DateTime.fromJSDate(offer.range.start).setZone(timezone),
          type: "Offered" as const,
          range: offer.range,
          durationHours: getDateRangeDurationHours(offer.range),
          approved: offer.status === "Approved",
          teamMemberName,
        });
      }

      // Add "Picked-up" shifts (where this person is the approved acceptor)
      const pickedUpByPerson = filter(data.insights.shiftOffers, o =>
        o.offereePersonId === person.id && o.status === "Approved"
      );
      for (const offer of pickedUpByPerson) {
        const teamMember = find(people, p => p.id === offer.personId);
        const teamMemberName = teamMember ?
          `${teamMember.firstName} ${teamMember.lastName}` :
          'Unknown';

        offeredShifts.push({
          date: DateTime.fromJSDate(offer.range.start).setZone(timezone),
          type: "Picked-up" as const,
          range: offer.range,
          durationHours: getDateRangeDurationHours(offer.range),
          approved: true, // Always true for picked-up shifts
          teamMemberName,
        });
      }

      // Sort offered shifts by date
      offeredShifts.sort((a, b) => a.date.toMillis() - b.date.toMillis());

      // Process shift swaps
      const shiftSwaps = [];

      // Get approved shift swaps for this person (both as offeror and offeree)
      const approvedSwapsForPerson = filter(data.insights.shiftSwaps, s =>
        s.status === "Approved" && (s.personId === person.id || s.offereePersonId === person.id)
      );

      for (const swap of approvedSwapsForPerson) {
        // For approved swaps, we create two records: one for offeror and one for offeree
        if (swap.personId === person.id) {
          // This person is the offeror
          const teamMember = swap.offereePersonId ?
            find(people, p => p.id === swap.offereePersonId) : null;
          const teamMemberName = teamMember ?
            `${teamMember.firstName} ${teamMember.lastName}` :
            '--';

          // Use offeror shift range if available, otherwise offeree shift range
          const range = swap.offerorShift?.range || swap.offereeShift?.range;
          if (range) {
            shiftSwaps.push({
              date: DateTime.fromJSDate(range.start).setZone(timezone),
              type: "Shift Swap" as const,
              range: range,
              durationHours: getDateRangeDurationHours(range),
              approved: true, // Always true for approved swaps
              teamMemberName,
            });
          }
        }

        if (swap.offereePersonId === person.id) {
          // This person is the offeree
          const teamMember = find(people, p => p.id === swap.personId);
          const teamMemberName = teamMember ?
            `${teamMember.firstName} ${teamMember.lastName}` :
            'Unknown';

          // Use offeree shift range if available, otherwise offeror shift range
          const range = swap.offereeShift?.range || swap.offerorShift?.range;
          if (range) {
            shiftSwaps.push({
              date: DateTime.fromJSDate(range.start).setZone(timezone),
              type: "Shift Swap" as const,
              range: range,
              durationHours: getDateRangeDurationHours(range),
              approved: true, // Always true for approved swaps
              teamMemberName,
            });
          }
        }
      }

      // Sort shift swaps by date
      shiftSwaps.sort((a, b) => a.date.toMillis() - b.date.toMillis());

      // Process individual shifts with daily availability
      const shiftsWithDailyAvailability = map(personShifts, shift => {
        const shiftDate = DateTime.fromJSDate(shift.range.start).setZone(timezone);
        const dayOfWeek = shiftDate.weekday; // 1=Monday, 7=Sunday
        const dateStr = shiftDate.toISODate();

        // Calculate availability for this specific day
        let dailyAvailabilityHours = 0;
        if (dateStr) {
          const sortedAvailability = availability.sort((a, b) => a.effectiveAt.localeCompare(b.effectiveAt));
          let effectiveAvailability = null;

          for (const avail of sortedAvailability) {
            if (avail.effectiveAt <= dateStr) {
              effectiveAvailability = avail;
            } else {
              break;
            }
          }

          if (effectiveAvailability) {
            const rangesForDay = filter(effectiveAvailability.ranges, r => r.dayOfWeek === dayOfWeek);
            dailyAvailabilityHours = sumBy(rangesForDay, range => {
              const startTime = DateTime.fromFormat(range.start, 'HH:mm');
              const endTime = DateTime.fromFormat(range.end, 'HH:mm');
              return endTime.diff(startTime, 'hours').hours;
            });
          }
        }

        return {
          date: shiftDate,
          range: shift.range,
          workedHours: getDateRangeDurationHours(shift.range),
          adminNonOpsHours: shift.adminNonOpsHours,
          dailyAvailabilityHours,
        };
      }).sort((a, b) => a.date.toMillis() - b.date.toMillis());

      return {
        person,
        totalAvailabilityHours,
        totalWorkedHours,
        totalAdminNonOpsHours,
        totalShifts,
        swapStats,
        offerStats,
        timeOffRequests,
        shifts: shiftsWithDailyAvailability,
        offeredShifts,
        shiftSwaps,
        timeOff: timeOffEntries,
      };
    });
  }, [data, schedulePeople, dateRange, timezone]);

  // Apply additional column filters from URL parameters if any
  const finalPeopleShiftData = useMemo(() => {
    let result = peopleShiftData;

    // Apply column filters if they exist
    const columnFilters = searchParams?.columnFilters ?? [];

    forEach(columnFilters, columnFilter => {
      if (columnFilter.id === 'fullName' && columnFilter.value) {
        const searchValue = columnFilter.value.toLowerCase();
        result = filter(result, row =>
          `${row.person.firstName} ${row.person.lastName}`.toLowerCase().includes(searchValue)
        );
      }
      // Add other column filters as needed
    });

    return result;
  }, [peopleShiftData, searchParams?.columnFilters]);

  // Check if we have the essential data loaded and processed
  const isDataReady = data?.insights && finalPeopleShiftData.length >= 0 && currentUser;

  // Show loading state while data is being processed
  if (!isDataReady) {
    return (
      <div className="w-full h-screen flex flex-col items-center justify-center bg-gray-50">
        <div className="flex flex-col items-center space-y-4">
          <Spinner size="xl" className="text-blue-600" />
          <Text size="lg" medium>
            Generating PDF...
          </Text>
          <Text size="sm" muted>
            Processing scheduling insights data
          </Text>
        </div>
      </div>
    );
  }

  return (
          <div className="w-full h-screen">
            <PDFSchedulingInsightsShifts
                    storeTitle={store.title}
                    timezone={timezone}
                    peopleShiftData={finalPeopleShiftData}
                    dateRange={dateRange}
                    timeFrame={timeFrame}
                    exportedBy={currentUser ? `${currentUser.person.firstName} ${currentUser.person.lastName}` : 'Unknown User'}
                    exportOptions={searchParams?.exportOptions}
            />
          </div>
  )
}
