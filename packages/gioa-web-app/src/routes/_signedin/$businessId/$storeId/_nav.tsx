import {create<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, Outlet} from '@tanstack/react-router'
import {
  ArrowUpDown,
  Calendar,
  ClipboardList,
  Cog,
  CopyCheck,
  DatabaseIcon,
  GraduationCap,
  Home,
  Menu,
  Timer,
} from "lucide-react";
import {api} from "@/src/api.ts";
import {useSignOut} from "@/src/hooks/useSignOut.tsx";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from "@/src/components/ui/dropdown-menu.tsx";
import {Button} from "@/src/components/ui/button.tsx";
import {Sheet, SheetClose, SheetContent, SheetTrigger} from "@/src/components/ui/sheet"
import {Text} from "@/src/components/Text.tsx";
import {PersonAvatar} from "@/src/components/PersonAvatar.tsx";
import {Badge} from "@/src/components/ui/badge.tsx";
import React, {useCallback, useEffect, useState} from 'react';
import {NavCalendarMonthIcon} from "@/src/assets/nav_icons/NavCalendarMonthIcon.tsx";
import {NavTeamMembersIcon} from "@/src/assets/nav_icons/NavTeamMembersIcon.tsx";
import {SidebarIcon} from "@/src/components/SideBarIcon.tsx";
import {NavHomeWorkIcon} from "@/src/assets/nav_icons/NavHomeWorkIcon.tsx";
import {NavEducation} from "@/src/assets/nav_icons/NavEducation.tsx";
import {hasPermissionPackage} from "../../../../../../api/src/authorization.util.ts";
import {TrialExpirationDialog} from "@/src/components/TrialExpirationDialog.tsx";
import {PaymentSuccessDialog} from "@/src/components/PaymentSuccessDialog.tsx";
import {PastDueDialog} from "@/src/components/PastDueDialog.tsx";
import {NoSubscriptionDialog} from "@/src/components/NoSubscriptionDialog.tsx";
import {useFeatureFlag} from "@/src/hooks/useFeatureFlag.tsx";
import {NavHRTeamIcon} from "@/src/assets/nav_icons/NavHRTeamIcon.tsx";
import {NavComputerMonitorIcon} from "@/src/assets/nav_icons/NavComputerMonitorIcon.tsx";
import {DateTime} from "luxon";
import {useOnboarding} from "@/src/hooks/useOnboarding.tsx";
import {NavSettingsIcon} from "@/src/assets/nav_icons/NavSettingsIcon.tsx";
import {canViewTraining} from "../../../../../../api/src/permissionChecks.ts";

export const Route = createFileRoute('/_signedin/$businessId/$storeId/_nav')({
  component: Dashboard,
})

function calculateDaysRemaining(trialEndDate: Date) {
  const now = new Date();
  return Math.ceil((trialEndDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
}

export function Dashboard() {
  const [isNavExpanded, setIsNavExpanded] = useState(false);
  const setNavIsExpanded = useCallback(() => setIsNavExpanded(true), [setIsNavExpanded]);
  const setNavIsCollapsed = useCallback(() => setIsNavExpanded(false), [setIsNavExpanded]);

  const [features] = api.feature.getFeatures.useSuspenseQuery();
  const isHrDocumentsFeatureEnabled = features["hrDocuments"];
  const isRemindersFeatureEnabled = features["reminders"];
  const isEducationFeatureEnabled = features["education"];
  const isInsightsFeatureEnabled = features["insights"];
  const isTrainingInsightsFeatureEnabled = features["trainingInsights"];

  const [user] = api.user.getUserProfile.useSuspenseQuery(undefined, {
    staleTime: 1000 * 60 * 15 // 15 minutes
  });
  const signOut = useSignOut();
  const navigate = Route.useNavigate();
  const {storeId, businessId} = Route.useParams();
  const searchParams = Route.useSearch() as { paymentSuccess?: boolean } | undefined;

  const [showTrialDialog, setShowTrialDialog] = useState(false);
  const [showPaymentSuccessDialog, setShowPaymentSuccessDialog] = useState(false);
  const [showPastDueDialog, setShowPastDueDialog] = useState(false);
  const [showNoSubscriptionDialog, setShowNoSubscriptionDialog] = useState(false);
  const [store] = api.user.getStoreAdmin.useSuspenseQuery({
    storeId
  }, {
    staleTime: 1000 * 60 * 60 // 1 hour
  });
  const [permissions] = api.user.getStreetPermissions.useSuspenseQuery({storeId: storeId});
  const isForecastingEnabled = Boolean(store.permissions?.isForecastingEnabled);
  const today = DateTime.now().setZone(store.timezone ?? "America/New_York");
  const trialEndDate = store.trialEndsAt ? DateTime.fromJSDate(store.trialEndsAt, {zone: store.timezone ?? "America/New_York"}) : null;
  const daysUntilTrialEnds = trialEndDate ? trialEndDate.diff(today, 'days').days : null;

  const storeIds = user.person.storeIds;
  const isMultipleStores = storeIds.length > 1;
  const currentIndex = storeIds.indexOf(storeId!);
  const nextIndex = (currentIndex + 1) % storeIds.length;
  const nextStoreId = storeIds[nextIndex];
  const timeOffRequests = api.user.getStoreTimeOffRequests.useQuery({
    storeId,
    status: "pending"
  });
  const shiftOffers = api.user.getShiftOffers.useQuery({
    storeId: storeId,
    status: "pending",
    appVersion: "web"
  })
  const availabilityRequests = api.user.getAvailabilityRequests.useQuery({
    storeId: storeId,

    status: "pending"
  });
  const numPending = (timeOffRequests.data?.items.length ?? 0) + (shiftOffers.data?.items.length ?? 0) + (availabilityRequests.data?.items.length ?? 0);
  const showDataLink = isForecastingEnabled;

  const isStoreAdmin = hasPermissionPackage(user, "storeAdmin");
  const hasViewSchedulesPermissions = hasPermissionPackage(user, "viewSchedules");
  const hasCreateCorrectiveActionPermissions = hasPermissionPackage(user, "createCorrectiveActions");
  const hasViewTrainingInsightsPermissions = hasPermissionPackage(user, "viewTraining");
  const isTimePunchFeatureEnabled = useFeatureFlag("timePunch");
  const {isOnboardingFeatureEnabled, steps, stepsIncompleted, percentageCompleted} = useOnboarding({storeId});
  const isOnboardingComplete = percentageCompleted === 100;

  useEffect(() => {
    if (!store.permissions?.isStripeEnabled || !store.permissions?.canManageSubscription) {
      return;
    }

    if (searchParams?.paymentSuccess && (store.subscriptionStatus === 'trialing' || store.subscriptionStatus === 'none')) {
      setShowPaymentSuccessDialog(true);
      navigate({
        search: (prev: any) => {
          const newSearch = {...prev};
          delete newSearch.paymentSuccess;
          return newSearch;
        },
        replace: true
      });
      return;
    }

    if (trialEndDate && !store.hasPaymentMethod && (store.subscriptionStatus === 'trialing' || store.subscriptionStatus === 'none')) {
      const daysRemaining = calculateDaysRemaining(trialEndDate.toJSDate());

      if (daysRemaining <= 14 && daysRemaining > 0) {
        setShowTrialDialog(true);
        return;
      }
    }

    if (store.subscriptionStatus === 'none') {
      setShowNoSubscriptionDialog(true);
      return;
    }

    if (store.subscriptionStatus === 'past_due') {
      setShowPastDueDialog(true);
      return;
    }
  }, [searchParams?.paymentSuccess, showPaymentSuccessDialog, store.permissions, store.trialEndsAt, store.hasPaymentMethod, store.subscriptionStatus, store.gracePeriodEndsAt, navigate]);

  /**
   * To be used when the user changes the store.  Now, since we don't have a "." root directory, we need to check
   * the user's permissions and land them on the first page they have access to.
   *
   * TODO: This will continue to be filled out as more pages are added.
   */
  const getNextStoreLinkTo = () => {
    // has scheduling permissions?
    return "../$storeId/schedules";
  }

  const daysRemaining = store.trialEndsAt ? calculateDaysRemaining(store.trialEndsAt) : 0;

  return (
    <div className="min-h-screen w-full relative">
      <div className={`fixed top-0 left-0 h-full border-r bg-white md:block hidden`}
           style={{
             width: isNavExpanded ? '14rem' : '4rem',
             transition: 'width 300ms ease-in-out',
             zIndex: 10000,
           }}
           onMouseEnter={setNavIsExpanded}
           onMouseLeave={setNavIsCollapsed}>
        <div className="flex h-full max-h-screen flex-col gap-2 bg-muted/40">
          <div className="flex h-14 items-center justify-left border-b lg:h-[60px] p-4">
            <img src={"https://media.api.gioanation.com/images/icon.png?w=200"}
                 alt={"Nation logo"}
                 className={"rounded-md max-w-full max-h-full"}
            />
          </div>
          <div className="flex-1 overflow-y-auto">
            <nav className="flex-1 flex flex-col gap-4 text-sm pt-1 pb-3 px-3 overflow-hidden">
              {(isOnboardingFeatureEnabled && steps.length && !isOnboardingComplete) ? <SidebarIcon icon={() => <NavComputerMonitorIcon/>} to="/$businessId/$storeId/walkthrough"
                           notificationCount={stepsIncompleted.length} label="Onboarding" isNavExpanded={isNavExpanded}/> : null}
              <SidebarIcon icon={NavCalendarMonthIcon} to="./schedules" label="Schedule"
                           isNavExpanded={isNavExpanded} subItems={[
                {label: "Dashboard", href: "./schedules", activeOptions: {exact: true}},
                {label: "Builder", href: "./schedules/builder"},
                {label: "Requests", href: "./schedules/requests", notificationCount: numPending},
                {label: "Reports", href: "./schedules/reports"},
                ...(showDataLink ? [
                  {label: "Data", href: "./schedules/data"}
                ] : []),
                ...(isInsightsFeatureEnabled && permissions?.canViewSchedulingInsights ? [
                  {label: "Insights", href: "./schedules/insights"}
                ] : []),
                {label: "Settings", href: "./schedules/settings"},
              ]}/>
              <SidebarIcon icon={NavTeamMembersIcon} to="./team/directory" label="Team Members"
                           isNavExpanded={isNavExpanded} subItems={[
                {label: "Directory", href: "./team/directory"},
                // {label: "Reports", href: "./team/reports"},
              ]}/>
              {permissions.hasChecklists2 ?
                <SidebarIcon icon={() => <ClipboardList/>} to="/$businessId/$storeId/checklists" label="Checklists"
                  isNavExpanded={isNavExpanded}/> : null}

              {isTimePunchFeatureEnabled && isStoreAdmin && hasViewSchedulesPermissions ? <SidebarIcon icon={() => <Timer/>} to="./timePunch" label="Time Punch"
                isNavExpanded={isNavExpanded}
                subItems={[
                  {label: "Punches", href: "./timePunch/punches"},
                  {label: "Variance", href: "./timePunch/variance"},
                  {label: "Team Totals", href: "./timePunch/teamTotals"}
                                           ]}
              /> : null}
              {isStoreAdmin ? <SidebarIcon icon={NavHomeWorkIcon} to="./store" label="Store"
                                           isNavExpanded={isNavExpanded}
                                           subItems={[
                                             {label: "Files", href: "./store/files"},
                                             {label: "Links", href: "./store/links"},
                                             {label: "Announcements", href: "./store/announcements"},
                                             {label: "Events", href: "./store/events"},
                                             {label: "Settings", href: "./store/settings/store-info"},
                                             {label: "Devices", href: "./devices"}
                                           ]}
              /> : null}
              {(isHrDocumentsFeatureEnabled || isRemindersFeatureEnabled || isInsightsFeatureEnabled) ? (
                      <SidebarIcon icon={NavHRTeamIcon} label="HR" to="./hr"
                                   isNavExpanded={isNavExpanded}
                                   subItems={[
                                     ...(isHrDocumentsFeatureEnabled
                                             ? [{ label: "Documents", href: "./hr/documents" }]
                                             : []),
                                     ...(isRemindersFeatureEnabled
                                             ? [{ label: "Reminders", href: "./hr/reminders" }]
                                             : []),
                                     ...(isInsightsFeatureEnabled && Boolean(permissions?.hasInsightsStreet)
                                             ? [{ label: "Insights", href: "./hr/insights" }]
                                             : []),
                                   ]}
                      />
              ) : null}
              {(() => {
                const trainingSubItems = [
                  ...(isTrainingInsightsFeatureEnabled && hasViewTrainingInsightsPermissions ? [
                    {label: "Insights", href: "./training/insights"}
                  ] : []),
                  // Future training features can be added here
                ];

                return trainingSubItems.length > 0 ? (
                  <SidebarIcon icon={() => <GraduationCap/>} to="./training" label="Training"
                               isNavExpanded={isNavExpanded}
                               subItems={trainingSubItems}
                  />
                ) : null;
              })()}
              {isEducationFeatureEnabled ?
                <SidebarIcon icon={NavEducation} to="/$businessId/$storeId/education" label="Education"
                            isNavExpanded={isNavExpanded}
                />
              : null}
              {store.permissions?.isStripeEnabled && store.permissions?.canManageSubscription ?
                      <SidebarIcon icon={NavSettingsIcon} to="./settings" label="Settings"
                                   isNavExpanded={isNavExpanded}
                                   subItems={[
                                     {label: "Manage Subscription", href: "./settings"},
                                   ]}
                      /> : null}
            </nav>
          </div>
        </div>
      </div>
      <div className="flex flex-col w-full md:pl-16">
        <header className="flex h-14 items-center gap-4 border-b bg-muted/40 px-4 lg:h-[60px] lg:px-6">
          <Sheet>
            <SheetTrigger asChild>
              <Button variant="outline"
                      size="icon"
                      className="shrink-0 md:hidden">
                <Menu className="h-5 w-5"/>
                <span className="sr-only">Toggle navigation menu</span>
              </Button>
            </SheetTrigger>
            <SheetContent side="left" className="flex flex-col">
              <nav className="flex flex-col items-stretch gap-2 text-lg font-medium pt-6">
                <SheetClose asChild>
                  <Link activeProps={{className: "bg-gioaBlue  text-white"}}
                        activeOptions={{exact: true}}
                        from={Route.fullPath} to="./schedules"
                        className="mx-[-0.65rem] flex items-center gap-4 rounded-xl px-3 py-2 text-muted-foreground hover:text-foreground">
                    <Home className="h-5 w-5"/>
                    Dashboard
                  </Link>
                </SheetClose>
                <SheetClose asChild>
                  <Link activeProps={{className: "bg-gioaBlue  text-white"}}
                        from={Route.fullPath} to="./schedules/builder"
                        className="mx-[-0.65rem] flex items-center gap-4 rounded-xl px-3 py-2 text-muted-foreground hover:text-foreground">
                    <Calendar className="h-5 w-5"/>
                    Schedule
                  </Link>
                </SheetClose>
                <SheetClose asChild>
                  <Link activeProps={{className: "bg-gioaBlue  text-white"}}
                        from={Route.fullPath} to="./schedules/requests"
                        className="mx-[-0.65rem] flex items-center gap-4 rounded-xl px-3 py-2 text-muted-foreground hover:text-foreground">
                    <CopyCheck className="h-5 w-5"/>
                    Requests
                    {numPending > 0 ?
                      <Badge size={"sm"} colorScheme={"destructive"}>
                        {numPending}
                      </Badge>
                      : null}
                  </Link>
                </SheetClose>
                <SheetClose asChild>
                  <Link activeProps={{className: "bg-muted text-foreground"}}
                        from={Route.fullPath} to="/$businessId/$storeId/checklists"
                        className="mx-[-0.65rem] flex items-center gap-4 rounded-xl px-3 py-2 text-muted-foreground hover:text-foreground">
                    <ClipboardList className="h-5 w-5"/>
                    Checklists
                  </Link>
                </SheetClose>
                <SheetClose asChild>
                  <Link activeProps={{className: "bg-gioaBlue  text-white"}}
                        from={Route.fullPath} to="/$businessId/$storeId/education"
                        className="mx-[-0.65rem] flex items-center gap-4 rounded-xl px-3 py-2 text-muted-foreground hover:text-foreground">
                    <GraduationCap className="h-5 w-5"/>
                    Education
                  </Link>
                </SheetClose>
                <SheetClose asChild>
                  <Link activeProps={{className: "bg-gioaBlue  text-white"}}
                        from={Route.fullPath} to="./schedules/settings"
                        className="mx-[-0.65rem] flex items-center gap-4 rounded-xl px-3 py-2 text-muted-foreground hover:text-foreground">
                    <Cog className="h-5 w-5"/>
                    Settings
                  </Link>
                </SheetClose>
                {showDataLink ? <SheetClose asChild>
                  <Link activeProps={{className: "bg-gioaBlue  text-white"}}
                        from={Route.fullPath} to="./schedules/data"
                        className="mx-[-0.65rem] flex items-center gap-4 rounded-xl px-3 py-2 text-muted-foreground hover:text-foreground">
                    <DatabaseIcon className="h-5 w-5"/>
                    Data and Forecasting
                  </Link>
                </SheetClose> : null}
              </nav>
            </SheetContent>
          </Sheet>
          <div className="w-full flex-1">
            {isMultipleStores
                    ? <Link from={Route.fullPath}
                            to={getNextStoreLinkTo()}
                            params={{storeId: nextStoreId}}
                            className={"flex gap-2 items-center"}>
                      <Text>{store.title}</Text>
                      <ArrowUpDown size={16}/>
                    </Link>
                    : <Text>{store.title}</Text>}
          </div>
          <div className="flex flex-row gap-3 items-center space-x-4">
            {(daysUntilTrialEnds && daysUntilTrialEnds > 0) ?
                    <div className={"border rounded py-1 px-2 flex flex-row items-center gap-2"}>
                      <Text size={"sm"} muted>Free trial ends in</Text>
                      <div className={"bg-gray-300"} style={{width: 1, height: 16}}/>
                      <Text size={"sm"} semibold>{daysUntilTrialEnds.toFixed(0)} days</Text>
                    </div> : null}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button className="rounded-full" size="icon" variant="ghost">
                  <PersonAvatar person={user.person}/>
                  <span className="sr-only">User Menu</span>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" sideOffset={12}>
                <DropdownMenuItem onSelect={signOut}>
                  Logout
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </header>
        <main className="flex flex-1 flex-col">
          <Outlet/>
        </main>
      </div>

            <TrialExpirationDialog
                    isOpen={showTrialDialog && !showPaymentSuccessDialog}
                    onOpenChange={setShowTrialDialog}
                    daysRemaining={daysRemaining}
                    storeId={storeId}
            />

            <PaymentSuccessDialog
                    isOpen={showPaymentSuccessDialog}
                    onOpenChange={setShowPaymentSuccessDialog}
                    daysRemaining={daysRemaining}
                    storeId={storeId}
            />

            <PastDueDialog gracePeriodEndsAt={store.gracePeriodEndsAt}
                           isOpen={showPastDueDialog && !showPaymentSuccessDialog}
                           onOpenChange={setShowPastDueDialog}
                           gracePeriodDaysRemaining={store.gracePeriodEndsAt ? calculateDaysRemaining(store.gracePeriodEndsAt) : 0}
                           storeId={storeId}
            />

            <NoSubscriptionDialog
                    isOpen={showNoSubscriptionDialog && !showPaymentSuccessDialog}
                    onOpenChange={setShowNoSubscriptionDialog}
                    storeId={storeId}
            />
          </div>
  )
}
