import {createFileRoute, SearchSchemaInput, useRouter} from '@tanstack/react-router'
import React, {useCallback, useMemo, useState} from "react";
import {api} from "@/src/api.ts";
import {RowData, RowSpan, TimelineGrid} from "@/src/components/TimelineGrid.tsx";
import {AvailabilityRowSpan} from "@/src/components/AvailabilityRowSpan.tsx";
import {Helmet} from "react-helmet";
import {clamp, filter, includes, isEmpty, map} from "lodash";
import {rightPanelWidth, rowHeight} from "@/src/components/ScheduleBuilder.util.tsx";
import {ReportTimeline} from "@/src/components/ReportTimeline.tsx";
import {
  filterPeopleByStoreAreaTraining,
  getRowsForTeamMemberAvailability,
  HeatmapSpanMeta,
  isAvailabilityRow,
  isAvailabilitySpan,
  isHeatmapSpan,
  isTimeOffSpan,
  ReportSpanMeta
} from "@/src/availabilityReport.ts";
import {ReportTeamMemberSidebarRow} from "@/src/components/ReportTeamMemberSidebarRow.tsx";
import {HeatmapRowSpan} from "@/src/components/HeatmapRowSpan.tsx";
import {WeeklyFilterControl} from "@/src/components/WeeklyFilterControl.tsx";
import {
  createAreaFilter,
  createNumStarsFilter,
  createTimeOffFilter
} from "@/src/components/WeeklyViewControlBar.types.ts";
import {DateTime} from "luxon";
import {DatePicker} from "@/src/components/DatePicker.tsx";
import {Button} from "@/src/components/ui/button.tsx";
import {ArrowLeftIcon, ChevronLeftIcon, ChevronRightIcon} from 'lucide-react';
import {z} from 'zod';
import {TimeOffRowSpan} from "@/src/components/TimeOffRowSpan.tsx";
import {useScheduleEvents} from "@/src/hooks/useScheduleEvents.tsx";
import {TimeOffRequestSheet} from "@/src/components/TimeOffRequestSheet.tsx";
import {useDisclosure} from "@/src/hooks/useDisclosure.tsx";
import { storeTimeToIncrement } from '@gioa/api/src/scheduleBuilder.util.ts';
import {SchedulePersonDto, SchedulePersonTimeOffDto} from "../../../../../../../api/src/schedulePersonDto.ts";

const availabilitySearchSchema = z.object({
  areaFilter: z.array(z.string()).optional(),
  starsFilter: z.array(z.string()).optional(),
  timeOffFilter: z.array(z.string()).optional(),
  selectedDate: z.string(),
  toReqId: z.string().optional(),
});

type AvailabilitySearch = z.infer<typeof availabilitySearchSchema>;

export const Route = createFileRoute('/_signedin/$businessId/$storeId/reports/availability')({
  component: AvailabilityReportScreen,
  validateSearch: (search: unknown & SearchSchemaInput): AvailabilitySearch => {
    if (availabilitySearchSchema.safeParse(search).success) {
      return search as any;
    }
    return {
      timeOffFilter: ["approved"],
      areaFilter: [],
      starsFilter: [],
      selectedDate: DateTime.now().toISO()
    };
  }
})

const defaultIncrementWidth = 24;
const storeHours = {
  start: "05:00",
  end: "23:00"
} // TODO get from schedule?

function AvailabilityReportScreen() {
  const {businessId, storeId} = Route.useParams();
  const navigate = Route.useNavigate();
  const searchParams = Route.useSearch();

  const [{people, timezone}] = api.user.getAllSchedulePeopleAtStore.useSuspenseQuery({
    storeId: storeId
  });

  const [store] = api.user.getStoreAdmin.useSuspenseQuery({storeId: storeId}, {
    staleTime: 1000 * 60 * 60 // 1 hour
  });
  const storeAreas = store.areas ?? [];
  const [isEventsOpen, setIsEventsOpen] = useState(true);
  const [zoom, setZoom] = useState<number>(0.6666666666666667);
  const today = DateTime.now().setZone(timezone);
  const selectedDate = useMemo(() => DateTime.fromISO(searchParams.selectedDate).setZone(timezone), [searchParams.selectedDate]);
  const toReqId = searchParams.toReqId;

  const starsFilter = useMemo(() => {
    const filter = createNumStarsFilter(0);
    filter.value = searchParams.starsFilter ?? [];
    return filter;
  }, [searchParams.starsFilter]);

  const timeOffFilter = useMemo(() => {
    return createTimeOffFilter(0, searchParams.timeOffFilter ?? ["approved"]);
  }, [searchParams.timeOffFilter]);

  const timelineHeight = 48
  const eventsHeight = rowHeight;

  const {
    eventsForToday,
  } = useScheduleEvents({
    week: {
      year: selectedDate.weekYear,
      week: selectedDate.weekNumber,
    },
    dayOfWeek: selectedDate.weekday,
    storeHours: storeHours,
    timezone: timezone ?? "America/New_York",
    storeId: storeId,
  })
  const topbarHeight = timelineHeight + (isEventsOpen && !isEmpty(eventsForToday) ? eventsHeight : 0);

  const areaFilter = useMemo(() => createAreaFilter(0, storeAreas, searchParams.areaFilter ?? []), [storeAreas, searchParams.areaFilter]);
  const incrementWidth = defaultIncrementWidth * zoom;
  const gridLineInterval = incrementWidth * 4;
  const numIncrements = storeTimeToIncrement(storeHours, storeHours.end);
  const incrementsContainerWidth = incrementWidth * numIncrements + rightPanelWidth;

  const filteredPeople = useMemo(() => {
    const peopleInAreas = filterPeopleByStoreAreaTraining({
      people,
      filterAreas: areaFilter.value,
      storeAreas
    });
    return !isEmpty(starsFilter?.value) ? filter(peopleInAreas, p => {
      const numStars = map(starsFilter.value, s => parseInt(s));
      return includes(numStars, p.proficiencyRanking);
    }) : peopleInAreas;
  }, [people, areaFilter, starsFilter]);

  const rowData = useMemo(() => {
    const includeApprovedTimeOff = includes(timeOffFilter.value, "approved");
    const includePendingTimeOff = includes(timeOffFilter.value, "pending");
    console.log("timeOffFilter", timeOffFilter.value);

    return getRowsForTeamMemberAvailability({
      isoWeek: {
        year: selectedDate.weekYear,
        week: selectedDate.weekNumber,
        day: selectedDate.weekday
      },
      people: filteredPeople,
      timezone,
      storeHours,
      includeApprovedTimeOff,
      includePendingTimeOff
    });
  }, [selectedDate, filteredPeople, timezone, storeHours, timeOffFilter.value]);

  const bgStyle = {
    width: incrementsContainerWidth,
    minHeight: `calc(100% - ${topbarHeight}px)`,
    backgroundImage: `
    repeating-linear-gradient(
      to right,
      transparent,
      transparent ${gridLineInterval - 1}px,
      rgba(0,0,0,0.1) ${gridLineInterval - 1}px,
      rgba(0,0,0,0.1) ${gridLineInterval}px
    )
  `,
    backgroundSize: '100% 100%',
    backgroundPosition: '0 0',
  };

  const onWheel = useCallback((e: React.WheelEvent) => {
    if (e.ctrlKey) {
      setZoom(z => {
        return clamp(z + (e.deltaY > 0 ? -0.05 : 0.05), 0.5, 1.5);
      });
    }
  }, []);

  const onClickTeamMember = useCallback((row: RowData<SchedulePersonDto, ReportSpanMeta>) => {
    console.log("Clicked team member", row);
  }, []);

  const timeOffSheet = useDisclosure(Boolean(toReqId));
  const [timeOffRequestId, setTimeOffRequestId] = useState<string | undefined>(toReqId);

  const onClickTimeOff = useCallback((timeOff: SchedulePersonTimeOffDto) => {
    setTimeOffRequestId(timeOff.requestId);
    timeOffSheet.onOpen();
  }, [])

  const renderSpan = useCallback((params: {
    row: RowData<SchedulePersonDto | {}, ReportSpanMeta>;
    span: RowSpan<ReportSpanMeta>;
    rowIndex: number;
    spanIdx: number;
  }) => {
    if (isAvailabilitySpan(params.span)) {
      const isAllDay = params.span.metadata.isAllDay;
      return <AvailabilityRowSpan {...params} key={params.row.id + params.span.start + params.span.end}
                                  range={params.span.metadata.availability}
                                  isAllDay={isAllDay}/>
    } else if (isTimeOffSpan(params.span)) {
      if (includes(timeOffFilter.value, params.span.metadata.timeOff.status)) {
        return <TimeOffRowSpan {...params} key={params.row.id + params.span.start + params.span.end}
                               onClick={onClickTimeOff}
                               isSelected={params.span.metadata.timeOff.requestId === timeOffRequestId && timeOffSheet.isOpen}
                               {...params.span.metadata}/>
      }
      return null;
    } else if (isHeatmapSpan(params.span)) {
      const includeApprovedTimeOff = includes(timeOffFilter.value, "approved");
      const includePendingTimeOff = includes(timeOffFilter.value, "pending");

      return <HeatmapRowSpan {...params} key={params.span.start + params.span.end}
                             span={params.span as RowSpan<HeatmapSpanMeta>}
                             includeApprovedTimeOff={includeApprovedTimeOff}
                             includePendingTimeOff={includePendingTimeOff}
                             allSpans={params.row.spans as RowSpan<HeatmapSpanMeta>[]}/>
    }
  }, [storeHours, timeOffFilter.value, onClickTimeOff, timeOffRequestId, timeOffSheet.isOpen]);

  const onGoToPreviousDay = () => {
    const newDate = selectedDate.minus({days: 1});
    navigate({
      search: (prev) => ({
        ...prev,
        selectedDate: newDate.toISO()
      }),
      replace: true
    });
  }

  const onGoToNextDay = () => {
    const newDate = selectedDate.plus({days: 1});
    navigate({
      search: (prev) => ({
        ...prev,
        selectedDate: newDate.toISO()
      }),
      replace: true
    });
  }

  const router = useRouter();
  const onBack = () => {
    router.history.back();
  }

  return <div className={"h-screen overflow-auto relative flex flex-col items-stretch"}>
    <Helmet>
      <title>
        Team Member Availability - Nation
      </title>
    </Helmet>

    {/*Topbar*/}
    <div className={"bg-white flex flex-wrap gap-2 items-center px-5 py-3 sticky left-0 border-b-2 border-slate-300"}>
      <Button variant={"outline"} onClick={onBack}
              leftIcon={<ArrowLeftIcon size={16} className={"text-gray-500"}/>}>
        Back
      </Button>

      <div className={"flex flex-row gap-1"}>
        <Button variant={"outline"} onClick={onGoToPreviousDay}
                title={"Go to previous day"}>
          <ChevronLeftIcon className={"text-gray-600"}/>
        </Button>
        <DatePicker
                value={selectedDate}
                labelFormat={DateTime.DATE_MED_WITH_WEEKDAY}
                onChange={(newDate) => {
                  navigate({
                    search: (prev) => ({
                      ...prev,
                      selectedDate: newDate.toISO()
                    }),
                    replace: true
                  });
                }}
                today={today}
        />
        <Button variant={"outline"} onClick={onGoToNextDay}
                title={"Go to next day"}>
          <ChevronRightIcon className={"text-gray-600"}/>
        </Button>
      </div>

      <WeeklyFilterControl
              filter={areaFilter}
              onDeleteFilter={() => {
                navigate({
                  search: (prev) => ({
                    ...prev,
                    areaFilter: []
                  }),
                  replace: true
                });
              }}
              deleteLabel={"Reset"}
              setFilterValue={(id, value) => {
                navigate({
                  search: (prev) => ({
                    ...prev,
                    areaFilter: value
                  }),
                  replace: true
                });
              }}
      />

      <WeeklyFilterControl
              filter={starsFilter}
              deleteLabel={"Reset"}
              onDeleteFilter={() => {
                navigate({
                  search: (prev) => ({
                    ...prev,
                    starsFilter: []
                  }),
                  replace: true
                });
              }}
              setFilterValue={(id, value) => {
                navigate({
                  search: (prev) => ({
                    ...prev,
                    starsFilter: value
                  }),
                  replace: true
                });
              }}
      />

      <WeeklyFilterControl
              filter={timeOffFilter}
              deleteLabel={"Reset"}
              onDeleteFilter={() => {
                navigate({
                  search: (prev) => ({
                    ...prev,
                    timeOffFilter: []
                  }),
                  replace: true
                });
              }}
              setFilterValue={(id, value) => {
                navigate({
                  search: (prev) => ({
                    ...prev,
                    timeOffFilter: value
                  }),
                  replace: true
                });
              }}
      />

    </div>

    <div className={"flex grow w-screen overflow-auto"}>
      {/*Left sidebar*/}
      <div className={"bg-white sticky left-0 z-20 border-r border-slate-300 py-3"}>
        <div style={{height: topbarHeight}}
             className={"border-b border-slate-200 px-4 flex items-center min-w-[14rem]"}>
          {store.title}
        </div>
        <div className={"pt-6"}>
          <div className={"py-1 px-4 text-lg bold text-right"} style={{height: rowHeight}}>
            {filteredPeople.length} Team Members
          </div>
          {map(rowData, row => {
            if (isAvailabilityRow(row)) {
              return <ReportTeamMemberSidebarRow onClickTeamMember={onClickTeamMember} key={row.id}
                                                 rowHeight={rowHeight} row={row}/>
            } else {
              return null;
            }
          })}
        </div>
      </div>

      {/*Timeline and shift rows*/}
      <div className={"pt-3 grow bg-slate-100"}>
        {/*Timeline*/}
        <ReportTimeline storeHours={storeHours}
                        timelineHeight={timelineHeight}
                        isEventsOpen={isEventsOpen}
                        events={eventsForToday}
                        numIncrements={numIncrements}
                        incrementWidth={incrementWidth}
                        topbarHeight={topbarHeight}
                        width={incrementsContainerWidth}/>

        {/*Team Members*/}
        <div style={bgStyle} className="relative pt-6 mx-4" onWheel={onWheel}>
          <TimelineGrid columnWidth={incrementWidth} rowHeight={rowHeight}
                        rows={rowData} renderSpan={renderSpan}/>
        </div>
      </div>
    </div>

    {timeOffRequestId ?
            <TimeOffRequestSheet isOpen={timeOffSheet.isOpen}
                                 businessId={businessId}
                                 storeId={storeId}
                                 onOpenChange={timeOffSheet.setOpen}
                                 timeOffId={timeOffRequestId}/> : null}
  </div>
}
