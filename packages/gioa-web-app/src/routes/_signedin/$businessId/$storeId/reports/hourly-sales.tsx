import {create<PERSON>ile<PERSON><PERSON><PERSON>, Link, SearchSchemaInput, useNavigate} from '@tanstack/react-router'
import {ArrowLeftIcon, ArrowRightIcon, TrendingUp} from "lucide-react"
import {Area, AreaChart, CartesianGrid, Tooltip, XAxis, YAxis} from "recharts"
import {Card, CardContent, CardDescription, CardHeader, CardTitle,} from "@/src/components/ui/card.tsx"
import {ChartConfig, ChartContainer,} from "@/src/components/ui/chart.tsx"
import {api} from "@/src/api.ts";
import React, {useMemo, useState} from "react";
import {z} from "zod";
import {DateTime} from "luxon";
import {Heading} from "@/src/components/Heading.tsx";
import {Select, SelectContent, SelectItem, SelectTrigger, SelectValue} from "@/src/components/ui/select.tsx";
import {WeekPicker} from "@/src/components/WeekPicker.tsx";
import {<PERSON><PERSON>, buttonVariants} from "@/src/components/ui/button.tsx";
import {cn} from "@/src/util.ts";
import {IsoDayPicker} from "@/src/components/IsoDayPicker.tsx";
import {YearMonthPicker} from "@/src/components/MonthPicker.tsx";
import {DateTimeUnit} from "luxon/src/datetime";
import {ErrorAlert} from "@/src/components/ErrorAlert.tsx";
import {Spinner} from "@/src/components/Spinner.tsx";
import {IsoCompleteDate, isoCompleteDate, IsoWeekDate} from "../../../../../../../api/src/timeSchemas.ts";
import {getDateFromIsoCompleteDate, getDateTimeFromWeekDayTime} from "../../../../../../../api/src/date.util.ts";
import {map, reduce, sumBy} from "lodash";
import {formatCurrency} from "@gioa/common/src/dataFormatters.ts";
import {dollars} from "../../../../../../../api/src/scheduling/metrics/dollars.ts";
import {generateIsoWeekDates, takeFromGenerator} from "../../../../../../../api/src/scheduleBuilder.util.ts";
import {toDollars} from "../../../../../../../api/src/scheduling/metrics/cents.ts";

// Define time frame options
export type TimeFrameFilter = "day" | "week" | "month" | "all";
export const timeFrameFilter = z.enum(["day", "week", "month", "all"]);

// Define search schema for URL parameters
const salesSearchSchema = isoCompleteDate.extend({
  timeFrame: timeFrameFilter.optional().default("day"),
});

type SalesSearch = z.infer<typeof salesSearchSchema>;

export const Route = createFileRoute('/_signedin/$businessId/$storeId/reports/hourly-sales')({
  component: HourlySales,
  validateSearch: (search: unknown & SearchSchemaInput): SalesSearch | undefined => {
    if (salesSearchSchema.safeParse(search).success) {
      return search as any;
    }
  },
})

const chartConfig = {
  sales: {
    label: "Sales",
    color: "hsl(var(--chart-1))",
    icon: TrendingUp,
  },
} satisfies ChartConfig

function HourlySales() {
  const {businessId, storeId} = Route.useParams();
  const searchParams = Route.useSearch();
  const navigate = useNavigate();
  const [store] = api.user.getStoreAdmin.useSuspenseQuery({storeId: storeId!}, {
    staleTime: 1000 * 60 * 60 // 1 hour
  });
  const timezone = store.timezone;
  const [today] = useState(DateTime.now().setZone(timezone));

  const timeFrameFilter = searchParams?.timeFrame ?? "day";
  const setTimeFrameFilter = (timeFrame: TimeFrameFilter) => {
    navigate({
      from: Route.fullPath,
      search: (prev: any) => ({...prev, timeFrame}),
    });
  };

  const isoDateSearch = searchParams?.week && searchParams?.year ? {
    week: searchParams.week,
    year: searchParams.year,
    month: searchParams.month ?? undefined,
    day: searchParams.day ?? undefined,
  } : {
    week: today.weekNumber,
    year: today.year,
    month: today.month,
    day: today.weekday,
  };

  const [selectedIsoDate, selectedDate] = useMemo(() => {
    const date = getDateTimeFromWeekDayTime({
      year: isoDateSearch?.year ?? today.year,
      week: isoDateSearch?.week ?? today.weekNumber,
      day: isoDateSearch?.day ?? today.weekday,
      time: "00:00",
      timezone: timezone ?? null
    });
    return [{
      year: date.weekYear,
      week: date.weekNumber,
      month: date.month,
      day: date.weekday,
    }, date]
  }, [isoDateSearch, today, timezone]);

  const dateRange = React.useMemo(() => {
    if (!timeFrameFilter || timeFrameFilter === "all") {
      // default to last 30 days for "all" time frame
      return {
        start: selectedDate.minus({days: 30}).startOf('day').toJSDate(),
        end: selectedDate.endOf('day').toJSDate(),
      };
    }

    const unit = timeFrameFilter.toLowerCase() as DateTimeUnit;
    return {
      start: selectedDate.startOf(unit).toJSDate(),
      end: selectedDate.endOf(unit).toJSDate(),
    }
  }, [timeFrameFilter, timezone, today, selectedIsoDate]);

  const salesQuery = api.data.queryHourlySales.useQuery({
    storeId: storeId,
    start: dateRange.start,
    end: dateRange.end,
  });

  // format data for chart
  const chartData = useMemo(() => {
    if (!salesQuery.data) return [];

    // For monthly view, aggregate data by day
    if (timeFrameFilter === "month") {
      // Group data points by date
      const dailyData = reduce(salesQuery.data.dataPoints, (acc, point) => {
        const pointTime = DateTime.fromJSDate(point.time).setZone(timezone);
        const dateKey = pointTime.toFormat('yyyy-MM-dd');

        if (!acc[dateKey]) {
          acc[dateKey] = {
            date: pointTime.toFormat('MM/dd'),
            time: pointTime.toFormat('MM/dd'),
            hour: '',
            dayOfWeek: pointTime.toFormat('cccc'), // Add day of week (e.g., 'Mon', 'Tue')
            dayOfMonth: pointTime.toFormat('d'), // Just the day number
            shortDayOfWeek: pointTime.toFormat('ccc').substring(0, 2), // First letter of day of week
            sales: 0,
            timestamp: point.time,
            pointTime: pointTime,
          };
        }

        acc[dateKey].sales += toDollars(point.amount); // Convert cents to dollars and add to daily total

        return acc;
      }, {} as Record<string, any>);

      // Convert to array and sort by date
      return Object.values(dailyData).sort((a, b) =>
              a.pointTime.toMillis() - b.pointTime.toMillis()
      );
    }

    // For other views, keep hourly data points
    return map(salesQuery.data.dataPoints, point => {
      const pointTime = DateTime.fromJSDate(point.time).setZone(timezone);
      return {
        time: pointTime.toFormat('MM/dd t'),
        hour: pointTime.toFormat('t'),
        date: pointTime.toFormat('MM/dd'),
        sales: toDollars(point.amount), // Convert cents to dollars
        timestamp: point.time,
      };
    });
  }, [salesQuery.data, timezone, timeFrameFilter]);

  // calculate summary stats
  const summaryStats = useMemo(() => {
    if (!chartData.length) return {
      total: dollars(0),
      average: dollars(0),
      max: dollars(0)
    };

    const total = sumBy(chartData, point => point.sales);
    const average = total / chartData.length;
    const max = Math.max(...chartData.map(point => point.sales));

    return {
      total: dollars(total),
      average: dollars(average),
      max: dollars(max)
    };
  }, [chartData]);

  // Date navigation handlers
  const setSelectedWeek = (week: IsoWeekDate) => {
    const date = getDateTimeFromWeekDayTime({
      year: week.year,
      week: week.week,
      day: week.day ?? selectedIsoDate.day ?? 1,
      time: "00:00",
      timezone: timezone ?? null
    });

    navigate({
      from: Route.fullPath,
      search: prev => ({
        ...prev,
        year: date.weekYear,
        week: date.weekNumber,
        month: date.month,
        day: date.weekday,
      })
    });
  }

  const setSelectedDate = (date: IsoCompleteDate) => {
    const newDate = getDateFromIsoCompleteDate(date, timezone);
    navigate({
      from: Route.fullPath,
      search: prev => ({
        ...prev,
        year: newDate.weekYear,
        week: newDate.weekNumber,
        month: newDate.month,
        day: newDate.weekday,
      })
    });
  }

  // format X-axis ticks based on time frame
  const formatXAxisTick = (value: string) => {
    const point = chartData.find(p => p.time === value);
    if (!point) return value;

    switch (timeFrameFilter) {
      case 'day':
        return point.hour;
      case 'month':
        // For monthly view, show day of month and first letter of day of week
        if (point.dayOfMonth && point.shortDayOfWeek) {
          return `${point.dayOfMonth}-${point.shortDayOfWeek}`;
        }
        return point.date;
      case 'week':
        return point.date;
      default:
        return value;
    }
  };

  return (
          <section className="p-3 md:p-6">
            <div className="flex flex-row items-center gap-2 mb-6">
              <Link className="flex flex-row items-center gap-2" to={`/${businessId}/${storeId}/schedules/reports` as any}>
                <ArrowLeftIcon size={16}/>
                Back to Reports
              </Link>
            </div>

            <Heading level={1} size={"md"} className="mb-6">
              Sales Report
            </Heading>

            <div className="flex flex-row gap-2 flex-wrap items-center justify-between py-4">
              <div className="flex flex-row items-center justify-between gap-3">
                <div>
                  <Select onValueChange={val => setTimeFrameFilter(val as TimeFrameFilter)}
                          value={timeFrameFilter}>
                    <SelectTrigger>
                      <SelectValue placeholder="Time Frame"/>
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value={"day"}>
                        Day
                      </SelectItem>
                      <SelectItem value={"week"}>
                        Week
                      </SelectItem>
                      <SelectItem value={"month"}>
                        Month
                      </SelectItem>
                      <SelectItem value={"all"}>
                        All Time (30 days)
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {timeFrameFilter === "day" ?
                        <DayFilter selectedWeek={selectedIsoDate} timezone={timezone}
                                   setSelectedWeek={setSelectedDate} today={today}/> : null}
                {timeFrameFilter === "week" ?
                        <WeekFilter selectedWeek={selectedIsoDate} timezone={timezone}
                                    setSelectedWeek={setSelectedWeek} today={today}/> : null}
                {timeFrameFilter === "month" ?
                        <MonthFilter selectedWeek={selectedIsoDate} timezone={timezone}
                                     setSelectedMonth={setSelectedDate}/> : null}
              </div>
            </div>

            {salesQuery.isError && <ErrorAlert error={salesQuery.error} className="mb-6"/>}

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">Total Sales</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{formatCurrency(summaryStats.total)}</div>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">
                    {timeFrameFilter === "month" ? "Average Per Day" : "Average Per Hour"}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{formatCurrency(summaryStats.average)}</div>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">
                    {timeFrameFilter === "month" ? "Peak Day" : "Peak Hour"}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{formatCurrency(summaryStats.max)}</div>
                </CardContent>
              </Card>
            </div>

            {salesQuery.isLoading ? (
                    <div className="flex justify-center items-center h-64">
                      <Spinner/>
                    </div>
            ) : chartData.length === 0 ? (
                    <Card className="p-6 text-center">
                      <p>No sales data available for the selected time period.</p>
                    </Card>
            ) : (
                    <Card className="p-4">
                      <CardHeader>
                        <CardTitle>Sales Over Time</CardTitle>
                        <CardDescription>
                          {DateTime.fromJSDate(dateRange.start).setZone(timezone).toFormat('LLL dd, yyyy')} - {DateTime.fromJSDate(dateRange.end).setZone(timezone).toFormat('LLL dd, yyyy')}
                        </CardDescription>
                      </CardHeader>
                      <CardContent>
                        <ChartContainer config={chartConfig} className={"max-h-[600px] aspect-[4]"}>
                          <AreaChart
                                  data={chartData}
                                  margin={{
                                    top: 10,
                                    right: 30,
                                    left: 0,
                                    bottom: 0,
                                  }}
                          >
                            <CartesianGrid strokeDasharray="3 3"/>
                            <XAxis
                                    dataKey="time"
                                    tickFormatter={formatXAxisTick}
                                    tickLine={false}
                                    axisLine={true}
                                    tickMargin={8}
                            />
                            <YAxis
                                    width={72}
                                    tickFormatter={(value) => formatCurrency(value)}
                                    tickLine={false}
                                    axisLine={true}
                                    tickMargin={8}
                            />
                            <Tooltip
                                    formatter={(value) => [formatCurrency(dollars(value as number)), "Sales"]}
                                    labelFormatter={(label) => {
                                      const point = chartData.find(p => p.time === label);
                                      if (!point) return label;

                                      // For monthly view, show date with day of week
                                      if (timeFrameFilter === "month") {
                                        return point.dayOfWeek ? `${point.date} ${point.dayOfWeek}` : point.date;
                                      }

                                      // For other views, show date and hour
                                      return `${point.date} ${point.hour}`;
                                    }}
                            />
                            <Area
                                    type="monotone"
                                    dataKey="sales"
                                    stroke="var(--color-sales)"
                                    fill="var(--color-sales)"
                                    fillOpacity={0.3}
                            />
                          </AreaChart>
                        </ChartContainer>
                      </CardContent>
                    </Card>
            )}
          </section>
  );
}

// Day filter component
const DayFilter = ({selectedWeek, timezone, setSelectedWeek, today}: {
  selectedWeek: IsoCompleteDate,
  timezone: string | null,
  setSelectedWeek: (week: IsoCompleteDate) => void,
  today: DateTime,
}) => {
  const thisDay = getDateFromIsoCompleteDate(selectedWeek, timezone);
  const nextDay = thisDay.plus({days: 1});
  const prevDay = thisDay.minus({days: 1});

  return <div className="flex flex-row items-center justify-between gap-3">
    <IsoDayPicker value={selectedWeek} onChange={setSelectedWeek} today={today}/>
    <div className="inline-flex rounded-md" role="group">
      <Button title={"Go to the previous day"}
              variant={"outline"} className={"rounded-r-none relative hover:z-10"}
              onClick={() => setSelectedWeek({day: prevDay.weekday, week: prevDay.weekNumber, year: prevDay.weekYear})}>
        <ArrowLeftIcon size={16} className={"mr-1"}/>
        Prev
      </Button>
      <Button title={"Go to the next day"}
              variant={"outline"} className={"rounded-l-none border-l-0 relative hover:z-10"}
              onClick={() => setSelectedWeek({day: nextDay.weekday, week: nextDay.weekNumber, year: nextDay.weekYear})}>
        Next
        <ArrowRightIcon size={16} className={"ml-1"}/>
      </Button>
    </div>
  </div>
}

// Week filter component
const WeekFilter = ({selectedWeek, timezone, setSelectedWeek, today}: {
  selectedWeek: IsoCompleteDate,
  timezone: string | null,
  setSelectedWeek: (week: IsoCompleteDate) => void,
  today: DateTime,
}) => {
  const [, nextWeek] = takeFromGenerator(generateIsoWeekDates(selectedWeek, timezone ?? null), 2);
  const [, prevWeek] = takeFromGenerator(generateIsoWeekDates(selectedWeek, timezone ?? null, (d: DateTime) => d.minus({weeks: 1})), 2);

  return <div className="flex flex-row items-center justify-between gap-3">
    <WeekPicker value={selectedWeek} today={today} timezone={timezone ?? null}
                onChange={setSelectedWeek}/>
    <div className="inline-flex rounded-md" role="group">
      <Link title={"Go to the previous week"}
            from={Route.fullPath} search={(prev: any) => ({...prev, week: prevWeek.week, year: prevWeek.year})}
            className={cn(buttonVariants({variant: "outline"}), "rounded-r-none relative hover:z-10")}
      >
        <ArrowLeftIcon size={16} className={"mr-1"}/>
        Prev
      </Link>
      <Link title={"Go to the next week"}
            from={Route.fullPath} search={(prev: any) => ({...prev, week: nextWeek.week, year: nextWeek.year})}
            className={cn(buttonVariants({variant: "outline"}), "rounded-l-none border-l-0 relative hover:z-10")}
      >
        Next
        <ArrowRightIcon size={16} className={"ml-1"}/>
      </Link>
    </div>
  </div>
}

// Month filter component
const MonthFilter = ({selectedWeek, timezone, setSelectedMonth}: {
  selectedWeek: IsoCompleteDate,
  timezone: string | null,
  setSelectedMonth: (week: IsoCompleteDate) => void,
}) => {
  const thisMonth = getDateFromIsoCompleteDate(selectedWeek, timezone);
  const nextMonth = thisMonth.plus({months: 1});
  const prevMonth = thisMonth.minus({months: 1});

  return <div className="flex flex-row items-center justify-between gap-3">
    <YearMonthPicker value={selectedWeek} onChange={setSelectedMonth}/>
    <div className="inline-flex rounded-md" role="group">
      <Button title={"Go to the previous month"}
              variant={"outline"} className={"rounded-r-none relative hover:z-10"}
              onClick={() => setSelectedMonth({
                day: prevMonth.weekday,
                week: prevMonth.weekNumber,
                year: prevMonth.weekYear,
                month: prevMonth.month
              })}>
        <ArrowLeftIcon size={16} className={"mr-1"}/>
        Prev
      </Button>
      <Button title={"Go to the next month"}
              variant={"outline"} className={"rounded-l-none border-l-0 relative hover:z-10"}
              onClick={() => setSelectedMonth({
                day: nextMonth.weekday,
                week: nextMonth.weekNumber,
                year: nextMonth.weekYear,
                month: nextMonth.month
              })}>
        Next
        <ArrowRightIcon size={16} className={"ml-1"}/>
      </Button>
    </div>
  </div>
}
