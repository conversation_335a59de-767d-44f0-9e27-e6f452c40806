import {create<PERSON>ile<PERSON><PERSON><PERSON>, useBlocker, useNavigate} from '@tanstack/react-router'
import {ScheduleBuilder} from "@/src/components/ScheduleBuilder.tsx";
import {z} from "zod";
import {DayOfWeek, dayOfWeek, TimeOfDay} from "../../../../../../../../api/src/timeSchemas.ts";
import {serializedFilter, weeklyViewSort} from "@/src/components/WeeklyViewControlBar.types.ts";
import {ScheduleWeekBuilder} from "@/src/components/ScheduleWeekBuilder.tsx";
import {api} from "@/src/api.ts";
import React, {useEffect, useMemo, useRef, useState} from "react";
import {SchedulePersonClientDto} from "../../../../../../../../api/src/schedulePersonDto.ts";
import {isEqual, map} from "lodash";
import {getEffectiveAvailability} from "../../../../../../../../api/src/getEffectiveAvailability.ts";
import {Activity, DraftSchedule, BaseSchedule} from "../../../../../../../../api/src/scheduleSchemas.ts";
import {scheduleBuilderMachine} from "@/src/components/ScheduleBuilder.machine.tsx";
import {useMutationQueue} from "@/src/hooks/useMutationQueue.tsx";
import {ScheduleRowInfo} from "../../../../../../../../api/src/scheduleBuilder.util.ts";
import {getShift} from "../../../../../../../../api/src/scheduling.util.ts";
import {genShiftId} from "../../../../../../../../api/src/schemas.ts";
import {
  deleteScheduleShift,
  getShiftDay,
  insertShiftAfter,
  moveShiftArea,
  updateScheduleShift
} from "../../../../../../../../api/src/schedule.ts";
import {copyShift, updateShift, updateShiftActivities} from "../../../../../../../../api/src/shift.ts";
import {useIgnoreScheduleValidationMessages} from "@/src/hooks/useIgnoreScheduleValidationMessages.tsx";
import {validateSchedule} from "../../../../../../../../api/src/scheduleValidation.ts";
import {scheduleAndPeopleToValidationRequest} from "../../../../../../../../api/src/scheduleValidation.util.ts";
import {useMachine, useSelector} from '@xstate/react';
import {toast} from 'sonner';
import {useScheduleEvents} from "@/src/hooks/useScheduleEvents.tsx";
import {useRealtimeMutationQueue} from "@/src/hooks/useRealtimeMutationQueue.tsx";
// import { createBrowserInspector } from '@statelyai/inspect';

const scheduleSearchSchema = z.object({
  dayOfWeek: dayOfWeek.default(1),
  shiftId: z.string().optional(),
  w: z.boolean().default(false),

  filters: z.array(serializedFilter).optional(),
  sort: weeklyViewSort.optional(),
  search: z.string().optional(),
  daySort: dayOfWeek.optional(),
  views: z.array(z.string()).optional(),
  week: z.boolean().default(false),

  paymentSuccess: z.boolean().optional(),
})

export const Route = createFileRoute('/_signedin/$businessId/$storeId/schedules/$scheduleId/')({
  component: Component,
  validateSearch: scheduleSearchSchema,
})

// Uncomment for debugging the state machine
// const inspector = createBrowserInspector();

function Component() {
  const {dayOfWeek, shiftId, daySort, w, filters, sort, search, views, week} = Route.useSearch();
  const {businessId, storeId, scheduleId} = Route.useParams();
  const navigate = useNavigate({from: Route.fullPath});

  const [[features, scheduleRev, {people: peopleRaw}, store, user], [, schedQuery]] = api.useSuspenseQueries(t => {
    return [
      t.feature.getFeatures(),
      t.user.getSchedule({id: scheduleId}, {
        // Get the schedule fresh every time so that we have the right draft data and draftVersionNumber
        // TODO optimize this further by using setData on the query whenever the draft changes instead of refetching
        gcTime: 0,
        staleTime: 1000
      }),
      t.user.getAllSchedulePeopleAtStore({storeId: storeId}, {
        staleTime: 1000 * 60 * 60
      }),
      t.user.getStoreAdmin({storeId: storeId}, {
        staleTime: 1000 * 60 * 60 // 1 hour
      }),
      t.user.getUserProfile(undefined, {
        staleTime: 1000 * 60 * 60 // 1 hour
      })
    ];
  });

  // Fetch store address for location-based validations
  const [storeAddress] = api.user.getStoreAddress.useSuspenseQuery({
    storeId: storeId
  });

  // Fetch preceding and following weeks hours for Hawaii ACA validation
  const [weeksHoursMap] = api.user.get3PrecedingAnd3FollowingWeeksHours.useSuspenseQuery({
    storeId: storeId,
    currentWeek: scheduleRev.draft.week,
    state: storeAddress.state
  }, {
    staleTime: 1000 * 60 * 60 // 1 hour
  });

  const isRealtimeEnabled = features["realtimeScheduling"];
  const timezone = scheduleRev.timezone;
  const isForecastingEnabled = Boolean(store.permissions?.isForecastingEnabled);

  const people = useMemo((): SchedulePersonClientDto[] => {
    return map(peopleRaw, p => {
      const {
        availability: weekAvailability,
        preferences: weekPreferences
      } = getEffectiveAvailability(p.availability, scheduleRev.draft.week, timezone);
      return {
        ...p,
        weekAvailability: weekAvailability,
        weekPreferences: weekPreferences
      }
    });
  }, [peopleRaw]);

  // this is the local copy of the initial schedule. We update the local copy in realtime and sync changes in the background.
  // protectedSetSchedule should ONLY be called from setScheduleAndRef, so that the ref is always up to date.
  const [schedule, protectedSetSchedule] = useState<DraftSchedule>(scheduleRev.draft);

  // keeping a ref on the current schedule object so that setSchedule always has the latest inside of memoized callbacks and such
  const schedRef = useRef<DraftSchedule>(scheduleRev.draft);
  const setScheduleAndRef = (sched: DraftSchedule) => {
    schedRef.current = sched;
    protectedSetSchedule(sched);
  }

  // update the local schedule when the server schedule changes
  useEffect(() => {
    console.log("server update", scheduleRev.draftVersion);
    setScheduleAndRef(scheduleRev.draft);
  }, [scheduleRev.draft]);

  // setSchedule checks if the change needs to be saved to the server. So don't use it for changes to the schedule that don't need saving, such as changes received from the server.
  const setSchedule = (updater: DraftSchedule | ((schedule: DraftSchedule) => DraftSchedule), hints: {weekday: DayOfWeek | undefined}) => {
    const newSchedule = typeof updater === "function" ? updater(schedRef.current) : updater;

    // save the schedule if anything in it changes
    const oldSchedule = schedRef.current;
    if (oldSchedule && !isEqual(oldSchedule, newSchedule)) {
      setHasDraft(true);
      console.log("updating");
      onScheduleDraftChange(oldSchedule, newSchedule, hints);
    }
    setScheduleAndRef(newSchedule);
  }

  const [hasDraft, setHasDraft] = useState(scheduleRev.hasDraft);
  const isPublishedWithDraft = Boolean(scheduleRev.published && hasDraft);

  const [snapshot, send] = useMachine(scheduleBuilderMachine, {
    // Uncomment for debugging the state machine
    // inspect: inspector.inspect,
  });


  // emit a schedule activity heartbeat every 5s or whenever select shift changes
  const emitActivity = api.scheduling.emitScheduleActivity.useMutation();
  useEffect(() => {
    const heartbeat = () => {
      emitActivity.mutate({
        scheduleId: scheduleId,
        version: latestServerVersion.current,
        selectedShiftId: snapshot.context.selectedShiftId,
      })
    };

    const timer = setInterval(heartbeat, 5000);
    heartbeat();

    return () => {
      clearInterval(timer);
    };
  }, [snapshot.context.selectedShiftId]);

  const autoSaveMachine = snapshot.children.autoSave!;
  const autoSaveSnapshot = useSelector(autoSaveMachine, s => s);
  const autoSaveSend = autoSaveMachine.send!;

  const isSaveQueued = autoSaveSnapshot.matches("unsaved");
  const isSaving = autoSaveSnapshot.matches("saving")
  const isSaveError = autoSaveSnapshot.matches("saveError")

  const {onScheduleDraftChange, latestServerVersion} = isRealtimeEnabled
    ? useRealtimeMutationQueue(scheduleId, scheduleRev.draftVersion, autoSaveSend)
    : useMutationQueue(scheduleRev.draftVersion, autoSaveSend);

  const savingStatus = isSaving
    ? "saving"
    : isSaveQueued
      ? "pending"
      : "saved";

  useBlocker({
    blockerFn: () => {
      if (isSaveError) {
        return true;
      }
      return window.confirm('You have unsaved changes. Are you sure you want to leave?');
    },
    condition: (savingStatus === "pending" || savingStatus === "saving") && !isSaveError,
  })

  const storeAreas = store.areas || [];

  const onDeleteShift = (shift: ScheduleRowInfo) => {
    setSchedule(sched => deleteScheduleShift(sched, shift.id), {weekday: shift.dayOfWeek});
    send({type: "shiftDeleted"});
  }
  const onDuplicateShift = (shiftRow: ScheduleRowInfo) => {
    setSchedule(sched => {
      if (!shiftRow.areaId) {
        return sched;
      }

      const shift = getShift(sched, shiftRow.id);
      if (!shift) {
        return sched;
      }

      const newShift = copyShift(shift, {
        newShiftId: genShiftId(),
        copyAssignment: false
      });

      return insertShiftAfter(sched, shiftRow.areaId, newShift, shiftRow.id)
    }, {weekday: shiftRow.dayOfWeek});
  }

  const {
    unignoreAllMessages
  } = useIgnoreScheduleValidationMessages({
    scheduleId: scheduleId,
    storeId: storeId,
  });

  const onShiftUpdated = (shiftId: string, values: {
    start: TimeOfDay;
    end: TimeOfDay;
    assignedPersonId: string | undefined;
    description: string | undefined;
    isShiftLead: boolean;
    storePositionId: string | undefined;
    shiftAreaId: string;
  }) => {
    const shift = getShift(schedule, shiftId);
    if (!shift) return;

    if (shift?.assignedPersonId !== values.assignedPersonId ||
      shift?.range.start !== values.start ||
      shift?.range.end !== values.end ||
      shift?.isShiftLead !== values.isShiftLead ||
      shift?.storePositionId !== values.storePositionId) {
      unignoreAllMessages(shiftId);
    }

    setSchedule(sched => {
      let newDraft = updateScheduleShift(sched, shiftId, shift => updateShift(shift, values));

      // if the shift area changed, we have to move the shift to the new area
      if (values.shiftAreaId !== shift.shiftAreaId) {
        newDraft = moveShiftArea(newDraft, {
          shiftId,
          targetAreaId: values.shiftAreaId
        })
      }

      return newDraft;
    }, {weekday: getShiftDay(schedule, shiftId)?.dayOfWeek})
  }

  const onShiftActivitiesUpdated = (shiftId: string, activities: Activity[]) => {
    setSchedule(sched => {
      return updateScheduleShift(sched, shiftId, shift => updateShiftActivities(shift, activities));
    }, {weekday: getShiftDay(schedule, shiftId)?.dayOfWeek})
  }

  const validationResult = useMemo(() => {
    return validateSchedule(
      scheduleAndPeopleToValidationRequest({
        schedule: schedule,
        people,
        timezone,
        storeAreas,
        settings: store.schedulingSettings,
        weeksHoursMap: weeksHoursMap,
        storeState: storeAddress.state
      })
    );
  }, [schedule, people, storeAreas, timezone, store.schedulingSettings, weeksHoursMap]);

  // weeksHoursMap is now used directly by components

  const goToWeek = () => {
    navigate({
      from: Route.fullPath,
      to: "..",
      search: schedule.week
    })
  }

  const discardScheduleDraft = api.user.discardScheduleDraft.useMutation();
  const onDiscardDraft = () => {
    const shouldDiscard = window.confirm("Are you sure you want to cancel your changes to the published schedule?");
    if (!shouldDiscard) {
      return;
    }

    discardScheduleDraft.mutate({
      scheduleId: schedule.id
    }, {
      onSuccess: () => {
        toast.success("Your draft has been discarded.", {closeButton: true});
        goToWeek();
      }
    })
  }

  const onChangeView = (value: string, day?: number) => {
    navigate({
      replace: true,
      search: (prev) => {
        return {
          ...prev,
          week: value === "Week",
          dayOfWeek: day ?? prev.dayOfWeek
        }
      }
    })
  }

  const {
    eventsForToday,
    scheduleEvents,
    localRawEvents,
    setLocalRawEvents
  } = useScheduleEvents({
    week: schedule.week,
    dayOfWeek: dayOfWeek,
    storeHours: schedule.storeHours,
    timezone: timezone,
    storeId: storeId,
  })

  return <fieldset disabled={schedQuery.isFetching}>
    {week
      ? <ScheduleWeekBuilder routeFullPath={Route.fullPath}
                             cameFromWeeklyView={w} latestServerVersion={latestServerVersion}
                             daySort={daySort} shiftOffers={scheduleRev.shiftOffers}
                             hasDraft={hasDraft} businessId={businessId} isPresenceEnabled={isRealtimeEnabled}
                             timezone={timezone} currentPersonId={user.person.id!}
                             people={people}
                             onScheduleChange={setSchedule}
                             savingStatus={savingStatus}
                             schedule={schedule}
                             scheduleRev={scheduleRev}
                             send={send} settings={store.schedulingSettings}
                             snapshot={snapshot}
                             storeAreas={storeAreas}
                             filters={filters}
                             search={search}
                             sort={sort} onChangeView={onChangeView}
                             views={views}
                             onDeleteShift={onDeleteShift}
                             onDuplicateShift={onDuplicateShift}
                             onShiftUpdated={onShiftUpdated}
                             onShiftActivitiesUpdated={onShiftActivitiesUpdated}
                             onDiscardDraft={onDiscardDraft}
                             validationResult={validationResult}
                             storeId={storeId} scheduleId={scheduleId} shiftId={shiftId}
                             weeksHoursMap={weeksHoursMap}
                             storeState={storeAddress.state}/>
      : <ScheduleBuilder routeFullPath={Route.fullPath} shiftOffers={scheduleRev.shiftOffers}
                         eventsForToday={eventsForToday} scheduleEvents={scheduleEvents}
                         settings={store.schedulingSettings}
                         localRawEvents={localRawEvents} setLocalRawEvents={setLocalRawEvents}
                         scheduleRev={scheduleRev} onChangeView={onChangeView}
                         onDeleteShift={onDeleteShift} canViewPayRates={Boolean(store.permissions?.canViewPayRates)}
                         onDuplicateShift={onDuplicateShift} isForecastingEnabled={isForecastingEnabled}
                         onShiftUpdated={onShiftUpdated} isPresenceEnabled={isRealtimeEnabled}
                         onShiftActivitiesUpdated={onShiftActivitiesUpdated}
                         onDiscardDraft={onDiscardDraft}
                         validationResult={validationResult}
                         people={people} hasDraft={hasDraft} isPublishedWithDraft={isPublishedWithDraft}
                         latestServerVersion={latestServerVersion}
                         onScheduleChange={setSchedule}
                         savingStatus={savingStatus}
                         schedule={schedule} currentPersonId={user.person.id!}
                         send={send}
                         snapshot={snapshot}
                         storeAreas={storeAreas} storeTitle={store.title}
                         cameFromWeeklyView={w}
                         storeId={storeId} scheduleId={scheduleId} shiftId={shiftId}
                         dayOfWeek={dayOfWeek}
                         weeksHoursMap={weeksHoursMap}
                         storeState={storeAddress.state}/>}
  </fieldset>

}
