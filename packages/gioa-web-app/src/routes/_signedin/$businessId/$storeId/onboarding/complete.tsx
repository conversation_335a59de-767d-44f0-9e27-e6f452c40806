import {createFileRoute, useNavigate} from '@tanstack/react-router'
import logo from "@/src/assets/logo_blue_dot.svg"
import scheduler_onboarding_complete from "@/src/assets/scheduler_onboarding_complete.svg"
import React from "react";
import {Text} from "@/src/components/Text.tsx";
import {Button} from "@/src/components/ui/button.tsx";

export const Route = createFileRoute('/_signedin/$businessId/$storeId/onboarding/complete')({
  component: StoreOnboardingComplete
})

function StoreOnboardingComplete() {
  const {storeId} = Route.useParams();
  const navigate = useNavigate();

  const onContinue = () => {
    navigate({
      from: Route.fullPath,
      to: '../..' // to the dashboard
    });
  }

  return <div className={"flex flex-col gap-8 justify-center items-center pt-32"}>
    <div className={"flex-1 flex flex-col gap-8 justify-center items-center"}>
      <img src={logo} alt="logo" width={60}/>
      <img src={scheduler_onboarding_complete} alt="logo" width={300}/>
      <div className={"flex flex-col gap-2 justify-center items-center"}>
        <Text size={"xl"}>Success!</Text>
        <Text muted>Continue to the Dashboard!</Text>
      </div>
      <Button style={{width: 200}} onClick={onContinue}>Let's go!</Button>
    </div>
  </div>
}
