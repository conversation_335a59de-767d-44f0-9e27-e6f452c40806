import {createFileRoute, redirect, useNavigate} from '@tanstack/react-router'
import logo from "@/src/assets/logo_blue_dot.svg"
import nation_gray from "@/src/assets/nation_solid_gray.svg"
import scheduler_onboarding_lets_start from "@/src/assets/scheduler_onboarding_lets_start.svg"
import React from "react";
import {Text} from "@/src/components/Text.tsx";
import {Button} from "@/src/components/ui/button.tsx";

export const Route = createFileRoute('/_signedin/$businessId/$storeId/onboarding/')({
  component: StoreOnboarding,
  loader: async ({context, params}) => {
    const store = await context.clientUtils.user.getStoreAdmin.ensureData({storeId: params.storeId});
    if (store.isSchedulingOnboarded) {
      throw redirect({
        from: Route.fullPath,
        to: `..`,
      });
    }
  }
})

function StoreOnboarding() {
  const {storeId} = Route.useParams();
  const navigate = useNavigate();

  const onContinue = () => {
    navigate({
      from: Route.fullPath,
      to: './requiredBreaks'
    })
  }

  return <div className={"flex flex-col gap-8 justify-center items-center h-full pt-32"}>
    <div className={"flex-1 flex flex-col gap-8 justify-center items-center"}>
      <img src={logo} alt="logo" width={60}/>
      <img src={nation_gray} alt="logo" width={200}/>
      <div className={"flex flex-col gap-2 justify-center items-center"}>
        <Text size="xl">Welcome to the Schedule Builder!</Text>
        <Text muted>Answer a few questions to begin setting up your schedule.</Text>
      </div>
      <img src={scheduler_onboarding_lets_start} alt="logo" width={300}/>
    </div>
    <div className={"py-14 flex flex-row gap-6"}>
      <Button style={{width: 200}} onClick={onContinue}>Let's Start!</Button>
    </div>
  </div>
}
