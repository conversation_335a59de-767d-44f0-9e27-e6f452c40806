// requiredBreaks.tsx
import React from "react";
import {createFileRoute, useNavigate} from '@tanstack/react-router';
import logo from "@/src/assets/logo_blue_dot.svg";
import {Text} from "@/src/components/Text.tsx";
import {Button} from "@/src/components/ui/button.tsx";
import {PipRow} from "@/src/components/PipRow";
import {BreakRulesForm} from "@/src/components/BreakRulesForm.tsx";

export const Route = createFileRoute('/_signedin/$businessId/$storeId/onboarding/requiredBreaks')({
  component: RequiredBreaks
});

function RequiredBreaks() {
  const { storeId } = Route.useParams();
  const navigate = useNavigate();

  const onBack = () => {
    navigate({
      from: Route.fullPath,
      to: '..'
    });
  };

  const onContinue = () => {
    navigate({
      from: Route.fullPath,
      to: '../laborLaws',
    });
  };

  const [isLoading, setIsLoading] = React.useState(false);
  const formRef = React.useRef<{ submit: () => Promise<void> }>(null);
  const handleSaveAndContinue = async () => {
    if (formRef.current) {
      await formRef.current.submit();
    }
  }

  return (
    <div className={"flex flex-col gap-8 justify-center items-center pt-32"}>
      <div className={"flex-1 flex flex-col gap-8 justify-center items-center"}>
        <img src={logo} alt="logo" width={60} />
        <Text muted>1 / 3</Text>
        <div className={"flex flex-col justify-center items-center"}>
          <Text size="xl" className={"mb-2"}>Define your Required Breaks</Text>
          <Text muted className={""}>Confirm and setup your store's required team member breaks.</Text>
          <Text muted className={""}>Below are recommended breaks based on your specified location.</Text>
        </div>

        <BreakRulesForm ref={formRef}
                        storeId={storeId}
                        setIsLoading={setIsLoading}
                        onContinue={onContinue}/>
      </div>

      <div className={"flex flex-col gap-6 justify-center items-center py-14 "}>
        <div className="flex flex-row gap-6">
          <Button style={{width: 200}} onClick={onBack} variant="outline">Back</Button>
          <Button style={{width: 200}} onClick={handleSaveAndContinue} isLoading={isLoading}>Save & Continue</Button>
        </div>
        <PipRow numSteps={2} activeStep={0}/>
        <Text muted size={"xs"}>The Nation app is not responsible for ensuring compliance with federal, state, or
          local laws.</Text>
      </div>
    </div>
  );
}
