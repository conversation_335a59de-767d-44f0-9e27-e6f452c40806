import React from "react";
import {createFileRoute, useNavigate} from '@tanstack/react-router';
import {api} from "@/src/api.ts";
import logo from "@/src/assets/logo_blue_dot.svg";
import {Text} from "@/src/components/Text.tsx";
import {Button} from "@/src/components/ui/button.tsx";
import {PipRow} from "@/src/components/PipRow";
import {LaborLawsForm} from "@/src/components/LaborLawsForm.tsx";

export const Route = createFileRoute('/_signedin/$businessId/$storeId/onboarding/laborLaws')({
  component: LaborLaws
});

function LaborLaws() {
  const {storeId} = Route.useParams();
  const navigate = useNavigate();

  const apiUtils = api.useUtils();
  const setStoreSchedulingOnboarded = api.user.setStoreSchedulingOnboarded.useMutation();

  const onBack = () => {
    navigate({
      from: Route.fullPath,
      to: '../requiredBreaks'
    });
  }
  const onContinue = async () => {
    await setStoreSchedulingOnboarded.mutateAsync({
      storeId: storeId!
    });
    await apiUtils.user.getStoreAdmin.refetch();
    navigate({
      from: Route.fullPath,
      to: '../complete'
    });
  }

  const [isLoading, setIsLoading] = React.useState(false);
  const formRef = React.useRef<{ submit: () => Promise<void> }>(null);
  const handleSaveAndContinue = async () => {
    if (formRef.current) {
      await formRef.current.submit();
    }
  }

  return (
    <form onSubmit={async (e) => {
      e.preventDefault()
      e.stopPropagation()
      await handleSaveAndContinue()
    }} className="flex flex-col gap-8 justify-center items-center pt-32">
      <div className="flex-1 flex flex-col gap-8 justify-center items-center">
        <img src={logo} alt="logo" width={60}/>
        <Text muted>2 / 3</Text>
        <div className="flex flex-col justify-center items-center">
          <Text size="xl" className="mb-2">Define Labor Laws</Text>
          <Text muted>Confirm and setup your store's labor law restrictions.</Text>
          <Text muted>Below are recommended settings based on your specified location.</Text>
        </div>

        <LaborLawsForm ref={formRef}
                       storeId={storeId}
                       setIsLoading={setIsLoading}
                       onContinue={onContinue}/>
      </div>

      <div className="flex flex-col gap-6 justify-center items-center py-14">
        <div className="flex flex-row gap-6">
          <Button style={{width: 200}} onClick={onBack} variant="outline">Back</Button>
          <Button style={{width: 200}} type={"submit"} isLoading={isLoading || setStoreSchedulingOnboarded.isPending}>Save & Continue</Button>
        </div>
        <PipRow numSteps={2} activeStep={1}/>
        <Text muted size="xs">
          The Nation app is not responsible for ensuring compliance with federal, state, or local laws.
        </Text>
      </div>
    </form>
  );
}
