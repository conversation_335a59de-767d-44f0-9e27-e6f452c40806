import { createFileRoute, Outlet } from "@tanstack/react-router";
import { StyledNavLink } from "@/src/components/NavLink.tsx";
import React from "react";

export const Route = createFileRoute("/_signedin/$businessId/$storeId/_nav/education")({
  component: RouteComponent,
});

function RouteComponent() {
  return (
    <>
      <div className="w-full min-h-screen bg-gray-50 p-6 flex flex-col gap-4 flex-1">
        <div className="rounded-xl border border-gray-200 bg-white p-4 w-full flex flex-row justify-between">
          <div className="flex flex-row flex-wrap justify-start gap-4">
            <StyledNavLink
              from={Route.fullPath}
              urlMatch={new RegExp(`/education`)}
              to={`/$businessId/$storeId/education/`}
            >
              <span className="text-sm whitespace-nowrap overflow-hidden transition-all duration-200">Education</span>
            </StyledNavLink>
          </div>
        </div>
        <Outlet />
      </div>
    </>
  );
}
