import {createFileRoute} from '@tanstack/react-router'
import React, { Suspense, useState } from "react";
import {api} from '@/src/api'
import {Spinner} from '@/src/components/Spinner'
import {<PERSON><PERSON>, buttonVariants} from '@/src/components/ui/button'

import {toast} from 'sonner'
import {MoreVertical} from 'lucide-react'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/src/components/ui/dropdown-menu'
import {Checklist2ItemsTabContainer} from '@/src/components/checklists2/Checklist2ItemsTabContainer'
import {Link} from "@/src/components/Link.tsx"
import {TextInlineEdit} from "@/src/components/TextInlineEdit"
import {TextAreaInlineEdit} from "@/src/components/TextAreaInlineEdit"
import {BackButton} from "@/src/components/BackButton.tsx";
import {genChecklistItemId} from "../../../../../../../../../../api/src/checklist.schemas.ts";
import {ChecklistTagsInlineEdit} from "@/src/components/ChecklistTagsInlineEdit.tsx";
import { ChecklistTagsManagementModal } from "@/src/components/ChecklistTagsManagementModal.tsx";
import {
  ChecklistTemplateDto
} from "../../../../../../../../../../api/src/checklists/checklistTemplate/checklistTemplateDtos.ts";

export const Route = createFileRoute(
  '/_signedin/$businessId/$storeId/_nav/checklists/templates/$templateId/',
)({
  component: RouteComponent,
})

function ChecklistTemplatesScreenComp() {
  const { storeId, templateId } = Route.useParams()
  const navigate = Route.useNavigate()

  const [{ template: checklist }] = api.checklist2.getChecklistTemplate.useSuspenseQuery({
    storeId,
    checklistTemplateId: templateId,
  })

  // Cache the template so we can see the changes immediately
  const [template, setTemplate] = useState<ChecklistTemplateDto>(checklist)

  const [isOpenChecklistTagEditorModal, setIsOpenChecklistTagEditorModal] = useState(false);
  const [{ tags }] = api.checklist2.getChecklistTags.useSuspenseQuery({storeId});

  const apiUtils = api.useUtils()
  const archiveChecklist = api.checklist2.archiveChecklistTemplate.useMutation()
  const duplicateChecklistTemplate = api.checklist2.duplicateChecklistTemplate.useMutation()
  const update = api.checklist2.updateChecklistTemplate.useMutation()

  const handleTitleChange = (newTitle: string) => {
    saveChanges(newTitle, checklist.description || "", checklist.tags)
  }

  const handleDescriptionChange = (newDescription: string) => {
    saveChanges(checklist.title, newDescription, checklist.tags)
  }

  const handleTagsChange = (newTags: string[]) => {
    saveChanges(checklist.title, checklist.description || "", newTags)
  }

  const saveChanges = (newTitle: string, newDescription: string, newTags: string[]) => {
    setTemplate({
      ...checklist,
      title: newTitle,
      description: newDescription,
      tags: newTags
    })
    update.mutate({
      checklistTemplateId: templateId,
      title: newTitle,
      description: newDescription,
      tags: newTags
    }, {
      onSuccess: async () => {
        await apiUtils.checklist2.invalidate()
        toast.success('Checklist template updated')
      },
      onError: (error) => {
        setTemplate(checklist)
        toast.error(`Error: ${error.message}`)
      }
    })
  }

  const onArchiveChecklist = () => {
    if (confirm('Are you sure you want to archive this template? It will no longer show up in the list of checklist templates.')) {
      archiveChecklist.mutate({
        storeId,
        checklistTemplateId: templateId
      }, {
        onSuccess() {
          apiUtils.checklist2.invalidate()
          toast.success('Checklist template archived')
          navigate({ to: '..' })
        },
        onError(e) {
          toast.error(`Error: ${e.message}`)
        }
      })
    }
  }

  const onDuplicateTemplate = () => {
    duplicateChecklistTemplate.mutate({
      storeId,
      checklistTemplateId: templateId,
    }, {
      onSuccess({ newTemplateId }) {
        apiUtils.checklist2.invalidate()
        toast.success('Checklist template duplicated')
        navigate({
          to: '../$templateId',
          params: {
            templateId: newTemplateId,
          }
        })
      },
      onError(e) {
        toast.error(`Error: ${e.message}`)
      }
    })
  }

  return (
    <div>
      <div className="px-6 pt-6">
        <BackButton/>

        <div className="flex flex-wrap gap-3 justify-between items-center">
          <div className="flex-1">
            <TextInlineEdit
              value={template.title}
              onChange={handleTitleChange}
              placeholder="Enter checklist title"
              className="text-2xl font-semibold"
              displayClassName="font-semibold text-2xl py-1"
              inputClassName="text-2xl font-semibold"
            />
          </div>
          <div className="flex items-center gap-2">
            <Link
              from={Route.fullPath}
              to={`use`}
              className={buttonVariants({ variant: "default" })}
            >
              Use Template
            </Link>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant={"secondary"} size="icon">
                  <MoreVertical className="h-5 w-5" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem onClick={onDuplicateTemplate}>
                  Duplicate Template
                </DropdownMenuItem>
                <DropdownMenuItem onClick={onArchiveChecklist} className="text-red-600">
                  Archive Template
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>

        <div className="mt-2 mb-3">
          <TextAreaInlineEdit
            value={template.description || ""}
            onChange={handleDescriptionChange}
            placeholder="Add a description..."
            className="w-full text-gray-600"
            displayClassName="text-gray-600 min-h-[24px]"
            rows={2}
            minHeight={60}
          />
        </div>

        <div className="mt-2 mb-3">
          <ChecklistTagsInlineEdit
            activeTags={template.tags}
            allTags={tags}
            onChange={handleTagsChange}
            placeholder="Add tags..."
            handleConfigureTags={() => setIsOpenChecklistTagEditorModal(true)}
          />
        </div>
        <hr/>

      </div>


      <div className="p-6">
        <Checklist2ItemsTabContainer
          checklist={checklist}
          addItemHref={() => navigate({
            to: 'item/$itemId',
            params: {
              itemId: genChecklistItemId(),
            }
          })}
          editItemHref={(itemId) => navigate({
            to: 'item/$itemId',
            params: {
              itemId,
            }
          })}
          checklistId={undefined}
          recurrenceId={undefined}
          checklistTemplateId={checklist.id}
          storeId={storeId}
        />
      </div>

      <ChecklistTagsManagementModal
        isOpen={isOpenChecklistTagEditorModal}
        onOpenChange={setIsOpenChecklistTagEditorModal}
        storeId={storeId}
      />
    </div>
  )
}

function RouteComponent() {
  return (
    <Suspense fallback={<div className="flex justify-center items-center h-64"><Spinner /></div>}>
      <ChecklistTemplatesScreenComp />
    </Suspense>
  )
}
