import {createFileRoute, Outlet, useRouterState, <PERSON>} from "@tanstack/react-router";
import {cn} from "@/src/util.ts";
import React from "react";

import {api} from "@/src/api.ts";
import { map } from 'lodash';
import {hasPermissionPackage} from "../../../../../../../api/src/authorization.util.ts";

export const Route = createFileRoute("/_signedin/$businessId/$storeId/_nav/training")({
  component: RouteComponent,
});

function RouteComponent() {
  const {businessId, storeId} = Route.useParams();
  const routerState = useRouterState();
  const pathname = routerState.location.pathname;
  const [user] = api.user.getUserProfile.useSuspenseQuery(undefined, {
    staleTime: 1000 * 60 * 15 // 15 minutes
  });

  // Get feature flags and permissions
  const [features] = api.feature.getFeatures.useSuspenseQuery();
  const isTrainingInsightsFeatureEnabled = features["trainingInsights"];
  const hasViewTrainingInsightsPermissions = hasPermissionPackage(user, "viewTraining");

  // Build navigation items array like in hr.tsx
  const navItems: Array<{label: string, to: string, pattern: RegExp}> = [];

  if (isTrainingInsightsFeatureEnabled && hasViewTrainingInsightsPermissions) {
    navItems.push({
      label: "Insights",
      to: `/${businessId}/${storeId}/training/insights`,
      pattern: /\/training\/insights\/?/
    });
  }

  return (
    <>
      <div className="w-full min-h-screen bg-gray-50 p-6 flex flex-col gap-4 flex-1">
        <div className="rounded-xl border border-gray-200 bg-white p-4 w-full flex flex-row justify-between">
          <div className="flex flex-row flex-wrap justify-start gap-4">
            {map(navItems, (item) => {
              const isActive = item.pattern.test(pathname);
              return (
                <Link
                  key={item.label}
                  to={item.to}
                  className={cn(
                    "px-3 py-2 text-sm font-medium rounded-md transition-colors whitespace-nowrap overflow-hidden transition-all duration-200",
                    isActive
                      ? "bg-blue-100 text-blue-700"
                      : "text-gray-600 hover:text-gray-900 hover:bg-gray-100"
                  )}
                >
                  {item.label}
                </Link>
              );
            })}
          </div>
        </div>
        <Outlet />
      </div>
    </>
  );
}
