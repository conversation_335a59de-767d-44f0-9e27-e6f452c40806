import {createFileRoute, Outlet, useRouterState, <PERSON>} from '@tanstack/react-router'
import {cn} from "@/src/util.ts";
import {Button} from "@/src/components/ui/button.tsx";
import {ArrowUpFromLine} from "lucide-react";
import {useDisclosure} from "@/src/hooks/useDisclosure.tsx";
import {TimePunchUploadModal} from "@/src/components/TimePunchUploadModal.tsx";
import { map } from 'lodash';
import {useFeatureFlag} from "@/src/hooks/useFeatureFlag.tsx";
import {api} from "@/src/api.ts";

export const Route = createFileRoute('/_signedin/$businessId/$storeId/_nav/timePunch')({
  component: TimePunchLayout
})

function TimePunchLayout() {
  const {businessId, storeId} = Route.useParams();
  const routerState = useRouterState();
  const pathname = routerState.location.pathname;
  const searchParams = routerState.location.search;
  const uploadModal = useDisclosure();
  const apiUtils = api.useUtils();

  // Handle successful upload by invalidating queries based on current page
  const handleUploadSuccess = async () => {
    if (pathname.includes('/punches')) {
      // Invalidate punches page queries
      await apiUtils.user.getTimePunchEntries.invalidate();
    } else if (pathname.includes('/variance') && !pathname.includes('/teamMemberVariance')) {
      // Invalidate variance page queries (but not team member variance)
      await apiUtils.user.getTimePunchVarianceEntries.invalidate();
    } else if (pathname.includes('/teamTotals')) {
      // Invalidate team totals page queries
      await apiUtils.user.getTeamTotalsVarianceEntries.invalidate();
    } else if (pathname.includes('/teamMemberVariance')) {
      // Invalidate team member variance page queries
      await apiUtils.user.getTimePunchVarianceEntries.invalidate();
    }
  };

  // Extract only the date filter parameters that are compatible with time punch routes
  const getDateFilterParams = () => {
    // Only include date filter params if they exist and are the right type
    const params: any = {};

    if (typeof searchParams?.week === 'number') {
      params.week = searchParams.week;
    }
    if (typeof searchParams?.year === 'number') {
      params.year = searchParams.year;
    }
    if (typeof searchParams?.day === 'number') {
      params.day = searchParams.day;
    }
    if (typeof searchParams?.month === 'number') {
      params.month = searchParams.month;
    }
    if (searchParams?.timeFrame && ['day', 'week', 'month', 'custom'].includes(searchParams.timeFrame)) {
      params.timeFrame = searchParams.timeFrame;
    }
    if (searchParams?.startDate) {
      params.startDate = searchParams.startDate;
    }
    if (searchParams?.endDate) {
      params.endDate = searchParams.endDate;
    }

    return params;
  };

  // Define navigation items
  const navItems = [
    {
      label: "Punches",
      to: `/${businessId}/${storeId}/timePunch/punches`,
      pattern: /\/punches\/?$/
    },
    {
      label: "Variance",
      to: `/${businessId}/${storeId}/timePunch/variance`,
      pattern: /\/variance\/?$/
    },
    {
      label: "Team Totals",
      to: `/${businessId}/${storeId}/timePunch/teamTotals`,
      pattern: /\/teamTotals\/?$/
    }
  ];

  return (
    <>
      <div className="w-full min-h-screen bg-gray-50 p-6 flex flex-col gap-4 flex-1">
        <div className="rounded-xl border border-gray-200 bg-white p-4 w-full flex flex-row justify-between">
          <div className="flex flex-row flex-wrap justify-start gap-4">
            {map(navItems, (item) => {
              const isActive = item.pattern.test(pathname);
              return (
                <Link
                  key={item.label}
                  to={item.to}
                  search={getDateFilterParams()} // Preserve only compatible date filter parameters
                  className={cn(
                    "px-3 py-2 text-sm font-medium rounded-md transition-colors whitespace-nowrap overflow-hidden transition-all duration-200",
                    isActive
                      ? "bg-blue-100 text-blue-700"
                      : "text-gray-600 hover:text-gray-900 hover:bg-gray-100"
                  )}
                >
                  {item.label}
                </Link>
              );
            })}
          </div>

          {/* Upload Button - show on punches, variance, team totals, and team member variance pages */}
          {(pathname.includes('/punches') || pathname.includes('/variance') || pathname.includes('/teamTotals') || pathname.includes('/teamMemberVariance')) && (
            <Button
              onClick={uploadModal.onOpen}
              variant="outline"
              size="sm"
              className="flex items-center gap-2 text-sm font-medium"
            >
              <ArrowUpFromLine size={16} />
              Upload Time Punch Data
            </Button>
          )}
        </div>
        <Outlet />
      </div>

      {/* Upload Modal */}
      <TimePunchUploadModal
        isOpen={uploadModal.isOpen}
        onClose={uploadModal.onClose}
        storeId={storeId}
        onSuccess={handleUploadSuccess}
      />
    </>
  );
}
