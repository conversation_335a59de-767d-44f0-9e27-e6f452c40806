import {createFileRoute, SearchSchemaInput} from '@tanstack/react-router'
import {Heading} from "@/src/components/Heading.tsx";
import React, {Suspense, useCallback, useEffect, useMemo, useState} from "react";
import {Spinner} from "@/src/components/Spinner.tsx";
import {api} from "@/src/api.ts";
import {Input} from '@/src/components/ui/input';
import {SearchIcon} from 'lucide-react';
import {DataTable} from "@/src/components/ui/data-table.tsx";
import {
  ColumnFiltersState,
  createColumnHelper,
  getCoreRowModel,
  getSortedRowModel,
  SortingState,
  useReactTable
} from "@tanstack/react-table";
import {map, orderBy} from "lodash";
import {Badge} from "@/src/components/ui/badge.tsx";
import {SelectChecklistTags} from "@/src/components/form/FormSelectChecklistTags";
import {ChecklistTemplatePreviewModal} from "@/src/components/ChecklistTemplatePreviewModal";
import {z} from 'zod';
import {
  AnonymousChecklistTemplateDto,
} from "../../../../../../../../../api/src/checklists/checklistTemplate/checklistTemplateDtos.ts";
import {Text} from "@/src/components/Text.tsx";
import {toast} from "sonner";
import {Button} from "@/src/components/ui/button.tsx";
import {useBack} from "@/src/components/BackButton.tsx";

const townSquareSearchSchema = z.object({
  tags: z.array(z.string()).optional(),
  pageIndex: z.coerce.number().optional(),
  pageSize: z.coerce.number().optional(),
  filter: z.string().optional(),
});

type TownSquareSearch = z.infer<typeof townSquareSearchSchema>;

export const Route = createFileRoute(
        '/_signedin/$businessId/$storeId/_nav/checklists/town-square/',
)({
  component: RouteComponent,
  validateSearch: (search: unknown & SearchSchemaInput): TownSquareSearch | undefined => {
    if (townSquareSearchSchema.safeParse(search).success) {
      return search as TownSquareSearch
    }
  }
})

const colHelper = createColumnHelper<AnonymousChecklistTemplateDto>();

const columns = [
  colHelper.accessor("title", {
    header: 'Title',
    cell: info => info.getValue(),
  }),
  colHelper.accessor("tags", {
    header: 'Tags',
    cell: info => <div className={"flex flex-wrap gap-1"}>
      {map(info.getValue(), tag => <Badge key={tag} colorScheme={"default"} size={"sm"}>
        {tag}
      </Badge>)}
    </div>,
  }),
  colHelper.accessor("description", {
    header: 'Description',
    cell: info => info.getValue(),
  }),
  colHelper.accessor("items", {
    header: 'Items',
    cell: info => info.getValue().length,
  }),
];

function RouteComponent() {
  const {storeId} = Route.useParams();
  const searchParams = Route.useSearch();
  const navigate = Route.useNavigate();
  const [sorting, setSorting] = useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);

  // Use local state for immediate UI updates
  const [localTagFilters, setLocalTagFilters] = useState<string[]>(searchParams?.tags || []);
  const [localGlobalFilter, setLocalGlobalFilter] = useState<string>(searchParams?.filter || "");
  const [debouncedGlobalFilter, setDebouncedGlobalFilter] = useState<string>(searchParams?.filter || "");

  // Sync local state with URL params when they change externally
  useEffect(() => {
    setLocalTagFilters(searchParams?.tags || []);
  }, [searchParams?.tags]);

  useEffect(() => {
    setLocalGlobalFilter(searchParams?.filter || "");
  }, [searchParams?.filter]);

  // Modal state
  const [selectedTemplate, setSelectedTemplate] = useState<AnonymousChecklistTemplateDto | null>(null);
  const [isPreviewModalOpen, setIsPreviewModalOpen] = useState(false);

  const onBack = useBack();

  // Debounce the global filter
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedGlobalFilter(localGlobalFilter);
    }, 500);

    return () => clearTimeout(timer);
  }, [localGlobalFilter]);

  // Pagination state
  const pageIndex = searchParams?.pageIndex || 0;
  const pageSize = searchParams?.pageSize || 10;

  const [{tags}] = api.checklist2.getTagsAcrossAllBusinesses.useSuspenseQuery({ownStoreId: storeId});
  const tagsOrdered = useMemo(() => {
    return orderBy(tags, tag => tag.toLowerCase());
  }, [tags]);

  // Memoize query parameters to prevent unnecessary re-renders
  const queryParams = useMemo(() => ({
    filter: debouncedGlobalFilter || undefined,
    pageIndex,
    pageSize,
    tags: localTagFilters.length > 0 ? localTagFilters : undefined,
    ownStoreId: storeId,
  }), [debouncedGlobalFilter, pageIndex, pageSize, localTagFilters, storeId]);

  const [{templates, pageCount}] = api.checklist2.getChecklistTemplatesAcrossAllBusinesses.useSuspenseQuery(queryParams);

  const copyChecklistTemplateToStore = api.checklist2.copyChecklistTemplateToStore.useMutation();

  const handleTagFiltersChange = useCallback((newTagFilters: string[]) => {
    // Update local state immediately for instant UI feedback
    setLocalTagFilters(newTagFilters);
  }, []);

  // Debounce URL updates for tag filters
  useEffect(() => {
    const timer = setTimeout(() => {
      const currentUrlTags = searchParams?.tags || [];
      const tagsChanged = JSON.stringify(currentUrlTags.sort()) !== JSON.stringify(localTagFilters.sort());

      if (tagsChanged) {
        navigate({
          search: (prev) => ({
            ...prev,
            tags: localTagFilters.length ? localTagFilters : undefined,
            pageIndex: 0
          }),
          replace: true
        });
      }
    }, 300); // Short delay to batch rapid changes

    return () => clearTimeout(timer);
  }, [localTagFilters, navigate, searchParams]);

  const handleGlobalFilterChange = useCallback((newFilter: string) => {
    setLocalGlobalFilter(newFilter);
  }, []);

  // Update URL when debounced filter changes
  useEffect(() => {
    const currentUrlFilter = searchParams?.filter || "";
    const filterChanged = currentUrlFilter !== debouncedGlobalFilter;

    // Only navigate if filter actually changed from what's in the URL
    if (filterChanged) {
      navigate({
        search: (prev) => ({
          ...prev,
          filter: debouncedGlobalFilter || undefined,
          pageIndex: 0 // Reset to first page when searching
        }),
        replace: true
      });
    }
  }, [debouncedGlobalFilter, navigate, searchParams]);

  const handlePaginationChange = useCallback((newPageIndex: number, newPageSize: number) => {
    navigate({
      search: (prev) => ({
        ...prev,
        pageIndex: newPageIndex,
        pageSize: newPageSize
      }),
      replace: true
    });
  }, [navigate]);

  const handleRowClick = useCallback((template: AnonymousChecklistTemplateDto) => {
    setSelectedTemplate(template);
    setIsPreviewModalOpen(true);
  }, []);

  const handleCloseModal = useCallback(() => {
    setIsPreviewModalOpen(false);
    setSelectedTemplate(null);
  }, []);

  const handleCopyTemplate = useCallback((templateId: string) => {
    const confirmed = confirm("Are you sure you want to copy this template to your store?");
    if (!confirmed) {
      return;
    }

    copyChecklistTemplateToStore.mutate({
      templateId,
      to: {
        storeId,
      },
    }, {
      onSuccess: () => {
        toast.success("Template successfully copied to your store", {
          position: "top-center",
        });
        handleCloseModal();
      },
      onError: (error) => {
        alert("Error copying template: " + error.message);
      }
    });
  }, [])

  const table = useReactTable({
    data: templates || [],
    columns,
    getCoreRowModel: getCoreRowModel(),
    manualPagination: true,
    manualFiltering: true,
    onSortingChange: setSorting,
    getSortedRowModel: getSortedRowModel(),
    onColumnFiltersChange: setColumnFilters,
    onGlobalFilterChange: handleGlobalFilterChange,
    state: {
      sorting,
      columnFilters,
      globalFilter: localGlobalFilter,
      pagination: {
        pageIndex,
        pageSize,
      },
    },
    pageCount,
    onPaginationChange: (updater) => {
      const newPagination = typeof updater === 'function'
              ? updater({pageIndex, pageSize})
              : updater;
      handlePaginationChange(newPagination.pageIndex, newPagination.pageSize);
    },
  });

  return (
          <div className={"p-6"}>
            <div className={"flex flex-row justify-between items-center"}>
              <Heading level={1} size={"md"} className={"m-0 mb-8 grow"}>Checklist Town Square</Heading>
              <Button variant={"outline"} onClick={() => onBack()}>
                Back
              </Button>
            </div>

            <Suspense fallback={<Spinner size={"lg"}/>}>
              <section>
                <div className="pb-4 flex flex-col justify-start gap-3">
                  <div className={"w-96"}>
                    <Input type={"search"}
                           leftIcon={SearchIcon}
                           placeholder="Search templates by title or description..."
                           value={localGlobalFilter}
                           onChange={(event) => handleGlobalFilterChange(event.target.value)}
                    />
                  </div>
                  <SelectChecklistTags
                          selectedTags={localTagFilters}
                          onTagsChange={handleTagFiltersChange}
                          allTags={tagsOrdered}
                  />
                </div>

                <DataTable<AnonymousChecklistTemplateDto>
                        table={table}
                        EmptyComponent={<Text>No templates found</Text>}
                        rowDetailLinkFrom={Route.fullPath}
                        onRowClicked={({original}) => {
                          handleRowClick(original)
                        }}
                />
              </section>
            </Suspense>

            <ChecklistTemplatePreviewModal
                    templateId={selectedTemplate?.id || null}
                    isOpen={isPreviewModalOpen}
                    onClose={handleCloseModal}
                    storeId={storeId}
                    onCopyTemplate={handleCopyTemplate}
                    isCopyPending={copyChecklistTemplateToStore.isPending}
            />
          </div>
  )
}
