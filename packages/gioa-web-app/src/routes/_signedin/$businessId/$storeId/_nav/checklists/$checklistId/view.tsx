import {createFile<PERSON>out<PERSON>, Link, SearchSchemaInput} from '@tanstack/react-router'
import React, {Suspense, useState} from 'react'
import {api} from '@/src/api'
import {find, map} from 'lodash'
import {DateTime} from 'luxon'
import {<PERSON><PERSON>, buttonVariants} from '@/src/components/ui/button'
import {Text} from '@/src/components/Text'
import {Spinner} from '@/src/components/Spinner'
import {ChecklistItemAccordion} from '@/src/components/checklists2/ChecklistItemAccordion'
import {Checklist2AssignmentFullDisplay} from '@/src/components/checklists2/Checklist2AssignmentFullDisplay'
import {useChecklist2Info} from '@/src/hooks/useChecklist2Info'
import {ArchiveIcon, ChevronsUpDownIcon, EditIcon} from 'lucide-react'
import {z} from 'zod'
import {Helmet} from "react-helmet";
import {Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle,} from '@/src/components/ui/dialog';
import {useForm} from '@tanstack/react-form';
import {zodValidator} from '@tanstack/zod-form-adapter';
import {Label} from '@/src/components/ui/label';
import {FormTextarea} from '@/src/components/form/FormTextarea';
import {FieldInfo} from '@/src/components/form/FieldInfo';
import {FormControl} from '@/src/components/form/FormControl';
import {toast} from 'sonner';
import {BackButton} from "@/src/components/BackButton.tsx";
import {PersonAvatar} from "@/src/components/PersonAvatar.tsx";

const checklistViewSearchSchema = z.object({
  recurrenceId: z.string().optional(),
  defaultExpanded: z.boolean().optional()
}).optional();

type ChecklistViewSearch = z.infer<typeof checklistViewSearchSchema>;

export const Route = createFileRoute(
  '/_signedin/$businessId/$storeId/_nav/checklists/$checklistId/view',
)({
  component: RouteComponent,
  validateSearch: (search: unknown & SearchSchemaInput): ChecklistViewSearch | undefined => {
    if (checklistViewSearchSchema.safeParse(search).success) {
      return search as any;
    }
  },
})

function ViewChecklistScreenComp() {
  const {businessId, storeId, checklistId} = Route.useParams()
  const searchParams = Route.useSearch()
  const recurrenceIdStr = searchParams?.recurrenceId
  const defaultExpanded = searchParams?.defaultExpanded ?? false
  const recurrenceId = recurrenceIdStr ? new Date(recurrenceIdStr) : undefined
  const [isArchiveDialogOpen, setIsArchiveDialogOpen] = useState(false)

  const [store] = api.user.getStore.useSuspenseQuery({storeId}, {
    staleTime: 1000 * 60 * 60 // 1 hour
  })

  const [{
    checklist,
    canEdit,
    canAssign,
    canArchive,
    timezone
  }] = api.checklist2.getChecklist2.useSuspenseQuery({
    storeId,
    checklistId,
    recurrenceId
  }, {
    refetchInterval: store.checklistRefetchInterval
  })

  const [user] = api.user.getUserProfile.useSuspenseQuery(undefined, {staleTime: 1000 * 60 * 60}) // 1 hour
  const apiUtil = api.useUtils()
  const archiveChecklist = api.checklist2.archiveChecklist.useMutation()

  const people = store.employees
  const [expandedItemId, setExpandedItemId] = useState<string>()
  const [isAllExpanded, setIsAllExpanded] = useState(Boolean(defaultExpanded))

  const archiveForm = useForm({
    defaultValues: {
      reason: ""
    },
    validatorAdapter: zodValidator(),
    onSubmit: async ({value}) => {
      archiveChecklist.mutate({
        checklistId: checklistId,
        recurrenceId: recurrenceId,
        reason: value.reason
      }, {
        onSuccess: () => {
          apiUtil.checklist2.invalidate();
          toast.success("Checklist archived", {
            position: "top-center"
          });
          setIsArchiveDialogOpen(false);
        },
        onError: (error) => {
          toast.error(`Error: ${error.message}`, {
            position: "top-center"
          });
        }
      })
    },
  })

  const {
    dueStr,
  } = useChecklist2Info({timezone, timingStrOverride: undefined, checklist})

  const isChecklistExplicitTiming = "isExplicitTiming" in checklist && checklist.isExplicitTiming
  const isChecklistArchived = "isArchived" in checklist && checklist.isArchived
  const timingStr = checklist.start && checklist.end && (isChecklistExplicitTiming || isChecklistArchived)
    ? DateTime.fromJSDate(checklist.start, {zone: timezone}).toLocaleString(DateTime.DATETIME_MED_WITH_WEEKDAY) + " — " +
    DateTime.fromJSDate(checklist.end, {zone: timezone}).toLocaleString(DateTime.DATETIME_MED_WITH_WEEKDAY)
    : `Created ${DateTime.fromJSDate(checklist.start, {zone: timezone}).toLocaleString(DateTime.DATETIME_MED_WITH_WEEKDAY)}`

  const archivedByPerson = checklist.isArchived ? find(people, p => p.id === checklist.archivedByPersonId) : null

  const onToggleExpand = (itemId: string) => {
    setIsAllExpanded(false)
    setExpandedItemId(expandedItemId === itemId ? undefined : itemId)
  }

  const onCollapse = () => {
    setExpandedItemId(undefined)
  }

  return (
    <div className="bg-slate-100">
      <Helmet>
        <title>
          {checklist.title} - Nation
        </title>
      </Helmet>

      {/* Archive Dialog */}
      <Dialog open={isArchiveDialogOpen} onOpenChange={setIsArchiveDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Archive Checklist</DialogTitle>
            <DialogDescription>
              Are you sure you want to archive the {checklist.title} checklist? Archiving will remove it from the list of
              overdue checklists. It will still be visible in the record of past checklists. All data will be preserved.
            </DialogDescription>
          </DialogHeader>

          <div className="py-4">
            <archiveForm.Field name="reason"
                      validators={{
                        onSubmit: z.string().min(1, "Required")
                      }}
                      children={(field) => (
                        <FormControl>
                          <Label>Reason for archiving</Label>
                          <FormTextarea
                            field={field}
                            placeholder="Enter the reason why you are archiving this checklist..."
                          />
                          <Text size="sm" className="text-gray-500">
                            The reason will be saved in the record of past checklists.
                          </Text>
                          <FieldInfo field={field}/>
                        </FormControl>
                      )}/>
          </div>

          <div className="flex justify-end gap-2 mt-4">
            <Button variant="outline" onClick={() => setIsArchiveDialogOpen(false)}>Cancel</Button>
            <Button
              onClick={archiveForm.handleSubmit}
              disabled={archiveChecklist.isPending}
            >
              {archiveChecklist.isPending ? <Spinner className="mr-2" size="sm" /> : null}
              Archive
            </Button>
          </div>
        </DialogContent>
      </Dialog>

      <div className="px-6 pt-6 bg-white">
        <BackButton/>

        <div className={"flex flex-row gap-3 justify-between items-start"}>
          <div>
            <h1 className={"text-2xl font-semibold"}>
              {checklist.title}
            </h1>

            <p className="mt-2 mb-3 text-gray-600">
              {checklist.description}
            </p>
          </div>
          <div className="flex gap-2 items-center">
            <Button leftIcon={<ChevronsUpDownIcon size={16} className="text-gray-700"/>}
              onClick={() => setIsAllExpanded(o => !o)}
              size="sm"
              variant="outline"
              className="bg-white"
            >
              Expand All
            </Button>
            {canEdit ? <Link from={Route.fullPath}
                             to=".."
                             search={{ recurrenceId: recurrenceId }}
                             className={buttonVariants({variant: "outline", size: "sm"})}>
                <EditIcon size={16} className="text-gray-700 mr-2"/>
                <span>Edit</span>
              </Link>
              : canArchive
                  ? <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setIsArchiveDialogOpen(true)}
                      className="flex items-center gap-1">
                    <ArchiveIcon size={16} className="text-gray-700 mr-2"/>
                    <span>Archive</span>
                  </Button>
                  : null}
          </div>
        </div>
      </div>

      <div className="px-6 pt-4 pb-10 max-w-screen-md">

        <div className="flex flex-col gap-2">
          {map(checklist.items, (item) => (
            <ChecklistItemAccordion
              key={item.id}
              item={item}
              storeId={storeId}
              checklistId={checklistId}
              onCollapse={onCollapse}
              recurrenceId={recurrenceId}
              checklistVersion={checklist.version}
              isExpanded={expandedItemId === item.id || isAllExpanded}
              onToggleExpand={onToggleExpand}
            />
          ))}
        </div>

        <div className="mt-4 flex flex-col gap-6">
          <div>
            <Text semibold className="mb-1">
              Checklist Timing
            </Text>
            <Text>
              {timingStr} {dueStr}
            </Text>
          </div>

          <Checklist2AssignmentFullDisplay
            checklist={checklist}
            people={people}
            timezone={timezone}
            storeId={storeId}
            currentPersonId={user?.person.id!}
          />

          {checklist.isArchived && <div>
              <Text semibold className="mb-2">
                  Archived by
              </Text>
            {archivedByPerson ? <div className="flex flex-row items-center gap-2 mb-3">
              <div className="w-10 h-10 rounded-full bg-gray-200 overflow-hidden">
                <PersonAvatar person={archivedByPerson}/>
              </div>
              <div>
                <Text semibold size="sm">
                  {archivedByPerson.firstName} {archivedByPerson.lastName} {archivedByPerson.id === user?.id ? "(You)" : null}
                </Text>
                {checklist.archivedAt && (
                  <Text size="sm" muted>
                    {DateTime.fromJSDate(checklist.archivedAt, {zone: timezone}).toLocaleString(DateTime.DATETIME_FULL)}
                  </Text>
                )}
              </div>
            </div> : <Text>System</Text>}

            {checklist.archivedReason && (
              <Text>{checklist.archivedReason}</Text>
            )}
          </div>}
        </div>
      </div>
    </div>
  )
}

function RouteComponent() {
  return (
    <Suspense fallback={<div className="flex justify-center items-center h-64"><Spinner/></div>}>
      <ViewChecklistScreenComp/>
    </Suspense>
  )
}
