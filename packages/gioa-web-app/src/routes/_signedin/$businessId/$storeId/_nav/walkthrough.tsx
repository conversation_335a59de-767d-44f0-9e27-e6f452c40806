import {createFileRoute, Outlet} from "@tanstack/react-router";
import {includes, map} from "lodash";
import {Text} from "@/src/components/Text.tsx";
import {Heading} from "@/src/components/Heading.tsx";
import {CalendarDaysIcon, PhoneOutgoingIcon} from "lucide-react";
import {StyledNavLink} from "@/src/components/NavLink.tsx";
import {Button} from "@/src/components/ui/button.tsx";
import {SolidCheckCircleIcon} from "@/src/components/icons/SolidCheckCircleIcon.tsx";
import {useOnboarding} from "@/src/hooks/useOnboarding.tsx";
import React, {useMemo} from "react";
import {DateTime} from "luxon";
import {useForm} from "@tanstack/react-form";
import {DatePicker} from "@/src/components/DatePicker.tsx";
import {LoadingPage} from "@/src/components/LoadingPage.tsx";
import {api} from "@/src/api";
import {toast} from "sonner";

export const Route = createFileRoute("/_signedin/$businessId/$storeId/_nav/walkthrough")({
  component: RouteComponent,
});

function RouteComponent() {
  const {businessId, storeId} = Route.useParams();
  const navigate = Route.useNavigate();

  const apiUtil = api.useUtils();
  const {steps, onboardingStep, stepsCompleted, isLoading} = useOnboarding({storeId});
  const [store] = api.user.getStoreAdmin.useSuspenseQuery({storeId: storeId!}, {
    staleTime: 1000 * 60 * 60 // 1 hour
  });
  const updateStoreGoLiveDate = api.user.updateStoreGoLiveDate.useMutation({
    onSuccess: async () => {
      await apiUtil.user.getStoreAdmin.invalidate({storeId});
      resetForm();
      toast.success("Go live date updated successfully");
    },
    onError: () => {
      toast.error(`Failed to update go live date`);
    },
  });

  const goLiveDate = useMemo(() => {
    return store.goLiveDate ? DateTime.fromJSDate(store.goLiveDate) : DateTime.now().setZone(store.timezone ?? "America/New_York").plus({days: 30})
  }, [store]);

  const form = useForm({
    defaultValues: {
      goLiveDate,
    },
    onSubmit: async ({value}) => {
      updateStoreGoLiveDate.mutate({
        storeId,
        goLiveDate: value.goLiveDate.toJSDate(),
      });
    },
  })

  const resetForm = () => form.reset({goLiveDate});

  const {_goLiveDate, isDirty} = form.useStore((state) => ({
    _goLiveDate: state.values.goLiveDate,
    isDirty: state.isDirty
  }));
  const goLiveDateFormatted = _goLiveDate.toFormat("MMM d, yyyy");

  if (isLoading) {
    return <LoadingPage/>;
  }

  if (!steps.length) {
    return <div className="flex flex-row items-center justify-center h-full my-12">
      <Text>Onboarding has not yet been configured</Text>
    </div>;
  }

  return (
          <div className="bg-gray-50 p-6 flex flex-col gap-6 flex-1">
            <div className="rounded-xl border bg-white p-6 w-full flex flex-wrap gap-5">
              {map(steps, (step) => {
                const isCompleted = includes(stepsCompleted, step.key);

                const navToStep = (step: string) => navigate({
                  to: `/${businessId}/${storeId}/walkthrough/${step}`
                })

                return <div key={step.title}
                            className={"flex flex-col gap-1 border rounded-xl py-2 px-6  hover:cursor-pointer"}
                            onClick={() => navToStep(step.key)}>
                  <Text size={"sm"}>{step.title}</Text>
                  <div className={"flex flex-row gap-1 items-center"}>
                    <div className={isCompleted ? "text-green-600" : "text-gray-300"}>
                      <SolidCheckCircleIcon/>
                    </div>
                    <Text muted size={"sm"}>
                      {isCompleted ? "Complete" : "Incomplete"}
                    </Text>
                  </div>
                </div>
              })}
            </div>
            <div className="rounded-xl border bg-white p-6 w-full flex flex-col gap-6">
              <div className={"flex flex-row items-center justify-between"}>
                <Heading level={1} className={"text-primary-600 font-semibold"}>
                  Onboarding Hub
                </Heading>
                <div className={"flex flex-row items-center gap-2"}>
                  <form.Field name={"goLiveDate"}
                              children={field => (
                                      <DatePicker
                                              value={field.state.value}
                                              onChange={field.handleChange}
                                              today={DateTime.now()}
                                              labelFormat={{month: 'short', day: 'numeric', year: 'numeric'}}
                                              children={
                                                <div className={"border rounded py-2 px-3 flex flex-row items-center gap-2 hover:cursor-pointer"}>
                                                  <Text size={"sm"} muted>Go Live Date</Text>
                                                  <div className={"bg-gray-300"} style={{width: 1, height: 16}}/>
                                                  <div className={"flex flex-row items-center gap-2"}>
                                                    <Text size={"sm"} semibold>{goLiveDateFormatted}</Text>
                                                    <CalendarDaysIcon size={16} className={"text-gray-600"}/>
                                                  </div>
                                                </div>
                                              }
                                      />
                              )}
                  />
                  {isDirty ? <>
                    <Button variant={"link"} className={"p-1 text-primary-600"} onClick={form.handleSubmit}
                            isLoading={updateStoreGoLiveDate.isPending}>Save</Button>
                    <div className={"bg-gray-300"} style={{width: 1, height: 16}}/>
                    <Button variant={"link"} className={"p-1 text-red-600"} onClick={resetForm}
                            disabled={updateStoreGoLiveDate.isPending}>Cancel</Button>
                  </> : null}

                </div>
              </div>
              <div className="grid grid-cols-[200px_1fr]">
                <div className={"flex-1 flex flex-col border-r"}>
                  <div className="flex-1 flex flex-col gap-2 pr-4">
                    {onboardingStep ? <StyledNavLink
                            key={"onboarding"}
                            from={Route.fullPath}
                            urlMatch={new RegExp(`/${businessId}/${storeId}/walkthrough`)}
                            activeOptions={{exact: true}}
                            to={`/${businessId}/${storeId}/walkthrough`}>
                    <span className="text-sm whitespace-nowrap overflow-hidden transition-all duration-200">
                      {onboardingStep.title}
                    </span>
                    </StyledNavLink> : null}
                    {map(steps, (step) => {
                      return (
                              <StyledNavLink
                                      key={step.title}
                                      from={Route.fullPath}
                                      urlMatch={new RegExp(`walkthrough/${step.key}`)}
                                      to={`/${businessId}/${storeId}/walkthrough/${step.key}`}
                              >
                    <span className="text-sm whitespace-nowrap overflow-hidden transition-all duration-200">
                      {step.title}
                    </span>
                              </StyledNavLink>
                      );
                    })}
                  </div>
                  <div>
                    <a href={"https://calendly.com/d/cnqk-js7-twp"} target={"_blank"}>
                      <Button variant={"outline"} className={"mr-2 mt-16 px-2"}
                              rightIcon={<PhoneOutgoingIcon size={13}/>}>
                        <Text size={"xs"}>Schedule a Training Call</Text>
                      </Button>
                    </a>
                  </div>
                </div>
                <div className="max-w-2xl w-full pl-6">
                  <Outlet/>
                </div>
              </div>
            </div>
          </div>
  );
}
