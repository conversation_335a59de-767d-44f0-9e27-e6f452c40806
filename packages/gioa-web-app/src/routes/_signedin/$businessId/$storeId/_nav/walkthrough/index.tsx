import {createFileRoute} from "@tanstack/react-router";
import {Text} from "@/src/components/Text.tsx";
import {Heading} from "@/src/components/Heading.tsx";
import {Progress} from "@/src/components/ui/progress.tsx";
import {map, split} from "lodash";
import React from "react";
import MuxPlayer from '@mux/mux-player-react';
import {useOnboarding} from "@/src/hooks/useOnboarding.tsx";

export const Route = createFileRoute("/_signedin/$businessId/$storeId/_nav/walkthrough/")({
  component: RouteComponent,
});

function RouteComponent() {
  const {storeId} = Route.useParams();

  const {steps, onboardingStep, stepsCompleted, percentageCompleted} = useOnboarding({storeId});

  return (
          <div className="flex flex-col gap-4">
            <Heading level={3} size="xs" className={"font-semibold mb-0"}>
              Welcome to Nation
            </Heading>

            <div className={"flex flex-row gap-4 items-center mb-2"}>
              <Progress className={"grow"} value={percentageCompleted} max={100}/>
              <Text size={"sm"}>{stepsCompleted.length}/{steps.length}</Text>
            </div>

            {onboardingStep?.body ? (
                    <div className="prose max-w-none">
                      {map(split(onboardingStep.body, "\n\n"), (paragraph, index) => (
                              <Text size={"sm"} key={index} className={index < split(onboardingStep.body, "\n\n").length - 1 ? "mb-4" : ""}>
                                {paragraph}
                              </Text>
                      ))}
                    </div>
            ) : null}

            {onboardingStep?.videoPlaybackId ? (
                    <div className="aspect-video">
                      <MuxPlayer playbackId={onboardingStep.videoPlaybackId}/>;
                    </div>
            ) : null}
          </div>
  );
}
