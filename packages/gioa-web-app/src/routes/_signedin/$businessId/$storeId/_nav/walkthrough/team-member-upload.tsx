import {createFileRoute} from "@tanstack/react-router";
import {find, includes, map, split} from "lodash";
import {Heading} from "@/src/components/Heading.tsx";
import {Text} from "@/src/components/Text.tsx";
import {Button} from "@/src/components/ui/button.tsx";
import {api} from "@/src/api.ts";
import {toast} from "sonner";
import {AddTeamMemberDialog} from "@/src/components/AddTeamMemberDialog.tsx";
import React from "react";
import {PlusIcon} from "lucide-react";
import {hasPermissionPackage} from "../../../../../../../../api/src/permission.util.ts";
import MuxPlayer from '@mux/mux-player-react';
import {useOnboarding} from "@/src/hooks/useOnboarding.tsx";
import {LoadingPage} from "@/src/components/LoadingPage.tsx";

export const Route = createFileRoute("/_signedin/$businessId/$storeId/_nav/walkthrough/team-member-upload")({
  component: RouteComponent,
});

function RouteComponent() {
  const { storeId } = Route.useParams();
  const step = "team-member-upload";

  const apiUtil = api.useUtils();
  const [userProfile] = api.user.getUserProfile.useSuspenseQuery(undefined, {
    staleTime: 1000 * 60 * 60 // 1 hour
  });

  const hasAddPeoplePermission = hasPermissionPackage(userProfile, "addPeople");
  const [isAddTeamMemberDialogOpen, setIsAddTeamMemberDialogOpen] = React.useState(false);

  const {stepsCompleted, findStep, isLoading} = useOnboarding({storeId});
  const currentStep = findStep(step);
  const isCompleted = includes(stepsCompleted, step);

  const markAsCompleted = api.user.completeTutorialStep.useMutation({
    onSuccess: async () => {
      await apiUtil.user.getTutorialStepsCompleted.invalidate();
      toast.success(`${currentStep?.title ?? "Successfully"} marked as completed`);
    },
  });
  const handleMarkAsCompleted = async () => {
    markAsCompleted.mutate({
      storeId,
      step,
    });
  };

  if (isLoading) {
    return <LoadingPage />;
  }

  if (!currentStep) {
    return (
      <div className="flex flex-col gap-4">
        <Heading level={2}>
          Training Area Not Found
        </Heading>
        <Text>The training area "{step}" does not exist.</Text>
      </div>
    );
  }

  return (
    <>
      <div className="flex flex-col gap-4">
        <Heading level={3} size="xs" className={"font-semibold"}>
          {currentStep.title}
        </Heading>

        {currentStep.body ? (
          <div className="prose max-w-none">
            {map(split(currentStep.body, "\n\n"), (paragraph, index) => (
              <Text size={"sm"} key={index} className={index < split(currentStep.body, "\n\n").length - 1 ? "mb-4" : ""}>
                {paragraph}
              </Text>
            ))}
          </div>
        ) : null}

        <div className="flex justify-end gap-2">
          <Button
            variant={"outline"}
            onClick={handleMarkAsCompleted}
            disabled={isCompleted}
            isLoading={markAsCompleted.isPending}
          >
            Mark as completed
          </Button>
          {hasAddPeoplePermission ? <Button variant={"outline"} rightIcon={<PlusIcon size={16} />}
                  onClick={() => setIsAddTeamMemberDialogOpen(true)}>Add Team Members</Button>
                  : <Text muted> You don't have permission to add team members.</Text>}
        </div>

        {currentStep.videoPlaybackId ? (
                <div className="aspect-video">
                  <MuxPlayer playbackId={currentStep.videoPlaybackId}/>;
                </div>
        ) : null}
      </div>
      <AddTeamMemberDialog
        isOpen={isAddTeamMemberDialogOpen}
        onOpenChange={setIsAddTeamMemberDialogOpen}
        storeId={storeId}
      />
    </>
  );
}
