import {createFileRoute, SearchSchemaInput} from '@tanstack/react-router'
import {z} from 'zod'
import {Input} from '@/src/components/ui/input';
import {SearchIcon, ChevronDown} from 'lucide-react';
import {DataTable} from "@/src/components/ui/data-table.tsx";
import {
  ColumnFiltersState,
  createColumnHelper,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  SortingState,
  useReactTable
} from "@tanstack/react-table";
import {useCallback, useMemo, useState} from "react";
import {api} from '@/src/api';
import {
  ChecklistTemplateDto
} from "../../../../../../../../../api/src/checklists/checklistTemplate/checklistTemplateDtos.ts";
import {every, flatMap, map, uniq } from "lodash";
import {Badge} from "@/src/components/ui/badge.tsx";
import {FilterChecklistTagsPopover, FilterChecklistTagsFormValues} from '@/src/components/FilterChecklistTagsPopover';
import {Button} from '@/src/components/ui/button';

const templatesSearchSchema = z.object({
  tags: z.array(z.string()).optional(),
});

type TemplatesSearch = z.infer<typeof templatesSearchSchema>;

export const Route = createFileRoute('/_signedin/$businessId/$storeId/_nav/checklists/_nav/templates')({
  component: TemplatesChecklists,
  validateSearch: (search: unknown & SearchSchemaInput): TemplatesSearch | undefined => {
    if (templatesSearchSchema.safeParse(search).success) {
      return search as any;
    }
  }
})

const colHelper = createColumnHelper<ChecklistTemplateDto>();

export const columns = [
  colHelper.accessor("title", {
    header: 'Title',
    cell: info => info.getValue(),
  }),
  colHelper.accessor("tags", {
    header: 'Tags',
    cell: info => <div className={"flex flex-wrap gap-1"}>
      {map(info.getValue(), tag => <Badge key={tag} colorScheme={"default"} size={"sm"}>
        {tag}
      </Badge>)}
    </div>,
  }),
  colHelper.accessor("description", {
    header: 'Description',
    cell: info => info.getValue(),
  }),
  colHelper.accessor("items", {
    header: 'Items',
    cell: info => info.getValue().length,
  }),
];

function TemplatesChecklists() {
  const {businessId, storeId} = Route.useParams();
  const searchParams = Route.useSearch();
  const navigate = Route.useNavigate();
  const [sorting, setSorting] = useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [tagFilters, setTagFilters] = useState<string[]>(searchParams?.tags || []);

  const [{items: checklists}] = api.checklist2.getChecklistTemplates.useSuspenseQuery({
    storeId,
  });

  // Extract unique tags from checklist templates data (only tags actually in use)
  const availableTags = useMemo(() => {
    const allTagsFromTemplates = flatMap(checklists || [], template => template.tags);
    return uniq(allTagsFromTemplates).sort();
  }, [checklists]);

  // Filter data by tags
  const filteredChecklists = useMemo(() => {
    if (tagFilters.length === 0) {
      return checklists || [];
    }
    return (checklists || []).filter((template) => {
      return every(tagFilters, tag => template.tags.includes(tag));
    });
  }, [checklists, tagFilters]);

  const handleTagFiltersChange = useCallback((newTagFilters: string[]) => {
    setTagFilters(newTagFilters);
    navigate({
      search: (prev) => ({ ...prev, tags: newTagFilters.length ? newTagFilters : undefined }),
      replace: true
    });
  }, [navigate]);

  // Filter form values
  const filterValues: FilterChecklistTagsFormValues = { tags: tagFilters };
  const defaultFilterValues: FilterChecklistTagsFormValues = { tags: [] };

  const table = useReactTable({
    data: filteredChecklists,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    onSortingChange: setSorting,
    getSortedRowModel: getSortedRowModel(),
    onColumnFiltersChange: setColumnFilters,
    getFilteredRowModel: getFilteredRowModel(),
    state: {
      sorting,
      columnFilters,
    },
    initialState: {
      pagination: {
        pageSize: 50
      }
    }
  });

  return <section>
    <div className="pb-4 flex justify-start gap-3">
      <div>
      <Input type={"search"}
             leftIcon={SearchIcon}
             placeholder="Search title..."
             value={(table.getColumn("title")?.getFilterValue() as string) ?? ""}
             onChange={(event) =>
               table.getColumn("title")?.setFilterValue(event.target.value)
             }
             className="w-auto"
      />
      </div>
      <FilterChecklistTagsPopover
        initialValues={filterValues}
        defaultValues={defaultFilterValues}
        onSubmit={(values) => handleTagFiltersChange(values.tags)}
        allTags={availableTags}
      >
        <div className={"relative"}>
          <Button variant={"outline"}>
            <div className="flex flex-row justify-between items-center gap-2">
              <span>Filter by Tags</span>
              <ChevronDown size={16}/>
            </div>
          </Button>
          {tagFilters.length > 0 && (
            <div className={"absolute"} style={{top: -3, right: -5}}>
              <Badge size={"sm"} colorScheme={"destructive"}
                     className={"min-h-5 min-w-5 ml-2 px-1 flex items-center justify-center"}>
                {tagFilters.length}
              </Badge>
            </div>
          )}
        </div>
      </FilterChecklistTagsPopover>
    </div>

    <DataTable table={table}
               rowDetailLinkFrom={Route.fullPath}
               getRowDetailTo={row => `${row.original.id}`}/>
  </section>
}
