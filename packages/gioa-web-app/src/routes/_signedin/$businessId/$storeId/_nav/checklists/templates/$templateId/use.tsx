import {createFileRoute} from '@tanstack/react-router'
import React, {Suspense, useCallback, useState} from 'react'
import {api} from '@/src/api'
import {clamp} from 'lodash'
import {
  ChecklistTemplateTimingTabContent,
  ChecklistTemplateTimingTabContentFormValues
} from '@/src/components/checklists2/ChecklistTemplateTimingTabContent'
import {
  ChecklistTemplateNotificationsFormValues,
  ChecklistTemplateNotificationsTabContent
} from '@/src/components/checklists2/ChecklistTemplateNotificationsTabContent'
import {Checklist2AssignTabContent,} from '@/src/components/checklists2/Checklist2AssignTabContent'
import {WizardStepper} from '@/src/components/WizardStepper'
import {convertRecurrenceScheduleToRRuleOptions} from '@gioa/api/src/calendar/recurrenceSet/recurrenceScheduleConverter'
import {Spinner} from '@/src/components/Spinner'
import {toast} from 'sonner'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/src/components/ui/alert-dialog'
import {BackButton} from "@/src/components/BackButton.tsx";
import {ChecklistAssignFormValues} from "@/src/components/checklists2/Checklist2AssignFormFields.tsx";

export const Route = createFileRoute(
  '/_signedin/$businessId/$storeId/_nav/checklists/templates/$templateId/use',
)({
  component: RouteComponent,
})

function ActivateAssignChecklistTemplateScreenComp() {
  const { businessId, storeId, templateId } = Route.useParams()
  const canUseRecurringTiming = true // This would be determined by user permissions

  const [{ template: checklist, timezone }] = api.checklist2.getChecklistTemplate.useSuspenseQuery({
    storeId,
    checklistTemplateId: templateId
  })

  const navigate = Route.useNavigate()
  const apiUtil = api.useUtils()
  const activate = api.checklist2.activateChecklistTemplate.useMutation()

  // State to store form values from each tab
  const [timingValues, setTimingValues] = useState<ChecklistTemplateTimingTabContentFormValues | null>(null)
  const [notificationsValues, setNotificationsValues] = useState<ChecklistTemplateNotificationsFormValues | null>(null)
  const [assignValues, setAssignValues] = useState<ChecklistAssignFormValues | null>(null)
  const [selectedViewIndex, setSelectedViewIndex] = useState(0)
  const [isConfirmDialogOpen, setIsConfirmDialogOpen] = useState(false)

  const onBack = () => {
    navigate({
      to: '..',
    })
  }

  const onPrev = () => {
    setSelectedViewIndex(clamp(selectedViewIndex - 1, 0, 2))
  }

  const onPrevSave = (values: ChecklistAssignFormValues) => {
    setAssignValues(values)
    onPrev()
  }

  const onNext = () => {
    setSelectedViewIndex(clamp(selectedViewIndex + 1, 0, 2))
  }

  // Callback functions for each tab
  const onTimingSubmit = useCallback((values: ChecklistTemplateTimingTabContentFormValues) => {
    setTimingValues(values)
    onNext()
  }, [onNext])

  const onNotificationsSubmit = useCallback((values: ChecklistTemplateNotificationsFormValues) => {
    setNotificationsValues(values)
    onNext()
  }, [onNext])

  const onAssignSubmit = useCallback((values: ChecklistAssignFormValues) => {
    setAssignValues(values)
    setIsConfirmDialogOpen(true)
  }, [])

  const handleActivate = () => {
    if (timingValues) {
      activate.mutate({
        storeId,
        checklistTemplateId: templateId,
        isAllDay: false,
        isExplicitTiming: timingValues.isExplicitTiming,
        start: timingValues.start,
        end: timingValues.end,
        rrule: timingValues.recurrence ? convertRecurrenceScheduleToRRuleOptions(timingValues.recurrence) : undefined,

        notifyOnIncomplete: notificationsValues?.notifyOnIncomplete ?? false,
        notifyGeneral: notificationsValues?.notifyGeneral ?? false,
        notifyOnComplete: notificationsValues?.notifyOnComplete ?? false,
        peopleToNotifyOnIncomplete: notificationsValues?.peopleToNotify || [],
        peopleToNotifyOnComplete: notificationsValues?.peopleToNotify || [],

        assignPeople: assignValues?.assignPeopleIds || [],
        assignShiftLead: assignValues?.assignShiftLead,
        assignStoreAreaId: assignValues?.assignStoreAreaId || undefined,
        assignScheduleAreaTitle: assignValues?.assignScheduleAreaTitle || undefined,
        assignStorePositionId: assignValues?.assignStorePositionId || undefined,
      }, {
        onSuccess: () => {
          apiUtil.checklist2.invalidate()
          toast.success("Checklist created from template. It will be visible to assigned team members.")
          navigate({
            to: '/$businessId/$storeId/checklists/templates',
            params: { businessId, storeId }
          })
        },
        onError: (error) => {
          toast.error(`Error: ${error.message}`)
        }
      })
    }
    setIsConfirmDialogOpen(false)
  }

  const renderTabContent = useCallback(() => {
    switch (selectedViewIndex) {
      case 0:
        return <ChecklistTemplateTimingTabContent
          timezone={timezone}
          checklist={checklist}
          canUseRecurringTiming={canUseRecurringTiming}
          defaultValues={timingValues || undefined}
          onSubmit={onTimingSubmit}
          onBack={onBack}
        />
      case 1:
        return <ChecklistTemplateNotificationsTabContent
          storeId={storeId}
          defaultValues={notificationsValues || undefined}
          onSubmit={onNotificationsSubmit}
          onPrev={onPrev}
        />
      case 2:
        return <Checklist2AssignTabContent
          storeId={storeId}
          defaultValues={assignValues || undefined}
          onSubmit={onAssignSubmit}
          onPrev={onPrevSave}
          isSubmitLoading={activate.isPending}
          submitLabel="Use Template"
        />
    }
  }, [selectedViewIndex, checklist, timezone, onBack, onPrev, timingValues, canUseRecurringTiming, notificationsValues, assignValues, onTimingSubmit, onNotificationsSubmit, onAssignSubmit, activate.isPending, storeId])

  return (
    <div>
      <div className="px-6 pt-6">
        <BackButton/>

        <h1 className={"text-2xl font-semibold"}>
          {checklist.title}
        </h1>

        <p className="mt-2 mb-3 text-gray-600">
          {checklist.description}
        </p>

        <hr/>
      </div>

      <div className="px-6 pt-4 mb-3 max-w-screen-sm">
        <WizardStepper
          steps={["Timing", "Notifications", "Assign"]}
          currentStep={selectedViewIndex}
        />
      </div>
      <div className="px-6 py-6 max-w-screen-sm">
        {renderTabContent()}
      </div>

      <AlertDialog open={isConfirmDialogOpen} onOpenChange={setIsConfirmDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Use Template?</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to use this template? This will create a checklist visible to assigned team members.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={handleActivate}>
              Use Template
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )
}

function RouteComponent() {
  return (
    <Suspense fallback={<div className="flex justify-center items-center h-64"><Spinner /></div>}>
      <ActivateAssignChecklistTemplateScreenComp />
    </Suspense>
  )
}
