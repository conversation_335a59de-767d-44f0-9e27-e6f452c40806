import {createFileRoute} from "@tanstack/react-router";
import {includes, map, split} from "lodash";
import {Heading} from "@/src/components/Heading.tsx";
import {Text} from "@/src/components/Text.tsx";
import {Button} from "@/src/components/ui/button.tsx";
import {ArrowRightIcon} from "lucide-react";
import {api} from "@/src/api.ts";
import {toast} from "sonner";
import MuxPlayer from '@mux/mux-player-react';
import {useOnboarding} from "@/src/hooks/useOnboarding.tsx";
import {LoadingPage} from "@/src/components/LoadingPage.tsx";
import React from "react";

export const Route = createFileRoute("/_signedin/$businessId/$storeId/_nav/walkthrough/$step")({
  component: RouteComponent,
});

function RouteComponent() {
  const {storeId, step} = Route.useParams();
  const apiUtil = api.useUtils();

  const {findStep, stepsCompleted, isLoading} = useOnboarding({storeId});

  const currentStep = findStep(step);
  const isCompleted = includes(stepsCompleted, step);

  const markAsCompleted = api.user.completeTutorialStep.useMutation({
    onSuccess: async () => {
      await apiUtil.user.getTutorialStepsCompleted.invalidate();
      toast.success(`${currentStep?.title ?? "Successfully"} marked as completed`);
    },
  });
  const handleMarkAsCompleted = async () => {
    markAsCompleted.mutate({
      storeId,
      step,
    });
  };

  const navigate = Route.useNavigate();
  const goToTutorialScreen = () => {
    navigate({
      to: currentStep?.linkToUrl,
    });
  };

  if (isLoading) {
    return <LoadingPage />;
  }

  if (!currentStep) {
    return (
            <div className="flex flex-col gap-4">
              <Heading level={2}>
                Training Area Not Found
              </Heading>
              <Text>The training area "{step}" does not exist.</Text>
            </div>
    );
  }

  return (
          <div className="flex flex-col gap-4">
            <Heading level={3} size="xs" className={"font-semibold"}>
              {currentStep.title}
            </Heading>

            {currentStep.body ? (
                    <div className="prose max-w-none">
                      {map(split(currentStep.body, "\n\n"), (paragraph, index) => (
                              <Text size={"sm"} key={index} className={index < split(currentStep.body, "\n\n").length - 1 ? "mb-4" : ""}>
                                {paragraph}
                              </Text>
                      ))}
                    </div>
            ) : null}

            <div className="flex justify-end gap-2">
              <Button
                      variant={"outline"}
                      onClick={handleMarkAsCompleted}
                      disabled={isCompleted}
                      isLoading={markAsCompleted.isPending}
              >
                Mark as completed
              </Button>
              {currentStep.linkToUrl ? (
                      <Button variant={"outline"} onClick={goToTutorialScreen} rightIcon={<ArrowRightIcon size={16}/>}>
                        Go to {currentStep.title}
                      </Button>
              ) : null}
            </div>

            {currentStep.videoPlaybackId ? (
                    <div className="aspect-video">
                      <MuxPlayer playbackId={currentStep.videoPlaybackId}/>;
                    </div>
            ) : null}
          </div>
  );
}
