import {createFileRoute, Outlet} from '@tanstack/react-router'
import {CreateChecklistDialogForm} from "@/src/components/CreateChecklistDialogForm.tsx"
import {Heading} from "@/src/components/Heading.tsx";
import {ChecklistNav} from '@/src/components/ChecklistNav';
import React, {Suspense} from "react";
import {Spinner} from "@/src/components/Spinner.tsx";
import {Button} from "@/src/components/ui/button.tsx";
import {StoreIcon} from "lucide-react";
import {useFeatureFlag} from "@/src/hooks/useFeatureFlag.tsx";

export const Route = createFileRoute('/_signedin/$businessId/$storeId/_nav/checklists/_nav')({
  component: ChecklistsLayout
})

function ChecklistsLayout() {
  const {storeId} = Route.useParams();
  const navigate = Route.useNavigate();

  const townSquareFeatureFlag = useFeatureFlag("checklistTownSquare")

  const navToTownSquare = () => {
    if (!townSquareFeatureFlag) {
      return;
    }

    navigate({
      to: `/$businessId/$storeId/checklists/town-square`,
    });
  }

  return (
    <div className={"p-6"}>
      <Heading level={1} size={"md"} className={"m-0 mb-8 grow"}>Checklists</Heading>

      <div className={"flex gap-3 flex-wrap items-center justify-between"}>
        <ChecklistNav storeId={storeId} routeFullPath={Route.fullPath}/>
        <div className={"flex flex-row gap-3"}>
          {townSquareFeatureFlag ? <Button variant={"outline"} onClick={navToTownSquare} leftIcon={<StoreIcon size={16} />}>Town Square</Button> : null}
          <CreateChecklistDialogForm storeId={storeId}/>
        </div>
      </div>

      <hr className="my-5"/>

      <div className="flex-1">
        <Suspense fallback={<Spinner size={"lg"}/>}>
        <Outlet/>
        </Suspense>
      </div>
    </div>
  )
}
