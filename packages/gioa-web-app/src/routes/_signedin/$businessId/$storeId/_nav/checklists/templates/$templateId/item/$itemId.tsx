import {createFileRoute} from '@tanstack/react-router'
import React, {Suspense} from 'react'
import {api} from '@/src/api'
import {Spinner} from '@/src/components/Spinner'
import {find} from 'lodash'
import {toast} from 'sonner'
import {ChecklistItemEditor} from '@/src/components/ChecklistItemEditor'
import {BackButton, useBack} from "@/src/components/BackButton.tsx";

export const Route = createFileRoute(
  '/_signedin/$businessId/$storeId/_nav/checklists/templates/$templateId/item/$itemId',
)({
  component: RouteComponent,
})

/**
 * This component mirrors the structure of the mobile app's ChecklistTemplateItemScreenComp
 */
function ChecklistTemplateItemScreenComp() {
  const {storeId, templateId, itemId} = Route.useParams()
  const navigate = Route.useNavigate()
  const [{template}] = api.checklist2.getChecklistTemplate.useSuspenseQuery({
    storeId,
    checklistTemplateId: templateId,
  })

  const apiUtils = api.useUtils()
  const getPresignedPost = api.checklist2.getPresignedPostForChecklistItem.useMutation()
  const upsertItem = api.checklist2.upsertChecklistTemplateItem.useMutation()
  const deleteItem = api.checklist2.deleteChecklistTemplateItem.useMutation()
  const checklistItem = find(template.items, item => item.id === itemId)
  const onBack = useBack()

  return (
    <div className="max-w-screen-sm">
      <div className="px-6 pt-6">
        <BackButton/>

        <h1 className={"text-2xl font-semibold"}>
          {template.title}
        </h1>

        <p className="mt-2 mb-3 text-gray-600">
          {template.description}
        </p>

        <hr/>
      </div>

      <ChecklistItemEditor className={"p-6"}
        storeId={storeId}
        itemId={itemId}
        checklistItem={checklistItem}
        getPresignedPost={getPresignedPost.mutateAsync}
        upsertItem={(params, options) => {
          return upsertItem.mutate({
            storeId,
            itemId: itemId,
            checklistTemplateId: templateId,
            title: params.title,
            description: params.description,
            attachment: params.attachment,
            requirements: params.requirements,
            instructions: params.instructions,
          }, {
            ...options,
            onSuccess: async () => {
              await apiUtils.checklist2.invalidate()
              toast.success('Checklist item saved')
              onBack();
              if (options?.onSuccess) options.onSuccess()
            },
            onError: (error) => {
              toast.error(`Error: ${error.message}`)
              if (options?.onError) options.onError(error)
            }
          })
        }}
        deleteItem={(params, options) => {
          return deleteItem.mutate({
            storeId,
            checklistTemplateId: templateId,
            itemId: itemId
          }, {
            ...options,
            onSuccess: async () => {
              await apiUtils.checklist2.invalidate()
              toast.success('Checklist item deleted')
              onBack();
              if (options?.onSuccess) options.onSuccess()
            },
            onError: (error) => {
              toast.error(`Error: ${error.message}`)
              if (options?.onError) options.onError(error)
            }
          })
        }}
        isUpsertPending={upsertItem.isPending}
        isDeletePending={deleteItem.isPending}
        invalidateApi={apiUtils.checklist2.invalidate}
      />
    </div>
  )
}

/**
 * This screen is for adding or editing a checklist template item.
 * It mirrors the mobile app implementation.
 */
function RouteComponent() {
  return (
    <Suspense fallback={<div className="flex justify-center items-center h-64"><Spinner/></div>}>
      <ChecklistTemplateItemScreenComp/>
    </Suspense>
  )
}
