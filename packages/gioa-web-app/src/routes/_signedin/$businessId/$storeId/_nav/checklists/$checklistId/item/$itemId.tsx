import {createFileRoute} from '@tanstack/react-router';
import React, {Suspense} from 'react';
import {api} from '@/src/api';
import {Spinner} from '@/src/components/Spinner';
import {find} from 'lodash';
import {ChecklistItemEditor} from '@/src/components/ChecklistItemEditor';
import {toast} from 'sonner';
import {BackButton, useBack} from "@/src/components/BackButton.tsx";

export const Route = createFileRoute('/_signedin/$businessId/$storeId/_nav/checklists/$checklistId/item/$itemId')({
  component: ChecklistItemPage,
  validateSearch: (search) => {
    return {
      recurrenceId: search.recurrenceId
        ? new Date(search.recurrenceId as string)
        : undefined
    };
  },
});

/**
 * This screen is for adding or editing a checklist item.
 */
function ChecklistItemContent() {
  const {storeId, checklistId, itemId} = Route.useParams();
  const {recurrenceId} = Route.useSearch();
  const [{checklist}] = api.checklist2.getChecklist2.useSuspenseQuery({
    storeId: storeId,
    checklistId: checklistId,
    recurrenceId: recurrenceId
  });

  const getPresignedPost = api.checklist2.getPresignedPostForChecklistItem.useMutation();
  const upsertItem = api.checklist2.upsertChecklistItem.useMutation();
  const deleteItem = api.checklist2.deleteChecklistItem.useMutation();
  const apiUtil = api.useUtils();

  const checklistItem = find(checklist.items, i => i.id === itemId);
  const onBack = useBack();

  return (
    <div className={"max-w-screen-sm"}>
      <div className="px-6 pt-6">
        <BackButton/>

        <h1 className={"text-2xl font-semibold"}>
          {checklist.title}
        </h1>

        <p className="mt-2 mb-3 text-gray-600">
          {checklist.description}
        </p>

        <hr/>
      </div>


      <ChecklistItemEditor
        storeId={storeId}
        itemId={itemId}
        checklistItem={checklistItem}
        getPresignedPost={getPresignedPost.mutateAsync}
        upsertItem={(params, options) => {
          return upsertItem.mutate({
            storeId,
            itemId: itemId,
            checklistId: checklistId,
            recurrenceId: recurrenceId,
            title: params.title,
            description: params.description,
            attachment: params.attachment,
            requirements: params.requirements,
            instructions: params.instructions,
          }, {
            onSuccess: async () => {
              await apiUtil.checklist2.invalidate();
              toast.success("Item saved");
              onBack();
              options?.onSuccess?.();
            },
            onError: (error) => {
              toast.error(`Error saving checklist item: ${error.message}`);
              options?.onError?.(error);
            }
          });
        }}
        deleteItem={(params, options) => {
          return deleteItem.mutate({
            storeId,
            checklistId: checklistId,
            recurrenceId: recurrenceId,
            itemId: itemId
          }, {
            onSuccess: async () => {
              await apiUtil.checklist2.invalidate();
              toast.success("Item deleted");
              onBack();
              options?.onSuccess?.();
            },
            onError: (error) => {
              toast.error(`Error deleting checklist item: ${error.message}`);
              options?.onError?.(error);
            }
          });
        }}
        isUpsertPending={upsertItem.isPending}
        isDeletePending={deleteItem.isPending}
        invalidateApi={apiUtil.checklist2.invalidate}
        className={"p-6"}
      />

    </div>
  );
}

function ChecklistItemPage() {
  return (
    <Suspense fallback={<div className="flex justify-center items-center h-64"><Spinner/></div>}>
      <ChecklistItemContent/>
    </Suspense>
  );
}
