import {createFileRoute, Link as RouterLink, useNavigate} from '@tanstack/react-router'
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator
} from "@/src/components/ui/breadcrumb.tsx";
import React from "react";
import {Heading} from "@/src/components/Heading.tsx";
import {toast} from "sonner";
import {api} from "@/src/api.ts";
import {genTimeOffRequestId} from "../../../../../../../../../../api/src/schemas.ts";
import {ErrorAlert} from "@/src/components/ErrorAlert.tsx";
import {TimeOffFormValues, TimeOffRequestForm} from "@/src/components/TimeOffRequestForm.tsx";
import {DateTimeRange} from '@gioa/api/src/timeSchemas.ts';
import {SchedulePersonDto} from "../../../../../../../../../../api/src/schedulePersonDto.ts";

export const Route = createFileRoute('/_signedin/$businessId/$storeId/_nav/schedules/requests/timeOff/create')({
  component: Component
})

function Component() {
  const {storeId} = Route.useParams();
  const create = api.user.createTimeOffRequestAdmin.useMutation();
  const navigate = useNavigate();
  const apiUtil = api.useUtils();

  const onSubmit = (values: TimeOffFormValues, range: DateTimeRange, person: SchedulePersonDto) => {
    const reqId = genTimeOffRequestId();
    create.mutate({
      id: reqId,
      personId: values.personId,
      storeId: storeId,
      range: range,
      timeOffType: values.timeOffType ?? "",
      submittedReason: values.reason ?? "",
    }, {
      onSuccess: () => {
        toast.success("Time off request created for " + person?.firstName + " " + person?.lastName);
        navigate({
          from: Route.fullPath,
          to: "../" + reqId
        });
        apiUtil.user.getStoreTimeOffRequests.invalidate();
        apiUtil.user.getAllSchedulePeopleAtStore.invalidate();
      }
    })
  }


  return <div className={"p-3 md:p-6 grow max-w-screen-md"}>
    <Breadcrumb className={"mb-3"}>
      <BreadcrumbList>
        <BreadcrumbItem>
          <BreadcrumbLink asChild>
            <RouterLink from={Route.fullPath} to={".."}>
              Time off requests
            </RouterLink>
          </BreadcrumbLink>
        </BreadcrumbItem>
        <BreadcrumbSeparator/>
        <BreadcrumbItem>
          <BreadcrumbPage>
            Create new request
          </BreadcrumbPage>
        </BreadcrumbItem>
      </BreadcrumbList>
    </Breadcrumb>

    <Heading level={1}>
      Create time off request
    </Heading>
    <p className={"mb-4"}>
      Create a pending time off request on behalf of a team member.
    </p>

    {create.isError && <ErrorAlert error={create.error} className={"mb-3"}/>}

    <TimeOffRequestForm onSubmit={onSubmit} submitButtonText={"Create"} storeId={storeId}
                        isPending={create.isPending}/>
  </div>
}
