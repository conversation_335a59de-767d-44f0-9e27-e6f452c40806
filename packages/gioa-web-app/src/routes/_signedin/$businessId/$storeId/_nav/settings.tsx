import { createFileRoute, Outlet } from "@tanstack/react-router";
import React, { useEffect } from "react";
import { getHumanReadableErrorMessageString } from "@/src/components/ErrorAlert.tsx";
import { api } from "@/src/api.ts";
import {useBack} from "@/src/components/BackButton.tsx";

export const Route = createFileRoute("/_signedin/$businessId/$storeId/_nav/settings")({
  component: RouteComponent,
});

function RouteComponent() {
  const { storeId } = Route.useParams();
  const [store] = api.user.getStoreAdmin.useSuspenseQuery(
          { storeId },
          { staleTime: 1000 * 60 * 60 } // 1 hour
  );
    const goBack = useBack();

  const startCustomerPortalSession = api.payment.startCustomerPortalSession.useMutation();
  const startSetupSession = api.payment.startSetupPaymentSession.useMutation();

  const onManageSubscription = () => {
    const sessionMutation = store.hasPaymentMethod ? startCustomerPortalSession : startSetupSession;

    sessionMutation.mutate(
            { storeId },
            {
              onSuccess: (data) => {
                window.location.href = data.url;
              },
              onError: (error) => {
                alert("Failed to start: " + getHumanReadableErrorMessageString(error));
                goBack();
              },
            }
    );
  };

  useEffect(() => {
    onManageSubscription();
  }, []);

  return (
          <div className="w-full min-h-screen bg-gray-50 flex items-center justify-center">
            Redirecting to subscription portal...
          </div>
  );
}
