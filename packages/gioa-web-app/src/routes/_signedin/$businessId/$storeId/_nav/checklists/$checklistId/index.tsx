import {createFileRoute} from '@tanstack/react-router';
import React, {Suspense, useState} from 'react';
import {api} from '@/src/api';
import {Tabs, TabsContent, TabsList, TabsTrigger} from '@/src/components/ui/tabs';
import {useForm} from '@tanstack/react-form';
import {zodValidator} from '@tanstack/zod-form-adapter';
import {toast} from 'sonner';
import {Spinner} from '@/src/components/Spinner';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle
} from '@/src/components/ui/alert-dialog';
import {Checklist2ItemsTabContainer} from '@/src/components/checklists2/Checklist2ItemsTabContainer';
import {
  Checklist2SettingsTabContent,
  ChecklistSettingsFormValues
} from '@/src/components/checklists2/Checklist2SettingsTabContent';
import {Checklist2AssignFormContainer} from '@/src/components/checklists2/Checklist2AssignFormContainer';
import {Checklist2EventActionButtons} from '@/src/components/checklists2/Checklist2EventActionButtons';
import {
  convertRecurrenceScheduleToRRuleOptions,
  convertRRuleOptionsToRecurrenceSchedule
} from '@gioa/api/src/calendar/recurrenceSet/recurrenceScheduleConverter';
import {isRRuleEqual} from '@gioa/api/src/calendar/recurrenceSet/recurrenceRule';
import {ChecklistUpdateType} from "../../../../../../../../../api/src/checklists/checklist/checklistDto.ts";
import {genChecklistItemId} from "../../../../../../../../../api/src/checklist.schemas.ts";
import {useDeleteChecklist2Event} from "@/src/hooks/useDeleteChecklist2Event.tsx";
import {Button} from "@/src/components/ui/button.tsx";
import {BackButton, useBack} from "@/src/components/BackButton.tsx";
import {RRuleOptions} from "../../../../../../../../../api/src/calendar/recurrenceSet/recurrenceRule.types.ts";
import {FormTextarea} from "@/src/components/form/FormTextarea.tsx";
import {Label} from "@/src/components/ui/label.tsx";
import {FieldInfo} from "@/src/components/form/FieldInfo.tsx";


export const Route = createFileRoute('/_signedin/$businessId/$storeId/_nav/checklists/$checklistId/')({
  component: EditChecklistPage,
  validateSearch: (search) => {
    return {
      recurrenceId: search.recurrenceId
        ? new Date(search.recurrenceId as string)
        : undefined
    };
  },
});

function ChecklistEditContent() {
  const {storeId, checklistId} = Route.useParams();
  const {recurrenceId} = Route.useSearch();
  const navigate = Route.useNavigate();
  const [selectedTab, setSelectedTab] = useState('items');
  const [isFormDirty, setIsFormDirty] = useState(false);
  const [isRecurrenceDialogOpen, setIsRecurrenceDialogOpen] = useState(false);
  const [isArchiveDialogOpen, setIsArchiveDialogOpen] = useState(false);
  const [formValues, setFormValues] = useState<{
    vals: ChecklistSettingsFormValues,
    formRRule: RRuleOptions | undefined
  }>();

  const [{checklist, timezone, canDelete, canUpdateSeries}] = api.checklist2.getChecklist2.useSuspenseQuery({
    storeId: storeId,
    checklistId: checklistId,
    recurrenceId: recurrenceId,
  });
  const [store] = api.user.getStore.useSuspenseQuery({storeId: storeId}, {
    staleTime: 1000 * 60 * 60 // 1 hour
  });

  const apiUtil = api.useUtils();
  const updateSettings = api.checklist2.updateChecklistSettings.useMutation();
  const people = store.employees || [];

  const settingsForm = useForm({
    defaultValues: {
      title: checklist.title,
      description: checklist.description,
      tags: checklist.tags,
      start: checklist.start as Date | undefined,
      end: checklist.end as Date | undefined,
      recurrence: checklist.rrule ? convertRRuleOptionsToRecurrenceSchedule(checklist.rrule) : null,
      isExplicitTiming: checklist.isExplicitTiming ?? false,
      notifyOnIncomplete: checklist.alarms.notifyOnIncomplete ?? false,
      notifyGeneral: checklist.alarms.notifyGeneral ?? false,
      notifyOnComplete: checklist.alarms.notifyOnComplete ?? false,
      peopleToNotify: checklist.alarms.peopleToNotifyOnIncomplete
    },
    validatorAdapter: zodValidator(),
    onSubmit: (event) => {
      const vals = event.value;
      if (!vals.start || !vals.end) {
        return;
      }

      const formRRule = vals.recurrence ? convertRecurrenceScheduleToRRuleOptions(vals.recurrence) : undefined;
      setFormValues({vals, formRRule});

      // if we're editing a checklist event, then determine if user wants to edit the event or series
      if (canUpdateSeries) {
        // If no rrule is defined, then we're editing only this event
        if (!formRRule && !checklist.rrule) {
          doUpdate("this", vals, formRRule, 1);
          return;
        }

        // If they edit the rrule
        if (!isRRuleEqual(checklist.rrule, formRRule)) {
          doUpdate("thisAndFuture", vals, formRRule, 2);
        } else {
          // Else they edited other parts of the checklist, not the rrule
          setIsRecurrenceDialogOpen(true);
        }
      }
    },
  });

  const doUpdate = (updateType: ChecklistUpdateType, vals: ChecklistSettingsFormValues, formRRule: RRuleOptions | undefined, numTimesBack: number) => {
    if (!vals.start || !vals.end) {
      return; // this won't happen but making TS happy
    }

    updateSettings.mutate({
      storeId: storeId,
      recurrenceId: recurrenceId,
      version: checklist.version,
      checklistId: checklistId,
      updateType: updateType,
      title: vals.title,
      description: vals.description,
      tags: vals.tags,
      start: vals.start,
      end: vals.end,
      rrule: formRRule,
      isExplicitTiming: vals.isExplicitTiming,
      notifyOnIncomplete: vals.notifyOnIncomplete,
      notifyGeneral: vals.notifyGeneral,
      notifyOnComplete: vals.notifyOnComplete,
      peopleToNotifyOnIncomplete: vals.peopleToNotify,
      peopleToNotifyOnComplete: vals.peopleToNotify,
    }, {
      onSuccess: async () => {
        apiUtil.checklist2.invalidate();
        toast.success("Checklist settings updated", {
          position: "top-center"
        });
        goBack(numTimesBack)
      },
      onError: (error) => {
        toast.error(`Error: ${error.message}`, {
          position: "top-center"
        });
      }
    });
  };

  const goBack = useBack();
  const settingsFormIsDirty = settingsForm.useStore((state) => state.isDirty);
  const {onDelete, isPending: isDeletePending, DeleteConfirmDialog, RecurrenceDeleteDialog} = useDeleteChecklist2Event({
    checklistId: checklist.id,
    hasRecurrence: checklist.rrule !== undefined,
    recurrenceId: checklist.recurrenceId,
    onDeleted: () => goBack(2)
  });

  const archiveChecklist = api.checklist2.archiveChecklist.useMutation();
  const unarchiveChecklist = api.checklist2.unarchiveChecklist.useMutation();

  const onUnarchive = () => {
    unarchiveChecklist.mutate({
      checklistId: checklistId,
      recurrenceId: recurrenceId,
    }, {
      onSuccess: () => {
        apiUtil.checklist2.invalidate();
        toast.success("Checklist unarchived", {
          position: "top-center"
        });
      },
      onError: (error) => {
        toast.error(`Error: ${error.message}`, {
          position: "top-center"
        });
      }
    });
  };

  const isUnarchivePending = unarchiveChecklist.isPending;

  const archiveForm = useForm({
    defaultValues: {
      reason: ""
    },
    validatorAdapter: zodValidator(),
    onSubmit: async ({value}) => {
      archiveChecklist.mutate({
        checklistId: checklistId,
        recurrenceId: recurrenceId,
        reason: value.reason || "No reason provided"
      }, {
        onSuccess: () => {
          apiUtil.checklist2.invalidate();
          toast.success("Checklist archived", {
            position: "top-center"
          });
          setIsArchiveDialogOpen(false);
          goBack(2);
        },
        onError: (error) => {
          toast.error(`Error: ${error.message}`, {
            position: "top-center"
          });
        }
      })
    },
  });

  return (
    <div className="max-w-screen-sm">
      <div className="px-6 pt-6 ">
        <BackButton/>
        <div className={"flex justify-between"}>
          <div>
            <h1 className={"text-2xl font-semibold"}>
              {(() => {
                if (checklist.isArchived) {
                  return `${checklist.title} (Archived)`;
                }

                const now = new Date();
                const isChecklistInPast = checklist.end && checklist.end < now;
                const isChecklistOverdue = isChecklistInPast && !checklist.isComplete;

                if (isChecklistOverdue) {
                  return `${checklist.title} (Overdue)`;
                }

                return checklist.title;
              })()}
            </h1>

            <p className="mt-2 mb-3 text-gray-600">
              {checklist.description}
            </p>
          </div>
          <div>
            {(() => {
              if (checklist.isArchived) {
                // For archived checklists, show Unarchive button
                return (
                  <div>
                    <Button
                      variant="outline"
                      onClick={onUnarchive}
                      disabled={isUnarchivePending}
                    >
                      Unarchive Checklist
                    </Button>
                  </div>
                );
              } else if (canDelete) {
                const now = new Date();
                const isChecklistInPast = checklist.end && checklist.end < now;

                if (isChecklistInPast) {
                  // For overdue checklists, always show Archive button
                  return (
                    <div>
                      <Button
                        variant="outline"
                        onClick={() => setIsArchiveDialogOpen(true)}
                      >
                        Archive Checklist
                      </Button>
                    </div>
                  );
                } else {
                  // For current/future checklists, always show Delete button
                  return (
                    <>
                      <div>
                        <Button
                          variant="outline"
                          onClick={onDelete}
                          disabled={isDeletePending}>
                          Delete Checklist
                        </Button>
                      </div>
                      <DeleteConfirmDialog/>
                      <RecurrenceDeleteDialog/>
                    </>
                  );
                }
              }
              return null;
            })()}
          </div>
        </div>
      </div>
      <Tabs value={selectedTab} onValueChange={setSelectedTab} className="flex-1 flex flex-col">
        <div className="px-6 py-4">
          <TabsList className="w-full">
            <TabsTrigger value="items" disabled={settingsFormIsDirty || isFormDirty}
                         className="flex-1">Items</TabsTrigger>
            <TabsTrigger value="settings" disabled={isFormDirty} className="flex-1">Settings</TabsTrigger>
            <TabsTrigger value="assign" disabled={settingsFormIsDirty} className="flex-1">Assign</TabsTrigger>
          </TabsList>
        </div>

        <div className={"px-6 pb-6"}>
          <TabsContent value="items">
            <Checklist2ItemsTabContainer
              checklist={checklist}
              recurrenceId={recurrenceId}
              addItemHref={() => navigate({
                to: 'item/$itemId',
                params: {
                  itemId: genChecklistItemId(),
                },
                search: {recurrenceId: recurrenceId}
              })}
              editItemHref={(itemId) => navigate({
                to: 'item/$itemId',
                params: {
                  itemId,
                },
                search: {recurrenceId: recurrenceId}
              })}
              storeId={storeId}
              checklistTemplateId={undefined}
              checklistId={checklist.id}
            />
          </TabsContent>

          <TabsContent value="settings">
            <div className="flex flex-col h-full">
              <div className="flex-1 overflow-auto">
                <Checklist2SettingsTabContent
                  checklist={checklist}
                  storeId={storeId}
                  people={people}
                  form={settingsForm}
                  timezone={timezone}
                />
              </div>
              <Checklist2EventActionButtons
                form={settingsForm}
                formIsDirty={settingsFormIsDirty}
                isSavePending={updateSettings.isPending}
              />
            </div>
          </TabsContent>

          <TabsContent value="assign">
            <Checklist2AssignFormContainer
              canUpdateSeries={canUpdateSeries}
              storeId={storeId}
              onSave={goBack}
              onFormDirty={setIsFormDirty}
              checklist={checklist}
            />
          </TabsContent>
        </div>
      </Tabs>

      {formValues ?
        <AlertDialog open={isRecurrenceDialogOpen} onOpenChange={setIsRecurrenceDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Edit recurring checklist</AlertDialogTitle>
            <AlertDialogDescription>
              Which checklists do you want to apply your changes to?
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel className={"self-start"}>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={() => doUpdate("this", formValues.vals, formValues.formRRule, 1)}>
              This checklist
            </AlertDialogAction>
            <AlertDialogAction onClick={() => doUpdate("thisAndFuture", formValues.vals, formValues.formRRule, 2)}>
              This and future checklists
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog> : null}

      {/* Archive Dialog */}
      <AlertDialog open={isArchiveDialogOpen} onOpenChange={setIsArchiveDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Archive checklist</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to archive this checklist?
            </AlertDialogDescription>
          </AlertDialogHeader>
          <form
            onSubmit={(e) => {
              e.preventDefault();
              e.stopPropagation();
              archiveForm.handleSubmit();
            }}
          >
            <div className="py-4">
              <Label htmlFor="reason">Reason (optional)</Label>
              <archiveForm.Field
                name="reason"
                children={(field) => (
                  <>
                    <FormTextarea
                      field={field}
                      id="reason"
                      placeholder="Enter reason for archiving..."
                    />
                    <FieldInfo field={field} />
                  </>
                )}
              />
              <div className="text-sm text-gray-600 mt-1">
                This checklist will be archived and no longer appear in active lists.
              </div>
            </div>
            <AlertDialogFooter>
              <AlertDialogCancel type="button">Cancel</AlertDialogCancel>
              <AlertDialogAction
                type="submit"
                disabled={archiveChecklist.isPending}
              >
                {archiveChecklist.isPending ? "Archiving..." : "Archive"}
              </AlertDialogAction>
            </AlertDialogFooter>
          </form>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}

function EditChecklistPage() {
  return (
    <Suspense fallback={<div className="flex justify-center items-center h-64"><Spinner/></div>}>
      <ChecklistEditContent/>
    </Suspense>
  );
}
