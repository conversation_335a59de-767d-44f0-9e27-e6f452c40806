import {createRootRouteWithContext, Outlet} from '@tanstack/react-router'
import {api, clientUtils, queryClient, trpcClient, trpcReactClient} from "../api.ts";
import {QueryClient, QueryClientProvider} from "@tanstack/react-query";
import {loadCatalog} from "@/src/i18n.ts";
import {I18nProvider} from "@lingui/react";
import {i18n} from "@lingui/core";
import {TanStackRouterDevtools} from "@tanstack/router-devtools";
import {Toaster} from "@/src/components/ui/sonner.tsx";
import {ReactQueryDevtools} from "@tanstack/react-query-devtools";

interface AppRouterContext {
  trpc: typeof trpcClient,
  clientUtils: typeof clientUtils,
  queryClient: QueryClient;
}

loadCatalog("en")

// Use the routerContext to create your root route
export const Route = createRootRouteWithContext<AppRouterContext>()({
  component: App,
})

function App() {
  return (
    <api.Provider client={trpcReactClient} queryClient={queryClient}>
      <QueryClientProvider client={queryClient}>
        <I18nProvider i18n={i18n}>
          <Outlet/>
          {import.meta.env.VITE_GIOA_ENV === "development" &&
              <>
                  {/*<ReactQueryDevtools/>*/}
                  {/*<TanStackRouterDevtools/>*/}
              </>}
          <Toaster/>
        </I18nProvider>
      </QueryClientProvider>
    </api.Provider>
  )
}

