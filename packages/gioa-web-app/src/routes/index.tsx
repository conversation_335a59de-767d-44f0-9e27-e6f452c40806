import {createFileRout<PERSON>, Link, redirect, useNavigate} from '@tanstack/react-router'
import {api} from "../api.ts";
import {Button} from "@/src/components/ui/button"
import {Input} from "@/src/components/ui/input"
import {Label} from "@/src/components/ui/label"
import React, {useLayoutEffect, useRef, useState} from "react";
import {useForm} from "@tanstack/react-form";
import {zodValidator} from '@tanstack/zod-form-adapter'
import {FieldInfo} from '../components/form/FieldInfo.tsx';
import {emailOrPhoneSchema, UserDto} from "@gioa/api/src/schemas";
import {first, isEmpty} from "lodash";
import {ErrorAlert} from "@/src/components/ErrorAlert.tsx";
import {toast} from "sonner";
import * as Sentry from "@sentry/react";
import {LogoWithText} from "@/src/components/LogoWithText.tsx";
import {MfaVerification} from "@/src/components/MfaVerification";

interface LoginSearchParams {
  redirect?: string;
}

function getUserDestination(user: { person: { businessId?: string, storeIds?: string[] }, systemRole?: string }) {
  const firstStore = first(user.person.storeIds);
  const destination = user.systemRole === "GIOA_ADMIN"
    ? "/admin/dashboard"
    : `/${user.person.businessId}/${firstStore}`;

  return destination;
}

export const Route = createFileRoute('/')({
  component: Login,
  validateSearch: (search: Record<string, unknown>): LoginSearchParams => {
    // validate and parse the search params into a typed state
    const redirectTo = (search.redirect as string) || undefined;

    // avoid open redirect (see https://thecopenhagenbook.com/open-redirect)
    if (redirectTo && !redirectTo.startsWith('/')) {
      return {
        redirect: undefined,
      }
    }

    return {
      redirect: redirectTo,
    }
  },
  beforeLoad: async ({context, search, location, navigate}) => {
    const validate = await context.clientUtils.auth.validateSession.ensureData();

    // If the user is logged in, redirect them to the dashboard
    if (validate.valid && !search.redirect) {
      throw redirect({
        replace: true,
        to: getUserDestination({
          systemRole: validate.systemRole,
          person: {
            businessId: validate.businessId,
            storeIds: validate.storeIds,
          }
        }),
      });
    }
  },
})

function Login() {
  const loginMutation = api.auth.login.useMutation();
  const navigate = useNavigate();
  const util = api.useUtils();
  const [showMfaVerification, setShowMfaVerification] = useState(false);
  const [pendingSessionId, setPendingSessionId] = useState<string | null>();

  const handleSuccessfulLogin = async (user: UserDto) => {
    await util.auth.validateSession.reset();
    Sentry.setUser({
      id: user.id,
      email: user.person?.email,
    });
    if (user.emailVerified) {
      const destination = getUserDestination(user);
      navigate({
        replace: true,
        to: search.redirect || destination
      });
    } else {
      navigate({
        to: "/verify-email"
      });
    }
  }

  const form = useForm({
    defaultValues: {
      email: '',
      password: '',
      trustClient: false
    },
    onSubmit: async ({value}) => {
      loginMutation.mutate({
        email: value.email,
        password: value.password,
        trustClient: value.trustClient
      }, {
        onSuccess: async (data) => {
          if (data.user && !data.requiresSmsMfa) {
            handleSuccessfulLogin(data.user);
          } else if (data.requiresSmsMfa && data.pendingSessionId) {
            setPendingSessionId(data.pendingSessionId);
            setShowMfaVerification(true);
          }
        }
      })
    },
    validatorAdapter: zodValidator(),
  })

  const search = Route.useSearch();
  const toastShown = useRef(false);
  useLayoutEffect(() => {
    if (search.redirect && !toastShown.current) {
      toastShown.current = true;
      toast.error("Your session has expired. Please login again.", {
        position: "top-center",
      });
    }
  }, [search, search.redirect])

  const handleMfaSuccess = async (user: UserDto) => {
    handleSuccessfulLogin(user);
  };

  return (
    <div className="w-full min-h-screen flex items-stretch bg-gradient-to-br from-primary-50 to-primary-100">
      <div className="flex-1 flex items-center justify-center p-8">
        <div className="w-full max-w-md">
          <div className="mb-8 text-center">
            <h1 className="text-3xl font-bold text-primary-800 mb-2">
              {showMfaVerification ? "Verify Your Identity" : "Welcome to Nation"}
            </h1>
            <p className="text-primary-600">
              {showMfaVerification
                ? "Enter the 6-digit code sent to your phone"
                : "Sign in to continue to your dashboard"}
            </p>
            {loginMutation.isError && !showMfaVerification &&
                <ErrorAlert className="mt-4 animate-fade-in-up" error={loginMutation.error}/>
            }
          </div>

          <div className="bg-white rounded-2xl shadow-xl p-8 transition-all duration-300 hover:shadow-2xl">
            {!showMfaVerification ? (
              <form onSubmit={e => {
                e.preventDefault();
                e.stopPropagation();
                form.handleSubmit();
              }}>
                <div className="space-y-5">
                  <form.Field
                    name="email"
                    validators={{
                      onSubmit: emailOrPhoneSchema
                    }}
                    children={(field) => {
                      return (
                        <div className="space-y-2">
                          <Label
                            htmlFor={field.name}
                            className="text-sm font-medium"
                          >
                            Email or Phone
                          </Label>
                          <div className="relative">
                            <Input
                              id={field.name}
                              hasError={!isEmpty(field.state.meta.errors)}
                              name={field.name}
                              value={field.state.value}
                              onBlur={field.handleBlur}
                              onChange={(e) => field.handleChange(e.target.value)}
                              placeholder="Enter your email or phone number"
                              className="pl-3 pr-3 py-2 focus:border-primary-400 focus:ring-primary-400 rounded-lg transition-all duration-200"
                            />
                          </div>
                          <FieldInfo field={field}/>
                        </div>
                      );
                    }}
                  />

                  <form.Field
                    name="password"
                    children={(field) => (
                      <div className="space-y-2">
                        <div className="flex items-center justify-between">
                          <Label
                            htmlFor={field.name}
                            className="text-sm font-medium"
                          >
                            Password
                          </Label>
                          <Link
                            to="/forgot-password"
                            className="text-xs font-medium text-primary-600 hover:text-primary-800 transition-colors"
                          >
                            Forgot password?
                          </Link>
                        </div>
                        <div className="relative">
                          <Input
                            id={field.name}
                            hasError={!isEmpty(field.state.meta.errors)}
                            name={field.name}
                            value={field.state.value}
                            onBlur={field.handleBlur}
                            onChange={(e) => field.handleChange(e.target.value)}
                            type="password"
                            className="pl-3 pr-3 py-2 focus:border-primary-400 focus:ring-primary-400 rounded-lg transition-all duration-200"
                          />
                        </div>
                        <FieldInfo field={field}/>
                      </div>
                    )}
                  />

                  <form.Field
                    name="trustClient"
                    children={(field) => (
                      <div className="flex items-center space-x-2 pt-2">
                        <div className="flex items-center h-5">
                          <input
                            type="checkbox"
                            id={field.name}
                            name={field.name}
                            checked={field.state.value}
                            onBlur={field.handleBlur}
                            onChange={(e) => field.handleChange(e.target.checked)}
                            className="h-4 w-4 text-primary-600 border-primary-300 rounded focus:ring-primary-500 transition-colors"
                          />
                        </div>
                        <Label
                          htmlFor={field.name}
                          className="text-sm "
                        >
                          Remember this device
                        </Label>
                        <FieldInfo field={field}/>
                      </div>
                    )}
                  />

                  <Button
                    type="submit"
                    className="w-full"
                    isLoading={loginMutation.isPending}
                  >
                    {loginMutation.isPending ? "Signing in..." : "Sign in"}
                  </Button>
                </div>
              </form>
            ) : (
              pendingSessionId && (
                <MfaVerification
                  pendingSessionId={pendingSessionId}
                  onVerificationSuccess={handleMfaSuccess}
                />
              )
            )}
          </div>
        </div>
      </div>

      <div
        className="hidden lg:block lg:w-1/2 bg-gradient-to-br from-primary-700 to-primary-900 relative overflow-hidden">
        <div
          className="absolute inset-0 bg-[url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPjxkZWZzPjxwYXR0ZXJuIGlkPSJwYXR0ZXJuIiB4PSIwIiB5PSIwIiB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHBhdHRlcm5Vbml0cz0idXNlclNwYWNlT25Vc2UiIHBhdHRlcm5UcmFuc2Zvcm09InJvdGF0ZSgxMzUpIj48cmVjdCB4PSIwIiB5PSIwIiB3aWR0aD0iMjAiIGhlaWdodD0iMjAiIGZpbGw9InJnYmEoMjU1LDI1NSwyNTUsMC4wNSkiLz48L3BhdHRlcm4+PC9kZWZzPjxyZWN0IHdpZHRoPSIxMDAlIiBoZWlnaHQ9IjEwMCUiIGZpbGw9InVybCgjcGF0dGVybikiLz48L3N2Zz4=')] opacity-20"></div>
        <div className="absolute inset-0 flex flex-col items-center justify-center p-12">
          <LogoWithText/>
        </div>
      </div>
    </div>
  )


}
