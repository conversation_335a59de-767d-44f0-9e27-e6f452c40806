import {mapValues, omitBy} from "lodash";

export function imageUrl(src: string = "", dims: {
  width?: number;
  height?: number;
  quality?: number;
  format?: "webp" | "jpeg";
}): string | undefined {
  if (!src) {
    return undefined;
  }
  const params = omitBy({
    w: dims.width?.toString(),
    h: dims.height?.toString(),
    q: dims.quality?.toString(),
    f: dims.format
  }, (v) => v === undefined);
  return src + "?" + new URLSearchParams(params as any).toString();
}
