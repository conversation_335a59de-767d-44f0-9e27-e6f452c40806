import {describe, expect, it} from 'vitest';
import {matchPeople, parseAndMatchPayRates, parseFullName} from './payRates';

describe('parseFullName', () => {
  it('should parse LastName, FirstName format', () => {
    const result = parseFullName('<PERSON>, <PERSON>');

    expect(result).toEqual({
      firstName: '<PERSON>',
      lastName: '<PERSON>'
    });
  });

  it('should parse LastName, FirstName MiddleName format with middle name as part of last name', () => {
    const result = parseFullName('<PERSON>, <PERSON>');

    expect(result).toEqual({
      firstName: '<PERSON>',
      lastName: '<PERSON>'
    });
  });

  it('should handle parenthetical nicknames', () => {
    const result = parseFullName('<PERSON>, <PERSON> (<PERSON>)');

    expect(result).toEqual({
      firstName: '<PERSON>',
      lastName: '<PERSON>'
    });
  });

  it('should handle parenthetical nicknames with middle names', () => {
    const result = parseFullName('<PERSON><PERSON>, <PERSON><PERSON> (<PERSON>)');

    expect(result).toEqual({
      firstName: '<PERSON><PERSON>',
      lastName: '<PERSON>'
    });
  });

  it('should parse FirstName LastName format', () => {
    const result = parseFullName('John Doe');

    expect(result).toEqual({
      firstName: 'John',
      lastName: 'Doe'
    });
  });

  it('should parse FirstName MiddleName LastName format with middle name as part of last name', () => {
    const result = parseFullName('Robert James Smith');

    expect(result).toEqual({
      firstName: 'Robert',
      lastName: 'James Smith'
    });
  });

  it('should handle multiple middle names', () => {
    const result = parseFullName('Joshua Emanuel Collado');

    expect(result).toEqual({
      firstName: 'Joshua',
      lastName: 'Emanuel Collado'
    });
  });

  it('should handle multiple middle names with comma format', () => {
    const result = parseFullName('Collado, Joshua Emanuel');

    expect(result).toEqual({
      firstName: 'Joshua',
      lastName: 'Emanuel Collado'
    });
  });

  it('should handle single names', () => {
    const result = parseFullName('Cher');

    expect(result).toEqual({
      firstName: 'Cher',
      lastName: ''
    });
  });

  it('should handle empty strings', () => {
    const result = parseFullName('');

    expect(result).toEqual({
      firstName: '',
      lastName: ''
    });
  });

  it('should handle extra whitespace', () => {
    const result = parseFullName('  John   Doe  ');

    expect(result).toEqual({
      firstName: 'John',
      lastName: 'Doe'
    });
  });

  it('should handle names with suffixes like Jr', () => {
    const result = parseFullName('Martin Luther King Jr');

    expect(result).toEqual({
      firstName: 'Martin',
      lastName: 'Luther King Jr'
    });
  });

  it('should handle hyphenated names', () => {
    const result = parseFullName('Jean-Claude Van Damme');

    expect(result).toEqual({
      firstName: 'Jean-Claude',
      lastName: 'Van Damme'
    });
  });

  it('should handle hyphenated last names with comma format', () => {
    const result = parseFullName('Smith-Jones, Sarah');

    expect(result).toEqual({
      firstName: 'Sarah',
      lastName: 'Smith-Jones'
    });
  });
});

describe('matchPeople', () => {
  it('should match people based on first and last names', () => {
    const existingPeople = [
      { firstName: 'John', lastName: 'Doe', id: '1' },
      { firstName: 'Jane', lastName: 'Smith', id: '2' }
    ];

    const newPeople = [
      { firstName: 'John', lastName: 'Doe', rowIndex: 0 },
      { firstName: 'Jane', lastName: 'Smith', rowIndex: 1 },
      { firstName: 'Bob', lastName: 'Johnson', rowIndex: 2 }
    ];

    const result = matchPeople(existingPeople, newPeople);

    expect(result.size).toBe(3);
    expect(result.get(newPeople[0])).toBe(existingPeople[0]);
    expect(result.get(newPeople[1])).toBe(existingPeople[1]);
    expect(result.get(newPeople[2])).toBe(null);
  });

  it('should match people with slight name variations', () => {
    const existingPeople = [
      { firstName: 'Jane', lastName: 'Smith', id: '2' }
    ];

    const newPeople = [
      { firstName: 'Jane', lastName: 'Smyth', rowIndex: 1 }
    ];

    const result = matchPeople(existingPeople, newPeople);

    expect(result.size).toBe(1);
    expect(result.get(newPeople[0])).toBe(existingPeople[0]);
  });
});

describe('parseAndMatchPayRates', () => {
  it('should parse CSV and match people', async () => {
    const csv = `FULL_NAME,HIRE_DATE,EMPLOYMENT_CATEGORY,JOB,RATE,PAY_BASIS,LAST_CHANGE_DATE
"Doe, John",01/15/2024,Parttime-Regular,"Developer",25,HOURLY,"2025-01-02 00:00:00.0"
"Smith, Jane",02/21/2021,Parttime-Regular,"Manager",30,HOURLY,"2024-11-13 00:00:00.0"
"Johnson, Bob",03/15/2021,Parttime-Regular,"Designer",22,HOURLY,"2025-01-16 00:00:00.0"`;

    const people = [
      { id: '1', firstName: 'John', lastName: 'Doe' },
      { id: '2', firstName: 'Jane', lastName: 'Smith' }
    ];

    const result = await parseAndMatchPayRates({ csv, people });

    expect(result).toMatchInlineSnapshot(`
      Map {
        {
          "firstName": "John",
          "fullName": "Doe, John",
          "job": "Developer",
          "lastName": "Doe",
          "payBasis": "HOURLY",
          "rate": 25,
          "rowIndex": 0,
        } => {
          "firstName": "John",
          "id": "1",
          "lastName": "Doe",
        },
        {
          "firstName": "Jane",
          "fullName": "Smith, Jane",
          "job": "Manager",
          "lastName": "Smith",
          "payBasis": "HOURLY",
          "rate": 30,
          "rowIndex": 1,
        } => {
          "firstName": "Jane",
          "id": "2",
          "lastName": "Smith",
        },
        {
          "firstName": "Bob",
          "fullName": "Johnson, Bob",
          "job": "Designer",
          "lastName": "Johnson",
          "payBasis": "HOURLY",
          "rate": 22,
          "rowIndex": 2,
        } => null,
      }
    `);

    // The third pay rate has no match, so it is mapped to null
    expect(result.size).toBe(3);
  });
});
