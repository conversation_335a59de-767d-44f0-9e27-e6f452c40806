import Fuse from 'fuse.js';
import {includes, join, map, replace, slice, split, trim, toLower} from 'lodash';
import {parsePayRatesCSV, PersonPayRate} from './payRatesParse.ts';

interface PersonName {
  firstName: string;
  lastName: string;
}

interface Person extends PersonName {
  id: string;
}

/**
 * Parses a full name string into first name and last name components
 * For names with middle names, returns multiple entries:
 * - One with middle name as part of first name
 * - One with middle name as part of last name
 *
 * @param fullName - The full name string to parse
 * @returns Array of parsed name objects
 */
export function parseFullName(fullName: string): PersonName {
  const cleanName = trim(replace(fullName, /\s+/g, ' '));

  // Check if we have a comma (LastName, FirstName format)
  if (includes(cleanName, ',')) {
    // Split by comma: "LastName, FirstName MiddleName"
    const parts = split(cleanName, ',');
    const lastName = trim(parts[0]);
    const firstPart = trim(parts[1]);

    // Remove parenthetical content if present
    const firstPartClean = trim(replace(firstPart, /\s*\(.*?\)\s*/g, ''));

    // Split the first part into words
    const firstPartWords = split(firstPartClean, ' ');

    // If there's only one word, it's just a first name
    if (firstPartWords.length === 1) {
      return {
        firstName: firstPartWords[0],
        lastName,
      };
    }

    // If there are multiple words, handle middle names
    const firstName = firstPartWords[0];
    const middleNames = join(slice(firstPartWords, 1), ' ');

    // Middle name as part of last name
    return {
      firstName,
      lastName: `${middleNames} ${lastName}`,
    }
  } else {
    // No comma, assume "FirstName MiddleName LastName" format
    const nameParts = split(trim(replace(cleanName, /\s*\(.*?\)\s*/g, '')), ' ');

    // If only one or two parts, it's simple
    if (nameParts.length <= 2) {
      const firstName = nameParts[0] || '';
      const lastName = nameParts[1] || '';
      return {
        firstName,
        lastName,
      };
    }

    // With 3+ parts, handle middle names and suffixes
    const firstName = nameParts[0];

    // Check for common suffixes (Jr, Sr, II, III, IV, etc.) that should be part of the last name
    let lastNameIndex = nameParts.length - 1;
    let lastNameParts = [nameParts[lastNameIndex]];

    // Check if the second-to-last part is a suffix
    const suffixRegex = /^(Jr|Sr|I{2,3}|IV|V|VI)$/i;
    if (nameParts.length > 2 && suffixRegex.test(nameParts[lastNameIndex])) {
      // If the last part is a suffix, include the previous part as the main last name
      lastNameParts = [nameParts[lastNameIndex - 1], nameParts[lastNameIndex]];
      lastNameIndex = lastNameIndex - 1;
    }

    const lastName = join(lastNameParts, ' ');
    const middleNames = join(slice(nameParts, 1, lastNameIndex), ' ');

    // Middle name as part of last name
    return {
      firstName,
      lastName: `${middleNames} ${lastName}`,
    }
  }
}


/**
 * Calculate similarity score between two names with improved matching logic
 */
function calculateNameSimilarity(name1: string, name2: string): number {
  const lower1 = trim(toLower(name1));
  const lower2 = trim(toLower(name2));

  // Check for exact matches first
  if (lower1 === lower2) {
    return 1.0; // Perfect match
  }

  // Check for partial matches (one name contains the other)
  // This helps with cases like "John Michael" vs "John" or "J." vs "John"
  if (includes(lower1, lower2) || includes(lower2, lower1)) {
    return 0.8; // Good partial match
  }

  // Use Fuse.js for fuzzy matching as fallback
  const fuse = new Fuse([name1], {
    includeScore: true,
    threshold: 0.6,
  });

  const result = fuse.search(name2);
  if (result.length > 0 && result[0].score !== undefined) {
    return 1 - result[0].score; // Convert Fuse score to similarity
  }

  return 0;
}

/**
 * Match objects between two arrays of people based on first and last names using improved fuzzy search
 * @param existingPeople - First array of objects
 * @param newPeople - Second array of objects
 * @returns Map of id from array1 to id from array2 (only includes matches)
 */
export function matchPeople<TPerson1 extends PersonName, TPerson2 extends PersonName>(existingPeople: TPerson1[], newPeople: TPerson2[]): Map<TPerson2, TPerson1 | null> {
  const newPersonToMatch = new Map<TPerson2, TPerson1 | null>();

  for (const newPerson of newPeople) {
    let bestMatch: TPerson1 | null = null;
    let bestScore = 0;

    for (const existingPerson of existingPeople) {
      // Calculate similarity for first and last names
      const firstNameSimilarity = calculateNameSimilarity(
        newPerson.firstName || '',
        existingPerson.firstName || ''
      );
      const lastNameSimilarity = calculateNameSimilarity(
        newPerson.lastName || '',
        existingPerson.lastName || ''
      );

      // Weighted score: last name is more important (0.6) than first name (0.4)
      const totalScore = (firstNameSimilarity * 0.4) + (lastNameSimilarity * 0.6);

      // Require minimum thresholds for both names to avoid false positives
      const minFirstNameScore = 0.6;
      const minLastNameScore = 0.7;
      const minTotalScore = 0.65;

      if (firstNameSimilarity >= minFirstNameScore &&
          lastNameSimilarity >= minLastNameScore &&
          totalScore >= minTotalScore &&
          totalScore > bestScore) {
        bestMatch = existingPerson;
        bestScore = totalScore;
      }
    }

    newPersonToMatch.set(newPerson, bestMatch);
  }

  return newPersonToMatch;
}

/**
 * Given a pay rates CSV file, parse it and match the rows against the store's people.
 * Returns a map of CSV row to store person.
 * @param csv
 * @param people
 */
export async function parseAndMatchPayRates({csv, people}: {
  csv: File | string;
  people: Person[];
}): Promise<Map<PersonPayRate, Person | null>> {
  const parsedPeople = await parsePayRatesCSV(csv);
  const namedPeople = map(parsedPeople, (person) => {
    const personName = parseFullName(person.fullName);

    return {
      ...person,
      ...personName
    }
  });

  return matchPeople(people, namedPeople);
}
