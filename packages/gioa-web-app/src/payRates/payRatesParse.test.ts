import {describe, expect, it} from 'vitest';
import {parsePayRatesCSV} from './payRatesParse';
import {dollars} from '../../../api/src/scheduling/metrics/dollars';

describe('parsePayRatesCSV', () => {
  it('should parse a CSV string with pay rates correctly', async () => {
    const csvContent = `FULL_NAME,HIRE_DATE,EMPLOYMENT_CATEGORY,JOB,RATE,PAY_BASIS,LAST_CHANGE_DATE
"<PERSON>e, <PERSON>",01/15/2024,Parttime-Regular,"Team Member - Non-exempt",14.5,HOURLY,"2025-01-02 00:00:00.0"
"<PERSON>, <PERSON>",02/21/2021,Parttime-Regular,"Manager - Non-exempt",22,HOURLY,"2024-11-13 00:00:00.0"
"<PERSON>, <PERSON>",03/15/2021,Parttime-Regular,"Assistant Mgr - Non-exempt",18.25,HOURLY,"2025-01-16 00:00:00.0"
"<PERSON>, <PERSON>",04/01/2023,Parttime-Regular,"Team Member - Non-exempt",14.75,HOURLY,"2025-02-02 00:00:00.0"`;

    const result = await parsePayRatesCSV(csvContent);

    expect(result.length).toBe(4);

    expect(result[0]).toEqual({
      rowIndex: 0,
      fullName: 'Doe, John A',
      job: 'Team Member - Non-exempt',
      rate: dollars(14.5),
      payBasis: 'HOURLY'
    });

    expect(result[1]).toEqual({
      rowIndex: 1,
      fullName: 'Smith, Jane',
      job: 'Manager - Non-exempt',
      rate: dollars(22),
      payBasis: 'HOURLY'
    });

    expect(result[2]).toEqual({
      rowIndex: 2,
      fullName: 'Johnson, Robert',
      job: 'Assistant Mgr - Non-exempt',
      rate: dollars(18.25),
      payBasis: 'HOURLY'
    });

    expect(result[3]).toEqual({
      rowIndex: 3,
      fullName: 'Brown, Michael',
      job: 'Team Member - Non-exempt',
      rate: dollars(14.75),
      payBasis: 'HOURLY'
    });
  });

  it('should handle different pay basis types', async () => {
    const csvContent = `FULL_NAME,HIRE_DATE,EMPLOYMENT_CATEGORY,JOB,RATE,PAY_BASIS,LAST_CHANGE_DATE
"Wilson, James",01/01/2020,Fulltime-Regular,"Executive",5000,MONTHLY,"2024-01-01 00:00:00.0"
"Taylor, Emma",02/02/2020,Parttime-Regular,"Consultant",45.75,HOURLY,"2024-02-02 00:00:00.0"
"Miller, David",03/03/2020,Fulltime-Regular,"Contractor",2500,efwawef,"2024-03-03 00:00:00.0"`;

    const result = await parsePayRatesCSV(csvContent);

    expect(result.length).toBe(3);

    expect(result[0].payBasis).toBe('MONTHLY');
    expect(result[0].rate).toEqual(dollars(5000));

    expect(result[1].payBasis).toBe('HOURLY');
    expect(result[1].rate).toEqual(dollars(45.75));

    expect(result[2].payBasis).toBe('UNKNOWN');
    expect(result[2].rate).toEqual(dollars(2500));
  });

  it('should handle quotes in the full name', async () => {
    const csvContent = `FULL_NAME,HIRE_DATE,EMPLOYMENT_CATEGORY,JOB,RATE,PAY_BASIS,LAST_CHANGE_DATE
"""Davis, John""",01/01/2020,Fulltime-Regular,"Team Lead",25,HOURLY,"2024-01-01 00:00:00.0"`;

    const result = await parsePayRatesCSV(csvContent);

    expect(result.length).toBe(1);
    expect(result[0].fullName).toBe('Davis, John');
  });
});
