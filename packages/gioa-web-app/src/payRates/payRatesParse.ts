import <PERSON> from 'papaparse';
import {map, trim} from 'lodash';
import {Dollars, dollars} from '../../../api/src/scheduling/metrics/dollars';

export type PayBasis = "HOURLY" | "MONTHLY" | "UNKNOWN";

export interface PersonPayRate {
  rowIndex: number;
  fullName: string;
  job: string;
  rate: Dollars;
  payBasis: PayBasis;
}

interface CSVRow {
  FULL_NAME: string;
  HIRE_DATE: string;
  EMPLOYMENT_CATEGORY: string;
  JOB: string;
  RATE: string;
  PAY_BASIS: string;
  LAST_CHANGE_DATE: string;
  [key: string]: string; // Allow for additional columns
}

/**
 * Parses CSV data containing pay rate information and returns a list of PersonPayRate objects
 *
 * @param csv - Either a File object or a string containing CSV data
 * @returns Promise that resolves to an array of PersonPayRate objects
 */
export function parsePayRatesCSV(csv: File | string): Promise<PersonPayRate[]> {
  return new Promise((resolve, reject) => {
    Papa.parse<CSVRow>(csv, {
      header: true,
      skipEmptyLines: true,
      transformHeader: (header) => trim(header),
      complete: (results) => {
        const payRates = map(results.data, (row, index) => {
          // Parse the rate as a number
          const rateValue = parseFloat(row.RATE);

          // Determine the pay basis
          let payBasis: PayBasis = "UNKNOWN";
          if (row.PAY_BASIS === "HOURLY") {
            payBasis = "HOURLY";
          } else if (row.PAY_BASIS === "MONTHLY") {
            payBasis = "MONTHLY";
          }

          return {
            rowIndex: index,
            fullName: trim(row.FULL_NAME.replace(/"/g, '')), // Remove quotes from the name
            job: trim(row.JOB),
            rate: dollars(rateValue),
            payBasis
          };
        });

        resolve(payRates);
      },
      error: (error) => {
        reject(error);
      }
    });
  });
}
