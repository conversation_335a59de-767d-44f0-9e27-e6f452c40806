import {describe, expect, it} from 'vitest';
import {DailyTimeRange} from "../../api/src/timeSchemas.ts";
import {DayPart} from "../../api/src/scheduleSchemas.ts";
import {
  dateRangeToIncrementRange,
  dateToIncrement,
  fillRangeGaps,
  generateIsoWeekDates,
  genShifts,
  getDayPartsCssBackground,
  getDayScheduleRows,
  incrementTo24HourTime,
  incrementToFriendlyTime,
  NumberRange,
  parseHtmlDateInputToLocalDate,
  parseHtmlTimeInputToLocalTime,
  storeTimeToIncrement,
  takeFromGenerator
} from "../../api/src/scheduleBuilder.util.ts";
import {testPeople, testSched} from "@/src/components/ScheduleBuilder.util.tsx";
import {map} from "lodash";
import {constructShift} from "../../api/src/shift.ts";

describe('ScheduleBuilder.util', () => {
  const testStoreHours: DailyTimeRange = {
    start: "5:00",
    end: "22:00"
  };

  describe('storeTimeToIncrement', () => {
    it('should convert store opening time to increment 0', () => {
      expect(storeTimeToIncrement(testStoreHours, "5:00")).toBe(0);
    });

    it('should convert times within store hours to correct increments', () => {
      expect(storeTimeToIncrement(testStoreHours, "6:00")).toBe(4);
      expect(storeTimeToIncrement(testStoreHours, "12:30")).toBe(30);
      expect(storeTimeToIncrement(testStoreHours, "21:45")).toBe(67);
    });

    it('should handle times outside store hours', () => {
      expect(storeTimeToIncrement(testStoreHours, "4:00")).toBe(-4);
      expect(storeTimeToIncrement(testStoreHours, "23:00")).toBe(72);
    });
  });

  describe('incrementTo24HourTime', () => {
    it('should convert increment 0 to store opening time', () => {
      expect(incrementTo24HourTime(testStoreHours, 0)).toBe("05:00");
    });

    it('should convert increments within store hours to correct times', () => {
      expect(incrementTo24HourTime(testStoreHours, 4)).toBe("06:00");
      expect(incrementTo24HourTime(testStoreHours, 30)).toBe("12:30");
      expect(incrementTo24HourTime(testStoreHours, 67)).toBe("21:45");
    });

    it('should handle increments outside store hours', () => {
      expect(incrementTo24HourTime(testStoreHours, -4)).toBe("04:00");
      expect(incrementTo24HourTime(testStoreHours, 72)).toBe("23:00");
    });
  });

  describe('incrementToFriendlyTime', () => {
    it('should convert increment 0 to friendly store opening time', () => {
      expect(incrementToFriendlyTime(testStoreHours, 0)).toBe("5:00a");
    });

    it('should convert increments to friendly times', () => {
      expect(incrementToFriendlyTime(testStoreHours, 4)).toBe("6:00a");
      expect(incrementToFriendlyTime(testStoreHours, 30)).toBe("12:30p");
      expect(incrementToFriendlyTime(testStoreHours, 67)).toBe("9:45p");
    });

    it('should handle noon and midnight correctly', () => {
      const midnightHours: DailyTimeRange = {start: "00:00", end: "23:59"};
      expect(incrementToFriendlyTime(midnightHours, 0)).toBe("12:00a");
      expect(incrementToFriendlyTime(midnightHours, 48)).toBe("12:00p");
    });
  });

  describe('genShifts', () => {
    it('should generate correct ScheduleRowInfo array for a given day', () => {
      const shifts = genShifts({draft: testSched.draft, people: testPeople, dayOfWeek: 1, positions: [], shiftOffers: []});
      expect(shifts).toHaveLength(3); // area + shift + add
      expect(shifts[0]).toMatchObject({
        type: "area",
        title: "Team Lead",
        id: expect.any(String),
        start: 0,
        end: 0,
        areaId: null
      });
      expect(shifts[1]).toMatchObject({
        type: "shift",
        start: 0,
        end: 28,
        title: "",
        id: expect.any(String),
        assignedTo: expect.objectContaining({
          id: "test6",
          firstName: "John",
          lastName: "Brown"
        }),
        areaId: expect.any(String)
      });
      expect(shifts[2]).toMatchObject({
        type: "add",
        title: "Add shift",
        id: expect.stringContaining("add"),
        start: 0,
        end: 0,
        areaId: expect.any(String)
      });
    });

    it('should return an empty array for an invalid day', () => {
      const shifts = genShifts({draft: testSched.draft, people: testPeople, dayOfWeek: 8, positions: [], shiftOffers: []});
      expect(shifts).toHaveLength(0);
    });
  });

  describe('dateToIncrement', () => {
    it("should convert a date to an increment", async () => {
      const result = dateToIncrement({
        date: new Date(2024, 6, 27, 5, 30), // Saturday, July 27, 2024, 5:30AM MDT
        isoWeek: {year: 2024, week: 30}, // Week 30 of 2024, which is July 22-28
        dayOfWeek: 6,
        storeHours: {
          start: "5:00", // 5AM
          end: "22:00" // 10PM
        },
        timezone: "America/Denver"
      });

      expect(result).toEqual({increment: 2});
    });

    it("should allow dates at store hours boundaries", async () => {
      const result = dateToIncrement({
        date: new Date(2024, 6, 27, 5, 0), // Saturday, July 27, 2024, 5:00AM MDT
        isoWeek: {year: 2024, week: 30}, // Week 30 of 2024, which is July 22-28
        dayOfWeek: 6,
        storeHours: {
          start: "5:00", // 5AM
          end: "22:00" // 10PM
        },
        timezone: "America/Denver"
      });

      expect(result).toEqual({increment: 0});

      const result2 = dateToIncrement({
        date: new Date(2024, 6, 27, 22, 0), // Saturday, July 27, 2024, 10:00PM MDT
        isoWeek: {year: 2024, week: 30}, // Week 30 of 2024, which is July 22-28
        dayOfWeek: 6,
        storeHours: {
          start: "5:00", // 5AM
          end: "22:00" // 10PM
        },
        timezone: "America/Denver"
      });

      expect(result2).toEqual({increment: 68});
    });

    it("should return negative if date is before of the store hours", async () => {
      const result = dateToIncrement({
        date: new Date(2024, 6, 27, 0, 0), // Saturday, July 27, 2024, 12:00AM MDT
        isoWeek: {year: 2024, week: 30}, // Week 30 of 2024, which is July 22-28
        dayOfWeek: 6,
        storeHours: {
          start: "5:00", // 5AM
          end: "22:00" // 10PM
        },
        timezone: "America/Denver"
      });

      // Date is BEFORE store hours, so negative number expected
      if (typeof result !== "number") {
        throw new Error("Expected a number");
      }
      expect(result < 0).toBe(true);
    });

    it("should return positive if date is after of the store hours", async () => {
      const result = dateToIncrement({
        date: new Date(2024, 6, 27, 23, 30), // Saturday, July 27, 2024, 11:30PM MDT
        isoWeek: {year: 2024, week: 30}, // Week 30 of 2024, which is July 22-28
        dayOfWeek: 6,
        storeHours: {
          start: "5:00", // 5AM
          end: "22:00" // 10PM
        },
        timezone: "America/Denver"
      });

      // Date is AFTER store hours, so positive number expected
      if (typeof result !== "number") {
        throw new Error("Expected a number");
      }
      expect(result > 0).toBe(true);
    });

    it("should return negative if the date is before the ISO day", async () => {
      const result = dateToIncrement({
        date: new Date(2024, 6, 25, 23, 30), // Thursday, July 25, 2024, 11:30PM MDT
        isoWeek: {year: 2024, week: 30}, // Week 30 of 2024, which is July 22-28
        dayOfWeek: 7,
        storeHours: {
          start: "5:00", // 5AM
          end: "22:00" // 10PM
        },
        timezone: "America/Denver"
      });

      // Date is BEFORE the ISO day, so negative number expected
      if (typeof result !== "number") {
        throw new Error("Expected a number");
      }
      expect(result < 0).toBe(true);
    });

    it("should return positive if the date is after the ISO day", async () => {
      const result = dateToIncrement({
        date: new Date(2024, 6, 25, 23, 30), // Thursday, July 25, 2024, 11:30PM MDT
        isoWeek: {year: 2024, week: 30}, // Week 30 of 2024, which is July 22-28
        dayOfWeek: 1,
        storeHours: {
          start: "5:00", // 5AM
          end: "22:00" // 10PM
        },
        timezone: "America/Denver"
      });

      // Date is AFTER the ISO day, so positive number expected
      if (typeof result !== "number") {
        throw new Error("Expected a number");
      }
      expect(result > 0).toBe(true);
    });

    it("should return negative if the date is before the ISO week", async () => {
      const result = dateToIncrement({
        date: new Date(2024, 5, 27, 23, 30), // Saturday, May 27, 2024, 11:30PM MDT
        isoWeek: {year: 2024, week: 30}, // Week 30 of 2024, which is July 22-28
        dayOfWeek: 6,
        storeHours: {
          start: "5:00", // 5AM
          end: "22:00" // 10PM
        },
        timezone: "America/Denver"
      });

      // Date is BEFORE the ISO week, so negative number expected
      if (typeof result !== "number") {
        throw new Error("Expected a number");
      }
      expect(result < 0).toBe(true);
    });

    it("should return positive if the date is after the ISO week", async () => {
      const result = dateToIncrement({
        date: new Date(2024, 8, 3, 23, 30), // Saturday, August 3, 2024, 11:30PM MDT
        isoWeek: {year: 2024, week: 30}, // Week 30 of 2024, which is July 22-28
        dayOfWeek: 6,
        storeHours: {
          start: "5:00", // 5AM
          end: "22:00" // 10PM
        },
        timezone: "America/Denver"
      });

      // Date is AFTER the ISO week, so positive number expected
      if (typeof result !== "number") {
        throw new Error("Expected a number");
      }
      expect(result > 0).toBe(true);
    });
  });

  describe("dateRangeToIncrementRange", () => {
    it("should get increment range within the store hours", async () => {
      const incRange = dateRangeToIncrementRange({
        range: {
          start: new Date(2024, 6, 27, 5, 30), // Saturday, July 27, 2024, 5:30AM MDT
          end: new Date(2024, 6, 27, 12, 45) // Saturday, July 27, 2024, 12:45PM MDT
        },
        isoWeek: {year: 2024, week: 30}, // Week 30 of 2024, which is July 22-28
        storeHours: {
          start: "5:00", // 5AM
          end: "22:00" // 10PM
        },
        dayOfWeek: 6,
        timezone: "America/Denver"
      });

      expect(incRange).toEqual({start: 2, end: 31});
    });

    it("should get increment range with start before store hours", async () => {
      const incRange = dateRangeToIncrementRange({
        range: {
          start: new Date(2024, 6, 27, 4, 30), // Saturday, July 27, 2024, 4:30AM MDT
          end: new Date(2024, 6, 27, 12, 45) // Saturday, July 27, 2024, 12:45PM MDT
        },
        isoWeek: {year: 2024, week: 30}, // Week 30 of 2024, which is July 22-28
        storeHours: {
          start: "5:00", // 5AM
          end: "22:00" // 10PM
        },
        dayOfWeek: 6,
        timezone: "America/Denver"
      });

      expect(incRange).toEqual({start: 0, end: 31});
    });

    it("should get increment range with end after store hours", async () => {
      const incRange = dateRangeToIncrementRange({
        range: {
          start: new Date (2024, 6, 27, 5, 30), // Saturday, July 27, 2024, 5:30AM MDT
          end: new Date(2024, 6, 27, 23, 45) // Saturday, July 27, 2024, 11:45PM MDT
        },
        isoWeek: {year: 2024, week: 30}, // Week 30 of 2024, which is July 22-28
        storeHours: {
          start: "5:00", // 5AM
          end: "22:00" // 10PM
        },
        dayOfWeek: 6,
        timezone: "America/Denver"
      });

      expect(incRange).toEqual({start: 2, end: 68});
    });

    it("should return full day if start is before store hours and end is after store hours", async () => {
      const incRange = dateRangeToIncrementRange({
        range: {
          start: new Date (2024, 6, 27, 4, 30), // Saturday, July 27, 2024, 4:30AM MDT
          end: new Date(2024, 6, 27, 23, 45) // Saturday, July 27, 2024, 11:45PM MDT
        },
        isoWeek: {year: 2024, week: 30}, // Week 30 of 2024, which is July 22-28
        storeHours: {
          start: "5:00", // 5AM
          end: "22:00" // 10PM
        },
        dayOfWeek: 6,
        timezone: "America/Denver"
      });

      expect(incRange).toEqual({start: 0, end: 68})
    });

    it("should return null if start is before store hours and end is before store hours", async () => {
      const incRange = dateRangeToIncrementRange({
        range: {
          start: new Date(2024, 6, 27, 4, 30), // Saturday, July 27, 2024, 4:30AM MDT
          end: new Date(2024, 6, 27, 4, 45) // Saturday, July 27, 2024, 4:45AM MDT
        },
        isoWeek: {year: 2024, week: 30}, // Week 30 of 2024, which is July 22-28
        storeHours: {
          start: "5:00", // 5AM
          end: "22:00" // 10PM
        },
        dayOfWeek: 6,
        timezone: "America/Denver"
      });

      expect(incRange).toBeNull();
    });

    it("should return null if start is after store hours and end is after store hours", async () => {
      const incRange = dateRangeToIncrementRange({
        range: {
          start: new Date(2024, 6, 27, 23, 30), // Saturday, July 27, 2024, 11:30PM MDT
          end: new Date(2024, 6, 27, 23, 45) // Saturday, July 27, 2024, 11:45PM MDT
        },
        isoWeek: {year: 2024, week: 30}, // Week 30 of 2024, which is July 22-28
        storeHours: {
          start: "5:00", // 5AM
          end: "22:00" // 10PM
        },
        dayOfWeek: 6,
        timezone: "America/Denver"
      });

      expect(incRange).toBeNull();
    });
  });

  describe('getDayPartsCssBackground', () => {
    const storeHours: DailyTimeRange = {start: '09:00', end: '17:00'};
    const incrementWidth = 10; // 10px per increment, 40px per hour

    it('should generate correct CSS for a single day part', () => {
      const dayParts: DayPart[] = [
        {range: {start: '09:00', end: '17:00'}, color: '#FF0000', title: "test"}
      ];
      const result = getDayPartsCssBackground({storeHours, incrementWidth, dayParts});
      expect(result).toMatchInlineSnapshot(`"linear-gradient(to right, #FF0000 0px 320px, transparent 320px)"`);
    });

    it('should generate correct CSS for two day parts', () => {
      const dayParts: DayPart[] = [
        {range: {start: '09:00', end: '13:00'}, color: '#FF0000', title: "test"},
        {range: {start: '13:00', end: '17:00'}, color: '#00FF00', title: "test"}
      ];
      const result = getDayPartsCssBackground({storeHours, incrementWidth, dayParts});
      expect(result).toMatchInlineSnapshot(`"linear-gradient(to right, #FF0000 0px 160px, #00FF00 160px 320px, transparent 320px)"`);
    });

    it('should generate correct CSS for three day parts', () => {
      const dayParts: DayPart[] = [
        {range: {start: '09:00', end: '12:00'}, color: '#FF0000', title: "test"},
        {range: {start: '12:00', end: '15:00'}, color: '#00FF00', title: "test"},
        {range: {start: '15:00', end: '17:00'}, color: '#0000FF', title: "test"}
      ];
      const result = getDayPartsCssBackground({storeHours, incrementWidth, dayParts});
      expect(result).toMatchInlineSnapshot(`"linear-gradient(to right, #FF0000 0px 120px, #00FF00 120px 240px, #0000FF 240px 320px, transparent 320px)"`);
    });

    it('should handle day parts with gaps', () => {
      const dayParts: DayPart[] = [
        {range: {start: '09:00', end: '11:00'}, color: '#FF0000', title: "test"},
        {range: {start: '13:00', end: '17:00'}, color: '#00FF00', title: "test"}
      ];
      const result = getDayPartsCssBackground({storeHours, incrementWidth, dayParts});
      expect(result).toMatchInlineSnapshot(`"linear-gradient(to right, #FF0000 0px 80px, transparent 80px 160px, #00FF00 160px 320px, transparent 320px)"`);
    });

    it('should handle day parts with overlaps', () => {
      const dayParts: DayPart[] = [
        {range: {start: '09:00', end: '13:30'}, color: '#FF0000', title: "test"},
        {range: {start: '13:00', end: '17:00'}, color: '#00FF00', title: "test"}
      ];
      const result = getDayPartsCssBackground({storeHours, incrementWidth, dayParts});
      expect(result).toMatchInlineSnapshot(`"linear-gradient(to right, #FF0000 0px 180px, #00FF00 180px 320px, transparent 320px)"`);
    });

    it('should handle day parts outside store hours', () => {
      const dayParts: DayPart[] = [
        {range: {start: '08:00', end: '10:00'}, color: '#FF0000', title: "test"},
        {range: {start: '16:00', end: '18:00'}, color: '#00FF00', title: "test"}
      ];
      const result = getDayPartsCssBackground({storeHours, incrementWidth, dayParts});
      expect(result).toMatchInlineSnapshot(`"linear-gradient(to right, #FF0000 0px 40px, transparent 40px 280px, #00FF00 280px 320px, transparent 320px)"`);
    });

    it('should return empty gradient for no day parts', () => {
      const result = getDayPartsCssBackground({storeHours, incrementWidth, dayParts: []});
      expect(result).toMatchInlineSnapshot(`""`);
    });

    it('should handle 15-minute increments correctly', () => {
      const dayParts: DayPart[] = [
        {range: {start: '09:00', end: '09:15'}, color: '#FF0000', title: "test"},
        {range: {start: '09:15', end: '09:30'}, color: '#00FF00', title: "test"},
        {range: {start: '09:30', end: '09:45'}, color: '#0000FF', title: "test"},
        {range: {start: '09:45', end: '10:00'}, color: '#FFFF00', title: "test"}
      ];
      const result = getDayPartsCssBackground({storeHours, incrementWidth, dayParts});
      expect(result).toMatchInlineSnapshot(`"linear-gradient(to right, #FF0000 0px 10px, #00FF00 10px 20px, #0000FF 20px 30px, #FFFF00 30px 40px, transparent 40px 320px, transparent 320px)"`);
    });
  });

  describe('fillRangeGaps', () => {
    it('should fill gaps in a simple range', () => {
      const intervals: NumberRange<string>[] = [
        {start: 3, end: 4, meta: 'A'},
        {start: 6, end: 8, meta: 'B'}
      ];
      const result = fillRangeGaps(1, 10, intervals);
      expect(result).toEqual([
        {start: 1, end: 3},
        {start: 3, end: 4, meta: 'A'},
        {start: 4, end: 6},
        {start: 6, end: 8, meta: 'B'},
        {start: 8, end: 10}
      ]);
    });

    it('should handle overlapping intervals', () => {
      const intervals: NumberRange<string>[] = [
        {start: 2, end: 5, meta: 'A'},
        {start: 4, end: 7, meta: 'B'},
        {start: 6, end: 9, meta: 'C'}
      ];
      const result = fillRangeGaps(1, 10, intervals);
      expect(result).toEqual([
        {start: 1, end: 2},
        {start: 2, end: 5, meta: 'A'},
        {start: 5, end: 7, meta: 'B'},
        {start: 7, end: 9, meta: 'C'},
        {start: 9, end: 10}
      ]);
    });

    it('should handle intervals outside the range', () => {
      const intervals: NumberRange<string>[] = [
        {start: 0, end: 2, meta: 'A'},
        {start: 9, end: 11, meta: 'B'}
      ];
      const result = fillRangeGaps(1, 10, intervals);
      expect(result).toEqual([
        {start: 1, end: 2, meta: 'A'},
        {start: 2, end: 9},
        {start: 9, end: 10, meta: 'B'}
      ]);
    });

    it('should handle no intervals', () => {
      const result = fillRangeGaps(1, 10, []);
      expect(result).toEqual([{start: 1, end: 10}]);
    });

    it('should handle intervals covering the entire range', () => {
      const intervals: NumberRange<string>[] = [
        {start: 1, end: 5, meta: 'A'},
        {start: 5, end: 10, meta: 'B'}
      ];
      const result = fillRangeGaps(1, 10, intervals);
      expect(result).toEqual([
        {start: 1, end: 5, meta: 'A'},
        {start: 5, end: 10, meta: 'B'}
      ]);
    });

    it('should handle a single interval', () => {
      const intervals: NumberRange<string>[] = [
        {start: 4, end: 7, meta: 'A'}
      ];
      const result = fillRangeGaps(1, 10, intervals);
      expect(result).toEqual([
        {start: 1, end: 4},
        {start: 4, end: 7, meta: 'A'},
        {start: 7, end: 10}
      ]);
    });

    it('should handle adjacent intervals', () => {
      const intervals: NumberRange<string>[] = [
        {start: 1, end: 3, meta: 'A'},
        {start: 3, end: 5, meta: 'B'},
        {start: 5, end: 10, meta: 'C'}
      ];
      const result = fillRangeGaps(1, 10, intervals);
      expect(result).toEqual([
        {start: 1, end: 3, meta: 'A'},
        {start: 3, end: 5, meta: 'B'},
        {start: 5, end: 10, meta: 'C'}
      ]);
    });

    it('should handle decimal numbers', () => {
      const intervals: NumberRange<string>[] = [
        {start: 2.5, end: 4.5, meta: 'A'},
        {start: 6.5, end: 8.5, meta: 'B'}
      ];
      const result = fillRangeGaps(1, 10, intervals);
      expect(result).toEqual([
        {start: 1, end: 2.5},
        {start: 2.5, end: 4.5, meta: 'A'},
        {start: 4.5, end: 6.5},
        {start: 6.5, end: 8.5, meta: 'B'},
        {start: 8.5, end: 10}
      ]);
    });

    it('should preserve meta data', () => {
      const intervals: NumberRange<{ id: number }>[] = [
        {start: 2, end: 4, meta: {id: 1}},
        {start: 6, end: 8, meta: {id: 2}}
      ];
      const result = fillRangeGaps(1, 10, intervals);
      expect(result).toEqual([
        {start: 1, end: 2},
        {start: 2, end: 4, meta: {id: 1}},
        {start: 4, end: 6},
        {start: 6, end: 8, meta: {id: 2}},
        {start: 8, end: 10}
      ]);
    });

    it('should handle intervals in reverse order', () => {
      const intervals: NumberRange<string>[] = [
        {start: 6, end: 8, meta: 'B'},
        {start: 3, end: 4, meta: 'A'}
      ];
      const result = fillRangeGaps(1, 10, intervals);
      expect(result).toEqual([
        {start: 1, end: 3},
        {start: 3, end: 4, meta: 'A'},
        {start: 4, end: 6},
        {start: 6, end: 8, meta: 'B'},
        {start: 8, end: 10}
      ]);
    });
  });
});

describe('generateIsoWeekDates', () => {
  it('should generate correct sequence starting mid-year', () => {
    const generator = generateIsoWeekDates({year: 2023, week: 23}, "America/Denver");
    const result = takeFromGenerator(generator, 5);

    expect(result).toEqual([
      {year: 2023, week: 23},
      {year: 2023, week: 24},
      {year: 2023, week: 25},
      {year: 2023, week: 26},
      {year: 2023, week: 27},
    ]);
  });

  it('should handle year-end transition correctly', () => {
    const generator = generateIsoWeekDates({year: 2023, week: 51}, "America/Denver");
    const result = takeFromGenerator(generator, 5);

    expect(result).toEqual([
      {year: 2023, week: 51},
      {year: 2023, week: 52},
      {year: 2024, week: 1},
      {year: 2024, week: 2},
      {year: 2024, week: 3},
    ]);
  });

  it('should handle leap year (2024) correctly', () => {
    const generator = generateIsoWeekDates({year: 2024, week: 51}, "America/Denver");
    const result = takeFromGenerator(generator, 5);

    expect(result).toEqual([
      {year: 2024, week: 51},
      {year: 2024, week: 52},
      {year: 2025, week: 1},
      {year: 2025, week: 2},
      {year: 2025, week: 3},
    ]);
  });

  it('should handle year with 53 weeks (2020) correctly', () => {
    const generator = generateIsoWeekDates({year: 2020, week: 51}, "America/Denver");
    const result = takeFromGenerator(generator, 5);

    expect(result).toEqual([
      {year: 2020, week: 51},
      {year: 2020, week: 52},
      {year: 2020, week: 53},
      {year: 2021, week: 1},
      {year: 2021, week: 2},
    ]);
  });
});

describe("date utils", () => {
  it("should parse HTML date properly", async () => {
    const date = parseHtmlDateInputToLocalDate("2024-07-27");
    expect(date.toISOString()).toBe("2024-07-27T06:00:00.000Z");
  });

  it("should parse HTML time", async () => {
    const datetime = parseHtmlTimeInputToLocalTime("05:30");
    expect(datetime.toISOString()).toBe("2000-01-01T12:30:00.000Z");
  });
});

describe("getDayScheduleRows", () => {
  it("should sort by start time", () => {
    const isoWeek = {
      year: 2024,
      week: 1,
      day: 1
    };
    const rows = getDayScheduleRows({
      isoWeek: isoWeek,
      shifts: map([{
        assignedPersonId: "testPerson1",
        id: "testShift1",
        shiftAreaId: "area1",
        isoWeek: isoWeek,
        range: {
          start: "09:00",
          end: "17:00"
        }
      }, {
        assignedPersonId: "testPerson1",
        id: "testShift2",
        shiftAreaId: "area1",
        isoWeek: isoWeek,
        range: {
          start: "05:00",
          end: "08:00"
        }
      }], s => {
        return {
          ...constructShift(s),
          isoWeek: s.isoWeek
        }
      }),
      timeOff: [{
        // the time off starts at 8:30AM Denver time, and ends at 12:30PM Denver time
        // Note that Vitest has America/Denver as the default timezone in our global settings, so that's why this works
        start: new Date(2024, 0, 1, 8, 30),
        end: new Date(2024, 0, 1, 12, 30)
      }],
      timezone: "America/Denver"
    });

    // testShift2, time off start, testShift1, time off end
    expect(rows).toMatchInlineSnapshot(`
      [
        {
          "activities": [],
          "assignedPersonId": "testPerson1",
          "id": "testShift2",
          "isoWeek": {
            "day": 1,
            "week": 1,
            "year": 2024,
          },
          "range": {
            "end": "08:00",
            "start": "05:00",
          },
          "shiftAreaId": "area1",
        },
        {
          "time": "08:30",
          "type": "timeOffStart",
        },
        {
          "activities": [],
          "assignedPersonId": "testPerson1",
          "id": "testShift1",
          "isoWeek": {
            "day": 1,
            "week": 1,
            "year": 2024,
          },
          "range": {
            "end": "17:00",
            "start": "09:00",
          },
          "shiftAreaId": "area1",
        },
        {
          "time": "12:30",
          "type": "timeOffEnd",
        },
      ]
    `);

  })

  it("should not show time off end if it's not on this day", () => {
    const isoWeek = {
      year: 2024,
      week: 1,
      day: 1
    };
    const rows = getDayScheduleRows({
      isoWeek: isoWeek,
      shifts: map([{
        assignedPersonId: "testPerson1",
        id: "testShift1",
        shiftAreaId: "area1",
        isoWeek: isoWeek,
        range: {
          start: "09:00",
          end: "17:00"
        }
      }], s => {
        return {
          ...constructShift(s),
          isoWeek: s.isoWeek
        }
      }),
      timeOff: [{
        // the time off starts at 8:30AM Denver time, and ends at 12:30PM Denver time
        // Note that Vitest has America/Denver as the default timezone in our global settings, so that's why this works
        start: new Date(2024, 0, 1, 8, 30),
        end: new Date(2024, 0, 3, 12, 30)
      }],
      timezone: "America/Denver"
    });

    // time off start (8:30), testShift1 (9:00 - 17:00)
    expect(rows).toMatchInlineSnapshot(`
      [
        {
          "time": "08:30",
          "type": "timeOffStart",
        },
        {
          "activities": [],
          "assignedPersonId": "testPerson1",
          "id": "testShift1",
          "isoWeek": {
            "day": 1,
            "week": 1,
            "year": 2024,
          },
          "range": {
            "end": "17:00",
            "start": "09:00",
          },
          "shiftAreaId": "area1",
        },
      ]
    `);

  })

  it("should not show time off start if it's not on this day", () => {
    const isoWeek = {
      year: 2024,
      week: 1,
      day: 1
    };
    const rows = getDayScheduleRows({
      isoWeek: isoWeek,
      shifts: map([{
        assignedPersonId: "testPerson1",
        id: "testShift1",
        shiftAreaId: "area1",
        isoWeek: isoWeek,
        range: {
          start: "09:00",
          end: "17:00"
        }
      }], s => {
        return {
          ...constructShift(s),
          isoWeek: s.isoWeek
        }
      }),
      timeOff: [{
        // the time off starts at 8:30AM Denver time, and ends at 12:30PM Denver time
        // Note that Vitest has America/Denver as the default timezone in our global settings, so that's why this works
        start: new Date(2024, 0, 3, 8, 30),
        end: new Date(2024, 0, 3, 12, 30)
      }],
      timezone: "America/Denver"
    });

    // testShift1 (9:00 - 17:00)
    expect(rows).toMatchInlineSnapshot(`
      [
        {
          "activities": [],
          "assignedPersonId": "testPerson1",
          "id": "testShift1",
          "isoWeek": {
            "day": 1,
            "week": 1,
            "year": 2024,
          },
          "range": {
            "end": "17:00",
            "start": "09:00",
          },
          "shiftAreaId": "area1",
        },
      ]
    `);
  })

  it("should no scheduled shifts as first item if no shifts", () => {
    const isoWeek = {
      year: 2024,
      week: 1,
      day: 1
    };
    const rows = getDayScheduleRows({
      isoWeek: isoWeek,
      shifts: [],
      timeOff: [{
        // the time off starts at 8:30AM Denver time, and ends at 12:30PM Denver time
        // Note that Vitest has America/Denver as the default timezone in our global settings, so that's why this works
        start: new Date(2024, 0, 1, 8, 30),
        end: new Date(2024, 0, 1, 12, 30)
      }],
      timezone: "America/Denver"
    });

    expect(rows).toMatchInlineSnapshot(`
      [
        {
          "type": "noShifts",
        },
        {
          "time": "08:30",
          "type": "timeOffStart",
        },
        {
          "time": "12:30",
          "type": "timeOffEnd",
        },
      ]
    `);
  })

  it("should handle all day time offs", () => {
    const isoWeek = {
      year: 2024,
      week: 1,
      day: 1
    };
    const rows = getDayScheduleRows({
      isoWeek: isoWeek,
      shifts: [],
      timeOff: [{
        // the time off starts at 8:30AM Denver time, and ends at 12:30PM Denver time
        // Note that Vitest has America/Denver as the default timezone in our global settings, so that's why this works
        start: new Date(2024, 0, 1, 0, 0),
        end: new Date(2024, 0, 1, 23, 59, 59)
      }],
      timezone: "America/Denver"
    });

    expect(rows).toMatchInlineSnapshot(`
      [
        {
          "type": "noShifts",
        },
        {
          "time": "00:00",
          "type": "timeOffStart",
        },
        {
          "time": "23:59",
          "type": "timeOffEnd",
        },
      ]
    `);
  })

  it("should no scheduled shifts as only item if no shifts and no time off on this day", () => {
    const isoWeek = {
      year: 2024,
      week: 1,
      day: 1
    };
    const rows = getDayScheduleRows({
      isoWeek: isoWeek,
      shifts: [],
      timeOff: [{
        // the time off starts at 8:30AM Denver time, and ends at 12:30PM Denver time
        // Note that Vitest has America/Denver as the default timezone in our global settings, so that's why this works
        start: new Date(2024, 0, 3, 8, 30),
        end: new Date(2024, 0, 3, 12, 30)
      }],
      timezone: "America/Denver"
    });

    expect(rows).toMatchInlineSnapshot(`
      [
        {
          "type": "noShifts",
        },
      ]
    `);
  });

  describe('generateIsoWeekDates with negative week numbers', () => {
    it('should handle week 1 minus 3 (negative week -2)', () => {
      // Week 1 - 3 = -2, should roll to previous year
      const weeks = Array.from(takeFromGenerator(
        generateIsoWeekDates({ year: 2025, week: -2 }, 'America/New_York'),
        7
      ));

      expect(weeks).toEqual([
        { year: 2024, week: 50 },
        { year: 2024, week: 51 },
        { year: 2024, week: 52 },
        { year: 2025, week: 1 },
        { year: 2025, week: 2 },
        { year: 2025, week: 3 },
        { year: 2025, week: 4 }
      ]);
    });

    it('should handle week 2 minus 3 (negative week -1)', () => {
      const weeks = Array.from(takeFromGenerator(
        generateIsoWeekDates({ year: 2025, week: -1 }, 'America/New_York'),
        7
      ));

      expect(weeks).toEqual([
        { year: 2024, week: 51 },
        { year: 2024, week: 52 },
        { year: 2025, week: 1 },
        { year: 2025, week: 2 },
        { year: 2025, week: 3 },
        { year: 2025, week: 4 },
        { year: 2025, week: 5 }
      ]);
    });

    it('should handle week 3 minus 3 (zero week)', () => {
      const weeks = Array.from(takeFromGenerator(
        generateIsoWeekDates({ year: 2025, week: 0 }, 'America/New_York'),
        7
      ));

      expect(weeks).toEqual([
        { year: 2024, week: 52 },
        { year: 2025, week: 1 },
        { year: 2025, week: 2 },
        { year: 2025, week: 3 },
        { year: 2025, week: 4 },
        { year: 2025, week: 5 },
        { year: 2025, week: 6 }
      ]);
    });

    it('should handle very negative week numbers', () => {
      // Test with week -10 to ensure robustness
      const weeks = Array.from(takeFromGenerator(
        generateIsoWeekDates({ year: 2025, week: -10 }, 'America/New_York'),
        7
      ));

      expect(weeks).toEqual([
        { year: 2024, week: 42 },
        { year: 2024, week: 43 },
        { year: 2024, week: 44 },
        { year: 2024, week: 45 },
        { year: 2024, week: 46 },
        { year: 2024, week: 47 },
        { year: 2024, week: 48 }
      ]);
    });
  });

  describe('Hawaii ACA week generation edge cases', () => {
    it('should generate correct weeks for early year Hawaii ACA validation', () => {
      // Simulate the exact scenario from the API: current week 1, need 3 preceding weeks
      const currentWeek = { year: 2025, week: 1 }; // Early January
      const startWeek = currentWeek.week - 3; // This becomes -2

      const weeks = Array.from(takeFromGenerator(
        generateIsoWeekDates({
          year: currentWeek.year,
          week: startWeek
        }, 'America/New_York'),
        7
      ));

      expect(weeks).toHaveLength(7);
      expect(weeks[3]).toEqual(currentWeek); // Current week should be at index 3

      // Verify year boundary handling
      expect(weeks.some(w => w.year === 2024)).toBe(true); // Should include 2024 weeks
      expect(weeks.some(w => w.year === 2025)).toBe(true); // Should include 2025 weeks

      // Verify the exact structure matches Hawaii ACA requirements
      expect(weeks).toEqual([
        { year: 2024, week: 50 }, // Preceding weeks from 2024
        { year: 2024, week: 51 },
        { year: 2024, week: 52 },
        { year: 2025, week: 1 },  // Current week
        { year: 2025, week: 2 },  // Following weeks
        { year: 2025, week: 3 },
        { year: 2025, week: 4 }
      ]);
    });

    it('should handle week 2 Hawaii ACA validation', () => {
      const currentWeek = { year: 2025, week: 2 };
      const startWeek = currentWeek.week - 3; // This becomes -1

      const weeks = Array.from(takeFromGenerator(
        generateIsoWeekDates({
          year: currentWeek.year,
          week: startWeek
        }, 'America/New_York'),
        7
      ));

      expect(weeks).toEqual([
        { year: 2024, week: 51 },
        { year: 2024, week: 52 },
        { year: 2025, week: 1 },
        { year: 2025, week: 2 },  // Current week at index 3
        { year: 2025, week: 3 },
        { year: 2025, week: 4 },
        { year: 2025, week: 5 }
      ]);
    });

    it('should handle week 3 Hawaii ACA validation', () => {
      const currentWeek = { year: 2025, week: 3 };
      const startWeek = currentWeek.week - 3; // This becomes 0

      const weeks = Array.from(takeFromGenerator(
        generateIsoWeekDates({
          year: currentWeek.year,
          week: startWeek
        }, 'America/New_York'),
        7
      ));

      expect(weeks).toEqual([
        { year: 2024, week: 52 },
        { year: 2025, week: 1 },
        { year: 2025, week: 2 },
        { year: 2025, week: 3 },  // Current week at index 3
        { year: 2025, week: 4 },
        { year: 2025, week: 5 },
        { year: 2025, week: 6 }
      ]);
    });
  });

  describe('53-week year boundary handling', () => {
    it('should handle negative weeks with 53-week years', () => {
      // 2020 was a 53-week year, test transition from 2020 to 2021
      const weeks = Array.from(takeFromGenerator(
        generateIsoWeekDates({ year: 2021, week: -2 }, 'America/New_York'),
        7
      ));

      expect(weeks).toEqual([
        { year: 2020, week: 51 },
        { year: 2020, week: 52 },
        { year: 2020, week: 53 }, // 2020 had 53 weeks
        { year: 2021, week: 1 },
        { year: 2021, week: 2 },
        { year: 2021, week: 3 },
        { year: 2021, week: 4 }
      ]);
    });

    it('should handle 2024 to 2025 transition (2024 has 52 weeks)', () => {
      // 2024 has 52 weeks, test the actual transition we'll see in production
      const weeks = Array.from(takeFromGenerator(
        generateIsoWeekDates({ year: 2025, week: -2 }, 'America/New_York'),
        7
      ));

      expect(weeks).toHaveLength(7);
      expect(weeks[0].year).toBe(2024);
      expect(weeks[0].week).toBe(50); // 2024 has 52 weeks, so -2 from 2025 should be week 50

      // Verify the exact sequence for 2024→2025 transition
      expect(weeks).toEqual([
        { year: 2024, week: 50 },
        { year: 2024, week: 51 },
        { year: 2024, week: 52 },
        { year: 2025, week: 1 },
        { year: 2025, week: 2 },
        { year: 2025, week: 3 },
        { year: 2025, week: 4 }
      ]);
    });
  });

  describe('Week generation robustness', () => {
    it('should handle week -5 correctly', () => {
      const weeks = Array.from(takeFromGenerator(
        generateIsoWeekDates({ year: 2025, week: -5 }, 'America/New_York'),
        7
      ));

      expect(weeks).toEqual([
        { year: 2024, week: 47 },
        { year: 2024, week: 48 },
        { year: 2024, week: 49 },
        { year: 2024, week: 50 },
        { year: 2024, week: 51 },
        { year: 2024, week: 52 },
        { year: 2025, week: 1 }
      ]);
    });

    it('should handle very negative week numbers (week -10)', () => {
      const weeks = Array.from(takeFromGenerator(
        generateIsoWeekDates({ year: 2024, week: -10 }, 'America/New_York'),
        7
      ));

      expect(weeks).toEqual([
        { year: 2023, week: 42 },
        { year: 2023, week: 43 },
        { year: 2023, week: 44 },
        { year: 2023, week: 45 },
        { year: 2023, week: 46 },
        { year: 2023, week: 47 },
        { year: 2023, week: 48 }
      ]);
    });

    it('should handle 53-week year boundary with negative weeks', () => {
      // 2020 had 53 weeks, test going back from 2021
      const weeks = Array.from(takeFromGenerator(
        generateIsoWeekDates({ year: 2021, week: -3 }, 'America/New_York'),
        7
      ));

      expect(weeks).toEqual([
        { year: 2020, week: 50 },
        { year: 2020, week: 51 },
        { year: 2020, week: 52 },
        { year: 2020, week: 53 }, // 2020 had 53 weeks
        { year: 2021, week: 1 },
        { year: 2021, week: 2 },
        { year: 2021, week: 3 }
      ]);
    });
  });
});
