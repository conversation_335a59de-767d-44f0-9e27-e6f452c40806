import React from 'react'
import ReactDOM from 'react-dom/client'
import './index.css'
import {RouterProvider} from '@tanstack/react-router'
import * as Sentry from "@sentry/react";
import {httpClientIntegration} from "@sentry/browser";
import {router} from "@/src/router.ts";

Sentry.init({
  dsn: "https://<EMAIL>/4507232654065664",
  integrations: [
    Sentry.browserTracingIntegration(),
    // Sentry.replayIntegration(),
    httpClientIntegration
  ],
  // Performance Monitoring
  tracesSampleRate: 0.05, //  Capture 5% of the transactions
  // Set 'tracePropagationTargets' to control for which URLs distributed tracing should be enabled
  tracePropagationTargets: ["localhost", "beta.gioanation.com", "api.gioanation.com"],
  // Session Replay
  replaysSessionSampleRate: 0, // This sets the sample rate at 0%. You may want to change it to 100% while in development and then sample at a lower rate in production.
  replaysOnErrorSampleRate: 0, // If you're not already sampling the entire session, change the sample rate to 100% when sampling sessions where errors occur.
  environment: import.meta.env.VITE_GIOA_ENV,
  enabled: process.env.NODE_ENV !== "development",
});

ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <RouterProvider router={router}/>
  </React.StrictMode>,
)
