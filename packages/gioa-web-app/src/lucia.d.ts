import lucia from "@gioa/api/src/lucia";

declare module "lucia" {
  interface Register {
    Lucia: typeof lucia;
    DatabaseUserAttributes: {
      email: string;
      emailVerified: boolean;
    };
    DatabaseSessionAttributes: {
      createdAt: Date;
      startedFromIpAddress: string;
      startedFromUserAgent: string;
      userClientId: string;
      impersonatedByUserId?: string;
    }
  }
}
