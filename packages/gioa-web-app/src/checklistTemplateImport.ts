import Papa from "papaparse";
import {z} from "zod";
import {trim} from "lodash";

// Valid requirement types based on the codebase
const VALID_REQUIREMENT_TYPES = [
  "addImage",
  "writeComment",
  "inputNumber",
  "inputNumber:currency",
  "inputNumber:percentage",
  "inputNumber:temperature",
  "inputBoolean",
] as const;

// Valid tags based on the CSV specification
const VALID_TAGS = ["Operations", "Training", "Onboarding", "FOH", "BOH"] as const;

// CSV row interface matching the expected format
interface ChecklistTemplateCSVRow {
  "Checklist Title": string;
  Tags: string;
  "Item Name": string;
  "Item Description": string;
  "Item Requirement": string;
}

// Parsed checklist item interface
export interface ParsedChecklistItem {
  title: string;
  description: string | null;
  requirement: string;
  requirementType: string;
  requirementSubtype?: string;
  order: number;
}

// Parsed checklist template interface
export interface ParsedChecklistTemplate {
  title: string;
  tags: string[];
  items: ParsedChecklistItem[];
}

// Validation error interface
export interface ValidationError {
  row: number;
  field: string;
  message: string;
}

// Parse result interface
export interface ChecklistTemplateParseResult {
  isValid: boolean;
  templates: ParsedChecklistTemplate[];
  errors: ValidationError[];
  warnings: string[];
}

/**
 * Parse a single CSV line, handling quoted fields
 */
function parseCSVLine(line: string): string[] {
  const result: string[] = [];
  let current = "";
  let inQuotes = false;
  let i = 0;

  while (i < line.length) {
    const char = line[i];

    if (char === '"') {
      if (inQuotes && line[i + 1] === '"') {
        // Escaped quote
        current += '"';
        i += 2;
      } else {
        // Toggle quote state
        inQuotes = !inQuotes;
        i++;
      }
    } else if (char === "," && !inQuotes) {
      // Field separator
      result.push(current);
      current = "";
      i++;
    } else {
      current += char;
      i++;
    }
  }

  // Add the last field
  result.push(current);

  return result;
}

/**
 * Validates a requirement type and parses it into type and subtype
 */
function parseRequirementType(requirement: string): {
  isValid: boolean;
  type: string;
  subtype?: string;
} {
  const trimmedReq = trim(requirement);

  if (VALID_REQUIREMENT_TYPES.includes(trimmedReq as any)) {
    if (trimmedReq.startsWith("inputNumber:")) {
      const [type, subtype] = trimmedReq.split(":");
      return { isValid: true, type, subtype };
    } else {
      return { isValid: true, type: trimmedReq };
    }
  }

  return { isValid: false, type: trimmedReq };
}

/**
 * Validates a requirement type
 */
function validateRequirementType(requirement: string): boolean {
  return parseRequirementType(requirement).isValid;
}

/**
 * Validates and parses tags from a comma-separated string
 */
function parseTags(tagsString: string): { tags: string[]; isValid: boolean; invalidTags: string[] } {
  if (!tagsString || trim(tagsString) === "") {
    return { tags: [], isValid: false, invalidTags: [] };
  }

  const tags = tagsString
    .split(",")
    .map((tag) => trim(tag))
    .filter((tag) => tag !== "");

  return {
    tags: tags,
    isValid: true,
    invalidTags: [],
  };
}

/**
 * Validates a single CSV row
 */
function validateRow(
  row: ChecklistTemplateCSVRow,
  rowIndex: number,
  isFirstRowOfChecklist: boolean,
): ValidationError[] {
  const errors: ValidationError[] = [];

  // If this is the first row of a checklist, validate title and tags
  if (isFirstRowOfChecklist) {
    if (!row["Checklist Title"] || trim(row["Checklist Title"]) === "") {
      errors.push({
        row: rowIndex,
        field: "Checklist Title",
        message: "Checklist Title is required for the first row of each checklist",
      });
    }

    const { isValid: tagsValid, invalidTags } = parseTags(row["Tags"]);
    if (!tagsValid) {
      if (!row["Tags"] || trim(row["Tags"]) === "") {
        errors.push({
          row: rowIndex,
          field: "Tags",
          message: "Tags are required for the first row of each checklist",
        });
      } else if (invalidTags.length > 0) {
        errors.push({
          row: rowIndex,
          field: "Tags",
          message: `Invalid tags: ${invalidTags.join(", ")}. Valid tags are: ${VALID_TAGS.join(", ")}`,
        });
      }
    }
  } else {
    // For subsequent rows, title and tags should be empty
    if (row["Checklist Title"] && trim(row["Checklist Title"]) !== "") {
      errors.push({
        row: rowIndex,
        field: "Checklist Title",
        message: "Checklist Title should be empty for subsequent items of the same checklist",
      });
    }
    if (row["Tags"] && trim(row["Tags"]) !== "") {
      errors.push({
        row: rowIndex,
        field: "Tags",
        message: "Tags should be empty for subsequent items of the same checklist",
      });
    }
  }

  // Validate item name (required for all rows)
  if (!row["Item Name"] || trim(row["Item Name"]) === "") {
    errors.push({
      row: rowIndex,
      field: "Item Name",
      message: "Item Name is required",
    });
  }

  // Validate item requirement (required for all rows)
  if (!row["Item Requirement"] || trim(row["Item Requirement"]) === "") {
    errors.push({
      row: rowIndex,
      field: "Item Requirement",
      message: "Item Requirement is required",
    });
  } else if (!validateRequirementType(trim(row["Item Requirement"]))) {
    errors.push({
      row: rowIndex,
      field: "Item Requirement",
      message: `Invalid requirement type: ${row["Item Requirement"]}. Valid types are: ${VALID_REQUIREMENT_TYPES.join(", ")}`,
    });
  }

  return errors;
}

/**
 * Groups CSV rows into checklists and validates the structure
 */
function groupRowsIntoChecklists(rows: ChecklistTemplateCSVRow[]): {
  checklists: { title: string; tags: string[]; rows: ChecklistTemplateCSVRow[] }[];
  errors: ValidationError[];
} {
  const checklists: { title: string; tags: string[]; rows: ChecklistTemplateCSVRow[] }[] = [];
  const errors: ValidationError[] = [];
  let currentChecklist: { title: string; tags: string[]; rows: ChecklistTemplateCSVRow[] } | null = null;

  rows.forEach((row, index) => {
    const rowIndex = index + 2; // +2 because CSV is 1-indexed and we skip header
    const hasTitle = row["Checklist Title"] && trim(row["Checklist Title"]) !== "";

    if (hasTitle) {
      // Start a new checklist
      const { tags, isValid: tagsValid } = parseTags(row["Tags"]);

      if (tagsValid) {
        currentChecklist = {
          title: trim(row["Checklist Title"]),
          tags,
          rows: [row],
        };
        checklists.push(currentChecklist);
      } else {
        // Error will be caught in row validation
        currentChecklist = null;
      }
    } else {
      // Add to current checklist
      if (currentChecklist) {
        currentChecklist.rows.push(row);
      } else {
        errors.push({
          row: rowIndex,
          field: "Checklist Title",
          message:
            "Row does not belong to any checklist. Each checklist must start with a row containing a Checklist Title.",
        });
      }
    }
  });

  return { checklists, errors };
}

/**
 * Parses CSV data containing checklist templates
 *
 * @param csv - Either a File object or a string containing CSV data
 * @returns Promise that resolves to a ChecklistTemplateParseResult
 */
export function parseChecklistTemplatesCSV(csv: File | string): Promise<ChecklistTemplateParseResult> {
  return new Promise((resolve) => {
    const errors: ValidationError[] = [];
    const warnings: string[] = [];

    Papa.parse<ChecklistTemplateCSVRow>(csv, {
      header: true,
      skipEmptyLines: true,
      transformHeader: (header) => trim(header),
      complete: (results) => {
        try {
          // Check for required headers
          const requiredHeaders = ["Checklist Title", "Tags", "Item Name", "Item Description", "Item Requirement"];
          const actualHeaders = results.meta.fields || [];
          const missingHeaders = requiredHeaders.filter((h) => !actualHeaders.includes(h));

          if (missingHeaders.length > 0) {
            resolve({
              isValid: false,
              templates: [],
              errors: [
                {
                  row: 1,
                  field: "Headers",
                  message: `Missing required headers: ${missingHeaders.join(", ")}`,
                },
              ],
              warnings: [],
            });
            return;
          }

          // Parse and validate rows
          const { checklists, errors: groupingErrors } = groupRowsIntoChecklists(results.data);
          errors.push(...groupingErrors);

          const templates: ParsedChecklistTemplate[] = [];

          checklists.forEach((checklist) => {
            const checklistErrors: ValidationError[] = [];
            const items: ParsedChecklistItem[] = [];

            checklist.rows.forEach((row, index) => {
              const rowIndex = results.data.indexOf(row) + 2; // +2 for 1-indexed and header
              const isFirstRow = index === 0;

              // Validate row
              const rowErrors = validateRow(row, rowIndex, isFirstRow);
              checklistErrors.push(...rowErrors);

              // If row is valid, create item
              if (rowErrors.length === 0) {
                const parsedReq = parseRequirementType(trim(row["Item Requirement"]));
                items.push({
                  title: trim(row["Item Name"]),
                  description:
                    row["Item Description"] && trim(row["Item Description"]) !== ""
                      ? trim(row["Item Description"])
                      : null,
                  requirement: trim(row["Item Requirement"]),
                  requirementType: parsedReq.type,
                  requirementSubtype: parsedReq.subtype,
                  order: index,
                });
              }
            });

            errors.push(...checklistErrors);

            // Only add template if it has valid items
            if (items.length > 0) {
              templates.push({
                title: checklist.title,
                tags: checklist.tags,
                items,
              });

              // Add warnings for checklist validation
              if (items.length < 3) {
                warnings.push(
                  `Checklist "${checklist.title}" has only ${items.length} item(s). Consider adding more items for completeness.`,
                );
              }
              if (items.length > 30) {
                warnings.push(
                  `Checklist "${checklist.title}" has ${items.length} items. Consider breaking it into smaller checklists for better usability.`,
                );
              }
            }
          });

          resolve({
            isValid: errors.length === 0,
            templates,
            errors,
            warnings,
          });
        } catch (error) {
          resolve({
            isValid: false,
            templates: [],
            errors: [
              {
                row: 0,
                field: "General",
                message: `Error processing CSV: ${error instanceof Error ? error.message : String(error)}`,
              },
            ],
            warnings: [],
          });
        }
      },
      error: (error) => {
        resolve({
          isValid: false,
          templates: [],
          errors: [
            {
              row: 0,
              field: "General",
              message: `Error parsing CSV: ${error.message}`,
            },
          ],
          warnings: [],
        });
      },
    });
  });
}

/**
 * Zod schema for validating parsed checklist templates
 */
export const ChecklistTemplateImportSchema = z.object({
  title: z.string().min(1, "Title is required"),
  tags: z.array(z.enum(VALID_TAGS)).min(1, "At least one tag is required"),
  items: z
    .array(
      z.object({
        title: z.string().min(1, "Item title is required"),
        description: z.string().nullable(),
        requirement: z.enum(VALID_REQUIREMENT_TYPES),
        requirementType: z.string(),
        requirementSubtype: z.string().optional(),
        order: z.number().int().min(0),
      }),
    )
    .min(1, "At least one item is required"),
});

export type ChecklistTemplateImportSchema = z.infer<typeof ChecklistTemplateImportSchema>;

/**
 * Validates parsed templates against the schema
 */
export function validateParsedTemplates(templates: ParsedChecklistTemplate[]): {
  isValid: boolean;
  validTemplates: ChecklistTemplateImportSchema[];
  errors: ValidationError[];
} {
  const errors: ValidationError[] = [];
  const validTemplates: ChecklistTemplateImportSchema[] = [];

  templates.forEach((template, index) => {
    try {
      const validatedTemplate = ChecklistTemplateImportSchema.parse(template);
      validTemplates.push(validatedTemplate);
    } catch (error) {
      if (error instanceof z.ZodError) {
        error.issues.forEach((err) => {
          errors.push({
            row: index + 1,
            field: err.path.join("."),
            message: err.message,
          });
        });
      } else {
        errors.push({
          row: index + 1,
          field: "General",
          message: `Validation error: ${error instanceof Error ? error.message : String(error)}`,
        });
      }
    }
  });

  return {
    isValid: errors.length === 0,
    validTemplates,
    errors,
  };
}
