import {suggestPeopleForShift} from "../../api/src/shiftSuggestion.ts";
import {getShift} from "../../api/src/scheduling.util.ts";

/**
 * This is a web worker that runs the shift suggestion logic in a separate thread.
 * The shift suggestions is computationally expensive when run on large stores and schedules.
 */
self.onmessage = (e) => {
  const { schedule, dayOfWeek, storeAreas, selectedShiftId, people, timezone, settings, weeksHoursMap = {}, storeState = 'Unknown' } = e.data;



  const shift = getShift(schedule, selectedShiftId);
  if (!shift) {
    self.postMessage([]);
    return;
  }
  const suggestions = suggestPeopleForShift({
    schedule,
    dayOfWeek,
    storeAreas,
    shift: shift,
    people,
    timezone,
    settings,
    weeksHoursMap,
    storeState
  });



  self.postMessage(suggestions);
};
