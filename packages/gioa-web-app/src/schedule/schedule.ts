import {IsoWeekDate} from "../../../api/src/timeSchemas.ts";
import {formatDateToMonthDay} from "@/src/date.util.ts";
import {getDateFromWeekDayTime} from "../../../api/src/date.util.ts";

export function getFriendlyIsoWeekString(week: IsoWeekDate): string {
  const weekStartDate = getDateFromWeekDayTime({...week, day: 1, time: "00:00", timezone: "local"});
  const weekEndDate = getDateFromWeekDayTime({...week, day: 7, time: "23:59", timezone: "local"});
  return `Week ${week.week}: ${formatDateToMonthDay(weekStartDate)} - ${formatDateToMonthDay(weekEndDate)}`
}
