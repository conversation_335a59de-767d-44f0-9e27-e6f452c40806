import {useState} from "react";
import {PresignedPostDataFileDto, PresignedPostTeamMemberDocumentDto} from "../../api/src/schemas.ts";
import {getHumanReadableErrorMessage} from "@/src/components/ErrorAlert.tsx";


export interface PresignedPost {
  url: string;
  fields: {
    [key: string]: string;
  };
}

export async function uploadFileToS3({
  preSignedPost,
  file,
  contentType,
  onProgress
}: {
  preSignedPost: PresignedPost,
  file: File,
  contentType?: string,
  onProgress?: (progress: number) => void
}) {
  return new Promise(((resolve, reject) => {
    const {url, fields} = preSignedPost;
    const xhr = new XMLHttpRequest();
    const formData: FormData = new FormData();

    Object.keys(fields).forEach(key => {
      formData.append(key, fields[key] ?? "");
    });

    formData.append("file", file);

    if (contentType) {
      formData.append("Content-Type", contentType);
    }

    // Add progress tracking
    if (onProgress) {
      xhr.upload.onprogress = (event) => {
        if (event.lengthComputable) {
          const percentComplete = Math.round((event.loaded / event.total) * 100);
          onProgress(percentComplete);
        }
      };
    }

    xhr.onload = () => {
      if (xhr.status >= 400) {
        console.error(`Error uploading to S3: ${xhr.status}: ${xhr.statusText}`); // e.g. 404: Not Found
        reject(xhr.statusText);
      } else {
        // Ensure we report 100% completion when done
        if (onProgress) {
          onProgress(100);
        }
        resolve(true);
      }
    };

    xhr.onerror = (e) => {
      console.error("Error uploading to S3", e);
      reject(e);
    };

    // fire the XHR POST request with the formData
    xhr.open("POST", url);
    xhr.send(formData);
  }));
}

export type UploadTeamMemberDocumentResult = {
  key: string;
  newId: string;
};

export function useUploadTeamMemberDocument(getPresignedPost: (params: {
  contentType: string;
  storeId: string;
}) => Promise<PresignedPostTeamMemberDocumentDto>) {
  const [isLoading, setIsLoading] = useState(false);
  const [isError, setIsError] = useState<boolean>();
  const [error, setError] = useState<string>();

  const upload = async ({file, contentType, storeId}: {
    file: File;
    contentType: string;
    storeId: string;
  }): Promise<UploadTeamMemberDocumentResult | undefined> => {
    try {
      setIsLoading(true);
      const uploadUrls = await getPresignedPost({
        contentType,
        storeId
      });

      await uploadFileToS3({
        preSignedPost: uploadUrls.presignedPost,
        contentType: contentType,
        file: file
      });

      return {
        key: uploadUrls.key,
        newId: uploadUrls.newDocumentId
      }
    } catch (e) {
      console.error("Error uploading data file", e);
      alert("Failed to upload data file. Please try again later or contact GIOA support.");
      setIsError(true);
      setError(getHumanReadableErrorMessage(e));
    } finally {
      setIsLoading(false);
    }
  };

  return {upload, isLoading, isError, error};
}

export type UploadDataFileResult = {
  key: string;
  newDataFileId: string;
};

export function useUploadDataFile(getPresignedPost: (params: {
  contentType: string;
  storeId: string;
}) => Promise<PresignedPostDataFileDto>) {
  const [isLoading, setIsLoading] = useState(false);
  const [isError, setIsError] = useState<boolean>(false);
  const [error, setError] = useState<string>();
  const [progress, setProgress] = useState(0);

  const upload = async ({file, contentType, storeId, onProgress}: {
    file: File;
    contentType: string;
    storeId: string;
    onProgress?: (progress: number) => void;
  }): Promise<UploadDataFileResult | undefined> => {
    try {
      setIsLoading(true);
      setIsError(false);
      setError(undefined);
      setProgress(0);

      const uploadUrls = await getPresignedPost({
        contentType,
        storeId
      });

      await uploadFileToS3({
        preSignedPost: uploadUrls.presignedPost,
        contentType: contentType,
        file: file,
        onProgress: (progressPercent) => {
          setProgress(progressPercent);
          onProgress?.(progressPercent);
        }
      });

      return {
        key: uploadUrls.key,
        newDataFileId: uploadUrls.newDataFileId
      }
    } catch (e) {
      console.error("Error uploading data file", e);
      alert("Failed to upload data file. Please try again later or contact Nation support.");
      setIsError(true);
      setError(getHumanReadableErrorMessage(e));
      return undefined;
    } finally {
      setIsLoading(false);
    }
  };

  return {
    upload,
    isLoading,
    isError,
    error,
    progress,
    reset: () => {
      setIsLoading(false);
      setIsError(false);
      setError(undefined);
      setProgress(0);
    }
  };
}
