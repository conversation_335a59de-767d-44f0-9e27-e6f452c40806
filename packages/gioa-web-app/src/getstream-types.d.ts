// custom-types.d.ts
import {
  DefaultAttachmentData,
  DefaultChannelData,
  DefaultCommandData,
  DefaultEventData,
  DefaultMemberData,
  DefaultMessageData,
  DefaultPollData,
  DefaultPollOptionData,
  DefaultReactionD<PERSON>,
  DefaultThreadD<PERSON>,
  DefaultUserData,
} from "stream-chat-react-native";
import {ChannelMembershipRule, StreamChatTeam} from "../../api/src/chat/chatSchemas.ts";

declare module "stream-chat" {
  /* eslint-disable @typescript-eslint/no-empty-object-type */

  interface CustomAttachmentData extends DefaultAttachmentData {}

  interface CustomChannelData extends DefaultChannelData {
    team?: StreamChatTeam;
    name?: string;
    image?: string;
    description?: string;
    nationMembershipRuleType?: ChannelMembershipRule["ruleType"];
    isBusiness?: boolean;
    jobIds?: string[];
    storeId?: string;
    storePositionIds?: string[];
    extraPeopleIds?: string[];
    has_unread?: boolean;
  }

  interface CustomCommandData extends DefaultCommandData {}

  interface CustomEventData extends DefaultEventData {}

  interface CustomMemberData extends DefaultMemberData {}

  interface CustomUserData extends DefaultUserData {
    jobTitle?: string;
    preferredLanguage?: string;
  }

  interface CustomMessageData extends DefaultMessageData {}

  interface CustomPollOptionData extends DefaultPollOptionData {}

  interface CustomPollData extends DefaultPollData {}

  interface CustomReactionData extends DefaultReactionData {}

  interface CustomThreadData extends DefaultThreadData {}

  /* eslint-enable @typescript-eslint/no-empty-object-type */
}
