import {format, isSameDay, isSameWeek, isSameYear} from 'date-fns';
import {formatInTimeZone} from "date-fns-tz";
import {UTCDate} from "@date-fns/utc";
import {DateTime} from "luxon";

/**
 * Parse a string like "2024-07-28" to a Date object in the local timezone.
 * @param dateStr
 */
export function dateStrToLocalDate(dateStr: string): Date {
  const [year, month, day] = dateStr.split("-").map(Number);
  return new UTCDate(year, month - 1, day);
}

export function twentyFourTimeToTwelveHourTime(time: string): string {
  const [hours, minutes] = time.split(":").map(Number);
  return `${hours % 12}:${minutes.toString().padStart(2, "0")}${hours >= 12 ? "pm" : "am"}`;
}

const monthDayFormat = new Intl.DateTimeFormat('en-US', {month: 'numeric', day: 'numeric'});

export function formatDateToMonthDay(date: Date): string {
  return monthDayFormat.format(date);
}

export function formatDateWithoutSeconds(date: Date) {
  return date.toLocaleString(undefined, {
    year: 'numeric',
    month: 'numeric',
    day: 'numeric',
    hour: "numeric",
    minute: '2-digit',
    hour12: true // Set to false for 24-hour format
  });
}

/**
 * We need to show time off datetimes in the store's timezone, not the user's timezone.
 * Say the store is in Atlanta, GA (Eastern Time). The scheduler travels to Seattle, WA. Their local device timezone is now Pacific Time.
 * A team member made a time off request for 10AM ET on Friday.
 * The scheduler, despite their local timezone being Pacific Time, should see the time off request as starting 10AM Friday, Eastern Time. Not 7AM Friday, Pacific Time.
 * @param date
 * @param timezone
 */
export function formatTimeOffDate(date: Date, timezone: string | null) {
  // if the date is in the current year, don't show the year
  const isThisYear = isSameYear(date, new Date());
  const tz = timezone ?? "America/New_York";

  if (isThisYear) {
    return formatInTimeZone(date, tz, "MMM d, h:mmaaaaa");
  } else {
    return formatInTimeZone(date, tz, "MMM d, yyyy, h:mmaaaaa");
  }
}

export function formatDataFileDate(date: Date, timezone: string) {
  return formatInTimeZone(date, timezone, "MMM d, yyyy, h:mmaaaaa");
}

export function formatDateMonthNoTime(date: Date) {
  const isThisYear = isSameYear(date, new Date());

  if (isThisYear) {
    return format(date, "MMM d");
  } else {
    return format(date, "MMM d, yyyy");
  }
}

// e.g. Friday, Apr 29, 2024
export function formatDateLongLocalized(date: Date) {
  return format(date, "cccc, PP");
}

export function getDateFormat(date: Date, now: Date): string {
  if (isSameDay(date, now)) {
    return 'h:mm a';
  }
  if (isSameYear(date, now)) {
    return 'eee MMM d \'at\' h:mm a';
  }
  return 'eee MMM d, yyyy \'at\' h:mm a';
}

export function formatAbsDateRangeFriendly(start: Date, end: Date, now: Date): string {
  if (isSameDay(start, now) && isSameDay(end, now)) {
    return `${format(start, 'h:mm a')} - ${format(end, 'h:mm a')}`;
  }

  if (isSameWeek(start, end, {weekStartsOn: 1})) {
    return `${format(start, 'eee')} ${format(start, 'h:mm a')} - ${format(end, 'eee')} ${format(end, 'h:mm a')}`;
  }

  const startFormat = getDateFormat(start, now);
  const endFormat = getDateFormat(end, now);
  return `${format(start, startFormat)} - ${format(end, endFormat)}`;
}

export const longDash = "―"

export function calculateDaysAge(endDate: Date, timezone: string): number {
  const now = DateTime.now().setZone(timezone);
  const end = DateTime.fromJSDate(endDate).setZone(timezone);
  const diff = now.diff(end, 'days');
  return Math.floor(diff.days);
}

export function calculateDataAge(endDate: Date, timezone: string): string {
  const days = calculateDaysAge(endDate, timezone);

  if (days === 0) {
    return "Today";
  } else if (days === 1) {
    return "1 day old";
  } else {
    return `${days} days old`;
  }
}

/**
 * Formats a date relative to another date based on their temporal distance
 * @param time The date to format
 * @param relative The date to compare against
 * @param timezone
 * @returns A formatted string representation of the time
 */
export function formatRelativeTime({time, relative, timezone}: {
  time: Date,
  relative: Date,
  timezone: string
}): string {
  const timeDate = DateTime.fromJSDate(time).setZone(timezone);
  const relativeDate = DateTime.fromJSDate(relative).setZone(timezone);

  if (timeDate.hasSame(relativeDate, 'day')) {
    return timeDate.toLocaleString(DateTime.TIME_SIMPLE);
  }

  if (timeDate.hasSame(relativeDate, 'week')) {
    return timeDate.toLocaleString(DateTime.DATETIME_MED_WITH_WEEKDAY)
      .split(',')[0] + ' ' + timeDate.toLocaleString(DateTime.TIME_SIMPLE);
  }

  if (timeDate.hasSame(relativeDate, 'year')) {
    return timeDate.toLocaleString(DateTime.DATETIME_MED)
      .replace(/,\s*\d{4}/, '');
  }

  return timeDate.toLocaleString(DateTime.DATETIME_MED);
}

export function formatDateRangeRelative({
                                          start, end, now, timezone
                                        }: {
  start: Date,
  end: Date,
  now: Date,
  timezone: string
}): string {
  const startFormatted = formatRelativeTime({time: start, relative: now, timezone});
  const endFormatted = formatRelativeTime({time: end, relative: now, timezone});
  return `${startFormatted} - ${endFormatted}`;
}


export function formatDateRangeByTimeFrame({
  range,
  timeFrame,
  timezone,
}: {
  range: { start: Date; end: Date };
  timeFrame: string;
  timezone: string;
}): string {
  const start = DateTime.fromJSDate(range.start, {zone: timezone});
  const end = DateTime.fromJSDate(range.end, {zone: timezone});

  switch (timeFrame) {
    case 'day':
      return start.toFormat('EEEE, MMM d, yyyy');
    case 'week': {
      if (start.year === end.year) {
        return `${start.toFormat('MMM d')} - ${end.toFormat('MMM d, yyyy')}`;
      }
      return `${start.toFormat('MMM d, yyyy')} - ${end.toFormat('MMM d, yyyy')}`;
    }
    case 'month':
      return start.toFormat('MMMM yyyy');
    case 'custom': {
      if (start.year === end.year) {
        return `${start.toFormat('MMM d')} - ${end.toFormat('MMM d, yyyy')}`;
      }
      return `${start.toFormat('MMM d, yyyy')} - ${end.toFormat('MMM d, yyyy')}`;
    }
    default:
      return `${start.toFormat('MMM d')} - ${end.toFormat('MMM d, yyyy')}`;
  }
}
