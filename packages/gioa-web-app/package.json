{"name": "@gioa/web", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "build:beta": "tsc && vite build --mode beta", "build:prod": "tsc && vite build --mode production", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "messages:extract": "lingui extract", "deploy:local:beta": "AWS_PROFILE=gioa-beta s3-spa-upload dist betastage-webstack-webbucket12880f5b-hmfddoubgell", "typecheck": "tsc --noEmit", "test": "vitest --run", "test:update": "vitest --run --update"}, "dependencies": {"@ag-media/react-pdf-table": "^1.0.1", "@date-fns/utc": "^1.2.0", "@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@gioa/api": "workspace:*", "@gioa/common": "workspace:*", "@lingui/macro": "4.11.2", "@lingui/react": "4.11.2", "@lottiefiles/dotlottie-react": "^0.13.5", "@mux/mux-player-react": "^3.5.0", "@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-alert-dialog": "^1.1.11", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-collapsible": "^1.1.0", "@radix-ui/react-context-menu": "^2.2.2", "@radix-ui/react-dialog": "^1.1.1", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-menubar": "^1.1.1", "@radix-ui/react-popover": "^1.1.1", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.0", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.3.2", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.0", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.8", "@react-pdf/fns": "3.0.0", "@react-pdf/font": "3.0.1", "@react-pdf/image": "3.0.1", "@react-pdf/layout": "4.1.3", "@react-pdf/pdfkit": "4.0.0", "@react-pdf/png-js": "3.0.0", "@react-pdf/primitives": "4.0.0", "@react-pdf/reconciler": "1.1.3", "@react-pdf/render": "4.0.2", "@react-pdf/renderer": "4.1.5", "@react-pdf/stylesheet": "5.1.0", "@react-pdf/textkit": "5.0.1", "@react-pdf/types": "2.7.0", "@sentry/react": "^8.20.0", "@statelyai/inspect": "^0.4.0", "@tanstack/react-form": "0.37.1", "@tanstack/react-query": "5.52.1", "@tanstack/react-query-devtools": "5.52.1", "@tanstack/react-ranger": "^0.0.4", "@tanstack/react-router": "1.115.2", "@tanstack/react-table": "^8.19.3", "@tanstack/zod-adapter": "^1.85.4", "@tanstack/zod-form-adapter": "0.37.1", "@trpc/client": "11.1.2", "@trpc/react-query": "11.1.2", "@trpc/server": "11.1.2", "@types/lodash": "4.17.16", "@xstate/react": "^4.1.3", "autoprefixer": "^10.4.19", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cmdk": "1.0.0", "currency.js": "^2.0.4", "date-fns": "^3.6.0", "file-saver": "^2.0.5", "framer-motion": "^11.3.21", "fuse.js": "^7.1.0", "immer": "^10.1.1", "input-otp": "^1.2.4", "libphonenumber-js": "^1.11.5", "lodash": "^4.17.21", "lucide-react": "0.475.0", "next-themes": "^0.3.0", "papaparse": "^5.5.2", "postcss": "^8", "react": "^18.3.1", "react-day-picker": "8.10.1", "react-device-detect": "^2.2.3", "react-dom": "^18.3.1", "react-dropzone": "^14.2.3", "react-helmet": "^6.1.0", "react-hotkeys-hook": "^4.5.0", "react-use": "^17.5.1", "recharts": "^2.15.1", "rxjs": "^7.8.1", "sonner": "^1.5.0", "superjson": "^2.2.1", "tailwind-merge": "^2.4.0", "tailwindcss": "^3.4.7", "tailwindcss-animate": "^1.0.7", "usehooks-ts": "^3.1.0", "xstate": "^5.14.0", "zod": "4.0.14", "zustand": "^4.5.4"}, "devDependencies": {"@lingui/cli": "4.11.2", "@lingui/vite-plugin": "4.11.2", "@tanstack/router-devtools": "1.45.11", "@tanstack/router-vite-plugin": "1.45.8", "@types/file-saver": "^2.0.7", "@types/papaparse": "^5.3.15", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@types/react-helmet": "^6.1.11", "@typescript-eslint/eslint-plugin": "^7.17.0", "@typescript-eslint/parser": "^7.17.0", "@vitejs/plugin-react": "^4.3.1", "babel-plugin-macros": "^3.1.0", "eslint": "^9.8.0", "eslint-plugin-lingui": "^0.3.0", "eslint-plugin-react-hooks": "^4.6.2", "eslint-plugin-react-refresh": "^0.4.9", "fast-check": "^3.18.0", "s3-spa-upload": "^2.1.5", "typescript": "5.8.3", "vite": "^5.3.5", "vite-tsconfig-paths": "^4.3.2"}}