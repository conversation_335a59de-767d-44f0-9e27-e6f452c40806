import {defineConfig} from 'vitest/config'
import * as path from "node:path";

// config specifically for unit tests that don't require the API integration test mocking
export default defineConfig({
  test: {
    globalSetup: './test-globals.ts',
    include: ['**/*.test.ts'],
    coverage: {
      provider: 'v8',
      reporter: ['lcov']
    },
    expect: {
      requireAssertions: false
    },
  },
  resolve: {
    alias: {
      '@': path.resolve(__dirname)
    },
  },
});
