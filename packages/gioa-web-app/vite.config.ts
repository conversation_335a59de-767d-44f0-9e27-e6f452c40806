import {defineConfig} from "vite";
import react from "@vitejs/plugin-react";
import {lingui} from "@lingui/vite-plugin";
import {TanStackRouterVite} from '@tanstack/router-vite-plugin';
import tsconfigPaths from 'vite-tsconfig-paths';
import * as path from "node:path";

export default defineConfig({
  assetsInclude: ['**/*.lottie'],
  resolve: {
    // preserveSymlinks: true,
    alias: {
      '@gioa/api': path.resolve(__dirname, '../api'),
    },
  },
  plugins: [
    react({
      babel: {
        plugins: ["macros"],
      },
    }),
    lingui(),
    TanStackRouterVite(),
    tsconfigPaths(),
  ],
});
