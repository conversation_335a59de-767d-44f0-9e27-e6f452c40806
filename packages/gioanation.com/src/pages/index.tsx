import type {NextPage} from "next";
import Head from "next/head";
import React from "react";
import Image from "next/image";
import apple from "./../../public/app_listing/apple.png";
import android from "./../../public/app_listing/android.png";
import Link from "next/link";

const Home: NextPage = () => {
  const AppleAppStoreListingUrl = "https://apps.apple.com/us/app/honeydo-app/id6448667976";

  return (
    <>
      <Head>
        <title>Nation</title>
        <meta name="description" content="Boost Productivity with Every Click"/>
        <link rel="preconnect" href="https://fonts.googleapis.com"/>
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin={"anonymous"}/>
        <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400&display=swap" rel="stylesheet"/>
      </Head>

      <div className="bg-bee bg-[#EEF0F7] flex flex-col min-h-screen" style={{ color: '#323D67' }}>
        <nav className="px-10 py-8">
          {/* Keep existing SVG logo */}
        </nav>

        <main className="flex flex-grow flex-col items-center justify-center text-center mx-4">
          <div className="inline-bloc">
            {/* Keep existing SVG image */}
          </div>
          <div className="mb-6 inline-block p-6 bg-[#3E4B7E] rounded-lg">
            <svg width="79" height="99" viewBox="0 0 79 99" fill="none" xmlns="http://www.w3.org/2000/svg">
              <circle cx="39.9717" cy="14.5236" r="14.5236" fill="white"/>
              <circle cx="69.0188" cy="31.6541" r="9.68242" fill="#E51636"/>
              <path d="M9.06241 98.5002C4.33197 98.5002 0.497192 94.6654 0.497192 89.935L0.497192 25.2796C0.497192 21.396 3.64543 18.2478 7.52898 18.2478C9.34894 18.2478 11.098 18.9534 12.4085 20.2163L56.0039 62.2297C56.3501 62.5633 56.8121 62.7497 57.2929 62.7497C58.3187 62.7497 59.1503 61.9181 59.1503 60.8922V52.1363C59.1503 47.4058 62.9851 43.5711 67.7155 43.5711H70.1361C74.8666 43.5711 78.7013 47.4058 78.7013 52.1363V91.8609C78.7013 95.5277 75.7288 98.5002 72.0621 98.5002C70.3483 98.5002 68.7009 97.8375 67.4645 96.6508L23.7291 54.6717C23.2788 54.2396 22.679 53.9983 22.0549 53.9983C20.7197 53.9983 19.6372 55.0807 19.6372 56.4159V89.935C19.6372 94.6654 15.8025 98.5002 11.072 98.5002H9.06241Z" fill="white"/>
              <path d="M59.1503 57.2414L59.1503 52.136C59.1503 47.4056 62.9851 43.5708 67.7156 43.5708H70.1362C74.8666 43.5708 78.7014 47.4056 78.7014 52.136V70.1975C70.1362 60.9584 67.7155 66.8785 59.1503 57.2414Z" fill="#E51636"/>
            </svg>

          </div>
          <div className="text-2xl mt-5 my-10">
            Boost Productivity with Every Click
          </div>

          <div className="text-lg container px-6 max-w-3xl">
            <span className="font-semibold">The Nation App</span> optimizes store operations by integrating scheduling, shift leadership,
            setup sheets, checklists, and HR functions into one platform. This consolidation streamlines
            workflows, reducing the time and complexity of managing multiple systems. The app enhances
            productivity and boosts team member efficiency by ensuring everyone is aligned and informed,
            leading to smoother operations, better communication, and improved organizational
            effectiveness.
          </div>

          <div className="container my-12 flex justify-center gap-4">
            <Link href={AppleAppStoreListingUrl} target={"_blank"}>
              <Image src={apple}  height={"80"} alt={"App Store"}/>
            </Link>
            <Link href={AppleAppStoreListingUrl} target={"_blank"}>
              <Image src={android}  height={"80"} alt={"App Store"}/>
            </Link>
          </div>

          <div className={"max-w-3xl"}>
            <div className="text-2xl mb-4">
              Key Features
            </div>
            <ul className={"text-md text-left"}>
              <li className={"pt-2"}><span className={"font-extrabold underline"}>Smart Scheduling</span>:
                Designed for and by Operators, the Nation App scheduling software
                enhances productivity by streamlining work shifts, labor laws, and task management. It
                saves leadership valuable hours by automating scheduling processes and reducing
                manual errors. With user-friendly features and real-time updates, the software ensures
                optimal resource allocation and efficient operations.
              </li>
              <li className={"pt-2"}><span className={"font-extrabold underline"}>Enhanced Shift Leads</span>:
                The Enhanced Shift Leads feature empowers shift leads by
                providing robust tools for team management, ensuring efficient and organized
                operations. It enables the optimization of setup sheets, facilitating seamless workflows
                and productivity. Additionally, this feature enforces compliance with all regulatory rules,
                streamlining oversight and accountability for shift leads.
              </li>
              <li className={"pt-2"}><span className={"font-extrabold underline"}>Task Optimization</span>:
                Task Optimization allows stores to set comprehensive daily, weekly,
                and monthly checklists, ensuring all tasks, big and small, are accomplished. By
                organizing and scheduling these tasks, it streamlines operations and enhances
                efficiency. This feature helps maintain consistency and accountability, ensuring nothing
                falls through the cracks in store management.
              </li>
              <li className={"pt-2"}><span className={"font-extrabold underline"}>HR Simplified</span>:
                The HR Simplified feature prioritizes team members by enabling
                operators and leadership to manage them easily through player cards. This intuitive
                system puts essential information at their fingertips, streamlining HR processes and
                enhancing team oversight. By empowering team members with clear, accessible data, it
                fosters an environment where individuals can excel and thrive.
              </li>
              <li className={"pt-2"}><span className={"font-extrabold underline"}>Data & Analytics Suite</span>:
                The Data and Analytics feature empowers operators to make
                informed decisions by providing clear, easily understandable information. This feature
                enhances productivity by offering actionable insights into operations and team
                performance. By leveraging these data-driven insights, operators can improve team
                member satisfaction and overall efficiency.
              </li>
            </ul>
          </div>

          <div className={"max-w-3xl"}>
            <div className="text-2xl mt-10 mb-4">
              How Nation App Enhances Operations
            </div>
            <ul className={"text-md text-left"}>
              <li className={"pt-2"}><span className={"font-extrabold underline"}>Time Management</span>:
                Our software streamlines operations, making team members more
                efficient by automating routine tasks and optimizing workflows. By reducing the time
                spent on manual processes, it lowers the required labor cost and allows team members
                to focus on more strategic activities. This increased efficiency boosts overall store
                productivity, leading to better performance and cost savings.
              </li>
              <li className={"pt-2"}><span className={"font-extrabold underline"}>Improved Productivity</span>:
                Our software leverages AI to provide precise marketing
                information, enabling targeted campaigns that drive increased revenue. By automating
                the analysis and reporting processes, it cuts down on wasted labor hours typically spent
                on manual data handling. This smart approach allows your team to focus on strategic
                initiatives, enhancing overall efficiency and profitability.
              </li>
              <li className={"pt-2"}><span className={"font-extrabold underline"}>Reduced Stress</span>:
                Our software delivers real-time information, empowering young
                leaders with the insights they need to manage their teams effectively. This instant
                access to data reduces the stressful burden of decision-making and team oversight. By
                streamlining communication and task management, it allows young leaders to focus on
                fostering a positive and productive work environment.
              </li>
              <li className={"pt-2"}><span className={"font-extrabold underline"}>Enhanced Team Member Retention</span>:
                Our software equips team members with the
                essential information they need to excel in their roles, fostering a culture of success and
                efficiency. Simultaneously, it provides leadership with valuable insights to optimize
                operations and support their teams effectively. This dual approach enhances overall
                productivity while promoting a better work-life balance for team members, leading to a
                more motivated and satisfied workforce.
              </li>
            </ul>
          </div>

          <div className="text-sm container mt-20 px-6 max-w-3xl">
            <p>
              Nation App use and transfer of information received from Google APIs to any other app will adhere to <a
              href={"https://developers.google.com/terms/api-services-user-data-policy"}
              className={"text-blue-600 hover:text-blue-800 transition duration-200 ease-in-out"}>Google API Services
              User Data Policy</a>, including the Limited Use requirements.
            </p>
          </div>
        </main>

        <footer className={"text-gray-500 mt-6 mx-4 mb-4 text-center"}>
          <div>Copyright © 2024 by HoneyDO LLC</div>
          <div className="mt-2">
            <Link href="/privacy"
                  className="text-blue-600 hover:text-blue-800 transition duration-200 ease-in-out">
              Privacy Policy
            </Link>
          </div>
        </footer>
      </div>
    </>
  );
};

export default Home;
