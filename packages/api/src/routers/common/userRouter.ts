import {employedStatsProcedure, t, verifiedProcedure} from "../../trpc";
import {
  chain,
  compact,
  countBy,
  filter,
  find,
  first,
  flatMap,
  forEach,
  includes,
  intersection,
  isEmpty,
  isUndefined,
  last,
  map,
  maxBy,
  minBy,
  omitBy,
  orderBy,
  partition,
  reduce,
  reject,
  some,
  times,
  uniq,
} from "lodash";
import {
  addressDto,
  BaseStoreDto,
  BusinessDto,
  businessDto,
  CoreValueDto,
  correctiveActionDto,
  createCorrectiveActionDto,
  editActionableItemDto,
  editCorrectiveActionDto,
  emailSchema,
  EmployeePositionTrainingRequestDto,
  employeePositionTrainingRequestDto,
  genAddressId,
  genCorrectiveActionId,
  genEmploymentId,
  genEmploymentRequestId,
  genGeneratorId,
  genImageId,
  genPersonId,
  genPositionTrainingHistoryId,
  genPositionTrainingHistoryRequestId,
  genPositionTrainingId,
  genScheduleAreaId,
  genScheduleEventId,
  genScheduleEventRecipientId,
  genScheduleId,
  genShiftId,
  genShiftOfferId,
  genStateMinorWorkGuidelinesId,
  genStoreEmploymentId,
  genStoreEmploymentRequestId,
  genUserRoleId,
  JobDto,
  MetadataDto,
  metadataDto,
  MetricTemplateDto,
  minorWorkGuidelines,
  newspaperDto,
  PermissionPackageDto,
  permissionPackageDto,
  permissionPackageId,
  PersonDetailDto,
  personDetailDto,
  personDto,
  PersonDto,
  personNoteDetailDto,
  phoneNumberSchema,
  policyInActionDto,
  positionMetricStatistic,
  presignedPostDto,
  RoleDto,
  roleDto,
  SaveImageDto,
  saveImageDto,
  scheduleBreakRuleDto,
  scheduleShiftDto,
  schedulingSettingsDto,
  StoreDashboardDto,
  storeDashboardDto,
  storeDto,
  testPhoneNumber,
  userDto,
} from "../../schemas";
import {allowRateOrThrow} from "../../rateLimiting";
import {presetTimesToLeadSchedule} from "../../util";
import {findExistingPersonByContactMethod, insertPerson, parseImportExcelWorksheet, PersonRow} from "../../excel.util";
import {z} from "zod";
import {TRPCError} from "@trpc/server";
import crypto from "crypto";
import {CopyObjectCommand, DeleteObjectCommand} from "@aws-sdk/client-s3";
import {
  allAllowedOrThrow,
  getDaysPersonCanEditSchedule,
  hasPermissionPackage,
  isAllowedFactory,
  isAllowedToMakeChanges,
  isEmployedByBusiness,
  throwIfDenied,
  throwIfNotEmployedByBusiness,
  toSecureStoreId,
  toSecureStoreIdOrThrow,
} from "../../authorization.util";
import {
  correctiveActionIncludes,
  correctiveActionIncludesWithHistory,
  getPersonMainEmployment,
  getPersonMainEmploymentOrRequest,
  getPersonMostRecentEmployment,
  getPersonMostRecentEmploymentRequest,
  isPermissionPackageStatement,
  noteHighSensitivityLevel,
  noteLowSensitivityLevel,
  noteMediumSensitivityLevel,
  permissionPolicyToPackages,
  PersonWithAcknowledgedEvents,
  personWithJobAndImageIncludes,
  shiftToDto,
  toAddressDto,
  toCorrectiveActionDto,
  toInvitationDto,
  toMinimalPersonDto,
  toPermissionPackageDto,
  toPersonDto,
  toPersonNoteDetailDto,
  toScheduleBreakRuleDto,
  toScheduleShiftsDto,
  toStateMinorWorkGuidelinesDto,
  toStoreAreaDto,
  toStoreDto
} from "../../schema.converters";
import {
  getDefaultInvitationMessage,
  getStoreOnboarderInvitationMessage,
  resendBusinessInvitation,
  sendBusinessInvitation
} from "../../invitations";
import {addressCreateDto} from "../gioa-web-app/admin.schemas";
import {throwIfImageNotExistsInS3} from "../../image.util";
import {RoleType} from "../../types";
import Excel from "exceljs";
import {isPersonEmploymentRequestSubmitted} from "../../person.util";
import {getCurrentUser} from "../../user";
import {
  acknowledgeCorrectiveAction,
  archiveCorrectiveAction,
  buildImageCreateInput,
  correctiveAction,
  createInReviewCorrectiveAction,
  formalizableCa,
  formalizeCorrectiveAction,
  formalizedCorrectiveAction,
  getCorrectiveActionCreateInput,
  hydrateCorrectiveAction,
  unacknowledgedCorrectiveAction,
  unArchiveCorrectiveAction,
  updateActionableItem,
  updateCorrectiveAction
} from "../../corrective-actions";
import * as Sentry from "@sentry/node";
import {DatabasePermissionPolicy, PackageId, PermissionPackageStatement} from "../../permission.types";
import {
  getAllowedPermissionPackageIdsForEmployment,
  getPackagesGranted,
  mergeJobAndPersonPolicies,
  permissionPackages
} from "../../permissionPackages";
import {generatePersonAiBio} from "../../ai";
import {combinePermissionPolicies} from "../../permissions";
import {
  approvedPersonAvailability,
  availabilityStatusFilter,
  declinedPersonAvailability,
  draftPersonAvailability,
  maxDaysPreferredInt,
  maxHoursPreferredInt,
  pendingPersonAvailability,
  personAvailabilityDto,
} from "../../availabilitySchemas";
import {
  approvePersonAvailability,
  createPersonAvailability,
  declinePersonAvailability,
  getPersonAvailabilityStatusWhereCondition,
  setDayRanges,
  submitPersonAvailability,
  toApprovedPersonAvailability,
  toDbPersonAvailabilityCreateInput,
  toDbPersonAvailabilityUpdateInput,
  toDeclinedPersonAvailability,
  toDraftPersonAvailability,
  toEditablePersonAvailability,
  toPendingPersonAvailability,
  toPersonAvailability,
  toPersonAvailabilityDto,
  unsubmitPersonAvailability,
} from "../../personAvailability";
import {
  dayParts,
  draftSchedule,
  hawaiiACAPersonHours,
  peakHours,
  publishedSchedule,
  scheduleDto,
  scheduleRevisionWithForecast
} from "../../scheduleSchemas";
import {
  getInitDraftScheduleParams,
  getSchedulesForDatesWithShiftsForPerson,
  hydrateIgnoredValidationMessages,
  hydrateSchedule,
  sanitizeDraftSchedule,
  storeRowToPartialCreateDraftScheduleParams,
  toDbScheduleCreateInput,
  toDraftSchedule
} from "../../scheduling";
import {dailyTimeRange, dateNoTime, dateTimeRange, dayOfWeek, isoWeekDate} from "../../timeSchemas";
import {
  dateStrToDbDate,
  dateTo24HrTime,
  formatDateForNotification,
  getDateFromWeekDayTime,
  getDateIsoWeek,
  getIsoWeekDateRangeInTimezone,
  getIsoWeeksOverlappingRange,
  isIsoWeekBefore,
  timeToDbTime,
  utcNow
} from "../../date.util";
import {
  getPersonDetail,
  getPersonDetailParamsFromEmploymentAndRequest,
  getPersonPositionStatistics,
  getRequesterPermissionsOnPerson
} from "../../person";
import {
  attachFileToScheduleEvent,
  canViewScheduleEventRecipient,
  deleteManyScheduleEventFileAttachments,
  detachFileFromScheduleEvent,
  findScheduleEventFileAttachments,
  findScheduleEventsForRange,
  findScheduleEventsForRangeRaw,
  getScheduleEventVisibilityLevelString,
  ScheduleEventCustomVisibility,
  toScheduleEventDto,
  updateScheduleEventFileAttachment
} from "../../scheduleEvents";
import statesDefaultChildLaborLaws from "../../childLaborLawsData";
import {PackageIds} from "../permissionPackageIds";
import {PrismaClientKnownRequestError} from "@prisma/client/runtime/library";
import {processBatchesWithConcurrency} from "../../asyncBatchProcessing";
import {
  getTimeOffStatus,
  getTimeOffStatusWhereCondition,
  hydrateTimeOff,
  toTimeOffAdminDto,
  toTimeOffDto
} from "../../personTimeOff";
import {isWithinAvailability, isWithinTimeOff, shiftRangeToDateRange} from "../../scheduleValidation";
import {
  acceptShiftOffer,
  approveShiftOffer,
  cancelShiftOffer,
  cancelShiftOfferAcceptance,
  createPendingShiftOffer,
  declineShiftOffer,
  hydrateShiftOffer,
  toAcceptedShiftOffer,
  toCancellableShiftOffer,
  toShiftDto,
  toShiftOfferCreateInput,
  toShiftOfferDto,
  toShiftOfferUpdateInput
} from "../../personShiftOffers";
import {getAllSchedulePeopleAtStore} from "../../schedulePeople";
import * as Prisma from "@prisma/client";
import {
  shiftConflictDto,
  ShiftConflictDto,
  timeOffAdminDto,
  timeOffDto,
  timeOffStatusFilter
} from "../../personTimeOff.schemas";
import {UTCDate} from "@date-fns/utc";
import {notifyPerson} from "../../notifications";
import {NotificationCode} from "../../notifications.types";
import {queryShiftConflicts} from "../../shiftConflicts";
import {getEffectiveAvailability} from "../../getEffectiveAvailability";
import {hydrateScheduleNoteDto, scheduleNoteDto, scheduleNoteMeta} from "../../note.schemas";
import {shiftDto, shiftOfferDto, shiftOfferStatusFilter} from "../../personShiftOffers.schemas";
import {publishScheduleInTransaction} from "../../publishScheduleWithSideEffects";
import {createDefaultSchedule} from "../../defaultSchedule";
import {loadTimeOffContext} from "../../personTimeOff.effects";
import {subDays} from "date-fns";
import {isStoreAreaReadonly} from "../../storeAreas";
import {isStorePositionReadonly} from "../../storePositions";
import {bohAreaId, fohAreaId} from "../../stockAreaTemplates";
import {excludeShiftsFromDraftSchedule} from "../../excludeShiftsFromDraftSchedule";
import {DateTime} from "luxon";
import {
  DashboardShiftOfferDto,
  PlayerStoreDashboardDto,
  playerStoreDashboardDto,
  toDashboardShiftOfferDto
} from "../../dashboardSchemas";
import {filterEligibleForShiftOffer, getShiftOfferStatusWhereCondition} from "../../shiftOffer";
import {scheduleEventDto} from "../../scheduleEventSchemas";
import {getBaseEvent} from "../../domainEvents";
import {getWeatherForecast} from "../../weather";
import {weatherForecastDto} from "../../weather.types";
import {maxSmsMessageLength} from "../../sms";
import {createGenerator, deleteGenerator, updateGenerator} from "../../reminders";
import {
  createQstashMessage,
  deleteDbGenerator,
  deleteDbScheduledEvents,
  deleteQstashMessages,
  getScheduledEvents,
  insertGenerator,
  insertScheduledEvents,
  sendEmailWithNationAppWebLink,
  sendSchedulerInformationEmail,
  updateDbGenerator
} from "../../effects";
import {getAvailabilityRequestDetails} from "../../personAvailability.effects";
import {ignoredValidationMessages} from "../../scheduleValidation.schemas";
import {schedulePersonDto} from "../../schedulePersonDto";
import {getAvailabilityRequestStatus} from "../../getAvailabilityRequestStatus";
import {validateSplitShiftTimes} from "../../splitShift.util";
import {generateSMSHash} from "../../contentHash";
import {copyDay} from "../../scheduleTemplates";
import {getBulkImportLink} from "../../bulkImportLinks";
import {v4 as uuidv4} from "uuid";
import {getStatus as getShiftOfferStatus} from "../../personShiftOffer.util";
import {getShiftDay, hydrateScheduleShift, startEditingPublishedSchedule} from "../../scheduling.util";
import {filterToShiftsTodayAssignedToPerson, getLiveChecklistsAssignedToPerson} from "../../checklists";
import {isChecklistProgressComplete} from "../../checklist.util";
import {getHasShiftLeaderAccess} from "../../hasShiftLeaderAccess";
import {generateIsoWeekDates, takeFromGenerator} from "../../scheduleBuilder.util";
import {getPublishedScheduleForWeekIfAllowed, isAllowedToGetPublishedSchedule} from "../../schedulingDb";
import {createMetricDataPoint} from "../../metricsDb";
import {
  getEarliestValidAvailabilityRequestStartTime,
  hydrateAvailabilityRestrictions
} from "../../availabilityRestrictions";
import * as perm from "../../permissionChecks";
import {canGetProficiency, canListCorrectiveActions} from "../../permissionChecks";
import {hydrateNotification} from "../../notifications.map";
import {notification} from "../../notifications.schema";
import {getShiftSwapStatusWhereCondition, toShiftSwapDto} from "../../shiftSwap";
import {diffItems} from "../../nestedUpdates";
import {getFileAttachmentS3Key, getFileS3KeyPrefix} from "../../fileAttachment.util";
import {attachmentMediaType, fileAttachmentDto, toFileAttachmentDto} from "../../fileAttachment.dto";
import {genFileAttachmentId, genFileId} from "../../fileAttachment.schemas";
import {storeResourceDto, toStoreResourceDto} from "../../storeResource.dto";
import {doesCorrectiveActionPassPermissionChecks} from "../../correctiveActionDb";
import {copyImagesInS3, genNextEntityNoteVersion, toEntityNoteHistoryCreateInput} from "../../entityNote";
import {addFileAttachments, reconcileFileAttachments} from "../../fileAttachmentDb";
import {handleCommandAndUpdateProjections} from "../../eventSourcing/handleCommandAndUpdateProjections";
import {
  ApproveTimeOffRequestCommand,
  CancelTimeOffRequestCommand,
  CreateTimeOffRequestCommand,
  DeclineTimeOffRequestCommand,
  UpdateTimeOffRequestCommand
} from "../../timeOff/timeOffCommand";
import {hydrateScheduleHourlySalesForecast} from "../../dataFileDb";
import {validScheduleHourlySalesForecast} from "../../scheduling/metrics/scheduleHourlySalesForecast/scheduleHourlySalesForecastTypes";
import {getLiveChecklistsAssignedToPerson as getLiveChecklists2AssignedToPerson} from "../../checklists/checklistAssignment/checklistAssignment";
import {getTeamMemberDocument3Key, getTeamMemberDocumentsS3KeyPrefix} from "../../teamDocuments.util";
import {mapToHawaiiACAMultiWeekHours} from "../../utils/multiWeekSchedules.util";
import {getSubscriptionCommitmentType} from "../../payments/subscriptions";
import {vendorDto} from "../../vendor.dto";
import {parseTimePunchPdfFromS3, TimePunchData, validateTimePunchData} from "../../timePunchPdf.util";
import {
  aggregateVarianceRowsByTeamMember,
  calculateTeamTotalsSummary,
  calculateTotalVarianceCost,
  combineTimePunchesWithUnpaidTimePunches,
  constructVarianceRows,
  filterTeamTotalsRows,
  filterVarianceRows,
  matchShiftsAndTimePunches,
  sortTeamTotalsRows,
  sortVarianceRows
} from "../../timePunch.util";
import {
  calculateHRInsightsSummary,
  convertCorrectiveActionsToInsights,
  convertPersonNotesToInsights,
  convertPositionScoresToInsights,
  filterHRInsightsEntries,
  hydrateCorrectiveAction as hydrateCorrectiveActionForHR,
  hydratePersonNote,
  hydratePersonWithMetadata,
  sortHRInsightsEntries
} from "../../hr.util";
import {
  baseTimePunchEntrySchema,
  teamTotalsEntrySchema,
  teamTotalsSummarySchema,
  timePunchEntryInputSchema,
  timePunchEntryOutputSchema,
  timePunchVarianceEntrySchema
} from "../../timePunch.schemas";
import {hrInsightsEntrySchema, hrInsightsSummarySchema} from "../../hr.schemas";
import {storeDeviceInfo} from "../../store/devices/storeDeviceTypes";
import {appendSignatureToPdf, uploadPdfToS3} from "../../pdfSignature.util";
import {
  processTeamMemberTrainingData,
  calculateTrainingSummaryWithAreas,
  filterPeopleByPersonId,
  filterTrainingDataByArea, personId, positionId,
} from "../../TeamMemberTrainingInsights.util";
import {onboardingTrainingStepDto} from "../../onboardingTrainingAreas";
import {
  filterPersonByName,
  getTotalScheduledHours,
  mapOffersIntoInsightsDto,
  mapShiftsToInsightsDto,
  mapShiftSwapsToInsightsDto,
  mapTimeOffToInsightsDto,
  reduceShiftOffersPerPerson,
  reduceShiftsPerPerson,
  reduceShiftSwapsPerPerson,
  reduceTimeOffPerPerson
} from "../../insights/scheduling";
import {schedulingInsightsData, schedulingInsightsShiftData} from "../../insights/scheduling.schema";
import {
  deactivateChatUser,
  personToChatUser,
  reactivateChatUser,
  removeChatUserImage,
  syncChatUsers,
  updateChatUserImage,
  updateChatUserPreferredLanguage,
  upsertAndSyncChatUsers
} from "../../chat/chatUsers";
import {getPresignedPost} from "../../getPresignedPost";
import {getS3Key} from "../../getS3Key";
import {syncPrivateChannelMemberships} from "../../chat/syncChannelMembership";
import {SecureStoreId} from "../../database.types";

export const userRouter = t.router({
  getUserProfile: verifiedProcedure
    .output(userDto)
    .query(async ({ctx}) => {
      const scopeToStoreId = ctx.auth.session.scopeToStoreId;
      return getCurrentUser(ctx, true, scopeToStoreId ? scopeToStoreId : undefined);
    }),

  deleteUser: verifiedProcedure
    .output(z.void())
    .mutation(async ({ctx}) => {
      // this procedure does not check for employment, because pending status users also need to be able to delete themselves
      // This is for the person deleting their own account
      await ctx.db.user.suspendUser(ctx.auth.user.id);
    }),

  registerPushToken: verifiedProcedure
    .input(z.object({
      token: z.string(),
      deviceType: z.enum(['ios', 'android'])
    }))
    .mutation(async ({ctx, input: {token, deviceType}}) => {
      return ctx.db.user.registerPushToken(ctx.auth.user.id, {token, deviceType});
    }),

  // @DEPRECATED - Remove in future versions, no longer in use
  registerFirebaseToken: verifiedProcedure
    .input(z.object({
      firebaseToken: z.string(),
      deviceId: z.string(),
      deviceType: z.enum(['ios', 'android']),
    }))
    .mutation(async ({ctx, input: {firebaseToken, deviceId, deviceType}}) => {
    }),

  // @DEPRECATED - Remove in future versions, no longer in use
  getUserFirebaseToken: verifiedProcedure
    .input(z.object({
      deviceId: z.string(),
    }))
    .output(z.object({
      token: z.string().optional(),
    }))
    .query(async ({ctx, input}) => {
      return {
        token: undefined
      };
    }),

  setInitDestination: verifiedProcedure
    .input(z.object({
      initDestination: z.string()
    }))
    .output(z.void())
    .mutation(async ({ctx, input}) => {
      // This procedure does not check for employment, because pending status users also need to be able to set their init destination as they complete their onboarding
      await ctx.db.user.setInitDestination(ctx.auth.user.id, input.initDestination);
    }),

  updatePersonMetadata: verifiedProcedure
    .input(metadataDto)
    .output(z.void())
    .mutation(async ({ctx, input}) => {
      // This procedure does not check for employment, because pending status users also need to be able to update their metadata, such as entering their birthdate during onboarding

      // The personId is secure here because it comes from a trusted source -- the user's session and database record
      const personId = ctx.auth.user.person.id;

      await ctx.db.person.updatePersonMetadata(personId, input);
    }),

  updatePreferredLanguage: verifiedProcedure
    .input(z.object({
      preferredLanguage: z.enum(["en", "es"])
    }))
    .output(z.void())
    .mutation(async ({ctx, input}) => {
      // This procedure does not check for employment, because pending status users also need to be able to update their preferred language

      // The personId is secure here because it comes from a trusted source -- the user's session and database record
      const personId = ctx.auth.user.person.id;
      await ctx.db.person.updatePreferredLanguage(personId, input.preferredLanguage);

      // purposefully not awaiting
      updateChatUserPreferredLanguage(ctx, {
        personId,
        preferredLanguage: input.preferredLanguage
      });
    }),

  removeProfileImage: verifiedProcedure
    .output(z.void())
    .mutation(async ({ctx}) => {
      // This procedure does not check for employment, because pending status users also need to be able to remove their profile image

      // The personId is secure here because it comes from a trusted source -- the user's session and database record
      const personId = ctx.auth.user.person.id;

      // get the user profile image
      const person = await ctx.db.person.getPerson(personId, {profileImage: true});

      if (person?.profileImage) {
        // delete the image from S3
        await ctx.s3Client.send(new DeleteObjectCommand({
          Bucket: ctx.s3MediaBucket,
          Key: person.profileImage.s3ObjectKey
        }));

        // update the user profile to remove the profile image
        await ctx.db.person.removeProfileImage(personId);

        // purposefully not awaiting
        removeChatUserImage(ctx, personId);
      }
    }),

  saveProfileImage: verifiedProcedure
    .input(saveImageDto)
    .output(z.object({
      profileImageUrl: z.string()
    }))
    .mutation(async ({ctx, input}) => {
      // This procedure does not check for employment, because pending status users also need to be able to save their profile image

      // The personId is secure here because it comes from a trusted source -- the user's session and database record
      const personId = ctx.auth.user.person.id;
      const userId = ctx.auth.user.id;

      const imageS3Key = getS3Key({prefix: personId, objectId: input.imageId});
      await throwIfImageNotExistsInS3({
        imageS3Key: imageS3Key,
        ctx: ctx
      });

      await ctx.db.person.createProfileImage(personId, userId, input);
      const imageUrl = ctx.getImageUrl(personId, input.imageId);

      // purposefully not awaiting
      updateChatUserImage(ctx, {
        personId,
        imageUrl: imageUrl
      });

      return {
        profileImageUrl: imageUrl
      };
    }),

  removeTeamMemberProfileImage: employedStatsProcedure({feature: "hr"})
    .input(z.object({
      personId: z.string()
    }))
    .output(z.void())
    .mutation(async ({ctx, input}) => {
      const {
        businessId,
        employment: userEmployment
      } = ctx;

      const person = await ctx.db.person.getPersonInBusiness(businessId, input.personId, {
        profileImage: true,
        user: true
      });

      throwIfDenied({
        ctx, principalEmployment: userEmployment,
        action: "person/uploadImage",
        resource: `business/${businessId}/person/${input.personId}`,
        resourceEntity: {
          ownerId: person.user?.id ?? undefined,
        }
      });

      // delete the old image
      if (person.profileImage) {
        await ctx.s3Client.send(new DeleteObjectCommand({
          Bucket: ctx.s3MediaBucket,
          Key: person.profileImage.s3ObjectKey
        }));

        // purposefully not awaiting
        removeChatUserImage(ctx, person.id);
      }

      await ctx.db.person.removeProfileImage(person.id);
    }),

  updateTeamMemberProfileImage: employedStatsProcedure({feature: "hr"})
    .input(z.object({
      personId: z.string(),
      image: saveImageDto
    }))
    .output(z.void())
    .mutation(async ({ctx, input}) => {
      const {prisma} = ctx;
      const userId = ctx.auth.user.id;
      const {
        businessId,
        employment: userEmployment
      } = throwIfNotEmployedByBusiness(ctx);

      const person = await ctx.db.person.getPersonInBusiness(businessId, input.personId, {
        profileImage: true,
        user: {
          select: {
            id: true
          }
        }
      });

      throwIfDenied({
        ctx, principalEmployment: userEmployment,
        action: "person/uploadImage",
        resource: `business/${businessId}/person/${input.personId}`,
        resourceEntity: {
          ownerId: person.user?.id ?? undefined,
        }
      });

      // delete the old image
      if (person.profileImage) {
        await ctx.s3Client.send(new DeleteObjectCommand({
          Bucket: ctx.s3MediaBucket,
          Key: person.profileImage.s3ObjectKey
        }));

        await ctx.db.person.removeProfileImage(person.id);
      }

      const imageS3Key = getS3Key({prefix: person.id, objectId: input.image.imageId});
      await throwIfImageNotExistsInS3({
        imageS3Key: imageS3Key,
        ctx: ctx
      });

      await ctx.db.person.createProfileImage(person.id, userId, input.image);

      // purposefully not awaiting
      updateChatUserImage(ctx, {
        personId: person.id,
        imageUrl: ctx.getImageUrl(person.id, input.image.imageId)
      });
    }),

  generateAndSaveAIBio: verifiedProcedure
    .output(z.void())
    .mutation(async ({ctx}) => {
      // This procedure does not check for employment, because it will be called by the onboarding flow

      const personId = ctx.auth.user.person.id;
      const person = await ctx.db.person.getPerson(personId, {});

      try {
        const bio = await generatePersonAiBio({person, openai: ctx.openai});
        if (!bio) {
          Sentry.captureMessage("Failed to generate AI bio for person", {
            extra: {
              personId: personId,
            }
          });
          return;
        }

        await ctx.db.person.updatePersonMetadata(personId, {bio});
      } catch (e) {
        Sentry.captureException(e);
        return;
      }
    }),

  // a getPresignedPost that is not limited and requires permissions
  getPresignedPostForTeamMember: employedStatsProcedure({feature: "hr"})
    .input(z.object({
      personId: z.string(),
      prefix: z.string().optional(),
      contentType: z.string()
    }))
    .output(presignedPostDto)
    .mutation(async ({ctx, input}) => {
      const userId = ctx.auth.user.id;
      const {s3MediaBucket, s3Client} = ctx;

      await allowRateOrThrow({
        key: userId,
        group: "getPresignedPost",
        maxRequestsPerDuration: 400,
        prisma: ctx.prisma
      });

      const {
        businessId,
        employment: userEmployment
      } = ctx;

      const person = await ctx.db.person.getPersonInBusiness(ctx.businessId, input.personId, {user: true});

      throwIfDenied({
        ctx, principalEmployment: userEmployment,
        action: "person/uploadImage",
        resource: `business/${businessId}/person/${input.personId}`,
        resourceEntity: {
          ownerId: person.user?.id,
        }
      });

      const twentyFiveMegs = 25 * 1024 * 1024;
      const securityPrefix = input.personId;
      const newImageId = genImageId();
      const key = getS3Key({prefix: securityPrefix, objectId: newImageId});
      const presignedPost = await getPresignedPost({
        s3Client: s3Client,
        s3Bucket: s3MediaBucket,
        maxSizeBytes: twentyFiveMegs,
        securityPrefix: securityPrefix,
        contentType: input.contentType,
        key: key
      });

      return {
        newId: newImageId,
        newImageId,
        key,
        presignedPost,
      };
    }),

  getPresignedPost: verifiedProcedure
    .input(z.object({
      prefix: z.string().optional(),
      contentType: z.string()
    }))
    .output(presignedPostDto)
    .mutation(async ({ctx, input}) => {
      // This procedure does not check for employment, because pending status users also need to be able to upload their headshot in onboarding

      const userId = ctx.auth.user.id;
      const {s3MediaBucket, s3Client} = ctx;

      await allowRateOrThrow({
        key: userId,
        group: "getPresignedPost",
        maxRequestsPerDuration: 100,
        prisma: ctx.prisma
      });

      // user can upload max 100 photos
      const imageCount = await ctx.db.user.countImagesUploadedByUser(userId);
      if (imageCount >= 100) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "You have reached the maximum number of images you can upload"
        });
      }

      const twentyFiveMegs = 25 * 1024 * 1024;
      const person = ctx.auth.user.person;
      const securityPrefix = person.id;
      const newImageId = genImageId();
      const key = getS3Key({prefix: securityPrefix, objectId: newImageId});
      const presignedPost = await getPresignedPost({
        s3Client: s3Client,
        s3Bucket: s3MediaBucket,
        maxSizeBytes: twentyFiveMegs,
        securityPrefix: securityPrefix,
        contentType: input.contentType,
        key: key
      });

      return {
        newImageId,
        newId: newImageId,
        key,
        presignedPost,
      };
    }),

  getDelegatedStoreAdmins: employedStatsProcedure({feature: "onboarding"})
    .output(z.array(personDto))
    .query(async ({ctx}) => {
      // TODO new permissions
      const currentPerson = ctx.auth.user.person;
      const adminPeople = await ctx.db.store.getDelegatedStoreAdmins(ctx.businessId);

      // do not include the current person in the list of admins or the operator themselves
      const admins = reject(adminPeople, a => a.id === currentPerson.id || a.employments[0]?.job?.createdFromTemplateId === "operator");

      return map(admins, (admin) => {
        const latestInvitation = maxBy(admin.invitations, i => i.sentAt);
        const invitedPerson: PersonDto = {
          ...toPersonDto(ctx, admin),
          invitation: latestInvitation ? toInvitationDto(latestInvitation) : undefined
        };
        return invitedPerson
      })
    }),

  addStoreToDelegatedStoreAdminInvite: employedStatsProcedure({feature: "onboarding"})
    .input(z.object({
      personId: z.string(),
      storeId: z.string()
    }))
    .output(z.void())
    .mutation(async ({ctx, input}) => {
      const {businessId} = ctx;
      const storeId = toSecureStoreIdOrThrow(ctx, input.storeId);
      // TODO new permissions

      const adminPerson = await ctx.db.person.getPersonInBusiness(businessId, input.personId, {
        employments: {
          include: {
            stores: true
          }
        }
      });

      const existingStoreEmployment = find(adminPerson.employments[0].stores, s => s.storeId === input.storeId);
      if (!existingStoreEmployment) {
        await ctx.db.store.createStoreEmployment(storeId, adminPerson.employments[0].id);
      }

      if (adminPerson.email) {
        await sendSchedulerInformationEmail({
          recipient: adminPerson.email,
          sendEmail: ctx.sendEmail,
        })
      }
    }),

  addEmployeeToNewStore: employedStatsProcedure({feature: "hr"})
    .input(z.object({
      personId: z.string(),
      toStoreId: z.string(),
    }))
    .output(z.void())
    .mutation(async ({ctx, input: {personId, toStoreId}}) => {
      const {
        businessId,
        employment: userEmployment
      } = ctx;
      const storeId = toSecureStoreIdOrThrow(ctx, toStoreId);

      const person = await ctx.db.person.getPersonInBusiness(businessId, personId, {
        employments: {
          include: {
            stores: true
          }
        },
        employmentRequests: {
          where: {
            isApproved: false,
          },
          include: {
            stores: true
          }
        },
        user: true,
      });

      const employment = getPersonMainEmployment(person.employments);
      if (!employment) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Person with employment in store not found",
        });
      }

      const toEmploymentRequest = find(person.employmentRequests, er => er.stores.some(store => store.storeId === toStoreId));
      if (toEmploymentRequest) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Person with employment request in store already exists",
        });
      }

      throwIfDenied({
        ctx,
        principalEmployment: userEmployment,
        action: "person/update",
        resource: `business/${businessId}/person/${person.id}`,
        resourceEntity: {
          id: person.id,
          businessId: person.businessId,
          ownerId: person.user?.id,
        }
      });

      await ctx.db.store.createEmploymentRequestForStore(businessId, {
        storeId: storeId,
        personId: person.id,
        jobId: employment.jobId
      });
    }),

  removeEmployeeFromStore: employedStatsProcedure({feature: "hr"})
    .input(z.object({
      personId: z.string(),
      storeId: z.string()
    }))
    .output(z.void())
    .mutation(async ({ctx, input}) => {
      const {
        businessId,
        employment: userEmployment
      } = ctx;
      const storeId = toSecureStoreIdOrThrow(ctx, input.storeId);
      // TODO new permissions

      const person = await ctx.db.person.getPersonInBusiness(businessId, input.personId, {
        employments: {
          include: {
            stores: true
          }
        },
        employmentRequests: {
          include: {
            stores: true
          }
        },
        user: true,
      });

      const employment = getPersonMainEmployment(person.employments);
      const employmentRequest = find(person.employmentRequests, er => !er.isApproved && er.stores.some(store => store.storeId === input.storeId));

      if (!employment) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Person with employment in store not found",
        });
      }

      const storeEmployment = find(employment.stores, s => s.storeId === input.storeId);
      const storeEmploymentRequest = find(employmentRequest?.stores, s => s.storeId === input.storeId);

      if (!storeEmployment && !storeEmploymentRequest) {
        Sentry.captureEvent({
          message: "Tried to remove person from store that they are not assigned to. This should not happen, but it did. I'm sorry.",
          extra: {personId: person.id, storeId: input.storeId}
        });
      }

      const hasOtherStoreEmployment = some(person.employments, e => some(e.stores, s => s.storeId !== input.storeId));
      if (!hasOtherStoreEmployment) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Team member must be employed in at least one store."
        });
      }

      throwIfDenied({
        ctx,
        principalEmployment: userEmployment,
        action: "person/update",
        resource: `business/${businessId}/person/${person.id}`,
        resourceEntity: {
          id: person.id,
          businessId: person.businessId,
          ownerId: person.user?.id,
        }
      });

      if (storeEmployment) {
        await ctx.db.store.deleteStoreEmploymentsForEmployment(storeId, employment.id);
      } else if (storeEmploymentRequest) {
        await ctx.db.store.deleteStoreEmploymentRequestsForEmploymentRequest(storeId, storeEmploymentRequest.employmentRequestId);
      }
    }),

  /**
   * Invite a team member to a store. This should only be used when the team member is created but no invitation sent yet. The only case that can happen is when adding the person via bulk import and not hitting "Send Invitations" in the last step of the bulk import process. If someone did that, they're a naughty boy. But we give a way here to get out of that situation.
   */
  invitePersonToStore: employedStatsProcedure({feature: "hr"})
    .input(z.object({
      storeId: z.string(),
      personId: z.string(),
    }))
    .output(z.void())
    .mutation(async ({ctx, input}) => {
      const {businessId, employment, checkAllowed} = ctx;
      const storeId = toSecureStoreIdOrThrow(ctx, input.storeId);

      allAllowedOrThrow({
        checkAllowed,
        actions: [
          "business/createBusinessInvitation",
        ],
        resource: `business/${businessId}`,
      });

      allAllowedOrThrow({
        checkAllowed,
        actions: ["store/createEmployment"],
        resource: `business/${businessId}/store/${input.storeId}`,
        resourceEntity: {
          businessId: businessId,
          storeId: input.storeId
        }
      });

      const person = await ctx.db.person.getPersonInBusiness(businessId, input.personId, {
        user: {
          include: {
            devicePushTokens: true,
          }
        },
        notificationSettings: true,
        employments: {
          include: {
            job: true,
            stores: true
          }
        },
        employmentRequests: {
          include: {
            job: true,
            stores: true
          }
        },
        invitations: true
      });

      // if the person is already employed at this store, don't send an invitation
      if (some(person.employments, e => some(e.stores, s => s.storeId === input.storeId))) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Team member is already employed at this store. Cannot send an invitation. Refresh your app by pulling down on the screen and trying again."
        });
      }

      // if the person doesn't have an employment request at this store, don't send an invitation
      if (!some(person.employmentRequests, e => some(e.stores, s => s.storeId === input.storeId))) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Team member does not have an employment request at this store. Cannot send an invitation."
        });
      }

      // if the person already has an invitation for this business, don't send an invitation
      if (some(person.invitations, i => i.businessId === businessId)) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Team member already has an invitation. Cannot send another. Refresh your app by pulling down on the screen. \n\nOr check that the team member is approved in all other stores at this business. Then if you want to add them to this store, edit their profile in the other store to add them to this store."
        });
      }

      const senderStore = await ctx.db.store.getStore(storeId);
      const senderPerson = ctx.auth.user.person;
      const defaultMessage = getDefaultInvitationMessage({
        person: person,
        senderPerson: senderPerson,
        senderStore: senderStore
      })

      await sendBusinessInvitation({
        ctx,
        input: {
          businessId,
          person: person,
          subject: "Welcome to Nation!",
          message: defaultMessage,
          requiresApproval: true,
          roleId: null,
          initDestination: "dest_onboarding"
        }
      });
    }),

  createAndInvitePerson: employedStatsProcedure({feature: "hr"})
    .input(z.object({
      firstName: z.string(),
      lastName: z.string(),
      phoneNumber: phoneNumberSchema.optional(),
      email: emailSchema.optional(),
      jobId: z.string().optional(),
      storeIds: z.array(z.string()),
      preferredLanguage: z.enum(["en", "es"]).optional(),
      transport: z.enum(["email", "phone"]),
      requiresApproval: z.boolean(),
      message: z.string().optional(),
      subject: z.string().optional(),
      initDestination: z.string()
    }))
    .output(z.void())
    .mutation(async ({ctx, input}) => {
      const {businessId, employment} = ctx;
      const storeIds = map(input.storeIds, storeId => toSecureStoreIdOrThrow(ctx, storeId));
      const checkAllowed = isAllowedFactory({businessId, principalEmployment: employment, userId: ctx.auth.user.id})
      allAllowedOrThrow({
        checkAllowed,
        actions: [
          "business/createPerson",
          "business/createEmployment",
          "business/createBusinessInvitation",
        ],
        resource: `business/${businessId}`,
      });

      for (const storeId of storeIds) {
        allAllowedOrThrow({
          checkAllowed,
          actions: ["store/createEmployment"],
          resource: `business/${businessId}/store/${storeId}`,
          resourceEntity: {
            businessId: businessId,
            storeId: storeId
          }
        });
      }

      if (!input.phoneNumber && !input.email) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Person must have an email or phone number"
        });
      }

      const isTestPhoneNumber = input.phoneNumber === testPhoneNumber;

      // TODO users cannot invite other users with roles greater than their own (otherwise they
      // could invite themselves as a higher role!)

      const jobs = await ctx.prisma.job.findMany({
        where: {
          businessId
        }
      });
      // Default job should be 'Team Member', if it doesn't exist
      // then use the job with the least amount of permissions.
      let defaultJob = find(jobs, job => job.title === "Team Member");
      if (!defaultJob) {
        const maxLevel = maxBy(jobs, job => job.level)!.level;
        defaultJob = last(filter(jobs, job => job.level === maxLevel))
      }

      if (!defaultJob) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "There are no jobs defined for this store"
        });
      }

      const existingPerson = await ctx.prisma.person.findFirst({
        where: {
          OR: [{
            email: input.email
          }, {
            phoneNumber: input.phoneNumber
          }]
        }
      });
      if (existingPerson && existingPerson.businessId === businessId) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "A person with that email or phone number already exists in your business. Cannot create another one."
        });
      } else if (existingPerson && existingPerson.businessId !== businessId) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "A person with that email or phone number already has a Nation account with another Operator's business. It's not currently supported to allow users to log into multiple businesses. Please contact Nation support if this is a feature you need."
        });
      }

      const jobId = input.jobId ?? defaultJob.id;
      const person = await ctx.prisma.person.create({
        data: {
          id: genPersonId(),
          business: {
            connect: {
              id: businessId
            }
          },
          email: input.email,
          firstName: input.firstName,
          lastName: input.lastName,
          phoneNumber: input.phoneNumber,
          preferredLanguage: input.preferredLanguage ?? "en",
          // If the invitation requires approval, we don't straightaway create the Person's Employment.
          // In that case, there is a 3-step process to creating the actual Employment:
          // 1. We send out the business invitation with requiresApproval set to true. At the same
          // time, we create EmploymentRequest and StoreEmploymentRequest for them.
          // 2. The person logs in and creates their PositionTrainingHistoryRequest by going through onboarding UI
          // 3. The business admin approves the request, which creates the actual Employment,
          // StoreEmployment, PositionTrainingHistory, and UserEmploymentRole records.
          ...input.requiresApproval ? {
            employmentRequests: {
              create: {
                id: genEmploymentRequestId(),
                isApproved: false,
                job: {
                  connect: {
                    id: jobId
                  }
                },
                stores: {
                  createMany: {
                    data: map(storeIds, storeId => {
                      return {
                        id: genStoreEmploymentRequestId(),
                        storeId: storeId
                      }
                    })
                  }
                }
              }
            }
          } : {
            employments: {
              create: {
                id: genEmploymentId(),
                job: {
                  connect: {
                    id: jobId
                  }
                },
                business: {
                  connect: {
                    id: businessId
                  }
                },
                stores: {
                  createMany: {
                    data: await Promise.all(map(storeIds, async storeId => {
                      return {
                        id: genStoreEmploymentId(),
                        storeId: storeId,
                        storeLoginPin: await ctx.db.store.generateUniqueStoreLoginPin(storeId)
                      }
                    }))
                  }
                }
              }
            }
          }
        },
        include: {
          user: {
            include: {
              devicePushTokens: true,
            }
          },
          notificationSettings: true,
          employments: {
            include: {
              job: true
            }
          },
          employmentRequests: {
            include: {
              job: true
            }
          }
        }
      });

      // This is just so we can test
      const transport = isTestPhoneNumber
        ? "email"
        : input.transport;

      const senderStore = await ctx.prisma.store.findFirst({
        where: {
          businessId,
          id: {in: storeIds}
        }
      })
      const senderPerson = ctx.auth.user.person;
      const defaultMessage = input.initDestination === "dest_storeOnboarding"
        // Don't give too bossy of a message to the Exec Director who will be onboarding the store
        ? getStoreOnboarderInvitationMessage({
          person: person,
          senderPerson: senderPerson,
          senderStore: senderStore
        })
        : getDefaultInvitationMessage({
          person: person,
          senderPerson: senderPerson,
          senderStore: senderStore
        })

      await sendBusinessInvitation({
        ctx,
        input: {
          businessId,
          person: person,
          subject: input.subject,
          message: input.message ?? defaultMessage,
          requiresApproval: input.requiresApproval,
          roleId: null,
          initDestination: input.initDestination
        }
      });

      if (input.initDestination === "dest_storeOnboarding" && person.email) {
        await sendSchedulerInformationEmail({
          recipient: person.email,
          sendEmail: ctx.sendEmail,
        })
      }
    }),

  sendWebAppLinkEmail: employedStatsProcedure({feature: "common"})
    .output(z.void())
    .mutation(async ({ctx}) => {
      throwIfNotEmployedByBusiness(ctx);

      const email = ctx.auth.user.person.email;
      if (email) {
        await sendEmailWithNationAppWebLink({
          recipient: email,
          sendEmail: ctx.sendEmail,
        })
      }
    }),

  getRoles: employedStatsProcedure({feature: "common"})
    .output(z.array(roleDto))
    .query(async ({ctx}) => {
      throwIfNotEmployedByBusiness(ctx);
      const roles = await ctx.prisma.role.findMany({
        where: {
          id: {
            not: "GIOA_ADMIN"
          }
        }
      });

      return map(roles, (r): RoleDto => {
        return {
          id: r.id,
          title: r.title,
          description: r.description ?? "",
        }
      });
    }),

  getPermissionPackages: employedStatsProcedure({feature: "common"})
    .output(z.array(permissionPackageDto))
    .query(async ({ctx}) => {
      throwIfNotEmployedByBusiness(ctx);
      return reduce(permissionPackages, (list, pkg, id) => {
        if (pkg.isUserFacing) {
          return [...list, toPermissionPackageDto(id as PackageId, pkg)]
        } else {
          return list;
        }
      }, [] as PermissionPackageDto[]);
    }),

  getStoreAddress: employedStatsProcedure({feature: "common"})
    .input(z.object({
      storeId: z.string()
    }))
    .output(addressDto)
    .query(async ({ctx, input}) => {
      const {businessId} = throwIfNotEmployedByBusiness(ctx);
      const storeId = toSecureStoreIdOrThrow(ctx, input.storeId);

      const store = await ctx.prisma.store.findUniqueOrThrow({
        where: {
          id: storeId,
          businessId,
        },
        select: {
          address: true
        }
      });

      return toAddressDto(store.address);
    }),

  getStoreAdmin: employedStatsProcedure({feature: "common"})
    .input(z.object({
      storeId: z.string()
    }))
    .output(storeDto.extend({
      isSchedulingOnboarded: z.boolean().optional()
    }))
    .query(async ({ctx, input}) => {
      const {businessId, employment} = ctx;
      const storeId = toSecureStoreIdOrThrow(ctx, input.storeId);

      const store = await ctx.prisma.store.findUniqueOrThrow({
        where: {
          id: storeId,
          businessId,
        },
        include: {
          address: true,
          areas: {
            where: {isActive: true},
            orderBy: {order: 'asc'},
            include: {
              storePositions: {
                where: {isActive: true},
                orderBy: {order: 'asc'}
              }
            }
          },
          image: true
        }
      });

      const storeDto = toStoreDto({ctx, businessId, store});

      const checkAllowed = isAllowedFactory({
        businessId,
        principalEmployment: employment,
        userId: ctx.auth.user.id,
      });
      const canRestrictTimeOff = checkAllowed({
        action: "store/restrictTimeOff",
        resource: `business/${businessId}/store/${storeId}`,
        resourceEntity: {
          storeId: storeId,
        }
      }).isAllowed;
      const canViewPayRates = perm.canViewPayRates(checkAllowed, {
        businessId: businessId,
        storeId: storeId
      });
      const canUpdatePayRates = perm.canUpdatePayRates(checkAllowed, {
        businessId: businessId,
        storeId: storeId
      });
      const canManageSubscription = perm.canManageStoreSubscription(checkAllowed, {
        businessId: businessId,
        storeId: storeId
      });

      const isForecastingEnabled = await ctx.db.feature.isFeatureEnabledForBusiness(businessId, "forecasting");
      const isStripeEnabled = await ctx.db.feature.isFeatureEnabledForBusiness(businessId, "stripe");
      const subscription = await ctx.db.store.getActiveSubscription(storeId);
      const commitmentType = subscription
        ? subscription.isOverride
          ? "monthly"
          : getSubscriptionCommitmentType(subscription)
        : "unknown";

      return {
        ...storeDto,
        isSchedulingOnboarded: store.isSchedulingOnboarded,
        permissions: {
          canRestrictTimeOff: canRestrictTimeOff,
          canViewPayRates: canViewPayRates,
          canUpdatePayRates: canUpdatePayRates,
          canManageSubscription: canManageSubscription,
          isForecastingEnabled: isForecastingEnabled,
          isStripeEnabled: isStripeEnabled
        },
        subscriptionStatus: subscription?.status ?? "none",
        hasOverridePlan: Boolean(store.overridePlanId),
        hasPaymentMethod: store.hasPaymentMethod,
        isDeniedAccess: store.denyAccess,
        trialEndsAt: subscription?.trialEnd ?? store.trialEnd ?? undefined,
        goLiveDate: store.goLiveDate ?? DateTime.fromJSDate(store.createdAt, {zone: store.timezone ?? "America/New_York"}).plus({days: 30}).toJSDate(),
        gracePeriodEndsAt: subscription?.gracePeriodEnd ?? undefined,
        subscriptionCommitmentType: commitmentType
      };
    }),

  getBusiness: employedStatsProcedure({feature: "common"})
    .output(businessDto)
    .query(async ({ctx}) => {
      const {businessId, employment} = throwIfNotEmployedByBusiness(ctx);

      const checkAllowed = isAllowedFactory({
        businessId,
        principalEmployment: employment,
        userId: ctx.auth.user.id,
      });

      const business = await ctx.prisma.business.findUniqueOrThrow({
        where: {
          id: businessId
        },
        include: {
          stores: {
            orderBy: {
              // we need a stable order for onboarding workflows
              title: 'asc'
            },
            include: {
              address: true,
              areas: {
                where: {isActive: true},
                orderBy: {order: 'asc'},
                include: {
                  storePositions: {
                    // where: {isActive: true},
                    orderBy: {order: 'asc'}
                  }
                }
              },
              image: true
            }
          },
          jobs: {
            where: {isActive: true},
            include: {defaultRole: true},
            orderBy: {
              order: 'asc'
            }
          },
          coreValues: {
            where: {isActive: true},
            orderBy: {
              createdAt: 'asc'
            },
            include: {
              // TODO remove metric templates
              metricTemplates: {
                take: 1
              }
            }
          },
          metricTemplates: {
            where: {isActive: true},
            orderBy: {
              createdAt: 'asc'
            }
          }
        }
      });

      const canListJobPermissions = checkAllowed({
        action: "business/listJobPermissions",
        resource: `business/${businessId}`,
      }).isAllowed;

      const jobs = map(business.jobs, (j): JobDto => {
        const policy = j.permissionPolicy as unknown as DatabasePermissionPolicy;
        return {
          id: j.id,
          title: j.title,
          description: j.description ?? "",
          isActive: j.isActive,
          level: j.level,
          defaultRoleId: j.defaultRoleId,
          isBusinessJob: j.isBusinessJob,
          isStoreJob: j.isStoreJob,
          createdFromTemplateId: j.createdFromTemplateId ?? undefined,
          permissionPolicy: canListJobPermissions ? permissionPolicyToPackages(policy, permissionPackages) : []
        }
      })

      const dto: BusinessDto = {
        id: business.id,
        metricTemplates: map(business.metricTemplates, (mt): MetricTemplateDto => {
          return {
            id: mt.id,
            description: mt.description ?? "",
            name: mt.name,
            isActive: mt.isActive,
            namespace: mt.namespace
          }
        }),
        coreValues: map(business.coreValues, (cv): CoreValueDto => {
          return {
            id: cv.id,
            title: cv.title,
            description: cv.description ?? "",
            isIncludedInMetrics: !isEmpty(cv.metricTemplates)
          }
        }),
        jobs: jobs,
        chickfilaOperatorId: business.chickfilaOperatorId,
        stores: map(business.stores, s => toStoreDto({ctx, businessId: businessId, store: s})),
        legalName: business.legalBusinessName ?? ""
      }

      return dto;
    }),

  setIsJobActive: employedStatsProcedure({feature: "hr"})
    .input(z.object({
      jobId: z.string(),
      isActive: z.boolean()
    }))
    .output(z.void())
    .mutation(async ({ctx, input}) => {
      const {businessId} = throwIfNotEmployedByBusiness(ctx);

      await ctx.prisma.job.update({
        where: {
          id: input.jobId,
          businessId: businessId,
          createdFromTemplateId: {
            not: "operator"
          }
        },
        data: {
          isActive: input.isActive
        }
      });
    }),

  setJobsOrder: employedStatsProcedure({feature: "hr"})
    .input(z.object({
      jobIdsInOrder: z.array(z.string())
    }))
    .output(z.void())
    .mutation(async ({ctx, input}) => {
      const {businessId} = throwIfNotEmployedByBusiness(ctx);
      await ctx.prisma.$transaction(async (prisma) => {
        for (let i = 0; i < input.jobIdsInOrder.length; i++) {
          const jobId = input.jobIdsInOrder[i];
          await prisma.job.update({
            where: {
              id: jobId,
              businessId: businessId
            },
            data: {
              order: i
            }
          });
        }
      });
    }),

  upsertCoreValue: employedStatsProcedure({feature: "hr"})
    .input(z.object({
      id: z.string(),
      title: z.string(),
    }))
    .output(z.void())
    .mutation(async ({ctx, input}) => {
      const {businessId, employment} = throwIfNotEmployedByBusiness(ctx);

      throwIfDenied({
        ctx, principalEmployment: employment,
        action: "business/createCoreValue",
        resource: `business/${businessId}`,
        resourceEntity: {
          businessId
        }
      })
      throwIfDenied({
        ctx, principalEmployment: employment,
        action: "coreValue/update",
        resource: `business/${businessId}/coreValue/${input.id}`,
        resourceEntity: {
          id: input.id,
          businessId
        }
      })

      await ctx.prisma.coreValue.upsert({
        where: {
          id: input.id,
          businessId: businessId
        },
        create: {
          id: input.id,
          businessId: businessId,
          title: input.title,
        },
        update: {
          title: input.title,
        }
      });
    }),

  deleteCoreValue: employedStatsProcedure({feature: "hr"})
    .input(z.object({
      id: z.string()
    }))
    .output(z.void())
    .mutation(async ({ctx, input}) => {
      // TODO enforce BUSINESS role
      const {businessId, employment} = throwIfNotEmployedByBusiness(ctx);

      throwIfDenied({
        ctx, principalEmployment: employment,
        action: "coreValue/delete",
        resource: `business/${businessId}/coreValue/${input.id}`,
        resourceEntity: {
          id: input.id,
          businessId
        }
      })

      await ctx.prisma.coreValue.updateMany({
        where: {
          businessId: businessId,
          id: input.id
        },
        data: {
          isActive: false
        }
      });

    }),

  upsertJob: employedStatsProcedure({feature: "hr"})
    .input(z.object({
      id: z.string(),
      title: z.string(),
      description: z.string(),
      permissionPackages: z.array(permissionPackageId)
    }))
    .output(z.void())
    .mutation(async ({ctx, input}) => {
      const {businessId, employment: userEmployment} = ctx;

      throwIfDenied({
        ctx, principalEmployment: userEmployment,
        action: "business/createJob",
        resource: `business/${businessId}`,
        resourceEntity: {
          businessId
        }
      })
      throwIfDenied({
        ctx, principalEmployment: userEmployment,
        action: "job/update",
        resource: `business/${businessId}/job/${input.id}`,
        resourceEntity: {
          id: input.id,
          businessId
        }
      })

      const maxOrder: number = await ctx.prisma.job.aggregate({
        where: {
          businessId: businessId,
        },
        _max: {
          order: true
        }
      }).then(agg => agg._max?.order ?? -1);


      // everyone should get the base permissions
      const packageIds: PackageId[] = ["base", ...input.permissionPackages];
      const packageStatements = map(packageIds, (packageId): PermissionPackageStatement => {
        const permissionPackage = permissionPackages[packageId]
        return {
          packageId,
          type: "package",
          effect: "Allow",
          isUserFacing: permissionPackage.isUserFacing
        }
      });

      const permissionPolicy: DatabasePermissionPolicy = {
        statements: packageStatements
      }

      // Cannot assign a policy that has more permissions that the user has (otherwise, a user with approvePeople permissions could invite and approve themselves as an employee that has all permissions)

      // Get the user person's effective permissions
      const requesterPackageIds = getAllowedPermissionPackageIdsForEmployment(userEmployment);
      const jobPackages = chain(permissionPolicy.statements)
        .filter(isPermissionPackageStatement)
        .map(p => p.packageId)
        .value();

      const packagesGranted = getPackagesGranted(jobPackages, requesterPackageIds);

      // we only care about highly sensitive packages
      const sensitivePackagesGranted = filter(packagesGranted, pkgId => {
        const pkg = permissionPackages[pkgId];
        return pkg.isHighlySensitive;
      });

      if (sensitivePackagesGranted.length > 0) {
        // throw new TRPCError({
        //   code: "FORBIDDEN",
        //   message: `You cannot assign sensitive permissions that you do not have: ` + map(sensitivePackagesGranted, packageId => {
        //     const pgk = permissionPackages[packageId];
        //     return pgk.title;
        //   }).join(", ") + ".\n\n You may need to contact your manager to get access to these permissions."
        // });
      }

      await ctx.prisma.job.upsert({
        where: {
          id: input.id,
          businessId: businessId
        },
        create: {
          id: input.id,
          businessId: businessId,
          title: input.title,
          description: input.description,
          // append jobs to the end of the list
          order: maxOrder + 1,
          // don't know what level to give here, as level is not yet used on the UI (and may never be).
          level: 999,
          defaultRoleId: "LEADER", // TODO remove
          isBusinessJob: true,
          isStoreJob: false,
          permissionPolicy: permissionPolicy as any
        },
        update: {
          title: input.title,
          description: input.description,
          defaultRoleId: "LEADER", // TODO remove
          isBusinessJob: true,
          isStoreJob: false,
          permissionPolicy: permissionPolicy as any
        }
      });

      // sync the StreamChat permissions of everyone who has this job title
      const peopleWithJob = await ctx.db.person.findPeopleWithJob(businessId, input.id);
      const chatUsers = map(peopleWithJob, person => personToChatUser(ctx, person));
      upsertAndSyncChatUsers(ctx, chatUsers); // purposefully not awaiting
    }),

  deleteJob: employedStatsProcedure({feature: "hr"})
    .input(z.object({
      id: z.string()
    }))
    .output(z.void())
    .mutation(async ({ctx, input}) => {
      const {businessId, employment} = throwIfNotEmployedByBusiness(ctx);

      throwIfDenied({
        ctx, principalEmployment: employment,
        action: "job/deactivate",
        resource: `business/${businessId}/job/${input.id}`,
        resourceEntity: {
          id: input.id,
          businessId
        }
      })

      const existingJobAssignments = await ctx.prisma.job.findFirst({
        where: {
          businessId: businessId,
          id: input.id
        },
        include: {
          employments: {
            include: {
              person: true
            }
          },
          employmentRequests: {
            include: {
              person: true
            }
          }
        }
      })

      // Check to see if there are any existing employments with this job title
      const existingEmployments = existingJobAssignments?.employments?.length ?? 0;
      const existingEmploymentRequests = existingJobAssignments?.employmentRequests?.length ?? 0;
      if (existingEmployments + existingEmploymentRequests > 0) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Unable to delete, job title is currently in use"
        });
      }

      await ctx.prisma.job.updateMany({
        where: {
          businessId: businessId,
          id: input.id
        },
        data: {
          isActive: false
        }
      });
    }),

  getStoreImagePresignedPost: employedStatsProcedure({feature: "common"})
    .input(z.object({
      contentType: z.string(),
      folder: z.string().optional(),
    }))
    .output(presignedPostDto)
    .mutation(async ({ctx, input}) => {
      const userId = ctx.auth.user.id;
      const {s3MediaBucket, s3Client} = ctx;
      const {businessId} = throwIfNotEmployedByBusiness(ctx);

      await allowRateOrThrow({
        key: userId,
        group: "getPresignedPost",
        maxRequestsPerDuration: 100,
        prisma: ctx.prisma
      });

      // user can upload max 100 photos
      const imageCount = await ctx.prisma.image.count({
        where: {
          uploadedByUserId: userId
        }
      });
      if (imageCount >= 100) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "You have reached the maximum number of images you can upload"
        });
      }

      const twentyFiveMegs = 25 * 1024 * 1024;
      const securityPrefix = businessId + (input.folder ? "/" + input.folder : "")
      const newImageId = genImageId();
      const key = getS3Key({prefix: securityPrefix, objectId: newImageId});
      const presignedPost = await getPresignedPost({
        s3Client: s3Client,
        s3Bucket: s3MediaBucket,
        maxSizeBytes: twentyFiveMegs,
        securityPrefix: securityPrefix,
        contentType: input.contentType,
        key: key
      });

      return {
        newId: newImageId,
        newImageId,
        key,
        presignedPost,
      };
    }),

  getPersonNotesPresignedPost: employedStatsProcedure({feature: "hr"})
    .input(z.object({
      contentType: z.string(),
      folder: z.string().optional(),
    }))
    .output(presignedPostDto)
    .mutation(async ({ctx, input}) => {
      const userId = ctx.auth.user.id;
      const {s3MediaBucket, s3Client} = ctx;
      const {businessId} = throwIfNotEmployedByBusiness(ctx);

      await allowRateOrThrow({
        key: userId,
        group: "getPresignedPost",
        maxRequestsPerDuration: 100,
        prisma: ctx.prisma
      });

      const twentyFiveMegs = 25 * 1024 * 1024;
      const securityPrefix = businessId + (input.folder ? "/" + input.folder : "")
      const newImageId = genImageId();
      const key = getS3Key({prefix: securityPrefix, objectId: newImageId});
      const presignedPost = await getPresignedPost({
        s3Client: s3Client,
        s3Bucket: s3MediaBucket,
        maxSizeBytes: twentyFiveMegs,
        securityPrefix: securityPrefix,
        contentType: input.contentType,
        key: key
      });

      return {
        newId: newImageId,
        newImageId,
        key,
        presignedPost,
      };
    }),

  updateStoreImage: employedStatsProcedure({feature: "common"})
    .input(z.object({
      id: z.string(),
      image: saveImageDto.optional(),
    }))
    .output(z.void())
    .mutation(async ({ctx, input}) => {
      const {businessId, employment} = throwIfNotEmployedByBusiness(ctx);
      const store = await ctx.prisma.store.findUniqueOrThrow({
        where: {
          id: input.id,
          businessId: businessId
        },
        include: {
          image: true
        }
      });

      throwIfDenied({
        ctx, principalEmployment: employment,
        action: "store/uploadPhoto",
        resource: `business/${businessId}/store/${store.id}`,
        resourceEntity: {
          storeId: store.id
        }
      })

      // if a new image has been supplied
      if (input.image && input.image.imageId !== store.image?.id) {
        const imageS3Key = getS3Key({prefix: businessId, objectId: input.image.imageId});
        await throwIfImageNotExistsInS3({
          imageS3Key: imageS3Key,
          ctx: ctx
        });

        // delete the old image
        if (store.image) {
          await ctx.s3Client.send(new DeleteObjectCommand({
            Bucket: ctx.s3MediaBucket,
            Key: store.image.s3ObjectKey
          }));
          await ctx.prisma.image.deleteMany({
            where: {
              stores: {
                some: {
                  image: {
                    id: store.image.id
                  }
                }
              }
            }
          })
        }

        // update the store to use the new image
        await ctx.prisma.store.update({
          where: {
            id: input.id,
            businessId: businessId
          },
          data: {
            image: {
              create: {
                id: input.image.imageId,
                s3ObjectKey: imageS3Key,
                width: input.image.width,
                height: input.image.height,
                mimeType: input.image.mimeType,
                uploadedBy: {
                  connect: {
                    id: ctx.auth.user.id
                  }
                }
              }
            }
          }
        });
      }

      // if the image has been removed
      if (!input.image && store.image) {
        await ctx.s3Client.send(new DeleteObjectCommand({
          Bucket: ctx.s3MediaBucket,
          Key: store.image.s3ObjectKey
        }));
        await ctx.prisma.store.update({
          where: {
            id: input.id,
            businessId: businessId
          },
          data: {
            image: {
              delete: true
            }
          }
        });
      }
    }),

  setStoreOnboardingStarted: employedStatsProcedure({feature: "onboarding"})
    .input(z.object({
      storeId: z.string()
    }))
    .output(z.void())
    .mutation(async ({ctx, input}) => {
      const {businessId, employment} = throwIfNotEmployedByBusiness(ctx);
      const checkAllowed = isAllowedFactory({businessId, principalEmployment: employment, userId: ctx.auth.user.id})
      allAllowedOrThrow({
        checkAllowed,
        actions: ["store/update"],
        resource: `business/${businessId}/store/${input.storeId}`,
        resourceEntity: {
          businessId: businessId,
          storeId: input.storeId
        }
      });

      await ctx.prisma.store.update({
        where: {
          id: input.storeId,
          businessId: businessId
        },
        data: {
          onboardingStartedAt: utcNow()
        }
      });
    }),

  setStoreOnboardingCompleted: employedStatsProcedure({feature: "onboarding"})
    .input(z.object({
      storeId: z.string()
    }))
    .output(z.void())
    .mutation(async ({ctx, input}) => {
      const {businessId, employment} = throwIfNotEmployedByBusiness(ctx);
      const checkAllowed = isAllowedFactory({businessId, principalEmployment: employment, userId: ctx.auth.user.id})
      allAllowedOrThrow({
        checkAllowed,
        actions: ["store/update"],
        resource: `business/${businessId}/store/${input.storeId}`,
        resourceEntity: {
          businessId: businessId,
          storeId: input.storeId
        }
      });

      await ctx.prisma.store.update({
        where: {
          id: input.storeId,
          businessId: businessId
        },
        data: {
          onboardingCompletedAt: utcNow()
        }
      });
    }),

  updateStore: employedStatsProcedure({feature: "common"})
    .input(z.object({
      id: z.string(),
      title: z.string(),
      chickfilaStoreId: z.string(),
      address: addressCreateDto,
      timezone: z.string().optional()
    }))
    .output(z.void())
    .mutation(async ({ctx, input}) => {
      const {businessId, employment} = throwIfNotEmployedByBusiness(ctx);
      const store = await ctx.prisma.store.findUniqueOrThrow({
        where: {
          id: input.id,
          businessId: businessId
        },
        include: {
          image: true
        }
      });

      throwIfDenied({
        ctx, principalEmployment: employment,
        action: "store/update",
        resource: `business/${businessId}/store/${store.id}`,
        resourceEntity: {
          storeId: store.id
        }
      })

      await ctx.prisma.store.update({
        where: {
          id: input.id,
          businessId: businessId
        },
        data: {
          title: input.title,
          chickfilaStoreId: input.chickfilaStoreId,
          address: {
            update: {
              line1: input.address.line1,
              line2: input.address.line2 ?? undefined,
              city: input.address.city,
              state: input.address.state,
              zipCode: input.address.zipCode,
              country: input.address.country
            }
          },
          timezone: input.timezone
        }
      });
    }),

  setStoreAreasOrder: employedStatsProcedure({feature: "common"})
    .input(z.object({
      storeId: z.string(),
      areaIdsInOrder: z.array(z.string())
    }))
    .output(z.void())
    .mutation(async ({ctx, input}) => {
      const {businessId, employment} = throwIfNotEmployedByBusiness(ctx);

      throwIfDenied({
        ctx, principalEmployment: employment,
        action: "store/area/reorderPositions",
        resource: `business/${businessId}/store/${input.storeId}`,
        resourceEntity: {
          storeId: input.storeId
        }
      })

      await ctx.prisma.$transaction(async (prisma) => {
        for (let i = 0; i < input.areaIdsInOrder.length; i++) {
          const areaId = input.areaIdsInOrder[i];
          await prisma.storeArea.update({
            where: {
              id: areaId,
              storeId: input.storeId
            },
            data: {
              order: i
            }
          });
        }
      });
    }),

  setStoreAreaPositionsOrder: employedStatsProcedure({feature: "common"})
    .input(z.object({
      areaId: z.string(),
      positionIdsInOrder: z.array(z.string())
    }))
    .output(z.void())
    .mutation(async ({ctx, input}) => {
      const {businessId, employment} = throwIfNotEmployedByBusiness(ctx);
      // TODO new permissions

      const area = await ctx.prisma.storeArea.findUniqueOrThrow({
        where: {
          id: input.areaId
        }
      })

      throwIfDenied({
        ctx, principalEmployment: employment,
        action: "store/area/reorderPositions",
        resource: `business/${businessId}/store/${area.storeId}`,
        resourceEntity: {
          storeId: area.storeId
        }
      })

      await ctx.prisma.$transaction(async (prisma) => {
        for (let i = 0; i < input.positionIdsInOrder.length; i++) {
          const positionId = input.positionIdsInOrder[i];
          await prisma.storePosition.update({
            where: {
              id: positionId,
              store: {
                businessId: businessId
              }
            },
            data: {
              order: i
            }
          });
        }
      });
    }),

  upsertPosition: employedStatsProcedure({feature: "common"})
    .input(z.object({
      id: z.string(),
      storeId: z.string(),
      title: z.string(),
      areaId: z.string(),
    }))
    .output(z.void())
    .mutation(async ({ctx, input}) => {
      const {businessId, employment} = throwIfNotEmployedByBusiness(ctx);

      throwIfDenied({
        ctx, principalEmployment: employment,
        action: "store/area/position/update",
        resource: `business/${businessId}/store/${input.storeId}`,
        resourceEntity: {
          storeId: input.storeId
        }
      })
      throwIfDenied({
        ctx, principalEmployment: employment,
        action: "store/area/createPosition",
        resource: `business/${businessId}/store/${input.storeId}`,
        resourceEntity: {
          storeId: input.storeId
        }
      })

      const maxOrder: number = await ctx.prisma.storePosition.aggregate({
        where: {
          areaId: input.areaId
        },
        _max: {
          order: true
        }
      }).then(agg => agg._max?.order ?? -1);

      const position = await ctx.prisma.storePosition.findUnique({
        where: {
          id: input.id,
          store: {
            id: input.storeId,
            businessId: businessId
          }
        }
      });

      await ctx.prisma.storePosition.upsert({
        where: {
          id: input.id,
          store: {
            id: input.storeId,
            businessId: businessId
          }
        },
        create: {
          id: input.id,
          title: input.title,
          order: maxOrder + 1,
          isActive: true,
          store: {
            connect: {
              id: input.storeId
            }
          },
          area: {
            connect: {
              id: input.areaId
            }
          },
          trainings: {
            create: [
              {
                id: genPositionTrainingId(),
                title: "Default Training",
                isActive: true,
              }
            ]
          }
        },
        update: {
          // don't allow editing the shift leader position
          title: position && isStorePositionReadonly(position) ? undefined : input.title,
          area: {
            connect: {
              id: input.areaId
            }
          }
        }
      });
    }),

  deleteArea: employedStatsProcedure({feature: "common"})
    .input(z.object({
      id: z.string()
    }))
    .output(z.void())
    .mutation(async ({ctx, input}) => {
      const {businessId, employment} = throwIfNotEmployedByBusiness(ctx);

      const area = await ctx.prisma.storeArea.findUniqueOrThrow({
        where: {
          id: input.id
        }
      })

      // cannot delete foh or boh areas
      if (isStoreAreaReadonly(area)) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: `You cannot delete the ${area.title} area. This area is necessary for the proper functioning of scheduling for your store.`
        })
      }

      throwIfDenied({
        ctx, principalEmployment: employment,
        action: "store/area/delete",
        resource: `business/${businessId}/store/${area.storeId}`,
        resourceEntity: {
          storeId: area.storeId
        }
      })

      await ctx.prisma.storeArea.updateMany({
        where: {
          id: input.id,
          store: {
            businessId: businessId
          }
        },
        data: {
          isActive: false
        }
      });
    }),

  upsertArea: employedStatsProcedure({feature: "common"})
    .input(z.object({
      id: z.string(),
      storeId: z.string(),
      title: z.string(),
    }))
    .output(z.void())
    .mutation(async ({ctx, input}) => {
      const {businessId, employment} = throwIfNotEmployedByBusiness(ctx);

      throwIfDenied({
        ctx, principalEmployment: employment,
        action: "store/area/update",
        resource: `business/${businessId}/store/${input.storeId}`,
        resourceEntity: {
          storeId: input.storeId
        }
      })
      throwIfDenied({
        ctx, principalEmployment: employment,
        action: "store/createArea",
        resource: `business/${businessId}/store/${input.storeId}`,
        resourceEntity: {
          storeId: input.storeId
        }
      })

      const maxOrder: number = await ctx.prisma.storeArea.aggregate({
        where: {
          storeId: input.storeId
        },
        _max: {
          order: true
        }
      }).then(agg => agg._max?.order ?? -1);

      const area = await ctx.prisma.storeArea.findUnique({
        where: {
          id: input.id,
          store: {
            id: input.storeId,
            businessId: businessId
          }
        }
      });

      if (area && isStoreAreaReadonly(area)) {
        return;
      }

      const data = {
        title: input.title,
      }
      await ctx.prisma.storeArea.upsert({
        where: {
          id: input.id,
          store: {
            id: input.storeId,
            businessId: businessId
          }
        },
        create: {
          id: input.id,
          ...data,
          order: maxOrder + 1,
          store: {
            connect: {
              id: input.storeId
            }
          },
        },
        update: {
          ...data,
        }
      });
    }),

  updatePositionsActiveState: employedStatsProcedure({feature: "common"})
    .input(z.object({
      areaId: z.string(),
      positions: z.record(z.string(), z.boolean())
    }))
    .output(z.void())
    .mutation(async ({ctx, input}) => {
      const {businessId, employment} = ctx;
      // TODO new permissions

      const area = await ctx.prisma.storeArea.findUniqueOrThrow({
        where: {
          id: input.areaId
        }
      });

      const storeId = toSecureStoreIdOrThrow(ctx, area.storeId);

      throwIfDenied({
        ctx, principalEmployment: employment,
        action: "store/area/position/update",
        resource: `business/${businessId}/store/${storeId}`,
        resourceEntity: {
          storeId: storeId
        }
      })

      const positions = await ctx.prisma.storePosition.findMany({
        where: {
          areaId: input.areaId,
          storeId: storeId
        }
      })

      for (const [storePositionId, isActive] of Object.entries(input.positions)) {
        const pos = find(positions, p => p.id === storePositionId);
        if (pos && !isStorePositionReadonly(pos)) {
          await ctx.prisma.storePosition.update({
            where: {
              id: storePositionId,
              areaId: input.areaId,
              storeId
            },
            data: {
              isActive
            }
          })
        }
      }
    }),

  deletePosition: employedStatsProcedure({feature: "common"})
    .input(z.object({
      areaId: z.string(),
      id: z.string()
    }))
    .output(z.void())
    .mutation(async ({ctx, input}) => {
      const {businessId, employment} = ctx

      const area = await ctx.prisma.storeArea.findUniqueOrThrow({
        where: {
          id: input.areaId
        }
      });

      const storeId = toSecureStoreIdOrThrow(ctx, area.storeId);

      throwIfDenied({
        ctx, principalEmployment: employment,
        action: "store/area/position/update",
        resource: `business/${businessId}/store/${storeId}`,
        resourceEntity: {
          storeId: storeId
        }
      });

      const position = await ctx.prisma.storePosition.findUniqueOrThrow({
        where: {
          id: input.id,
          storeId: storeId
        }
      });

      if (isStorePositionReadonly(position)) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: `You cannot delete the ${position.title} position. This position is necessary for the proper functioning of scheduling for your store.`
        });
      }

      await ctx.prisma.storePosition.updateMany({
        where: {
          id: input.id,
          storeId,
          area: {
            id: input.areaId
          }
        },
        data: {
          isActive: false
        }
      });
    }),

  getEmployeeOnboardingUrl: employedStatsProcedure({feature: "common"})
    .input(z.object({
      storeId: z.string(),
    }))
    .output(z.object({
      url: z.string()
    }))
    .mutation(async ({ctx, input}) => {
      const {businessId} = throwIfNotEmployedByBusiness(ctx);
      // TODO new permissions

      const {link, user, store} = await getBulkImportLink(ctx, {
        businessId,
        storeId: input.storeId,
        userId: ctx.auth.user.id
      });

      return {
        url: link
      }
    }),

  emailEmployeeOnboardingUrl: employedStatsProcedure({feature: "common"})
    .input(z.object({
      storeId: z.string(),
      emailAddress: emailSchema.optional(),
    }))
    .mutation(async ({ctx, input}) => {
      const {businessId} = throwIfNotEmployedByBusiness(ctx);
      // TODO new permissions

      const {link, user, store, newCode} = await getBulkImportLink(ctx, {
        businessId,
        storeId: input.storeId,
        userId: ctx.auth.user.id
      });
      const title = "Nation Employee Onboarding Link"

      const body = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Team Upload Instructions</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        h2 {
            color: #1a73e8;
            margin-bottom: 5px;
        }
        .location {
            font-size: 18px;
            color: #4a4a4a;
            margin-top: 0;
            margin-bottom: 20px;
        }
        .link {
            color: #1a73e8;
            word-break: break-all;
        }
        .faq {
            margin-top: 30px;
        }
        .faq h3 {
            margin-bottom: 5px;
        }
        .faq p {
            margin-top: 0;
        }
    </style>
</head>
<body>
    <h2>Team Upload Instructions</h2>
    <p class="location"><strong>Store Location: ${store.title}</strong></p>

    <p><strong>The link below will guide you through the steps to upload your team members for the ${store.title} location.</strong></p>

    <p><a href="${link}" class="link">${link}</a></p>

    <p>Enter this code if prompted: <strong>${newCode}</strong></p>

    <h3>What's after that?</h3>
    <p>Your team members will receive an SMS with links to download the app and a verification code. Once they fill out their profiles, you can quickly review their training, permissions, and give them a star rating for scheduling. This can all be done on the Team tab in the app.</p>

    <div class="faq">
        <h3>FAQ:</h3>
        <h4>1. What happens if I upload a team member, but their phone number is incorrect?</h4>
        <p>Simply go to the Team tab in the app, click on Pending, edit their phone number, and resend the invite.</p>

        <h4>2. What if I don't see the team member in my pending invites?</h4>
        <p>On the Team tab, in the top left corner, you will see a + symbol. From there, you can manually add the team member.</p>

        <h4>3. What happens if we have more than 1 store, and a team member is on both HR payrolls?</h4>
        <p>Whoever uploads the Excel file first will have that team member initially assigned to their store. After their profile is created, they can be easily added to both stores or moved to the correct store by editing their profile on the player card.</p>

        <h4>4. How are permissions handled?</h4>
        <p>Every job title has a set of permissions. When you assign a job title to a team member, it will automatically assign the relevant permission sets. After assigning their job title, you can give additional permissions to that team member or remove permissions as needed.</p>

        <h4>5. How do I edit a job title's permissions?</h4>
        <p>On the Dashboard tab, click the three dashes in the top left corner, and select "Manage job titles." From there, you can set core permissions based on the job title. After the title is assigned to a team member, you can modify their permissions individually.</p>
    </div>
</body>
</html>
      `

      const email = input.emailAddress ?? user.person.email;
      if (email) {
        await ctx.sendEmail({
          to: [email],
          subject: title,
          bodyHtml: body,
        });
      } else {
        Sentry.captureMessage('Email address not found for sending employee onboarding email');
      }
    }),

  downloadEmployeeOnboardingExcel: employedStatsProcedure({feature: "common"})
    .input(z.object({
      storeId: z.string(),
    }))
    .output(z.object({
      data: z.string(),
    }))
    .mutation(async ({ctx, input}) => {
      const {businessId} = throwIfNotEmployedByBusiness(ctx);
      // TODO new permissions

      const store = await ctx.prisma.store.findFirstOrThrow({
        where: {
          id: input.storeId,
          businessId
        }
      })

      if (store.id !== input.storeId) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Invalid store id"
        });
      }

      // Create the workbook and worksheet
      const workbook = new Excel.Workbook();
      const worksheet = workbook.addWorksheet('Nation Employee Onboarding');

      worksheet.columns = [
        {header: 'First Name', key: 'firstName', width: 32},
        {header: 'Last Name', key: 'lastName', width: 32},
        {header: 'Phone Number', key: 'phone', width: 32},
        {header: 'Pref. Language (Optional)', key: 'language', width: 32},
      ];

      const languageChoices = ['English', 'Spanish'];

      const storeDetailsSheet = workbook.addWorksheet('metadata');
      const warningRow = storeDetailsSheet.addRow(["Do not touch this sheet."]);
      warningRow.getCell(1).font = {bold: true};

      /**
       * Insert 500 Empty Rows.  We need to do this because we can't apply a Validation rule, like Drop Down, to an
       * entire column, we can only apply them to individual cells.  Before we can apply it to cells, we have to create
       * the rows first.  500 should be more than enough rows for any store.
       */
      for (let i = 0; i < 500; i++) {
        const row = worksheet.addRow([]);
        // Set the format of the phone cell to text so that Excel doesn't assume the column is a number and remove
        // any plus (+) or dashes (-) from the cell.
        const phoneCell = row.getCell('phone');
        phoneCell.numFmt = '@';
        row.getCell('language').dataValidation = {
          type: 'list',
          allowBlank: true,
          formulae: [`"${languageChoices.join(',')}"`],
          showErrorMessage: true,
          errorTitle: 'Invalid Language',
          error: 'Language must be either English or Spanish',
        };
      }

      const excelBuffer = await workbook.xlsx.writeBuffer();
      const buf = Buffer.from(excelBuffer);
      const data = buf.toString('base64');
      return {data};
    }),

  // TODO edit users on web admin

  uploadEmployeeOnboardingExcel: employedStatsProcedure({feature: "onboarding"})
    .input(z.object({
      storeId: z.string(),
      file: z.string()
    }))
    .output(z.object({
      errors: z.array(z.string()),
      addedPersons: z.array(z.object({id: z.string(), firstName: z.string(), lastName: z.string()})),
      existingPersons: z.array(z.object({id: z.string(), firstName: z.string(), lastName: z.string()})),
    }))
    .mutation(async ({ctx, input}) => {
      const {businessId} = throwIfNotEmployedByBusiness(ctx);
      // TODO new permissions

      const person = await ctx.prisma.person.findFirstOrThrow({
        where: {
          id: ctx.auth.user.person.id,
        }
      });

      const store = await ctx.prisma.store.findFirstOrThrow({
        where: {
          id: input.storeId,
          businessId
        }
      })

      if (store.id !== input.storeId) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Invalid store id"
        });
      }

      const parseResults = await parseImportExcelWorksheet(input.file);

      if (!parseResults) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Unable to parse excel file"
        });
      }

      const {errors, persons} = parseResults;

      const jobs = await ctx.prisma.job.findMany({
        where: {
          businessId
        }
      });

      // Default job should be 'Team Member', if it doesn't exist
      // then use the job with the least amount of permissions.
      let defaultJob = find(jobs, job => job.title === "Team Member");
      if (!defaultJob) {
        const maxLevel = maxBy(jobs, job => job.level)!.level;
        defaultJob = last(filter(jobs, job => job.level === maxLevel))
      }

      if (!defaultJob) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "There are no jobs defined for this store"
        });
      }

      const existingPersons: Prisma.Person[] = [];
      const addedPersons: PersonRow[] = [];


      await ctx.prisma.$transaction(async (prisma) => {
        for (const personRow of persons) {
          const existingPerson = await findExistingPersonByContactMethod(prisma, personRow);
          if (existingPerson) {
            existingPersons.push(existingPerson);
            continue;
          }

          await insertPerson({
            prisma,
            storeId: input.storeId,
            businessId,
            personRow,
            jobId: defaultJob!.id
          });

          addedPersons.push(personRow);
        }
      })

      if (person.email) {
        await sendSchedulerInformationEmail({
          sendEmail: ctx.sendEmail,
          recipient: person.email,
        })
      }

      return {
        errors,
        addedPersons: map(addedPersons, p => ({id: p.id, firstName: p.firstName, lastName: p.lastName})),
        existingPersons: map(existingPersons, p => ({
          id: p.id,
          firstName: p.firstName ?? "",
          lastName: p.lastName ?? ""
        }))
      }
    }),

  invitePersons: employedStatsProcedure({feature: "onboarding"})
    .input(z.object({
      storeId: z.string(),
      personIds: z.array(z.string()),
      transport: z.enum(["email", "phone"]).default("phone"),
    }))
    .mutation(async ({ctx, input}) => {
      const {storeId, personIds, transport} = input;
      const {businessId} = throwIfNotEmployedByBusiness(ctx);

      const store = await ctx.prisma.store.findFirstOrThrow({
        where: {
          id: storeId,
          businessId
        }
      });

      const senderStore = await ctx.prisma.store.findFirst({
        where: {
          businessId,
          id: input.storeId
        }
      })
      const senderPerson = ctx.auth.user.person;

      // just a sanity check...
      if (personIds.length > 500) {
        // why would someone have more than 500 people to invite?
        Sentry.captureEvent({
          message: "Someone is trying to invite more than 500 people at once. They need a stern talking to.",
          extra: {
            personId: ctx.auth.user.id,
            storeId: input.storeId,
          }
        });
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Too many people to invite at once. Please limit to 500 or less."
        });

        // TODO more in depth rate limiting
      }

      // we process with concurrency here so that we don't run into the synchronous request 60 second timeout
      // when inviting whole stores. Going one at a time only scales up to about 180 invites.
      await processBatchesWithConcurrency({
        items: personIds,
        concurrency: 3, // Note: Courier started having internal errors at 5 concurrency
        batchProcessor: async (personId) => {
          const person = await ctx.prisma.person.findFirstOrThrow({
            where: {
              id: personId,
            },
            include: {
              user: {
                include: {
                  devicePushTokens: true,
                }
              },
              notificationSettings: true,
              employments: {
                include: {
                  job: true,
                  stores: true
                }
              },
              employmentRequests: {
                include: {
                  job: true,
                  stores: true
                }
              },
              invitations: {
                where: {
                  businessId: businessId,
                  sentAt: {not: null},
                }
              }
            }
          })

          if (!person.phoneNumber && !person.email) {
            throw new TRPCError({
              code: "BAD_REQUEST",
              message: "Person must have an email or phone number"
            });
          }

          // if the person is already employed at this store, don't send an invitation
          if (some(person.employments, e => some(e.stores, s => s.storeId === input.storeId))) {
            throw new TRPCError({
              code: "BAD_REQUEST",
              message: "Person is already employed at this store. Cannot send an invitation."
            });
          }

          // if the person doesn't have an employment request at this store, don't send an invitation
          if (!some(person.employmentRequests, e => some(e.stores, s => s.storeId === input.storeId))) {
            throw new TRPCError({
              code: "BAD_REQUEST",
              message: "Team member does not have an employment request at this store. Cannot send an invitation."
            });
          }

          // if a person is already invited, don't send another invitation. This function is not
          // for resending invitations, only for sending new ones. There could be cases where it gets
          // called multiple times for the same person, e.g. if the user invites people through the app and then goes back to the web bulk import and presses Send Invitations Now. We just want to make this safe and idempotent so that team members don't get multiple invitations, which is confusing for them.
          if (some(person.invitations, i => i.businessId === businessId)) {
            throw new TRPCError({
              code: "BAD_REQUEST",
              message: "Team member already has an invitation."
            });
          }

          const message = getDefaultInvitationMessage({
            person: person,
            senderPerson: senderPerson,
            senderStore: senderStore,
          })

          const roleId = first(person.employmentRequests)?.job.defaultRoleId as RoleType | null
          await sendBusinessInvitation({
            ctx,
            input: {
              businessId: store.businessId,
              person: person,
              subject: undefined,
              message: message,
              requiresApproval: true,
              roleId: roleId,
              initDestination: "dest_onboarding"
            }
          });
        },
        handleError: async (item, error) => {
          Sentry.captureException(error, {
            extra: {
              personId: item,
              storeId: storeId,
            }
          });
          console.error("Error inviting person", JSON.stringify(error))
        }
      })
    }),
  completeOperatorOnboarding: employedStatsProcedure({feature: "onboarding"})
    .output(userDto)
    .mutation(async ({ctx}) => {
      throwIfNotEmployedByBusiness(ctx);
      await ctx.prisma.user.update({
        where: {
          id: ctx.auth.user.id,
        },
        data: {
          initDestination: "dest_dashboard"
        },
      });

      return getCurrentUser(ctx);
    }),

  completeEmployeeOnboarding: verifiedProcedure
    .input(z.object({
      storeIds: z.array(z.string())
    }))
    .output(userDto)
    .mutation(async ({ctx, input}) => {
      // TODO Onboarding Employees will fail this check becacuse they are not fully employed yet
      // throwIfNotEmployedByBusiness(ctx);
      const user = await ctx.prisma.user.update({
        where: {
          id: ctx.auth.user.id,
        },
        data: {
          initDestination: "dest_dashboard"
        },
        include: {
          employmentRoles: true,
          person: {
            include: {
              employments: {
                include: {
                  job: true,
                  stores: true
                }
              },
              employmentRequests: {
                include: {
                  job: true,
                  stores: true
                }
              }
            }
          }
        }
      });

      // if the user has a store employment request, submit it
      const storeRequests = flatMap(user.person.employmentRequests, r => r.stores);
      const storeRequestsToSubmit = filter(storeRequests, r => input.storeIds.includes(r.storeId));
      if (!isEmpty(storeRequestsToSubmit)) {
        await ctx.prisma.storeEmploymentRequest.updateMany({
          where: {
            id: {
              in: map(storeRequestsToSubmit, r => r.id)
            }
          },
          data: {
            isSubmitted: true
          }
        });
      }

      return getCurrentUser(ctx);
    }),

  setStoreSchedulingOnboarded: employedStatsProcedure({feature: "onboarding"})
    .input(z.object({
      storeId: z.string()
    }))
    .output(z.void())
    .mutation(async ({ctx, input}) => {
      const {businessId} = throwIfNotEmployedByBusiness(ctx);
      // TODO permissions

      await ctx.prisma.store.update({
        where: {
          id: input.storeId,
          businessId
        },
        data: {
          isSchedulingOnboarded: true
        }
      })
    }),

  getStore: verifiedProcedure
    .input(z.object({
      storeId: z.string()
    }))
    .output(storeDashboardDto)
    .query(async ({ctx, input}) => {
      const user = await ctx.prisma.user.findFirstOrThrow({
        where: {
          id: ctx.auth.user.id
        },
        include: {
          person: {
            include: {
              employmentRequests: {
                where: {
                  isApproved: false,
                  stores: {
                    some: {
                      storeId: input.storeId
                    }
                  }
                },
                include: {
                  stores: {
                    include: {
                      store: {
                        include: {
                          image: true
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        }
      });

      const noPermissionsResponse = (store: {
        id: string;
        title: string;
        image: {
          id: string;
        } | null;
        timezone: string | null;
      }) => ({
        id: store.id,
        title: store.title,
        imageUrl: store.image && ctx.auth.user.person.businessId ? ctx.getImageUrl(ctx.auth.user.person.businessId, store.image.id) : undefined,
        timezone: store.timezone ?? "America/New_York",
        isPersonUnapproved: true,
        employees: [],
        maxSmsMessageLength: maxSmsMessageLength,
        storeAreas: [],
        schedulingSettings: {
          disabledScheduleValidations: []
        },
        isWorkModeEnabled: false,
      });

      const employmentRequest = first(user.person.employmentRequests);
      if (employmentRequest && !employmentRequest.isApproved) {
        const store = find(employmentRequest.stores, s => s.storeId === input.storeId)?.store;
        if (!store) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "No store found"
          })
        }

        return noPermissionsResponse(store);
      }

      const {businessId, employment, storeIds} = throwIfNotEmployedByBusiness(ctx);
      // use listPeopleFull to determine if the user can see star ratings and statuses
      // in the returned employees
      // Note: every employee can see basic list, so no need for a permission explicitly for that yet
      const checkAllowed = isAllowedFactory({
        businessId,
        principalEmployment: employment,
        userId: ctx.auth.user.id,
      });

      const storeId = toSecureStoreId(storeIds, input.storeId);
      if (!storeId) {
        const store = await ctx.prisma.store.findUniqueOrThrow({
          where: {
            id: input.storeId,
            businessId
          },
          include: {
            image: true
          }
        });
        return noPermissionsResponse(store);
      }
      const store = await ctx.prisma.store.findUniqueOrThrow({
        where: {
          id: storeId,
          businessId
        },
        include: {
          address: true,
          image: true,
          areas: {
            where: {isActive: true},
            orderBy: {order: 'asc'},
            include: {
              storePositions: {
                // where: {isActive: true},
                orderBy: {order: 'asc'}
              }
            }
          },
          employments: {
            include: {
              employment: {
                include: {
                  person: {
                    include: {
                      user: true,
                      profileImage: true
                    }
                  },
                  job: true
                }
              }
            }
          },
          employmentRequests: {
            include: {
              employmentRequest: {
                include: {
                  person: {
                    include: {
                      user: true,
                      profileImage: true,
                      invitations: {
                        where: {
                          businessId: businessId,
                          sentAt: {not: null},
                        }
                      },
                    }
                  },
                  job: true
                }
              }
            }
          },
        }
      });

      const employees = map(store.employments, (storeEmployment): PersonDto => {
        const person = storeEmployment.employment.person;
        const showProficiency = checkAllowed({
          action: "storeEmployment/getProficiency", // list with star ratings
          resource: `business/${businessId}/storeEmployment/${storeEmployment.id}`,
          resourceEntity: {
            id: storeEmployment.id,
            storeId: storeEmployment.storeId,
          }
        }).isAllowed

        return {
          id: person.id ?? undefined,
          email: person.email ?? undefined,
          profileImageUrl: person.profileImage ? ctx.getImageUrl(person.id, person.profileImage.id!) : undefined,
          firstName: person.firstName ?? undefined,
          lastName: person.lastName ?? undefined,
          metadata: (person.metadata ?? {}) as MetadataDto,
          businessId: person.businessId ?? undefined,
          jobTitle: storeEmployment.employment.job.title,
          storeIds: [],
          proficiencyRanking: showProficiency
            ? storeEmployment.proficiencyRanking ?? 1
            : undefined,
          isArchived: person.isArchived,
          isSuspended: person.user?.isSuspended,
          createdAt: person.createdAt
        }
      });

      const storeStreetAddress = toAddressDto(store.address);

      const unapprovedRequests = filter(store.employmentRequests, r => !r.isApproved);
      const viewEmploymentRequests = checkAllowed({
        action: "employmentRequest/get",
        resource: `business/${businessId}/store/${store.id}`,
        resourceEntity: {
          id: store.id,
          storeId: store.id,
        }
      }).isAllowed;
      const visibleUnapprovedRequests = viewEmploymentRequests
        ? unapprovedRequests
        : [];
      const unapprovedEmployees = map(visibleUnapprovedRequests, (storeRequest): PersonDto => {
        const person = storeRequest.employmentRequest.person;
        const latestInvitation = maxBy(person.invitations, i => i.sentAt);

        return {
          id: person.id ?? undefined,
          email: person.email ?? undefined,
          profileImageUrl: person.profileImage ? ctx.getImageUrl(person.id, person.profileImage.id!) : undefined,
          firstName: person.firstName ?? undefined,
          lastName: person.lastName ?? undefined,
          metadata: (person.metadata ?? {}) as MetadataDto,
          businessId: person.businessId ?? undefined,
          jobTitle: storeRequest.employmentRequest.job.title,
          storeIds: [],
          invitation: latestInvitation ? toInvitationDto(latestInvitation) : undefined,
          isEmploymentRequestPending: true,
          isEmploymentRequestSubmitted: isPersonEmploymentRequestSubmitted(storeRequest),
          isArchived: person.isArchived,
          isSuspended: person.user?.isSuspended,
          createdAt: person.createdAt
        }
      });

      const canRestrictTimeOff = checkAllowed({
        action: "store/restrictTimeOff",
        resource: `business/${businessId}/store/${store.id}`,
        resourceEntity: {
          storeId: store.id,
        }
      }).isAllowed;

      const storeDto: StoreDashboardDto = {
        id: store.id,
        title: store.title,
        imageUrl: store.image ? ctx.getImageUrl(businessId, store.image.id) : undefined,
        imageId: store.image?.id,
        employees: [
          ...employees,
          ...unapprovedEmployees,
        ],
        streetAddress: storeStreetAddress,
        scheduleRequestLeadTime: store.scheduleRequestLeadTime,
        timezone: store.timezone ?? "America/New_York",
        isSchedulingOnboarded: store.isSchedulingOnboarded,
        onboardingStartedAt: store.onboardingStartedAt ?? undefined,
        onboardingCompletedAt: store.onboardingCompletedAt ?? undefined,
        storeHours: {
          start: dateTo24HrTime(store.storeHoursStart),
          end: dateTo24HrTime(store.storeHoursEnd),
        },
        storeAreas: map(store.areas, toStoreAreaDto),
        maxSmsMessageLength: maxSmsMessageLength,
        permissions: {
          canRestrictTimeOff: canRestrictTimeOff,
        },
        schedulingSettings: {
          disabledScheduleValidations: store.disabledScheduleValidations ?? []
        },
        // controlling this from the server side so that if we start getting hammered we can adjust it...
        checklistRefetchInterval: 5000,
        isWorkModeEnabled: store.isWorkModeEnabled
      }

      return storeDto;
    }),

  getStoreDashboard: verifiedProcedure
    .input(z.object({
      storeId: z.string(),
      // to keep the API backwards compatible, it may return different info depending on the version of the client
      appVersion: z.string().optional(),
    }))
    .output(playerStoreDashboardDto)
    .query(async ({ctx, input}): Promise<PlayerStoreDashboardDto> => {
      const user = await ctx.prisma.user.findFirstOrThrow({
        where: {
          id: ctx.auth.user.id
        },
        include: {
          person: {
            include: {
              employmentRequests: {
                where: {
                  isApproved: false,
                  stores: {
                    some: {
                      storeId: input.storeId
                    }
                  }
                },
                include: {
                  stores: {
                    include: {
                      store: {
                        include: {
                          image: true
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        }
      });

      const noPermissionsResponse = (store: {
        id: string;
        title: string;
        image: {
          id: string;
        } | null;
        timezone: string | null;
      }) => ({
        correctiveActions: [],
        store: {
          id: store.id,
          title: store.title,
          imageUrl: store.image && ctx.auth.user.person.businessId ? ctx.getImageUrl(ctx.auth.user.person.businessId, store.image.id) : undefined,
          timezone: store.timezone ?? "America/New_York",
          schedulingSettings: {
            disabledScheduleValidations: []
          },
          isWorkModeEnabled: false,
        },
        storeAreas: [],
        setupSheetShifts: [],
        shifts: [],
        shiftLeaders: [],
        isPersonUnapproved: true,
        hasSchedule: false,
        scheduleEvents: [],
        scheduleAnnouncements: [],
        shiftOffers: [],
        shiftSwapOffers: [],
        checklists: {
          numChecklistsCompleted: 0,
          numChecklists: 0,
          percentageComplete: 0,
        },
        isWorkModeEnabled: false,
      });

      const employmentRequest = first(user.person.employmentRequests);
      if (employmentRequest && !employmentRequest.isApproved) {
        const store = find(employmentRequest.stores, s => s.storeId === input.storeId)?.store;
        if (!store) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "No store found"
          })
        }

        return noPermissionsResponse(store)
      }

      const {businessId, storeIds, employment: userEmployment, currentPersonId} = throwIfNotEmployedByBusiness(ctx);
      const secureStoreId = toSecureStoreId(storeIds, input.storeId);
      if (!secureStoreId) {
        const store = await ctx.prisma.store.findUniqueOrThrow({
          where: {
            id: input.storeId,
            businessId
          },
          include: {
            image: true
          }
        });
        // the no permissions response does not have any sensitive info about the store. Just
        // title, image, timezone... And they have to be employed at the business already.
        return noPermissionsResponse(store);
      }

      const checkAllowed = isAllowedFactory({
        businessId,
        principalEmployment: userEmployment,
        userId: ctx.auth.user.id,
      });

      const store = await ctx.prisma.store.findUniqueOrThrow({
        where: {
          id: secureStoreId,
          businessId
        },
        include: {
          address: true,
          image: true,
          areas: {
            where: {isActive: true},
            orderBy: {order: 'asc'},
            include: {
              storePositions: {
                where: {isActive: true},
                orderBy: {order: 'asc'}
              }
            }
          },
        }
      });

      const storeStreetAddress = toAddressDto(store.address);

      const now = DateTime.now();
      const timezone = store.timezone ?? "America/New_York";
      const nowAtStore = now.setZone(timezone);
      const end = now.endOf("week");
      const storeTodayStart = nowAtStore.startOf("day");
      const storeTodayEnd = nowAtStore.endOf("day");

      const shiftRows = await ctx.prisma.shift.findMany({
        where: {
          endAbs: {
            gte: storeTodayStart.toJSDate(),
            lte: end.toJSDate()
          },
          assignedPersonId: currentPersonId,
          // use the storeId on the shift itself for more efficient query.
          storeId: secureStoreId,
        },
        include: {
          shiftArea: {
            include: {
              scheduleDay: true
            }
          },
          shiftActivities: true,
          storePosition: true,
          ShiftOffer: true,
          offeredByShiftSwaps: true,
          offeredToShiftSwaps: true,
        },
        orderBy: {
          startAbs: "asc"
        }
      });

      const shiftLeaderRows = await ctx.prisma.shift.findMany({
        where: {
          startAbs: {
            gte: storeTodayStart.toJSDate(),
            lte: storeTodayEnd.toJSDate()
          },
          isShiftLead: true,
          assignedPersonId: {
            not: null
          },
          storeId: secureStoreId,
        },
        include: {
          assignedPerson: personWithJobAndImageIncludes
        },
        orderBy: {
          startAbs: "asc"
        }
      });

      const shiftLeaders = map(shiftLeaderRows, row => {
        return {
          person: {
            ...toMinimalPersonDto(ctx, row.assignedPerson!),
            phoneNumber: row.assignedPerson?.phoneNumber ?? undefined,
          },
          shift: {
            range: {
              start: dateTo24HrTime(row.start),
              end: dateTo24HrTime(row.end)
            }
          }
        }
      })

      const storeDto: BaseStoreDto = {
        id: secureStoreId,
        title: store.title,
        imageUrl: store.image ? ctx.getImageUrl(businessId, store.image.id) : undefined,
        streetAddress: storeStreetAddress,
        scheduleRequestLeadTime: store.scheduleRequestLeadTime,
        timezone: store.timezone ?? "America/New_York",
        schedulingSettings: {
          disabledScheduleValidations: store.disabledScheduleValidations ?? []
        },
        isWorkModeEnabled: store.isWorkModeEnabled
      }

      // get the schedule for this week
      // get the ISO week in the store's timezone
      const isoWeekAtStore = {
        week: nowAtStore.weekNumber,
        year: nowAtStore.weekYear
      }

      const weekRange = getIsoWeekDateRangeInTimezone({
        week: isoWeekAtStore,
        timezone
      })

      const scheduleEvents = await findScheduleEventsForRange({
        ctx: ctx,
        storeId: secureStoreId,
        businessId,
        range: weekRange,
        prisma: ctx.prisma,
        checkAllowed,
        excludeEventType: "announcement"
      })

      const scheduleAnnouncements = await findScheduleEventsForRange({
        ctx: ctx,
        storeId: secureStoreId,
        businessId,
        range: weekRange,
        prisma: ctx.prisma,
        checkAllowed,
        includeEventType: "announcement"
      })

      const canViewSchedules = checkAllowed({
        action: "store/getPublishedSchedule",
        resource: `business/${businessId}/store/${secureStoreId}`,
        resourceEntity: {
          id: secureStoreId,
          storeId: secureStoreId,
        }
      }).isAllowed;

      const currentUserPersons = await getAllSchedulePeopleAtStore({
        ctx,
        prisma: ctx.prisma,
        storeId: secureStoreId,
        businessId,
        checkAllowed,
        personIds: [currentPersonId]
      });

      const storeEmployment = find(userEmployment.stores, s => s.storeId === input.storeId);
      if (!storeEmployment) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Store employment not found"
        });
      }

      const canCreateCAs = perm.canCreateCorrectiveActions(checkAllowed, {
        businessId,
        storeEmploymentId: storeEmployment.id,
        storeId: secureStoreId,
      })

      const correctiveActions = canCreateCAs
        ? await ctx.db.correctiveAction.getCorrectiveActionsNeedingAttention(businessId, secureStoreId, {
          checkAllowed,
          currentPersonId: currentPersonId
        }) : [];

      const shiftSwapOffers = await ctx.prisma.shiftSwap.findMany({
        where: {
          ...getShiftSwapStatusWhereCondition(["pending", "accepted"], nowAtStore.toJSDate(), {
            offereePersonId: currentPersonId,
          }),
          storeId: input.storeId,
        },
        include: {
          offerorPerson: personWithJobAndImageIncludes,
          offereePerson: personWithJobAndImageIncludes,
          approvedByPerson: personWithJobAndImageIncludes,
          declinedByPerson: personWithJobAndImageIncludes,
          cancelledByPerson: personWithJobAndImageIncludes,
          offerorShift: {
            include: {
              shiftArea: true,
              shiftActivities: true
            }
          },
          offereeShift: {
            include: {
              shiftArea: true,
              shiftActivities: true
            }
          },
        },
        orderBy: {
          submittedAt: "desc"
        }
      });

      const shiftSwapOfferDtos = map(shiftSwapOffers, swap => toShiftSwapDto(ctx, swap));

      // get active shift offers
      const shiftOfferRows = await ctx.prisma.shiftOffer.findMany({
        where: {
          storeId: input.storeId,
          isSubmitted: true,
          isApproved: false,
          isDeclined: false,
          isCancelled: false,

          // Older app versions don't pass in the appVersion. Those older versions cannot handle house shift offers (i.e. those offers that don't have an offerorPersonId). So don't return them if that is the case.
          ...!input.appVersion ? {offerorPersonId: {not: null}} : {},

          // only show shift offers for shifts that have not started yet
          shift: {
            startAbs: {
              gt: new Date()
            },
          },

          // don't show offers they already accepted
          acceptances: {
            none: {
              acceptorPersonId: currentPersonId,
            }
          }
        },
        include: {
          shift: {
            include: {
              assignedPerson: personWithJobAndImageIncludes,
              shiftActivities: true
            }
          },
          schedule: {
            include: {
              ScheduleDay: {
                include: {
                  ShiftArea: {
                    include: {
                      Shift: {
                        include: {
                          shiftActivities: true
                        }
                      }
                    }
                  }
                }
              },
            }
          },
          offerorPerson: personWithJobAndImageIncludes
        }
      });

      const storeAreas = map(store.areas, toStoreAreaDto);

      // see if the current user is eligible for any of the shift offers
      const eligibleShiftOffers: Array<DashboardShiftOfferDto> = chain(shiftOfferRows)
        .filter(offerRow => {
          if (!offerRow.shift || !offerRow.schedule) {
            return false;
          }
          const sched = hydrateSchedule(offerRow.schedule);
          if (!sched.published) {
            return false;
          }

          const shift = hydrateScheduleShift(offerRow.shift);
          const suggestionsForShiftOffer = filterEligibleForShiftOffer({
            people: currentUserPersons,
            offerorPersonId: offerRow.offerorPersonId ?? undefined,
            schedule: sched.published,
            shift: shift,
            shiftOffer: offerRow,
            storeAreas: storeAreas,
            dayOfWeek: offerRow.shift.day!,
            timezone: store.timezone ?? "America/New_York",
            settings: {
              disabledScheduleValidations: store.disabledScheduleValidations ?? []
            }
          });

          // since the current user is the only person passed in, we can assume that the offer is eligible
          // if the resultant suggestions is not empty.
          return !isEmpty(suggestionsForShiftOffer);
        }).map(offerRow => {
          const sched = hydrateSchedule(offerRow.schedule);
          return toDashboardShiftOfferDto({
            ctx,
            schedule: sched.published,
            offerRow,
            storeAreas
          })
        }).value();

      const storeId = toSecureStoreIdOrThrow(storeIds, input.storeId);
      const checklistRows = await ctx.db.checklists.findLiveChecklistEvents(storeId, timezone)
      const shiftsAssignedToCurrentPerson = filterToShiftsTodayAssignedToPerson({
        personId: currentPersonId,
        now: nowAtStore,
        shifts: shiftRows
      });
      const assignedChecklists = getLiveChecklistsAssignedToPerson({
        checklists: checklistRows,
        personId: currentPersonId,
        shifts: shiftsAssignedToCurrentPerson,
      })
      const completedChecklists = filter(assignedChecklists, checklist => {
        return isChecklistProgressComplete(checklist);
      });

      const startOfDay = now.startOf("day");
      const endOfDay = now.endOf("day");

      const checklists2 = await ctx.db.checklist.getChecklistsInRange(storeId, {
        range: {
          start: startOfDay.minus({days: 8}),
          end: endOfDay,
        }
      });
      const assignedChecklists2 = getLiveChecklists2AssignedToPerson({
        checklists: checklists2,
        personId: currentPersonId,
        shifts: shiftsAssignedToCurrentPerson,
        now,
        timezone,
      });
      const completedChecklists2 = filter(assignedChecklists2, checklist => {
        return checklist.isComplete;
      });

      const setupSheetPositionShiftRows = await ctx.prisma.setupSheetPositionShift.findMany({
        where: {
          personId: currentPersonId,
          startAbs: {
            gte: storeTodayStart.toJSDate(),
            lte: storeTodayEnd.toJSDate()
          },
        },
        include: {
          setupSheetPosition: true
        },
        orderBy: {
          start: 'asc'
        }
      });
      const setupSheetShifts = map(setupSheetPositionShiftRows, row => {
        return {
          positionTitle: row.setupSheetPosition.title,
          range: {
            start: dateTo24HrTime(row.start),
            end: dateTo24HrTime(row.end)
          }
        }
      })

      const numChecklists = assignedChecklists.length + assignedChecklists2.length;
      const numChecklistsCompleted = completedChecklists.length + completedChecklists2.length;
      const percentageChecklistsComplete = numChecklists > 0 ? numChecklistsCompleted / numChecklists : 0;

      return {
        correctiveActions: map(correctiveActions, ca => toCorrectiveActionDto(ctx, ca)),
        setupSheetShifts,
        store: storeDto,
        storeAreas: storeAreas,
        shifts: map(shiftRows, row => shiftToDto({
          ctx,
          shift: row,
          dayOfWeek: row.shiftArea.scheduleDay.dayOfWeek,
          area: row.shiftArea
        })),
        checklists: {
          numChecklists: numChecklists,
          numChecklistsCompleted: numChecklistsCompleted,
          percentageComplete: percentageChecklistsComplete,
        },
        shiftLeaders: shiftLeaders,
        isPersonUnapproved: false,
        scheduleEvents: scheduleEvents,
        scheduleAnnouncements: scheduleAnnouncements,
        hasSchedule: canViewSchedules,
        shiftOffers: orderBy(eligibleShiftOffers, [offer => offer.date, offer => offer.range.start], ["asc", "asc"]),
        shiftSwapOffers: orderBy(shiftSwapOfferDtos, [swap => swap.submittedAt], ["asc"]),
        isWorkModeEnabled: store.isWorkModeEnabled
      };
    }),

  getWeatherForecast: employedStatsProcedure({feature: "common"})
    .input(z.object({
      range: z.object({
        start: dateNoTime,
        end: dateNoTime,
      }).optional(),
      zipCode: z.string(),
    }))
    .output(z.object({
      weather: weatherForecastDto.optional()
    }))
    .query(async ({ctx, input}) => {
      throwIfNotEmployedByBusiness(ctx);
      const range = input.range ?? {
        start: DateTime.now().startOf('week').toFormat('yyyy-MM-dd'),
        end: DateTime.now().endOf('week').toFormat('yyyy-MM-dd'),
      };
      const weather = await getWeatherForecast({
        range,
        weather: ctx.weather,
        zipCode: input.zipCode,
        prisma: ctx.prisma,
      });

      return {weather};
    }),

  /**
   * Get the published schedule for the current ISO week in the store's timezone.
   * Or for the given week if the week parameter is provided.
   */
  getPublishedScheduleForThisWeek: employedStatsProcedure({feature: "scheduling"})
    .input(z.object({
      storeId: z.string(),
      week: isoWeekDate.optional()
    }))
    .output(z.object({
      schedule: publishedSchedule.optional(),
      forecast: validScheduleHourlySalesForecast.optional(),
      // the current ISO week day of the current ISO week in the store's timezone
      dayOfWeek: dayOfWeek,
      // the store's timezone
      timezone: z.string(),
    }))
    .query(async ({ctx, input}) => {
      const {businessId, checkAllowed} = ctx;
      const storeId = toSecureStoreIdOrThrow(ctx, input.storeId);

      const store = await ctx.prisma.store.findUniqueOrThrow({
        where: {
          id: input.storeId,
          businessId
        },
        select: {timezone: true}
      });
      const timezone = store.timezone ?? "America/New_York";
      const now = DateTime.now();
      const nowAtStore = now.setZone(timezone);

      // get the schedule for this week
      // get the ISO week in the store's timezone
      const isoWeekAtStore = {
        week: nowAtStore.weekNumber,
        year: nowAtStore.weekYear
      }
      const dayOfWeek = nowAtStore.weekday;
      const {schedule, forecast} = await getPublishedScheduleForWeekIfAllowed({
        db: ctx.db,
        isoWeek: input.week ?? isoWeekAtStore,
        storeId: storeId,
        businessId,
        checkAllowed
      });

      return {
        forecast,
        schedule,
        dayOfWeek,
        timezone,
      }
    }),

  getPersonNote: employedStatsProcedure({feature: "hr"})
    .input(z.object({
      noteId: z.string(),
    }))
    .output(personNoteDetailDto)
    .query(async ({ctx, input}) => {
      const {businessId, employment: userEmployment} = throwIfNotEmployedByBusiness(ctx);
      const checkAllowed = isAllowedFactory({
        businessId,
        principalEmployment: userEmployment,
        userId: ctx.auth.user.id,
      });

      const note = await ctx.prisma.entityNote.findUniqueOrThrow({
        where: {
          id: input.noteId,
          businessId,
        },
        include: {
          createdByPerson: personWithJobAndImageIncludes,
          updatedBy: personWithJobAndImageIncludes,
          person: personWithJobAndImageIncludes,
          images: true,
          fromCorrectiveAction: {
            include: {
              ...correctiveActionIncludes,
            }
          },
          history: {
            include: {
              createdByPerson: personWithJobAndImageIncludes,
              person: personWithJobAndImageIncludes,
              images: true,
              fromCorrectiveAction: {
                include: {
                  ...correctiveActionIncludes,
                }
              },
            }
          },
        }
      });

      const canView = checkAllowed({
        action: "person/getNote",
        resource: `business/${businessId}/person/${note.personId}`,
        resourceEntity: {
          sensitivityLevel: note.sensitivityLevel === noteLowSensitivityLevel
            ? "low"
            : note.sensitivityLevel === noteMediumSensitivityLevel
              ? "medium"
              : note.sensitivityLevel === noteHighSensitivityLevel
                ? "high"
                : "unknown",
        }
      }).isAllowed;

      if (!canView) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "You are not authorized to view this note."
        });
      }

      const storeId = note.storeId ? toSecureStoreIdOrThrow(ctx, note.storeId) : undefined;
      const shouldIncludeFromCorrectiveAction = storeId
        && note.fromCorrectiveAction
        && doesCorrectiveActionPassPermissionChecks({
          checkAllowed,
          correctiveAction: note.fromCorrectiveAction,
          businessId,
          storeIds: [storeId],
          requestingPersonId: ctx.auth.user.person.id,
          allowArchivedRecipient: true,
        });
      const noteWithFromCorrectiveActionCheck = {
        ...note,
        fromCorrectiveAction: shouldIncludeFromCorrectiveAction?.isOk
          ? shouldIncludeFromCorrectiveAction.value
          : null,
      };

      return toPersonNoteDetailDto(ctx, noteWithFromCorrectiveActionCheck);
    }),

  getPersonDetail: employedStatsProcedure({feature: "hr"})
    .input(z.object({
      personId: z.string(),
      storeId: z.string(),
    }))
    .output(personDetailDto)
    .query(async ({ctx, input}) => {
      const {businessId, currentPersonId, checkAllowed} = ctx;
      const storeId = toSecureStoreIdOrThrow(ctx, input.storeId);

      const maxSensitivityLevel = perm.getMaxAllowedPersonNoteSensitivityLevel(checkAllowed, {
        businessId,
        personId: input.personId,
        storeId,
        requestingPersonId: currentPersonId,
      });

      const person = await ctx.db.person.getPersonInBusiness(businessId, input.personId, {
        user: true,
        correctiveActionsReceived: {
          where: {
            businessId,
          },
          include: correctiveActionIncludes
        },
        notes: {
          where: {
            businessId,
            sensitivityLevel: {
              lte: maxSensitivityLevel
            }
          },
          include: {
            createdByPerson: {
              include: {
                profileImage: true,
                employments: {
                  include: {
                    job: true
                  }
                }
              }
            },
            images: true,
          }
        },
        employments: {
          where: {
            businessId,
            stores: {
              some: {}
            }
          },
          include: {
            job: true,
            userRoles: {
              include: {
                role: true
              }
            },
            stores: {
              include: {
                store: {}
              }
            }
          }
        },
        employmentRequests: {
          where: {
            job: {
              businessId
            },
            stores: {
              some: {
                isApproved: false
              }
            }
          },
          include: {
            stores: true,
            job: {
              include: {
                defaultRole: true
              }
            },
            trainingHistory: {
              include: {
                positionTraining: {
                  include: {
                    storePosition: true
                  }
                }
              }
            }
          }
        },
        metrics: {
          where: {
            businessId: businessId,
            personId: input.personId,
            recorderPersonId: {not: null},
          },
          include: {
            dataPoints: {
              orderBy: {
                timestamp: "desc"
              },
              include: {
                recorderPerson: {
                  include: {
                    profileImage: true,
                    employments: {
                      include: {
                        job: true
                      }
                    }
                  }
                }
              }
            }
          }
        },
        profileImage: true,
        invitations: {
          where: {
            businessId,
            sentAt: {not: null},
          }
        },
        trainingHistory: {
          where: {
            positionTraining: {
              storePosition: {
                store: {
                  businessId,
                  id: input.storeId
                }
              }
            }
          },
          include: {
            positionTraining: {
              include: {
                storePosition: true
              }
            }
          }
        },
        address: true,
      });

      if (!perm.canGetPerson(checkAllowed, {
        businessId,
        storeId,
        personId: input.personId
      })) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "You do not have permission to view this person"
        });
      }

      const store = await ctx.prisma.store.findUniqueOrThrow({
        where: {
          id: input.storeId,
          businessId,
        },
        include: {
          areas: {
            where: {isActive: true},
            orderBy: {order: 'asc'},
            include: {
              storePositions: {
                where: {isActive: true},
                orderBy: {order: 'asc'}
              }
            }
          },
          image: true
        }
      });

      const baseDto = getPersonDetail(ctx, {
        person,
        checkAllowed,
        storeId: storeId,
        businessId,
        timezone: store.timezone ?? "America/New_York",
      });
      const employment = getPersonMainEmployment(person?.employments);
      const employmentRequest = find(person.employmentRequests, er => {
        return er.stores.some(store => store.storeId === input.storeId) && !er.endedAt && !er.isApproved;
      });
      // prioritize the employment request because this may be an employee already employed at one store who is getting added to another store in the business
      const employmentOrRequest = employmentRequest || employment;
      const permissions = getRequesterPermissionsOnPerson({
        checkAllowed,
        businessId,
        employment: employmentOrRequest,
        personId: person.id,
        userId: person.user?.id,
        storeId: storeId,
      });

      const detailDto: PersonDetailDto = {
        ...baseDto,
        storeAreas: map(store.areas, toStoreAreaDto),
        permissionPackages: employmentOrRequest ? getAllowedPermissionPackageIdsForEmployment(employmentOrRequest) : undefined,
        requesterPermissionEvaluation: permissions
      }

      return detailDto;
    }),

  getEmployeePositionTrainings: verifiedProcedure
    .input(z.object({
      storeId: z.string().optional()
    }))
    .output(z.object({
      storeTitle: z.string(),
      positionTrainings: z.array(employeePositionTrainingRequestDto)
    }))
    .query(async ({ctx, input}) => {
      const user = await ctx.prisma.user.findFirstOrThrow({
        where: {
          id: ctx.auth.user.id
        },
        include: {
          person: {
            include: {
              employments: {
                where: {
                  stores: {
                    some: {
                      storeId: input.storeId
                    }
                  }
                },
              }
            }
          }
        }
      })

      const employment = getPersonMainEmployment(user.person?.employments)
      if (!employment) {
        return {
          storeTitle: "",
          positionTrainings: []
        }
      }

      const store = await ctx.prisma.store.findUniqueOrThrow({
        where: {
          id: input.storeId,
          businessId: user.person.businessId!
        },
        include: {
          areas: {
            where: {
              isActive: true,
              storePositions: {
                some: {
                  isActive: true,
                  trainings: {
                    some: {
                      isActive: true,
                    }
                  }
                }
              }
            },
            orderBy: {
              order: 'asc',
            },
            include: {
              storePositions: {
                where: {
                  isActive: true,
                },
                orderBy: {
                  order: 'asc',
                },
                include: {
                  trainings: {
                    include: {
                      trainingHistory: {
                        where: {
                          personId: user.person.id
                        }
                      },
                    },
                  },
                },
              },
            },
          }
        }
      })

      const storeAreas = store.areas;
      const trainings = flatMap(storeAreas, storeArea =>
        flatMap(storeArea.storePositions, storePosition =>
          map(storePosition.trainings, (training): EmployeePositionTrainingRequestDto => {
            const trainingHistory = training.trainingHistory[0];
            return {
              storeAreaTitle: storeArea.title,
              storePositionTitle: storePosition.title,
              storePositionId: storePosition.id,
              storePositionOrder: storePosition.order,
              isCompleted: trainingHistory?.isCompleted ?? false,
              isPersonPreferred: trainingHistory?.isPersonPreferred ?? false,
            };
          })
        )
      );

      return {
        storeTitle: store.title,
        positionTrainings: trainings
      }
    }),

  getEmployeePositionTrainingRequests: verifiedProcedure
    .input(z.object({
      storeId: z.string().optional()
    }))
    .output(z.object({
      storeTitle: z.string(),
      positionTrainings: z.array(employeePositionTrainingRequestDto)
    }))
    .query(async ({ctx, input}) => {
      const user = await ctx.prisma.user.findFirstOrThrow({
        where: {
          id: ctx.auth.user.id
        },
        include: {
          person: {
            include: {
              employmentRequests: {
                where: {
                  stores: {
                    some: {
                      storeId: input.storeId
                    }
                  }
                },
              }
            }
          }
        }
      })

      const employmentRequest = user.person.employmentRequests[0];
      if (!employmentRequest) {
        return {
          storeTitle: "",
          positionTrainings: []
        }
      }

      const store = await ctx.prisma.store.findUniqueOrThrow({
        where: {
          id: input.storeId,
          businessId: user.person.businessId!
        },
        include: {
          areas: {
            where: {
              isActive: true,
              storePositions: {
                some: {
                  isActive: true,
                  trainings: {
                    some: {
                      isActive: true,
                    }
                  }
                }
              }
            },
            orderBy: {
              order: 'asc',
            },
            include: {
              storePositions: {
                where: {
                  isActive: true,
                },
                orderBy: {
                  order: 'asc',
                },
                include: {
                  trainings: {
                    include: {
                      trainingHistoryRequests: {
                        where: {
                          employmentRequestId: employmentRequest?.id!,
                        }
                      },
                    },
                  },
                },
              },
            },
          }
        }
      });

      const storeAreas = store.areas;
      const requests = flatMap(storeAreas, storeArea =>
        flatMap(storeArea.storePositions, storePosition =>
          map(storePosition.trainings, training => {
            const trainingRequest = training.trainingHistoryRequests[0];
            return {
              storeAreaTitle: storeArea.title,
              storePositionTitle: storePosition.title,
              storePositionId: storePosition.id,
              storePositionOrder: storePosition.order,
              isCompleted: trainingRequest?.isCompleted ?? false,
              isPersonPreferred: trainingRequest?.isPersonPreferred ?? false,
            };
          })
        )
      );

      return {
        storeTitle: store.title,
        positionTrainings: requests
      }
    }),

  setEmployeePositionTrainingRequest: verifiedProcedure
    .input(z.object({
      storeId: z.string(),
      storePositionId: z.string(),
      isCompleted: z.boolean(),
      isPersonPreferred: z.boolean(),
    }))
    .mutation(async ({ctx, input}) => {
      const {storeId, storePositionId, isCompleted, isPersonPreferred} = input;

      const user = await ctx.prisma.user.findFirstOrThrow({
        where: {
          id: ctx.auth.user.id,
        },
        include: {
          person: {
            include: {
              employmentRequests: {
                include: {
                  stores: true
                }
              },
            }
          }
        }
      })

      const employmentRequest = find(user.person?.employmentRequests, req => {
        return req.stores.some(store => store.storeId === storeId);
      });

      if (!employmentRequest) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Employement Request not found"
        });
      }

      const positionTraining = await ctx.prisma.positionTraining.findFirstOrThrow({
        where: {
          storePositionId,
        },
      });

      const {id: employmentRequestId} = employmentRequest;
      const {id: positionTrainingId} = positionTraining;

      const historyRequest = await ctx.prisma.positionTrainingHistoryRequest.findFirst({
        where: {
          positionTrainingId: positionTrainingId,
          employmentRequestId: employmentRequestId,
        },
      });

      if (historyRequest) {
        await ctx.prisma.positionTrainingHistoryRequest.update({
          where: {
            id: historyRequest.id
          },
          data: {
            isCompleted,
            isPersonPreferred,
          }
        })
      } else {
        await ctx.prisma.positionTrainingHistoryRequest.create({
          data: {
            id: genPositionTrainingHistoryRequestId(),
            positionTrainingId,
            employmentRequestId,
            isCompleted,
            isPersonPreferred,
          }
        })
      }
    }),

  approveEmploymentRequest: employedStatsProcedure({feature: "hr"})
    .input(z.object({
      personId: z.string(),
      storeId: z.string(),
    }))
    .output(z.void())
    .mutation(async ({ctx, input}) => {
      const {businessId, employment} = ctx;
      const storeId = toSecureStoreIdOrThrow(ctx, input.storeId);

      throwIfDenied({
        ctx, principalEmployment: employment,
        action: "employmentRequest/approve",
        resource: `business/${businessId}/store/${storeId}`,
        resourceEntity: {
          id: storeId,
          storeId: storeId,
        }
      })

      // convert the employment request rows to real rows
      const {personId} = await ctx.prisma.$transaction(async (prisma) => {
        const person = await prisma.person.findUniqueOrThrow({
          where: {
            id: input.personId,
            businessId: businessId,
          },
          include: {
            user: true,
            employmentRequests: {
              include: {
                job: true,
                stores: true,
                trainingHistory: {
                  include: {
                    positionTraining: {
                      include: {
                        storePosition: true
                      }
                    }
                  }
                }
              },
              where: {
                stores: {
                  some: {
                    isApproved: false,
                    storeId: storeId
                  }
                }
              }
            }
          }
        });

        const employmentRequest = find(person.employmentRequests, er => er.stores.some(store => store.storeId === storeId));
        if (!employmentRequest) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "No unapproved employment request for the store found"
          });
        }

        // if the person doesn't have an employment yet, create one
        // They wouldn't have an employment if this was the first store they're getting approved at.
        const employment = await prisma.employment.findFirst({
          where: {
            personId: person.id,
            businessId: businessId,
          },
          include: {
            stores: true
          }
        });

        const store = find(employmentRequest.stores, store => store.storeId === storeId);
        const proficiencyRanking = store?.proficiencyRanking ?? 1;

        if (!employment) {
          await prisma.employment.create({
            data: {
              id: genEmploymentId(),
              personId: person.id,
              businessId: businessId,
              jobId: employmentRequest.jobId,
              permissionPolicy: employmentRequest.permissionPolicy as any,
              userRoles: {
                create: {
                  id: genUserRoleId(),
                  userId: person.user!.id,
                  roleId: employmentRequest.job.defaultRoleId
                }
              },
              stores: {
                create: {
                  id: genStoreEmploymentId(),
                  storeId: storeId,
                  proficiencyRanking,
                  storeLoginPin: await ctx.db.store.generateUniqueStoreLoginPin(storeId)
                }
              }
            }
          });

        } else {
          // if the person already has an employment, just add the store to their employments
          if (find(employment.stores, s => s.id === storeId)) {
            throw new TRPCError({
              code: "BAD_REQUEST",
              message: "Person is already employed at this store"
            });
          }

          await prisma.storeEmployment.create({
            data: {
              id: genStoreEmploymentId(),
              storeId: storeId,
              employmentId: employment.id,
              proficiencyRanking,
              storeLoginPin: await ctx.db.store.generateUniqueStoreLoginPin(storeId)
            }
          });
        }

        // mark overall employment request as approved, and the store employment request as approved
        await prisma.employmentRequest.update({
          where: {
            id: employmentRequest.id
          },
          data: {
            isApproved: true,
            stores: {
              updateMany: {
                where: {
                  storeId: storeId,
                },
                data: {
                  isApproved: true
                }
              }
            }
          }
        });

        // create the position training histories
        await prisma.positionTrainingHistory.createMany({
          data: map(employmentRequest.trainingHistory, trainingHistoryRequest => ({
            id: genPositionTrainingHistoryId(),
            personId: person.id,
            positionTrainingId: trainingHistoryRequest.positionTraining.id,
            isCompleted: trainingHistoryRequest.isCompleted,
            isPersonPreferred: trainingHistoryRequest.isPersonPreferred
          }))
        })

        return {
          personId: person.id,
        }
      });

      if (personId) {
        const person = await ctx.db.person.getPersonInStore(storeId, personId, {
          ...personWithJobAndImageIncludes.include
        });

        const chatUser = personToChatUser(ctx, person);

        // purposefully not awaiting
        upsertAndSyncChatUsers(ctx, [chatUser]);
      }
    }),

  createEvaluation: employedStatsProcedure({feature: "hr"})
    .input(z.object({
      storeId: z.string(),
      personId: z.string(),
      storePositionId: z.string(),
      storePositionTitle: z.string().optional(),
      rating: z.number(),
      notes: z.string().optional(),
    }))
    .output(z.void())
    .mutation(async ({ctx, input}) => {
      const {businessId, employment} = throwIfNotEmployedByBusiness(ctx);
      // TODO new permissions
      const recordedAt = new Date();

      const person = await ctx.prisma.person.findFirstOrThrow({
        where: {
          id: input.personId,
          businessId,
        },
        include: {
          user: {
            include: {
              devicePushTokens: true
            }
          },
          employments: {
            include: {
              stores: {
                where: {
                  storeId: input.storeId
                }
              },
              job: true,
            }
          },
          employmentRequests: {
            include: {
              stores: {
                where: {
                  storeId: input.storeId
                }
              },
              job: true,
            }
          },
          notificationSettings: {
            where: {
              storeId: input.storeId
            }
          }
        }
      });

      const mainEmploymentRequest = getPersonMainEmploymentOrRequest(person);
      const storeEmployment = mainEmploymentRequest?.stores?.find(store => store.storeId === input.storeId);

      throwIfDenied({
        ctx, principalEmployment: employment,
        action: "storeEmployment/createPositionScore",
        resource: `business/${businessId}/storeEmployment/${storeEmployment?.id}`,
        resourceEntity: {
          storeId: storeEmployment?.storeId,
        }
      })

      await createMetricDataPoint(ctx, {
        businessId: businessId,
        personId: input.personId,
        storePositionId: input.storePositionId,
        storeId: input.storeId,
        recorderPersonId: ctx.auth.user.person.id,
        namespace: "Store Position Performance",
        name: `Evaluation`,
        notes: input.notes,
        value: input.rating,
        timestamp: recordedAt,
        unit: "PERCENT",
      })

      await notifyPerson({
        ctx,
        person: person,
        message: `Nation: ${ctx.auth.user.person.firstName} ${ctx.auth.user.person.lastName} has updated your position score${input.storePositionTitle ? ` for ${input.storePositionTitle}` : ''} to ${input.rating}%${input.notes ? ` with note: ${input.notes}` : ''}.`,
        subject: "Nation: Position Score Updated",
        preferences: {
          pushColumn: "receiveFeedbackUpdates",
          smsColumn: "receiveFeedbackUpdatesSMS"
        },
        deduplicationId: null,
        code: NotificationCode.positionScore.updated,
        entityIds: {
          storeId: input.storeId,
          personId: input.personId,
          positionId: input.storePositionId,
        }
      });
    }),

  createCoreValueEvaluation: employedStatsProcedure({feature: "hr"})
    .input(z.object({
      storeId: z.string(),
      personId: z.string(),
      coreValues: z.array(z.object({
        id: z.string(),
        rating: z.number(),
      })),
      notes: z.string().optional(),
    }))
    .output(z.void())
    .mutation(async ({ctx, input}) => {
      const {businessId, employment} = throwIfNotEmployedByBusiness(ctx);
      // TODO new permission

      const recordedAt = new Date();

      const person = await ctx.prisma.person.findUniqueOrThrow({
        where: {
          id: input.personId,
          businessId: businessId
        },
        include: {
          user: true
        }
      })

      throwIfDenied({
        ctx, principalEmployment: employment,
        action: "person/createCoreValueScore",
        resource: `business/${businessId}/person/${input.personId}`,
        resourceEntity: {
          ownerId: person.user?.id,
        }
      })

      for (const coreValue of input.coreValues) {
        await createMetricDataPoint(ctx, {
          businessId: businessId,
          personId: input.personId,
          coreValueId: coreValue.id,
          recorderPersonId: ctx.auth.user.person.id,
          namespace: "Core Value Demonstration",
          name: `Evaluation`,
          notes: input.notes,
          value: coreValue.rating,
          timestamp: recordedAt,
          unit: "PERCENT",
        })
      }
    }),

  upsertPositionTrainingHistoryRequest: employedStatsProcedure({feature: "hr"})
    .input(z.object({
      personId: z.string(),
      storePositionId: z.string(),
      isCompleted: z.boolean(),
    }))
    .output(z.void())
    .mutation(async ({ctx, input}) => {
      const {businessId} = throwIfNotEmployedByBusiness(ctx);

      await ctx.prisma.$transaction(async (prisma) => {
        // find the store that this position is in
        const store = await prisma.store.findFirstOrThrow({
          where: {
            businessId,
            positions: {
              some: {
                id: input.storePositionId
              }
            }
          }
        })

        const person = await prisma.person.findUniqueOrThrow({
          where: {
            id: input.personId,
            businessId,
          },
          include: {
            employmentRequests: {
              where: {
                stores: {
                  some: {
                    storeId: store.id
                  }
                },
                isApproved: false,
                job: {
                  businessId
                },
              },
              include: {
                trainingHistory: {
                  where: {
                    positionTraining: {
                      storePositionId: input.storePositionId
                    }
                  }
                }
              }
            }
          }
        });

        const employmentRequest = person.employmentRequests[0];
        if (!employmentRequest) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "Person does not have a pending employment request in this store."
          });
        }

        if (!isEmpty(employmentRequest.trainingHistory)) {
          const request = employmentRequest.trainingHistory[0];
          await prisma.positionTrainingHistoryRequest.update({
            where: {
              id: request.id
            },
            data: {
              isCompleted: input.isCompleted
            }
          })
        } else {
          const positionTraining = await prisma.positionTraining.findFirstOrThrow({
            where: {
              storePositionId: input.storePositionId,
              isActive: true
            }
          })
          await prisma.positionTrainingHistoryRequest.create({
            data: {
              id: genPositionTrainingHistoryRequestId(),
              isCompleted: input.isCompleted,
              isPersonPreferred: false,
              employmentRequest: {
                connect: {
                  id: employmentRequest.id
                }
              },
              positionTraining: {
                connect: {
                  id: positionTraining.id
                }
              }
            }
          })
        }
      });
    }),

  upsertPositionTrainingHistory: employedStatsProcedure({feature: "hr"})
    .input(z.object({
      personId: z.string(),
      storeId: z.string(),
      storePositionId: z.string(),
      isCompleted: z.boolean(),
      isPersonPreferred: z.boolean().optional(),
    }))
    .output(z.void())
    .mutation(async ({ctx, input}) => {
      const {businessId, employment} = throwIfNotEmployedByBusiness(ctx);
      const storeId = toSecureStoreIdOrThrow(ctx, input.storeId);
      const storeEmployment = await ctx.prisma.storeEmployment.findFirst({
        where: {
          storeId: storeId,
          employment: {
            personId: input.personId,
          }
        },
        include: {
          employment: {
            include: {
              person: {
                include: {
                  user: true,
                }
              }
            }
          }
        }
      });

      throwIfDenied({
        ctx, principalEmployment: employment,
        action: "storeEmployment/putPositionTraining",
        resource: `business/${businessId}/storeEmployment/${storeEmployment?.id}`,
        resourceEntity: {
          storeId: storeId,
          ownerId: storeEmployment?.employment?.person?.user?.id,
        }
      })

      await ctx.prisma.$transaction(async prisma => {
        const positionTraining = await prisma.positionTraining.findFirstOrThrow({
          where: {
            storePositionId: input.storePositionId,
            isActive: true,
          },
          include: {
            trainingHistory: {
              where: {
                personId: input.personId
              }
            }
          }
        });

        if (!isEmpty(positionTraining.trainingHistory)) {
          const history = positionTraining.trainingHistory[0];
          await prisma.positionTrainingHistory.update({
            where: {
              id: history.id
            },
            data: {
              isCompleted: input.isCompleted,
              isPersonPreferred: input.isPersonPreferred
            }
          })
        } else {
          await prisma.positionTrainingHistory.create({
            data: {
              id: genPositionTrainingHistoryId(),
              person: {
                connect: {
                  id: input.personId
                }
              },
              positionTraining: {
                connect: {
                  id: positionTraining.id
                }
              },
              isCompleted: input.isCompleted,
              isPersonPreferred: input.isPersonPreferred ?? false
            }
          })
        }
      })

      // purposefully not awaiting. Sync membership of private channels. Some of the private channels may use position training as a membership rule, thus why we have to sync here.
      syncPrivateChannelMemberships(ctx)
    }),

  upsertPersonNote: employedStatsProcedure({feature: "hr"})
    .input(z.object({
      personId: z.string(),
      note: z.string(),
      noteId: z.string(),
      type: z.string().optional(),
      sensitivityLevel: z.string(),
      requiresAcknowledgement: z.boolean().optional(),
      policiesInAction: z.array(z.string()).optional(),
      storeId: z.string(),
      // Any new images that will be added to the note
      images: z.array(saveImageDto).optional().default([]),
      // When editing an existing note, this is the existing image IDs that will be copied to the new note
      copyImageIds: z.array(z.string()).optional(),
      // If this note is being created from a Corrective Action
      fromCorrectiveActionId: z.string().optional(),
      // DEPRECATED: Use `copyImageIds` instead; remove in later app version
      // Which images from the Corrective Action will be copied to the new note
      copyCorrectiveActionImageIds: z.array(z.string()).optional(),
    }))
    .output(z.void())
    .mutation(async ({ctx, input}) => {
      const {businessId, employment} = ctx;
      const storeId = toSecureStoreIdOrThrow(ctx, input.storeId);
      const person = await ctx.db.person.getPersonInBusiness(businessId, input.personId, {
        employments: {
          include: {
            stores: true,
            job: true,
          }
        },
        user: {
          include: {
            devicePushTokens: true,
          }
        },
        notificationSettings: true,
      });

      throwIfDenied({
        ctx, principalEmployment: employment,
        action: "person/createNote",
        resource: `business/${businessId}/person/${person.id}`,
        resourceEntity: {
          id: input.personId,
          businessId: businessId,
          ownerId: person.user?.id,
        }
      });

      const existingNote = await ctx.db.note.findNoteById(businessId, storeId, {
        noteId: input.noteId
      });

      let historicalCopiedImages: SaveImageDto[] = [];
      if (existingNote) {
        historicalCopiedImages = await copyImagesInS3({
          noteId: input.noteId,
          businessId,
          s3Client: ctx.s3Client,
          s3MediaBucket: ctx.s3MediaBucket,
          images: existingNote.images,
          folderName: "note",
        });
      }

      const imagesToCopy = [
        ...input.copyCorrectiveActionImageIds ?? [],
        ...input.copyImageIds ?? []
      ];

      // If we're copying images from an existing note or corrective action, we need to copy them to S3
      let existingImages: SaveImageDto[] = [];
      if (!existingNote && imagesToCopy) {
        const copyingImages = await ctx.prisma.image.findMany({
          where: {
            id: {
              in: imagesToCopy
            }
          }
        });
        existingImages = await copyImagesInS3({
          noteId: input.noteId,
          businessId,
          s3Client: ctx.s3Client,
          s3MediaBucket: ctx.s3MediaBucket,
          images: copyingImages,
          folderName: "note",
        });
      }

      const sensitivityLevel = input.sensitivityLevel === "low"
        ? noteLowSensitivityLevel
        : input.sensitivityLevel === "medium"
          ? noteMediumSensitivityLevel
          : input.sensitivityLevel === "high"
            ? noteHighSensitivityLevel
            : noteLowSensitivityLevel;

      await ctx.prisma.$transaction(async prisma => {
        await prisma.entityNote.upsert({
          where: {
            id: input.noteId,
            storeId,
            businessId,
          },
          update: {
            version: genNextEntityNoteVersion(existingNote),
            note: input.note,
            updatedById: ctx.auth.user.person.id,
            updatedAt: new Date(),
            sensitivityLevel,
            requiresAcknowledgement: input.requiresAcknowledgement,
            policiesInAction: input.policiesInAction,
            images: {
              set: [],
              connect: map(imagesToCopy, id => ({
                id
              })),
              create: [
                ...buildImageCreateInput({
                  images: input.images,  // any new images added to this version,
                  userId: ctx.auth.user.id,
                  businessId: businessId,
                  prefixId: "note",
                })
              ]
            },
          },
          create: {
            id: input.noteId,
            businessId,
            storeId: storeId,
            personId: person.id,
            note: input.note,
            createdByPersonId: ctx.auth.user.person.id,
            noteType: input.type,
            sensitivityLevel,
            requiresAcknowledgement: input.requiresAcknowledgement,
            policiesInAction: input.policiesInAction,
            images: {
              create: [
                ...buildImageCreateInput({
                  images: [...existingImages, ...input.images],
                  userId: ctx.auth.user.id,
                  businessId: businessId,
                  prefixId: "note",
                })
              ]
            },
            fromCorrectiveActionId: input.fromCorrectiveActionId,
            version: 1,
          }
        })

        if (existingNote) {
          await prisma.entityNoteHistory.create({
            data: toEntityNoteHistoryCreateInput({
              note: existingNote,
              userId: ctx.auth.user.id,
              historicalCopiedImages,
            })
          });
        }
      });

      // If the new note is a positive, coaching, or general feedback, notify the person
      // if they have permission to view the sensitivity level of the note.
      if (input.storeId
        && person.user
        && includes(["positive-feedback", "coaching", "general"], input.type)) {
        const storeId = toSecureStoreIdOrThrow(ctx, input.storeId);
        const employment = getPersonMainEmployment(person.employments);
        if (!employment) {
          return;
        }

        const checkAllowed = isAllowedFactory({
          businessId,
          principalEmployment: employment,
          userId: person.user.id,
        });

        const maxSensitivityLevel = perm.getMaxAllowedPersonNoteSensitivityLevel(checkAllowed, {
          storeId,
          businessId,
          personId: input.personId,
          // The recipient is the requestingPersonId since it will be _their_ own note
          requestingPersonId: input.personId,
        });

        if (maxSensitivityLevel >= sensitivityLevel) {
          await notifyPerson({
            ctx,
            person: person,
            message: `Nation: A new ${(input.type ?? "").replace(/[^a-zA-Z]/g, " ")} note has been added to your profile by ${ctx.auth.user.person.firstName} ${ctx.auth.user.person.lastName}.`,
            subject: "Nation: Note Added",
            preferences: {
              pushColumn: "receiveFeedbackUpdates",
              smsColumn: "receiveFeedbackUpdatesSMS"
            },
            deduplicationId: null,
            code: NotificationCode.note.created,
            entityIds: {
              storeId: input.storeId,
              personId: person.id,
              entityNoteId: input.noteId,
            }
          })
        }
      }
    }),

  archivePersonNote: employedStatsProcedure({feature: "hr"})
    .input(z.object({
      id: z.string(),
      storeId: z.string(),
      isArchived: z.boolean().optional().default(true)
    }))
    .output(z.void())
    .mutation(async ({ctx, input}) => {
      const {businessId, employment, auth} = ctx;
      const personId = auth.user.person.id!;
      const storeId = toSecureStoreIdOrThrow(ctx, input.storeId);

      throwIfDenied({
        ctx, principalEmployment: employment,
        action: "person/createNote",
        resource: `business/${businessId}/person/${personId}`,
        resourceEntity: {
          id: personId,
          businessId: businessId,
          ownerId: personId,
        }
      });

      const entityNote = await ctx.db.note.findNoteById(businessId, storeId, {
        noteId: input.id
      });
      if (!entityNote) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Unable to find note."
        });
      }

      // Copy images for historical record
      const historicalCopiedImages = await copyImagesInS3({
        noteId: entityNote.id,
        businessId,
        s3Client: ctx.s3Client,
        s3MediaBucket: ctx.s3MediaBucket,
        images: entityNote.images,
        folderName: "note",
      });

      // create historical record
      await ctx.prisma.entityNoteHistory.create({
        data: toEntityNoteHistoryCreateInput({
          note: entityNote,
          userId: ctx.auth.user.id,
          historicalCopiedImages,
        })
      });

      await ctx.prisma.entityNote.update({
        where: {
          id: entityNote.id,
          businessId,
          storeId,
        },
        data: {
          version: genNextEntityNoteVersion(entityNote),
          isArchived: input.isArchived,
          archivedAt: input.isArchived ? new Date() : undefined,
          updatedById: ctx.auth.user.person.id,
          updatedAt: new Date(),
        }
      });
    }),

  // TODO: Remove in future version
  deletePersonNote: employedStatsProcedure({feature: "hr"})
    .input(z.object({
      id: z.string()
    }))
    .output(z.void())
    .mutation(async ({}) => {
      throw new TRPCError({
        code: "FORBIDDEN",
        message: "Deprecated procedure.  Use archivePersonNote instead."
      });
    }),

  resendInvite: employedStatsProcedure({feature: "common"})
    .input(z.object({
      personId: z.string()
    }))
    .output(z.void())
    .mutation(async ({ctx, input}) => {
      const {businessId} = throwIfNotEmployedByBusiness(ctx);
      await resendBusinessInvitation({ctx, personId: input.personId, businessId})
    }),

  // TODO:  This is a duplicate of `ArchiveActivePerson`.  We should consolidate these two.
  cancelInviteAndArchivePerson: employedStatsProcedure({feature: "common"})
    .input(z.object({
      personId: z.string()
    }))
    .output(z.void())
    .mutation(async ({ctx, input}) => {
      const {businessId, employment: requesterEmployment} = throwIfNotEmployedByBusiness(ctx);

      const usersStores = map(requesterEmployment.stores, s => s.storeId);

      await ctx.prisma.$transaction(async prisma => {

        // 1 find the person
        const person = await prisma.person.findUniqueOrThrow({
          where: {
            id: input.personId,
            businessId,
            // can only archive people from stores you have access to
            OR: [{
              employments: {
                some: {
                  stores: {
                    some: {
                      storeId: {in: usersStores}
                    }
                  }
                }
              }
            }, {
              employmentRequests: {
                some: {
                  stores: {
                    some: {
                      storeId: {in: usersStores}
                    }
                  }
                }
              }
            }
            ]
          },
          include: {
            employments: true,
            employmentRequests: true,
            user: {
              select: {
                id: true
              }
            }
          }
        });

        const archivedPersonUserId = person.user?.id;
        throwIfDenied({
          ctx,
          principalEmployment: requesterEmployment,
          action: "person/archive",
          resource: `business/${businessId}/person/${person.id}`,
          resourceEntity: {
            id: person.id,
            businessId: person.businessId,
            ownerId: archivedPersonUserId,
          }
        });

        // Mark the person as archived
        await prisma.person.update({
          where: {
            id: input.personId
          },
          data: {
            isArchived: true,
          }
        });

        // mark the person's employment at this business and all its store employments as ended
        const employment = getPersonMostRecentEmployment(person.employments);
        const employmentRequest = getPersonMostRecentEmploymentRequest(person.employmentRequests);

        if (employment) {
          await prisma.employment.update({
            where: {
              id: employment.id,
              personId: input.personId,
            },
            data: {
              endedAt: new Date(),
              stores: {
                updateMany: {
                  where: {},
                  data: {
                    endedAt: new Date(),
                  }
                }
              }
            }
          });
        } else if (employmentRequest) {
          await prisma.employmentRequest.update({
            where: {
              id: employmentRequest.id,
              personId: input.personId,
            },
            data: {
              endedAt: new Date(),
              stores: {
                updateMany: {
                  where: {},
                  data: {
                    endedAt: new Date(),
                  }
                }
              }
            }
          });
        } else {
          throw new TRPCError({
            code: "BAD_REQUEST",
            message: "Person is not employed at this business."
          });
        }

        if (archivedPersonUserId) {
          // If the user has a person record, suspend the user so that they can't log in
          await prisma.user.update({
            where: {
              id: archivedPersonUserId
            },
            data: {
              isSuspended: true,
            }
          });

          // delete any active sessions for the user
          await ctx.prisma.passwordResetCode.deleteMany({
            where: {userId: archivedPersonUserId}
          })
          await ctx.prisma.emailVerificationCode.deleteMany({
            where: {userId: archivedPersonUserId}
          });
          await ctx.prisma.tempAuthToken.deleteMany({
            where: {userId: archivedPersonUserId}
          });
          await ctx.prisma.session.deleteMany({
            where: {userId: archivedPersonUserId}
          })
          await ctx.prisma.userClient.deleteMany({
            where: {userId: archivedPersonUserId}
          })
        }

        await prisma.businessInvitation.updateMany({
          where: {
            personId: person.id,
            isAccepted: false,
          },
          data: {
            expiresAt: new Date(),
            // clear out the codeHash so that it doesn't occupy code space after acceptance
            code: uuidv4(),
            codeHash: uuidv4(),
          }
        });

        // publish domain event
        await ctx.eventPublisher.publish({
          ...getBaseEvent({
            requestingPersonId: ctx.auth.user.person.id,
            connections: {
              businessId,
              personId: input.personId,
            }
          }),
          type: "personArchived",
          data: {
            personId: input.personId,
            businessId,
            note: "Archiving team member in pending status",
          }
        });
      });
    }),

  archiveActivePerson: employedStatsProcedure({feature: "common"})
    .input(z.object({
      personId: z.string(),
      note: z.string().optional(),
    }))
    .output(z.void())
    .mutation(async ({ctx, input}) => {
      const {businessId, employment: requesterEmployment, storeIds} = ctx;
      const usersStores = map(requesterEmployment.stores, s => s.storeId);

      await ctx.prisma.$transaction(async prisma => {

        // 1 find the person
        const person = await prisma.person.findUniqueOrThrow({
          where: {
            id: input.personId,
            businessId,
            // can only archive people from stores you have access to
            OR: [{
              employments: {
                some: {
                  stores: {
                    some: {
                      storeId: {in: usersStores}
                    }
                  }
                }
              }
            }, {
              employmentRequests: {
                some: {
                  stores: {
                    some: {
                      storeId: {in: usersStores}
                    }
                  }
                }
              }
            }
            ]
          },
          include: {
            employments: true,
            employmentRequests: true,
            user: {
              select: {
                id: true
              }
            }
          }
        });

        const archivedPersonUserId = person.user?.id;
        throwIfDenied({
          ctx,
          principalEmployment: requesterEmployment,
          action: "person/archive",
          resource: `business/${businessId}/person/${person.id}`,
          resourceEntity: {
            id: person.id,
            businessId: person.businessId,
            ownerId: archivedPersonUserId,
          }
        });

        // Mark the person as archived
        await prisma.person.update({
          where: {
            id: input.personId
          },
          data: {
            isArchived: true,
          }
        });

        // mark the person's employment at this business and all its store employments as ended
        const employment = getPersonMostRecentEmployment(person.employments);
        const employmentRequest = getPersonMostRecentEmploymentRequest(person.employmentRequests);

        if (employment) {
          await prisma.employment.update({
            where: {
              id: employment.id,
              personId: input.personId,
            },
            data: {
              endedAt: new Date(),
              stores: {
                updateMany: {
                  where: {},
                  data: {
                    endedAt: new Date(),
                  }
                }
              }
            }
          });
        } else if (employmentRequest) {
          await prisma.employmentRequest.update({
            where: {
              id: employmentRequest.id,
              personId: input.personId,
            },
            data: {
              endedAt: new Date(),
              stores: {
                updateMany: {
                  where: {},
                  data: {
                    endedAt: new Date(),
                  }
                }
              }
            }
          });
        } else {
          throw new TRPCError({
            code: "BAD_REQUEST",
            message: "Person is not employed at this business."
          });
        }

        if (archivedPersonUserId) {
          // If the user has a person record, suspend the user so that they can't log in
          await prisma.user.update({
            where: {
              id: archivedPersonUserId
            },
            data: {
              isSuspended: true,
            }
          });

          // delete any active sessions for the user
          await ctx.prisma.passwordResetCode.deleteMany({
            where: {userId: archivedPersonUserId}
          })
          await ctx.prisma.emailVerificationCode.deleteMany({
            where: {userId: archivedPersonUserId}
          });
          await ctx.prisma.tempAuthToken.deleteMany({
            where: {userId: archivedPersonUserId}
          });
          await ctx.prisma.session.deleteMany({
            where: {userId: archivedPersonUserId}
          })
          await ctx.prisma.userClient.deleteMany({
            where: {userId: archivedPersonUserId}
          })
        }

        await prisma.businessInvitation.updateMany({
          where: {
            personId: person.id,
            isAccepted: false,
          },
          data: {
            expiresAt: new Date(),
            // clear out the codeHash so that it doesn't occupy code space after acceptance
            code: uuidv4(),
            codeHash: uuidv4(),
          }
        });
      });

      // purposefully not awaiting
      for (const storeId of storeIds) {
        deactivateChatUser(ctx, storeId, input.personId);
      }

      // publish domain event
      await ctx.eventPublisher.publish({
        ...getBaseEvent({
          requestingPersonId: ctx.auth.user.person.id,
          connections: {
            businessId,
            personId: input.personId,
          }
        }),
        type: "personArchived",
        data: {
          personId: input.personId,
          businessId,
          note: input.note,
        }
      });
    }),

  editEmployeeProficiencyRanking: employedStatsProcedure({feature: "common"})
    .input(z.object({
      personId: z.string(),
      storeId: z.string(),
      ranking: z.number()
    }))
    .output(z.void())
    .mutation(async ({ctx, input}) => {
      const {businessId, employment: userEmployment} = throwIfNotEmployedByBusiness(ctx);

      const person = await ctx.prisma.person.findFirstOrThrow({
        where: {
          AND: [
            {id: input.personId},
            {
              OR: [{
                employments: {
                  some: {
                    job: {
                      businessId,
                    }
                  }
                }
              }, {
                employmentRequests: {
                  some: {
                    job: {
                      businessId,
                    }
                  }
                }
              }]
            }
          ]
        },
        include: {
          user: true,
          employments: {
            include: {
              stores: true,
              userRoles: true
            }
          },
          employmentRequests: {
            include: {
              stores: true
            }
          },
        }
      })

      const employment = getPersonMainEmployment(person?.employments);
      const employmentRequest = find(person.employmentRequests, e => some(e.stores, store => store.storeId === input.storeId))

      const _employment = employment ?? employmentRequest;
      throwIfDenied({
        ctx,
        principalEmployment: userEmployment,
        action: "storeEmployment/putProficiencyScore",
        resource: `business/${businessId}/storeEmployment/${_employment?.id}`,
        resourceEntity: {
          id: _employment?.id,
          storeId: input.storeId,
          ownerId: person.user?.id,
        }
      });

      if (employment) {
        const storeEmployment = find(employment.stores, s => s.storeId === input.storeId)
        if (storeEmployment) {
          await ctx.prisma.storeEmployment.update({
            where: {
              id: storeEmployment.id
            },
            data: {
              proficiencyRanking: input.ranking
            }
          })
        }
      }
      if (employmentRequest) {
        const storeEmploymentRequest = find(employmentRequest.stores, s => s.storeId === input.storeId)
        if (storeEmploymentRequest) {
          await ctx.prisma.storeEmploymentRequest.update({
            where: {
              id: storeEmploymentRequest.id
            },
            data: {
              proficiencyRanking: input.ranking
            }
          })
        }
      }
    }),

  editEmployeeDetails: employedStatsProcedure({feature: "common"})
    .input(z.object({
      personId: z.string(),
      storeId: z.string(),
      details: z.object({
        firstName: z.string().optional(),
        lastName: z.string().optional(),
        email: emailSchema.optional(),
        phone: phoneNumberSchema.optional(),
        metadata: metadataDto.optional(),
        address: addressCreateDto.optional(),
      }),
    }))
    .mutation(async ({ctx, input}) => {
      const {businessId, employment: userEmployment} = ctx

      if (input.details.phone || input.details.email) {
        // check for existing phone or email
        const personWithPhoneOrEmail = await ctx.prisma.person.findFirst({
          where: {
            AND: [{
              OR: [
                {phoneNumber: input.details.phone},
                {email: input.details.email}
              ]
            }, {
              NOT: {
                id: input.personId
              }
            }]
          }
        });

        if (personWithPhoneOrEmail) {
          if (personWithPhoneOrEmail.businessId !== businessId) {
            throw new TRPCError({
              code: "BAD_REQUEST",
              message: "That phone or email is already in use by another business"
            });
          } else {
            throw new TRPCError({
              code: "BAD_REQUEST",
              message: "Another employee already has that phone or email"
            });
          }
        }
      }

      const person = await ctx.prisma.person.findFirstOrThrow({
        where: {
          AND: [
            {id: input.personId},
            {
              OR: [{
                employments: {
                  some: {
                    job: {
                      businessId,
                    }
                  }
                }
              }, {
                employmentRequests: {
                  some: {
                    job: {
                      businessId,
                    }
                  }
                }
              }]
            }
          ]
        },
        include: {
          user: true,
          employments: {
            include: {
              stores: true,
              userRoles: true
            }
          },
          employmentRequests: {
            include: {
              stores: true
            }
          },
        }
      })

      throwIfDenied({
        ctx,
        principalEmployment: userEmployment,
        action: "person/update",
        resource: `business/${businessId}/person/${person.id}`,
        resourceEntity: {
          id: person.id,
          businessId: person.businessId,
          ownerId: person.user?.id,
        }
      });

      const updatedMetadata = {
        ...(person.metadata as any) ?? {},
        ...omitBy(input.details.metadata, isUndefined)
      };

      const address = input.details.address ? {
        address: {
          create: {
            id: genAddressId(),
            ...input.details.address,
            line2: input.details.address.line2 ?? undefined,
          },
          update: {
            ...input.details.address,
            line2: input.details.address.line2 ?? undefined,
          }
        },
      } : {}

      const newPerson = await ctx.prisma.person.update({
        where: {
          businessId,
          id: input.personId
        },
        data: {
          firstName: input.details.firstName ?? person.firstName,
          lastName: input.details.lastName ?? person.lastName,
          email: input.details.email ?? person.email,
          phoneNumber: input.details.phone ?? person.phoneNumber,
          metadata: updatedMetadata,
          ...address,
        },
        ...personWithJobAndImageIncludes
      })

      // If this person is an operator, we need to make sure they stay on the operators team
      const employment = getPersonMainEmployment(newPerson.employments);
      const jobCreatedFromId = employment?.job.createdFromTemplateId;
      const isOperator = jobCreatedFromId === "operator";

      // purposefully not awaiting
      const chatUser = personToChatUser(ctx, newPerson, isOperator ? ["operators"] : []);
      upsertAndSyncChatUsers(ctx, [chatUser]);
    }),

  editEmployeeJob: employedStatsProcedure({feature: "common"})
    .input(z.object({
      personId: z.string(),
      jobId: z.string(),
      permissionPackages: z.array(permissionPackageId)
    }))
    .output(z.void())
    .mutation(async ({ctx, input}) => {
      const {businessId, employment: userEmployment} = ctx;
      const person = await ctx.prisma.person.findFirstOrThrow({
        where: {
          id: input.personId,
          businessId,
        },
        include: {
          employments: {
            include: {
              stores: true,
              job: true
            }
          },
          employmentRequests: true,
          user: true,
        }
      })

      const employment = getPersonMainEmployment(person?.employments);
      const employmentRequests = person.employmentRequests;
      const job = await ctx.prisma.job.findFirstOrThrow({
        where: {
          id: input.jobId,
          businessId: businessId,
        }
      })

      // if this is the operator, then they can edit their own operator job.
      const personJob = employment?.job.createdFromTemplateId;
      const isOperator = personJob === "operator";
      if (!isOperator) {
        // cannot edit their own job
        if (ctx.auth.user.person.id === input.personId) {
          throw new TRPCError({
            code: "BAD_REQUEST",
            message: "You cannot edit your own job"
          });
        }

        // cannot edit the Operator's job
        if (job.createdFromTemplateId === "operator") {
          throw new TRPCError({
            code: "BAD_REQUEST",
            message: "You cannot edit the Operator's job"
          });
        }
      }

      if (employment) {
        throwIfDenied({
          ctx,
          principalEmployment: userEmployment,
          action: "employment/updatePermissionPolicy",
          resource: `business/${businessId}/employment/${employment.id}`,
          resourceEntity: {
            id: employment.id,
            ownerId: person.user?.id,
          }
        });
      }

      forEach(employmentRequests, er => {
        throwIfDenied({
          ctx,
          principalEmployment: userEmployment,
          action: "employment/updatePermissionPolicy",
          resource: `business/${businessId}/employment/${er.id}`,
          resourceEntity: {
            id: er.id,
            ownerId: person.user?.id,
          }
        });
      })

      const jobPolicy = job.permissionPolicy as any as DatabasePermissionPolicy;
      const permissionPolicy = combinePermissionPolicies({
        jobPermissionPolicy: jobPolicy,
        userPermissionPackages: input.permissionPackages
      });

      // Cannot assign a policy that has more permissions that the user has (otherwise, a user with approvePeople permissions could invite and approve themselves as an employee that has all permissions)

      // Get the user person's effective permissions
      const requesterPackageIds = getAllowedPermissionPackageIdsForEmployment(userEmployment);
      const jobPackageIds = chain(jobPolicy.statements)
        .filter(isPermissionPackageStatement)
        .map(p => p.packageId)
        .value();
      const personPackages = chain(permissionPolicy.statements)
        .filter(isPermissionPackageStatement)
        .map(p => p.packageId)
        .value();

      const personPackagesGranted = getPackagesGranted(personPackages, requesterPackageIds);
      const jobPackagesGranted = getPackagesGranted(jobPackageIds, requesterPackageIds);

      // we only care about highly sensitive packages
      const sensitivePackagesGranted = filter([...personPackagesGranted, ...jobPackagesGranted], pkgId => {
        const pkg = permissionPackages[pkgId];
        return pkg.isHighlySensitive;
      });

      if (sensitivePackagesGranted.length > 0) {
        // throw new TRPCError({
        //   code: "FORBIDDEN",
        //   message: `You cannot assign sensitive permissions that you do not have: ` + map(sensitivePackagesGranted, packageId => {
        //     const pgk = permissionPackages[packageId];
        //     return pgk.title;
        //   }).join(", ") + ".\n\n You may need to contact your manager to get access to these permissions."
        // });
      }

      if (employment) {
        await ctx.prisma.employment.update({
          where: {
            id: employment.id
          },
          data: {
            jobId: input.jobId,
            permissionPolicy: permissionPolicy as any
          }
        })
      }
      if (employmentRequests.length) {
        await ctx.prisma.employmentRequest.updateMany({
          where: {
            personId: input.personId,
            id: {
              in: map(employmentRequests, er => er.id)
            }
          },
          data: {
            jobId: input.jobId,
            permissionPolicy: permissionPolicy as any
          }
        })
      }

      if (!person.email) {
        return;
      }

      if (employment) {
        const currentPermissionPackageIds = getAllowedPermissionPackageIdsForEmployment(employment);

        const isCreateSchedulesNewlyGranted =
          input.permissionPackages.includes('createSchedules') &&
          !currentPermissionPackageIds.includes('createSchedules');

        if (isCreateSchedulesNewlyGranted) {
          sendSchedulerInformationEmail({
            sendEmail: ctx.sendEmail,
            recipient: person.email,
          })
        }
      } else {
        if (input.permissionPackages.includes('createSchedules')) {
          sendSchedulerInformationEmail({
            sendEmail: ctx.sendEmail,
            recipient: person.email,
          })
        }
      }

      // sync the chat user
      syncChatUsers(ctx, businessId, [person.id]);
    }),

  addPolicyInAction: employedStatsProcedure({feature: "common"})
    .input(z.object({
      title: z.string().min(1).max(512)
    }))
    .output(policyInActionDto)
    .mutation(async ({ctx, input}) => {
      const {businessId} = throwIfNotEmployedByBusiness(ctx);

      const policy = await ctx.prisma.correctiveActionPolicy.create({
        data: {
          businessId,
          title: input.title
        }
      });

      return {
        title: policy.title
      }
    }),

  getCorrectiveActionPolicies: employedStatsProcedure({feature: "common"})
    .output(z.array(z.string()))
    .query(async ({ctx}) => {
      const {businessId} = throwIfNotEmployedByBusiness(ctx);

      const policies = await ctx.prisma.correctiveActionPolicy.findMany({
        where: {
          businessId
        }
      });

      return map(policies, policy => policy.title)
    }),

  getCorrectiveActionPolicyFilters: employedStatsProcedure({feature: "common"})
    .input(z.object({
      personId: z.string()
    }))
    .output(z.array(z.string()))
    .query(async ({ctx, input}) => {
      const {businessId} = throwIfNotEmployedByBusiness(ctx);

      // all possible policies
      const res = await ctx.prisma.$queryRaw<{
        policy: string
      }[]>`select distinct unnest(ca."policiesInAction") as policy
           from "CorrectiveAction" ca
           where ca."businessId" = ${businessId}
             and ca."recipientPersonId" = ${input.personId}`;

      return map(res, r => r.policy);
    }),

  deleteCorrectiveActionPolicy: employedStatsProcedure({feature: "common"})
    .input(z.object({
      policyTitle: z.string()
    }))
    .output(z.void())
    .mutation(async ({ctx, input}) => {
      const {businessId} = throwIfNotEmployedByBusiness(ctx);

      await ctx.prisma.correctiveActionPolicy.delete({
        where: {
          businessId_title: {
            businessId,
            title: input.policyTitle
          }
        }
      })
    }),

  createCorrectiveAction: employedStatsProcedure({feature: "common"})
    .input(createCorrectiveActionDto)
    .output(z.void())
    .mutation(async ({ctx, input}) => {
      const {businessId, employment} = throwIfNotEmployedByBusiness(ctx);
      const storeId = toSecureStoreIdOrThrow(ctx, input.storeId)
      const storeEmployment = await ctx.prisma.storeEmployment.findFirst({
        where: {
          storeId: input.storeId,
          employment: {
            personId: input.recipientPersonId
          }
        }
      });
      throwIfDenied({
        ctx, principalEmployment: employment,
        action: "storeEmployment/createActionableItem",
        resource: `business/${businessId}/storeEmployment/${storeEmployment?.id}`,
        resourceEntity: {
          storeId: storeEmployment?.storeId
        }
      })

      const recordedAt = new Date();
      const infractionAt = input.infractionAt;
      const correctiveActionId = genCorrectiveActionId();
      const data = getCorrectiveActionCreateInput(createInReviewCorrectiveAction({
        id: correctiveActionId,
        businessId,
        storeId: input.storeId,
        images: input.images,
        infractionAt,
        policiesInAction: input.policiesInAction,
        incidentDescription: input.incidentDescription,
        recipientPersonId: input.recipientPersonId,
        recordedAt,
        recorderPersonId: ctx.auth.user.person.id,
      }), ctx.auth.user.id);
      await ctx.prisma.correctiveAction.create({
        data
      });

      // notify anyone with createCorrectiveActions permission package who is employed at the store
      const storeEmployments = await ctx.prisma.storeEmployment.findMany({
        where: {
          storeId: input.storeId,
        },
        include: {
          employment: {
            include: {
              person: {
                include: {
                  user: {
                    include: {
                      devicePushTokens: true
                    }
                  },
                  notificationSettings: {
                    where: {
                      storeId: input.storeId
                    }
                  },
                }
              },
              job: true
            }
          }
        }
      });

      const hrPeopleAll = map(filter(storeEmployments, storeEmployment => {
        const policy = mergeJobAndPersonPolicies({
          personPolicy: storeEmployment.employment.permissionPolicy as unknown as DatabasePermissionPolicy,
          jobPolicy: storeEmployment.employment.job.permissionPolicy as unknown as DatabasePermissionPolicy
        });

        const hasCreateCaPermissions = some(policy.statements, statement => isPermissionPackageStatement(statement)
          && statement.packageId === PackageIds.createCorrectiveActions);
        // the person needs to be able to view actionable items
        const hasViewActionableItemsPermissions = some(policy.statements, statement => isPermissionPackageStatement(statement)
          && statement.packageId === PackageIds.viewActionableItems);
        return hasCreateCaPermissions && hasViewActionableItemsPermissions;
      }), se => se.employment.person);

      // Filter out the recipient
      const hrPeople = hrPeopleAll.filter(person =>
        person.id !== input.recipientPersonId
      );

      const recipientPerson = await ctx.prisma.person.findUniqueOrThrow({
        where: {
          id: input.recipientPersonId
        }
      });
      const recipientFullName = `${recipientPerson.firstName} ${recipientPerson.lastName}`;
      const store = await ctx.prisma.store.findUniqueOrThrow({
        where: {
          id: input.storeId
        }
      });

      // limit the number of notifications sent to some sane number, just in case...
      const maxNotifications = 20;
      let notificationCount = 0;
      for (const hrPerson of hrPeople) {
        if (notificationCount >= maxNotifications) {
          Sentry.captureMessage("Exceeded max number of notifications for corrective action creation", {
            extra: {
              correctiveActionId,
              businessId
            }
          });
          break;
        }

        await notifyPerson({
          ctx,
          person: hrPerson,
          messageHtml: `An Actionable Item has been created for ${recipientFullName} in store ${store.title}. Open the Nation app and navigate to ${recipientFullName} to review.`,
          message: `An Actionable Item has been created for ${recipientFullName}. Open the Nation app to review: ${encodeURI(`gioa://person/${input.storeId}/${input.recipientPersonId}/corrective-action/${correctiveActionId}`)}`,
          subject: "Nation: Actionable Item created",
          preferences: {
            pushColumn: "receiveActionableItemUpdates",
            smsColumn: "receiveActionableItemUpdatesSMS",
          },
          onError: error => Sentry.captureMessage(error, {
            extra: {
              correctiveActionId,
              businessId,
              personId: hrPerson.id
            }
          }),
          deduplicationId: null,
          code: NotificationCode.correctiveAction.review,
          entityIds: {
            storeId: store.id,
            recipientPersonId: input.recipientPersonId,
            correctiveActionId,
          }
        });

        notificationCount++;
      }
    }),

  editActionableItem: employedStatsProcedure({feature: "common"})
    .input(z.object({
      id: z.string(),
      data: editActionableItemDto,
      version: z.number().int().nonnegative(),
    }))
    .output(z.void())
    .mutation(async ({ctx, input}) => {
      const {businessId, storeIds, employment, currentPersonId, checkAllowed} = ctx;

      const oldCaRow = await ctx.db.correctiveAction.getCorrectiveAction(businessId, storeIds, {
        checkAllowed: checkAllowed,
        correctiveActionId: input.id,
        currentPersonId,
      });

      // TODO use canCreateActionableItem permission check function
      // Check permissions
      // const storeEmployment = await ctx.prisma.storeEmployment.findFirst({
      //   where: {
      //     storeId: existingCA.storeId,
      //     employment: {
      //       personId: existingCA.recipientPersonId
      //     }
      //   }
      // });
      // throwIfDenied({
      //   ctx,
      //   principalEmployment: employment,
      //   action: "storeEmployment/createActionableItem",
      //   resource: `business/${businessId}/storeEmployment/${storeEmployment?.id}`,
      //   resourceEntity: {
      //     storeId: storeEmployment?.storeId
      //   }
      // });


      const correctiveAction = hydrateCorrectiveAction(oldCaRow);
      const actionableItem = formalizableCa.parse(correctiveAction);
      const newCa = updateActionableItem(actionableItem, input.data);

      // TODO catch version conflict and return good message
      await ctx.db.correctiveAction.updateCorrectiveAction(businessId, oldCaRow.storeId, {
        ctx,
        oldCaRow,
        oldCa: actionableItem,
        newCa,
        requestingUserId: ctx.auth.user.id,
        version: input.version
      });

    }),

  editCorrectiveAction: employedStatsProcedure({feature: "common"})
    .input(z.object({
      id: z.string(),
      data: editCorrectiveActionDto,
      version: z.number().int().nonnegative(),
    }))
    .output(z.void())
    .mutation(async ({ctx, input}) => {
      const {businessId, employment} = ctx;

      // Find the existing corrective action
      const oldCaRow = await ctx.db.correctiveAction.getCorrectiveAction(businessId, ctx.storeIds, {
        checkAllowed: ctx.checkAllowed,
        correctiveActionId: input.id,
        currentPersonId: ctx.currentPersonId
      });

      // TODO Check permissions
      // const storeEmployment = await ctx.prisma.storeEmployment.findFirst({
      //   where: {
      //     storeId: existingCA.storeId,
      //     employment: {
      //       personId: existingCA.recipientPersonId
      //     }
      //   }
      // });
      // throwIfDenied({
      //   ctx,
      //   principalEmployment: employment,
      //   action: "storeEmployment/createActionableItem",
      //   resource: `business/${businessId}/storeEmployment/${storeEmployment?.id}`,
      //   resourceEntity: {
      //     storeId: storeEmployment?.storeId
      //   }
      // });


      const correctiveAction = hydrateCorrectiveAction(oldCaRow);
      const formalizedCa = formalizedCorrectiveAction.parse(correctiveAction);
      const newCa = updateCorrectiveAction(formalizedCa, input.data);

      // TODO catch version conflict and return good message
      await ctx.db.correctiveAction.updateCorrectiveAction(businessId, oldCaRow.storeId, {
        ctx,
        oldCaRow,
        oldCa: formalizedCa,
        newCa,
        requestingUserId: ctx.auth.user.id,
        version: input.version
      });
    }),

  formalizeCorrectiveAction: employedStatsProcedure({feature: "common"})
    .input(z.object({
      correctiveActionId: z.string(),
      storeId: z.string(),
      incidentDescription: z.string(),
      actionType: z.string(),
      witnesses: z.string().optional(),
      consequencesDescription: z.string().optional(),
      policiesInAction: z.array(z.string()),
      images: z.array(saveImageDto),
      version: z.number().int().nonnegative().optional()
    }))
    .output(z.void())
    .mutation(async ({ctx, input}) => {
      // enforce BUSINESS or HR role
      const {businessId, currentPersonId, checkAllowed} = ctx;
      const userPerson = await getCurrentUser(ctx);
      const storeId = toSecureStoreIdOrThrow(ctx, input.storeId);

      const oldCaRow = await ctx.db.correctiveAction.getCorrectiveAction(businessId, ctx.storeIds, {
        checkAllowed: ctx.checkAllowed,
        correctiveActionId: input.correctiveActionId,
        currentPersonId: currentPersonId
      });

      const storeEmployment = await ctx.prisma.storeEmployment.findFirstOrThrow({
        where: {
          storeId: input.storeId,
          employment: {
            personId: oldCaRow.recipientPersonId,
          }
        }
      });

      if (!perm.canCreateCorrectiveActions(checkAllowed, {
        businessId,
        storeEmploymentId: storeEmployment.id,
        storeId,
      })) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "You are not allowed to create corrective actions"
        });
      }

      const formalizedAt = new Date();
      const oldCa = formalizableCa.parse(hydrateCorrectiveAction(oldCaRow));
      const formalizedCa = formalizeCorrectiveAction(oldCa, {
        images: input.images,
        formalizedAt,
        reviewerPersonId: userPerson.person.id,
        incidentDescription: input.incidentDescription,
        actionType: input.actionType,
        witnesses: input.witnesses,
        consequencesDescription: input.consequencesDescription,
        policiesInAction: input.policiesInAction,
      });

      await ctx.db.correctiveAction.updateCorrectiveAction(businessId, oldCaRow.storeId, {
        ctx,
        oldCaRow: oldCaRow,
        oldCa: oldCa,
        newCa: formalizedCa,
        requestingUserId: ctx.auth.user.id,
        version: input.version ?? oldCaRow.version
      });

      // notify the recipient
      const recipientPerson = await ctx.prisma.person.findUniqueOrThrow({
        where: {
          id: oldCaRow.recipientPersonId
        },
        include: {
          user: {
            include: {
              devicePushTokens: true,
            }
          },
          notificationSettings: true,
        }
      });
      const store = await ctx.prisma.store.findUniqueOrThrow({
        where: {
          id: oldCaRow.storeId
        }
      });

      await notifyPerson({
        ctx,
        person: recipientPerson,
        subject: "Nation: Corrective Action",
        message: `${store.title} requires that you read a new Corrective Action. Open the Nation app to review.`,
        onError: (error) => Sentry.captureMessage(error, {
          extra: {
            correctiveActionId: oldCaRow.id,
            businessId,
            personId: recipientPerson.id
          }
        }),
        deduplicationId: null,
        code: NotificationCode.correctiveAction.read,
        entityIds: {
          correctiveActionId: oldCaRow.id,
          storeId: oldCaRow.storeId,
          recipientPersonId: recipientPerson.id
        }
      })

      // set the initDestination of the recipient's user to the corrective action screen
      await ctx.prisma.user.updateMany({
        where: {
          personId: recipientPerson.id
        },
        data: {
          initDestination: "dest_correctiveAction"
        }
      })
    }),

  acknowledgeCorrectiveAction: employedStatsProcedure({feature: "common"})
    .input(z.object({
      correctiveActionId: z.string(),
      recipientStatement: z.string().optional(),
      version: z.number().int().nonnegative().optional(),
    }))
    .output(z.void())
    .mutation(async ({ctx, input}) => {
      const {businessId, currentPersonId} = ctx;
      const userPerson = await getCurrentUser(ctx);

      const oldCaRow = await ctx.db.correctiveAction.getCorrectiveAction(businessId, ctx.storeIds, {
        checkAllowed: ctx.checkAllowed,
        correctiveActionId: input.correctiveActionId,
        currentPersonId: currentPersonId
      });

      // ensure the action belongs to the user acknowledging it (recipientPersonId)
      const row = await ctx.prisma.correctiveAction.findUniqueOrThrow({
        where: {
          id: input.correctiveActionId,
          businessId,
          recipientPersonId: userPerson.person.id
        },
        include: correctiveActionIncludesWithHistory
      });

      const unacknowledgedCa = unacknowledgedCorrectiveAction.parse(hydrateCorrectiveAction(row));
      const acknowledgedCa = acknowledgeCorrectiveAction(unacknowledgedCa, {
        acknowledgedAt: new Date(),
        recipientStatement: input.recipientStatement
      });

      await ctx.db.correctiveAction.updateCorrectiveAction(businessId, oldCaRow.storeId, {
        ctx,
        oldCaRow,
        oldCa: unacknowledgedCa,
        newCa: acknowledgedCa,
        requestingUserId: ctx.auth.user.id,
        version: input.version ?? oldCaRow.version
      });

      // notify the person that created the CA
      const ca = await ctx.prisma.correctiveAction.findUniqueOrThrow({
        where: {
          id: input.correctiveActionId
        },
        include: {
          reviewerPerson: {
            include: {
              user: {
                select: {
                  devicePushTokens: true,
                }
              },
              notificationSettings: true,
            }
          }
        }
      });

      if (ca.reviewerPerson) {
        await notifyPerson({
          ctx,
          subject: "Nation: Corrective Action Acknowledged",
          person: ca.reviewerPerson,
          message: `${userPerson.person.firstName} ${userPerson.person.lastName} has acknowledged the Corrective Action you created on ${ca.formalizedAt?.toLocaleDateString()}.`,
          deduplicationId: null,
          code: NotificationCode.correctiveAction.acknowledged,
          entityIds: {
            storeId: ca.storeId,
            recipientPersonId: userPerson.person.id!,
            correctiveActionId: ca.id,
          }
        })
      } else {
        Sentry.captureMessage("No reviewerPerson found for corrective action after it was formalized. I don't know how this happened...", {
          extra: {
            correctiveActionId: input.correctiveActionId,
            businessId
          }
        })
      }
    }),

  archiveCorrectiveAction: employedStatsProcedure({feature: "common"})
    .input(z.object({
      correctiveActionId: z.string(),
      isArchived: z.boolean().optional().default(true),
      version: z.number().int().nonnegative().optional()
    }))
    .output(z.void())
    .mutation(async ({ctx, input}) => {
      // enforce BUSINESS or HR role
      const {businessId, currentPersonId, storeIds, checkAllowed} = ctx;
      // TODO use new permisisons
      const userPerson = await getCurrentUser(ctx);

      const oldCaRow = await ctx.db.correctiveAction.getCorrectiveAction(businessId, storeIds, {
        checkAllowed: checkAllowed,
        correctiveActionId: input.correctiveActionId,
        currentPersonId,
      });

      const ca = correctiveAction.parse(hydrateCorrectiveAction(oldCaRow));
      const updatedCA = input.isArchived
        ? archiveCorrectiveAction(ca, {archivedAt: new Date()})
        : unArchiveCorrectiveAction(ca)

      await ctx.db.correctiveAction.updateCorrectiveAction(businessId, oldCaRow.storeId, {
        ctx,
        oldCaRow: oldCaRow,
        oldCa: ca,
        newCa: updatedCA,
        requestingUserId: ctx.auth.user.id,
        version: input.version ?? oldCaRow.version
      });
    }),

  /**
   * Get the number of times each policy has been violated by a person in a store.
   * Returns a record of policy titles to number of violations in the past specified days.
   * Returns the number of coaching moments and corrective actions in the past specified days.
   */
  getPolicyViolationCountsNew: employedStatsProcedure({feature: "common"})
    .input(z.object({
      personId: z.string(),
      storeId: z.string(),
      pastDays: z.number(),
    }))
    .output(z.object({
      totalCoachingMoments: z.number(),
      totalCorrectiveActions: z.number(),
      correctiveActionPolicies: z.record(z.string(), z.number().int()),
      coachingMomentPolicies: z.record(z.string(), z.number().int()),
    }))
    .query(async ({ctx, input}) => {
      const {businessId, checkAllowed} = ctx;
      const storeId = toSecureStoreIdOrThrow(ctx, input.storeId);
      const person = await ctx.db.person.getPersonInBusiness(businessId, input.personId, {
        ...personWithJobAndImageIncludes.include,
        user: true
      });

      const personEmployment = getPersonMainEmployment(person.employments);
      if (!personEmployment) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Person not associated with your store."
        });
      }

      const personStoreEmployment = find(personEmployment.stores, store => store.storeId === input.storeId);
      if (!personStoreEmployment) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Person not associated with your store."
        });
      }

      const canListCA = canListCorrectiveActions(checkAllowed, {
        businessId,
        storeId,
        storeEmploymentId: personStoreEmployment.id,
        userId: person.user?.id,
      })

      // get all the corrective actions in the past {input.pastDays} days for this person if user has permission
      let correctiveActions: Prisma.CorrectiveAction[] = [];
      if (canListCA) {
        correctiveActions = await ctx.prisma.correctiveAction.findMany({
          where: {
            businessId,
            storeId: input.storeId,
            recipientPersonId: person.id,
            isArchived: false,
            OR: [
              {
                reviewedAt: {
                  gte: subDays(new UTCDate(), input.pastDays)
                },
              },
              {
                formalizedAt: {
                  gte: subDays(new UTCDate(), input.pastDays)
                },
              },
            ],
          },
        });
      }

      const maxSensitivityLevel = perm.getMaxAllowedPersonNoteSensitivityLevel(checkAllowed, {
        businessId,
        personId: input.personId,
        requestingPersonId: ctx.auth.user.person.id,
        storeId: storeId,
      });

      // get all the coaching moments in the past {input.pastDays} days for this person that the user has access to
      const coachingMoments = await ctx.prisma.entityNote.findMany({
        where: {
          businessId,
          storeId: input.storeId,
          personId: person.id,
          sensitivityLevel: {
            lte: maxSensitivityLevel
          },
          noteType: "coaching",
          isArchived: false,
          createdAt: {
            gte: subDays(new UTCDate(), input.pastDays)
          }
        },
      })

      // count the policy violations for each policy in action type
      const correctiveActionPolicies = flatMap(correctiveActions, ca => ca.policiesInAction);
      const coachingPolicies = flatMap(coachingMoments, ca => ca.policiesInAction);

      return {
        totalCoachingMoments: coachingMoments.length,
        totalCorrectiveActions: correctiveActions.length,
        correctiveActionPolicies: countBy(correctiveActionPolicies),
        coachingMomentPolicies: countBy(coachingPolicies),
      };
    }),

  getCorrectiveAction: employedStatsProcedure({feature: "common"})
    .input(z.object({
      correctiveActionId: z.string(),
    }))
    .output(correctiveActionDto)
    .query(async ({ctx, input}) => {
      const {checkAllowed} = ctx;
      const correctiveAction = await ctx.db.correctiveAction.getCorrectiveAction(
        ctx.businessId,
        ctx.storeIds,
        {
          checkAllowed,
          correctiveActionId: input.correctiveActionId,
          currentPersonId: ctx.currentPersonId,
          allowArchivedRecipient: true,
        });

      return toCorrectiveActionDto(ctx, correctiveAction);
    }),

  // get any corrective actions the current user has to acknowledge
  getUnacknowledgedCorrectiveActions: verifiedProcedure
    .output(z.array(correctiveActionDto))
    .query(async ({ctx, input}) => {
      const isEmployed = await isEmployedByBusiness(ctx);
      if (!isEmployed) {
        return [];
      }
      const businessId = isEmployed.businessId;
      const correctiveActions = await ctx.prisma.correctiveAction.findMany({
        where: {
          businessId,
          recipientPersonId: ctx.auth.user.person.id,
          isFormalized: true,
          isAcknowledged: false,
          isArchived: false,
        },
        include: correctiveActionIncludesWithHistory
      });

      return map(correctiveActions, ca => toCorrectiveActionDto(ctx, ca));
    }),

  getUnacknowledgedNotes: verifiedProcedure
    .output(z.array(personNoteDetailDto))
    .query(async ({ctx}) => {
      const isEmployed = isEmployedByBusiness(ctx);
      if (!isEmployed) {
        return [];
      }
      const businessId = isEmployed.businessId;
      const notes = await ctx.prisma.entityNote.findMany({
        where: {
          businessId,
          personId: ctx.auth.user.person.id,
          requiresAcknowledgement: true,
          isAcknowledged: false,
        },
        include: {
          createdByPerson: personWithJobAndImageIncludes,
          person: personWithJobAndImageIncludes,
          images: true,
          fromCorrectiveAction: {
            include: {
              ...correctiveActionIncludes,
            }
          },
          history: {
            include: {
              images: true,
              createdByPerson: personWithJobAndImageIncludes,
            }
          },
        }
      });

      return compact(map(notes, note => {
        const storeId = note.storeId ? toSecureStoreId(isEmployed, note.storeId) : undefined;
        if (note.storeId && !storeId) {
          return null;
        }
        const shouldIncludeFromCorrectiveAction = storeId
          && note.fromCorrectiveAction
          && doesCorrectiveActionPassPermissionChecks({
            checkAllowed: isEmployed.checkAllowed,
            correctiveAction: note.fromCorrectiveAction,
            businessId,
            storeIds: [storeId],
            requestingPersonId: ctx.auth.user.person.id,
          });
        const noteWithFromCorrectiveActionCheck = {
          ...note,
          fromCorrectiveAction: shouldIncludeFromCorrectiveAction?.isOk
            ? shouldIncludeFromCorrectiveAction.value
            : null,
        };
        return toPersonNoteDetailDto(ctx, noteWithFromCorrectiveActionCheck)
      }));
    }),

  getUnacknowledgedAnnouncements: verifiedProcedure
    .input(z.object({
      storeId: z.string(),
      // TODO remove after app update. There is no need to pass time in from the frontend; the server knows the current time also.
      endOfDay: z.date()
    }))
    .output(z.array(scheduleEventDto))
    .query(async ({ctx, input}) => {
      const isEmployed = isEmployedByBusiness(ctx);
      if (!isEmployed) {
        return [];
      }
      const storeId = toSecureStoreId(isEmployed, input.storeId);
      if (!storeId) {
        return [];
      }
      const {businessId, employment, checkAllowed} = isEmployed;

      const person = await ctx.db.person.getPersonInBusiness(businessId, employment?.personId, {user: true});
      const end = DateTime.now();
      // get at most the last 30 days of events, to avoid loading too many events into memory
      const start = DateTime.max(DateTime.fromJSDate(person.createdAt), end.minus({days: 30}));
      const range = {start: start.toJSDate(), end: end.toJSDate()};

      const scheduleAnnouncements = await findScheduleEventsForRange({
        ctx: ctx,
        storeId,
        businessId,
        range,
        prisma: ctx.prisma,
        checkAllowed,
        includeHrDocuments: true
      });

      const unacknowledgedAnnouncements = filter(scheduleAnnouncements, evt => {
        return Boolean(evt.requiresAcknowledgment && !includes(evt.personsAcknowledged?.map((p) => p.id), person?.id));
      });

      return unacknowledgedAnnouncements;
    }),

  acknowledgeNote: employedStatsProcedure({feature: "common"})
    .input(z.object({
      noteId: z.string(),
      recipientStatement: z.string().optional(),
    }))
    .output(z.void())
    .mutation(async ({ctx, input}) => {
      const {businessId} = throwIfNotEmployedByBusiness(ctx);

      const entityNote = await ctx.prisma.entityNote.findUniqueOrThrow({
        where: {
          id: input.noteId,
          businessId,
        },
        include: {
          createdByPerson: personWithJobAndImageIncludes,
          updatedBy: personWithJobAndImageIncludes,
          person: personWithJobAndImageIncludes,
          images: true,
          fromCorrectiveAction: {
            include: {
              ...correctiveActionIncludes,
            }
          },
          history: {
            include: {
              createdByPerson: personWithJobAndImageIncludes,
              person: personWithJobAndImageIncludes,
              images: true,
              fromCorrectiveAction: {
                include: {
                  ...correctiveActionIncludes,
                }
              },
            }
          },
        }
      });

      const historicalCopiedImages = await copyImagesInS3({
        noteId: entityNote.id,
        businessId,
        s3Client: ctx.s3Client,
        s3MediaBucket: ctx.s3MediaBucket,
        images: entityNote.images,
        folderName: "note",
      });

      await ctx.prisma.$transaction(async prisma => {
        // create historical record
        await ctx.prisma.entityNoteHistory.create({
          data: toEntityNoteHistoryCreateInput({
            note: entityNote,
            userId: ctx.auth.user.id,
            historicalCopiedImages,
          })
        });

        await ctx.prisma.entityNote.update({
          where: {
            id: input.noteId,
            businessId,
            personId: ctx.auth.user.person.id,
          },
          data: {
            version: genNextEntityNoteVersion(entityNote),
            isAcknowledged: true,
            acknowledgedAt: new Date(),
            recipientStatement: input.recipientStatement,
            updatedById: ctx.auth.user.person.id,
            updatedAt: new Date(),
          }
        });
      });
    }),

  getPersonAvailability: verifiedProcedure
    .input(z.object({
      storeId: z.string(),
    }))
    .output(z.object({
      approved: approvedPersonAvailability.optional(),
      declined: z.array(declinedPersonAvailability).optional(),
      approvedFuture: z.array(approvedPersonAvailability).optional(),
      pending: pendingPersonAvailability.optional(),
      draft: draftPersonAvailability.optional(),
      storeTitle: z.string(),
    }))
    .query(async ({ctx, input}) => {
      // const {businessId, employment} = throwIfNotEmployedByBusiness(ctx);
      // TODO permissisons: need to temporarily check for employment because we want to allow
      //                    defining availability during onboarding.
      const now = new Date();
      // Single query to fetch all relevant PersonAvailability records
      const availabilities = await ctx.prisma.personAvailability.findMany({
        where: {
          personId: ctx.auth.user.person.id,
          storeId: input.storeId,
          OR: [
            {isApproved: true}, // approved
            {isDeclined: true}, // declined
            { // pending
              isSubmitted: true,
              isDeclined: false,
              isCancelled: false,
              isApproved: false
            },
            { // draft
              isSubmitted: false,
              isDeclined: false,
              isCancelled: false,
              isApproved: false
            }
          ]
        },
        orderBy: [
          {approvedAt: 'desc'},
          {submittedAt: 'desc'},
          {createdAt: 'desc'}
        ],
        include: {
          PersonAvailabilityDayTimeRange: true
        }
      });

      const store = await ctx.prisma.store.findFirstOrThrow({
        where: {
          id: input.storeId,
        }
      })

      // Categorize the results
      const approved = availabilities.filter(a => a.isApproved);
      const declined = availabilities.filter(a => a.isDeclined);
      const current = approved.find(a => a.effectiveAt <= now);
      const future = approved.filter(a => a.effectiveAt > now);
      const pending = availabilities.find(a => a.isSubmitted && !a.isDeclined && !a.isCancelled && !a.isApproved);
      const draft = availabilities.find(a => !a.isSubmitted && !a.isDeclined && !a.isCancelled && !a.isApproved);

      return {
        approved: current ? toApprovedPersonAvailability(current) : undefined,
        approvedFuture: future.length ? map(future, toApprovedPersonAvailability) : undefined,
        declined: declined.length ? map(declined, toDeclinedPersonAvailability) : undefined,
        pending: pending ? toPendingPersonAvailability(pending) : undefined,
        draft: draft ? toDraftPersonAvailability(draft) : undefined,
        storeTitle: store.title,
      };
    }),

  getPersonAvailabilityAdmin: employedStatsProcedure({feature: "common"})
    .input(z.object({
      storeId: z.string(),
      personId: z.string()
    }))
    .output(z.object({
      approved: approvedPersonAvailability.optional(),
      declined: z.array(declinedPersonAvailability).optional(),
      approvedFuture: z.array(approvedPersonAvailability).optional(),
      pending: pendingPersonAvailability.optional(),
      draft: draftPersonAvailability.optional(),
      storeTitle: z.string(),
    }))
    .query(async ({ctx, input}) => {
      const {businessId, employment} = throwIfNotEmployedByBusiness(ctx);

      const checkAllowed = isAllowedFactory({
        businessId,
        principalEmployment: employment,
        userId: ctx.auth.user.id,
      });
      if (!checkAllowed({
        action: "store/listAvailabilityRequests",
        resource: `business/${businessId}/store/${input.storeId}`,
        resourceEntity: {
          storeId: input.storeId,
        }
      }).isAllowed) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "You are not authorized to view this availability request."
        });
      }

      const now = new Date();
      // Single query to fetch all relevant PersonAvailability records
      const availabilities = await ctx.prisma.personAvailability.findMany({
        where: {
          personId: input.personId,
          storeId: input.storeId,
          OR: [
            {isApproved: true}, // approved
            {isDeclined: true}, // declined
            { // pending
              isSubmitted: true,
              isDeclined: false,
              isCancelled: false,
              isApproved: false
            },
            { // draft
              isSubmitted: false,
              isDeclined: false,
              isCancelled: false,
              isApproved: false
            }
          ]
        },
        orderBy: [
          {approvedAt: 'desc'},
          {submittedAt: 'desc'},
          {createdAt: 'desc'}
        ],
        include: {
          PersonAvailabilityDayTimeRange: true
        }
      });

      const store = await ctx.prisma.store.findFirstOrThrow({
        where: {
          id: input.storeId,
        }
      })

      // Categorize the results
      const approved = availabilities.filter(a => a.isApproved);
      const declined = availabilities.filter(a => a.isDeclined);
      const current = approved.find(a => a.effectiveAt <= now);
      const future = approved.filter(a => a.effectiveAt > now);
      const pending = availabilities.find(a => a.isSubmitted && !a.isDeclined && !a.isCancelled && !a.isApproved);
      const draft = availabilities.find(a => !a.isSubmitted && !a.isDeclined && !a.isCancelled && !a.isApproved);

      return {
        approved: current ? toApprovedPersonAvailability(current) : undefined,
        approvedFuture: future.length ? map(future, toApprovedPersonAvailability) : undefined,
        declined: declined.length ? map(declined, toDeclinedPersonAvailability) : undefined,
        pending: pending ? toPendingPersonAvailability(pending) : undefined,
        draft: draft ? toDraftPersonAvailability(draft) : undefined,
        storeTitle: store.title,
      };
    }),

  /**
   * Create a new availability change request.
   */
  createPersonAvailability: verifiedProcedure
    .input(z.object({
      id: z.string(),
      storeId: z.string(),
      effectiveAt: dateNoTime,
      maxHoursPreferred: maxHoursPreferredInt.optional(),
      maxDaysPreferred: maxDaysPreferredInt.optional(),
    }))
    .output(z.void())
    .mutation(async ({ctx, input}) => {
      // const {businessId, employment} = throwIfNotEmployedByBusiness(ctx);
      // TODO permissisons: need to temporarily check for employment because we want to allow
      //                    defining availability during onboarding.


      const humanId = await ctx.prisma.personAvailability.count({
        where: {
          storeId: input.storeId,
        },
      });

      const store = await ctx.prisma.store.findUniqueOrThrow({
        where: {
          id: input.storeId
        },
        select: {
          timezone: true
        }
      });

      const personAvailability = createPersonAvailability({
        personId: ctx.auth.user.person.id,
        humanId: humanId + 1,
        id: input.id,
        storeId: input.storeId,
        effectiveAt: input.effectiveAt,
        maxHoursPreferred: input.maxHoursPreferred,
        maxDaysPreferred: input.maxDaysPreferred,
      });

      const mostRecentApproved = await ctx.prisma.personAvailability.findFirst({
        where: {
          personId: ctx.auth.user.person.id,
          storeId: input.storeId,
          isApproved: true,
        },
        orderBy: {
          approvedAt: 'desc',
        },
        include: {
          PersonAvailabilityDayTimeRange: true,
        },
      });

      let populatedAvailability = personAvailability;

      if (mostRecentApproved) {
        const approvedAvailability = toApprovedPersonAvailability(mostRecentApproved);

        for (const range of approvedAvailability.ranges) {
          const day = range.dayOfWeek;
          const timeRange = {
            start: range.start,
            end: range.end,
          };
          populatedAvailability = setDayRanges(populatedAvailability, [timeRange], new Set([day]));
        }
      }

      await ctx.prisma.personAvailability.create({
        data: toDbPersonAvailabilityCreateInput(populatedAvailability, store.timezone),
      });
    }),

  /**
   * Delete an availability change request.
   */
  deletePersonAvailability: verifiedProcedure
    .input(z.object({
      id: z.string(),
      storeId: z.string(),
    }))
    .output(z.void())
    .mutation(async ({ctx, input}) => {
      // const {businessId, employment} = throwIfNotEmployedByBusiness(ctx);

      await ctx.prisma.personAvailability.delete({
        where: {
          id: input.id,
          storeId: input.storeId,
          personId: ctx.auth.user.person.id,
        },
      });
    }),

  /**
   * Create a new availability change request for a team member.
   */
  createPersonAvailabilityAdmin: employedStatsProcedure({feature: "common"})
    .input(z.object({
      id: z.string(),
      personId: z.string(),
      storeId: z.string(),
      effectiveAt: dateNoTime,
      maxHoursPreferred: maxHoursPreferredInt.optional(),
      maxDaysPreferred: maxDaysPreferredInt.optional(),
    }))
    .output(z.void())
    .mutation(async ({ctx, input}) => {
      const {businessId, employment} = throwIfNotEmployedByBusiness(ctx);

      throwIfDenied({
        ctx, principalEmployment: employment,
        action: "store/createAvailabilityRequestForOthers",
        resource: `business/${businessId}/store/${input.storeId}`,
        resourceEntity: {
          storeId: input.storeId,
        }
      });

      const humanId = await ctx.prisma.personAvailability.count({
        where: {
          storeId: input.storeId,
        },
      });

      const store = await ctx.prisma.store.findUniqueOrThrow({
        where: {
          id: input.storeId
        },
        select: {
          timezone: true
        }
      });

      const personAvailability = createPersonAvailability({
        personId: input.personId,
        humanId: humanId + 1,
        id: input.id,
        storeId: input.storeId,
        effectiveAt: input.effectiveAt,
        maxHoursPreferred: input.maxHoursPreferred,
        maxDaysPreferred: input.maxDaysPreferred,
      });

      const checkAllowed = isAllowedFactory({
        businessId,
        principalEmployment: employment,
        userId: ctx.auth.user.id,
      });
      if (!checkAllowed({
        action: "store/listAvailabilityRequests",
        resource: `business/${businessId}/store/${input.storeId}`,
        resourceEntity: {
          storeId: input.storeId,
        }
      }).isAllowed) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "You are not authorized to view this availability request."
        });
      }

      const mostRecentApproved = await ctx.prisma.personAvailability.findFirst({
        where: {
          personId: input.personId,
          storeId: input.storeId,
          isApproved: true,
        },
        orderBy: {
          approvedAt: 'desc',
        },
        include: {
          PersonAvailabilityDayTimeRange: true,
        },
      });

      let populatedAvailability = personAvailability;

      if (mostRecentApproved) {
        for (const range of mostRecentApproved.PersonAvailabilityDayTimeRange) {
          const day = range.dayOfWeek;
          const timeRange = {
            start: DateTime.fromJSDate(range.start, {zone: 'utc'}).toFormat('HH:mm'),
            end: DateTime.fromJSDate(range.end, {zone: 'utc'}).toFormat('HH:mm'),
          };

          populatedAvailability = setDayRanges(populatedAvailability, [timeRange], new Set([day]));
        }
      }

      await ctx.prisma.personAvailability.create({
        data: toDbPersonAvailabilityCreateInput(populatedAvailability, store.timezone),
      });
    }),

  setPersonAvailabilityDayRanges: verifiedProcedure
    .input(z.object({
      id: z.string(),
      ranges: z.array(dailyTimeRange),
      days: z.set(dayOfWeek)
    }))
    .output(z.void())
    .mutation(async ({ctx, input}) => {
      // const {businessId, employment} = throwIfNotEmployedByBusiness(ctx);
      // TODO permissisons: need to temporarily check for employment because we want to allow
      //                    defining availability during onboarding.

      const row = await ctx.prisma.personAvailability.findUniqueOrThrow({
        where: {
          id: input.id,
          personId: ctx.auth.user.person.id
        },
        include: {
          PersonAvailabilityDayTimeRange: true,
          store: {
            select: {
              timezone: true
            }
          }
        }
      });
      const personAvailability = toEditablePersonAvailability(row);
      const updatedPersonAvailability = setDayRanges(personAvailability, input.ranges, input.days);

      await ctx.prisma.personAvailability.update({
        where: {
          id: input.id,
          personId: ctx.auth.user.person.id
        },
        data: toDbPersonAvailabilityUpdateInput(updatedPersonAvailability, row.store.timezone)
      });
    }),

  setPersonAvailabilityDayRangesAdmin: employedStatsProcedure({feature: "common"})
    .input(z.object({
      id: z.string(),
      personId: z.string(),
      storeId: z.string(),
      ranges: z.array(dailyTimeRange),
      days: z.set(dayOfWeek)
    }))
    .output(z.void())
    .mutation(async ({ctx, input}) => {
      const {businessId, employment} = throwIfNotEmployedByBusiness(ctx);

      throwIfDenied({
        ctx, principalEmployment: employment,
        action: "store/createAvailabilityRequestForOthers",
        resource: `business/${businessId}/store/${input.storeId}`,
        resourceEntity: {
          storeId: input.storeId,
        }
      });

      const row = await ctx.prisma.personAvailability.findUniqueOrThrow({
        where: {
          id: input.id,
          personId: input.personId,
        },
        include: {
          PersonAvailabilityDayTimeRange: true,
          store: {
            select: {
              timezone: true
            }
          }
        }
      });

      const personAvailability = toPersonAvailability(row);
      const updatedPersonAvailability = setDayRanges(personAvailability, input.ranges, input.days);

      await ctx.prisma.personAvailability.update({
        where: {
          id: input.id,
          personId: input.personId,
        },
        data: toDbPersonAvailabilityUpdateInput(updatedPersonAvailability, row.store.timezone)
      });
    }),

  submitPersonAvailabilityWeek: verifiedProcedure
    .input(z.object({
      id: z.string(),
      storeId: z.string(),
      reason: z.string().max(256).optional(),
      effectiveAt: dateNoTime,
      maxHoursPreferred: maxHoursPreferredInt.optional(),
      maxDaysPreferred: maxDaysPreferredInt.optional(),
    }))
    .output(z.void())
    .mutation(async ({ctx, input}) => {
      const isEmployed = await isEmployedByBusiness(ctx);

      if (isEmployed) {
        const {employment, businessId} = isEmployed;

        const checkAllowed = isAllowedFactory({
          businessId,
          principalEmployment: employment,
          userId: ctx.auth.user.id,
        });

        const submitterCanBypassAvailabilityRestriction = checkAllowed({
          action: "store/bypassAvailabilityRestriction",
          resource: `business/${businessId}/store/${input.storeId}`,
          resourceEntity: {
            storeId: input.storeId
          }
        }).isAllowed;

        const store = await ctx.prisma.store.findUniqueOrThrow({
          where: {
            id: input.storeId,
          },
          select: {
            timezone: true,
            availabilityRestrictionWeekday: true,
            availabilityRestrictionTime: true,
            availabilityRestrictionWindowDurationHours: true
          }
        });

        const timezone = store.timezone ?? "America/New_York";
        const earliestValidStartTime = getEarliestValidAvailabilityRequestStartTime({
          now: DateTime.fromJSDate(new Date(), {zone: timezone}),
          ...hydrateAvailabilityRestrictions(store),
          timezone: timezone,
        });

        const effectiveDateDt = DateTime.fromFormat(input.effectiveAt, 'yyyy-MM-dd', {zone: timezone});
        const isBeforeValidStartTime = effectiveDateDt.toMillis() < earliestValidStartTime.toMillis();

        if (isBeforeValidStartTime && !submitterCanBypassAvailabilityRestriction) {
          throw new TRPCError({
            code: "BAD_REQUEST",
            message: `You cannot request availability in this period because it conflicts with store policy. Requested availability must start later than ${earliestValidStartTime.toLocaleString(DateTime.DATETIME_MED_WITH_WEEKDAY)}`
          });
        }
      }

      const row = await ctx.prisma.personAvailability.findUniqueOrThrow({
        where: {
          id: input.id,
          personId: ctx.auth.user.person.id
        },
        include: {
          PersonAvailabilityDayTimeRange: true,
          store: {
            select: {
              timezone: true
            }
          }
        }
      });
      const personAvailability = toEditablePersonAvailability(row);

      const otherRequestRows = await ctx.prisma.personAvailability.findMany({
        where: {
          personId: ctx.auth.user.person.id,
          storeId: input.storeId,
          // pending
          isSubmitted: true,
          isDeclined: false,
          isCancelled: false,
          isApproved: false
        },
        include: {
          PersonAvailabilityDayTimeRange: true
        }
      });
      const otherRequests = filter(map(otherRequestRows, row => toPendingPersonAvailability(row)), req => req.id !== input.id);

      const updatedPersonAvailability = submitPersonAvailability(personAvailability, {
        maxHoursPreferred: input.maxHoursPreferred,
        maxDaysPreferred: input.maxDaysPreferred,
        effectiveAt: input.effectiveAt,
        submittedAt: utcNow(),
        submittedByPersonId: ctx.auth.user.person.id,
        submittedReason: input.reason ?? undefined,
        otherRequests: otherRequests
      });

      await ctx.prisma.personAvailability.update({
        where: {
          id: input.id,
          personId: ctx.auth.user.person.id
        },
        data: toDbPersonAvailabilityUpdateInput(updatedPersonAvailability, row.store.timezone)
      });
    }),

  unSubmitPersonAvailability: employedStatsProcedure({feature: "common"})
    .input(z.object({
      id: z.string(),
      storeId: z.string(),
    }))
    .output(z.void())
    .mutation(async ({ctx, input}) => {
      throwIfNotEmployedByBusiness(ctx);

      const row = await ctx.prisma.personAvailability.findUniqueOrThrow({
        where: {
          id: input.id,
          storeId: input.storeId,
          personId: ctx.auth.user.person.id,
          isSubmitted: true,
          isApproved: false,
          isDeclined: false,
          isCancelled: false
        },
        include: {
          PersonAvailabilityDayTimeRange: true,
          store: {
            select: {
              timezone: true
            }
          }
        }
      });

      const personAvailability = toPendingPersonAvailability(row);
      const unsubmittedAvailability = unsubmitPersonAvailability(personAvailability);

      await ctx.prisma.personAvailability.update({
        where: {
          id: input.id,
          personId: ctx.auth.user.person.id,
        },
        data: toDbPersonAvailabilityUpdateInput(unsubmittedAvailability, row.store.timezone)
      });
    }),

  submitPersonAvailabilityWeekAdmin: employedStatsProcedure({feature: "common"})
    .input(z.object({
      id: z.string(),
      personId: z.string(),
      storeId: z.string(),
      effectiveAt: dateNoTime,
      maxHoursPreferred: maxHoursPreferredInt.optional(),
      maxDaysPreferred: maxDaysPreferredInt.optional(),
    }))
    .output(z.void())
    .mutation(async ({ctx, input}) => {
      const {businessId, employment} = throwIfNotEmployedByBusiness(ctx);

      throwIfDenied({
        ctx, principalEmployment: employment,
        action: "store/createAvailabilityRequestForOthers",
        resource: `business/${businessId}/store/${input.storeId}`,
        resourceEntity: {
          storeId: input.storeId,
        }
      });

      const row = await ctx.prisma.personAvailability.findUniqueOrThrow({
        where: {
          id: input.id,
          personId: input.personId,
        },
        include: {
          PersonAvailabilityDayTimeRange: true,
          store: {
            select: {
              timezone: true
            }
          }
        }
      });
      const personAvailability = toDraftPersonAvailability(row);

      const otherRequestRows = await ctx.prisma.personAvailability.findMany({
        where: {
          personId: input.personId,
          storeId: input.storeId,
          // pending
          isSubmitted: true,
          isDeclined: false,
          isCancelled: false,
          isApproved: false
        },
        include: {
          PersonAvailabilityDayTimeRange: true
        }
      });
      const otherRequests = map(otherRequestRows, row => toPendingPersonAvailability(row));

      const updatedPersonAvailability = submitPersonAvailability(personAvailability, {
        maxHoursPreferred: input.maxHoursPreferred,
        maxDaysPreferred: input.maxDaysPreferred,
        effectiveAt: input.effectiveAt,
        submittedAt: utcNow(),
        submittedByPersonId: ctx.auth.user.person.id,
        submittedReason: undefined,
        otherRequests: otherRequests
      });

      await ctx.prisma.personAvailability.update({
        where: {
          id: input.id,
          personId: input.personId,
        },
        data: toDbPersonAvailabilityUpdateInput(updatedPersonAvailability, row.store.timezone)
      });
    }),

  // scheduler availability APIs
  getAvailabilityRequests: employedStatsProcedure({feature: "common"})
    .input(z.object({
      storeId: z.string(),
      status: availabilityStatusFilter.default("all")
    }))
    .output(z.object({
      items: z.array(personAvailabilityDto)
    }))
    .query(async ({ctx, input}) => {
      const {businessId, employment} = throwIfNotEmployedByBusiness(ctx);

      const checkAllowed = isAllowedFactory({
        businessId,
        principalEmployment: employment,
        userId: ctx.auth.user.id,
      });

      const canList = checkAllowed({
        action: "store/listAvailabilityRequests",
        resource: `business/${businessId}/store/${input.storeId}`,
        resourceEntity: {
          storeId: input.storeId,
        }
      }).isAllowed;

      if (!canList) {
        return {items: []}
      }

      const requests = await ctx.prisma.personAvailability.findMany({
        where: {
          storeId: input.storeId,
          ...getPersonAvailabilityStatusWhereCondition(input.status),
        },
        include: {
          person: personWithJobAndImageIncludes,
          PersonAvailabilityDayTimeRange: true,
          approvedByPerson: personWithJobAndImageIncludes,
          declinedByPerson: personWithJobAndImageIncludes,
          submittedByPerson: personWithJobAndImageIncludes,
          cancelledByPerson: personWithJobAndImageIncludes,
        },
        orderBy: {
          submittedAt: "desc"
        }
      });

      return {
        items: map(requests, row => toPersonAvailabilityDto(ctx, row))
      }
    }),

  // scheduler availability APIs
  getAvailabilityRequestsAdvanced: employedStatsProcedure({feature: "common"})
    .input(z.object({
      storeId: z.string(),
      status: availabilityStatusFilter.default("all"),
      pastDays: z.number().int().min(0).max(365).optional().default(0),
      dateRange: dateTimeRange.optional(),
    }))
    .output(z.object({
      items: z.array(personAvailabilityDto)
    }))
    .query(async ({ctx, input}) => {
      const {businessId, employment} = throwIfNotEmployedByBusiness(ctx);

      const checkAllowed = isAllowedFactory({
        businessId,
        principalEmployment: employment,
        userId: ctx.auth.user.id,
      });

      const canList = checkAllowed({
        action: "store/listAvailabilityRequests",
        resource: `business/${businessId}/store/${input.storeId}`,
        resourceEntity: {
          storeId: input.storeId,
        }
      }).isAllowed;

      if (!canList) {
        return {items: []}
      }

      const historyStartDate = input.pastDays ? subDays(utcNow(), input.pastDays) : undefined;
      const searchQuery = input.dateRange !== undefined ? {
        AND: [
          {effectiveAtAbs: {gte: input.dateRange.start}},
          {effectiveAtAbs: {lte: input.dateRange.end}}
        ]
      } : historyStartDate
        ? {submittedAt: {gte: historyStartDate}}
        : undefined;

      const requests = await ctx.prisma.personAvailability.findMany({
        where: {
          storeId: input.storeId,
          ...getPersonAvailabilityStatusWhereCondition(input.status),
          ...searchQuery,
        },
        include: {
          person: personWithJobAndImageIncludes,
          PersonAvailabilityDayTimeRange: true,
          approvedByPerson: personWithJobAndImageIncludes,
          declinedByPerson: personWithJobAndImageIncludes,
          submittedByPerson: personWithJobAndImageIncludes,
          cancelledByPerson: personWithJobAndImageIncludes,
        },
        orderBy: {
          submittedAt: "desc"
        }
      });

      return {
        items: map(requests, row => toPersonAvailabilityDto(ctx, row))
      }
    }),

  /**
   * TODO:  This is being deprecated.  Use getPersonApprovedAvailability instead.
   * When getPersonApprovedAvailability is fully in use, remove this.
   */
  getApprovedAvailability: employedStatsProcedure({feature: "common"})
    .input(z.object({
      storeId: z.string(),
      personId: z.string(),
    }))
    .output(approvedPersonAvailability.optional())
    .query(async ({ctx, input}) => {
      const {businessId, employment} = throwIfNotEmployedByBusiness(ctx);

      const checkAllowed = isAllowedFactory({
        businessId,
        principalEmployment: employment,
        userId: ctx.auth.user.id,
      });
      if (!checkAllowed({
        action: "store/listAvailabilityRequests",
        resource: `business/${businessId}/store/${input.storeId}`,
        resourceEntity: {
          storeId: input.storeId,
        }
      }).isAllowed) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "You are not authorized to view this availability request."
        });
      }

      const now = new Date();

      // Find the most recently approved availability before current time
      const availability = await ctx.prisma.personAvailability.findFirst({
        where: {
          personId: input.personId,
          storeId: input.storeId,
          isApproved: true,
          effectiveAt: {
            lte: now
          },
        },
        orderBy: {
          approvedAt: 'desc'
        },
        include: {
          PersonAvailabilityDayTimeRange: true
        }
      });

      if (!availability) {
        return undefined;
      }

      return toApprovedPersonAvailability(availability);
    }),

  getPersonApprovedAvailability: employedStatsProcedure({feature: "common"})
    .input(z.object({
      storeId: z.string(),
      personId: z.string(),
    }))
    .output(z.object({
      availability: approvedPersonAvailability.optional()
    }))
    .query(async ({ctx, input}) => {
      const {businessId, employment} = throwIfNotEmployedByBusiness(ctx);

      const checkAllowed = isAllowedFactory({
        businessId,
        principalEmployment: employment,
        userId: ctx.auth.user.id,
      });
      if (!checkAllowed({
        action: "store/listAvailabilityRequests",
        resource: `business/${businessId}/store/${input.storeId}`,
        resourceEntity: {
          storeId: input.storeId,
        }
      }).isAllowed) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "You are not authorized to view this availability request."
        });
      }

      const now = new Date();

      // Find the most recently approved availability before current time
      const availability = await ctx.prisma.personAvailability.findFirst({
        where: {
          personId: input.personId,
          storeId: input.storeId,
          isApproved: true,
          effectiveAt: {
            lte: now
          },
        },
        orderBy: {
          approvedAt: 'desc'
        },
        include: {
          PersonAvailabilityDayTimeRange: true
        }
      });

      if (!availability) {
        return {
          availability: undefined
        };
      }

      return {
        availability: toApprovedPersonAvailability(availability)
      };
    }),

  getLatestPendingAvailabilityRequestDetails: employedStatsProcedure({feature: "common"})
    .input(z.object({
      storeId: z.string(),
      personId: z.string(),
    }))
    .output(z.object({
      request: personAvailabilityDto.optional(),
      timezone: z.string().optional(),
    }))
    .query(async ({ctx, input}) => {
      const {businessId, employment} = throwIfNotEmployedByBusiness(ctx);
      const latestPendingAvailabilityRequest = await ctx.prisma.personAvailability.findFirst({
        where: {
          personId: input.personId,
          storeId: input.storeId,
          ...getPersonAvailabilityStatusWhereCondition("pending")
        },
        orderBy: {
          submittedAt: "desc"
        },
        take: 1,
        select: {
          id: true,
        }
      });

      if (!latestPendingAvailabilityRequest) {
        return {};
      }

      try {
        const request = await getAvailabilityRequestDetails({
          ctx, personAvailabilityId: latestPendingAvailabilityRequest.id,
          businessId, requesterEmployment: employment
        });

        return {
          request: toPersonAvailabilityDto(ctx, request),
          timezone: request.store.timezone ?? undefined
        };
      } catch (e) {
        if (e instanceof TRPCError && e.code === "FORBIDDEN") {
          return {};
        }
        throw e;
      }
    }),

  // get request details -- return the latest approved along with the submitted so the UI can diff them
  getAvailabilityRequestDetails: employedStatsProcedure({feature: "common"})
    .input(z.object({
      personAvailabilityId: z.string(),
    }))
    .output(z.object({
      approved: personAvailabilityDto.optional(),
      request: personAvailabilityDto,
      lastRequests: z.array(personAvailabilityDto),
      conflictingShifts: z.array(shiftConflictDto),
      timezone: z.string().optional(),
    }))
    .query(async ({ctx, input}) => {
      const {businessId, employment} = throwIfNotEmployedByBusiness(ctx);
      const request = await getAvailabilityRequestDetails({
        ctx, personAvailabilityId: input.personAvailabilityId,
        businessId, requesterEmployment: employment
      });

      // get latest approved before the current time
      const approved = await ctx.prisma.personAvailability.findFirst({
        where: {
          personId: request.personId,
          isApproved: true,
          effectiveAt: {
            lte: new Date()
          },
          storeId: request.storeId,
        },
        orderBy: {
          approvedAt: "desc"
        },
        include: {
          person: personWithJobAndImageIncludes,
          PersonAvailabilityDayTimeRange: true,
          approvedByPerson: personWithJobAndImageIncludes,
          declinedByPerson: personWithJobAndImageIncludes,
          submittedByPerson: personWithJobAndImageIncludes,
          cancelledByPerson: personWithJobAndImageIncludes,
        }
      });

      // the 5 most recent  requests for this person at this store
      const lastRequestRows = await ctx.prisma.personAvailability.findMany({
        where: {
          id: {not: request.id},
          personId: request.personId,
          storeId: request.storeId,
          isSubmitted: true,
        },
        orderBy: {
          submittedAt: "desc"
        },
        take: 5,
        include: {
          person: personWithJobAndImageIncludes,
          PersonAvailabilityDayTimeRange: true,
          approvedByPerson: personWithJobAndImageIncludes,
          declinedByPerson: personWithJobAndImageIncludes,
          submittedByPerson: personWithJobAndImageIncludes,
          cancelledByPerson: personWithJobAndImageIncludes,
        }
      });
      const lastRequests = map(lastRequestRows, row => toPersonAvailabilityDto(ctx, row));

      let conflictingShifts: ShiftConflictDto[] = [];
      if (getAvailabilityRequestStatus(request) === "Pending") {
        const pendingRequest = toPendingPersonAvailability(request);
        const pretendApprovedRequest = approvePersonAvailability(pendingRequest, {
          approvedAt: utcNow(),
          approvedByPersonId: ctx.auth.user.person.id,
          approvedReason: "Pretend approved for conflict check"
        });
        const approvedRequest = approved ? toApprovedPersonAvailability(approved) : undefined;
        conflictingShifts = await queryShiftConflicts({
          prisma: ctx.prisma,
          personId: request.personId,
          isConflict: ({schedRev: sched, shift, day}) => {
            // get what the person's effective availability would be if this request were approved
            const {availability: effectiveAvailability} = getEffectiveAvailability(compact([approvedRequest, pretendApprovedRequest]), sched.published!.week, request.store.timezone);

            return !isWithinAvailability(shift.range, day.dayOfWeek, effectiveAvailability);
          },
          onOrAfter: new UTCDate(dateStrToDbDate(pretendApprovedRequest.effectiveAt)),
          timezone: request.store.timezone,
        });
      }

      return {
        approved: approved ? toPersonAvailabilityDto(ctx, approved) : undefined,
        request: toPersonAvailabilityDto(ctx, request),
        lastRequests: lastRequests,
        conflictingShifts: conflictingShifts,
        timezone: request.store.timezone ?? undefined
      };
    }),

  // approve request
  approveAvailabilityRequest: employedStatsProcedure({feature: "common"})
    .input(z.object({
      personAvailabilityId: z.string(),
      reason: z.string().optional(),
    }))
    .output(z.void())
    .mutation(async ({ctx, input}) => {
      const {businessId, employment} = throwIfNotEmployedByBusiness(ctx);
      const row = await ctx.prisma.personAvailability.findUniqueOrThrow({
        where: {
          id: input.personAvailabilityId
        },
        include: {
          PersonAvailabilityDayTimeRange: true,
          person: {
            include: {
              user: {
                select: {
                  devicePushTokens: true,
                }
              },
              notificationSettings: true,
            }
          },
          store: true
        }
      });

      const checkAllowed = isAllowedFactory({
        businessId,
        principalEmployment: employment,
        userId: ctx.auth.user.id,
      });

      const hasPermission = checkAllowed({
        action: "store/approveAvailabilityRequest",
        resource: `business/${businessId}/store/${row.storeId}`,
        resourceEntity: {
          storeId: row.storeId,
        }
      }).isAllowed;
      if (!hasPermission) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "You are not authorized to approve this availability request."
        });
      }

      const personAvailability = toPendingPersonAvailability(row);
      const updatedPersonAvailability = approvePersonAvailability(personAvailability, {
        approvedAt: utcNow(),
        approvedByPersonId: ctx.auth.user.person.id,
        approvedReason: input.reason
      });

      await ctx.prisma.personAvailability.update({
        where: {
          id: input.personAvailabilityId
        },
        data: toDbPersonAvailabilityUpdateInput(updatedPersonAvailability, row.store.timezone)
      });

      // notify team member
      await notifyPerson({
        ctx,
        message: `Nation: Your availability request in ${row.store.title} has been approved.`,
        subject: "Nation: Availability approved",
        person: row.person,
        deduplicationId: null,
        code: NotificationCode.availability.approved,
        entityIds: {
          storeId: row.storeId,
          availabilityId: input.personAvailabilityId,
        }
      })
    }),

  // decline request
  declineAvailabilityRequest: employedStatsProcedure({feature: "common"})
    .input(z.object({
      personAvailabilityId: z.string(),
      reason: z.string().optional()
    }))
    .output(z.void())
    .mutation(async ({ctx, input}) => {
      const {businessId, employment} = throwIfNotEmployedByBusiness(ctx);
      const row = await ctx.prisma.personAvailability.findUniqueOrThrow({
        where: {
          id: input.personAvailabilityId
        },
        include: {
          PersonAvailabilityDayTimeRange: true,
          person: {
            include: {
              user: {
                select: {
                  devicePushTokens: true,
                }
              },
              notificationSettings: true,
            }
          },
          store: true
        }
      });

      const checkAllowed = isAllowedFactory({
        businessId,
        principalEmployment: employment,
        userId: ctx.auth.user.id,
      });

      const hasPermission = checkAllowed({
        action: "store/approveAvailabilityRequest",
        resource: `business/${businessId}/store/${row.storeId}`,
        resourceEntity: {
          storeId: row.storeId,
        }
      }).isAllowed;
      if (!hasPermission) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "You are not authorized to decline this availability request."
        });
      }

      const personAvailability = toPendingPersonAvailability(row);
      const updatedPersonAvailability = declinePersonAvailability(personAvailability, {
        declinedAt: utcNow(),
        declinedByPersonId: ctx.auth.user.person.id,
        declinedReason: input.reason
      });

      await ctx.prisma.personAvailability.update({
        where: {
          id: input.personAvailabilityId
        },
        data: toDbPersonAvailabilityUpdateInput(updatedPersonAvailability, row.store.timezone)
      });

      // notify team member
      const message = `Nation: Your availability request in ${row.store.title} has been declined. Open the Nation app to review.`;
      await notifyPerson({
        ctx,
        message,
        subject: "Nation: Availability declined",
        person: row.person,
        deduplicationId: null,
        code: NotificationCode.availability.declined,
        entityIds: {
          storeId: row.storeId,
          availabilityId: input.personAvailabilityId,
        }
      })
    }),

  updateStoreHours: employedStatsProcedure({feature: "common"})
    .input(z.object({
      storeId: z.string(),
      hours: dailyTimeRange,
    }))
    .output(z.void())
    .mutation(async ({ctx, input}) => {
      const {businessId, employment} = throwIfNotEmployedByBusiness(ctx);
      const checkAllowed = isAllowedFactory({businessId, principalEmployment: employment, userId: ctx.auth.user.id})
      allAllowedOrThrow({
        checkAllowed,
        actions: ["store/update"],
        resource: `business/${businessId}/store/${input.storeId}`,
        resourceEntity: {
          businessId: businessId,
          storeId: input.storeId
        }
      });

      await ctx.prisma.store.update({
        where: {
          businessId,
          id: input.storeId,
        },
        data: {
          storeHoursStart: timeToDbTime(input.hours.start),
          storeHoursEnd: timeToDbTime(input.hours.end),
        }
      })
    }),

  updateStoreDayParts: employedStatsProcedure({feature: "common"})
    .input(z.object({
      storeId: z.string(),
      dayParts: dayParts,
    }))
    .output(z.void())
    .mutation(async ({ctx, input}) => {
      const {businessId, employment} = throwIfNotEmployedByBusiness(ctx);
      // TODO permissions

      await ctx.prisma.store.update({
        where: {
          businessId,
          id: input.storeId,
        },
        data: {
          dayParts: input.dayParts
        }
      })
    }),

  updateStoreSchedulingSettings: employedStatsProcedure({feature: "common"})
    .input(z.object({
      storeId: z.string(),
      schedulingSettings: schedulingSettingsDto
    }))
    .output(z.void())
    .mutation(async ({ctx, input}) => {
      const {businessId, employment, checkAllowed} = ctx;
      const storeId = toSecureStoreIdOrThrow(ctx, input.storeId);
      allAllowedOrThrow({
        checkAllowed,
        actions: ["store/update"],
        resource: `business/${businessId}/store/${storeId}`,
        resourceEntity: {
          businessId: businessId,
          storeId: storeId
        }
      });

      await ctx.prisma.store.update({
        where: {
          businessId,
          id: storeId,
        },
        data: {
          disabledScheduleValidations: input.schedulingSettings.disabledScheduleValidations
        }
      })
    }),

  updateStorePeakHours: employedStatsProcedure({feature: "common"})
    .input(z.object({
      storeId: z.string(),
      peakHours: peakHours
    }))
    .output(z.void())
    .mutation(async ({ctx, input}) => {
      const {businessId, employment} = throwIfNotEmployedByBusiness(ctx);
      // TODO permissions

      await ctx.prisma.store.update({
        where: {
          businessId,
          id: input.storeId,
        },
        data: {
          peakHours: input.peakHours
        }
      })
    }),

  // schedule builder APIs
  createSchedule: employedStatsProcedure({feature: "common"})
    .input(z.object({
      storeId: z.string(),
      week: isoWeekDate
    }))
    .output(z.object({
      scheduleId: z.string()
    }))
    .mutation(async ({ctx, input}) => {
      const {businessId, employment} = throwIfNotEmployedByBusiness(ctx);
      throwIfDenied({
        ctx, principalEmployment: employment,
        action: "store/createDraftSchedule",
        resource: `business/${businessId}/store/${input.storeId}`,
        resourceEntity: {
          storeId: input.storeId,
        }
      });

      const existingSchedule = await ctx.prisma.schedule.findFirst({
        where: {
          businessId,
          storeId: input.storeId,
          week: input.week.week,
          year: input.week.year,
          isTemplate: false
        }
      });

      if (existingSchedule) {
        throw new TRPCError({
          code: "CONFLICT",
          message: `A schedule already exists for ${input.week.year} week ${input.week.week}. Please refresh the page to see the latest schedule.`
        });
      }

      const store = await ctx.prisma.store.findUniqueOrThrow({
        where: {
          id: input.storeId
        },
        include: {
          areas: true
        }
      })

      const fohArea = find(store.areas, a => a.createdFromTemplateId === fohAreaId);
      const bohArea = find(store.areas, a => a.createdFromTemplateId === bohAreaId);

      const newScheduleId = genScheduleId();
      const draft = createDefaultSchedule({
        ...getInitDraftScheduleParams({
          ...storeRowToPartialCreateDraftScheduleParams(store),
          id: newScheduleId,
          businessId,
          week: input.week,
        }),
        genShiftId: genShiftId,
        genAreaId: genScheduleAreaId,
        fohStoreAreaId: fohArea?.id,
        bohStoreAreaId: bohArea?.id,
      });

      await ctx.prisma.schedule.create({
        data: toDbScheduleCreateInput(draft)
      });

      return {
        scheduleId: newScheduleId
      }
    }),

  getScheduleWeeks: employedStatsProcedure({feature: "common"})
    .input(z.object({
      onOrAfter: isoWeekDate,
      storeId: z.string(),
      take: z.number().min(1).max(20).optional().default(8)
    }))
    .output(z.array(scheduleRevisionWithForecast))
    .query(async ({ctx, input}) => {
      const {businessId, employment} = throwIfNotEmployedByBusiness(ctx);
      const storeId = toSecureStoreIdOrThrow(ctx, input.storeId);
      const checkAllowed = isAllowedFactory({
        businessId,
        principalEmployment: employment,
        userId: ctx.auth.user.id,
      });

      const canViewPublishedSchedules = perm.canViewSchedules(checkAllowed, {
        businessId,
        storeId,
      });
      const canViewDraftSchedules = checkAllowed({
        action: "store/getDraftSchedule",
        resource: `business/${businessId}/store/${storeId}`,
        resourceEntity: {
          storeId: storeId,
        }
      }).isAllowed;
      if (!canViewPublishedSchedules || !canViewDraftSchedules) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "You do not have permission to view the draft + published schedules for this store."
        });
      }

      const store = await ctx.prisma.store.findUniqueOrThrow({
        where: {
          id: storeId,
          businessId
        },
        select: {timezone: true}
      });
      const timezone = store.timezone ?? "America/New_York";
      const weeks = takeFromGenerator(generateIsoWeekDates(input.onOrAfter, timezone), input.take);

      const scheduleRows = await ctx.prisma.schedule.findMany({
        where: {
          businessId,
          storeId: storeId,
          isTemplate: false,
          OR: map(weeks, week => ({
            year: week.year,
            week: week.week,
          })),
        },
        orderBy: [
          {year: 'asc'},
          {week: 'asc'}
        ],
        take: input.take,
        include: {
          hourlySalesForecast: true,
          ScheduleDay: {
            include: {
              ShiftArea: {
                include: {
                  Shift: {
                    include: {
                      shiftActivities: true
                    }
                  }
                }
              }
            }
          }
        }
      });

      return map(scheduleRows, row => {
        return {
          ...hydrateSchedule(row),
          forecast: row.hourlySalesForecast[0] ? hydrateScheduleHourlySalesForecast(row.hourlySalesForecast[0]) : undefined
        }
      });
    }),

  get3PrecedingAnd3FollowingWeeksHours: employedStatsProcedure({feature: "common"})
    .input(z.object({
      storeId: z.string(),
      currentWeek: isoWeekDate,
      state: z.string()
    }))
    .output(hawaiiACAPersonHours)
    .query(async ({ctx, input}) => {
      const {businessId, employment} = throwIfNotEmployedByBusiness(ctx);
      const storeId = toSecureStoreIdOrThrow(ctx, input.storeId);

      // Early return for non-Hawaii stores to avoid expensive computation
      if (input.state !== "HI") {
        return {};
      }

      const checkAllowed = isAllowedFactory({
        businessId,
        principalEmployment: employment,
        userId: ctx.auth.user.id,
      });

      const canViewSchedules = perm.canViewSchedules(checkAllowed, {
        businessId,
        storeId,
      });
      if (!canViewSchedules) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "You do not have permission to view the schedules for this store."
        });
      }

      const timezone = await ctx.db.store.getStoreTimezone(storeId);
      const weeks = takeFromGenerator(generateIsoWeekDates({
        year: input.currentWeek.year,
        week: input.currentWeek.week - 3
      }, timezone), 7);

      const scheduleRows = await ctx.db.scheduling.getPublishedSchedulesForMultipleWeeks(
        businessId,
        storeId,
        { weeks }
      );

      // Use utility function to calculate hours per person for each week
      return mapToHawaiiACAMultiWeekHours(scheduleRows, weeks);
    }),

  getAllSchedulePeopleAtStore: employedStatsProcedure({feature: "common"})
    .input(z.object({
      storeId: z.string(),
      includeArchived: z.boolean().optional().default(false),
      includeSuspended: z.boolean().optional().default(false),
    }))
    .output(z.object({
      people: z.array(schedulePersonDto),
      timezone: z.string()
    }))
    .query(async ({ctx, input}) => {
      const {businessId, employment} = ctx;
      const storeId = toSecureStoreIdOrThrow(ctx, input.storeId);

      const storeIds = map(employment.stores, s => s.storeId);
      if (!includes(storeIds, input.storeId)) {
        // user is trying to access a store they are not employed at
        throw new TRPCError({
          code: "NOT_FOUND"
        });
      }

      // TODO make sure the user calling this has at least the minimum permissions required for
      // getting people info necessary for scheduling, such as proficiency scores, training, availability, etc.
      // Otherwise the schedule builder won't work too well.
      const checkAllowed = isAllowedFactory({
        businessId,
        principalEmployment: employment,
        userId: ctx.auth.user.id,
      });

      const peopleDtos = await getAllSchedulePeopleAtStore({
        storeId: storeId,
        businessId,
        checkAllowed,
        prisma: ctx.prisma,
        ctx,
        includeArchived: input.includeArchived,
        includeSuspended: input.includeSuspended,
      })
      const store = await ctx.prisma.store.findUniqueOrThrow({
        where: {
          id: storeId,
          businessId
        }
      })

      return {
        people: peopleDtos,
        timezone: store.timezone ?? "America/New_York"
      };
    }),

  getSchedule: employedStatsProcedure({feature: "common"})
    .input(z.object({
      id: z.string()
    }))
    .output(scheduleDto)
    .query(async ({ctx, input}) => {
      const {businessId, checkAllowed, storeIds} = ctx;

      const scheduleRow = await ctx.prisma.schedule.findUniqueOrThrow({
        where: {
          id: input.id,
          storeId: {
            in: storeIds
          },
          businessId,
        },
        include: {
          store: true,
          hourlySalesForecast: true,
          ScheduleDay: {
            include: {
              ShiftArea: {
                include: {
                  Shift: {
                    include: {
                      shiftActivities: true,
                      ShiftOffer: {
                        where: {
                          ...getShiftOfferStatusWhereCondition("pending", {}),
                        },
                        include: {
                          offereePersons: true,
                          acceptances: true,
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        }
      });
      const scheduleRevision = hydrateSchedule(scheduleRow);
      const shiftOfferRows = flatMap(scheduleRow.ScheduleDay, day =>
        flatMap(day.ShiftArea, area =>
          flatMap(area.Shift, shift => shift.ShiftOffer)));
      const shiftOffers = map(shiftOfferRows, row => {
        return {
          ...hydrateShiftOffer(row),
          status: getShiftOfferStatus(row),
        }
      });

      const storeId = toSecureStoreIdOrThrow(ctx, scheduleRow.storeId);
      const canViewSchedules = isAllowedToGetPublishedSchedule({checkAllowed, storeId, businessId});
      const canViewSalesData = perm.canListDataFiles(checkAllowed, {
        businessId,
        storeId: storeId,
      });

      if (!canViewSchedules) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "You are not authorized to view schedules"
        });
      }

      return {
        hasDraft: scheduleRevision.draft !== undefined,
        draft: scheduleRevision.draft ?? startEditingPublishedSchedule(scheduleRevision.published!),
        draftVersion: scheduleRevision.draftVersion ?? 1,
        published: scheduleRevision.published,
        publishedVersion: scheduleRevision.publishedVersion,
        timezone: scheduleRow.store.timezone ?? "America/New_York",
        shiftOffers: shiftOffers,
        forecast: canViewSalesData ?
          !isEmpty(scheduleRow.hourlySalesForecast) ? hydrateScheduleHourlySalesForecast(scheduleRow.hourlySalesForecast[0]) : undefined
          : undefined,
      }
    }),

  getScheduleForWeek: employedStatsProcedure({feature: "common"})
    .input(z.object({
      storeId: z.string(),
      week: isoWeekDate,
    }))
    .output(z.object({
      schedule: scheduleDto.optional(),
      canEditScheduleDays: z.array(dayOfWeek)
    }))
    .query(async ({ctx, input}) => {
      const {businessId, employment} = throwIfNotEmployedByBusiness(ctx);

      const scheduleRow = await ctx.prisma.schedule.findFirst({
        where: {
          storeId: input.storeId,
          businessId,
          week: input.week.week,
          year: input.week.year,
          isTemplate: false,
        },
        include: {
          store: true,
          ScheduleDay: {
            include: {
              ShiftArea: {
                include: {
                  Shift: {
                    include: {
                      shiftActivities: true,
                      ShiftOffer: {
                        include: {
                          offereePersons: true,
                          acceptances: true,
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        }
      });

      if (!scheduleRow) {
        return {
          schedule: undefined,
          canEditScheduleDays: []
        }
      }

      const user = await getCurrentUser(ctx);
      const scheduleRevision = hydrateSchedule(scheduleRow);
      const canEditScheduleDays = getDaysPersonCanEditSchedule({scheduleRevision, user});
      const shiftOfferRows = flatMap(scheduleRow.ScheduleDay, day =>
        flatMap(day.ShiftArea, area =>
          flatMap(area.Shift, shift => shift.ShiftOffer)));
      const shiftOffers = map(shiftOfferRows, row => {
        return {
          ...hydrateShiftOffer(row),
          status: getShiftOfferStatus(row),
        }
      });

      return {
        schedule: {
          hasDraft: scheduleRevision.draft !== undefined,
          draft: scheduleRevision.draft ?? startEditingPublishedSchedule(scheduleRevision.published!),
          draftVersion: scheduleRevision.draftVersion ?? 1,
          published: scheduleRevision.published,
          publishedVersion: scheduleRevision.publishedVersion,
          timezone: scheduleRow.store.timezone ?? "America/New_York",
          shiftOffers: shiftOffers,
        },
        canEditScheduleDays
      }
    }),

  discardScheduleDraft: employedStatsProcedure({feature: "common"})
    .input(z.object({
      scheduleId: z.string()
    }))
    .output(z.void())
    .mutation(async ({ctx, input}) => {
      const {businessId, employment} = throwIfNotEmployedByBusiness(ctx);
      const sched = await ctx.prisma.schedule.findUniqueOrThrow({
        where: {
          id: input.scheduleId,
          businessId,
          storeId: {
            in: map(employment.stores, s => s.storeId)
          }
        },
        select: {
          id: true,
          storeId: true
        }
      });

      throwIfDenied({
        ctx, principalEmployment: employment,
        action: "store/updateDraftSchedule",
        resource: `business/${businessId}/store/${sched.storeId}`,
        resourceEntity: {
          storeId: sched.storeId,
        }
      });

      await ctx.prisma.schedule.update({
        where: {
          id: input.scheduleId,
          businessId,
          storeId: {
            in: map(employment.stores, s => s.storeId)
          }
        },
        data: {
          draftData: Prisma.Prisma.DbNull,
          draftVersion: {increment: 1},
        }
      });
    }),

  updateSchedule: employedStatsProcedure({feature: "common"})
    .input(z.object({
      id: z.string(),
      version: z.number(),
      draft: draftSchedule
    }))
    .output(z.object({
      version: z.number()
    }))
    .mutation(async ({ctx, input}) => {
      const {businessId, employment} = throwIfNotEmployedByBusiness(ctx);
      const existingSched = await ctx.prisma.schedule.findUniqueOrThrow({
        where: {
          id: input.id,
          businessId,
          storeId: {
            in: map(employment.stores, s => s.storeId)
          },
          isTemplate: false,
        },
        include: {
          store: true,
          ScheduleDay: {
            include: {
              ShiftArea: {
                include: {
                  Shift: {
                    include: {
                      shiftActivities: true
                    }
                  }
                }
              }
            }
          }
        }
      });

      const user = await getCurrentUser(ctx);

      const scheduleRevision = hydrateSchedule(existingSched);
      const isAllowedToEditAffectedDays = isAllowedToMakeChanges({
        scheduleRevision,
        original: scheduleRevision.draft,
        draft: input.draft,
        user
      });

      // Check to see if the user is allowed to edit all days (is a shift lead on those days) or
      // if not, throw if they do not have the required permissions.
      if (!isAllowedToEditAffectedDays) {
        throwIfDenied({
          ctx, principalEmployment: employment,
          action: "store/updateDraftSchedule",
          resource: `business/${businessId}/store/${existingSched.storeId}`,
          resourceEntity: {
            storeId: existingSched.storeId
          }
        });
      }

      const hasNoPriorDraft = !Boolean(existingSched.draftData);

      // cannot edit a schedule in the past
      if (isIsoWeekBefore(input.draft.week, getDateIsoWeek(new UTCDate()))) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Cannot edit a schedule in the past"
        });
      }

      try {
        const newDraftData = sanitizeDraftSchedule(input.draft);
        const sched = await ctx.prisma.schedule.update({
          where: {
            id: input.id,
            businessId,
            storeId: {
              in: map(employment.stores, s => s.storeId)
            },
            draftVersion: input.version,
          },
          data: {
            draftData: newDraftData,
            draftVersion: {increment: 1},
            draftStartedFromPublishedVersion: hasNoPriorDraft ? existingSched.publishedVersion : undefined
          }
        });

        return {
          version: sched.draftVersion
        }
      } catch (e) {
        if (e instanceof PrismaClientKnownRequestError && (e.code === 'P2001' || e.code === 'P2025')) {
          throw new TRPCError({
            code: "CONFLICT",
            message: `We couldn't save your most recent change because another user has updated the schedule. Press OK to refresh the page and try again.`
          });
        }

        throw e;
      }
    }),

  // get schedule events that have start or end times within the given ISO week date
  getScheduleEvents: employedStatsProcedure({feature: "common"})
    .input(z.object({
      week: isoWeekDate.optional(),
      range: dateTimeRange.optional(),
      storeId: z.string(),
      excludeEventType: z.string()
        .optional()
        .default("hrDocument")
    }).refine(input => {
      if (input.week && input.range) {
        throw new Error("Cannot specify both week and range");
      }
      if (!input.week && !input.range) {
        throw new Error("Must specify either week or range");
      }
      return true;
    }, "Must specify either week or range"))
    .output(z.array(scheduleEventDto))
    .query(async ({ctx, input}) => {
      const {businessId, employment} = throwIfNotEmployedByBusiness(ctx);

      const checkAllowed = isAllowedFactory({
        businessId,
        principalEmployment: employment,
        userId: ctx.auth.user.id,
      });

      const store = await ctx.prisma.store.findUniqueOrThrow({
        where: {
          id: input.storeId,
          businessId
        }
      });

      const range = input.range ?? getIsoWeekDateRangeInTimezone({
        week: input.week!,
        timezone: store.timezone ?? "America/New_York",
      });

      return findScheduleEventsForRange({
        ctx: ctx,
        storeId: input.storeId,
        businessId,
        range: range,
        prisma: ctx.prisma,
        checkAllowed,
        excludeEventType: input.excludeEventType ?? undefined
      })
    }),

  // get schedule events that have start or end times within the given ISO week date
  getScheduleEventsWithAcknowledgementStatus: employedStatsProcedure({feature: "common"})
    .input(z.object({
      week: isoWeekDate.optional(),
      range: dateTimeRange.optional(),
      storeId: z.string(),
      excludeEventType: z.string()
        .nullable()
        .default("hrDocument"),
    }).refine(input => {
      if (input.week && input.range) {
        throw new Error("Cannot specify both week and range");
      }
      if (!input.week && !input.range) {
        throw new Error("Must specify either week or range");
      }
      return true;
    }, "Must specify either week or range"))
    .output(z.array(scheduleEventDto))
    .query(async ({ctx, input}) => {
      const {businessId, employment} = throwIfNotEmployedByBusiness(ctx);
      const storeId = toSecureStoreIdOrThrow(ctx, input.storeId);

      const storeEmployment = find(employment.stores, {storeId});
      if (!storeEmployment) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "You are not employed at this store"
        });
      }

      // Those who have access to create corrective actions can view HR Documents.
      const canViewHRDocuments = perm.canCreateCorrectiveActions(ctx.checkAllowed, {
        businessId,
        storeId: storeId,
        storeEmploymentId: storeEmployment.id,
      });

      const store = await ctx.prisma.store.findUniqueOrThrow({
        where: {
          id: storeId,
          businessId
        }
      });

      const range = input.range ?? getIsoWeekDateRangeInTimezone({
        week: input.week!,
        timezone: store.timezone ?? "America/New_York",
      });

      const baseExcludeEventTypes = !canViewHRDocuments ? ["hrDocument"] : [];
      const excludeEventTypes = compact(uniq([...baseExcludeEventTypes, input.excludeEventType ?? undefined]));

      // Get all schedule events for the week using findScheduleEventsForRangeRaw
      const visibleEvents = await findScheduleEventsForRangeRaw({
        storeId: storeId,
        businessId,
        range: range,
        prisma: ctx.prisma,
        checkAllowed: ctx.checkAllowed,
        excludeEventTypes,
      });

      const dtoCtx = {
        ...ctx,
        businessId,
        storeId: storeId,
      };

      // For events that require acknowledgment, we need to calculate personsUnacknowledged
      const eventsRequiringAcknowledgment = filter(visibleEvents, (event): event is typeof event => Boolean(event.requiresAcknowledgment));

      // If no events require acknowledgment, just return the base DTOs
      if (eventsRequiringAcknowledgment.length === 0) {
        return map(visibleEvents, event => toScheduleEventDto(dtoCtx, {
          ...event,
          recipients: null,
        }));
      }

      const earliestEventStart = minBy(eventsRequiringAcknowledgment, e => e.start)?.start ?? new Date();

      // Get all event IDs for the acknowledgment query
      const eventIds = map(eventsRequiringAcknowledgment, e => e.id);

      // Separate events by visibility type for efficient querying
      const standardEvents = filter(eventsRequiringAcknowledgment, e => e.visibilityLevel !== ScheduleEventCustomVisibility);
      const customEvents = filter(eventsRequiringAcknowledgment, e => e.visibilityLevel === ScheduleEventCustomVisibility);

      const customRecipientPersonIds = uniq(flatMap(customEvents, e => e.recipients?.map(r => r.personId) || []));

      const allPersonsRequiredToAcknowledge = await ctx.prisma.person.findMany({
        where: {
          OR: [
            ...!isEmpty(standardEvents) ? [{
              createdAt: {
                lt: earliestEventStart
              },
              employments: {
                some: {
                  job: {
                    isActive: true,
                  },
                  stores: {
                    some: {
                      storeId: input.storeId
                    }
                  }
                }
              }
            }] : [],
            ...!isEmpty(customRecipientPersonIds) ? [{
              id: {
                in: customRecipientPersonIds
              }
            }] : []
          ],
          businessId,
          isArchived: false,
          user: {
            isSuspended: false,
          }
        },
        include: {
          employments: {
            include: {
              job: true,
              stores: true
            }
          },
          user: true,
          profileImage: true,
          acknowledgedEvents: {
            where: {
              id: {in: eventIds}
            }
          }
        }
      });

      // Process each event with the batched person data
      return map(visibleEvents, (event) => {
        const baseEventDto = toScheduleEventDto(dtoCtx, {
          ...event,
          recipients: null,
        });

        if (!event.requiresAcknowledgment) {
          return baseEventDto;
        }

        // Get all persons who could potentially acknowledge this event
        let personsForThisEvent: PersonWithAcknowledgedEvents[] = [];

        if (event.visibilityLevel === ScheduleEventCustomVisibility) {
          personsForThisEvent = filter(allPersonsRequiredToAcknowledge, person => {
            return includes(event.recipients?.map(r => r.personId) || [], person.id)
          });
        } else {
          personsForThisEvent = filter(allPersonsRequiredToAcknowledge, person => {
            // Check if person was created before this specific event
            if (person.createdAt >= event.start) {
              return false;
            }

            const personEmployment = person.employments?.find((e) => e.businessId === businessId);

            if (!person.user?.id || !personEmployment) {
              return event.visibilityLevel === 0;
            }

            const personCheckAllowed = isAllowedFactory({
              businessId,
              principalEmployment: personEmployment,
              userId: person.user.id,
            });

            const canViewLeadershipEvents = personCheckAllowed({
              action: "store/listScheduleEventsLeadership",
              resource: `business/${businessId}/store/${input.storeId}`,
              resourceEntity: {
                id: input.storeId,
                storeId: input.storeId,
              }
            }).isAllowed;

            const canViewSchedulerEvents = personCheckAllowed({
              action: "store/listScheduleEventsScheduler",
              resource: `business/${businessId}/store/${input.storeId}`,
              resourceEntity: {
                id: input.storeId,
                storeId: input.storeId,
              }
            }).isAllowed;

            return canViewScheduleEventRecipient({
              canViewLeadershipEvents,
              canViewSchedulerEvents,
              visibilityLevel: event.visibilityLevel
            });
          });
        }

        const unacknowledgedPersons = filter(personsForThisEvent, person => {
          return !person?.acknowledgedEvents?.some(e => e.id === event.id);
        });

        return {
          ...baseEventDto,
          personsUnacknowledged: unacknowledgedPersons.map((p) => toMinimalPersonDto(dtoCtx, p, event.storeId))
        };
      });
    }),

  getScheduleEvent: employedStatsProcedure({feature: "common"})
    .input(z.object({
      eventId: z.string(),
    }))
    .output(scheduleEventDto)
    .query(async ({ctx, input}) => {
      const {businessId, employment} = throwIfNotEmployedByBusiness(ctx);

      const checkAllowed = isAllowedFactory({
        businessId,
        principalEmployment: employment,
        userId: ctx.auth.user.id,
      });

      const eventRow = await ctx.prisma.scheduleEvent.findUniqueOrThrow({
        where: {
          businessId,
          storeId: {in: map(employment?.stores, s => s.storeId)},
          id: input.eventId
        },
        include: {
          recipients: {
            include: {
              person: personWithJobAndImageIncludes
            }
          },
          createdByPerson: personWithJobAndImageIncludes,
          updatedByPerson: personWithJobAndImageIncludes,
          personsAcknowledged: personWithJobAndImageIncludes,
          attachments: {
            include: {
              file: true
            },
          }
        }
      })

      const visibilityStr = getScheduleEventVisibilityLevelString(eventRow.visibilityLevel);
      const canViewEvent = checkAllowed({
        action: "store/getScheduleEvent",
        resource: `business/${businessId}/store/${eventRow.storeId}`,
        resourceEntity: {
          storeId: eventRow.storeId,
          visibilityLevel: visibilityStr
        }
      }).isAllowed;

      if (!canViewEvent) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "You do not have permission to view this event"
        });
      }

      const canUpdateEvent = checkAllowed({
        action: "store/updateScheduleEvent",
        resource: `business/${businessId}/store/${eventRow.storeId}`,
        resourceEntity: {
          storeId: eventRow.storeId,
          visibilityLevel: visibilityStr
        }
      }).isAllowed;

      const canDeleteEvent = checkAllowed({
        action: "store/deleteScheduleEvent",
        resource: `business/${businessId}/store/${eventRow.storeId}`,
        resourceEntity: {
          storeId: eventRow.storeId,
          visibilityLevel: visibilityStr
        }
      }).isAllowed;

      const dtoCtx = {
        ...ctx,
        businessId,
        storeId: eventRow.storeId,
      };

      return {
        ...toScheduleEventDto(dtoCtx, eventRow),
        canEdit: canUpdateEvent,
        canDelete: canDeleteEvent
      }
    }),

  getScheduleEventWithAcknowledgementStatus: employedStatsProcedure({feature: "common"})
    .input(z.object({
      eventId: z.string(),
      storeId: z.string()
    }))
    .output(z.object({
      scheduleEvent: scheduleEventDto,
      canViewStatus: z.boolean()
    }))
    .query(async ({ctx, input}) => {
      const {businessId, employment} = throwIfNotEmployedByBusiness(ctx);
      const storeId = toSecureStoreIdOrThrow(ctx, input.storeId);
      const {eventId} = input;

      const storeEmployment = find(employment.stores, {storeId});
      if (!storeEmployment) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "You are not employed at this store"
        });
      }

      // Those who have access to create corrective actions can view HR Documents.
      const canViewHRDocuments = perm.canCreateCorrectiveActions(ctx.checkAllowed, {
        businessId,
        storeId: storeId,
        storeEmploymentId: storeEmployment.id,
      });

      const eventRow = await ctx.db.scheduleEvent.getScheduleEvent(businessId, storeId, {
        eventId, include: {
          recipients: {
            include: {
              person: {
                include: {
                  employments: {
                    include: {
                      job: true,
                      stores: true
                    }
                  },
                  user: true,
                  profileImage: true,
                  acknowledgedEvents: {
                    where: {
                      id: eventId
                    }
                  }
                }
              }
            },
          },
          createdByPerson: personWithJobAndImageIncludes,
          updatedByPerson: personWithJobAndImageIncludes,
          personsAcknowledged: personWithJobAndImageIncludes,
          attachments: {
            include: {
              file: true
            },
          }
        }
      });

      if (!eventRow) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Schedule Event not found"
        });
      }

      if (eventRow.eventType === "hrDocument" && !canViewHRDocuments) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "You do not have permission to view this event"
        });
      }

      const visibilityStr = getScheduleEventVisibilityLevelString(eventRow.visibilityLevel);

      const canViewEvent = ctx.checkAllowed({
        action: "store/getScheduleEvent",
        resource: `business/${businessId}/store/${storeId}`,
        resourceEntity: {
          storeId: storeId,
          visibilityLevel: visibilityStr
        }
      }).isAllowed

      if (!canViewEvent) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "You do not have permission to view this event"
        });
      }

      const canCreateScheduleEvents = ctx.checkAllowed({
        action: "store/createScheduleEvent",
        resource: `business/${businessId}/store/${storeId}`,
        resourceEntity: {
          storeId: storeId,
        }
      }).isAllowed;

      const canUpdateEvent = ctx.checkAllowed({
        action: "store/updateScheduleEvent",
        resource: `business/${businessId}/store/${storeId}`,
        resourceEntity: {
          storeId: storeId,
          visibilityLevel: visibilityStr
        }
      }).isAllowed

      const canDeleteEvent = ctx.checkAllowed({
        action: "store/deleteScheduleEvent",
        resource: `business/${businessId}/store/${storeId}`,
        resourceEntity: {
          storeId: storeId,
          visibilityLevel: visibilityStr
        }
      }).isAllowed

      const dtoCtx = {
        ...ctx,
        businessId,
        storeId: storeId,
      };

      const baseEventDto = {
        ...toScheduleEventDto(dtoCtx, eventRow),
        canEdit: canUpdateEvent,
        canDelete: canDeleteEvent
      };

      if (!canCreateScheduleEvents) {
        return {
          scheduleEvent: baseEventDto,
          canViewStatus: false,
        };
      }

      // If the event doesn't require acknowledgment, return the base DTO
      if (!eventRow.requiresAcknowledgment) {
        return {
          scheduleEvent: baseEventDto,
          canViewStatus: true,
        };
      }

      // Get all persons who could potentially acknowledge this event
      let allPersonsRequiredToAcknowledge: PersonWithAcknowledgedEvents[] = [];

      if (eventRow.visibilityLevel === ScheduleEventCustomVisibility) {
        allPersonsRequiredToAcknowledge = map(eventRow.recipients, r => r.person);
      } else {
        // Standard visibility levels - get all eligible people
        allPersonsRequiredToAcknowledge = await ctx.prisma.person.findMany({
          where: {
            createdAt: {
              lt: eventRow.start
            },
            employments: {
              some: {
                job: {
                  isActive: true,
                },
                stores: {
                  some: {
                    storeId: eventRow.storeId
                  }
                }
              }
            },
            businessId,
            isArchived: false,
            user: {
              isSuspended: false,
            }
          },
          include: {
            employments: {
              include: {
                job: true,
                stores: true
              }
            },
            user: true,
            profileImage: true,
            acknowledgedEvents: {
              where: {
                id: input.eventId
              }
            }
          }
        });
      }

      // Filter persons based on event-specific criteria
      let personsForThisEvent: PersonWithAcknowledgedEvents[] = [];

      // For standard visibility levels, apply permission filtering
      personsForThisEvent = filter(allPersonsRequiredToAcknowledge, person => {
        // Check if person was created before this specific event
        if (person.createdAt >= eventRow.start) {
          return false;
        }

        const personEmployment = person.employments?.find((e) => e.businessId === businessId);

        if (!person.user?.id || !personEmployment) {
          return eventRow.visibilityLevel === 0;
        }

        const personCheckAllowed = isAllowedFactory({
          businessId,
          principalEmployment: personEmployment,
          userId: person.user.id,
        });

        const canViewLeadershipEvents = personCheckAllowed({
          action: "store/listScheduleEventsLeadership",
          resource: `business/${businessId}/store/${eventRow.storeId}`,
          resourceEntity: {
            id: eventRow.storeId,
            storeId: eventRow.storeId,
          }
        }).isAllowed;

        const canViewSchedulerEvents = personCheckAllowed({
          action: "store/listScheduleEventsScheduler",
          resource: `business/${businessId}/store/${eventRow.storeId}`,
          resourceEntity: {
            id: eventRow.storeId,
            storeId: eventRow.storeId,
          }
        }).isAllowed;

        return canViewScheduleEventRecipient({
          canViewLeadershipEvents,
          canViewSchedulerEvents,
          visibilityLevel: eventRow.visibilityLevel
        });
      });

      const unacknowledgedPersons = filter(personsForThisEvent, person => {
        return !some(person?.acknowledgedEvents, e => e.id === eventRow.id);
      });

      const scheduleEvent = {
        ...baseEventDto,
        personsAcknowledged: map(eventRow.personsAcknowledged, (p) => toMinimalPersonDto(dtoCtx, p, eventRow.storeId)),
        personsUnacknowledged: map(unacknowledgedPersons, (p) => toMinimalPersonDto(dtoCtx, p, eventRow.storeId))
      };

      return {
        scheduleEvent,
        canViewStatus: true
      }
    }),

  getScheduleTemplates: employedStatsProcedure({feature: "common"})
    .input(z.object({
      storeId: z.string(),
    }))
    .output(z.array(draftSchedule))
    .query(async ({ctx, input}) => {
      const {businessId, employment} = throwIfNotEmployedByBusiness(ctx);
      throwIfDenied({
        ctx, principalEmployment: employment,
        action: "store/listScheduleTemplates",
        resource: `business/${businessId}/store/${input.storeId}`,
        resourceEntity: {
          storeId: input.storeId,
        }
      });

      const templates = await ctx.prisma.schedule.findMany({
        where: {
          businessId,
          storeId: input.storeId,
          isTemplate: true,
        }
      });

      return map(templates, templ => {
        return {
          ...toDraftSchedule(templ.draftData),
          id: templ.id,
          title: templ.title ?? undefined,
          description: templ.description ?? undefined,
        }
      })
    }),

  getStoreChildLaborLaws: employedStatsProcedure({feature: "common"})
    .input(z.object({
      storeId: z.string()
    }))
    .output(minorWorkGuidelines)
    .query(async ({ctx, input}) => {
      const {businessId, employment} = throwIfNotEmployedByBusiness(ctx);
      // TODO permissions

      const store = await ctx.prisma.store.findUniqueOrThrow({
        where: {
          id: input.storeId
        },
        include: {
          address: true,
          stateMinorWorkGuidelines: true
        }
      })

      const guidelines = first(store.stateMinorWorkGuidelines);

      if (guidelines) {
        return toStateMinorWorkGuidelinesDto(guidelines);
      }

      const laws = statesDefaultChildLaborLaws.find(laws => laws.state === store.address.state);

      if (!laws) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "State not found"
        });
      }

      return laws.data;
    }),

  updateStoreChildLaborLaws: employedStatsProcedure({feature: "common"})
    .input(z.object({
      storeId: z.string(),
      childLaborLaws: minorWorkGuidelines,
    }))
    .output(z.void())
    .mutation(async ({ctx, input}) => {
      const {businessId, employment} = throwIfNotEmployedByBusiness(ctx);
      const store = await ctx.prisma.store.findUniqueOrThrow({
        where: {
          id: input.storeId
        },
        include: {
          stateMinorWorkGuidelines: true
        }
      })

      throwIfDenied({
        ctx, principalEmployment: employment,
        action: "store/updateLaborLaws",
        resource: `business/${businessId}/store/${input.storeId}`,
        resourceEntity: {
          storeId: input.storeId,
        }
      });

      const guidelines = first(store.stateMinorWorkGuidelines);

      const childLaborLaws = input.childLaborLaws;
      const data = {
        ageGroup14To15_schoolDays_hoursPerDay: childLaborLaws.ageGroup14To15.schoolDays.hoursPerDay ?? null,
        ageGroup14To15_schoolDays_hoursPerWeek: childLaborLaws.ageGroup14To15.schoolDays.hoursPerWeek ?? null,
        ageGroup14To15_nonSchoolDays_hoursPerDay: childLaborLaws.ageGroup14To15.nonSchoolDays.hoursPerDay ?? null,
        ageGroup14To15_nonSchoolDays_hoursPerWeek: childLaborLaws.ageGroup14To15.nonSchoolDays.hoursPerWeek ?? null,
        ageGroup14To15_workHours_start: childLaborLaws.ageGroup14To15.workHours.start ? timeToDbTime(childLaborLaws.ageGroup14To15.workHours.start) : null,
        ageGroup14To15_workHours_end: childLaborLaws.ageGroup14To15.workHours.end ? timeToDbTime(childLaborLaws.ageGroup14To15.workHours.end) : null,
        ageGroup14To15_workHours_summer_start: childLaborLaws.ageGroup14To15.workHours.summerStart ? timeToDbTime(childLaborLaws.ageGroup14To15.workHours.summerStart) : null,
        ageGroup14To15_workHours_summer_end: childLaborLaws.ageGroup14To15.workHours.summerEnd ? timeToDbTime(childLaborLaws.ageGroup14To15.workHours.summerEnd) : null,
        ageGroup16to17_schoolDays_hoursPerDay: childLaborLaws.ageGroup16To17.schoolDays.hoursPerDay ?? null,
        ageGroup16to17_schoolDays_hoursPerWeek: childLaborLaws.ageGroup16To17.schoolDays.hoursPerWeek ?? null,
        ageGroup16to17_nonSchoolDays_hoursPerDay: childLaborLaws.ageGroup16To17.nonSchoolDays.hoursPerDay ?? null,
        ageGroup16to17_nonSchoolDays_hoursPerWeek: childLaborLaws.ageGroup16To17.nonSchoolDays.hoursPerWeek ?? null,
        summer_start: childLaborLaws.summerDates.start ?? null,
        summer_end: childLaborLaws.summerDates.end ?? null,
      }
      if (guidelines) {
        await ctx.prisma.stateMinorWorkGuidelines.update({
          where: {
            id: guidelines.id
          },
          data
        })
      } else {
        await ctx.prisma.stateMinorWorkGuidelines.create({
          data: {
            id: genStateMinorWorkGuidelinesId(),
            storeId: input.storeId,
            ...data,
          }
        })
      }
    }),

  getStoreScheduleBreakRules: employedStatsProcedure({feature: "common"})
    .input(z.object({
      storeId: z.string()
    }))
    .output(z.array(scheduleBreakRuleDto))
    .query(async ({ctx, input}) => {
      const {businessId, employment} = throwIfNotEmployedByBusiness(ctx);
      // TODO permissions

      const storeBreakRules = await ctx.prisma.scheduleBreakRules.findMany({
        where: {
          storeId: input.storeId
        }
      });

      return map(storeBreakRules, toScheduleBreakRuleDto);
    }),

  updateScheduleBreakRules: employedStatsProcedure({feature: "common"})
    .input(z.object({
      storeId: z.string(),
      breakRules: z.array(scheduleBreakRuleDto)
    }))
    .output(z.void())
    .mutation(async ({ctx, input}) => {
      const {businessId, employment} = throwIfNotEmployedByBusiness(ctx);
      throwIfDenied({
        ctx, principalEmployment: employment,
        action: "store/updateBreakRules",
        resource: `business/${businessId}/store/${input.storeId}`,
        resourceEntity: {
          storeId: input.storeId,
        }
      });

      await ctx.prisma.scheduleBreakRules.deleteMany({
        where: {
          storeId: input.storeId
        }
      })

      if (input.breakRules.length) {
        await ctx.prisma.scheduleBreakRules.createMany({
          data: map(input.breakRules, breakRule => ({
            id: breakRule.id,
            storeId: input.storeId,
            isRequiredByLaw: breakRule.isRequiredByLaw,
            ageGroup: breakRule.ageGroup,
            shiftLengthHours: breakRule.shiftLengthHours,
            breakLengthMinutes: breakRule.breakLengthMinutes,
            isPaid: breakRule.isPaid
          }))
        })
      }
    }),

  setScheduleRequestLeadTime: employedStatsProcedure({feature: "common"})
    .input(z.object({
      storeId: z.string(),
      leadTime: z.number().nonnegative(),
    }))
    .output(z.void())
    .mutation(async ({ctx, input}) => {
      const {businessId, employment} = throwIfNotEmployedByBusiness(ctx);
      throwIfDenied({
        ctx, principalEmployment: employment,
        action: "store/updateTimeOffRules",
        resource: `business/${businessId}/store/${input.storeId}`,
        resourceEntity: {
          storeId: input.storeId,
        }
      });

      await ctx.prisma.store.update({
        where: {
          id: input.storeId
        },
        data: {
          scheduleRequestLeadTime: input.leadTime
        }
      })
    }),

  sendReminderToIncompletePerson: employedStatsProcedure({feature: "common"})
    .input(z.object({
      personId: z.string(),
      storeId: z.string()
    }))
    .output(z.void())
    .mutation(async ({ctx, input}) => {
      const {businessId, employment} = throwIfNotEmployedByBusiness(ctx);
      await allowRateOrThrow({
        key: input.personId,
        group: "sendReminderToIncompletePerson",
        maxRequestsPerDuration: 10,
        prisma: ctx.prisma,
      });

      throwIfDenied({
        ctx, principalEmployment: employment,
        action: "employmentRequest/approve",
        resource: `business/${businessId}/store/${input.storeId}`,
        resourceEntity: {
          id: input.storeId,
          storeId: input.storeId,
        }
      });

      const person = await ctx.prisma.person.findUniqueOrThrow({
        where: {
          id: input.personId,
        },
        include: {
          user: {
            select: {
              devicePushTokens: true,
            }
          },
          notificationSettings: true,
        }
      });
      const store = await ctx.prisma.store.findUniqueOrThrow({
        where: {
          id: input.storeId,
          businessId
        }
      });
      const message = `Hi ${person.firstName}, please complete your onboarding for the ${store.title} store: Open the Nation app, tap "Sign in", enter your phone/email and password, and complete the onboarding steps until you reach the dashboard.`;
      await notifyPerson({
        ctx,
        person: person,
        subject: "Nation: Please complete your onboarding",
        overrideTransports: new Set(["email", "sms"]),
        message: message,
        code: NotificationCode.teamMember.onboardingReminder,
        entityIds: {
          storeId: store.id,
        },
        deduplicationId: businessId + "_" + person.id + "_" + generateSMSHash({
          body: message,
          timestamp: new Date()
        }, {
          includeTimestamp: true,
          timeWindowMinutes: 30,
          normalize: true
        })
      })
    }),

  getPresignedPostForScheduleEvent: employedStatsProcedure({feature: "common"})
    .input(z.object({
      storeId: z.string(),
      contentType: z.string(),

      // TODO make this required after app update (Jan 8, 2025)
      mediaType: attachmentMediaType.optional()
    }))
    .output(presignedPostDto)
    .mutation(async ({ctx, input}) => {
      const userId = ctx.auth.user.id;
      const {s3MediaBucket, s3Client} = ctx;

      await allowRateOrThrow({
        key: userId,
        group: "getPresignedPost",
        maxRequestsPerDuration: 200,
        prisma: ctx.prisma
      });

      const {
        businessId,
        employment: userEmployment
      } = ctx

      if (!userEmployment.stores.some(store => store.storeId === input.storeId)) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "You're not employed at the store"
        });
      }

      // TODO permissions

      const twoHundredMegs = 200 * 1024 * 1024;
      const securityPrefix = getFileS3KeyPrefix({
        mediaType: input.mediaType,
        businessId,
        storeId: input.storeId,
      })
      const newId = genFileAttachmentId();
      const key = getS3Key({prefix: securityPrefix, objectId: newId});
      const presignedPost = await getPresignedPost({
        s3Client: s3Client,
        s3Bucket: s3MediaBucket,
        maxSizeBytes: twoHundredMegs,
        securityPrefix: securityPrefix,
        contentType: input.contentType,
        key: key
      });

      return {
        newId: newId,
        key,
        presignedPost,
      };
    }),

  upsertScheduleEvent: employedStatsProcedure({feature: "common"})
    .input(z.object({
      id: z.string(),
      storeId: z.string(),
      title: z.string().min(0).max(256),
      description: z.string().max(1024).optional(),
      range: dateTimeRange,
      eventType: z.string().min(0).max(256).optional(),
      visibilityLevel: z.number().int(),
      isTimeOffRestricted: z.boolean().optional(),
      requiresAcknowledgment: z.boolean().optional(),
      attachment: fileAttachmentDto.optional(),
      recipientPersonIds: z.array(z.string()).optional(),
    }))
    .output(z.void())
    .mutation(async ({ctx, input}) => {
      const {businessId, employment} = ctx;
      const storeId = toSecureStoreIdOrThrow(ctx, input.storeId);

      throwIfDenied({
        ctx,
        principalEmployment: employment,
        action: "store/createScheduleEvent",
        resource: `business/${businessId}/store/${storeId}`,
        resourceEntity: {
          storeId: storeId
        }
      });

      // Validate Custom Recipients
      if (input.visibilityLevel === ScheduleEventCustomVisibility) {
        if (!input.recipientPersonIds || input.recipientPersonIds.length === 0) {
          throw new TRPCError({
            code: "BAD_REQUEST",
            message: "Must provide at least one recipient for custom visibility level"
          });
        }
      }

      const existingEvent = await ctx.db.scheduleEvent.getScheduleEvent(businessId, storeId, {
        eventId: input.id,
        include: {
          attachments: true,
          generators: true
        }
      });

      if (existingEvent && existingEvent.eventType === "hrDocument") {
        if (input.attachment?.fileId !== existingEvent.attachments[0]?.fileId) {
          throw new TRPCError({
            code: "BAD_REQUEST",
            message: "Cannot change attachment for HR document"
          });
        }
      }

      const event = await ctx.prisma.scheduleEvent.upsert({
        where: {
          id: input.id,
          businessId,
          storeId: input.storeId
        },
        create: {
          id: input.id,
          storeId: input.storeId,
          businessId,
          title: input.title,
          description: input.description,
          start: input.range.start,
          end: input.range.end,
          eventType: input.eventType,
          visibilityLevel: input.visibilityLevel,
          createdByPersonId: ctx.auth.user.person.id,
          isTimeOffRestricted: input.isTimeOffRestricted,
          requiresAcknowledgment: input.requiresAcknowledgment,
        },
        update: {
          title: input.title,
          description: input.description,
          start: input.range.start,
          end: input.range.end,
          eventType: input.eventType,
          visibilityLevel: input.visibilityLevel,
          updatedByPersonId: ctx.auth.user.person.id,
          isTimeOffRestricted: input.isTimeOffRestricted,
          requiresAcknowledgment: input.requiresAcknowledgment,
        }
      });

      // Handle custom recipients
      if (input.visibilityLevel === ScheduleEventCustomVisibility && input.recipientPersonIds) {
        // Delete existing recipients
        await ctx.prisma.scheduleEventRecipient.deleteMany({
          where: {
            scheduleEventId: event.id
          }
        });

        // Create new recipients
        await ctx.prisma.scheduleEventRecipient.createMany({
          data: input.recipientPersonIds.map(personId => ({
            id: genScheduleEventRecipientId(),
            scheduleEventId: event.id,
            personId: personId
          }))
        });
      } else if (input.visibilityLevel !== ScheduleEventCustomVisibility) {
        // If changing from custom to non-custom, delete all recipients
        await ctx.prisma.scheduleEventRecipient.deleteMany({
          where: {
            scheduleEventId: event.id
          }
        });
      }

      const oldAttachments = await findScheduleEventFileAttachments(ctx.prisma, storeId, event.id);
      const attachmentDiff = diffItems({
        oldItems: oldAttachments,
        newItems: input.attachment ? [input.attachment] : [],
        getId: a => a.id
      })

      for (const attachment of attachmentDiff.toCreate) {
        await attachFileToScheduleEvent(ctx.prisma, storeId, {
          scheduleEventId: event.id,
          attachment,
          businessId
        });
      }

      for (const attachment of attachmentDiff.toUpdate) {
        await updateScheduleEventFileAttachment(ctx.prisma, storeId, {
          fileAttachmentId: attachment.id,
          attachment
        });
      }

      const [orphanedAttachments, adoptedAttachments] = partition(attachmentDiff.toDelete, a => a?.scheduleEvent);

      if (!isEmpty(orphanedAttachments)) {
        await deleteManyScheduleEventFileAttachments(ctx.prisma, storeId, map(orphanedAttachments, a => a.id))
      }
      for (const attachment of adoptedAttachments) {
        await detachFileFromScheduleEvent(ctx.prisma, storeId, {
          fileAttachmentId: attachment.id,
          scheduleEventId: event.id
        })
      }

      if (input.eventType === "announcement" || input.eventType === "hrDocument" || input.eventType === "reminder") {
        const title = (input.eventType === "hrDocument")
          ? "Nation: New document requires your signature. Please open the app to review."
          : "Nation: " + input.title;

        const message = (input.eventType === "hrDocument")
          ? undefined
          : input.description;

        const timezone = await ctx.db.store.getStoreTimezone(storeId);
        const presetTimes = ["event time"];
        const baseGen = {
          title,
          message,
          time: input.range.start,
          group: {
            businessId,
            storeId: input.storeId,
            visibilityLevel: getScheduleEventVisibilityLevelString(input.visibilityLevel),
          },
          persons: [],
          leadSchedule: presetTimesToLeadSchedule({
            timeZone: timezone,
            presetTimes: presetTimes,
            eventTime: input.range.start,
            now: new Date(),
            channels: new Set(["sms"])
          }),
        };

        if (existingEvent) {
          for (const generator of existingEvent.generators) {
            await updateGenerator({
              now: new Date(),
              genScheduledEventId: genScheduleEventId,
              generator: {
                ...baseGen,
                id: generator.id,
              },
              updateDbGenerator: (generator) => {
                return updateDbGenerator({
                  generator,
                  prisma: ctx.prisma,
                  presetTimes: presetTimes
                });
              },
              getScheduledEvents: (generatorId) => {
                return getScheduledEvents({
                  generatorId,
                  prisma: ctx.prisma
                });
              },
              deleteQStashMessages: (messageIds) => {
                return deleteQstashMessages({
                  qstashClient: ctx.qstash,
                  messageIds
                });
              },
              deleteDbScheduledEvents: (events) => {
                return deleteDbScheduledEvents({
                  events,
                  prisma: ctx.prisma
                });
              },
              createQstashMessage: (event) => {
                return createQstashMessage({
                  qstash: ctx.qstash,
                  qstashApiUrl: ctx.qstashApiUrl,
                  event
                });
              },
              insertScheduledEvents: (events) => {
                return insertScheduledEvents({
                  prisma: ctx.prisma,
                  events
                });
              },
              context: {
                now: new Date(),
                timeZone: timezone,
              }
            })
          }
        } else {
          await createGenerator({
            generator: {
              ...baseGen,
              id: genGeneratorId(),
            },
            genScheduledEventId: genScheduleEventId,
            insertGenerator: (generator) => {
              return insertGenerator({
                generator,
                scheduleEventId: input.id,
                prisma: ctx.prisma,
                generatorPurpose: "announcement",
                leadSchedule: {
                  title: presetTimes.join(","),
                  icon: "📅",
                  presetTimes: presetTimes
                }
              });
            },
            createQstashMessage: (event) => {
              return createQstashMessage({
                qstash: ctx.qstash,
                qstashApiUrl: ctx.qstashApiUrl,
                event
              });
            },
            now: new Date(),
            insertScheduledEvents: (events) => {
              return insertScheduledEvents({
                prisma: ctx.prisma,
                events
              });
            },
            context: {
              now: new Date(),
              timeZone: timezone,
            }
          })
        }
      }
    }),

  acknowledgeScheduleEvent: employedStatsProcedure({feature: "common"})
    .input(z.object({
      eventId: z.string(),
    }))
    .output(z.void())
    .mutation(async ({ctx, input}) => {
      const {businessId, employment} = throwIfNotEmployedByBusiness(ctx);

      const event = await ctx.prisma.scheduleEvent.findUniqueOrThrow({
        where: {
          id: input.eventId,
          businessId,
          storeId: {in: map(employment.stores, s => s.storeId)},
        },
        include: {
          personsAcknowledged: personWithJobAndImageIncludes,
        }
      });

      if (!event) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Event not found"
        });
      }

      await ctx.prisma.scheduleEvent.update({
        where: {
          id: input.eventId,
        },
        data: {
          personsAcknowledged: {
            connect: {id: ctx.auth.user.person.id},
          },
        },
      });
    }),

  digitallySignScheduleEvent: employedStatsProcedure({feature: "common"})
    .input(z.object({
      eventId: z.string(),
      storeId: z.string(),
      clientId: z.string(),
      signatureData: z.object({
        acknowledgementStatement: z.string(),
        digitalSignature: z.string(),
        deviceInfo: storeDeviceInfo,
      })
    }))
    .output(z.void())
    .mutation(async ({ctx, input}) => {
      const {businessId, employment} = throwIfNotEmployedByBusiness(ctx);
      const storeId = toSecureStoreIdOrThrow(ctx, input.storeId);

      const event = await ctx.db.scheduleEvent.getScheduleEvent(businessId, storeId, {
        eventId: input.eventId,
        include: {
          store: true,
          attachments: {
            include: {
              file: true,
            },
          },
        },
      });

      if (!event) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Event not found"
        });
      }

      const attachment = event.attachments[0];
      const timezone = event.store.timezone ?? "America/New_York";

      if (!attachment) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Event does not have a document"
        });
      }

      const ipAddress = ctx.clientIpAddress ?? "unknown";
      const deviceInfo = JSON.stringify(input.signatureData.deviceInfo);
      const personId = ctx.auth.user.person.id;

      await ctx.prisma.digitalSignatureAcknowledgement.create({
        data: {
          scheduleEventId: event.id,
          personId: ctx.auth.user.person.id,
          acknowledgementTimestamp: new Date(),
          acknowledgementStatement: input.signatureData.acknowledgementStatement,
          digitalSignature: input.signatureData.digitalSignature,
          ipAddress,
          clientId: input.clientId,
          deviceInfo: deviceInfo,
        },
      });

      // create a copy of the attachment in the team member's documents
      const newS3Key = getTeamMemberDocument3Key({
        businessId,
        storeId,
        personId,
        fileId: attachment.file.id,
      });

      try {
        const isPdf = attachment.file.mimeType === 'application/pdf';

        if (isPdf) {
          const signatureData = {
            acknowledgementStatement: input.signatureData.acknowledgementStatement,
            digitalSignature: input.signatureData.digitalSignature,
            deviceInfo,
            ipAddress,
            personName: `${ctx.auth.user.person.firstName} ${ctx.auth.user.person.lastName}`,
            signedAt: new Date(),
          };

          const modifiedPdfBuffer = await appendSignatureToPdf({
            s3Client: ctx.s3Client,
            s3Bucket: ctx.s3MediaBucket,
            s3ObjectKey: attachment.file.s3ObjectKey,
            signatureData,
            timezone,
          });

          await uploadPdfToS3({
            s3Client: ctx.s3Client,
            s3Bucket: ctx.s3MediaBucket,
            s3ObjectKey: newS3Key,
            pdfBuffer: modifiedPdfBuffer,
          });
        } else {
          await ctx.s3Client.send(new CopyObjectCommand({
            Bucket: ctx.s3MediaBucket,
            CopySource: `${ctx.s3MediaBucket}/${attachment.file.s3ObjectKey}`,
            Key: newS3Key
          }));
        }

        await ctx.prisma.fileAttachment.create({
          data: {
            id: genFileAttachmentId(),
            title: `${event.title} (signed)`,
            description: event.description,
            documentType: attachment.documentType,
            person: {
              connect: {
                id: personId,
              },
            },
            file: {
              create: {
                id: genFileId(),
                businessId: businessId,
                storeId: storeId,
                filename: attachment.file.filename,
                mimeType: attachment.file.mimeType,
                s3ObjectKey: newS3Key,
                width: attachment.file.width,
                height: attachment.file.height,
                mediaType: attachment.file.mediaType,
              },
            },
          },
        })
      } catch (error) {
        Sentry.captureException(error, {
          extra: {
            eventId: event.id,
            personId: ctx.auth.user.person.id,
            fileId: attachment.file.id,
          }
        });
      }
    }),

  // Get all the schedule events for the current user
  getPersonScheduleEvents: employedStatsProcedure({feature: "common"})
    .input(z.object({
      storeId: z.string(),
      calendars: z.array(z.string()).optional()
    }))
    .output(z.object({
      events: z.array(scheduleEventDto),
    }))
    .query(async ({ctx, input}) => {
      const {businessId, employment} = throwIfNotEmployedByBusiness(ctx);
      const storeIds = map(employment.stores, s => s.storeId);
      if (!includes(storeIds, input.storeId)) {
        return {
          events: []
        }
      }

      const events = await ctx.prisma.scheduleEvent.findMany({
        where: {
          businessId,
          storeId: input.storeId,
          createdByPersonId: ctx.auth.user.person.id,
          end: {
            gte: new Date()
          },
          ...(input.calendars ? {eventType: {in: input.calendars}} : {}),
          isArchived: false,
        },
        include: {
          recipients: {
            include: {
              person: personWithJobAndImageIncludes
            }
          },
          createdByPerson: personWithJobAndImageIncludes,
          updatedByPerson: personWithJobAndImageIncludes,
          personsAcknowledged: personWithJobAndImageIncludes,
          attachments: {
            include: {
              file: true
            },
          }
        },
        orderBy: {
          start: "asc"
        }
      });

      const dtoCtx = {
        ...ctx,
        businessId,
        storeId: input.storeId,
      };

      return {
        events: map(events, evt => {
          return toScheduleEventDto(dtoCtx, evt)
        })
      }
    }),

  getScheduleEventAcknowledgementStatus: employedStatsProcedure({feature: "common"})
    .input(z.object({
      eventId: z.string(),
      storeId: z.string()
    }))
    .output(z.object({
      canViewStatus: z.boolean(),
      unacknowledgedPersons: z.array(personDto),
      requiredAcknowledgementCount: z.number(),
      acknowledgedByAll: z.boolean()
    }))
    .query(async ({ctx, input}) => {
      const {businessId, employment} = throwIfNotEmployedByBusiness(ctx);
      const storeId = toSecureStoreIdOrThrow(ctx, input.storeId);
      const {eventId} = input;

      const checkAllowed = isAllowedFactory({
        businessId,
        principalEmployment: employment,
        userId: ctx.auth.user.id,
      });

      const canCreateScheduleEvents = checkAllowed({
        action: "store/createScheduleEvent",
        resource: `business/${businessId}/store/${storeId}`,
        resourceEntity: {
          storeId: storeId,
        }
      }).isAllowed;

      if (!canCreateScheduleEvents) {
        return {
          canViewStatus: false,
          unacknowledgedPersons: [],
          requiredAcknowledgementCount: 0,
          acknowledgedByAll: false
        }
      }

      const event = await ctx.db.scheduleEvent.getScheduleEvent(businessId, storeId, {
        eventId, include: {
          recipients: {
            select: {
              personId: true
            }
          }
        }
      });

      if (!event) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Event not found"
        });
      }

      const {visibilityLevel} = event;

      let allPersonsRequiredToAcknowledge: PersonWithAcknowledgedEvents[] = [];

      if (visibilityLevel === ScheduleEventCustomVisibility) {
        // Custom recipients - only get the specific people
        const recipientPersonIds = map(event.recipients, r => r.personId);
        if (recipientPersonIds.length > 0) {
          allPersonsRequiredToAcknowledge = await ctx.prisma.person.findMany({
            where: {
              id: {
                in: recipientPersonIds
              },
              businessId,
              isArchived: false,
              user: {
                isSuspended: false,
              }
            },
            include: {
              employments: {
                include: {
                  job: true,
                  stores: true
                }
              },
              user: true,
              profileImage: true,
              acknowledgedEvents: {
                where: {
                  id: eventId
                }
              }
            }
          });
        }
      } else {
        // Standard visibility levels - get all eligible people and filter by permissions
        allPersonsRequiredToAcknowledge = await ctx.prisma.person.findMany({
          where: {
            createdAt: {
              lt: event.start
            },
            employments: {
              some: {
                job: {
                  isActive: true,
                },
                stores: {
                  some: {
                    storeId: storeId
                  }
                }
              }
            },
            businessId,
            isArchived: false,
            user: {
              isSuspended: false,
            }
          },
          include: {
            employments: {
              include: {
                job: true,
                stores: true
              }
            },
            user: true,
            profileImage: true,
            acknowledgedEvents: {
              where: {
                id: eventId
              }
            }
          }
        });
      }

      // For custom recipients, we don't need to filter by permissions since they were explicitly selected
      if (visibilityLevel !== ScheduleEventCustomVisibility) {
        allPersonsRequiredToAcknowledge = filter(allPersonsRequiredToAcknowledge, person => {
          const personEmployment = person.employments?.find((e) => e.businessId === businessId)

          if (!person.user?.id || !personEmployment) {
            return visibilityLevel === 0;
          }

          const checkAllowed = isAllowedFactory({
            businessId,
            principalEmployment: personEmployment,
            userId: person.user.id,
          });

          const canViewLeadershipEvents = checkAllowed({
            action: "store/listScheduleEventsLeadership",
            resource: `business/${businessId}/store/${storeId}`,
            resourceEntity: {
              id: storeId,
              storeId: storeId,
            }
          }).isAllowed;

          const canViewSchedulerEvents = checkAllowed({
            action: "store/listScheduleEventsScheduler",
            resource: `business/${businessId}/store/${storeId}`,
            resourceEntity: {
              id: storeId,
              storeId: storeId,
            }
          }).isAllowed;

          return canViewScheduleEventRecipient({
            canViewLeadershipEvents,
            canViewSchedulerEvents,
            visibilityLevel
          });
        });
      }

      const unacknowledgedPersons = filter(allPersonsRequiredToAcknowledge, person => {
        return !person?.acknowledgedEvents?.some(e => e.id === input.eventId);
      });

      return {
        canViewStatus: true,
        unacknowledgedPersons: unacknowledgedPersons.map((p) => toMinimalPersonDto(ctx, p, event.storeId)),
        requiredAcknowledgementCount: allPersonsRequiredToAcknowledge.length,
        acknowledgedByAll: unacknowledgedPersons.length === 0
      };
    }),

  archiveScheduleEvent: employedStatsProcedure({feature: "common"})
    .input(z.object({
      id: z.string(),
      storeId: z.string(),
    }))
    .output(z.void())
    .mutation(async ({ctx, input}) => {
      const {businessId, employment} = throwIfNotEmployedByBusiness(ctx);
      const storeId = toSecureStoreIdOrThrow(ctx, input.storeId);
      const event = await ctx.prisma.scheduleEvent.findUniqueOrThrow({
        where: {
          id: input.id,
          businessId,
          storeId,
        },
        include: {
          store: true,
          generators: true
        }
      });

      throwIfDenied({
        ctx,
        principalEmployment: employment,
        action: "store/archiveScheduleEvent",
        resource: `business/${businessId}/store/${storeId}`,
        resourceEntity: {
          storeId: event.storeId
        }
      });

      for (const generator of event.generators) {
        await deleteGenerator({
          generatorId: generator.id,
          deleteDbGenerator: generatorId => {
            return deleteDbGenerator({
              generatorId,
              prisma: ctx.prisma
            });
          },
          getScheduledEvents: (generatorId) => {
            return getScheduledEvents({
              generatorId,
              prisma: ctx.prisma
            });
          },
          deleteDbScheduledEvents: (events) => {
            return deleteDbScheduledEvents({
              events,
              prisma: ctx.prisma
            });
          },
          deleteQStashMessages: (messageIds) => {
            return deleteQstashMessages({
              messageIds: messageIds,
              qstashClient: ctx.qstash
            });
          }
        })
      }

      await ctx.prisma.scheduleEvent.update({
        where: {
          id: input.id,
          businessId,
          storeId,
        },
        data: {
          isArchived: true,
          archivedAt: new Date(),
          archivedByPersonId: ctx.auth.user.person.id,
        }
      });
    }),

  deleteScheduleEvent: employedStatsProcedure({feature: "common"})
    .input(z.object({
      id: z.string(),
    }))
    .output(z.void())
    .mutation(async ({ctx, input}) => {
      const {businessId, employment} = throwIfNotEmployedByBusiness(ctx);

      const evt = await ctx.prisma.scheduleEvent.findUnique({
        where: {
          id: input.id,
          businessId,
          storeId: {
            in: map(employment.stores, s => s.storeId)
          }
        },
        include: {
          generators: true
        }
      });
      if (!evt) {
        return;
      }

      throwIfDenied({
        ctx,
        principalEmployment: employment,
        action: "store/deleteScheduleEvent",
        resource: `business/${businessId}/store/${evt.storeId}`,
        resourceEntity: {
          storeId: evt.storeId
        }
      });

      for (const generator of evt.generators) {
        await deleteGenerator({
          generatorId: generator.id,
          deleteDbGenerator: generatorId => {
            return deleteDbGenerator({
              generatorId,
              prisma: ctx.prisma
            });
          },
          getScheduledEvents: (generatorId) => {
            return getScheduledEvents({
              generatorId,
              prisma: ctx.prisma
            });
          },
          deleteDbScheduledEvents: (events) => {
            return deleteDbScheduledEvents({
              events,
              prisma: ctx.prisma
            });
          },
          deleteQStashMessages: (messageIds) => {
            return deleteQstashMessages({
              messageIds: messageIds,
              qstashClient: ctx.qstash
            });
          }
        })
      }

      await ctx.prisma.scheduleEvent.deleteMany({
        where: {
          id: input.id,
          businessId,
          storeId: {
            in: map(employment.stores, s => s.storeId)
          }
        }
      })
    }),

  updateScheduleTemplateMeta: employedStatsProcedure({feature: "common"})
    .input(z.object({
      id: z.string(),
      title: z.string().min(0).max(1024).trim(),
      description: z.string().max(4096).trim().optional(),
    }))
    .output(z.void())
    .mutation(async ({ctx, input}) => {
      const {businessId, employment} = throwIfNotEmployedByBusiness(ctx);
      // TODO permissions

      await ctx.prisma.schedule.updateMany({
        where: {
          id: input.id,
          businessId,
          isTemplate: true,
          storeId: {in: map(employment.stores, s => s.storeId)}
        },
        data: {
          title: input.title,
          description: input.description
        }
      })
    }),

  upsertScheduleTemplate: employedStatsProcedure({feature: "common"})
    .input(z.object({
      scheduleId: z.string(),
      storeId: z.string(),
      title: z.string().min(0).max(1024).trim(),
      description: z.string().max(4096).trim().optional(),
      dayOfWeek: dayOfWeek
    }))
    .output(z.void())
    .mutation(async ({ctx, input}) => {
      const {businessId, employment} = throwIfNotEmployedByBusiness(ctx);
      throwIfDenied({
        ctx, principalEmployment: employment,
        action: "store/createScheduleTemplate",
        resource: `business/${businessId}/store/${input.storeId}`,
        resourceEntity: {
          storeId: input.storeId,
        }
      });

      const scheduleRow = await ctx.prisma.schedule.findUniqueOrThrow({
        where: {
          id: input.scheduleId,
          businessId,
          storeId: input.storeId
        },
        include: {
          ScheduleDay: {
            include: {
              ShiftArea: {
                include: {
                  Shift: {
                    include: {
                      shiftActivities: true
                    }
                  }
                }
              }
            }
          }
        }
      });
      const schedRev = hydrateSchedule(scheduleRow);
      const template = schedRev.draft ?? (schedRev.published ? startEditingPublishedSchedule(schedRev.published) : null);
      if (!template) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Schedule not found"
        });
      }

      // the day that we're choosing to be the template
      const templateDay = find(template.days, d => d.dayOfWeek === input.dayOfWeek);
      if (!templateDay) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Day not found in schedule"
        });
      }

      const allAreaTitles = uniq(flatMap(template.days, d => map(d.areas, a => a.title)));
      template.days = times(7, i => {
        const day = i + 1;
        return copyDay({
          fromDay: templateDay,
          dayOfWeek: day,
          copyShiftAssignments: true,
          includeAreaTitles: allAreaTitles,
          toDay: templateDay,
          shouldFilterAreas: false
        });
      });

      // if a schedule template with this title already exists, overwrite it
      const existingTemplate = await ctx.prisma.schedule.findFirst({
        where: {
          businessId,
          storeId: input.storeId,
          isTemplate: true,
          title: input.title
        }
      });

      if (existingTemplate) {
        await ctx.prisma.schedule.update({
          where: {
            id: existingTemplate.id
          },
          data: {
            draftData: sanitizeDraftSchedule(template),
            description: input.description,
          }
        })
      } else {
        // else create a new schedule template
        await ctx.prisma.schedule.create({
          data: {
            id: genScheduleId(),
            title: input.title,
            description: input.description,
            businessId,
            storeId: input.storeId,
            isTemplate: true,
            draftData: sanitizeDraftSchedule(template),
            draftVersion: 1,

            // the following data is not used for templates, but we need to set it to something
            year: template.week.year,
            week: template.week.week,
            storeHoursStart: timeToDbTime(template.storeHours.start),
            storeHoursEnd: timeToDbTime(template.storeHours.end),
            dayParts: template.dayParts,
            peakHours: template.peakHours,
            isPublished: false
          }
        })
      }
    }),

  deleteScheduleTemplate: employedStatsProcedure({feature: "common"})
    .input(z.object({
      id: z.string()
    }))
    .output(z.void())
    .mutation(async ({ctx, input}) => {
      const {businessId, employment} = throwIfNotEmployedByBusiness(ctx);
      const sched = await ctx.prisma.schedule.findUniqueOrThrow({
        where: {
          id: input.id,
          businessId,
          storeId: {in: map(employment.stores, s => s.storeId)}
        },
        select: {
          id: true,
          storeId: true
        }
      });

      throwIfDenied({
        ctx, principalEmployment: employment,
        action: "store/deleteScheduleTemplate",
        resource: `business/${businessId}/store/${sched.storeId}`,
        resourceEntity: {
          storeId: sched.storeId,
        }
      });

      await ctx.prisma.schedule.deleteMany({
        where: {
          id: input.id,
          businessId,
          storeId: {in: map(employment.stores, s => s.storeId)},
          isTemplate: true
        }
      })
    }),

  submitAppFeedback: verifiedProcedure
    .input(z.object({
      feedback: z.string(),
      storeId: z.string(),
    }))
    .mutation(async ({ctx, input}) => {
      if (!input.feedback.trim().length) {
        return;
      }

      const user = await ctx.prisma.user.findFirst({
        where: {
          id: ctx.auth.user.id,
        },
        include: {
          person: true,
        }
      });

      const mainEmployment = getPersonMainEmployment(ctx.auth.user.person.employments);
      const jobTitle = mainEmployment?.job.title;

      const store = await ctx.prisma.store.findFirstOrThrow({
        where: {
          id: input.storeId,
        },
        include: {
          address: true,
        }
      });

      const {city, state, zipCode} = store.address;
      const storeAddress = `${city}, ${state} ${zipCode}`

      const defaultRecipients = [
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
      ];
      const recipients = process.env.FEEDBACK_RECIPIENTS
        ? process.env.FEEDBACK_RECIPIENTS.split(",")
        : defaultRecipients;

      ctx.sendEmail({
        to: recipients,
        subject: "Nation Feedback Received",
        bodyHtml: `<b>User</b>: ${user?.person.firstName} ${user?.person.lastName}${jobTitle ? `, ${jobTitle}` : ""} <br />
        <b>Location</b>: ${store.title}, ${storeAddress} <br />
        <b>ph:</b> ${user?.person.phoneNumber ?? "--"}, <b>email</b>: ${user?.person.email ?? "--"} <br /><br />
        <b>Feedback</b>: "<i>${input.feedback}</i>" <br /> <br />
        ---------------------------------------<br />
        user id:      ${ctx.auth.user.id} <br />
        store id:     ${input.storeId} <br />
        business id:  ${store.businessId} <br />
        ---------------------------------------`,
      })
    }),

  /**
   * Create a new time off request as a team member in the mobile app.
   */
  createTimeOffRequest: employedStatsProcedure({feature: "common"})
    .input(z.object({
      id: z.string(),
      storeId: z.string(),
      range: dateTimeRange,
      timeOffType: z.string().min(0).max(256),
      submittedReason: z.string().min(0).max(1024),
    }))
    .output(z.void())
    .mutation(async ({ctx, input}) => {
      // TODO permissions
      const storeId = toSecureStoreIdOrThrow(ctx, input.storeId);
      const {
        scheduleEvents,
        timeOffTypes,
        otherTimeOff,
        timeOffRestrictions,
        timezone, submittedByPersonId, submitterCanBypassTimeOffRestriction
      } = await loadTimeOffContext({
        storeId,
        ctx,
        input,
        timeOffForPersonId: ctx.currentPersonId
      });

      const command: CreateTimeOffRequestCommand = {
        type: "CreateTimeOffRequestCommand",
        id: input.id,
        timezone: timezone,
        storeId,
        overlappingScheduleEvents: scheduleEvents,
        otherTimeOffRequests: otherTimeOff,
        submittedByPersonId, submitterCanBypassTimeOffRestriction,
        range: input.range,
        reason: input.submittedReason,
        timeOffType: input.timeOffType,
        timeOffTypes,
        now: DateTime.now(),
        forPersonId: ctx.currentPersonId,
        timeOffRestrictions: timeOffRestrictions
      };

      await handleCommandAndUpdateProjections(ctx, command);
    }),

  /**
   * Create a time off request as a manager in the web app.
   */
  createTimeOffRequestAdmin: employedStatsProcedure({feature: "common"})
    .input(z.object({
      id: z.string(),
      personId: z.string(),
      storeId: z.string(),
      range: dateTimeRange,
      timeOffType: z.string().min(0).max(256),
      submittedReason: z.string().min(0).max(1024),
    }))
    .output(z.void())
    .mutation(async ({ctx, input}) => {
      const {businessId, employment} = ctx;

      throwIfDenied({
        ctx, principalEmployment: employment,
        action: "store/createTimeOffRequestsForOthers",
        resource: `business/${businessId}/store/${input.storeId}`,
        resourceEntity: {
          storeId: input.storeId,
        }
      });

      const storeId = toSecureStoreIdOrThrow(ctx, input.storeId);
      const {
        scheduleEvents,
        timeOffTypes,
        otherTimeOff,
        timeOffRestrictions,
        submittedByPersonId, submitterCanBypassTimeOffRestriction,
        timezone
      } = await loadTimeOffContext({
        storeId,
        ctx,
        input,
        timeOffForPersonId: input.personId
      });

      const command: CreateTimeOffRequestCommand = {
        type: "CreateTimeOffRequestCommand",
        id: input.id,
        timezone: timezone,
        storeId,
        overlappingScheduleEvents: scheduleEvents,
        otherTimeOffRequests: otherTimeOff,
        submittedByPersonId, submitterCanBypassTimeOffRestriction,
        range: input.range,
        reason: input.submittedReason,
        timeOffType: input.timeOffType,
        timeOffTypes,
        now: DateTime.now(),
        forPersonId: input.personId,
        timeOffRestrictions: timeOffRestrictions
      };

      await handleCommandAndUpdateProjections(ctx, command);
    }),

  /**
   * For a team member using the mobile app, get all time off requests
   * that have ranges in the future
   */
  getTimeOffRequests: employedStatsProcedure({feature: "common"})
    .input(z.object({
      storeId: z.string(),
    }))
    .output(z.object({
      items: z.array(timeOffDto)
    }))
    .query(async ({ctx, input}) => {
      const {businessId, employment} = throwIfNotEmployedByBusiness(ctx);
      // TODO permissions
      if (!some(employment.stores, s => s.storeId === input.storeId)) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "You are not employed at this store"
        });
      }

      const timeOffRequests = await ctx.prisma.personTimeOff.findMany({
        where: {
          personId: ctx.auth.user.person.id,
          storeId: input.storeId,
          OR: [{
            start: {gte: utcNow()},
          }, {
            end: {gte: utcNow()},
          }]
        }
      });

      return {
        items: orderBy(map(timeOffRequests, toTimeOffDto), req => req.range.start)
      };
    }),

  getTimeOffRequest: employedStatsProcedure({feature: "common"})
    .input(z.object({
      storeId: z.string(),
      timeOffId: z.string(),
    }))
    .output(timeOffAdminDto.optional())
    .query(async ({ctx, input}) => {
      const {businessId, employment} = throwIfNotEmployedByBusiness(ctx);
      // TODO permissions
      if (!some(employment.stores, s => s.storeId === input.storeId)) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "You are not employed at this store"
        });
      }

      const timeOffRequest = await ctx.prisma.personTimeOff.findUniqueOrThrow({
        where: {
          id: input.timeOffId,
          storeId: input.storeId,
          personId: ctx.auth.user.person.id,
        },
        include: {
          approvedByPerson: personWithJobAndImageIncludes,
          declinedByPerson: personWithJobAndImageIncludes,
          person: personWithJobAndImageIncludes,
          submittedByPerson: personWithJobAndImageIncludes
        }
      });

      if (!timeOffRequest) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Time off request not found"
        });
      }

      return toTimeOffAdminDto(ctx, timeOffRequest);
    }),

  /**
   * Cancel a time off request for a team member using the mobile app. E.g. I thought I needed to take time off
   * because my dog was sick, but it turns out he just ate a sock and he's fine now.
   */
  cancelTimeOffRequest: employedStatsProcedure({feature: "common"})
    .input(z.object({
      id: z.string(),
      reason: z.string().min(0).max(4000).optional()
    }))
    .output(z.void())
    .mutation(async ({ctx, input}) => {
      const {businessId, employment} = ctx;
      // TODO permissions

      const command: CancelTimeOffRequestCommand = {
        type: "CancelTimeOffRequestCommand",
        id: input.id,
        cancelledAt: new Date(),
        cancelledByPersonId: ctx.currentPersonId,
        cancelledReason: input.reason,
      }

      await handleCommandAndUpdateProjections(ctx, command);
    }),

  /**
   * Approve time off request
   */
  approveTimeOffRequest: employedStatsProcedure({feature: "common"})
    .input(z.object({
      id: z.string(),
      reason: z.string().min(0).max(4000).optional()
    }))
    .output(z.void())
    .mutation(async ({ctx, input}) => {
      const {businessId, employment, checkAllowed} = ctx;
      const timeOffRow = await ctx.prisma.personTimeOff.findUniqueOrThrow({
        where: {
          id: input.id,
          storeId: {in: map(employment.stores, s => s.storeId)}
        },
        include: {
          person: {
            include: {
              notificationSettings: true,
              user: {
                include: {
                  devicePushTokens: true
                }
              },
            }
          },
          store: true,
        }
      });

      const storeId = toSecureStoreIdOrThrow(ctx, timeOffRow.storeId);
      const hasPermission = checkAllowed({
        action: "store/approveTimeOffRequest",
        resource: `business/${businessId}/store/${storeId}`,
        resourceEntity: {
          storeId: storeId,
        }
      }).isAllowed;
      if (!hasPermission) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "You are not authorized to approve this time off request."
        });
      }

      const command: ApproveTimeOffRequestCommand = {
        type: "ApproveTimeOffRequestCommand",
        id: input.id,
        approvedAt: new Date(),
        approvedByPersonId: ctx.currentPersonId,
        approvedReason: input.reason,
      }

      await handleCommandAndUpdateProjections(ctx, command);

      // notify team member. Example:
      // "Nation: Jim Jones has approved your time off request for Sun, Dec 15 at 9:15AM to Mon, Dec 16 at 6:30PM."
      const timeZone = timeOffRow.store.timezone ?? "America/New_York";
      const localStartTime = DateTime.fromJSDate(timeOffRow.start, {zone: timeZone}).toFormat('ccc, LLL d \'at\' h:mma');
      const localEndTime = DateTime.fromJSDate(timeOffRow.end, {zone: timeZone}).toFormat('ccc, LLL d \'at\' h:mma');
      const message = `Nation: ${ctx.auth.user.person.firstName} ${ctx.auth.user.person.lastName} has approved your time off request for ${localStartTime} to ${localEndTime}.`;

      await notifyPerson({
        ctx,
        person: timeOffRow.person,
        message,
        subject: "Nation: Time off approved",
        deduplicationId: null,
        code: NotificationCode.timeOff.approved,
        entityIds: {
          storeId: timeOffRow.storeId,
          personId: timeOffRow.personId,
          personTimeOffId: timeOffRow.id,
        },
      })
    }),

  /**
   * Decline time off request
   */
  declineTimeOffRequest: employedStatsProcedure({feature: "common"})
    .input(z.object({
      id: z.string(),
      reason: z.string().min(0).max(4000)
    }))
    .output(z.void())
    .mutation(async ({ctx, input}) => {
      const {businessId, employment, checkAllowed} = ctx;
      const timeOffRow = await ctx.prisma.personTimeOff.findUniqueOrThrow({
        where: {
          id: input.id,
          storeId: {in: map(employment.stores, s => s.storeId)}
        },
        include: {
          person: {
            include: {
              notificationSettings: true,
              user: {
                include: {
                  devicePushTokens: true
                }
              },
            }
          },
          store: true,
        }
      });

      const hasPermission = checkAllowed({
        action: "store/approveTimeOffRequest",
        resource: `business/${businessId}/store/${timeOffRow.storeId}`,
        resourceEntity: {
          storeId: timeOffRow.storeId,
        }
      }).isAllowed;
      if (!hasPermission) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "You are not authorized to decline this time off request."
        });
      }

      const command: DeclineTimeOffRequestCommand = {
        type: "DeclineTimeOffRequestCommand",
        id: input.id,
        declinedAt: new Date(),
        declinedByPersonId: ctx.currentPersonId,
        declinedReason: input.reason,
      }

      await handleCommandAndUpdateProjections(ctx, command);

      // notify team member. Example:
      // "Nation: Jim Jones has declined your time off request for Sun, Dec 15 at 9:15AM to Mon, Dec 16 at 6:30PM.
      // Open the Nation app to review."
      const timeZone = timeOffRow.store.timezone ?? "America/New_York";
      const localStartTime = DateTime.fromJSDate(timeOffRow.start, {zone: timeZone}).toFormat('ccc, LLL d \'at\' h:mma');
      const localEndTime = DateTime.fromJSDate(timeOffRow.end, {zone: timeZone}).toFormat('ccc, LLL d \'at\' h:mma');

      // notify team member
      const message = `Nation: ${ctx.auth.user.person.firstName} ${ctx.auth.user.person.lastName} has declined your time off request for ${localStartTime} to ${localEndTime}. Open the Nation app to review.`;

      await notifyPerson({
        ctx,
        person: timeOffRow.person,
        message,
        subject: "Nation: Time off declined",
        deduplicationId: null,
        code: NotificationCode.timeOff.declined,
        entityIds: {
          storeId: timeOffRow.storeId,
          personId: timeOffRow.personId,
          personTimeOffId: timeOffRow.id,
        },
      })
    }),

  updateTimeOffRequestAdmin: employedStatsProcedure({feature: "common"})
    .input(z.object({
      id: z.string(),
      storeId: z.string(),
      range: dateTimeRange,
      timeOffType: z.string().min(0).max(256),
      reason: z.string().min(0).max(1024),
    }))
    .output(z.void())
    .mutation(async ({ctx, input}) => {
      const {businessId, employment} = ctx;
      const storeId = toSecureStoreIdOrThrow(ctx, input.storeId);

      throwIfDenied({
        ctx, principalEmployment: employment,
        action: "store/createTimeOffRequestsForOthers",
        resource: `business/${businessId}/store/${storeId}`,
        resourceEntity: {
          storeId: storeId,
        }
      });

      const request = await ctx.db.timeOff.getTimeOffRequest(storeId, input.id);

      const {
        scheduleEvents,
        timeOffTypes,
        otherTimeOff,
        timeOffRestrictions,
        timezone, submittedByPersonId, submitterCanBypassTimeOffRestriction
      } = await loadTimeOffContext({
        storeId,
        ctx,
        input,
        timeOffForPersonId: request.personId
      });

      const command: UpdateTimeOffRequestCommand = {
        type: "UpdateTimeOffRequestCommand",
        id: input.id,
        timezone: timezone,
        overlappingScheduleEvents: scheduleEvents,
        otherTimeOffRequests: otherTimeOff,
        submittedByPersonId, submitterCanBypassTimeOffRestriction,
        range: input.range,
        updatedReason: input.reason,
        timeOffType: input.timeOffType,
        timeOffTypes,
        now: DateTime.now(),
        timeOffRestrictions: timeOffRestrictions
      };

      await handleCommandAndUpdateProjections(ctx, command);
    }),

  publishDraftSchedule: employedStatsProcedure({feature: "common"})
    .input(z.object({
      scheduleId: z.string(),
      draftVersion: z.number(),
      changeReason: z.string().min(0).max(1024).optional(),
      excludeOpenShifts: z.boolean().optional(),
    }))
    .output(z.void())
    .mutation(async ({ctx, input}) => {
      const {businessId, checkAllowed, storeIds, employment} = ctx;
      const sched = await ctx.prisma.schedule.findUniqueOrThrow({
        where: {
          id: input.scheduleId,
          businessId,
          storeId: {in: map(employment.stores, s => s.storeId)},
          isTemplate: false,
        },
        select: {
          id: true,
          storeId: true
        }
      });

      const storeId = toSecureStoreIdOrThrow(ctx, sched.storeId);
      const canPublish = perm.canPublishSchedule(checkAllowed, {
        businessId,
        storeId: storeId,
      });

      if (!canPublish) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "You are not authorized to publish schedules"
        });
      }

      await publishScheduleInTransaction(ctx, {
        scheduleId: input.scheduleId,
        requestingPersonId: ctx.auth.user.person.id,
        storeIds: storeIds,
        draftVersion: input.draftVersion,
      }, async ({scheduleRev: schedule}) => {
        if (!schedule.draft) {
          return;
        }

        const allShifts = flatMap(schedule.draft.days, day => flatMap(day.areas, area => area.shifts));
        const allPublishedShifts = flatMap(schedule.published?.days, day => flatMap(day.areas, area => area.shifts));

        // a shift is "open" for the purposes of publishing if both the draft and published versions have no assignment
        const openShiftIds = chain(allShifts).filter(s => {
          const publishedShift = find(allPublishedShifts, p => p.id === s.id);
          return !Boolean(s.assignedPersonId) && !Boolean(publishedShift?.assignedPersonId);
        }).map(s => s.id).value();

        const newDraft = input.excludeOpenShifts ? excludeShiftsFromDraftSchedule({
          draft: schedule.draft,
          excludedShiftIds: new Set(openShiftIds),
          includedDays: [1, 2, 3, 4, 5, 6, 7],
          published: schedule.published,
        }) : schedule.draft;

        const isPartialDraft = input.excludeOpenShifts && !isEmpty(openShiftIds);

        return {
          draft: newDraft,
          changeReason: input.changeReason ?? "",
          discardDraft: !isPartialDraft,
        }
      });

    }),

  publishPartialDraftSchedule: employedStatsProcedure({feature: "common"})
    .input(z.object({
      scheduleId: z.string(),
      changeReason: z.string().min(0).max(1024).optional(),
      excludeShiftIds: z.array(z.string()),
      dayOfWeek: dayOfWeek,
      draftVersion: z.number(),
    }))
    .output(z.void())
    .mutation(async ({ctx, input}) => {
      const {businessId, employment, storeIds, checkAllowed} = ctx;
      const user = await getCurrentUser(ctx);

      await publishScheduleInTransaction(ctx, {
        scheduleId: input.scheduleId,
        storeIds: storeIds,
        requestingPersonId: ctx.auth.user.person.id,
        draftVersion: input.draftVersion,
      }, async ({scheduleRev: schedule, storeId}) => {
        if (!schedule.draft) {
          return;
        }

        const partialDraft = excludeShiftsFromDraftSchedule({
          draft: schedule.draft,
          excludedShiftIds: new Set(input.excludeShiftIds),
          includedDays: [input.dayOfWeek],
          published: schedule.published,
        });

        const canPublish = perm.canPublishScheduleAsShiftLead(checkAllowed, {
          draft: partialDraft,
          schedule: schedule,
          storeId,
          businessId,
          user
        });
        if (!canPublish) {
          throw new TRPCError({
            code: "FORBIDDEN",
            message: "You are not authorized to publish schedules"
          });
        }

        return {
          draft: partialDraft,
          changeReason: input.changeReason ?? "",
          discardDraft: false,
        }
      })
    }),

  /**
   * For a scheduler using the web app, get all time off requests in the store that have
   * ranges in the future.
   */
  getStoreTimeOffRequests: employedStatsProcedure({feature: "common"})
    .input(z.object({
      storeId: z.string(),
      status: timeOffStatusFilter.default("all")
    }))
    .output(z.object({
      items: z.array(timeOffAdminDto)
    }))
    .query(async ({ctx, input}) => {
      const {businessId, checkAllowed} = ctx;
      const storeId = toSecureStoreIdOrThrow(ctx, input.storeId);
      const canList = perm.canListTimeOffRequests(checkAllowed, {
        businessId,
        storeId: storeId,
      });

      if (!canList) {
        return {items: []}
      }

      const timeOffRequests = await ctx.prisma.personTimeOff.findMany({
        where: {
          ...getTimeOffStatusWhereCondition(input.status, {}),
          storeId: input.storeId,
        },
        include: {
          person: personWithJobAndImageIncludes,
          approvedByPerson: personWithJobAndImageIncludes,
          declinedByPerson: personWithJobAndImageIncludes,
          submittedByPerson: personWithJobAndImageIncludes,
        },
        orderBy: {
          submittedAt: "desc"
        }
      });

      return {
        items: map(timeOffRequests, toReq => {
          return toTimeOffAdminDto(ctx, toReq);
        })
      };
    }),

  getStoreTimeOffRequestsAdvanced: employedStatsProcedure({feature: "common"})
    .input(z.object({
      storeId: z.string(),
      status: timeOffStatusFilter.default("all"),
      pastDays: z.number().int().min(0).max(365).optional().default(0),
      dateRange: dateTimeRange.optional(),
    }))
    .output(z.object({
      items: z.array(timeOffAdminDto)
    }))
    .query(async ({ctx, input}) => {
      const {businessId, checkAllowed} = ctx;
      const storeId = toSecureStoreIdOrThrow(ctx, input.storeId);
      const canList = perm.canListTimeOffRequests(checkAllowed, {
        businessId,
        storeId: storeId,
      });

      if (!canList) {
        return {items: []}
      }

      const timeOffRequests = await ctx.prisma.personTimeOff.findMany({
        where: {
          ...getTimeOffStatusWhereCondition(input.status, {
            dateRange: input.dateRange,
            pastDays: input.pastDays
          }),
          storeId: input.storeId,
        },
        include: {
          person: personWithJobAndImageIncludes,
          approvedByPerson: personWithJobAndImageIncludes,
          declinedByPerson: personWithJobAndImageIncludes,
          submittedByPerson: personWithJobAndImageIncludes,
        },
        orderBy: {
          submittedAt: "desc"
        }
      });

      return {
        items: map(timeOffRequests, toReq => {
          return toTimeOffAdminDto(ctx, toReq);
        })
      };
    }),

  getStoreTimeOffRequestDetails: employedStatsProcedure({feature: "common"})
    .input(z.object({
      id: z.string()
    }))
    .output(z.object({
      request: timeOffAdminDto,
      lastRequests: z.array(timeOffAdminDto),
      conflictingShifts: z.array(shiftConflictDto),
      timezone: z.string().optional()
    }))
    .query(async ({ctx, input}) => {
      const {businessId, checkAllowed} = ctx;
      const timeOffRequestRow = await ctx.prisma.personTimeOff.findUniqueOrThrow({
        where: {
          id: input.id,
        },
        include: {
          store: true,
          person: personWithJobAndImageIncludes,
          approvedByPerson: personWithJobAndImageIncludes,
          declinedByPerson: personWithJobAndImageIncludes,
          submittedByPerson: personWithJobAndImageIncludes,
        },
      });

      const storeId = toSecureStoreIdOrThrow(ctx, timeOffRequestRow.storeId);
      const canList = perm.canListTimeOffRequests(checkAllowed, {
        businessId,
        storeId: storeId,
      });

      if (!canList) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "You are not authorized to view this time off request."
        });
      }

      const timeOffRequest = hydrateTimeOff(timeOffRequestRow);

      const conflictingShifts = getTimeOffStatus(timeOffRequestRow) === "pending"
        ? await queryShiftConflicts({
          prisma: ctx.prisma,
          personId: timeOffRequest.personId,
          isConflict: ({schedRev: sched, shift, day}) => isWithinTimeOff(shiftRangeToDateRange({
            ...sched.published!.week,
            day: day.dayOfWeek,
            range: shift.range,
            timezone: timeOffRequestRow.store.timezone
          }), [timeOffRequest.range]),
          onOrAfter: new UTCDate(),
          timezone: timeOffRequestRow.store.timezone
        })
        : [];

      // the 10 most recent time off requests for this person at this store
      const otherTimeOffRows = await ctx.prisma.personTimeOff.findMany({
        where: {
          id: {not: timeOffRequest.id},
          personId: timeOffRequest.personId,
          storeId: timeOffRequest.storeId,
          isSubmitted: true,
        },
        orderBy: {
          submittedAt: "desc"
        },
        take: 10,
        include: {
          person: personWithJobAndImageIncludes,
          approvedByPerson: personWithJobAndImageIncludes,
          declinedByPerson: personWithJobAndImageIncludes,
          submittedByPerson: personWithJobAndImageIncludes,
        }
      });

      return {
        timezone: timeOffRequestRow.store.timezone ?? undefined,
        request: toTimeOffAdminDto(ctx, timeOffRequestRow, timeOffRequestRow.store.id),
        conflictingShifts,
        lastRequests: map(otherTimeOffRows, toReq => {
          return toTimeOffAdminDto(ctx, toReq);
        })
      }
    }),

  getScheduleNotes: employedStatsProcedure({feature: "common"})
    .input(z.object({
      scheduleId: z.string(),
    }))
    .output(z.array(scheduleNoteDto))
    .query(async ({ctx, input}) => {
      const {businessId, storeIds, checkAllowed} = ctx;
      const sched = await ctx.db.scheduling.getScheduleRevision(businessId, {
        storeIds: storeIds,
        scheduleId: input.scheduleId
      });

      const canGetScheduleNotes = perm.canGetScheduleNotes(checkAllowed, {
        businessId,
        storeId: sched.storeId,
      });

      if (!canGetScheduleNotes) {
        return [];
      }

      const notes = await ctx.db.note.getScheduleNotesForSchedule(businessId, sched.storeId, {
        scheduleId: input.scheduleId
      });

      return map(notes, n => hydrateScheduleNoteDto(ctx, n));
    }),

  createScheduleNote: employedStatsProcedure({feature: "common"})
    .input(z.object({
      noteId: z.string(),
      scheduleId: z.string(),
      note: z.string().min(0).max(4096).trim(),
      dayOfWeek: dayOfWeek
    }))
    .output(z.void())
    .mutation(async ({ctx, input}) => {
      const {businessId, storeIds, employment} = throwIfNotEmployedByBusiness(ctx);
      const storeId = await ctx.db.scheduling.getScheduleStoreId(storeIds, input.scheduleId);
      if (!storeId) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: `Could not find store ID ${input.scheduleId}`,
        });
      }

      if (!ctx.checkAllowed({
        action: "store/createScheduleNotes",
        resource: `business/${businessId}/store/${storeId}`,
        resourceEntity: { storeId },
      }).isAllowed
      ) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "You are not authorized to create schedule notes",
        });
      }

      await ctx.db.note.createScheduleNote(businessId, storeId, {
        scheduleId: input.scheduleId,
        noteId: input.noteId,
        note: input.note,
        createdByPersonId: ctx.auth.user.person.id,
        dayOfWeek: input.dayOfWeek,
      })
    }),

  editScheduleNote: employedStatsProcedure({feature: "common"})
    .input(z.object({
      noteId: z.string(),
      scheduleId: z.string(),
      note: z.string().min(0).max(4096).trim(),
    }))
    .output(z.void())
    .mutation(async ({ctx, input}) => {
      const {businessId, storeIds, employment} = throwIfNotEmployedByBusiness(ctx);
      const storeId = await ctx.db.scheduling.getScheduleStoreId(storeIds, input.scheduleId);
      if (!storeId) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: `Could not find store ID ${input.scheduleId}`,
        });
      }

      if (!ctx.checkAllowed({
        action: "store/createScheduleNotes",
        resource: `business/${businessId}/store/${storeId}`,
        resourceEntity: { storeId },
      }).isAllowed
      ) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "You are not authorized to create schedule notes",
        });
      }

      await ctx.db.note.updateScheduleNote(businessId, storeId, {
        scheduleId: input.scheduleId,
        noteId: input.noteId,
        note: input.note,
        updatedById: ctx.auth.user.person.id,
      });
    }),

  deleteScheduleNote: employedStatsProcedure({feature: "common"})
    .input(z.object({
      noteId: z.string(),
      scheduleId: z.string(),
    }))
    .output(z.void())
    .mutation(async ({ctx, input}) => {
      const {businessId, storeIds, employment} = throwIfNotEmployedByBusiness(ctx);
      const storeId = await ctx.db.scheduling.getScheduleStoreId(storeIds, input.scheduleId);
      if (!storeId) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: `Could not find store ID ${input.scheduleId}`,
        });
      }

      if (!ctx.checkAllowed({
          action: "store/createScheduleNotes",
          resource: `business/${businessId}/store/${storeId}`,
          resourceEntity: { storeId },
        }).isAllowed
      ) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "You are not authorized to create schedule notes",
        });
      }

      await ctx.db.note.deleteScheduleNote(businessId, storeId, {
        noteId: input.noteId,
        scheduleId: input.scheduleId,
      })
    }),

  getShiftOfferDetails: employedStatsProcedure({feature: "common"})
    .input(z.object({
      id: z.string()
    }))
    .output(z.object({
      offer: shiftOfferDto,
      timezone: z.string()
    }))
    .query(async ({ctx, input}) => {
      const {businessId, employment} = throwIfNotEmployedByBusiness(ctx);
      const offerRow = await ctx.prisma.shiftOffer.findUniqueOrThrow({
        where: {
          id: input.id,
          storeId: {in: map(employment.stores, s => s.storeId)},
        },
        include: {
          store: true,
          offerorPerson: personWithJobAndImageIncludes,
          offereePersons: true,
          approvedByPerson: personWithJobAndImageIncludes,
          declinedByPerson: personWithJobAndImageIncludes,
          cancelledByPerson: personWithJobAndImageIncludes,
          acceptances: {
            include: {
              acceptorPerson: personWithJobAndImageIncludes
            },
            orderBy: {
              acceptedAt: "asc"
            }
          },
          shift: {
            include: {
              shiftArea: true,
              shiftActivities: true,
            }
          },
          approvedAcceptorPerson: personWithJobAndImageIncludes,
        },
      });

      return {
        offer: toShiftOfferDto(ctx, offerRow),
        timezone: offerRow.store.timezone ?? "America/New_York"
      };
    }),

  getShiftOffers: employedStatsProcedure({feature: "common"})
    .input(z.object({
      storeId: z.string(),
      status: shiftOfferStatusFilter.default("pending"),
      offerorPersonId: z.string().optional(),
      appVersion: z.string().optional(),
    }))
    .output(z.object({
      items: z.array(shiftOfferDto)
    }))
    .query(async ({ctx, input}) => {
      const {businessId, employment} = throwIfNotEmployedByBusiness(ctx);

      if (!some(employment.stores, s => s.storeId === input.storeId)) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "You are not employed at this store"
        });
      }

      const offerRows = await ctx.prisma.shiftOffer.findMany({
        where: {
          ...getShiftOfferStatusWhereCondition(input.status, {
            offerorPersonId: input.offerorPersonId,
          }),
          storeId: input.storeId,
          // Older app versions don't pass in the appVersion. Those older versions cannot handle house shift offers (i.e. those offers that don't have an offerorPersonId). So don't return them if that is the case.
          ...!input.appVersion ? {offerorPersonId: {not: null}} : {},
        },
        include: {
          offerorPerson: personWithJobAndImageIncludes,
          offereePersons: true,
          approvedByPerson: personWithJobAndImageIncludes,
          declinedByPerson: personWithJobAndImageIncludes,
          cancelledByPerson: personWithJobAndImageIncludes,
          acceptances: {
            include: {
              acceptorPerson: personWithJobAndImageIncludes
            }
          },
          shift: {
            include: {
              shiftArea: true,
              shiftActivities: true,
            }
          },
          approvedAcceptorPerson: personWithJobAndImageIncludes,
        },
        orderBy: {
          submittedAt: "desc"
        }
      });

      return {
        items: map(offerRows, row => {
          return toShiftOfferDto(ctx, row)
        })
      };

    }),

  getShiftOffersAdvanced: employedStatsProcedure({feature: "common"})
    .input(z.object({
      storeId: z.string(),
      status: shiftOfferStatusFilter.default("pending"),
      offerorPersonId: z.string().optional(),
      pastDays: z.number().int().min(0).max(365).optional().default(0),
      dateRange: dateTimeRange.optional(),
      appVersion: z.string().optional(),
    }))
    .output(z.object({
      items: z.array(shiftOfferDto)
    }))
    .query(async ({ctx, input}) => {
      const {businessId, employment} = throwIfNotEmployedByBusiness(ctx);

      const checkAllowed = isAllowedFactory({
        businessId,
        principalEmployment: employment,
        userId: ctx.auth.user.id,
      });

      const canList = checkAllowed({
        action: "store/listShiftOffers",
        resource: `business/${businessId}/store/${input.storeId}`,
        resourceEntity: {
          storeId: input.storeId,
        }
      }).isAllowed;

      if (!some(employment.stores, s => s.storeId === input.storeId)) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "You are not employed at this store"
        });
      }

      const offerRows = await ctx.prisma.shiftOffer.findMany({
        where: {
          ...getShiftOfferStatusWhereCondition(input.status, {
            offerorPersonId: input.offerorPersonId,
            dateRange: input.dateRange,
            pastDays: input.pastDays,
          }),
          storeId: input.storeId,
          // Older app versions don't pass in the appVersion. Those older versions cannot handle house shift offers (i.e. those offers that don't have an offerorPersonId). So don't return them if that is the case.
          ...!input.appVersion ? {offerorPersonId: {not: null}} : {},
        },
        include: {
          offerorPerson: personWithJobAndImageIncludes,
          offereePersons: true,
          approvedByPerson: personWithJobAndImageIncludes,
          declinedByPerson: personWithJobAndImageIncludes,
          cancelledByPerson: personWithJobAndImageIncludes,
          acceptances: {
            include: {
              acceptorPerson: personWithJobAndImageIncludes
            }
          },
          shift: {
            include: {
              shiftArea: true,
              shiftActivities: true,
            }
          },
          approvedAcceptorPerson: personWithJobAndImageIncludes,
        },
        orderBy: {
          submittedAt: "desc"
        }
      });

      return {
        items: map(offerRows, row => {
          return toShiftOfferDto(ctx, row)
        })
      };
    }),

  getPendingShiftOfferAcceptances: employedStatsProcedure({feature: "common"})
    .input(z.object({
      storeId: z.string(),
    }))
    .output(z.object({
      items: z.array(shiftDto)
    }))
    .query(async ({ctx, input}) => {
      const {businessId, employment} = throwIfNotEmployedByBusiness(ctx);
      // TODO permissions

      const offerRows = await ctx.prisma.shiftOffer.findMany({
        where: {
          storeId: input.storeId,
          isSubmitted: true,
          isApproved: false,
          isDeclined: false,
          isCancelled: false,
          acceptances: {
            some: {
              acceptorPersonId: ctx.auth.user.person.id,
            }
          },
        },
        include: {
          shift: {
            include: {
              shiftArea: true,
              shiftActivities: true
            }
          },
        }
      });

      return {
        items: map(filter(offerRows, offer => offer.shift !== null), offer => {
          const offerShift = toShiftDto(offer.shift!);
          if (offer.start && offer.end) {
            return {
              ...offerShift,
              range: {
                start: dateTo24HrTime(offer.start),
                end: dateTo24HrTime(offer.end)
              }
            }
          }
          return offerShift;
        })
      };
    }),

  approveShiftOffer: employedStatsProcedure({feature: "common"})
    .input(z.object({
      id: z.string(),
      reason: z.string().min(0).max(4000).optional(),
      // the person that the approver has selected to accept the shift offer. Has to
      // be a person that accepted the shift offer
      acceptorPersonId: z.string()
    }))
    .output(z.void())
    .mutation(async ({ctx, input}) => {
      const {businessId, employment, storeIds, checkAllowed} = ctx;
      const scheduleId = await ctx.db.scheduling.getShiftOfferScheduleId(storeIds, {offerId: input.id});

      await publishScheduleInTransaction(ctx, {
        scheduleId: scheduleId,
        storeIds,
        requestingPersonId: ctx.auth.user.person.id,
        draftVersion: undefined,
      }, async ({prisma, scheduleRev}) => {
        const offerRow = await ctx.db.scheduling.getShiftOffer(storeIds, {offerId: input.id});
        const acceptorPerson = await prisma.person.findUniqueOrThrow({
          where: {
            id: input.acceptorPersonId,
            businessId: businessId
          }
        })

        if (!scheduleRev.published) {
          throw new TRPCError({
            code: "BAD_REQUEST",
            message: "Cannot approve shift offer for a schedule that has not been published"
          });
        }

        const shiftOffer = hydrateShiftOffer(offerRow);
        if (!shiftOffer.shiftId) {
          throw new TRPCError({
            code: "BAD_REQUEST",
            message: "Cannot approve shift offer for a non-existent shift"
          });
        }

        const canApprove = perm.canApproveShiftOffer(checkAllowed, {
          businessId,
          schedule: scheduleRev.published,
          personId: ctx.auth.user.person.id,
          storeId: offerRow.storeId,
          shiftId: shiftOffer.shiftId
        });
        if (!canApprove) {
          throw new TRPCError({
            code: "FORBIDDEN",
            message: "You are not authorized to approve this shift offer."
          });
        }

        const acceptedShiftOffer = toAcceptedShiftOffer(shiftOffer);
        const {
          shiftOffer: approvedShiftOffer,
          toPublish: draftToPublish,
          draft: updatedDraft
        } = approveShiftOffer(acceptedShiftOffer, {
          approvedAt: utcNow(),
          approvedReason: input.reason,
          approvedByPersonId: ctx.auth.user.person.id,
          acceptorPersonId: acceptorPerson.id,
          scheduleRevision: scheduleRev
        });

        await prisma.shiftOffer.update({
          where: {
            id: approvedShiftOffer.id,
          },
          data: toShiftOfferUpdateInput(approvedShiftOffer)
        });

        // edit the draft schedule too
        if (scheduleRev.draft && updatedDraft) {
          const newDraftData = sanitizeDraftSchedule(updatedDraft);
          await prisma.schedule.update({
            where: {
              id: scheduleRev.draft.id,
              draftVersion: scheduleRev.draftVersion
            },
            data: {
              draftVersion: {increment: 1},
              draftData: newDraftData
            }
          })
        }

        return {
          draft: draftToPublish,
          changeReason: `Approving shift offer` + (input.reason ? ": " + input.reason : ""),
          discardDraft: false,
        }
      });

      const offerRow = await ctx.db.scheduling.getShiftOffer(storeIds, {offerId: input.id});

      // notify team member
      const forTheShiftSuffix = offerRow.shift?.startAbs ? ` for the shift on ${formatDateForNotification(DateTime.fromJSDate(offerRow.shift.startAbs, {zone: offerRow.store.timezone ?? "America/New_York"}))}.` : ".";

      if (offerRow.offerorPerson) {
        await notifyPerson({
          ctx,
          person: offerRow.offerorPerson,
          message: `Nation: ${ctx.auth.user.person.firstName} ${ctx.auth.user.person.lastName} has approved your shift offer${forTheShiftSuffix}`,
          subject: "Nation: Shift offer approved",
          preferences: {
            pushColumn: "receiveShiftOfferUpdates",
            smsColumn: "receiveShiftOfferUpdatesSMS"
          },
          deduplicationId: null,
          code: NotificationCode.shiftOffer.approved,
          entityIds: {
            storeId: offerRow.storeId,
            shiftOfferId: input.id
          },
        });
      }

      // Iterate through all the people who have accepted this shift offer and notify them that the shift has been taken
      if (offerRow.acceptances) {
        // Notify all people who accepted but weren't chosen
        for (const acceptance of offerRow.acceptances) {
          // Skip the person who was approved
          if (acceptance.acceptorPersonId === offerRow.approvedAcceptorPersonId) {
            continue;
          }

          notifyPerson({
            ctx,
            person: acceptance.acceptorPerson,
            message: `Nation: ${ctx.auth.user.person.firstName} ${ctx.auth.user.person.lastName} has declined your acceptance of shift offer${forTheShiftSuffix}`,
            subject: "Nation: Shift offer update",
            preferences: {
              pushColumn: "receiveShiftOfferUpdates",
              smsColumn: "receiveShiftOfferUpdatesSMS"
            },
            deduplicationId: null,
            code: NotificationCode.shiftOffer.declined,
            entityIds: {
              storeId: offerRow.storeId,
              shiftOfferId: input.id
            },
          });
        }
      }

      if (offerRow.approvedAcceptorPerson) {
        notifyPerson({
          ctx,
          person: offerRow.approvedAcceptorPerson,
          message: `Nation: ${ctx.auth.user.person.firstName} ${ctx.auth.user.person.lastName} has approved your acceptance of shift offer${forTheShiftSuffix}`,
          subject: "Nation: Shift offer approved",
          preferences: {
            pushColumn: "receiveShiftOfferUpdates",
            smsColumn: "receiveShiftOfferUpdatesSMS"
          },
          deduplicationId: null,
          code: NotificationCode.shiftOffer.acceptanceApproved,
          entityIds: {
            storeId: offerRow.storeId,
            shiftOfferId: input.id
          },
        })
      } else {
        Sentry.captureEvent({
          message: "Shift offer acceptor not found after approving it. What fresh hell is this?",
          extra: {shiftOfferId: input.id, acceptorPersonId: input.acceptorPersonId}
        })
        return;
      }
    }),

  declineShiftOffer: employedStatsProcedure({feature: "common"})
    .input(z.object({
      id: z.string(),
      reason: z.string().min(0).max(4000).optional(),
    }))
    .output(z.void())
    .mutation(async ({ctx, input}) => {
      const {businessId, employment} = throwIfNotEmployedByBusiness(ctx);

      await ctx.prisma.$transaction(async prisma => {
        const offerRow = await prisma.shiftOffer.findUniqueOrThrow({
          where: {
            id: input.id,
            storeId: {in: map(employment.stores, s => s.storeId)}
          },
          include: {
            offereePersons: true,
            acceptances: {
              include: {
                acceptorPerson: true
              }
            }
          }
        });

        const checkAllowed = isAllowedFactory({
          businessId,
          principalEmployment: employment,
          userId: ctx.auth.user.id,
        });

        const hasPermission = checkAllowed({
          action: "store/approveShiftOffer",
          resource: `business/${businessId}/store/${offerRow.storeId}`,
          resourceEntity: {
            storeId: offerRow.storeId,
          }
        }).isAllowed;
        if (!hasPermission) {
          throw new TRPCError({
            code: "FORBIDDEN",
            message: "You are not authorized to decline this shift offer."
          });
        }

        const declinedOffer = declineShiftOffer(toCancellableShiftOffer(hydrateShiftOffer(offerRow)), {
          declinedAt: utcNow(),
          declinedReason: input.reason,
          declinedByPersonId: ctx.auth.user.person.id
        });

        await prisma.shiftOffer.update({
          where: {
            id: declinedOffer.id,
          },
          data: toShiftOfferUpdateInput(declinedOffer)
        });
      });

      // notify team member
      const offerRow = await ctx.prisma.shiftOffer.findUnique({
        where: {
          id: input.id
        },
        include: {
          store: {
            select: {timezone: true}
          },
          shift: true,
          offerorPerson: {
            include: {
              user: {
                select: {
                  devicePushTokens: true,
                }
              },
              notificationSettings: true,
            }
          },
          acceptances: {
            include: {
              acceptorPerson: true
            }
          }
        }
      });
      if (!offerRow) {
        Sentry.captureEvent({
          message: "Shift offer not found after declining it.",
          extra: {shiftOfferId: input.id}
        })
        return;
      }

      const forTheShiftSuffix = offerRow.shift?.startAbs ? ` for your shift on ${formatDateForNotification(DateTime.fromJSDate(offerRow.shift.startAbs, {zone: offerRow.store.timezone ?? "America/New_York"}))}.` : ".";
      if (offerRow.offerorPerson) {
        await notifyPerson({
          ctx,
          person: offerRow.offerorPerson,
          message: `Nation: ${ctx.auth.user.person.firstName} ${ctx.auth.user.person.lastName} has declined your shift offer${forTheShiftSuffix} You are expected to work the shift.`,
          subject: "Nation: Shift offer declined",
          deduplicationId: null,
          code: NotificationCode.shiftOffer.declined,
          entityIds: {
            storeId: offerRow.storeId,
            shiftOfferId: input.id
          },
        });
      }
    }),

  cancelShiftOffer: employedStatsProcedure({feature: "common"})
    .input(z.object({
      id: z.string(),
      reason: z.string().min(0).max(4000).optional()
    }))
    .output(z.void())
    .mutation(async ({ctx, input}) => {
      const {businessId, employment} = throwIfNotEmployedByBusiness(ctx);
      const offerRow = await ctx.prisma.shiftOffer.findUniqueOrThrow({
        where: {
          id: input.id,
          offerorPersonId: ctx.auth.user.person.id,
        },
        include: {
          offereePersons: true,
          acceptances: {
            include: {
              acceptorPerson: true
            }
          }
        },
      });

      if (!offerRow) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Shift offer not found"
        });
      }

      const canceledShiftOffer = cancelShiftOffer(toCancellableShiftOffer(hydrateShiftOffer(offerRow)), {
        cancelledByPersonId: ctx.auth.user.person.id,
        cancelledAt: new Date(),
        cancelledReason: input.reason
      })

      await ctx.prisma.shiftOffer.update({
        where: {
          id: input.id,
        },
        data: toShiftOfferUpdateInput(canceledShiftOffer)
      });
    }),

  /**
   * Get the shifts and calendar events for the current user
   */
  getCalendar: employedStatsProcedure({feature: "common"})
    .input(z.object({
      storeId: z.string(),
      start: z.date(),
      end: z.date(),
      // provide personId to view the calendar for a specific person. Requires permission to view other people's shifts
      personId: z.string().optional(),
      // provide filters to view different types of data. Valid values are "shifts", "events", and "timeOff"
      filters: z.array(z.string()).optional(),
    }))
    .output(z.object({
      shifts: z.array(scheduleShiftDto),
      events: z.array(scheduleEventDto),
      timeOff: z.array(timeOffDto),
    }))
    .query(async ({ctx, input: input}) => {
      const {storeId, start, end} = input;
      const {businessId, employment} = throwIfNotEmployedByBusiness(ctx);
      const store = await ctx.prisma.store.findUniqueOrThrow({
        where: {
          id: storeId,
          businessId
        },
        include: {
          areas: {
            where: {isActive: true},
            orderBy: {order: 'asc'},
          },
        }
      });

      const timezone = store.timezone ?? "America/New_York";
      const weeks = getIsoWeeksOverlappingRange({start, end}, timezone);

      // get the calendar events that the user has access to
      const checkAllowed = isAllowedFactory({
        businessId,
        principalEmployment: employment,
        userId: ctx.auth.user.id,
      });

      // check if person can view other people's shifts
      if (input.personId && !checkAllowed({
        action: "store/getPublishedSchedule",
        resource: `business/${businessId}/store/${storeId}`,
        resourceEntity: {
          storeId: storeId,
        }
      }).isAllowed) {
        return {
          shifts: [],
          events: [],
          timeOff: [],
        };
      }

      const firstWeek = first(weeks);
      const lastWeek = last(weeks);
      const weekStartDate = firstWeek ? getDateFromWeekDayTime({
        year: firstWeek.year,
        week: firstWeek.week,
        day: 1,
        time: "00:00",
        timezone: timezone
      }) : null;

      const weekEndDate = lastWeek ? getDateFromWeekDayTime({
        year: lastWeek.year,
        week: lastWeek.week,
        day: 7,
        time: "23:59",
        timezone: timezone
      }) : null;

      const scheduleEvents = weekStartDate && weekEndDate ? await findScheduleEventsForRange({
        ctx: ctx,
        checkAllowed,
        storeId: store.id,
        businessId,
        prisma: ctx.prisma,
        range: {
          start: weekStartDate,
          end: weekEndDate
        }
      }) : [];

      // if viewing their own shifts (input.personId = undefined), does not need a permission check beyond employment, because a person should always be able to see their own shifts
      const personId = input.personId ?? ctx.auth.user.person.id;
      const schedules = await getSchedulesForDatesWithShiftsForPerson({
        prisma: ctx.prisma,
        businessId,
        personId: personId,
        storeId, start, end,
        timezone: timezone
      });
      const shifts = toScheduleShiftsDto({ctx, schedules, storeAreas: store.areas});

      // TODO permission for viewing other people's time off?
      const approvedTimeOff = weekStartDate && weekEndDate ? await ctx.prisma.personTimeOff.findMany({
        where: {
          personId: personId,
          storeId: store.id,
          isApproved: true,
          isCancelled: false,
          isDeclined: false,
          OR: [{
            start: {
              gte: weekStartDate,
              lte: weekEndDate
            }
          }, {
            end: {
              gte: weekStartDate,
              lte: weekEndDate
            }
          }, {
            start: {
              lt: weekStartDate
            },
            end: {
              gt: weekEndDate
            }
          }]
        },
      }) : [];

      const timeOff = map(approvedTimeOff, toTimeOffDto);
      const areFiltersActive = !isEmpty(input.filters);
      if (!areFiltersActive) {
        return {
          shifts,
          events: scheduleEvents,
          timeOff
        }
      }

      return {
        shifts: includes(input.filters, "shifts") ? shifts : [],
        events: includes(input.filters, "events") ? scheduleEvents : [],
        timeOff: includes(input.filters, "timeOff") ? timeOff : [],
      }
    }),

  // Get shifts for the current user
  getShifts: employedStatsProcedure({feature: "common"})
    .input(z.object({
      storeId: z.string(),
      start: z.date(),
      end: z.date(),
    }))
    .output(z.object({
      shifts: z.array(scheduleShiftDto),
    }))
    .query(async ({ctx, input: {storeId, start, end}}) => {
      const {businessId, employment} = throwIfNotEmployedByBusiness(ctx);
      const store = await ctx.prisma.store.findUniqueOrThrow({
        where: {
          id: storeId,
          businessId
        },
        include: {
          areas: {
            where: {isActive: true},
            orderBy: {order: 'asc'},
          },
        }
      });

      // does not need a permission check beyond employment, because a person should always be able to see their own shifts

      const schedules = await getSchedulesForDatesWithShiftsForPerson({
        prisma: ctx.prisma,
        businessId,
        personId: ctx.auth.user.person.id,
        storeId, start, end,
        timezone: store.timezone ?? "America/New_York"
      });
      const shifts = toScheduleShiftsDto({ctx, schedules, storeAreas: store.areas});

      return {
        shifts,
      }
    }),

  getPersonPublishedShifts: employedStatsProcedure({feature: "common"})
    .input(z.object({
      storeId: z.string(),
      start: z.date(),
      end: z.date(),
      personId: z.string()
    }))
    .output(z.object({
      shifts: z.array(scheduleShiftDto),
    }))
    .query(async ({ctx, input: {storeId, personId, start, end}}) => {
      const {businessId, employment} = throwIfNotEmployedByBusiness(ctx);

      // TODO does this need to check for get published schedule permissions?

      const store = await ctx.prisma.store.findUniqueOrThrow({
        where: {
          id: storeId,
          businessId
        },
        include: {
          areas: {
            where: {isActive: true},
            orderBy: {order: 'asc'},
          },
        }
      });

      const schedules = await getSchedulesForDatesWithShiftsForPerson({
        prisma: ctx.prisma,
        businessId,
        personId: personId,
        storeId, start, end,
        timezone: store.timezone ?? "America/New_York"
      });
      const shifts = toScheduleShiftsDto({ctx, schedules, storeAreas: store.areas});

      return {
        shifts,
      }
    }),

  getShiftDetails: employedStatsProcedure({feature: "common"})
    .input(z.object({
      storeId: z.string(),
      shiftId: z.string(),
    }))
    .output(scheduleShiftDto)
    .query(async ({ctx, input}) => {
      const {businessId, checkAllowed} = ctx;
      const storeId = toSecureStoreIdOrThrow(ctx, input.storeId);
      const shift = await ctx.db.scheduling.getShiftDetails(storeId, {shiftId: input.shiftId});

      const canView = perm.canViewShiftDetails(checkAllowed, {
        businessId,
        storeId,
        shiftAssignedPersonId: shift.assignedPersonId ?? undefined,
        currentPersonId: ctx.auth.user.person.id,
      });
      if (!canView) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "You are not assigned to this shift and don't have permission to view schedules."
        });
      }

      return shiftToDto({
        ctx,
        shift: shift,
        area: shift.shiftArea,
        dayOfWeek: shift.day ?? 1, // it SHOULD be defined always
        storeAreaTitle: shift.storeArea?.title,
      });
    }),

  offerShift: employedStatsProcedure({feature: "common"})
    .input(z.object({
      storeId: z.string(),
      shiftId: z.string(),
      reason: z.string(),
      range: dailyTimeRange.optional(),
    }))
    .output(z.void())
    .mutation(async ({ctx, input}) => {
      const {businessId, employment} = throwIfNotEmployedByBusiness(ctx);
      const storeEmployment = find(employment.stores, s => s.storeId === input.storeId);
      if (!storeEmployment) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Store employment not found"
        });
      }

      let scheduleId: string | undefined;
      const newShiftOfferId = genShiftOfferId();

      await ctx.prisma.$transaction(async prisma => {
        const scheduleRow = await prisma.schedule.findFirstOrThrow({
          where: {
            businessId,
            storeId: input.storeId,
            isTemplate: false,
            ScheduleDay: {
              some: {
                ShiftArea: {
                  some: {
                    Shift: {
                      some: {
                        id: input.shiftId
                      }
                    }
                  }
                }
              }
            }
          },
          include: {
            store: true,
            ScheduleDay: {
              include: {
                ShiftArea: {
                  include: {
                    Shift: {
                      include: {
                        shiftActivities: true,
                        ShiftOffer: {
                          include: {
                            offereePersons: true,
                            acceptances: true,
                          }
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        });

        const hydratedSchedule = hydrateSchedule(scheduleRow)
        const schedule = hydratedSchedule.published;
        if (!schedule) {
          throw new TRPCError({
            code: "BAD_REQUEST",
            message: "Schedule is not published"
          });
        }

        const shiftRow = find(flatMap(scheduleRow.ScheduleDay, day => flatMap(day.ShiftArea, area => flatMap(area.Shift, shift => shift))), s => s.id === input.shiftId);
        if (!shiftRow) {
          throw new TRPCError({
            code: "BAD_REQUEST",
            message: "Shift does not exist"
          })
        }

        if (some(shiftRow.ShiftOffer, (offer) => ["Accepted", "Pending"].includes(getShiftOfferStatus(offer)))) {
          throw new TRPCError({
            code: "BAD_REQUEST",
            message: "Shift offer already exists"
          })
        }

        if (input.range !== undefined) {
          const validationErrors = validateSplitShiftTimes(hydrateScheduleShift(shiftRow), input.range);
          if (validationErrors) {
            throw new TRPCError({
              code: "BAD_REQUEST",
              message: validationErrors
            })
          }
        }

        scheduleId = scheduleRow.id;
        const humanIdRes = await prisma.shiftOffer.aggregate({
          where: {
            storeId: input.storeId,
          },
          _max: {
            humanId: true
          }
        });
        const humanId = (humanIdRes._max.humanId ?? 1) + 1;

        const shiftOffer = createPendingShiftOffer({
          id: newShiftOfferId,
          shiftId: shiftRow.id,
          humanId,
          storeId: input.storeId,
          offerorPersonId: ctx.auth.user.person.id!,
          offereePersonIds: [],
          publishedScheduleVersion: hydratedSchedule.publishedVersion!,
          scheduleId: schedule.id,
          submittedAt: new Date(),
          submittedReason: input.reason,
          range: input.range,
        });

        await prisma.shiftOffer.create({
          data: toShiftOfferCreateInput(shiftOffer)
        });
      });

      await ctx.eventPublisher.publish({
        ...getBaseEvent({
          requestingPersonId: ctx.auth.user.person.id,
          occurredAt: new Date(),
          connections: {
            scheduleId: scheduleId,
            storeId: input.storeId,
            businessId,
            personId: ctx.auth.user.person.id,
          }
        }),
        type: "shiftOffered",
        data: {
          businessId,
          storeId: input.storeId,
          shiftId: input.shiftId,
          reason: input.reason,
          shiftOfferId: newShiftOfferId
        },
      });
    }),

  acceptShiftOffer: employedStatsProcedure({feature: "common"})
    .input(z.object({
      shiftOfferId: z.string(),
    }))
    .output(z.void())
    .mutation(async ({ctx, input}) => {
      const {businessId, employment} = throwIfNotEmployedByBusiness(ctx);
      const storeIds = map(employment.stores, s => s.storeId);
      // TODO new permissions

      const cachedShiftOfferRow = await ctx.prisma.$transaction(async prisma => {
        const shiftOfferRow = await prisma.shiftOffer.findUniqueOrThrow({
          where: {
            id: input.shiftOfferId,
            storeId: {in: storeIds},
            isSubmitted: true,
            isApproved: false,
            isDeclined: false,
            isCancelled: false,
          },
          include: {
            offereePersons: true,
            shift: true,
            acceptances: true,
          }
        });

        const shiftOffer = toCancellableShiftOffer(hydrateShiftOffer(shiftOfferRow));
        const acceptorPersonId = ctx.auth.user.person.id!;
        const acceptedShiftOffer = acceptShiftOffer(shiftOffer, {
          acceptedAt: utcNow(),
          acceptorPersonId: acceptorPersonId,
          comments: "",
        });

        await prisma.shiftOffer.update({
          where: {
            id: input.shiftOfferId,
          },
          data: toShiftOfferUpdateInput(acceptedShiftOffer)
        });
        return shiftOfferRow
      });

      const forTheShiftSuffix = cachedShiftOfferRow?.shift?.startAbs ? ` for the shift on ${formatDateForNotification(DateTime.fromJSDate(cachedShiftOfferRow.shift.startAbs))}.` : ".";

      // notify anyone with create Schedule permission package who is employed at the store
      const storeEmployments = await ctx.prisma.storeEmployment.findMany({
        where: {
          storeId: cachedShiftOfferRow.storeId,
        },
        include: {
          employment: {
            include: {
              person: {
                include: {
                  user: {
                    include: {
                      devicePushTokens: true
                    }
                  },
                  notificationSettings: {
                    where: {
                      storeId: cachedShiftOfferRow.storeId
                    }
                  },
                }
              },
              job: true
            }
          }
        }
      });

      const schedulersWithPermission = map(filter(storeEmployments, storeEmployment => {
        const policy = mergeJobAndPersonPolicies({
          personPolicy: storeEmployment.employment.permissionPolicy as unknown as DatabasePermissionPolicy,
          jobPolicy: storeEmployment.employment.job.permissionPolicy as unknown as DatabasePermissionPolicy
        });
        // the person needs to be able to create schedules
        return some(policy.statements, statement => isPermissionPackageStatement(statement)
          && statement.packageId === PackageIds.createSchedules);
      }), se => se.employment.person);

      Promise.all(map(schedulersWithPermission, scheduler =>
        notifyPerson({
          ctx,
          person: scheduler,
          message: `Nation: ${ctx.auth.user.person.firstName} ${ctx.auth.user.person.lastName} has accepted a shift offer${forTheShiftSuffix}`,
          subject: "Nation: A Shift Offer Has Been Accepted and is Pending Approval",
          preferences: {
            pushColumn: "receiveAdminShiftAndScheduleUpdates",
            smsColumn: "receiveAdminShiftAndScheduleUpdatesSMS"
          },
          deduplicationId: null,
          code: NotificationCode.shiftOffer.accepted,
          entityIds: {
            storeId: cachedShiftOfferRow.storeId,
            shiftOfferId: cachedShiftOfferRow.id
          }
        })
      ));
    }),

  cancelAcceptShiftOffer: employedStatsProcedure({feature: "common"})
    .input(z.object({
      storeId: z.string(),
      shiftId: z.string(),
    }))
    .output(z.void())
    .mutation(async ({ctx, input}) => {
      const {businessId, employment} = throwIfNotEmployedByBusiness(ctx);

      const shiftOfferRow = await ctx.prisma.shiftOffer.findFirstOrThrow({
        where: {
          storeId: input.storeId,
          isSubmitted: true,
          isAccepted: true,
          isApproved: false,
          isDeclined: false,
          isCancelled: false,
          acceptances: {
            some: {
              acceptorPersonId: ctx.auth.user.person.id,
            }
          },
        },
        include: {
          offereePersons: true,
          acceptances: true,
        }
      });

      const shiftOffer = toCancellableShiftOffer(hydrateShiftOffer(shiftOfferRow));
      const updatedShiftOffer = cancelShiftOfferAcceptance(shiftOffer, {
        acceptorPersonId: ctx.auth.user.person.id,
      });

      await ctx.prisma.shiftOffer.update({
        where: {
          id: shiftOffer.id,
        },
        data: toShiftOfferUpdateInput(updatedShiftOffer)
      });
    }),

  getTimeOffTypes: employedStatsProcedure({feature: "common"})
    .input(z.object({
      storeId: z.string(),
    }))
    .output(z.object({
      types: z.array(z.object({
        title: z.string(),
        isPaid: z.boolean(),
      }))
    }))
    .query(async ({ctx, input}) => {
      const {businessId, employment} = throwIfNotEmployedByBusiness(ctx);

      const store = await ctx.prisma.store.findUniqueOrThrow({
        where: {
          id: input.storeId,
          businessId
        },
      });

      const timeOffTypeRecords = await ctx.prisma.timeOffType.findMany({
        where: {
          storeId: store.id,
          isArchived: false,
        },
      });

      return {
        types: map(timeOffTypeRecords, r => ({
          title: r.title,
          isPaid: r.isPaid,
        }))
      };
    }),

  createTimeOffType:
    employedStatsProcedure({feature: "common"})
      .input(z.object({
        storeId: z.string(),
        title: z.string().min(1).max(100),
        isPaid: z.boolean(),
      }))
      .output(z.void())
      .mutation(async ({ctx, input}) => {
        const {businessId, employment} = throwIfNotEmployedByBusiness(ctx);
        const checkAllowed = isAllowedFactory({businessId, principalEmployment: employment, userId: ctx.auth.user.id})
        allAllowedOrThrow({
          checkAllowed,
          actions: ["store/update"],
          resource: `business/${businessId}/store/${input.storeId}`,
          resourceEntity: {
            businessId: businessId,
            storeId: input.storeId
          }
        });

        await ctx.prisma.timeOffType.upsert({
          where: {
            storeId_title: {
              storeId: input.storeId,
              title: input.title,
            }
          },
          update: {
            isArchived: false,
            isPaid: input.isPaid,
          },
          create: {
            storeId: input.storeId,
            title: input.title,
            isPaid: input.isPaid,
            isArchived: false,
          }
        });
      }),

  archiveTimeOffType: employedStatsProcedure({feature: "common"})
    .input(z.object({
      storeId: z.string(),
      title: z.string(),
    }))
    .output(z.void())
    .mutation(async ({ctx, input}) => {
      const {businessId, employment} = throwIfNotEmployedByBusiness(ctx);
      const checkAllowed = isAllowedFactory({businessId, principalEmployment: employment, userId: ctx.auth.user.id})
      allAllowedOrThrow({
        checkAllowed,
        actions: ["store/update"],
        resource: `business/${businessId}/store/${input.storeId}`,
        resourceEntity: {
          businessId: businessId,
          storeId: input.storeId
        }
      });

      await ctx.prisma.timeOffType.update({
        where: {
          storeId_title: {
            storeId: input.storeId,
            title: input.title,
          }
        },
        data: {
          isArchived: true
        }
      });
    }),

  unarchiveAndUndeletePerson: employedStatsProcedure({feature: "common"})
    .input(z.object({
      personId: z.string(),
    }))
    .output(z.void())
    .mutation(async ({ctx, input}) => {
      const {businessId, employment: requesterEmployment, storeIds} = ctx;
      const requesterStoreIds = map(requesterEmployment.stores, s => s.storeId);

      await ctx.prisma.$transaction(async prisma => {
        // 1. find the person
        const person = await prisma.person.findUniqueOrThrow({
          where: {
            id: input.personId,
            businessId,
            // can only un-archive people at stores you have access to
            OR: [{
              employments: {
                some: {
                  stores: {
                    some: {
                      storeId: {in: requesterStoreIds}
                    }
                  }
                }
              }
            }, {
              employmentRequests: {
                some: {
                  stores: {
                    some: {
                      storeId: {in: requesterStoreIds}
                    }
                  }
                }
              }
            }
            ]
          },
          include: {
            employments: true,
            employmentRequests: true,
            user: {
              select: {
                id: true
              }
            }
          }
        });

        const archivedPersonUserId = person.user?.id;
        throwIfDenied({
          ctx,
          principalEmployment: requesterEmployment,
          action: "person/unarchive",
          resource: `business/${businessId}/person/${person.id}`,
          resourceEntity: {
            id: person.id,
            businessId: person.businessId,
            ownerId: archivedPersonUserId,
          }
        });

        // 2. Mark the person as NOT archived
        await prisma.person.update({
          where: {
            id: input.personId,
            businessId,
          },
          data: {
            isArchived: false,
          }
        });

        // 3. mark the person's employment at this business and all its store employments as NOT ended
        const employment = getPersonMostRecentEmployment(person.employments);
        const employmentRequest = getPersonMostRecentEmploymentRequest(person.employmentRequests);

        if (employment) {
          await prisma.employment.update({
            where: {
              id: employment.id,
              personId: input.personId,
            },
            data: {
              endedAt: null,
              stores: {
                updateMany: {
                  where: {},
                  data: {
                    endedAt: null,
                  }
                }
              }
            }
          });
        } else if (employmentRequest) {
          await prisma.employmentRequest.update({
            where: {
              id: employmentRequest.id,
              personId: input.personId,
            },
            data: {
              endedAt: null,
              stores: {
                updateMany: {
                  where: {},
                  data: {
                    endedAt: null,
                  }
                }
              }
            }
          });
        } else {
          throw new TRPCError({
            code: "BAD_REQUEST",
            message: "Person is not employed at this business."
          });
        }

        // 4. un-suspend the user so that they can log in
        if (archivedPersonUserId) {
          await prisma.user.update({
            where: {
              id: archivedPersonUserId
            },
            data: {
              isSuspended: false,
            }
          });
        }
      });

      // purposefully not awaiting
      for (const storeId of storeIds) {
        reactivateChatUser(ctx, storeId, input.personId);
      }

      // 5. publish domain event
      await ctx.eventPublisher.publish({
        ...getBaseEvent({
          requestingPersonId: ctx.auth.user.person.id,
          connections: {
            businessId,
            personId: input.personId,
          }
        }),
        type: "personUnarchived",
        data: {
          personId: input.personId,
          businessId,
        }
      });
    }),

  setScheduleValidationMessageIgnored: employedStatsProcedure({feature: "common"})
    .input(z.object({
      storeId: z.string(),
      scheduleId: z.string(),
      shiftId: z.string(),
      code: z.string(),
      isIgnored: z.boolean(),
    }))
    .output(z.void())
    .mutation(async ({ctx, input}) => {
      const {businessId, employment: requesterEmployment} = throwIfNotEmployedByBusiness(ctx);
      const user = await getCurrentUser(ctx);

      const existingSched = await ctx.prisma.schedule.findUniqueOrThrow({
        where: {
          id: input.scheduleId,
          businessId,
          storeId: {
            in: map(requesterEmployment.stores, s => s.storeId)
          },
          isTemplate: false,
        },
        include: {
          store: true,
          ScheduleDay: {
            include: {
              ShiftArea: {
                include: {
                  Shift: {
                    include: {
                      shiftActivities: true
                    }
                  }
                }
              }
            }
          }
        }
      });

      const scheduleRevision = hydrateSchedule(existingSched);
      const dayOfValidationMessage = getShiftDay(scheduleRevision.draft, input.shiftId);
      const canEditScheduleDays = getDaysPersonCanEditSchedule({scheduleRevision, user});

      // if the schedule shift doesn't exist yet, then ignore the permission check
      if (dayOfValidationMessage && !includes(canEditScheduleDays, dayOfValidationMessage?.dayOfWeek)) {
        throwIfDenied({
          ctx, principalEmployment: requesterEmployment,
          action: "store/updateDraftSchedule",
          resource: `business/${businessId}/store/${input.storeId}`,
          resourceEntity: {
            storeId: input.storeId,
          }
        });
      }

      await ctx.prisma.scheduleValidationMessageIgnore.upsert({
        where: {
          scheduleId_shiftId_code: {
            scheduleId: input.scheduleId,
            shiftId: input.shiftId,
            code: input.code,
          },
          schedule: { // this is to make sure the schedule exists in the store and business and not sombody else's
            storeId: input.storeId,
            businessId
          }
        },
        create: {
          scheduleId: input.scheduleId,
          shiftId: input.shiftId,
          code: input.code,
          isIgnored: input.isIgnored,
          ignoredByPersonId: input.isIgnored ? ctx.auth.user.person.id : undefined,
          unignoredByPersonId: input.isIgnored ? undefined : ctx.auth.user.person.id,
        },
        update: {
          isIgnored: input.isIgnored,
          ignoredByPersonId: input.isIgnored ? ctx.auth.user.person.id : undefined,
          unignoredByPersonId: input.isIgnored ? undefined : ctx.auth.user.person.id,
        }
      });
    }),

  unignoreAllScheduleValidationMessages: employedStatsProcedure({feature: "common"})
    .input(z.object({
      storeId: z.string(),
      scheduleId: z.string(),
      shiftId: z.string(),
    }))
    .output(z.void())
    .mutation(async ({ctx, input}) => {
      const {businessId, employment: requesterEmployment} = throwIfNotEmployedByBusiness(ctx);
      const user = await getCurrentUser(ctx);

      const existingSched = await ctx.prisma.schedule.findUniqueOrThrow({
        where: {
          id: input.scheduleId,
          businessId,
          storeId: {
            in: map(requesterEmployment.stores, s => s.storeId)
          },
          isTemplate: false,
        },
        include: {
          store: true,
          ScheduleDay: {
            include: {
              ShiftArea: {
                include: {
                  Shift: {
                    include: {
                      shiftActivities: true
                    }
                  }
                }
              }
            }
          }
        }
      });

      const scheduleRevision = hydrateSchedule(existingSched);
      const dayOfValidationMessage = getShiftDay(scheduleRevision.draft ?? scheduleRevision.published!, input.shiftId);
      const canEditScheduleDays = getDaysPersonCanEditSchedule({scheduleRevision, user});

      // if the schedule shift doesn't exist yet, then ignore the permission check
      if (dayOfValidationMessage && !includes(canEditScheduleDays, dayOfValidationMessage?.dayOfWeek)) {
        throwIfDenied({
          ctx, principalEmployment: requesterEmployment,
          action: "store/updateDraftSchedule",
          resource: `business/${businessId}/store/${input.storeId}`,
          resourceEntity: {
            storeId: input.storeId,
          }
        });
      }


      await ctx.prisma.scheduleValidationMessageIgnore.updateMany({
        where: {
          scheduleId: input.scheduleId,
          shiftId: input.shiftId,
          schedule: {
            storeId: input.storeId,
            businessId
          }
        },
        data: {
          isIgnored: false,
          unignoredByPersonId: ctx.auth.user.person.id,
        }
      });
    }),

  getIgnoredScheduleValidationMessages: employedStatsProcedure({feature: "common"})
    .input(z.object({
      storeId: z.string(),
      scheduleId: z.string(),
    }))
    .output(ignoredValidationMessages)
    .query(async ({ctx, input}) => {
      const {businessId, employment: requesterEmployment} = throwIfNotEmployedByBusiness(ctx);
      // TODO new permissions

      const ignoredRows = await ctx.prisma.scheduleValidationMessageIgnore.findMany({
        where: {
          scheduleId: input.scheduleId,
          schedule: {
            businessId,
            storeId: {
              in: map(requesterEmployment.stores, s => s.storeId)
            }
          }
        },
      });

      return hydrateIgnoredValidationMessages(ignoredRows);
    }),

  getPersonPositionStatistics: employedStatsProcedure({feature: "common"})
    .input(z.object({
      storeId: z.string(),
      personId: z.string(),
    }))
    .output(z.array(positionMetricStatistic))
    .query(async ({ctx, input}) => {
      const {businessId, employment: requesterEmployment} = throwIfNotEmployedByBusiness(ctx);

      const person = await ctx.prisma.person.findUniqueOrThrow({
        where: {
          id: input.personId,
          businessId: businessId,
        },
        include: {
          user: true,
          employments: {
            where: {
              businessId,
              stores: {
                some: {}
              }
            },
            include: {
              job: true,
              userRoles: {
                include: {
                  role: true
                }
              },
              stores: {
                include: {
                  store: {}
                }
              }
            }
          },
          metrics: {
            where: {
              businessId: businessId,
              personId: input.personId,
              recorderPersonId: {not: null},
            },
            include: {
              dataPoints: {
                orderBy: {
                  timestamp: "desc"
                },
              }
            }
          },
        }
      });

      const checkAllowed = isAllowedFactory({
        businessId,
        principalEmployment: requesterEmployment,
        userId: ctx.auth.user.id,
      });

      const {employmentResourceId, employmentStoreId} = getPersonDetailParamsFromEmploymentAndRequest({
        storeId: input.storeId,
        person: {
          employments: person.employments,
          invitations: [],
          employmentRequests: [],
          trainingHistory: [],
        }
      })
      return getPersonPositionStatistics(ctx, {
        person: person,
        storeId: input.storeId,
        businessId: businessId,
        checkAllowed: checkAllowed,
        employmentResourceId,
        employmentStoreId
      })
    }),

  getCurrentPersonAvailabilityRequestDetails: employedStatsProcedure({feature: "common"})
    .input(z.object({
      storeId: z.string(),
      availabilityRequestId: z.string(),
    }))
    .output(z.object({
      request: personAvailabilityDto
    }))
    .query(async ({ctx, input}) => {
      const {businessId, employment} = throwIfNotEmployedByBusiness(ctx);
      // TODO permissions
      if (!some(employment.stores, s => s.storeId === input.storeId)) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "You are not employed at this store"
        });
      }

      const availabilityReq = await ctx.prisma.personAvailability.findUniqueOrThrow({
        where: {
          id: input.availabilityRequestId,
          storeId: input.storeId,
          personId: ctx.auth.user.person.id,
        },
        include: {
          approvedByPerson: personWithJobAndImageIncludes,
          declinedByPerson: personWithJobAndImageIncludes,
          person: personWithJobAndImageIncludes,
          submittedByPerson: personWithJobAndImageIncludes,
          cancelledByPerson: personWithJobAndImageIncludes,
          PersonAvailabilityDayTimeRange: true,
        }
      });

      if (!availabilityReq) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Time off request not found"
        });
      }

      return {
        request: toPersonAvailabilityDto(ctx, availabilityReq)
      }
    }),

  // TO DO: This is the old version of updatePersonNotificationSettings. We can
  // delete this once enough people updated to the app version that supports updatePersonNotificationSettings2
  updatePersonNotificationSettings: employedStatsProcedure({feature: "common"})
    .input(z.object({
      personId: z.string(),
      storeId: z.string(),
      settings: z.object({
        smsEnabled: z.boolean(),
        pushEnabled: z.boolean(),
        receiveScheduleUpdates: z.boolean(),
        receiveShiftOfferUpdates: z.boolean(),
      })
    }))
    .output(z.void())
    .mutation(async ({ctx, input}) => {
      const {employment} = throwIfNotEmployedByBusiness(ctx);

      if (ctx.auth.user.person.id !== input.personId) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "You can only update your own notification settings"
        });
      }

      const storeEmployment = filter(employment.stores, s => s.storeId === input.storeId);
      if (!storeEmployment) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "You are not employed at this store"
        });
      }

      await ctx.prisma.personNotificationSettings.upsert({
        where: {
          personId_storeId: {
            personId: input.personId,
            storeId: input.storeId
          }
        },
        create: {
          personId: input.personId,
          storeId: input.storeId,
          smsEnabled: input.settings.smsEnabled,
          pushEnabled: input.settings.pushEnabled,
          receiveScheduleUpdates: input.settings.receiveScheduleUpdates,
          receiveShiftOfferUpdates: input.settings.receiveShiftOfferUpdates,
        },
        update: {
          smsEnabled: input.settings.smsEnabled,
          pushEnabled: input.settings.pushEnabled,
          receiveScheduleUpdates: input.settings.receiveScheduleUpdates,
          receiveShiftOfferUpdates: input.settings.receiveShiftOfferUpdates,
        }
      });
    }),

  updatePersonNotificationSettings2: employedStatsProcedure({feature: "common"})
    .input(z.object({
      personId: z.string(),
      storeId: z.string(),
      settings: z.object({
        smsEnabled: z.boolean(),
        pushEnabled: z.boolean(),
        settings: z.record(z.string(), z.boolean()),
        smsSettings: z.record(z.string(), z.boolean()).optional()
      })
    }))
    .output(z.void())
    .mutation(async ({ctx, input}) => {
      const {employment} = throwIfNotEmployedByBusiness(ctx);

      if (ctx.auth.user.person.id !== input.personId) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "You can only update your own notification settings"
        });
      }

      const storeEmployment = filter(employment.stores, s => s.storeId === input.storeId);
      if (!storeEmployment) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "You are not employed at this store"
        });
      }

      await ctx.prisma.personNotificationSettings.upsert({
        where: {
          personId_storeId: {
            personId: input.personId,
            storeId: input.storeId
          }
        },
        create: {
          personId: input.personId,
          storeId: input.storeId,
          smsEnabled: input.settings.smsEnabled,
          pushEnabled: input.settings.pushEnabled,
          ...input.settings.settings,
          ...input.settings.smsSettings
        },
        update: {
          smsEnabled: input.settings.smsEnabled,
          pushEnabled: input.settings.pushEnabled,
          ...input.settings.settings,
          ...input.settings.smsSettings
        }
      });
    }),

  // TO DO: This is the old version of updatePersonNotificationSettings. We can
  // delete this once enough people updated to the app version that supports getPersonNotificationSettings2
  getPersonNotificationSettings: employedStatsProcedure({feature: "common"})
    .input(z.object({
      storeId: z.string(),
    }))
    .output(z.object({
      smsEnabled: z.boolean(),
      pushEnabled: z.boolean(),
      receiveScheduleUpdates: z.boolean(),
      receiveShiftOfferUpdates: z.boolean(),
    }))
    .query(async ({ctx, input}) => {
      const {businessId} = throwIfNotEmployedByBusiness(ctx);

      // Get or create settings
      const settings = await ctx.prisma.personNotificationSettings.upsert({
        where: {
          personId_storeId: {
            personId: ctx.auth.user.person.id,
            storeId: input.storeId
          }
        },
        create: {
          personId: ctx.auth.user.person.id,
          storeId: input.storeId,
          smsEnabled: true, // Default values
          pushEnabled: true
        },
        update: {}, // No updates if found
        select: {
          smsEnabled: true,
          pushEnabled: true,
          receiveScheduleUpdates: true,
          receiveShiftOfferUpdates: true,
        }
      });

      return settings;
    }),

  getPersonNotificationSettings2: employedStatsProcedure({feature: "common"})
    .input(z.object({
      storeId: z.string(),
    }))
    .output(z.object({
      smsEnabled: z.boolean(),
      pushEnabled: z.boolean(),
      settings: z.record(z.string(), z.boolean()),
      smsSettings: z.record(z.string(), z.boolean()),
      preferences: z.array(z.object({
        name: z.string(),
        pushKey: z.string(), // this is the name of the column holding bool that shows push notifications are enabled
        smsKey: z.string(), // this is the name of the column holding bool that shows text notifications are enabled
        title: z.string(),
        description: z.string(),
        isShown: z.boolean(),
        textToggleIsShown: z.boolean(),
        pushToggleIsShown: z.boolean(),
        settingsGroup: z.string()
      }))
    }))
    .query(async ({ctx, input}) => {
      const {employment} = throwIfNotEmployedByBusiness(ctx);
      const storeId = toSecureStoreIdOrThrow(ctx, input.storeId)
      const user = await getCurrentUser(ctx);
      const hasChecklistManagerPermissions = hasPermissionPackage(user, "checklistManager");

      // Merge the job and person policies
      const policy = mergeJobAndPersonPolicies({
        personPolicy: employment.permissionPolicy as unknown as DatabasePermissionPolicy,
        jobPolicy: employment.job.permissionPolicy as unknown as DatabasePermissionPolicy
      });

      // Check for the required permissions
      const hasCreateCaPermissions = some(policy.statements, statement =>
        isPermissionPackageStatement(statement) &&
        statement.packageId === PackageIds.createCorrectiveActions
      );

      const hasViewActionableItemsPermissions = some(policy.statements, statement =>
        isPermissionPackageStatement(statement) &&
        statement.packageId === PackageIds.viewActionableItems
      );
      const hasSchedulerPermissions = hasPermissionPackage(user, "createSchedules");

      type NotificationPreferenceConfig = {
        name: string;
        pushKey: string;
        smsKey: string;
        title: string;
        description: string;
        isShown: boolean;
        pushToggleIsShown: boolean;
        textToggleIsShown: boolean;
        settingsGroup: 'Scheduler Admin' | 'HR Admin' | 'Scheduling' | 'General';
      }

      const notificationPreferences: NotificationPreferenceConfig[] = [
        {
          name: 'receiveAdminShiftAndScheduleUpdates',
          pushKey: 'receiveAdminShiftAndScheduleUpdates',
          smsKey: 'receiveAdminShiftAndScheduleUpdatesSMS',
          title: 'Shift & Schedule Updates',
          description: 'Receive updates when an offer is ready for review or approval, and when an offered shift has a volunteer',
          isShown: hasSchedulerPermissions,
          pushToggleIsShown: true,
          textToggleIsShown: false,
          settingsGroup: 'Scheduler Admin'
        },
        {
          name: 'receiveActionableItemUpdates',
          pushKey: 'receiveActionableItemUpdates',
          smsKey: 'receiveActionableItemUpdatesSMS',
          title: 'Actionable Item Updates',
          description: 'Receive updates about actionable items',
          isShown: hasCreateCaPermissions && hasViewActionableItemsPermissions,
          pushToggleIsShown: true,
          textToggleIsShown: false,
          settingsGroup: 'HR Admin'
        },
        {
          name: 'receiveScheduleUpdates',
          pushKey: 'receiveScheduleUpdates',
          smsKey: 'receiveScheduleUpdatesSMS',
          title: 'Schedule Shift Updates',
          description: 'Receive updates when you are assigned to a shift or when a shift is changed',
          isShown: true,
          pushToggleIsShown: true,
          textToggleIsShown: false,
          settingsGroup: 'Scheduling'
        },
        {
          name: 'receiveShiftOfferUpdates',
          pushKey: 'receiveShiftOfferUpdates',
          smsKey: 'receiveShiftOfferUpdatesSMS',
          title: 'Shift Offer Updates',
          description: 'Receive updates regarding shift offers',
          isShown: true,
          pushToggleIsShown: true,
          textToggleIsShown: false,
          settingsGroup: 'Scheduling'
        },
        {
          name: 'receiveChecklistUpdates',
          pushKey: 'receiveChecklistUpdates',
          smsKey: 'receiveChecklistUpdatesSMS',
          title: 'Checklist Updates',
          description: 'Receive updates about checklists',
          isShown: hasChecklistManagerPermissions,
          pushToggleIsShown: true,
          textToggleIsShown: false,
          settingsGroup: 'General'
        },
        {
          name: 'receiveFeedbackUpdates',
          pushKey: 'receiveFeedbackUpdates',
          smsKey: 'receiveFeedbackUpdatesSMS',
          title: 'Feedback',
          description: 'Receive updates when you get feedback',
          isShown: true,
          pushToggleIsShown: true,
          textToggleIsShown: false,
          settingsGroup: 'General'
        }
      ];
      const settingsList = await ctx.db.person.getPersonNotificationSettings(storeId, [ctx.auth.user.person.id]);
      const settings = settingsList[0];

      // If no settings found, return defaults
      if (!settings) {
        return {
          smsEnabled: false, // Default values
          pushEnabled: true,
          settings: {
            receiveScheduleUpdates: true,
            receiveShiftOfferUpdates: true,
            receiveActionableItemUpdates: true,
            receiveChecklistUpdates: true,
            receiveFeedbackUpdates: true,
            receiveAdminShiftAndScheduleUpdates: true,
          },
          smsSettings: {
            receiveScheduleUpdatesSMS: false,
            receiveShiftOfferUpdatesSMS: false,
            receiveActionableItemUpdatesSMS: false,
            receiveChecklistUpdatesSMS: false,
            receiveFeedbackUpdatesSMS: false,
            receiveAdminShiftAndScheduleUpdatesSMS: false,
          },
          preferences: notificationPreferences
        };
      }

      return {
        smsEnabled: settings.smsEnabled,
        pushEnabled: settings.pushEnabled,
        settings: {
          receiveScheduleUpdates: settings.receiveScheduleUpdates,
          receiveShiftOfferUpdates: settings.receiveShiftOfferUpdates,
          receiveActionableItemUpdates: settings.receiveActionableItemUpdates,
          receiveChecklistUpdates: settings.receiveChecklistUpdates,
          receiveFeedbackUpdates: settings.receiveFeedbackUpdates,
          receiveAdminShiftAndScheduleUpdates: settings.receiveAdminShiftAndScheduleUpdates,
        },
        smsSettings: {
          receiveScheduleUpdatesSMS: settings.receiveScheduleUpdatesSMS,
          receiveShiftOfferUpdatesSMS: settings.receiveShiftOfferUpdatesSMS,
          receiveActionableItemUpdatesSMS: settings.receiveActionableItemUpdatesSMS,
          receiveChecklistUpdatesSMS: settings.receiveChecklistUpdatesSMS,
          receiveFeedbackUpdatesSMS: settings.receiveFeedbackUpdatesSMS,
          receiveAdminShiftAndScheduleUpdatesSMS: settings.receiveAdminShiftAndScheduleUpdatesSMS,
        },
        preferences: notificationPreferences
      };
    }),

  getUnreadNotificationCount: employedStatsProcedure({feature: "common"})
    .input(z.object({
      storeId: z.string()
    }))
    .output(z.object({
      count: z.number()
    }))
    .query(async ({ctx, input}) => {
      const {businessId} = throwIfNotEmployedByBusiness(ctx);

      const count = await ctx.prisma.notificationSend.count({
        where: {
          personId: ctx.auth.user.person.id,
          OR: [{
            storeId: input.storeId,
          }, {
            storeId: null
          }],
          isRead: false,
          isSuccess: true,
          isArchived: false
        }
      });

      return {count};
    }),

  getNotifications: employedStatsProcedure({feature: "common"})
    .input(z.object({
      storeId: z.string(),
      search: z.string().optional(),
      category: z.string().optional(),
      cursor: z.number().default(0),
      includeArchived: z.boolean().default(false)
    }))
    .output(z.object({
      items: z.array(notification),
      nextCursor: z.number().optional()
    }))
    .query(async ({ctx, input}) => {
      const NOTIFICATIONS_PER_PAGE = 20;
      const {businessId} = throwIfNotEmployedByBusiness(ctx);

      const categoryMap = {
        "Schedule": [
          NotificationCode.schedule.changed,
          NotificationCode.shift.assigned,
          NotificationCode.shift.unassigned,
          NotificationCode.shift.rangeChange,
          NotificationCode.shift.reminder
        ],
        "Offers": [
          NotificationCode.shiftOffer.available,
          NotificationCode.shiftOffer.approved,
          NotificationCode.shiftOffer.acceptanceApproved,
          NotificationCode.shiftOffer.declined,
          NotificationCode.shiftOffer.cancelled
        ],
        "Availability": [
          NotificationCode.availability.approved,
          NotificationCode.availability.declined,
          NotificationCode.timeOff.approved,
          NotificationCode.timeOff.declined
        ],
        "Checklists": [
          NotificationCode.checklist.assigned,
          NotificationCode.checklist.overdue,
          NotificationCode.checklist2.assigned,
          NotificationCode.checklist2.overdue
        ],
        "Corrective Actions": [
          NotificationCode.correctiveAction.read,
          NotificationCode.correctiveAction.review,
          NotificationCode.correctiveAction.acknowledged
        ]
      };

      const notifications = await ctx.prisma.notificationSend.findMany({
        where: {
          personId: ctx.auth.user.person.id,
          OR: [{
            storeId: input.storeId,
          }, {
            storeId: null
          }],
          ...(input.search ? {
            AND: [{
              messageText: {
                contains: input.search,
                mode: "insensitive"
              }
            }]
          } : {}),
          ...(input.category && !includes(["All", "Archived"], input.category) ? {
            code: {
              in: categoryMap[input.category as keyof typeof categoryMap]
            }
          } : {}),
          isSuccess: true,
          isArchived: input.category === "Archived"
            ? true
            : input.includeArchived
              ? undefined
              : false

        },
        orderBy: [
          {
            isRead: 'asc'
          },
          {
            startedAt: 'desc'
          }
        ],
        skip: input.cursor,
        take: NOTIFICATIONS_PER_PAGE + 1
      });

      let nextCursor: number | undefined = undefined;
      if (notifications.length > NOTIFICATIONS_PER_PAGE) {
        notifications.pop();
        nextCursor = input.cursor + NOTIFICATIONS_PER_PAGE;
      }

      const items = notifications.map(hydrateNotification);
      return {
        items,
        nextCursor
      };
    }),

  markNotificationAsRead: employedStatsProcedure({feature: "common"})
    .input(z.object({
      notificationId: z.string(),
    }))
    .output(z.void())
    .mutation(async ({ctx, input}) => {
      const {businessId} = throwIfNotEmployedByBusiness(ctx);

      await ctx.prisma.notificationSend.update({
        where: {
          id: input.notificationId,
          personId: ctx.auth.user.person.id,
        },
        data: {
          isRead: true,
          readAt: new Date()
        }
      });
    }),

  markAllNotificationsAsRead: employedStatsProcedure({feature: "common"})
    .input(z.object({
      storeId: z.string(),
    }))
    .output(z.void())
    .mutation(async ({ctx, input}) => {
      const {businessId} = throwIfNotEmployedByBusiness(ctx);

      await ctx.prisma.notificationSend.updateMany({
        where: {
          OR: [{
            personId: ctx.auth.user.person.id,
            storeId: input.storeId,
            isRead: false,
          }, {
            personId: ctx.auth.user.person.id,
            storeId: null,
            isRead: false,
          }]
        },
        data: {
          isRead: true,
          readAt: new Date(),
        },
      });
    }),

  markNotificationAsArchived: employedStatsProcedure({feature: "common"})
    .input(z.object({
      notificationId: z.string(),
      isArchived: z.boolean().optional().default(true)
    }))
    .output(z.void())
    .mutation(async ({ctx, input}) => {
      const {businessId} = throwIfNotEmployedByBusiness(ctx);

      await ctx.prisma.notificationSend.update({
        where: {
          id: input.notificationId,
          personId: ctx.auth.user.person.id,
        },
        data: {
          isArchived: input.isArchived === true,
          archivedAt: input.isArchived === true ? new Date() : null
        }
      });
    }),

  getStreetPermissions: verifiedProcedure
    .input(z.object({
      storeId: z.string(),
    }))
    .output(z.object({
      hasSchedulerStreet: z.boolean(),
      hasChecklistManagerStreet: z.boolean(),
      hasChecklists2: z.boolean(),
      hasShiftLeaderStreet: z.boolean(),
      hasInsightsStreet: z.boolean(),
      hasOperatorGroupsStreet: z.boolean(),
      canCreateResource: z.boolean(),
      canViewSchedulingInsights: z.boolean().optional()
    }))
    .query(async ({input, ctx}) => {
      const isEmployed = await isEmployedByBusiness(ctx);
      const zeroStateResponse = {
        hasSchedulerStreet: false,
        hasChecklistManagerStreet: false,
        hasChecklists2: false,
        hasShiftLeaderStreet: false,
        hasInsightsStreet: false,
        hasOperatorGroupsStreet: false,
        canCreateResource: false,
      };

      if (!isEmployed) {
        return zeroStateResponse;
      }

      const user = await getCurrentUser(ctx);
      const store = await ctx.prisma.store.findFirstOrThrow({
        where: {
          id: input.storeId
        },
        select: {
          timezone: true
        }
      });

      const checkAllowed = isAllowedFactory({
        businessId: isEmployed.businessId,
        principalEmployment: isEmployed.employment,
        userId: user.id!,
      });
      const storeId = toSecureStoreId(isEmployed.storeIds, input.storeId);
      if (!storeId) {
        return zeroStateResponse;
      }

      const hasSchedulerStreet = hasPermissionPackage(user, "createSchedules");
      const hasChecklistManagerStreet = hasPermissionPackage(user, "checklistManager");
      const hasShiftLeaderStreet = await getHasShiftLeaderAccess({
        checkAllowed,
        businessId: isEmployed.businessId,
        personId: ctx.auth.user.person.id,
        prisma: ctx.prisma,
        storeId: input.storeId,
        timezone: store.timezone ?? "America/New_York",
      });
      const hasInsightsStreet = hasPermissionPackage(user, "insights");
      const canCreateResource = perm.canCreateStoreResource(checkAllowed, {
        businessId: isEmployed.businessId,
        storeId,
      });

      const hasBetaChecklistsFeatureEnabled = await ctx.db.feature.isFeatureEnabledForBusiness(isEmployed.businessId, "checklistsBeta");
      const hasChecklistsFeatureEnabled = await ctx.db.feature.isFeatureEnabledForBusiness(isEmployed.businessId, "checklists");

      const hasOperatorGroupsFeatureEnabled = await ctx.db.feature.isFeatureEnabledForBusiness(isEmployed.businessId, "operatorGroups");
      const isOperator = perm.isAnOperator(isEmployed.employment);

      const canViewSchedulingInsights = perm.canViewSchedulingInsights(checkAllowed, {
        businessId: isEmployed.businessId,
        storeId,
      });

      return {
        hasSchedulerStreet,
        hasChecklistManagerStreet: hasChecklistManagerStreet && hasBetaChecklistsFeatureEnabled,
        hasChecklists2: hasChecklistManagerStreet && hasChecklistsFeatureEnabled,
        hasShiftLeaderStreet,
        hasInsightsStreet,
        hasOperatorGroupsStreet: hasOperatorGroupsFeatureEnabled && isOperator,
        canCreateResource,
        canViewSchedulingInsights
      };
    }),

  getStoreResources: employedStatsProcedure({feature: "common"})
    .input(z.object({
      storeId: z.string(),
      resourceType: z.enum(["link", "file", "vendor"]).optional(),
    }))
    .output(z.array(storeResourceDto))
    .query(async ({ctx, input}) => {
      const {businessId} = ctx;
      const storeId = toSecureStoreIdOrThrow(ctx, input.storeId);

      const resources = await ctx.db.store.getStoreResources(storeId, input.resourceType);

      const dtoCtx = {
        ...ctx,
        businessId,
        storeId
      };

      return map(resources, resource => toStoreResourceDto(dtoCtx, resource));
    }),

  getStoreResource: employedStatsProcedure({feature: "common"})
    .input(z.object({
      id: z.string(),
      storeId: z.string(),
      resourceType: z.enum(["link", "file", "vendor"]).optional(),
    }))
    .output(storeResourceDto)
    .query(async ({ctx, input}) => {
      const {businessId} = ctx;
      const storeId = toSecureStoreIdOrThrow(ctx, input.storeId);

      const resource = await ctx.db.store.getStoreResource(storeId, input.id, input.resourceType);

      const dtoCtx = {
        ...ctx,
        businessId,
        storeId
      };

      return toStoreResourceDto(dtoCtx, resource);
    }),

  getPresignedPostForStoreResource: employedStatsProcedure({feature: "common"})
    .input(z.object({
      storeId: z.string(),
      contentType: z.string(),
      mediaType: attachmentMediaType
    }))
    .output(presignedPostDto)
    .mutation(async ({ctx, input}) => {
      const userId = ctx.auth.user.id;
      const {s3MediaBucket, s3Client, businessId, checkAllowed} = ctx;
      const storeId = toSecureStoreIdOrThrow(ctx, input.storeId);

      if (!perm.canCreateStoreResource(checkAllowed, {
        businessId,
        storeId,
      })) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "You are not allowed to create store resources"
        });
      }

      await allowRateOrThrow({
        key: userId,
        group: "getPresignedPost",
        maxRequestsPerDuration: 200,
        prisma: ctx.prisma
      });

      const twoHundredMegs = 200 * 1024 * 1024;
      const securityPrefix = getFileS3KeyPrefix({
        mediaType: input.mediaType,
        businessId,
        storeId: storeId,
      })
      const newId = genFileAttachmentId();
      const key = getS3Key({prefix: securityPrefix, objectId: newId});
      const presignedPost = await getPresignedPost({
        s3Client: s3Client,
        s3Bucket: s3MediaBucket,
        maxSizeBytes: twoHundredMegs,
        securityPrefix: securityPrefix,
        contentType: input.contentType,
        key: key
      });

      return {
        newId: newId,
        key,
        presignedPost,
      };
    }),

  upsertStoreResource: employedStatsProcedure({feature: "common"})
    .input(z.object({
      id: z.string(),
      storeId: z.string(),
      resourceType: z.enum(["link", "file", "vendor"]),
      resourceTitle: z.string().optional(),
      linkTitle: z.string().optional(),
      linkUrl: z.string().url().optional(),
      attachment: fileAttachmentDto.optional(),
      vendor: vendorDto.optional(),
    }))
    .output(z.void())
    .mutation(async ({ctx, input}) => {
      const {businessId, checkAllowed} = ctx;
      const storeId = toSecureStoreIdOrThrow(ctx, input.storeId);
      const title = input.resourceTitle ?? input.linkTitle;

      if (!perm.canCreateStoreResource(checkAllowed, {
        businessId,
        storeId,
      })) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "You are not allowed to create store resources"
        });
      }

      if (!title) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Resource title is required"
        });
      }

      if (input.resourceType === "vendor") {
        if (!input.vendor) {
          throw new TRPCError({
            code: "BAD_REQUEST",
            message: "Vendor details are required for vendor resources"
          });
        }

        await ctx.db.store.upsertVendor(storeId, input.vendor.id, {
          companyName: input.vendor.companyName,
          pointOfContact: input.vendor.pointOfContact,
          website: input.vendor.website,
          phoneNumber: input.vendor.phoneNumber,
          email: input.vendor.email,
          notes: input.vendor.notes,
          addressLine1: input.vendor.addressLine1,
          addressLine2: input.vendor.addressLine2,
          city: input.vendor.city,
          state: input.vendor.state,
          zipCode: input.vendor.zipCode,
        });
      }

      await ctx.db.store.upsertStoreResource(storeId, input.id, {
        resourceType: input.resourceType,
        resourceTitle: title,
        linkUrl: input.linkUrl,
        vendorId: input.vendor?.id,
      });

      if (input.resourceType === "file") {
        await addFileAttachments(ctx.prisma, storeId, input.id, businessId, "storeResources", input.attachment);
      }
    }),

  deleteStoreResource: employedStatsProcedure({feature: "common"})
    .input(z.object({
      id: z.string(),
      storeId: z.string(),
      vendorId: z.string().optional(),
    }))
    .output(z.void())
    .mutation(async ({ctx, input}) => {
      const {businessId, checkAllowed} = ctx;
      const storeId = toSecureStoreIdOrThrow(ctx, input.storeId);

      if (!perm.canCreateStoreResource(checkAllowed, {
        businessId,
        storeId,
      })) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "You are not allowed to delete store resources"
        });
      }

      if (input.vendorId) {
        await ctx.db.store.deleteVendor(storeId, input.vendorId);
      }

      await reconcileFileAttachments(ctx.prisma, storeId, input.id, "storeResources");

      await ctx.db.store.deleteStoreResource(storeId, input.id);
    }),

  uploadTimePunchFile: employedStatsProcedure({feature: "variance"})
    .input(z.object({
      id: z.string(),
      storeId: z.string(),
      resourceTitle: z.string().optional(),
      attachment: fileAttachmentDto.optional(),
    }))
    .output(z.void())
    .mutation(async ({ctx, input}) => {
      const {businessId, checkAllowed} = ctx;
      const storeId = toSecureStoreIdOrThrow(ctx, input.storeId);
      const title = input.resourceTitle;
      const personId = ctx.auth.user.person.id;

      if (!perm.canUploadTimePunchFile(checkAllowed, {
        businessId,
        storeId,
      })) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "You are not allowed to upload time punch files"
        });
      }

      if (!title) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Resource title is required"
        });
      }

      // Skip creating StoreResource and FileAttachment records - this is the key difference!
      // Create only a File record with sensitivity information
      if (input.attachment) {
        const s3ObjectKey = getFileAttachmentS3Key({
          businessId,
          fileId: input.attachment.fileId,
          storeId,
          mediaType: input.attachment.mediaType,
        });

        await ctx.prisma.file.create({
          data: {
            id: input.attachment.fileId,
            businessId,
            storeId,
            title: title,
            filename: input.attachment.filename,
            mimeType: input.attachment.mimeType,
            s3ObjectKey,
            width: input.attachment.width,
            height: input.attachment.height,
            mediaType: input.attachment.mediaType,
            sensitivityLevel: 30,
            sensitivityReason: "Time Punch Data",
          },
        });
      }
    }),

  getPresignedPostForTeamMemberDocument: employedStatsProcedure({feature: "common"})
    .input(z.object({
      storeId: z.string(),
      personId: z.string(),
      contentType: z.string(),
      mediaType: attachmentMediaType
    }))
    .output(presignedPostDto)
    .mutation(async ({ctx, input}) => {
      const userId = ctx.auth.user.id;
      const {s3Client, businessId, checkAllowed} = ctx;
      const storeId = toSecureStoreIdOrThrow(ctx, input.storeId);

      const person = await ctx.db.person.getPersonInBusiness(businessId, input.personId, {user: false});

      if (!perm.canEditPerson(checkAllowed, {
        businessId,
        personId: person.id,
        userId,
      })) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "You are not allowed to upload documents for this team member."
        });
      }

      await allowRateOrThrow({
        key: userId,
        group: "getPresignedPost",
        maxRequestsPerDuration: 400,
        prisma: ctx.prisma
      });

      const twoHundredMegs = 200 * 1024 * 1024;
      const securityPrefix = getTeamMemberDocumentsS3KeyPrefix({
        businessId,
        storeId: storeId,
        personId: person.id,
      })
      const newId = genFileAttachmentId();
      const key = getTeamMemberDocument3Key({
        businessId,
        storeId,
        personId: person.id,
        fileId: newId,
      });
      // const key = getS3Key({prefix: securityPrefix, objectId: newId});
      const presignedPost = await getPresignedPost({
        s3Client: s3Client,
        s3Bucket: ctx.s3MediaBucket,
        maxSizeBytes: twoHundredMegs,
        securityPrefix: securityPrefix,
        contentType: input.contentType,
        key: key
      });

      return {
        key,
        presignedPost,
        newId,
      };
    }),

  uploadTeamMemberDocument: employedStatsProcedure({feature: "common"})
    .input(z.object({
      storeId: z.string(),
      personId: z.string(),
      resourceType: z.enum(["file"]),
      resourceTitle: z.string(),
      documentType: z.string().optional(),
      expiresAt: z.date().optional(),
      attachment: fileAttachmentDto,
    }))
    .output(z.void())
    .mutation(async ({ctx, input}) => {
      const {businessId, checkAllowed} = ctx;
      const storeId = toSecureStoreIdOrThrow(ctx, input.storeId);

      const person = await ctx.db.person.getPersonInBusiness(businessId, input.personId, {user: false});

      if (!perm.canEditPerson(checkAllowed, {
        businessId,
        personId: person.id,
        userId: person.user?.id ?? undefined,
      })) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "You are not allowed to upload documents for this team member."
        });
      }

      const imageS3Key = getTeamMemberDocument3Key({
        businessId,
        storeId,
        personId: person.id,
        fileId: input.attachment.fileId,
      });
      await throwIfImageNotExistsInS3({
        imageS3Key: imageS3Key,
        ctx: ctx,
        bucketName: ctx.s3MediaBucket,
      });

      await ctx.prisma.fileAttachment.create({
        data: {
          id: genFileAttachmentId(),
          title: input.resourceTitle,
          documentType: input.documentType,
          expiresAt: input.expiresAt,
          person: {
            connect: {
              id: person.id,
            },
          },
          file: {
            create: {
              id: input.attachment.fileId,
              businessId: businessId,
              storeId: storeId,
              filename: input.attachment.filename,
              mimeType: input.attachment.mimeType,
              s3ObjectKey: imageS3Key,
              width: input.attachment.width,
              height: input.attachment.height,
              mediaType: input.attachment.mediaType,
            },
          },
        },
      })
    }),

  getTeamMemberDocuments: employedStatsProcedure({feature: "common"})
    .input(z.object({
      storeId: z.string(),
      personId: z.string(),
    }))
    .output(z.object({
      items: z.array(fileAttachmentDto)
    }))
    .query(async ({ctx, input}) => {
      const {businessId, checkAllowed} = ctx;
      const storeId = toSecureStoreIdOrThrow(ctx, input.storeId);

      const person = await ctx.db.person.getPersonInBusiness(businessId, input.personId, {
        user: false,
        employments: {
          include: {
            stores: true,
          }
        }
      });
      const employment = getPersonMainEmployment(person.employments);
      const personStoreEmployment = find(employment?.stores, store => store.storeId === storeId);

      if (!personStoreEmployment
        || !perm.canGetPerson(checkAllowed, {
          businessId,
          storeId,
          personId: person.id
        }) || !perm.canCreateCorrectiveActions(checkAllowed, {
          businessId,
          storeEmploymentId: personStoreEmployment.id,
          storeId,
        })) {
        return {items: []};
      }

      const documents = await ctx.db.person.getPersonDocuments(businessId, storeId, person.id);
      return {
        items: map(documents, a => toFileAttachmentDto(ctx, a))
      };
    }),

  deleteTeamMemberDocument: employedStatsProcedure({feature: "common"})
    .input(z.object({
      storeId: z.string(),
      personId: z.string(),
      id: z.string(),
    }))
    .output(z.void())
    .mutation(async ({ctx, input}) => {
      const {businessId, checkAllowed} = ctx;
      const storeId = toSecureStoreIdOrThrow(ctx, input.storeId);

      const person = await ctx.db.person.getPersonInBusiness(businessId, input.personId, {user: false});

      if (!perm.canEditPerson(checkAllowed, {
        businessId,
        personId: person.id,
        userId: ctx.auth.user.id,
      })) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "You are not allowed to modify documents for this team member."
        });
      }

      const document = await ctx.db.person.getPersonDocument(businessId, storeId, person.id, input.id);

      if (!document) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Document not found"
        });
      }

      await ctx.s3Client.send(new DeleteObjectCommand({
        Bucket: ctx.s3MediaBucket,
        Key: document.file.s3ObjectKey
      }));

      await ctx.prisma.fileAttachment.delete({
        where: {
          id: document.id,
        },
      });
    }),

  parseTimePunchPdfPreview: employedStatsProcedure({feature: "common"})
    .input(z.object({
      storeId: z.string(),
      fileId: z.string(),
    }))
    .output(z.object({
      rawText: z.string(),
      timePunchEntries: z.array(baseTimePunchEntrySchema.extend({
        date: z.string(),
      })),
      parseErrors: z.array(z.string()),
      validationErrors: z.array(z.string()),
      dateRange: z.object({
        startDate: z.date(),
        endDate: z.date(),
      }).optional(),
    }))
    .mutation(async ({ctx, input}) => {
      const {businessId} = throwIfNotEmployedByBusiness(ctx);
      const storeId = toSecureStoreIdOrThrow(ctx, input.storeId);

      if (!perm.canUploadTimePunchFile(ctx.checkAllowed, {
        businessId,
        storeId,
      })) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "You are not allowed to view time punch data"
        });
      }

      // Find the file in the database
      const file = await ctx.prisma.file.findFirst({
        where: {
          id: input.fileId,
          businessId,
          storeId: input.storeId,
        },
      });

      if (!file) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "File not found",
        });
      }

      // Verify it's a PDF file
      if (file.mimeType !== 'application/pdf') {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "File must be a PDF",
        });
      }

      try {
        // Parse the PDF from S3 (but don't save to database)
        const parseResult = await parseTimePunchPdfFromS3(ctx, file.s3ObjectKey);

        // Validate the parsed data
        const validationErrors = validateTimePunchData(parseResult.timePunchEntries);

        return {
          rawText: parseResult.rawText,
          timePunchEntries: parseResult.timePunchEntries,
          parseErrors: parseResult.parseErrors,
          validationErrors,
          dateRange: parseResult.dateRange,
        };
      } catch (error) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: `Failed to parse PDF: ${error instanceof Error ? error.message : 'Unknown error'}`,
        });
      }
    }),

  getTimePunchEntries: employedStatsProcedure({feature: "common"})
    .input(z.object({
      storeId: z.string(),
      personId: z.string().optional(), // Person ID for filtering
      startDate: z.date().optional(), // Date object in store timezone
      endDate: z.date().optional(),   // Date object in store timezone
      payType: z.array(z.string()).optional(),
      limit: z.number().min(1).max(1000).default(100),
      offset: z.number().min(0).default(0),
      sortBy: z.enum(['employeeName', 'date', 'timeIn', 'timeOut', 'totalHours', 'payType']).optional(),
      sortOrder: z.enum(['asc', 'desc']).optional(),
    }))
    .output(z.object({
      entries: z.array(timePunchEntryOutputSchema),
      totalCount: z.number(),
      uniqueEmployeeNames: z.array(z.string()),
      uniquePayTypes: z.array(z.string()),
    }))
    .query(async ({ctx, input}) => {
      const {businessId} = throwIfNotEmployedByBusiness(ctx);
      const storeId = toSecureStoreIdOrThrow(ctx, input.storeId);
      const timezone = await ctx.db.store.getStoreTimezone(storeId);

      if (!perm.canUploadTimePunchFile(ctx.checkAllowed, {
        businessId,
        storeId,
      })) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "You are not allowed to view time punch data"
        });
      }

      // Get total count for pagination
      const totalCount = await ctx.db.timePunch.countTimePunchEntries(storeId, businessId, {
        startDate: input.startDate,
        endDate: input.endDate,
        personId: input.personId,
        payType: input.payType,
      });

      // Get entries with pagination and include person information
      const entries = await ctx.db.timePunch.getTimePunchEntries(storeId, businessId, {
        startDate: input.startDate,
        endDate: input.endDate,
        personId: input.personId,
        payType: input.payType,
        limit: input.limit,
        offset: input.offset,
        sortBy: input.sortBy,
        sortOrder: input.sortOrder,
      });

      // Get unique employee names for filter dropdown with person information
      const uniqueEmployees = await ctx.db.timePunch.getUniqueEmployeeNames(storeId, businessId);

      // Get unique pay types for filter dropdown
      const uniquePayTypesResult = await ctx.db.timePunch.getUniquePayTypes(storeId, businessId);

      return {
        entries: map(entries, entry => ({
          id: entry.id,
          employeeName: entry.person
            ? `${entry.person.firstName} ${entry.person.lastName}`.trim()
            : entry.employeeName, // Fallback to PDF name if no person mapped
          dayOfWeek: DateTime.fromJSDate(entry.date).setZone(timezone).toFormat('cccc'), // Day of week in store timezone
          date: DateTime.fromJSDate(entry.date).setZone(timezone).toJSDate(), // Convert to store timezone
          timeIn: entry.timeIn,
          timeOut: entry.timeOut,
          totalHours: entry.totalHours ? Number(entry.totalHours) : null,
          payType: entry.payType,
        })),
        totalCount,
        uniqueEmployeeNames: map(uniqueEmployees, e =>
          e.person
            ? `${e.person.firstName} ${e.person.lastName}`.trim()
            : e.employeeName // Fallback to PDF name if no person mapped
        ),
        uniquePayTypes: compact(map(uniquePayTypesResult, p => p.payType)),
      };
    }),

  getTimePunchVarianceEntries: employedStatsProcedure({feature: "common"})
    .input(z.object({
      storeId: z.string(),
      personId: z.string().optional(), // Person ID for filtering
      startDate: z.date().optional(), // Date object in store timezone
      endDate: z.date().optional(),   // Date object in store timezone
      showOpenPunchesOnly: z.boolean().optional(), // Filter for open punches
      variancePunchType: z.enum(['all', 'clockIn', 'clockOut']).optional(),
      variancePunchTiming: z.enum(['all', 'early', 'late']).optional(),
      varianceTimeCondition: z.enum(['all', 'lessThan', 'greaterThan']).optional(),
      varianceTimeThreshold: z.number().min(1).max(60).optional(), // minutes
      limit: z.number().min(1).max(1000).default(100),
      offset: z.number().min(0).default(0),
      sortBy: z.enum(['employeeName', 'date', 'timeIn', 'timeOut', 'totalHours', 'scheduledTimeRange', 'clockInVariance', 'clockOutVariance', 'varianceTotal']).optional(),
      sortOrder: z.enum(['asc', 'desc']).optional(),
    }))
    .output(z.object({
      entries: z.array(timePunchVarianceEntrySchema),
      totalCount: z.number(),
      uniqueEmployeeNames: z.array(z.string()),
      totalVarianceCostCents: z.number(), // Positive = cost (bad), Negative = savings (good), 0 = even
    }))
    .query(async ({ctx, input}) => {
      const {businessId} = throwIfNotEmployedByBusiness(ctx);
      const storeId = toSecureStoreIdOrThrow(ctx, input.storeId);
      const timezone = await ctx.db.store.getStoreTimezone(storeId);

      if (!perm.canUploadTimePunchFile(ctx.checkAllowed, {
        businessId,
        storeId,
      })) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "You are not allowed to view time punch data"
        });
      }

      // Build query options with pay type filter for variance analysis
      const queryOptions = {
        startDate: input.startDate,
        endDate: input.endDate,
        personId: input.personId,
        payType: ["Regular", "Unpaid"], // Include both Regular and Unpaid for combining
        showOpenPunchesOnly: input.showOpenPunchesOnly,
        sortBy: input.sortBy,
        sortOrder: input.sortOrder,
      };

      // 1. Get ALL time punch entries (without pagination) for matching
      const allTimePunchesRaw = await ctx.db.timePunch.getTimePunchEntriesWithPayRates(storeId, businessId, queryOptions);

      // 1.5. Combine time punches with unpaid breaks to create unified work sessions
      const allTimePunches = combineTimePunchesWithUnpaidTimePunches(allTimePunchesRaw);

      // 2. Get ALL shifts in date range for variance analysis
      const shiftQueryOptions = {
        startDate: input.startDate,
        endDate: input.endDate,
        personId: input.personId,
      };
      const allShiftRows = await ctx.db.shift.getShiftsForVarianceAnalysis(storeId, businessId, shiftQueryOptions);

      // Get unique employee names for filter dropdown (from both time punches and shifts)
      const uniqueEmployees = await ctx.db.timePunch.getUniqueEmployeeNames(storeId, businessId);

      // 3. Create shift data with both domain object and date info for matching
      const shiftsWithDateInfo = map(allShiftRows, (shiftRow) => {
        const hydratedShift = hydrateScheduleShift(shiftRow);
        return {
          shift: hydratedShift,
          startAbs: shiftRow.startAbs, // Keep date info for grouping
          assignedPersonId: shiftRow.assignedPersonId,
        };
      });

      // 4. Perform matching between shifts and time punches
      const matchingResults = matchShiftsAndTimePunches(shiftsWithDateInfo, allTimePunches, timezone);

      // 5. Construct unified variance rows (includes matched pairs, unmatched time punches, and unmatched shifts)
      const allVarianceRows = constructVarianceRows(matchingResults, allTimePunches, allShiftRows);

      // 6. Apply variance filtering if specified
      const filteredRows = filterVarianceRows(allVarianceRows, {
        showOpenPunchesOnly: input.showOpenPunchesOnly,
        punchType: input.variancePunchType,
        punchTiming: input.variancePunchTiming,
        timeCondition: input.varianceTimeCondition,
        timeThreshold: input.varianceTimeThreshold,
      });

      // 7. Apply sorting
      const sortedRows = sortVarianceRows(filteredRows, input.sortBy, input.sortOrder);

      // 8. Calculate total count and apply pagination
      const totalCount = sortedRows.length;
      const paginatedRows = sortedRows.slice(input.offset, input.offset + input.limit);

      // 9. Calculate total variance cost from filtered rows (includes matched, unmatched time punches, and unmatched shifts)
      const totalVarianceCostCents = calculateTotalVarianceCost(filteredRows);

      return {
        entries: paginatedRows,
        totalCount,
        uniqueEmployeeNames: map(uniqueEmployees, e =>
          e.person
            ? `${e.person.firstName} ${e.person.lastName}`.trim()
            : e.employeeName
        ),
        totalVarianceCostCents,
      };
    }),

  getTeamTotalsVarianceEntries: employedStatsProcedure({feature: "variance"})
    .input(z.object({
      storeId: z.string(),
      personId: z.string().optional(), // Person ID for filtering
      startDate: z.date().optional(), // Date object in store timezone
      endDate: z.date().optional(),   // Date object in store timezone
      timeType: z.enum(['all', 'scheduled', 'actual']).optional(),
      timeCondition: z.enum(['all', 'over', 'under']).optional(),
      timeThreshold: z.number().min(0).optional(), // hours (any positive number)
      limit: z.number().min(1).max(1000).default(100),
      offset: z.number().min(0).default(0),
      sortBy: z.enum(['employeeName', 'scheduledTime', 'actualTime', 'clockInVariance', 'clockOutVariance', 'varianceTotal', 'breaks']).optional(),
      sortOrder: z.enum(['asc', 'desc']).optional(),
    }))
    .output(z.object({
      entries: z.array(teamTotalsEntrySchema),
      totalCount: z.number(),
      uniqueEmployeeNames: z.array(z.string()),
      totalVarianceCostCents: z.number(), // Positive = cost (bad), Negative = savings (good), 0 = even
      summary: teamTotalsSummarySchema, // Summary statistics for all filtered data
    }))
    .query(async ({ctx, input}) => {
      const {businessId} = throwIfNotEmployedByBusiness(ctx);
      const storeId = toSecureStoreIdOrThrow(ctx, input.storeId);
      const timezone = await ctx.db.store.getStoreTimezone(storeId);

      if (!perm.canUploadTimePunchFile(ctx.checkAllowed, {
        businessId,
        storeId,
      })) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "You are not allowed to view time punch data"
        });
      }

      // Build query options to get all relevant pay types in one query
      const queryOptions = {
        startDate: input.startDate,
        endDate: input.endDate,
        personId: input.personId,
        payType: ["Regular", "Unpaid", "Break", "Break (Conv To Paid)"], // Include Unpaid for combining
        sortBy: input.sortBy,
        sortOrder: input.sortOrder,
      };

      // 1. Get ALL time punch entries (without pagination) for matching and breaks
      const allTimePunchEntries = await ctx.db.timePunch.getTimePunchEntriesWithPayRates(storeId, businessId, queryOptions);

      // 1a. Filter for Regular and Unpaid entries, then combine them
      const regularAndUnpaidEntries = filter(allTimePunchEntries, entry =>
        entry.payType === "Regular" || entry.payType === "Unpaid"
      );
      const allRegularTimePunches = combineTimePunchesWithUnpaidTimePunches(regularAndUnpaidEntries);

      // 1b. Filter for break entries (for break totals)
      const allBreakPunches = filter(allTimePunchEntries, entry =>
        entry.payType === "Break" || entry.payType === "Break (Conv To Paid)"
      );

      // 2. Get ALL shifts in date range for variance analysis
      const shiftQueryOptions = {
        startDate: input.startDate,
        endDate: input.endDate,
        personId: input.personId,
      };
      const allShiftRows = await ctx.db.shift.getShiftsForVarianceAnalysis(storeId, businessId, shiftQueryOptions);

      // Get ALL employees in the store (not just those with time punch entries)
      const allStoreEmployees = await ctx.db.person.findPeopleInStore(businessId, storeId, {includeMetadata: true});

      // 3. Create shift data with both domain object and date info for matching
      const shiftsWithDateInfo = map(allShiftRows, (shiftRow) => {
        const hydratedShift = hydrateScheduleShift(shiftRow);
        return {
          shift: hydratedShift,
          startAbs: shiftRow.startAbs, // Keep date info for grouping
          assignedPersonId: shiftRow.assignedPersonId,
        };
      });

      // 4. Perform matching between shifts and time punches
      const matchingResults = matchShiftsAndTimePunches(shiftsWithDateInfo, allRegularTimePunches, timezone);

      // 5. Construct unified variance rows (includes matched pairs, unmatched time punches, and unmatched shifts)
      const allVarianceRows = constructVarianceRows(matchingResults, allRegularTimePunches, allShiftRows);

      // 6. Aggregate variance rows by team member (assignedPersonId)
      const aggregatedTeamTotals = aggregateVarianceRowsByTeamMember(allVarianceRows, allShiftRows, allRegularTimePunches, allBreakPunches, allStoreEmployees, ctx, storeId);

      // 7. Apply time-based filtering to aggregated data
      const filteredTeamTotals = filterTeamTotalsRows(aggregatedTeamTotals, {
        personId: input.personId,
        timeType: input.timeType,
        timeCondition: input.timeCondition,
        timeThreshold: input.timeThreshold,
      });

      // 8. Apply sorting
      const sortedTeamTotals = sortTeamTotalsRows(filteredTeamTotals, input.sortBy, input.sortOrder);

      // 9. Calculate total count and apply pagination
      const totalCount = sortedTeamTotals.length;
      const paginatedTeamTotals = sortedTeamTotals.slice(input.offset, input.offset + input.limit);

      // 10. Calculate summary statistics from all filtered data (not just paginated subset)
      const summary = calculateTeamTotalsSummary(filteredTeamTotals);

      return {
        entries: paginatedTeamTotals,
        totalCount,
        uniqueEmployeeNames: map(allStoreEmployees, employee =>
          `${employee.firstName} ${employee.lastName}`.trim()
        ),
        totalVarianceCostCents: summary.totalVarianceCost, // Use summary value for backward compatibility
        summary, // New summary statistics for all filtered data
      };
    }),

  getHRInsightsEntries: employedStatsProcedure({feature: "insights"})
    .input(z.object({
      storeId: z.string(),
      personId: z.string().optional(), // Person ID for filtering
      insightType: z.enum(['Corrective Action', 'Actionable Item', 'Coaching Moment', 'Position Score', 'Positive Feedback', 'General Note']).optional(), // Type filtering
      startDate: z.date().optional(), // Date object in store timezone
      endDate: z.date().optional(),   // Date object in store timezone
      limit: z.number().min(1).max(1000).default(100),
      offset: z.number().min(0).default(0),
      sortBy: z.enum(['employeeName', 'date', 'type', 'status', 'createdBy']).optional(),
      sortOrder: z.enum(['asc', 'desc']).optional(),
    }))
    .output(z.object({
      entries: z.array(hrInsightsEntrySchema),
      totalCount: z.number(),
      uniqueEmployeeNames: z.array(z.string()),
      summary: hrInsightsSummarySchema, // Summary statistics for all filtered data
    }))
    .query(async ({ctx, input}) => {
      const {businessId} = throwIfNotEmployedByBusiness(ctx);
      const storeId = toSecureStoreIdOrThrow(ctx, input.storeId);

      if (!perm.canViewInsights(ctx.checkAllowed, {
        businessId,
        storeId,
      })) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "You are not allowed to view HR insights data"
        });
      }

      // 1. Get all corrective actions in the date range using the dedicated function
      const correctiveActions = await ctx.db.correctiveAction.getCorrectiveActions(businessId, storeId, {
        startDate: input.startDate,
        endDate: input.endDate,
      });

      // 2. Get all person notes (coaching moments, positive feedback, general notes) in the date range using the dedicated function
      const personNotes = await ctx.db.note.getPersonNotes(businessId, storeId, {
        startDate: input.startDate,
        endDate: input.endDate,
      });

      // 3. Get all position scores (evaluations) in the date range using the metrics database function
      const positionScores = await ctx.db.metrics.getMetricDataPoints(businessId, storeId, {
        namespace: "Store Position Performance",
        name: "Evaluation",
        startDate: input.startDate,
        endDate: input.endDate,
      });

      // Get ALL employees in the store for the uniqueEmployeeNames list and position score conversion
      const rawAllStoreEmployees = await ctx.db.person.findPeopleInStore(businessId, storeId, {includeMetadata: true});
      const allStoreEmployees = map(rawAllStoreEmployees, hydratePersonWithMetadata);

      // Store positions are now included in the metric relationship, no separate query needed

      // 4. Hydrate and convert corrective actions to insights format
      const hydratedCorrectiveActions = map(correctiveActions, hydrateCorrectiveActionForHR);
      const correctiveActionInsights = convertCorrectiveActionsToInsights(hydratedCorrectiveActions, ctx, storeId);

      // 5. Hydrate and convert person notes to insights format
      const hydratedPersonNotes = map(personNotes, hydratePersonNote);
      const personNoteInsights = convertPersonNotesToInsights(hydratedPersonNotes, ctx, storeId);

      // 6. Convert position scores to insights format
      const positionScoreInsights = convertPositionScoresToInsights(
        positionScores,
        allStoreEmployees,
        ctx,
        storeId
      );

      // 7. Combine all insights
      const allInsights = [...correctiveActionInsights, ...personNoteInsights, ...positionScoreInsights];

      // 8. Apply filtering
      const filteredInsights = filterHRInsightsEntries(allInsights, {
        personId: input.personId,
        insightType: input.insightType
      });

      // 9. Apply sorting
      const sortedInsights = sortHRInsightsEntries(filteredInsights, input.sortBy, input.sortOrder);

      // 10. Calculate total count and apply pagination
      const totalCount = sortedInsights.length;
      const paginatedInsights = sortedInsights.slice(input.offset, input.offset + input.limit);

      // 11. Calculate summary statistics from all filtered data
      const summary = calculateHRInsightsSummary(filteredInsights);

      return {
        entries: paginatedInsights,
        totalCount,
        uniqueEmployeeNames: map(allStoreEmployees, employee =>
          `${employee.firstName} ${employee.lastName}`.trim()
        ),
        summary,
      };
    }),

  getTeamMemberTrainingData: employedStatsProcedure({feature: "insights"})
    .input(z.object({
      storeId: z.string(),
      personId: z.string().optional(), // For filtering by specific person
    }))
    .output(z.object({
      people: z.array(z.object({
        id: z.string(),
        firstName: z.string(),
        lastName: z.string(),
        fullName: z.string(),
        profileImageUrl: z.string().optional(),
        age: z.number().optional(),
        proficiencyRanking: z.number().optional(),
      })),
      storeAreas: z.array(z.object({
        id: z.string(),
        title: z.string(),
        order: z.number(),
        positions: z.array(z.object({
          id: z.string(),
          title: z.string(),
          areaId: z.string(),
          areaTitle: z.string(),
          order: z.number(),
          trainingId: z.string(),
        })),
      })),
      trainingMatrix: z.record(personId, z.record(positionId, z.object({
        isCompleted: z.boolean(),
        completedAt: z.date().optional(),
      }))),
      summary: z.object({
        overall: z.object({
          partiallyTrained: z.number(),
          fullyTrained: z.number(),
          totalPeople: z.number(),
          totalPositions: z.number(),
        }),
        areas: z.array(z.object({
          areaId: z.string(),
          areaTitle: z.string(),
          partiallyTrained: z.number(),
          fullyTrained: z.number(),
          totalPositions: z.number(),
        })),
      }),
    }))
    .query(async ({ctx, input}) => {
      const {businessId, employment: userEmployment} = throwIfNotEmployedByBusiness(ctx);
      const storeId = toSecureStoreIdOrThrow(ctx, input.storeId);
      const userId = ctx.auth.user.id;

      if (!perm.canViewTraining(ctx.checkAllowed, {
        businessId,
        storeId,
      })) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "You are not allowed to view training data"
        });
      }

      const storeEmployment = find(userEmployment.stores, s => s.storeId === input.storeId);

      // Check if user can view proficiency scores
      const canViewProficiencyScores = storeEmployment ? perm.canGetProficiency(ctx.checkAllowed, {
        businessId,
        storeEmploymentId: storeEmployment.id,
        userId,
        storeId,
      }) : false;

      // 1. Get all active employees at the store using the same pattern as HR insights
      const rawPeople = await ctx.db.person.findPeopleInStore(businessId, storeId, {includeMetadata: true});

      // 2. Get all active positions with their areas and training programs
      const rawPositions = await ctx.db.store.getActivePositionsWithTrainings(storeId);

      // 3. Get all training history records for these people/positions
      const positionTrainingIds = flatMap(rawPositions, p => map(p.trainings, t => t.id));

      const rawTrainingHistory = await ctx.db.store.getTrainingHistoryForPeopleAndPositions(storeId, map(rawPeople, p => p.id), positionTrainingIds);

      // 4. Hydrate people using the same function as HR insights and add fullName
      const hydratedPeople = map(rawPeople, (person) => {
        const hydrated = hydratePersonWithMetadata(person);
        return {
          ...hydrated,
          fullName: `${hydrated.lastName || ''}, ${hydrated.firstName || ''}`.trim(),
        };
      });

      // 5. Process all data using pure functions
      const trainingData = processTeamMemberTrainingData(
        hydratedPeople,
        rawPositions,
        rawTrainingHistory,
        ctx,
        storeId,
        canViewProficiencyScores
      );

      // 6. Apply person filter if provided
      let filteredPeople = input.personId
        ? filterPeopleByPersonId(trainingData.people, input.personId)
        : trainingData.people;

      // 7. Calculate summary statistics with area breakdowns (always use unfiltered data for summary)
      const summary = calculateTrainingSummaryWithAreas(
        filteredPeople, // Use original people data for accurate summary
        trainingData.storeAreas, // Use original areas data for accurate summary
        trainingData.trainingMatrix
      );

      return {
        people: filteredPeople,
        storeAreas: trainingData.storeAreas,
        trainingMatrix: trainingData.trainingMatrix,
        summary,
      };
    }),

  saveTimePunchEntries: employedStatsProcedure({feature: "variance"})
    .input(z.object({
      storeId: z.string(),
      fileId: z.string(),
      mappedEntries: z.array(timePunchEntryInputSchema),
      dateRange: z.object({
        startDate: z.date(),
        endDate: z.date(),
      }).optional(),
    }))
    .output(z.object({
      savedEntries: z.number(),
      deletedEntries: z.number().optional(),
      validationErrors: z.array(z.string()),
    }))
    .mutation(async ({ctx, input}) => {
      const {businessId} = throwIfNotEmployedByBusiness(ctx);
      const storeId = toSecureStoreIdOrThrow(ctx, input.storeId);

      if (!perm.canUploadTimePunchFile(ctx.checkAllowed, {
        businessId,
        storeId,
      })) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "You are not allowed to save time punch data"
        });
      }

      try {
        // Validate and save entries
        let savedEntries = 0;
        let deletedEntries = 0;
        const validationErrors: string[] = [];

        if (input.mappedEntries.length > 0) {
          // Filter out entries that have validation errors and only save valid ones
          const validEntries: TimePunchData[] = [];

          input.mappedEntries.forEach((entry, index) => {
            const entryErrors: string[] = [];

            // Check required fields for this specific entry
            if (!entry.employeeName || entry.employeeName.trim().length === 0) {
              entryErrors.push(`Entry ${index + 1}: Employee name is required`);
            }
            if (!entry.date) {
              entryErrors.push(`Entry ${index + 1}: Date is required`);
            }
            if (!entry.timeIn) {
              entryErrors.push(`Entry ${index + 1}: Time in is required`);
            }
            if (!entry.timeOut) {
              entryErrors.push(`Entry ${index + 1}: Time out is required`);
            }

            // Validate total hours
            if (entry.totalHours && (entry.totalHours <= 0 || entry.totalHours > 24)) {
              entryErrors.push(`Entry ${index + 1}: Invalid total hours (${entry.totalHours})`);
            }

            // Validate date format
            try {
              new Date(entry.date);
            } catch {
              entryErrors.push(`Entry ${index + 1}: Invalid date format (${entry.date})`);
            }

            if (entryErrors.length === 0) {
              validEntries.push(entry);
            } else {
              validationErrors.push(...entryErrors);
            }
          });

          // Only delete existing entries if we have valid entries to replace them with
          if (validEntries.length > 0 && input.dateRange) {
            const deleteResult = await ctx.db.timePunch.deleteTimePunchEntriesInDateRange(
              storeId,
              businessId,
              input.dateRange.startDate,
              input.dateRange.endDate
            );
            deletedEntries = deleteResult.deletedCount;
          }

          if (validEntries.length > 0) {
            // Generate IDs for valid entries only
            const timePunchEntriesToSave = map(validEntries, entry => ({
              id: crypto.randomUUID(),
              storeId: input.storeId,
              businessId,
              uploadedFileId: input.fileId,
              employeeName: entry.employeeName,
              personId: entry.personId || null,
              dayOfWeek: entry.dayOfWeek || null,
              date: new Date(entry.date),
              timeIn: entry.timeIn || null,
              timeOut: entry.timeOut || null,
              totalHours: entry.totalHours || null,
              payType: entry.payType || null,
            }));

            // Batch insert all valid entries (includes safety check)
            await ctx.db.timePunch.createManyTimePunchEntries(storeId, timePunchEntriesToSave);

            savedEntries = timePunchEntriesToSave.length;
          }
        }

        return {
          savedEntries,
          deletedEntries: deletedEntries > 0 ? deletedEntries : undefined,
          validationErrors,
        };
      } catch (error) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: `Failed to save time punch entries: ${error instanceof Error ? error.message : 'Unknown error'}`,
        });
      }
    }),

  getTutorialSteps: verifiedProcedure
    .output(z.object({
      steps: z.array(onboardingTrainingStepDto)
    }))
    .query(async ({ctx}) => {
      const rows = await ctx.prisma.tutorialSteps.findMany({
        where: {
          isVisible: true,
        }
      });
      const steps = map(rows, row => ({
        id: row.id,
        key: row.key,
        title: row.title,
        body: row.body,
        linkToUrl: row.linkToUrl ?? undefined,
        videoPlaybackId: row.videoPlaybackId ?? undefined,
        order: row.order,
      }));
      return {
        steps
      }
    }),

  getTutorialStepsCompleted: employedStatsProcedure({feature: "common"})
    .input(z.object({
      storeId: z.string(),
    }))
    .output(z.object({
      steps: z.array(z.string())
    }))
    .query(async ({ctx, input}) => {
      const storeId = toSecureStoreIdOrThrow(ctx, input.storeId);
      const steps = await ctx.db.tutorial.getStepsCompleted(storeId);
      return {
        steps
      }
    }),

  completeTutorialStep: employedStatsProcedure({feature: "common"})
    .input(z.object({
      storeId: z.string(),
      step: z.string(),
    }))
    .output(z.void())
    .mutation(async ({ctx, input}) => {
      const storeId = toSecureStoreIdOrThrow(ctx, input.storeId);
      await ctx.db.tutorial.completeStep(storeId, ctx.currentPersonId, input.step);
    }),

  updateStoreGoLiveDate: employedStatsProcedure({feature: "common"})
    .input(z.object({
      storeId: z.string(),
      goLiveDate: z.date(),
    }))
    .output(z.void())
    .mutation(async ({ctx, input}) => {
      const storeId = toSecureStoreIdOrThrow(ctx, input.storeId);

      allAllowedOrThrow({
        checkAllowed: ctx.checkAllowed,
        actions: ["store/update"],
        resource: `business/${ctx.businessId}/store/${storeId}`,
        resourceEntity: {
          businessId: ctx.businessId,
          storeId: storeId
        }
      });

      await ctx.db.store.updateStoreGoLiveDate(storeId, input.goLiveDate);
    }),

  getSchedulingInsightsData: employedStatsProcedure({feature: "insights"})
    .input(z.object({
      storeId: z.string(),
      personIds: z.array(z.string()).max(1000).optional(),
      personFilter: z.string().optional(),
      range: dateTimeRange,
      includeArchived: z.boolean().optional().default(false),
    }))
    .output(z.object({
      insights: schedulingInsightsData
    }))
    .query(async ({ctx, input}) => {
      const storeId = toSecureStoreIdOrThrow(ctx, input.storeId);
      const {businessId} = throwIfNotEmployedByBusiness(ctx);

      if (!perm.canViewSchedulingInsights(ctx.checkAllowed, {
        businessId,
        storeId,
      })) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "You are not allowed to view Scheduling insights data"
        });
      }

      const allPeople = await ctx.db.person.findPeopleInStore(businessId, storeId, {
        includeArchived: input.includeArchived
      });

      const searchLower = input.personFilter?.toLowerCase().trim();
      const filteredPeople = searchLower?.length
        ? filter(allPeople, person => filterPersonByName(person, searchLower))
        : allPeople;

      const personIds = map(filteredPeople, p => p.id);

      // Get store timezone for proper date calculations
      const timezone = await ctx.db.store.getStoreTimezone(storeId);

      const baseFilters = {
        personIds: personIds,
        range: input.range,
        status: undefined,
      };
      const allShifts = await ctx.db.scheduling.getShiftsForPeopleForDateRange(storeId, baseFilters);
      const allSwaps = await ctx.db.scheduling.getShiftSwapsForPeopleInDateRange(storeId, {
        ...baseFilters,
      }, {
        offerorShift: true,
        offereeShift: true,
      });
      const allOffers = await ctx.db.scheduling.getShiftOffersForPeopleInDateRange(storeId, {
        ...baseFilters
      }, {});
      const allTimeOffs = await ctx.db.scheduling.getTimeOffForPeopleInDateRange(storeId, {
        ...baseFilters,
        status: undefined
      });

      const totalShifts = allShifts.length;
      const totalScheduledHours = getTotalScheduledHours(allShifts);
      const personShiftStats = reduceShiftsPerPerson(allShifts);
      const personSwapStats = reduceShiftSwapsPerPerson(allSwaps, input.range, timezone);
      const personOfferStats = reduceShiftOffersPerPerson(allOffers);
      const personTimeOffHours = reduceTimeOffPerPerson(allTimeOffs, input.range, timezone);

      return {
        insights: {
          totalShifts,
          totalScheduledHours,
          totalSwaps: allSwaps.length,
          totalOffers: allOffers.length,
          totalTimeOffRequests: allTimeOffs.length,
          personShiftStats,
          personSwapStats,
          personOfferStats,
          personTimeOffHours,
        }
      }
    }),

  getSchedulingInsightsShiftData: employedStatsProcedure({feature: "insights"})
    .input(z.object({
      storeId: z.string(),
      personIds: z.array(z.string()).max(1000).optional(),
      personFilter: z.string().optional(),
      range: dateTimeRange,
      includeArchived: z.boolean().optional().default(false),
    }))
    .output(z.object({
      insights: schedulingInsightsShiftData
    }))
    .query(async ({ctx, input}) => {
      const storeId = toSecureStoreIdOrThrow(ctx, input.storeId);
      const {businessId} = throwIfNotEmployedByBusiness(ctx);

      if (!perm.canViewSchedulingInsights(ctx.checkAllowed, {
        businessId,
        storeId,
      })) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "You are not allowed to view HR insights data"
        });
      }

      const allPeople = await ctx.db.person.findPeopleInStore(businessId, storeId, {
        includeArchived: input.includeArchived
      });

      const searchLower = input.personFilter?.toLowerCase().trim();
      const filteredPeople = searchLower?.length
        ? filter(allPeople, person => filterPersonByName(person, searchLower))
        : allPeople;

      const personIds = map(filteredPeople, p => p.id);

      const baseFilters = {
        personIds: personIds,
        range: input.range,
      };

      const shiftRows = await ctx.db.scheduling.getShiftsForPeopleForDateRange(storeId, baseFilters);
      const swapRows = await ctx.db.scheduling.getShiftSwapsForPeopleInDateRange(storeId, {
        ...baseFilters,
        status: ["approved", "declined"]
      }, {
        offerorPerson: true,
        offereePerson: true,
        offerorShift: true,
        offereeShift: true,
      });
      const offerRows = await ctx.db.scheduling.getShiftOffersForPeopleInDateRange(storeId, {
        ...baseFilters,
        status: ["approved", "declined"],
      }, {
        shift: true,
      });

      const allTimeOffs = await ctx.db.scheduling.getTimeOffForPeopleInDateRange(storeId, {
        ...baseFilters,
        status: ["approved", "declined"],
      });
      const allOffers = filter(offerRows, offer => offer.shift !== null);

      return {
        insights: {
          scheduledShifts: mapShiftsToInsightsDto(shiftRows),
          shiftSwaps: mapShiftSwapsToInsightsDto(swapRows, input.range),
          shiftOffers: mapOffersIntoInsightsDto(allOffers),
          timeOff: mapTimeOffToInsightsDto(allTimeOffs),
        }
      };
    }),

  /**
   * Get the people in the business that the current person has access to. For example if the current person can access 2 out of 3 stores in the business, then return the people that are employed at those 2 stores.
   */
  getActivePeopleInBusiness: employedStatsProcedure({feature: "common"})
    .input(z.object({
      // narrow down to particular stores if you desire to do so
      storeIds: z.array(z.string()).optional(),
    }))
    .output(z.array(personDto))
    .query(async ({ctx, input}) => {
      const userStoreIds = input.storeIds ? intersection(ctx.storeIds, input.storeIds) as SecureStoreId[] : ctx.storeIds;

      const people = await ctx.db.business.getActivePeopleAtStores(ctx.businessId, userStoreIds);
      return map(people, person => {
        const employment = getPersonMainEmployment(person.employments);
        return {
          id: person?.id ?? undefined,
          profileImageUrl: person?.profileImage ? ctx.getImageUrl(person.id, person.profileImage.id!) : undefined,
          firstName: person?.firstName ?? undefined,
          lastName: person?.lastName ?? undefined,
          jobTitle: employment ? employment.job.title : undefined,
          storeIds: []
        }
      });
    }),

  getNewspapers: employedStatsProcedure({feature: "common"})
    .output(z.object({
      newspapers: z.array(newspaperDto)
    }))
    .query(async ({ctx}) => {
      const personJobTitle = ctx.employment.job.title;
      const oneWeekAgo = DateTime.now().minus({weeks: 1}).toJSDate();

      const newspapers = await ctx.prisma.newspaper.findMany({
        where: {
          publishedAt: {
            gte: oneWeekAgo
          },
          visibilityGroup: {
            jobTitles: {
              has: personJobTitle
            }
          },
          readReceipts: {
            none: {
              personId: ctx.currentPersonId
            }
          }
        },
        take: 10, // safety limit
        orderBy: {
          publishedAt: "desc"
        }
      });

      return {
        newspapers: map(newspapers, newspaper => ({
          id: newspaper.id,
          title: newspaper.title,
          body: newspaper.body,
          videoPlaybackId: newspaper.videoPlaybackId || undefined,
          publishedAt: newspaper.publishedAt ?? new Date(),
        }))
      };
    }),

  getNewspaper: employedStatsProcedure({feature: "common"})
    .input(z.object({
      id: z.string(),
    }))
    .output(newspaperDto)
    .query(async ({ctx, input}) => {
      const newspaper = await ctx.prisma.newspaper.findUniqueOrThrow({
        where: {
          id: input.id
        }
      });

      return {
        id: newspaper.id,
        title: newspaper.title,
        body: newspaper.body,
        videoPlaybackId: newspaper.videoPlaybackId || undefined,
        publishedAt: newspaper.publishedAt ?? new Date(),
      };
    }),

  markNewspaperRead: employedStatsProcedure({feature: "common"})
    .input(z.object({
      newspaperId: z.string(),
    }))
    .output(z.void())
    .mutation(async ({ctx, input}) => {
      await ctx.prisma.newspaperReadReceipt.upsert({
        where: {
          newspaperId_personId: {
            newspaperId: input.newspaperId,
            personId: ctx.currentPersonId
          }
        },
        update: {},
        create: {
          personId: ctx.currentPersonId,
          newspaperId: input.newspaperId,
        }
      });
    }),

  toggleStoreWorkMode: employedStatsProcedure({feature: "common"})
    .input(z.object({
      storeId: z.string(),
      isEnabled: z.boolean(),
    }))
    .output(z.void())
    .mutation(async ({ctx, input}) => {
      const {businessId, employment} = throwIfNotEmployedByBusiness(ctx);
      const storeId = toSecureStoreIdOrThrow(ctx, input.storeId);

      throwIfDenied({
        ctx, principalEmployment: employment,
        action: "store/update",
        resource: `business/${businessId}/store/${storeId}`,
        resourceEntity: {
          storeId: storeId,
        }
      });

      await ctx.db.store.toggleStoreWorkMode(storeId, {
        isEnabled: input.isEnabled,
        updatedByPersonId: ctx.currentPersonId
      });
    }),

});

