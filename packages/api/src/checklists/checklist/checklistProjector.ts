import {filterForEventTypes} from "../../eventSourcing/eventSourcingUtil";
import {Projector} from "../../eventSourcing/projection";
import {Database} from "../../database";
import {EventStore} from "../../eventSourcing/eventStore";
import {DomainEvent} from "../../eventSourcing/domainEvent";
import {compact, filter, includes, map} from "lodash";
import {SecureBusinessId, SecureStoreId} from "../../database.types";
import {checklistEventTypes} from "./checklistEvent";
import {getChecklist} from "./checklistRepository";
import {getRecurrenceDateTime} from "../../calendar/recurrenceSet/recurrenceId";
import {FileAttachmentDto} from "../../fileAttachment.dto";
import {notifyPerson} from "../../notifications";
import {getChecklistAlarms, getChecklistItem, getTitle} from "./checklist";
import {NotificationCode, NotificationEntityMap, PersonToNotify} from "../../notifications.types";
import {Context} from "../../types";
import {processBatchesWithConcurrency} from "../../asyncBatchProcessing";
import * as Sentry from "@sentry/node";
import {getAllowedPermissionPackageIdsForEmployment} from "../../permissionPackages";
import {getPersonMainEmployment} from "../../schema.converters";
import {PackageIds} from "../../routers/permissionPackageIds";
import {getChecklistItemLatestInteraction} from "./checklistDto";
import {ChecklistWithInteractions} from "./checklistTypes";
import {isItemCompleteNonCompliant} from "../../../../gioa-app/src/components/checklists2/checklistInteractionUtil";

function getEventRecurrenceId(event: DomainEvent): Date | undefined {
  if (event.type === "ChecklistItemCompletedEvent" || event.type === "ChecklistItemUncompletedEvent") {
    return event.recurrenceId ? getRecurrenceDateTime(event.recurrenceId).toJSDate() : undefined;
  }
}

function getChecklistEventFileAttachmentDtos(event: DomainEvent): FileAttachmentDto[] {
  switch (event.type) {
    // creating or updating an item can create new attachments
    case "ChecklistItemUpsertedEvent":
      return compact([
        event.attachment,
        ...map(event.instructions, instr => instr.attachment)
      ]);
    // completing an item can create new attachments
    case "ChecklistItemCompletedEvent":
      return compact(map(event.requirementCompletions, completion => "attachment" in completion ? completion.attachment : undefined));
  }

  return [];
}

async function getPeopleToNotifyOnComplete(
  database: Database,
  businessId: SecureBusinessId,
  storeId: SecureStoreId,
  peopleToNotifyOnCompleteIds: string[]
): Promise<PersonToNotify[]> {
  let peopleToNotifyOnComplete: PersonToNotify[];

  if (peopleToNotifyOnCompleteIds.length > 0) {
    peopleToNotifyOnComplete = await database.person.findManyPeopleById(businessId, peopleToNotifyOnCompleteIds);
  } else {
    const peopleAtStore = await database.person.findPeopleInStore(businessId, storeId);

    // Filter to only include people with checklistManager permissions
    peopleToNotifyOnComplete = filter(peopleAtStore, (person) => {
      // Check if any of their employment has checklistManager permissions and the current store
      const employment = getPersonMainEmployment(person.employments);

      if (!employment) {
        return false; // person has no active employment
      }

      if (!employment.stores.some(store => store.storeId === storeId)) {
        return false; // employment does not contain the checklist's store
      }

      const allowedPackages = getAllowedPermissionPackageIdsForEmployment(employment);
      return includes(allowedPackages, PackageIds.checklistManager);
    });
  }

  // sanity limit
  if (peopleToNotifyOnComplete.length > 50) {
    peopleToNotifyOnComplete.splice(50);
  }

  return peopleToNotifyOnComplete;
}

async function sendChecklistNotifications(
  ctx: Pick<Context, 'courier' | 'sendEmail' | 'prisma' | 'pushNotificationService' | 'telnyx' | 'telnyxFromNumber'>,
  peopleToNotify: PersonToNotify[],
  checklistId: string,
  subject: string,
  message: string,
  code: keyof NotificationEntityMap,
  entityIds: { storeId: SecureStoreId; checklistId: string; recurrenceId?: Date }
) {
  processBatchesWithConcurrency({
    items: peopleToNotify,
    concurrency: 3,
    batchProcessor: (person) => {
      return notifyPerson({
        ctx,
        deduplicationId: null,
        person: {
          ...person,
          id: person.id!,
          user: {
            devicePushTokens: map(person.user?.devicePushTokens, dpt => ({
              deviceToken: dpt.deviceToken,
            })),
          },
          notificationSettings: map(person.notificationSettings, ns => ({
            smsEnabled: ns.smsEnabled,
            pushEnabled: ns.pushEnabled,
            receiveScheduleUpdates: ns.receiveScheduleUpdates,
            receiveShiftOfferUpdates: ns.receiveShiftOfferUpdates,
            receiveActionableItemUpdates: ns.receiveActionableItemUpdates,
            receiveChecklistUpdates: ns.receiveChecklistUpdates,
            receiveScheduleUpdatesSMS: ns.receiveScheduleUpdatesSMS,
            receiveShiftOfferUpdatesSMS: ns.receiveShiftOfferUpdatesSMS,
            receiveActionableItemUpdatesSMS: ns.receiveActionableItemUpdatesSMS,
            receiveChecklistUpdatesSMS: ns.receiveChecklistUpdatesSMS,
            receiveFeedbackUpdatesSMS: ns.receiveFeedbackUpdatesSMS,
            receiveAdminShiftAndScheduleUpdatesSMS: ns.receiveAdminShiftAndScheduleUpdatesSMS,
            receiveFeedbackUpdates: ns.receiveFeedbackUpdates,
            receiveAdminShiftAndScheduleUpdates: ns.receiveAdminShiftAndScheduleUpdates,
          }))
        },
        subject,
        message,
        preferences: {
          pushColumn: "receiveChecklistUpdates",
          smsColumn: "receiveChecklistUpdatesSMS",
        },
        code,
        entityIds
      })
    },
    handleError: async (item, error) => {
      Sentry.captureException(error, {
        extra: {
          personId: item.id,
          checklistId,
        }
      });
      console.error("Error notifying ", JSON.stringify(error))
    }
  })
}

export const getChecklistProjector = ({businessId, database, eventStore, ...ctx}: {
  businessId: SecureBusinessId;
  storeIds: SecureStoreId[];
  eventStore: EventStore;
  database: Database;
} & Pick<Context, 'courier' | 'sendEmail' | 'prisma' | 'pushNotificationService' | 'telnyx' | 'telnyxFromNumber'>): Projector => async (allEvents) => {
  const events = filterForEventTypes(checklistEventTypes, allEvents);

  for (const event of events) {
    const checklistId = event.checklistId;
    const {checklist: newChecklist, latestEventId} = await getChecklist({
      checklistId,
      eventStore,
      // for item interaction events, we need to load the interactions for the checklist so that we can update the checklist interaction summary row
      recurrenceId: getEventRecurrenceId(event)
    })

    // Update (synchronous) projections.
    if (newChecklist && latestEventId) {
      const oldChecklist = await database.checklist.doesChecklistExist(newChecklist.storeId, checklistId);

      if (!oldChecklist) {
        await database.checklist.createChecklist(newChecklist.storeId, {
          checklist: newChecklist,
          initVersion: latestEventId,
        });
      } else {
        const alarms = getChecklistAlarms(newChecklist);
        const newAttachments = getChecklistEventFileAttachmentDtos(event);
        const recurrenceId = "recurrenceId" in event ? event.recurrenceId : undefined;

        if (event.type === "ChecklistItemCompletedEvent") {
          if ("interactions" in newChecklist) {
            const item = getChecklistItem(newChecklist, recurrenceId, event.itemId);
            if (!item) {
              Sentry.captureEvent({
                message: "Could not find checklist item for completed event",
                extra: {
                  itemId: event.itemId,
                  checklistId: event.checklistId,
                  recurrenceId: event.recurrenceId,
                }
              });
              continue;
            }
            const interactions = getChecklistItemLatestInteraction(newChecklist as ChecklistWithInteractions, item.id);
            const isCompleteNonCompliant = isItemCompleteNonCompliant(item, interactions);
            if (isCompleteNonCompliant) {
              const peopleToNotifyOnCompleteIds = alarms.peopleToNotifyOnComplete;
              const peopleToNotifyOnComplete = await getPeopleToNotifyOnComplete(
                database,
                businessId,
                newChecklist.storeId,
                peopleToNotifyOnCompleteIds
              );

              // purposefully not awaiting here, since it's not a critical task
              sendChecklistNotifications(
                ctx,
                peopleToNotifyOnComplete,
                newChecklist.id,
                "Checklist Item Non-compliant",
                `Nation: Checklist "${getTitle(newChecklist)}" has an item out of compliance.`,
                NotificationCode.checklist2.completedNonCompliant,
                {
                  storeId: newChecklist.storeId,
                  checklistId: newChecklist.id,
                  recurrenceId: recurrenceId ? getRecurrenceDateTime(recurrenceId).toJSDate() : undefined,
                }
              );
            }
          }
        }

        const summaryResult = await database.checklist.conditionallyUpdateChecklist(businessId, newChecklist.storeId, {
          checklist: newChecklist,
          newVersion: latestEventId,
          newAttachments: newAttachments,
          interactions: newChecklist.interactions,
          recurrenceId: recurrenceId
        });

        if (summaryResult) {
          const {oldSummary, newSummary} = summaryResult;
          if (!oldSummary.isComplete && newSummary.isComplete) {
            if (!alarms.notifyOnComplete) {
              return;
            }

            const peopleToNotifyOnCompleteIds = alarms.peopleToNotifyOnComplete;
            const peopleToNotifyOnComplete = await getPeopleToNotifyOnComplete(
              database,
              businessId,
              newChecklist.storeId,
              peopleToNotifyOnCompleteIds
            );

            // purposefully not awaiting here, since it's not a critical task
            sendChecklistNotifications(
              ctx,
              peopleToNotifyOnComplete,
              newChecklist.id,
              "Checklist Complete",
              `Nation: Checklist "${getTitle(newChecklist)}" has been completed.`,
              NotificationCode.checklist2.completed,
              {
                storeId: newChecklist.storeId,
                checklistId: newChecklist.id,
                recurrenceId: recurrenceId ? getRecurrenceDateTime(recurrenceId).toJSDate() : undefined,
              }
            );
          }
        }
      }
    }
  }

}
