{
  name: '@gioa/api',
  version: '1.0.0',
  description: '',
  main: 'src/server.ts',
  exports: {
    './*': './*',
  },
  scripts: {
    build: 'tsc',
    // starts the api in hot reloading dev mode. You should start the database in the docker-compose.dev.yaml
    // before running this script
    dev: 'cross-env NODE_ENV=development tsx watch src/server.ts',
    start: 'cross-env NODE_ENV=development tsx src/server.ts',
    'prod:watch': 'cross-env NODE_ENV=production tsx watch src/server.ts',
    prod: 'cross-env NODE_ENV=production tsx src/server.ts',
    typecheck: 'tsc --noEmit',
    // see why it is necessary to have an explicit postinstall script for generating the prisma client: https://github.com/prisma/prisma/issues/6603
    postinstall: 'prisma generate',
    'db:push': 'prisma db push',
    'db:migrate': 'prisma migrate dev',
    test: 'vitest --run && pnpm test:unit',
    'test:update': 'vitest --run --update',
    'test:unit': 'vitest --run --config vitest.config.unit.ts',
    'test:unit:update': 'vitest --run --config vitest.config.unit.ts --update',
    'aws:login': 'aws sso login --profile gioa-beta',
    'aws:login:prod': 'aws sso login --profile gioa-prod',
    studio: 'prisma studio',
    'stripe:listen': 'stripe listen --forward-to localhost:3000/api/stripe',
    'seed-people': 'tsx src/scripts/seed-people.ts',
    'compile-checklist-worker': 'esbuild src/checklistWorker.ts --bundle --outfile=dist/checklistWorker.js --platform=node',
  },
  keywords: [],
  author: '',
  license: 'ISC',
  dependencies: {
    '@aws-sdk/client-cloudwatch': '^3.841.0',
    '@aws-sdk/client-s3': '^3.617.0',
    '@aws-sdk/client-ses': '^3.616.0',
    '@aws-sdk/client-sns': '^3.675.0',
    '@aws-sdk/client-sqs': '^3.616.0',
    '@aws-sdk/s3-presigned-post': '^3.617.0',
    '@aws-sdk/s3-request-presigner': '^3.617.0',
    '@date-fns/utc': '^1.2.0',
    '@formkit/auto-animate': '^0.8.2',
    '@keyv/redis': '^4.4.0',
    '@lucia-auth/adapter-prisma': '^4.0.1',
    '@prisma/client': '5.22.0',
    '@sentry/node': '8.38.0',
    '@sentry/profiling-node': '8.38.0',
    '@trpc/server': '11.1.2',
    '@trycourier/courier': '^6.1.1',
    '@types/cors': '^2.8.17',
    '@types/lodash': '4.17.16',
    '@types/mime-types': '^2.1.4',
    '@upstash/qstash': '^2.5.0',
    'aws-embedded-metrics': '^4.1.1',
    cacheable: '^1.9.0',
    compression: '^1.7.4',
    cors: '^2.8.5',
    'currency.js': '^2.0.4',
    'date-fns': '^3.6.0',
    'date-fns-tz': '3.1.0',
    dotenv: '^16.4.5',
    'dotenv-expand': '^11.0.6',
    exceljs: '^4.4.0',
    'expo-server-sdk': '^3.13.0',
    express: '^4.19.2',
    helmet: '^7.1.0',
    'html-to-text': '^9.0.5',
    immer: '^10.1.1',
    jsonwebtoken: '^9.0.2',
    keyv: '^5.3.3',
    'libphonenumber-js': '^1.10.61',
    lodash: '^4.17.21',
    'lru-cache': '^11.1.0',
    lucia: '^3.2.0',
    'mime-types': '^2.1.35',
    'moment-timezone': '^0.5.45',
    morgan: '^1.10.0',
    murmurhash: '^2.0.1',
    nanoid: '^5.0.7',
    openai: '^4.52.7',
    oslo: '^1.2.0',
    'pdf-lib': '^1.17.1',
    'pdf-parse': '^1.1.1',
    prisma: '^5.22.0',
    'rate-limiter-flexible': '^5.0.3',
    'react-draggable': '^4.4.6',
    'react-resizable': '^3.0.5',
    redis: '^5.0.1',
    rfc6902: '^5.1.1',
    rrule: '^2.8.1',
    'stream-chat': '^9.11.0',
    stripe: '^18.1.1',
    superjson: '^2.2.1',
    telnyx: '2.0.0',
    tsx: '^4.7.2',
    typescript: '5.8.3',
    zod: '4.0.14',
  },
  devDependencies: {
    '@faker-js/faker': '^9.4.0',
    '@testcontainers/localstack': '^10.10.4',
    '@testcontainers/postgresql': '^10.10.4',
    '@trpc/client': '11.1.2',
    '@types/compression': '^1.7.5',
    '@types/express': '^4.17.21',
    '@types/html-to-text': '^9.0.4',
    '@types/jsonwebtoken': '^9.0.9',
    '@types/morgan': '^1.9.9',
    '@types/node': '^20.12.6',
    '@types/react-resizable': '^3.0.8',
    '@types/tailwindcss': '^3.1.0',
    '@vitest/coverage-v8': '^2.0.4',
    axios: '^1.6.8',
    'cross-env': '^7.0.3',
    esbuild: '^0.25.5',
    'fast-check': '^3.18.0',
    'form-data': '^4.0.0',
    'ts-node': '^10.9.2',
    vitest: '2.1.5',
  },
}
