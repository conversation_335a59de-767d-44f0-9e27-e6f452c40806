// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider        = "prisma-client-js"
  previewFeatures = ["tracing", "metrics"]
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model SuperSecretAdminPassword {
  id       String @id
  password String
}

model File {
  businessId        String? // TODO Make required after backfill
  storeId           String?
  id                String  @id
  title             String?
  description       String?
  altText           String?
  sensitivityLevel  Int?
  sensitivityReason String?

  // the full key -- (user id)/(generated key)
  s3ObjectKey String
  thumbhash   String?
  width       Int?
  height      Int?
  filename    String?
  mimeType    String?
  createdAt   DateTime  @default(now())
  updatedAt   DateTime? @updatedAt

  // "image" | "video" | "file" | "document"
  mediaType String

  attachments      FileAttachment[]
  timePunchEntries TimePunchEntry[]

  @@index([businessId, storeId])
}

model TimePunchEntry {
  id             String   @id
  storeId        String
  businessId     String
  uploadedFileId String
  employeeName   String
  personId       String? // The mapped store employee
  dayOfWeek      String? // Mon, Tue, Wed, etc.
  date           DateTime @db.Date
  timeIn         String?
  timeOut        String?
  totalHours     Decimal? @db.Decimal(5, 2)
  payType        String? // Regular, Break, etc.
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt

  store        Store    @relation(fields: [storeId], references: [id], onDelete: Cascade)
  business     Business @relation(fields: [businessId], references: [id], onDelete: Cascade)
  uploadedFile File     @relation(fields: [uploadedFileId], references: [id], onDelete: Cascade)
  person       Person?  @relation(fields: [personId], references: [id], onDelete: SetNull)

  @@index([storeId])
  @@index([businessId])
  @@index([uploadedFileId])
  @@index([employeeName])
  @@index([personId])
  @@index([date])
}

model Image {
  id          String  @id
  title       String?
  description String?
  altText     String?

  // the full key -- (user id)/(generated key)
  s3ObjectKey String
  // see https://github.com/evanw/thumbhash
  thumbhash   String?
  width       Int?
  height      Int?
  fileName    String?
  mimeType    String?
  createdAt   DateTime  @default(now())
  updatedAt   DateTime? @updatedAt

  // the user who uploaded the image
  uploadedBy       User?   @relation("ImagesUploadedByUser", fields: [uploadedByUserId], references: [id])
  uploadedByUserId String?

  personProfileImage      Person?
  stores                  Store[]
  correctiveActions       CorrectiveAction[]
  correctiveActionHistory CorrectiveActionHistory[]
  actionableItems         CorrectiveAction[]        @relation("ActionableItemImages")
  actionableItemsHistory  CorrectiveActionHistory[] @relation("HistoryActionableItemImages")
  entityNotes             EntityNote[]
  entityNotesHistory      EntityNoteHistory[]

  @@index([uploadedByUserId])
}

model StoreDevice {
  businessId String
  business   Business @relation(fields: [businessId], references: [id])

  // The Nation app generates a clientId for itself that stays constant for the lifetime of the app install
  clientId String @id
  storeId  String
  store    Store  @relation(fields: [storeId], references: [id])

  // Changing the nonce revokes existing JWTs without removing or changing the clientId of this device
  nonce String @unique

  title       String
  description String

  deviceInfo Json

  isLocked Boolean @default(false)

  createdByPersonId String
  createdByPerson   Person @relation("StoreDeviceCreatedByPerson", fields: [createdByPersonId], references: [id])

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  sessionHistory StoreDeviceSessionHistory[]

  @@index([storeId, clientId])
}

model StoreDeviceSessionHistory {
  storeDeviceId String
  storeDevice   StoreDevice @relation(fields: [storeDeviceId], references: [clientId], onDelete: Cascade)

  personId String
  person   Person @relation(fields: [personId], references: [id])

  createdAt DateTime @default(now())
  operation String // "login" | "logout" | "revoked" | "expired"

  @@id([storeDeviceId, createdAt])
  @@index([personId, storeDeviceId])
}

model Session {
  id        String   @id
  userId    String
  expiresAt DateTime
  user      User     @relation(references: [id], fields: [userId], onDelete: Cascade)

  impersonatedByUserId String?
  impersonatedByUser   User?   @relation("ImpersonatedByUser", fields: [impersonatedByUserId], references: [id])

  scopeToStoreId String?
  scopeToStore   Store?  @relation(fields: [scopeToStoreId], references: [id])

  createdAt            DateTime?
  startedFromIpAddress String?
  startedFromUserAgent String?

  userClientId String?
  userClient   UserClient? @relation(fields: [userId, userClientId], references: [userId, id])

  @@index([userId])
}

// TODO remove
model Role {
  id                  String               @id
  title               String
  description         String?
  users               User[]
  jobs                Job[]
  userEmploymentRoles UserEmploymentRole[]
  invitations         BusinessInvitation[]

  createdAt DateTime  @default(now())
  updatedAt DateTime? @updatedAt
}

model User {
  id          String  @id
  username    String?
  images      Image[] @relation("ImagesUploadedByUser")
  isSuspended Boolean @default(false)

  hashedPassword    String
  passwordResetCode PasswordResetCode?

  emailVerificationCode EmailVerificationCode?
  emailVerified         Boolean                @default(false)
  phoneNumberVerified   Boolean                @default(false)

  // Where the user should land after logging in. This could be the onboarding process, the
  // dashboard, the last page they were on, notices that we want to show them, etc.
  initDestination String?

  featureUserOverride   FeatureUserOverride[]
  generatorStatusChange GeneratorStatusChange[]

  clients  UserClient[]
  sessions Session[]

  // TODO move person to a join table, so that users may have multiple businesses
  personId String @unique
  person   Person @relation(fields: [personId], references: [id])

  statusHistory   UserStatusHistory[]
  changedStatuses UserStatusHistory[]  @relation("userStatusChangedByUser")
  employmentRoles UserEmploymentRole[] // TODO remove

  // role for users who are not associated with a business employment (e.g. GIOA admins)
  systemRoleId String?
  systemRole   Role?     @relation(fields: [systemRoleId], references: [id])
  createdAt    DateTime  @default(now())
  updatedAt    DateTime? @updatedAt

  tempAuthToken    TempAuthToken?
  devicePushTokens UserDevicePushToken[]

  smsMfaEnabled      Boolean              @default(false)
  smsMfaVerification SmsMfaVerification[]

  pendingSessions       PendingSession[]
  impersonationSessions Session[]        @relation("ImpersonatedByUser")

  @@index([personId])
}

model SmsMfaVerification {
  id        String   @id
  code      String
  createdAt DateTime @default(now())
  expiresAt DateTime

  userId String
  user   User   @relation(fields: [userId], references: [id])

  @@index([code])
  @@index([userId])
}

// Created when a user tries to login and needs to pass MFA verification
model PendingSession {
  id     String @id
  userId String
  user   User   @relation(fields: [userId], references: [id])

  createdAt DateTime @default(now())
  expiresAt DateTime

  @@index([userId])
}

model UserStatusHistory {
  id              String  @id
  userId          String
  isSuspended     Boolean
  notes           String?
  changedByUserId String
  changedByUser   User    @relation("userStatusChangedByUser", fields: [changedByUserId], references: [id])

  changedAt DateTime
  user      User     @relation(fields: [userId], references: [id])

  createdAt DateTime  @default(now())
  updatedAt DateTime? @updatedAt
}

model Person {
  id String @id

  firstName String?
  lastName  String?

  profileImage   Image?  @relation(fields: [profileImageId], references: [id])
  profileImageId String? @unique

  // Email and phone do not have globally unique constraints here because a person might be
  // associated with multiple businesses, so they would have a Person row for each business.
  // E.g. if a person works at one chick-fil-a for some time and then transfers to another.
  email       String?
  phoneNumber String?

  birthdate           DateTime?
  hometown            String?
  timezone            String?
  chickfilaEmployeeId String?
  preferredLanguage   String
  isArchived          Boolean   @default(false)

  addressId String?
  address   StreetAddress? @relation(fields: [addressId], references: [id])

  generators        Generator[] @relation("PersonGenerators")
  relatedGenerators Generator[] @relation("PersonGen")

  // TODO move business to a join table, so that people may have multiple businesses
  // and GIOA admins can have a person without a business
  businessId String?
  business   Business? @relation(fields: [businessId], references: [id])

  // arbitrary stuff we want to associate with the person, such as nickname,
  // high school mascot, spouse's name, etc.
  metadata Json?

  emergencyContactName        String?
  emergencyContactPhoneNumber String?
  biography                   String?

  user                     User?
  invitations              BusinessInvitation[]
  trainingHistory          PositionTrainingHistory[]
  employments              Employment[]
  employmentRequests       EmploymentRequest[]
  metrics                  Metric[]
  domainEvents             DomainEvent[]
  domainEventsRequested    DomainEvent[]             @relation("requestedByPerson")
  notes                    EntityNote[]
  notesHistory             EntityNoteHistory[]
  createdNotes             EntityNote[]              @relation("noteCreatedByPerson")
  updatedNotes             EntityNote[]              @relation("noteUpdatedByPerson")
  // When a note history is created, it is the person who created the note that is recorded.
  notesHistoryCreatedBy    EntityNoteHistory[]       @relation("noteHistoryCreatedByPerson")
  recordedMetricDataPoints MetricDataPoint[]
  recorderMetrics          Metric[]                  @relation("Recorder")
  updatedCorrectiveActions CorrectiveAction[]        @relation("correctiveActionUpdatedByPerson")
  correctiveActionsHistory CorrectiveActionHistory[] @relation("HistoryUpdatedByPerson")

  createdAt                        DateTime                  @default(now())
  updatedAt                        DateTime?                 @updatedAt
  correctiveActionsRecorded        CorrectiveAction[]        @relation("Recorder")
  correctiveActionsReceived        CorrectiveAction[]        @relation("Received")
  correctiveActionsReviewed        CorrectiveAction[]        @relation("Reviewer")
  correctiveActionsRecordedHistory CorrectiveActionHistory[] @relation("HistoryRecorder")
  correctiveActionsReceivedHistory CorrectiveActionHistory[] @relation("HistoryReceived")
  correctiveActionsReviewedHistory CorrectiveActionHistory[] @relation("HistoryReviewer")
  PersonAvailability               PersonAvailability[]
  Shift                            Shift[]

  timeOff          PersonTimeOff[]
  timeOffUpdated   PersonTimeOff[] @relation("TimeOffUpdater")
  timeOffApproved  PersonTimeOff[] @relation("TimeOffApprover")
  timeOffDeclined  PersonTimeOff[] @relation("TimeOffDecliner")
  timeOffSubmitted PersonTimeOff[] @relation("TimeOffSubmitter")
  timeOffCancelled PersonTimeOff[] @relation("TimeOffCanceller")

  shiftOffersOffered         ShiftOffer[] @relation("ShiftOfferredToPerson")
  shiftOffersSubmitted       ShiftOffer[] @relation("ShiftOfferSubmittedBy")
  shiftOffersApproved        ShiftOffer[] @relation("ShiftOfferApprovedByPerson")
  shiftOffersDeclined        ShiftOffer[] @relation("ShiftOfferDeclinedByPerson")
  shiftOffersCancelled       ShiftOffer[] @relation("ShiftOfferCancelledByPerson")
  shiftOfferApprovedAcceptor ShiftOffer[] @relation("ShiftOfferApprovedAcceptorPerson")

  ShiftOfferAcceptance ShiftOfferAcceptance[]

  publishedSchedules PublishedScheduleHistory[]

  personAvailabilitySubmitted PersonAvailability[] @relation("PersonAvailabilitySubmitter")
  personAvailabilityApproved  PersonAvailability[] @relation("PersonAvailabilityApprover")
  personAvailabilityDeclined  PersonAvailability[] @relation("PersonAvailabilityDecliner")
  personAvailabilityCanceller PersonAvailability[] @relation("PersonAvailabilityCanceller")
  scheduleEventsCreated       ScheduleEvent[]      @relation("EventCreatedByPerson")
  scheduleEventsUpdated       ScheduleEvent[]      @relation("EventUpdatedByPerson")
  acknowledgedEvents          ScheduleEvent[]      @relation("AcknowledgedEvents")
  archivedScheduleEvents      ScheduleEvent[]      @relation("ScheduleEventArchivedByPerson")
  DataFileUploadHistory       DataFile[]
  DataFileJob                 DataFileJob[]

  notificationSettings PersonNotificationSettings[]

  checklistItemProgress           TeamMemberChecklistItemProgress[]
  checklistRequirementCompletions ChecklistRequirementCompletion[]
  assignedChecklistTemplates      ChecklistTemplate[]               @relation("AssignedChecklistTemplate")
  assignedChecklistGenerators     ChecklistGenerator[]              @relation("AssignedChecklistGenerator")
  assignedChecklistEvents         ChecklistEventPersonAssignment[]
  assignedChecklistItems          ChecklistItemPersonAssignment[]
  checklistsArchived              ChecklistEvent[]                  @relation("ChecklistArchivedByPerson")
  checklistTemplatesArchived      ChecklistTemplate[]               @relation("ChecklistTemplateArchivedByPerson")

  createdSetupSheets         SetupSheet[]
  createdSetupSheetTemplates SetupSheetTemplate[]
  setupSheetShifts           SetupSheetPositionShift[]
  shiftBreaks                ShiftBreak[]
  notificationSendRecipient  NotificationSend[]        @relation("RecipientPerson")
  notifications              NotificationSend[]        @relation("NotificationSentToPerson")
  SystemAlertInteraction     SystemAlertInteraction[]

  swapsSubmitted            ShiftSwap[]                 @relation("ShiftSwapOfferSubmittedBy")
  swapsReceived             ShiftSwap[]                 @relation("ShiftSwapOfferedToPerson")
  swapsAccepted             ShiftSwap[]                 @relation("ShiftSwapApprovedByPerson")
  swapsDeclined             ShiftSwap[]                 @relation("ShiftSwapDeclinedByPerson")
  swapsCancelled            ShiftSwap[]                 @relation("ShiftSwapCancelledByPerson")
  houseOffersCreated        ShiftOffer[]                @relation("ShiftOfferCreatedByPerson")
  dataFileArchived          DataFile[]                  @relation("DataFileArchivedByPerson")
  salesForecastJobCreated   SalesForecastJob[]
  salesForecastJobArchived  SalesForecastJob[]          @relation("SalesForecastJobArchivedByPerson")
  payRatesUpdated           StoreEmployment[]           @relation("PayRateUpdatedByPerson")
  vEventsArchived           VEvent[]                    @relation("VEventArchivedByPerson")
  storeDevicesCreated       StoreDevice[]               @relation("StoreDeviceCreatedByPerson")
  storeDeviceSessionHistory StoreDeviceSessionHistory[]
  DraftScheduleHistory      DraftScheduleHistory[]

  scheduleDraftsChanged Schedule[] @relation("latestDraftChangeByPerson")

  attachments      FileAttachment[]
  timePunchEntries TimePunchEntry[]

  scheduleEventsRecipient      ScheduleEventRecipient[]
  scheduleEventAcknowledgments DigitalSignatureAcknowledgement[]

  createdLayouts StoreLayout[]

  SetupActivity        SetupActivity[]        @relation("setupActivityPerson")
  SetupHistoryActivity SetupHistoryActivity[] @relation("setupHistoryActivityPerson")

  tutorialStepsCompletion TutorialStepsCompletion[]

  newspapersRead NewspaperReadReceipt[]

  updatedIsWorkModeEnabled Store[] @relation("isWorkModeEnabledUpdatedByPerson")

  pinnedChannels PinnedChannel[]

  @@unique([businessId, email])
  @@unique([businessId, phoneNumber])
}

model EntityNote {
  id         String   @id
  businessId String
  business   Business @relation(fields: [businessId], references: [id])

  storeId String?
  store   Store?  @relation(fields: [storeId], references: [id])

  // can add more nullable columns for other entities in future
  personId String?
  person   Person? @relation(fields: [personId], references: [id])

  timeOffRequestId String?
  timeOffRequest   PersonTimeOff? @relation(fields: [timeOffRequestId], references: [id])

  availabilityRequestId String?
  availabilityRequest   PersonAvailability? @relation(fields: [availabilityRequestId], references: [id])

  shiftOfferId String?
  shiftOffer   ShiftOffer? @relation(fields: [shiftOfferId], references: [id])

  scheduleId String?
  schedule   Schedule? @relation(fields: [scheduleId], references: [id])

  shiftSwapId String?
  shiftSwap   ShiftSwap? @relation(fields: [shiftSwapId], references: [id])

  scheduleWeekDay Int?

  createdByPersonId String?
  createdByPerson   Person? @relation("noteCreatedByPerson", fields: [createdByPersonId], references: [id])

  images Image[]

  note String

  // like coaching moment, positive feedback, general note, etc.
  noteType String?

  // Was this note created from a CorrectiveAction?
  fromCorrectiveActionId String?
  fromCorrectiveAction   CorrectiveAction? @relation(fields: [fromCorrectiveActionId], references: [id])

  // coaching moment policies in action
  policiesInAction        String[]  @default([])
  recipientStatement      String?
  requiresAcknowledgement Boolean   @default(false)
  isAcknowledged          Boolean   @default(false)
  acknowledgedAt          DateTime? // it's acknowledged by the `personId` person

  isArchived Boolean   @default(false)
  archivedAt DateTime?

  notificationSends NotificationSend[]

  // any metadata associated with the note
  metadata Json?

  // 10 = low, 20 = medium, 30 = high
  sensitivityLevel Int @default(10)

  createdAt   DateTime  @unique @default(now())
  updatedAt   DateTime? @updatedAt
  updatedById String?
  updatedBy   Person?   @relation("noteUpdatedByPerson", fields: [updatedById], references: [id])

  // The current version of the EntityNote
  version Int                 @default(1)
  history EntityNoteHistory[]

  @@index([shiftSwapId])
  @@index([businessId])
  @@index([storeId])
  @@index([personId])
  @@index([timeOffRequestId])
  @@index([availabilityRequestId])
  @@index([shiftOfferId])
  @@index([scheduleId])
  @@index([createdByPersonId])
  @@index([updatedById])
  @@index([noteType])
  @@index([fromCorrectiveActionId])
  @@index([sensitivityLevel])
}

model EntityNoteHistory {
  id String @id

  entityNoteId String
  entityNote   EntityNote @relation(fields: [entityNoteId], references: [id])

  businessId String
  business   Business @relation(fields: [businessId], references: [id])

  storeId String?
  store   Store?  @relation(fields: [storeId], references: [id])

  // can add more nullable columns for other entities in future
  personId String?
  person   Person? @relation(fields: [personId], references: [id])

  timeOffRequestId String?
  timeOffRequest   PersonTimeOff? @relation(fields: [timeOffRequestId], references: [id])

  availabilityRequestId String?
  availabilityRequest   PersonAvailability? @relation(fields: [availabilityRequestId], references: [id])

  shiftOfferId String?
  shiftOffer   ShiftOffer? @relation(fields: [shiftOfferId], references: [id])

  scheduleId String?
  schedule   Schedule? @relation(fields: [scheduleId], references: [id])

  shiftSwapId String?
  shiftSwap   ShiftSwap? @relation(fields: [shiftSwapId], references: [id])

  scheduleWeekDay Int?

  // This reflects the person who made the change of this version,
  // not the person making the edit to cause this version to be created.
  // e.g.: Bob is making the first edit of a note that Paul created.
  //       This would represent Paul, since it's Paul's version being recorded.
  createdByPersonId String?
  createdByPerson   Person?   @relation("noteHistoryCreatedByPerson", fields: [createdByPersonId], references: [id])
  createdAt         DateTime @default(now())

  images Image[]

  note String

  // like coaching moment, positive feedback, general note, etc.
  noteType String?

  // Was this note created from a CorrectiveAction?
  fromCorrectiveActionId String?
  fromCorrectiveAction   CorrectiveAction? @relation(fields: [fromCorrectiveActionId], references: [id])

  // coaching moment policies in action
  policiesInAction        String[]  @default([])
  recipientStatement      String?
  requiresAcknowledgement Boolean   @default(false)
  isAcknowledged          Boolean   @default(false)
  acknowledgedAt          DateTime? // it's acknowledged by the `personId` person

  isArchived Boolean   @default(false)
  archivedAt DateTime?

  // any metadata associated with the note
  metadata Json?

  // 10 = low, 20 = medium, 30 = high
  sensitivityLevel Int @default(10)

  version            Int
  effectiveDateStart DateTime
  effectiveDateEnd   DateTime

  @@unique([entityNoteId, version])
  @@index([entityNoteId])
  @@index([shiftSwapId])
  @@index([businessId])
  @@index([storeId])
  @@index([personId])
  @@index([timeOffRequestId])
  @@index([availabilityRequestId])
  @@index([shiftOfferId])
  @@index([scheduleId])
  @@index([createdByPersonId])
  @@index([noteType])
  @@index([fromCorrectiveActionId])
  @@index([sensitivityLevel])
}

// polymorphic table which can contain both UserReminder or UserEvent rows
model Generator {
  userId      String?
  id          String   @id
  title       String
  description String?
  time        DateTime
  status      String   @default("pending")
  timezone    String?

  message    String?
  // The original voice input text that was used to create this reminder
  voiceInput String?

  leadScheduleId String?
  leadSchedule   LeadSchedule? @relation(fields: [leadScheduleId], references: [id])

  createdAt DateTime  @default(now())
  updatedAt DateTime? @updatedAt

  scheduledEvents       ScheduledEvent[]
  generatorGroups       GeneratorGroups[]
  generatorStatusChange GeneratorStatusChange[]
  personRecipients      Person[]                @relation("PersonGenerators")

  // the recipients of this generator. This is a union of the different types of recipients that can be specified for a generator.
  // If personRecipients is empty, recipients will be used instead.
  recipients Json?

  // an optional shift. This is necessary to locate the generators associated with shifts, such as reminders to team members 30 minutes before their shift.
  shiftId String?
  // if the shift gets deleted, delete any generators pointing to it. It typically doesn't make sense to remind people about a deleted shift.
  // Note that if we set shiftId to null instead of cascade delete, then the process that created this generator would have no way of locating it to delete it anyway!
  shift   Shift?  @relation(fields: [shiftId], references: [id], onDelete: Cascade)

  // the person that this generator "concerns". Most likely would also be a recipient, but not necessarily. This is for querying purposes for the various processes that may create generators for their own purposes. They can use it for what they want to use it for.
  personId String?
  // If the person is deleted, delete the generators pointing to it
  person   Person? @relation("PersonGen", fields: [personId], references: [id], onDelete: Cascade)

  // for querying purposes
  scheduleId String?
  schedule   Schedule? @relation(fields: [scheduleId], references: [id], onDelete: Cascade)

  scheduleEventId String?
  scheduleEvent   ScheduleEvent? @relation(fields: [scheduleEventId], references: [id], onDelete: Cascade)

  // this helps disambiguate generator instances for different purposes. E.g. two generators might have the same shift but different purposes -- one for reminding the team member of their shift 30 minutes before, and another to remind their Shift Leader that certain checklist items should be completed during that shift. Different purposes would be managed by different modules, and those modules need a way to find the generators that they created. That's what generatorPurpose gives them.
  generatorPurpose String?

  @@index([shiftId, generatorPurpose])
  @@index([userId])
  @@index([leadScheduleId])
  @@index([userId, time])
}

model BusinessInvitation {
  id                  String    @id
  code                String    @default("")
  codeHash            String    @unique
  personId            String
  person              Person    @relation(fields: [personId], references: [id])
  message             String
  expiresAt           DateTime
  sentAt              DateTime?
  acceptedAt          DateTime?
  businessId          String
  business            Business  @relation(fields: [businessId], references: [id])
  isAccepted          Boolean
  verifiesPhoneNumber Boolean
  verifiesEmail       Boolean
  requiresApproval    Boolean
  transport           String?
  subject             String?

  // Override the role from the job -- this will set the role on the person's UserEmploymentRole
  // specifically. TODO deprecated
  roleId String?
  role   Role?   @relation(fields: [roleId], references: [id])

  // Gets set on the user when they accept the invitation.
  // Controls where they get directed to when they log in
  initDestination String?

  createdAt DateTime  @default(now())
  updatedAt DateTime? @updatedAt

  @@index([businessId, personId])
}

model Job {
  id         String   @id
  businessId String
  business   Business @relation(fields: [businessId], references: [id])

  title       String
  description String?
  quantity    Int?
  level       Int

  // this is for tracking the order on the UI.
  order Int @default(0)

  // TODO obsolete -- roles will be removed
  defaultRoleId String
  defaultRole   Role   @relation(fields: [defaultRoleId], references: [id])

  // Every job comes with its own permission
  // policy that can be edited by the business.
  permissionPolicy Json @default("{}")

  isActive              Boolean             @default(true)
  isBusinessJob         Boolean
  isStoreJob            Boolean
  createdFromTemplateId String?
  employments           Employment[]
  employmentRequests    EmploymentRequest[]

  createdAt DateTime  @default(now())
  updatedAt DateTime? @updatedAt

  @@index([title])
  @@index([businessId, isActive])
}

model StoreArea {
  id                    String          @id
  storeId               String
  store                 Store           @relation(fields: [storeId], references: [id], onDelete: Cascade)
  title                 String
  description           String?
  isActive              Boolean         @default(true)
  createdFromTemplateId String?
  storePositions        StorePosition[]

  setupSheetTemplateAreas SetupSheetTemplateArea[]
  setupSheetAreas         SetupSheetArea[]

  order Int @default(0)

  createdAt           DateTime             @default(now())
  updatedAt           DateTime?            @updatedAt
  ShiftArea           ShiftArea[]
  ChecklistTemplate   ChecklistTemplate[]
  ChecklistGenerator  ChecklistGenerator[]
  ChecklistEvent      ChecklistEvent[]
  shifts              Shift[]
  setupSheetPositions SetupSheetPosition[]

  @@index([storeId, isActive])
}

model StorePosition {
  id                    String    @id
  title                 String
  description           String?
  createdFromTemplateId String?
  isActive              Boolean   @default(true)
  quantity              Int?
  storeId               String
  store                 Store     @relation(fields: [storeId], references: [id], onDelete: Cascade)
  areaId                String
  area                  StoreArea @relation(fields: [areaId], references: [id])

  // for ordering on the UI
  order Int @default(0)

  trainings PositionTraining[]
  metrics   Metric[]

  setupSheetTemplatePositions SetupSheetTemplatePosition[]
  setupSheetPositions         SetupSheetPosition[]

  createdAt          DateTime             @default(now())
  updatedAt          DateTime?            @updatedAt
  Shift              Shift[]
  ChecklistTemplate  ChecklistTemplate[]
  ChecklistGenerator ChecklistGenerator[]
  ChecklistEvent     ChecklistEvent[]

  @@index([storeId, areaId, isActive])
}

model PositionTraining {
  id              String        @id
  storePositionId String
  storePosition   StorePosition @relation(fields: [storePositionId], references: [id])
  title           String

  isActive                Boolean                          @default(true)
  description             String?
  createdFromTemplateId   String?
  trainingHistory         PositionTrainingHistory[]
  trainingHistoryRequests PositionTrainingHistoryRequest[]
  metrics                 Metric[]

  createdAt DateTime  @default(now())
  updatedAt DateTime? @updatedAt

  @@index([storePositionId])
}

model PositionTrainingHistory {
  id                 String           @id
  personId           String
  person             Person           @relation(fields: [personId], references: [id])
  positionTrainingId String
  positionTraining   PositionTraining @relation(fields: [positionTrainingId], references: [id])
  isCompleted        Boolean
  isPersonPreferred  Boolean

  startedAt   DateTime?
  completedAt DateTime?

  createdAt DateTime  @default(now())
  updatedAt DateTime? @updatedAt

  @@index([personId])
  @@index([positionTrainingId])
}

model CoreValue {
  id          String   @id
  title       String
  description String?
  businessId  String
  business    Business @relation(fields: [businessId], references: [id])

  storeId         String?
  store           Store?           @relation(fields: [storeId], references: [id])
  metrics         Metric[]
  metricTemplates MetricTemplate[]

  isActive Boolean @default(true)

  createdAt DateTime  @default(now())
  updatedAt DateTime? @updatedAt

  @@index([storeId])
  @@index([businessId])
}

model StreetAddress {
  id      String   @id
  line1   String
  line2   String?
  city    String
  state   String
  zipCode String
  country String   @default("USA")
  stores  Store[]
  persons Person[]
  vendors Vendor[]
}

model Store {
  id                    String   @id
  chickfilaStoreId      String
  businessId            String
  business              Business @relation(fields: [businessId], references: [id])
  title                 String
  storeType             String?
  phoneNumber           String?
  email                 String?
  timezone              String? // TODO make required after deployment and backfill
  isSchedulingOnboarded Boolean  @default(false)

  dayParts        Json     @default("[]") // z.array(dayPart).min(0).max(20)
  peakHours       Json     @default("[]") // z.array(weekDayTimeRange)
  storeHoursStart DateTime @default("2000-01-01T06:00:00Z") @db.Time(0) // 24hr time
  storeHoursEnd   DateTime @default("2000-01-01T21:00:00Z") @db.Time(0) // 24hr time

  imageId String?
  image   Image?  @relation(fields: [imageId], references: [id])

  addressId String
  address   StreetAddress @relation(fields: [addressId], references: [id])

  areas                    StoreArea[]
  positions                StorePosition[]
  coreValues               CoreValue[]
  employmentRequests       StoreEmploymentRequest[]
  employments              StoreEmployment[]
  metrics                  Metric[]
  domainEvents             DomainEvent[]
  stateMinorWorkGuidelines StateMinorWorkGuidelines[]
  scheduleBreakRules       ScheduleBreakRules[]
  scheduleRequestLeadTime  Int                        @default(7)

  onboardingStartedAt   DateTime?
  onboardingCompletedAt DateTime?

  timeOffRestrictionWeekday             Int       @default(1)
  timeOffRestrictionTime                DateTime? @db.Time(0) // 24hr time
  timeOffRestrictionWindowDurationHours Int       @default(0)

  availabilityRestrictionWeekday             Int       @default(1)
  availabilityRestrictionTime                DateTime? @db.Time(0) // 24hr time
  availabilityRestrictionWindowDurationHours Int       @default(0)

  // Array of strings of the validation message codes to disable:
  // | ShiftDurationExceedsMax
  //   | ShiftDurationLessThanMin
  //   | ShiftOutsideStoreHours
  //   | ShiftUnassigned
  //   | ShiftOverlaps
  //   | ShiftOutsideAvailability
  //   | ShiftConflictsWithTimeOff
  //   | ExceedsMaxHoursPreferred
  //   | ShiftIdNotUnique
  //   | AreaIdNotUnique
  //   | ShiftLeaderNotAssigned
  //   | PersonNotAssignedToStore
  //   | PersonNotActive
  //   | NotTrainedInPosition
  //   | ScheduledOvertimeHours
  //   | MinorSchoolDayMaxHoursPerDay
  //   | MinorMaxHoursPerSchoolWeek
  //   | MinorScheduledBeforePermittedDayStart
  //   | MinorScheduledAfterPermittedDayEnd
  //   | PotentialOvertimeShift
  //   | MinorScheduledCloseToPermittedDayEnd
  //   | CloserOpenerShift
  //   | TimeOffRequestConflict
  //   | MinorOvertimeDay
  //   | MinorOvertimeWeek
  disabledScheduleValidations String[]

  createdAt                   DateTime                           @default(now())
  updatedAt                   DateTime?                          @updatedAt
  correctiveActions           CorrectiveAction[]
  correctiveActionHistory     CorrectiveActionHistory[]
  correctiveActionActionTypes CorrectiveActionCustomActionType[]
  PersonAvailability          PersonAvailability[]
  Schedule                    Schedule[]
  ScheduleEvent               ScheduleEvent[]
  PersonTimeOff               PersonTimeOff[]
  ShiftOffer                  ShiftOffer[]
  timeOffType                 TimeOffType[]
  DataFile                    DataFile[]
  SalesHourlyDataPoint        SalesHourlyDataPoint[]
  EntityNote                  EntityNote[]
  EntityNoteHistory           EntityNoteHistory[]

  teamMemberNotificationSettings PersonNotificationSettings[]

  checklistTemplates  ChecklistTemplate[]
  checklistGenerators ChecklistGenerator[]
  checklistEvents     ChecklistEvent[]
  ChecklistTags       ChecklistTags[]
  setupSheet          SetupSheet[]
  setupSheetTemplate  SetupSheetTemplate[]
  setupDays           SetupDay[]
  shifts              Shift[]
  shiftSwaps          ShiftSwap[]

  notificationSend NotificationSend[]
  systemAlerts     SystemAlert[]

  resources StoreResource[]
  vendors   Vendor[]

  DataFileJob                 DataFileJob[]
  SalesForecastJob            SalesForecastJob[]
  ScheduleHourlySalesForecast ScheduleHourlySalesForecast[]

  vchecklistTemplates            VChecklistTemplate[]
  vchecklistInteractionSummaries VChecklistInteractionSummary[]

  vevents      VEvent[]
  storeDevices StoreDevice[]

  sessions Session[]

  layouts StoreLayout[]

  // When work mode is enabled it allows team members to skip acknowledgments, and other "app-locking" features outside
  // of their work hours.
  isWorkModeEnabled Boolean @default(false)
  isWorkModeEnabledUpdatedAt DateTime?
  isWorkModeEnabledUpdatedByPersonId String?
  isWorkModeEnabledUpdatedByPerson Person? @relation("isWorkModeEnabledUpdatedByPerson", fields: [isWorkModeEnabledUpdatedByPersonId], references: [id])


  // this can be set by a switch in Nation admin. This gives this store a plan without an active Subscription. I just know some store is going to forget to pay their bill and we’ll have to extend them beyond their grace period while they wire us the money or mail a check. It happens more often than you might think. Also the override plan will be useful for our demo and test accounts, so that those accounts done pollute our Stripe subscription reports.
  overridePlanId String?
  overridePlan   Plan?   @relation(fields: [overridePlanId], references: [id])

  // if true, denies all access from the store. It will log out all TMs and not allow logging in. This a “kill switch” for stores in case we need to hard deny access.
  denyAccess Boolean @default(false)

  // So that we can correlate webhooks with the correct store, query Stripe API for Customer payment method status, etc.
  stripeCustomerId String? // TODO make @unique after backfill

  // If their Stripe Customer has a valid default payment method. This will be synced by webhooks. This helps us in our payment method collection process.
  hasPaymentMethod            Boolean @default(false)
  paymentMethodInvalidCode    String?
  paymentMethodInvalidMessage String?

  // if the store doesn't have a subscription, our system falls back to this trial end date and prompts the user to start their subscription as they approach that date
  trialEnd   DateTime?
  goLiveDate DateTime?

  subscriptions  Subscription[]
  StoreTimeFrame StoreTimeFrame[]

  timePunchEntries     TimePunchEntry[]
  setupPositions       Json? // type ValidStoreSetupPositions
  SetupActivity        SetupActivity[]
  SetupHistoryActivity SetupHistoryActivity[]

  tutorialStepsCompletion TutorialStepsCompletion[]
  SetupSheetTag           SetupSheetTag[]

  @@index([businessId])
  @@index([stripeCustomerId])
}

model Business {
  id                  String               @id
  doingBusinessAs     String?
  legalBusinessName   String?
  chickfilaOperatorId String
  startedAt           DateTime?
  isArchived          Boolean              @default(false)
  invitations         BusinessInvitation[]
  jobs                Job[]
  stores              Store[]
  coreValues          CoreValue[]
  metrics             Metric[]
  domainEvents        DomainEvent[]
  people              Person[]
  employments         Employment[]
  employmentsRequests EmploymentRequest[]
  metricTemplates     MetricTemplate[]
  notes               EntityNote[]
  notesHistory        EntityNoteHistory[]

  createdAt                         DateTime                           @default(now())
  updatedAt                         DateTime?                          @updatedAt
  correctiveActionPolicies          CorrectiveActionPolicy[]
  correctiveActions                 CorrectiveAction[]
  correctiveActionHistory           CorrectiveActionHistory[]
  correctiveActionCustomActionTypes CorrectiveActionCustomActionType[]
  Schedule                          Schedule[]
  ScheduleEvent                     ScheduleEvent[]
  DataFileUploadHistory             DataFile[]
  systemAlerts                      SystemAlert[]
  InternalDomainEvent               InternalDomainEvent[]
  FeatureBusinessOverride           FeatureBusinessOverride[]
  StoreDevice                       StoreDevice[]
  timePunchEntries                  TimePunchEntry[]
  storeLayouts                      StoreLayout[]
  setupDays                         SetupDay[]
  StoreTimeFrame                    StoreTimeFrame[]
  ScheduledAction                   ScheduledAction[]
}

model Employment {
  id       String @id
  personId String
  person   Person @relation(fields: [personId], references: [id])
  jobId    String
  job      Job    @relation(fields: [jobId], references: [id])

  // a permissions policy for this specific person in this employment, which gets merged
  // with the job's permission policy. This allows control over permissions at the
  // individual person level.
  permissionPolicy Json?

  // businessId is just for ease of querying, the business can be inferred from the job
  businessId String
  business   Business @relation(fields: [businessId], references: [id])

  startedAt DateTime?
  endedAt   DateTime?
  userRoles UserEmploymentRole[] // TODO remove
  stores    StoreEmployment[]

  createdAt DateTime  @default(now())
  updatedAt DateTime? @updatedAt

  @@index([personId])
  @@index([jobId])
}

// TODO remove
model UserEmploymentRole {
  id           String     @id
  userId       String
  user         User       @relation(fields: [userId], references: [id])
  employmentId String
  employment   Employment @relation(fields: [employmentId], references: [id])
  roleId       String
  role         Role       @relation(fields: [roleId], references: [id])

  createdAt DateTime  @default(now())
  updatedAt DateTime? @updatedAt

  @@index([userId])
  @@index([employmentId])
}

model EmploymentRequest {
  id       String @id
  personId String
  person   Person @relation(fields: [personId], references: [id])
  jobId    String
  job      Job    @relation(fields: [jobId], references: [id])

  // a permissions policy for this specific person in this employment, which gets merged
  // with the job's permission policy. This allows control over permissions at the
  // individual person level.
  permissionPolicy Json?

  startedAt       DateTime?
  endedAt         DateTime?
  isApproved      Boolean
  stores          StoreEmploymentRequest[]
  trainingHistory PositionTrainingHistoryRequest[]

  createdAt  DateTime  @default(now())
  updatedAt  DateTime? @updatedAt
  Business   Business? @relation(fields: [businessId], references: [id])
  businessId String?

  @@index([personId])
  @@index([jobId])
}

model StoreEmploymentRequest {
  id                  String            @id
  employmentRequestId String
  employmentRequest   EmploymentRequest @relation(fields: [employmentRequestId], references: [id], onDelete: Cascade)
  storeId             String
  store               Store             @relation(fields: [storeId], references: [id])
  startedAt           DateTime?
  endedAt             DateTime?
  isApproved          Boolean           @default(false)
  isSubmitted         Boolean           @default(false)
  proficiencyRanking  Int               @default(1)

  createdAt DateTime  @default(now())
  updatedAt DateTime? @updatedAt

  @@index([employmentRequestId])
  @@index([storeId])
}

model StoreEmployment {
  id           String     @id
  employmentId String
  employment   Employment @relation(fields: [employmentId], references: [id], onDelete: Cascade)
  storeId      String
  store        Store      @relation(fields: [storeId], references: [id])

  startedAt          DateTime?
  endedAt            DateTime?
  proficiencyRanking Int       @default(1)
  payRate            Int? // cents per hour

  createdAt DateTime  @default(now())
  updatedAt DateTime? @updatedAt

  isStoreLoginPinExplicitlySet Boolean @default(false)
  storeLoginPin                String  @default("")

  payRateUpdatedAt         DateTime? @updatedAt
  payRateUpdatedByPersonId String?
  payRateUpdatedByPerson   Person?   @relation("PayRateUpdatedByPerson", fields: [payRateUpdatedByPersonId], references: [id])
  // TODO after backfill
  // @@unique([storeId, storeLoginPin])

  @@index([employmentId])
  @@index([storeId, storeLoginPin])
}

model PositionTrainingHistoryRequest {
  id                  String            @id
  employmentRequestId String
  employmentRequest   EmploymentRequest @relation(fields: [employmentRequestId], references: [id])
  positionTrainingId  String
  positionTraining    PositionTraining  @relation(fields: [positionTrainingId], references: [id])
  isCompleted         Boolean
  isPersonPreferred   Boolean

  startedAt   DateTime?
  completedAt DateTime?

  createdAt DateTime  @default(now())
  updatedAt DateTime? @updatedAt

  @@index([employmentRequestId])
  @@index([positionTrainingId])
}

model MetricTemplate {
  id          String  @id
  namespace   String
  name        String
  description String?

  businessId String
  business   Business @relation(fields: [businessId], references: [id])
  isActive   Boolean  @default(true)

  coreValueId String?
  coreValue   CoreValue? @relation(fields: [coreValueId], references: [id])

  createdAt DateTime  @default(now())
  updatedAt DateTime? @updatedAt
}

model Metric {
  id                    String    @id
  createdFromTemplateId String?
  namespace             String
  name                  String
  description           String?
  businessId            String
  business              Business  @relation(fields: [businessId], references: [id])
  createdAt             DateTime  @default(now())
  updatedAt             DateTime? @updatedAt

  // dimensions
  coreValueId      String?
  coreValue        CoreValue?        @relation(fields: [coreValueId], references: [id])
  storeId          String?
  store            Store?            @relation(fields: [storeId], references: [id])
  storePositionId  String?
  storePosition    StorePosition?    @relation(fields: [storePositionId], references: [id])
  trainingId       String?
  training         PositionTraining? @relation(fields: [trainingId], references: [id])
  personId         String?
  person           Person?           @relation(fields: [personId], references: [id])
  recorderPersonId String?
  recorderPerson   Person?           @relation("Recorder", fields: [recorderPersonId], references: [id])
  scheduleId       String?
  schedule         Schedule?         @relation(fields: [scheduleId], references: [id])
  weekday          Int?

  dataPoints MetricDataPoint[]

  @@index(coreValueId)
  @@index([businessId, namespace, name])
  @@index([businessId, storeId, namespace, name])
}

enum Unit {
  SECONDS
  MICROSECONDS
  MILLISECONDS
  BYTES
  KILOBYTES
  MEGABYTES
  GIGABYTES
  TERABYTES
  BITS
  KILOBITS
  MEGABITS
  GIGABITS
  TERABITS
  PERCENT
  COUNT
  BYTES_PER_SECOND
  KILOBYTES_PER_SECOND
  MEGABYTES_PER_SECOND
  GIGABYTES_PER_SECOND
  TERABYTES_PER_SECOND
  BITS_PER_SECOND
  KILOBITS_PER_SECOND
  MEGABITS_PER_SECOND
  GIGABITS_PER_SECOND
  TERABITS_PER_SECOND
  COUNT_PER_SECOND
}

model MetricDataPoint {
  id        String   @id
  metricId  String
  metric    Metric   @relation(fields: [metricId], references: [id])
  timestamp DateTime @unique @db.Timestamptz(6)
  value     Float
  unit      Unit

  // metadata
  recorderPersonId String?
  recorderPerson   Person? @relation(fields: [recorderPersonId], references: [id])
  notes            String?

  @@index([metricId, value])
  @@index([metricId, timestamp, value])
  @@index([recorderPersonId])
}

model DomainEvent {
  id         String   @id
  occurredAt DateTime
  version    String // event schema version
  eventType  String
  eventData  Json

  // The version of the domain entity that this event was created for
  eventOnVersion BigInt?

  requestingPersonId String?
  requestingPerson   Person? @relation("requestedByPerson", fields: [requestingPersonId], references: [id])
  requestingService  String?

  businessId String?
  business   Business? @relation(fields: [businessId], references: [id])
  storeId    String?
  store      Store?    @relation(fields: [storeId], references: [id])
  personId   String?
  person     Person?   @relation(fields: [personId], references: [id])
  scheduleId String?
  schedule   Schedule? @relation(fields: [scheduleId], references: [id])

  @@index([businessId, storeId])
  @@index([storeId])
  @@index([personId])
  @@index([scheduleId])
}

// InternalDomainEvent is a newer schema for DomainEvents. The original DomainEvents table uses a UUID primary key, which
// is not suitable for doing concurrency control in an event sourcing system. As well as other incompatibilities with
// event sourcing, such as lack of streamId, use of foreign keys, etc. Rather than disrupt the existing
// table and code, I'm adding a new table that is better geared towards event sourcing.
model InternalDomainEvent {
  // An autoincrementing int enforces an ordering to events. This is necessary and important for various uses in
  // event sourcing, such as concurrency control and conditional writes.
  id BigInt @id @default(autoincrement())

  // The streamId may be used to identify the set of events associated with a particular domain object.
  // For example, "stream_customer_47" for all events related to the customer with id 47. It is not mandatory
  // that you use a streamId however. You could alternatively rely on the "tag" columns (businessId, storeId, personTimeOffId, etc)
  // to identify sets of events. Whatever works best for your use case.
  streamId String?

  createdAt DateTime @default(now())

  // the schema version of the eventData. E.g. if we change the structure of the eventData for an event, this
  // version column will tell the code that replays events what structure to expect.
  version Int

  eventType String
  eventData Json

  requestingPersonId String?
  requestingService  String?

  impersonatedByPersonId String?

  // the domain object version the client had when the client made this mutation
  clientVersion BigInt?
  // the ID of the client that made this mutation
  clientId      String?
  // the time on the client when the client made this mutation
  clientTime    DateTime?

  // Why no foreign keys here? Because the rest of the tables in this database are considered "projections", in event sourcing terms.
  // Projections don't necessarily have to exist. In an event sourcing system, it should be possible to rely entirely on
  // the events if you want to. So we refer to these columns as "tags".
  personTimeOffId               String?
  storeId                       String?
  personId                      String?
  scheduleId                    String?
  scheduleHourlySalesForecastId String?
  checklistTemplateId           String?
  checklistId                   String?
  checklistRecurrenceId         DateTime?
  checklistItemId               String?
  setupDayId                    String?

  // The businessId here is required and has a FK, because it is used to separate different business's events.
  businessId String
  business   Business @relation(fields: [businessId], references: [id])

  @@index([streamId])
  @@index([personTimeOffId])
  @@index([businessId])
  @@index([storeId])
  @@index([personId])
  @@index([scheduleId])
  @@index([scheduleHourlySalesForecastId])
  @@index([checklistTemplateId])
  @@index([checklistId, checklistRecurrenceId, checklistItemId])
  @@index([setupDayId])
}

model UserClient {
  userId String
  user   User   @relation(references: [id], fields: [userId], onDelete: Cascade)
  id     String

  createdAt     DateTime
  description   String?
  lastUserAgent String?
  title         String?
  // if user has logged in with this client, lastLogin will be set
  lastLogin     DateTime?
  lastIpAddress String?
  isTrusted     Boolean
  session       Session[]

  @@id([userId, id])
  @@index([userId])
}

model EmailVerificationCode {
  id        String   @id
  userId    String   @unique
  code      String
  email     String
  expiresAt DateTime
  user      User     @relation(references: [id], fields: [userId], onDelete: Cascade)

  createdAt DateTime @default(now())
}

model RateLimitedCallHistory {
  id        String   @id
  group     String
  createdAt DateTime
  key       String

  @@index([group, key])
  @@index([createdAt])
}

model RateLimiterFlexible {
  key    String    @id
  points Int
  expire DateTime?
}

model PasswordResetCode {
  id        String   @id
  userId    String   @unique
  codeHash  String   @unique
  expiresAt DateTime
  user      User     @relation(references: [id], fields: [userId], onDelete: Cascade)

  createdAt DateTime @default(now())

  @@index([userId])
}

model Feature {
  id                       String                    @id
  name                     String
  description              String
  isEnabled                Boolean
  featureUserOverrides     FeatureUserOverride[]
  featureBusinessOverrides FeatureBusinessOverride[]

  createdAt DateTime  @default(now())
  updatedAt DateTime? @updatedAt

  @@index([name])
}

model FeatureBusinessOverride {
  featureId  String
  feature    Feature  @relation(fields: [featureId], references: [id])
  businessId String
  business   Business @relation(fields: [businessId], references: [id])
  isEnabled  Boolean

  createdAt DateTime  @default(now())
  updatedAt DateTime? @updatedAt

  @@id([featureId, businessId])
}

model FeatureUserOverride {
  id        String  @id
  isEnabled Boolean
  featureId String
  feature   Feature @relation(fields: [featureId], references: [id])
  userId    String
  user      User    @relation(fields: [userId], references: [id])

  createdAt DateTime  @default(now())
  updatedAt DateTime? @updatedAt

  @@index([featureId])
  @@index([userId])
}

model GeneratorGroups {
  userId String
  id     String @id

  createdAt  DateTime    @default(now())
  generators Generator[]
}

model GeneratorStatusChange {
  id           String   @id
  generatorId  String
  status       String // "pending" | "complete"
  changedBy    String
  changeMethod String // "manual" | "sms"
  changedAt    DateTime @default(now())

  generator     Generator @relation(fields: [generatorId], references: [id])
  changedByUser User      @relation(fields: [changedBy], references: [id])

  @@index([generatorId])
  @@index([changedBy])
}

model LeadSchedule {
  userId      String?
  id          String      @id
  generators  Generator[]
  schedule    Json // type LeadSchedule from generators.types.ts
  title       String
  description String?
  icon        String?
  presetTimes String?

  createdAt DateTime  @default(now())
  updatedAt DateTime? @updatedAt

  @@index([userId])
}

model ScheduledEvent {
  id String @id

  generatorId String
  generator   Generator @relation(fields: [generatorId], references: [id], onDelete: Cascade)

  time            DateTime
  // enum: "notification" | "generate"
  type            String
  data            Json
  qstashMessageId String?
  SmsSend         SmsSend[]

  @@index([generatorId])
  @@index([type])
}

model SmsSend {
  id String @id

  personId String
  to       String

  scheduledEventId String?
  scheduledEvent   ScheduledEvent? @relation(fields: [scheduledEventId], references: [id])

  message   String
  createdAt DateTime @default(now())
}

model NotificationSend {
  id          String  @id
  personId    String
  person      Person  @relation(fields: [personId], references: [id], name: "NotificationSentToPerson")
  email       String?
  phoneNumber String?
  pushToken   String?
  messageText String?

  startedAt   DateTime  @default(now())
  completedAt DateTime?
  transports  String[]
  sentCount   Int       @default(0)

  isSuccess            Boolean @default(false)
  numDuplicatesBlocked Int     @default(0)

  isRead Boolean   @default(false)
  readAt DateTime?

  isArchived Boolean   @default(false)
  archivedAt DateTime?

  code String @default("NC_uncategorized")

  // -- Start Entity Relations --
  storeId            String?
  store              Store?              @relation(fields: [storeId], references: [id])
  vEventId           String?
  vEvent             VEvent?             @relation(fields: [vEventId], references: [id])
  checklistEventId   String?
  checklistEvent     ChecklistEvent?     @relation(fields: [checklistEventId], references: [id])
  correctiveActionId String?
  correctiveAction   CorrectiveAction?   @relation(fields: [correctiveActionId], references: [id])
  personTimeOffId    String?
  personTimeOff      PersonTimeOff?      @relation(fields: [personTimeOffId], references: [id])
  shiftId            String?
  shift              Shift?              @relation(fields: [shiftId], references: [id])
  shiftOfferId       String?
  shiftOffer         ShiftOffer?         @relation(fields: [shiftOfferId], references: [id])
  availabilityId     String?
  availability       PersonAvailability? @relation(fields: [availabilityId], references: [id])
  shiftSwapId        String?
  shiftSwap          ShiftSwap?          @relation(fields: [shiftSwapId], references: [id])
  isoDate            String?
  recipientPersonId  String?
  recipientPerson    Person?             @relation(fields: [recipientPersonId], references: [id], name: "RecipientPerson")
  entityNoteId       String?
  entityNote         EntityNote?         @relation(fields: [entityNoteId], references: [id])

  @@index([recipientPersonId])
  @@index([storeId])
  @@index([checklistEventId])
  @@index([correctiveActionId])
  @@index([personTimeOffId])
  @@index([shiftId])
  @@index([shiftOfferId])
  @@index([availabilityId])
  @@index([entityNoteId])
  // -- End Entity Relations --
  @@index([isRead])
  @@index([isSuccess])
  @@index([personId])
}

model TempAuthToken {
  id        String   @id
  userId    String   @unique
  code      String?
  codeHash  String   @unique
  storeId   String
  expiresAt DateTime
  user      User     @relation(references: [id], fields: [userId], onDelete: Cascade)

  @@index([code])
  @@index([userId])
}

model CorrectiveActionPolicy {
  businessId String
  business   Business @relation(fields: [businessId], references: [id], onDelete: Cascade)
  title      String

  @@id([businessId, title])
}

model CorrectiveAction {
  id             String  @id
  isArchived     Boolean @default(false)
  isReviewed     Boolean @default(false)
  isAcknowledged Boolean @default(false)
  isFormalized   Boolean @default(false)

  witnesses  String?
  actionType String?

  infractionAt               DateTime
  actionableItemInfractionAt DateTime
  recordedAt                 DateTime  @unique
  archivedAt                 DateTime?
  acknowledgedAt             DateTime?
  reviewedAt                 DateTime?
  formalizedAt               DateTime?

  recipientPersonId String
  recipientPerson   Person @relation(name: "Received", fields: [recipientPersonId], references: [id])

  recorderPersonId String
  recorderPerson   Person @relation(name: "Recorder", fields: [recorderPersonId], references: [id])

  reviewerPersonId String?
  reviewerPerson   Person? @relation(name: "Reviewer", fields: [reviewerPersonId], references: [id])

  incidentDescription               String
  actionableItemIncidentDescription String
  consequencesDescription           String?
  recipientStatement                String?
  policiesInAction                  String[]
  actionableItemPoliciesInAction    String[]

  storeId              String
  store                Store    @relation(fields: [storeId], references: [id])
  businessId           String
  business             Business @relation(fields: [businessId], references: [id])
  images               Image[]
  actionableItemImages Image[]  @relation("ActionableItemImages")

  notificationSend NotificationSend[]

  // CorrectiveAction (ActionableItems) that were converted into EntityNotes (Coaching Moments)
  toEntityNotes        EntityNote[]
  toEntityNotesHistory EntityNoteHistory[]

  createdAt DateTime @default(now())

  version Int                       @default(1)
  history CorrectiveActionHistory[]

  updatedAt   DateTime? @updatedAt
  updatedById String?
  updatedBy   Person?   @relation("correctiveActionUpdatedByPerson", fields: [updatedById], references: [id])

  @@index([isArchived])
  @@index([isReviewed])
  @@index([isAcknowledged])
  @@index([isFormalized])
  @@index([infractionAt])
  @@index([actionableItemInfractionAt])
  @@index([recordedAt])
  @@index([formalizedAt])
  @@index([recipientPersonId])
  @@index([recorderPersonId])
  @@index([reviewerPersonId])
  @@index([policiesInAction])
  @@index([actionableItemPoliciesInAction])
  @@index([updatedById])
  @@index([storeId])
  @@index([businessId])
}

model CorrectiveActionHistory {
  id String @id

  correctiveActionId String
  correctiveAction   CorrectiveAction @relation(fields: [correctiveActionId], references: [id])

  isArchived     Boolean @default(false)
  isReviewed     Boolean @default(false)
  isAcknowledged Boolean @default(false)
  isFormalized   Boolean @default(false)

  witnesses  String?
  actionType String?

  infractionAt               DateTime
  actionableItemInfractionAt DateTime
  recordedAt                 DateTime
  archivedAt                 DateTime?
  acknowledgedAt             DateTime?
  reviewedAt                 DateTime?
  formalizedAt               DateTime?

  recipientPersonId String
  recipientPerson   Person @relation(name: "HistoryReceived", fields: [recipientPersonId], references: [id])

  recorderPersonId String
  recorderPerson   Person @relation(name: "HistoryRecorder", fields: [recorderPersonId], references: [id])

  reviewerPersonId String?
  reviewerPerson   Person? @relation(name: "HistoryReviewer", fields: [reviewerPersonId], references: [id])

  incidentDescription               String
  actionableItemIncidentDescription String
  consequencesDescription           String?
  recipientStatement                String?
  policiesInAction                  String[]
  actionableItemPoliciesInAction    String[]

  storeId              String
  store                Store    @relation(fields: [storeId], references: [id])
  businessId           String
  business             Business @relation(fields: [businessId], references: [id])
  images               Image[]
  actionableItemImages Image[]  @relation("HistoryActionableItemImages")

  // Version tracking fields
  version            Int
  effectiveDateStart DateTime
  effectiveDateEnd   DateTime

  // This reflects the person who made the change
  createdAt         DateTime @default(now())
  createdByPersonId String
  createdByPerson   Person   @relation("HistoryUpdatedByPerson", fields: [createdByPersonId], references: [id])

  @@unique([correctiveActionId, version])
  @@index([correctiveActionId])
  @@index([businessId])
  @@index([storeId])
  @@index([recipientPersonId])
  @@index([recorderPersonId])
  @@index([reviewerPersonId])
  @@index([createdByPersonId])
}

model CorrectiveActionCustomActionType {
  id         String   @id
  businessId String
  business   Business @relation(fields: [businessId], references: [id], onDelete: Cascade)
  storeId    String
  store      Store    @relation(fields: [storeId], references: [id])

  title String

  isArchived Boolean   @default(false)
  archivedAt DateTime?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@unique([storeId, title])
  @@index([storeId])
  @@index([businessId])
}

model PersonAvailabilityDayTimeRange {
  id                   String             @id
  personAvailabilityId String
  personAvailability   PersonAvailability @relation(fields: [personAvailabilityId], references: [id], onDelete: Cascade)

  dayOfWeek Int
  start     DateTime @db.Time(0)
  end       DateTime @db.Time(0)

  @@index([personAvailabilityId])
}

model PersonAvailability {
  id       String @id
  humanId  Int?
  personId String
  person   Person @relation(fields: [personId], references: [id])
  storeId  String
  store    Store  @relation(fields: [storeId], references: [id])

  // effectiveAt is an abstract date that the user wants the availability to become effective at. It's implicitly in the store's timezone.
  effectiveAt DateTime @db.Date

  // effectiveAtAbs is the effectiveAt date in the store's timezone. It's used for easier querying and calculations. It can be derived from effectiveAt and store.timezone.
  effectiveAtAbs DateTime? // TODO make required after deployment and backfill

  maxHoursPreferred Int?
  maxDaysPreferred  Int?

  isSubmitted         Boolean
  submittedAt         DateTime?
  // TODO make required after deployment
  submittedByPersonId String?
  submittedByPerson   Person?   @relation("PersonAvailabilitySubmitter", fields: [submittedByPersonId], references: [id])
  submittedReason     String?

  isApproved         Boolean
  approvedAt         DateTime?
  approvedByPersonId String?
  approvedByPerson   Person?   @relation("PersonAvailabilityApprover", fields: [approvedByPersonId], references: [id])
  approvedReason     String?

  isDeclined         Boolean
  declinedAt         DateTime?
  declinedByPersonId String?
  declinedByPerson   Person?   @relation("PersonAvailabilityDecliner", fields: [declinedByPersonId], references: [id])
  declinedReason     String?

  isCancelled         Boolean
  cancelledAt         DateTime?
  cancelledByPersonId String?
  cancelledByPerson   Person?   @relation("PersonAvailabilityCanceller", fields: [cancelledByPersonId], references: [id])
  cancelledReason     String?

  createdAt                      DateTime                         @default(now())
  updatedAt                      DateTime?                        @updatedAt
  PersonAvailabilityDayTimeRange PersonAvailabilityDayTimeRange[]
  EntityNote                     EntityNote[]
  entityNoteHistory              EntityNoteHistory[]

  notificationSend NotificationSend[]

  // TODO Need to deploy first before adding unique constraint. Since we already have data in the table.
  // @@unique([storeId, humanId])
  @@index([personId])
  @@index([storeId, personId])
}

model PersonTimeOff {
  id String @id

  // version for concurrency control. This is the ID of the latest InternalDomainEvent that
  // was projected to build this PersonTimeOff.
  version BigInt @default(1)

  personId String
  person   Person @relation(fields: [personId], references: [id])
  storeId  String
  store    Store  @relation(fields: [storeId], references: [id])

  isSubmitted         Boolean
  submittedAt         DateTime?
  submittedReason     String?
  submittedByPersonId String?
  submittedByPerson   Person?   @relation("TimeOffSubmitter", fields: [submittedByPersonId], references: [id])

  personTimeOffTypeTitle String?
  personTimeOffType      TimeOffType? @relation(fields: [storeId, personTimeOffTypeTitle], references: [storeId, title])

  isApproved         Boolean
  approvedAt         DateTime?
  approvedReason     String?
  approvedByPersonId String?
  approvedByPerson   Person?   @relation("TimeOffApprover", fields: [approvedByPersonId], references: [id])

  isDeclined         Boolean
  declinedAt         DateTime?
  declinedReason     String?
  declinedByPersonId String?
  declinedByPerson   Person?   @relation("TimeOffDecliner", fields: [declinedByPersonId], references: [id])

  isCancelled         Boolean
  cancelledAt         DateTime?
  cancelledReason     String?
  cancelledByPersonId String?
  cancelledByPerson   Person?   @relation("TimeOffCanceller", fields: [cancelledByPersonId], references: [id])

  updatedByPersonId String?
  updatedByPerson   Person? @relation("TimeOffUpdater", fields: [updatedByPersonId], references: [id])
  updatedReason     String?

  createdAt DateTime  @default(now())
  updatedAt DateTime? @updatedAt

  start             DateTime
  end               DateTime
  EntityNote        EntityNote[]
  entityNoteHistory EntityNoteHistory[]

  notificationSend NotificationSend[]

  @@index([personId])
  @@index([storeId, personId])
}

model ShiftActivity {
  id String @id

  shiftId String
  shift   Shift  @relation(fields: [shiftId], references: [id], onDelete: Cascade)

  activityType       String
  title              String
  description        String?
  countsTowardsLabor Boolean

  // pay status for activityType = "breaks"
  payStatus String? // "paid" | "unpaid"

  setupPositionTitle String?

  start DateTime @db.Time(0) // 24hr time
  end   DateTime @db.Time(0) // 24hr time

  @@index([shiftId])
  @@index([activityType])
}

model Shift {
  id              String         @id
  shiftAreaId     String
  shiftArea       ShiftArea      @relation(fields: [shiftAreaId], references: [id])
  storePositionId String?
  storePosition   StorePosition? @relation(fields: [storePositionId], references: [id])
  storeAreaId     String?
  storeArea       StoreArea?     @relation(fields: [storeAreaId], references: [id])

  // storeId should actually be required, but we added it after rows already existed
  storeId String?
  store   Store?  @relation(fields: [storeId], references: [id])

  assignedPersonId String?
  assignedPerson   Person? @relation(fields: [assignedPersonId], references: [id])

  title       String?
  description String?

  start DateTime @db.Time(0) // 24hr time
  end   DateTime @db.Time(0) // 24hr time

  // Absolute datetime of the start and end of the shift.
  // This is for easier querying only.
  startAbs DateTime?
  endAbs   DateTime?

  // ISO 8601 week date, for easier querying only
  year Int?
  week Int?
  day  Int?

  isShiftLead Boolean @default(false)

  // order on the schedule builder UI
  order Int @default(0)

  ShiftOffer ShiftOffer[]
  Generator  Generator[]

  setupSheetPositionShifts SetupSheetPositionShift[]
  shiftBreaks              ShiftBreak[]
  shiftActivities          ShiftActivity[]

  offeredByShiftSwaps ShiftSwap[] @relation("ShiftSwapOfferedByShift")
  offeredToShiftSwaps ShiftSwap[] @relation("ShiftSwapOfferedToShift")

  notificationSend     NotificationSend[]
  SetupActivity        SetupActivity[]
  SetupHistoryActivity SetupHistoryActivity[]

  @@index([shiftAreaId])
  @@index([storePositionId])
  @@index([storeAreaId])
  @@index([storeId])
  @@index([assignedPersonId])
  @@index([startAbs, endAbs])
  @@index([year, week, day])
}

model ShiftArea {
  id                 String      @id
  scheduleDayId      String
  scheduleDay        ScheduleDay @relation(fields: [scheduleDayId], references: [id])
  storeAreaId        String?
  storeArea          StoreArea?  @relation(fields: [storeAreaId], references: [id])
  title              String
  description        String?
  countsTowardsLabor Boolean     @default(true)
  order              Int
  Shift              Shift[]

  @@index([scheduleDayId])
  @@index([storeAreaId])
}

model ScheduleDay {
  id         String   @id
  scheduleId String
  schedule   Schedule @relation(fields: [scheduleId], references: [id])

  projectedRevenue      Float?
  productivityGoal      Float?
  averagePayRate        Float?
  createdFromTemplateId String?

  dayOfWeek Int
  ShiftArea ShiftArea[]

  @@index([scheduleId])
}

model ScheduleValidationMessageIgnore {
  scheduleId String
  schedule   Schedule @relation(fields: [scheduleId], references: [id])
  // I should've named this "ignoreId". Because we might want to ignore messages on other types of things lik people, days, etc.
  // Since I lacked the foresight to do that, you just have to keep in mind that this may not actually be a shift ID. It could be a personId or something else.
  shiftId    String
  code       String
  isIgnored  Boolean

  ignoredByPersonId   String?
  unignoredByPersonId String?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@id([scheduleId, shiftId, code])
}

model Schedule {
  businessId String
  business   Business @relation(fields: [businessId], references: [id])
  storeId    String
  store      Store    @relation(fields: [storeId], references: [id])
  id         String   @id

  // for template schedules, a title and description can be set
  title       String?
  description String?

  createdFromId String?

  year Int
  week Int

  storeHoursStart DateTime @db.Time(0) // 24hr time
  storeHoursEnd   DateTime @db.Time(0) // 24hr time

  dayParts  Json // z.array(dayPart).min(0).max(20)
  peakHours Json // z.array(weekDayTimeRange)

  isTemplate  Boolean
  isPublished Boolean
  publishedAt DateTime?

  createdAt DateTime  @default(now())
  updatedAt DateTime? @updatedAt

  publishedVersion                 Int?
  draftVersion                     Int // for optimistic locking
  draftStartedFromPublishedVersion Int?
  latestDraftChangeByPersonId      String?
  latestDraftChangeByPerson        Person? @relation("latestDraftChangeByPerson", fields: [latestDraftChangeByPersonId], references: [id])

  draftData                Json? // "Schedule" typescript type
  ScheduleDay              ScheduleDay[]
  PublishedScheduleHistory PublishedScheduleHistory[]
  ShiftOffer               ShiftOffer[]
  DomainEvent              DomainEvent[]
  EntityNote               EntityNote[]
  EntityNoteHistory        EntityNoteHistory[]
  Generator                Generator[]
  ignoredMessages          ScheduleValidationMessageIgnore[]
  setupSheets              SetupSheet[]
  metrics                  Metric[]
  shiftSwaps               ShiftSwap[]
  hourlySalesForecast      ScheduleHourlySalesForecast[]
  DraftScheduleHistory     DraftScheduleHistory[]
  setupDays                SetupDay[]

  @@index([businessId, storeId, year, week])
}

// history of changes to the published schedule
model PublishedScheduleHistory {
  scheduleId       String
  schedule         Schedule @relation(fields: [scheduleId], references: [id])
  publishedVersion Int

  createdAt           DateTime @default(now())
  changeReason        String?
  publishedByPersonId String?
  publishedByPerson   Person?  @relation(fields: [publishedByPersonId], references: [id])

  publishedData Json? // "Schedule" typescript type

  @@id([scheduleId, publishedVersion])
  @@index([publishedByPersonId])
}

model DraftScheduleHistory {
  scheduleId   String
  schedule     Schedule @relation(fields: [scheduleId], references: [id])
  draftVersion Int

  createdAt         DateTime @default(now())
  changedByPersonId String?
  changedByPerson   Person?  @relation(fields: [changedByPersonId], references: [id])

  delta     Json? // rfc6902 patch from prev draft version
  isDiscard Boolean

  operation     String? // whether it was a normal schedule edit, a shift swap, etc.
  operationData Json?

  @@id([scheduleId, draftVersion])
  @@index([changedByPersonId])
}

model StateMinorWorkGuidelines {
  id      String @id
  storeId String
  store   Store  @relation(fields: [storeId], references: [id])

  ageGroup14To15_schoolDays_hoursPerDay  Int?
  ageGroup14To15_schoolDays_hoursPerWeek Int?

  ageGroup14To15_nonSchoolDays_hoursPerDay  Int?
  ageGroup14To15_nonSchoolDays_hoursPerWeek Int?

  ageGroup14To15_workHours_start        DateTime? @db.Time(0)
  ageGroup14To15_workHours_end          DateTime? @db.Time(0)
  ageGroup14To15_workHours_summer_start DateTime? @db.Time(0)
  ageGroup14To15_workHours_summer_end   DateTime? @db.Time(0)

  ageGroup16to17_schoolDays_hoursPerDay  Int?
  ageGroup16to17_schoolDays_hoursPerWeek Int?

  ageGroup16to17_nonSchoolDays_hoursPerDay  Int?
  ageGroup16to17_nonSchoolDays_hoursPerWeek Int?

  summer_start String? // "MM-dd" format
  summer_end   String? // "MM-dd" format

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([storeId])
}

model ScheduleBreakRules {
  id      String @id
  storeId String
  store   Store  @relation(fields: [storeId], references: [id], onDelete: Cascade)

  isRequiredByLaw Boolean
  ageGroup        String

  shiftLengthHours   Int
  breakLengthMinutes Int
  isPaid             Boolean

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([storeId])
}

model ScheduleEvent {
  businessId  String
  business    Business @relation(fields: [businessId], references: [id])
  storeId     String
  store       Store    @relation(fields: [storeId], references: [id])
  id          String   @id
  title       String
  description String?
  start       DateTime
  end         DateTime
  eventType   String?
  color       String?

  // 0 = everyone, 1 = leadership, 2 = scheduler only, 999 = custom recipients
  visibilityLevel Int @default(0)

  createdByPersonId String?
  createdByPerson   Person? @relation("EventCreatedByPerson", fields: [createdByPersonId], references: [id])

  updatedByPersonId String?
  updatedByPerson   Person? @relation("EventUpdatedByPerson", fields: [updatedByPersonId], references: [id])

  createdAt  DateTime    @default(now())
  updatedAt  DateTime    @updatedAt
  generators Generator[]

  isArchived         Boolean   @default(false)
  archivedAt         DateTime?
  archivedByPersonId String?
  archivedByPerson   Person?   @relation(fields: [archivedByPersonId], references: [id], name: "ScheduleEventArchivedByPerson")

  isTimeOffRestricted Boolean @default(false)

  requiresAcknowledgment Boolean? @default(false)
  personsAcknowledged    Person[] @relation("AcknowledgedEvents")

  attachments                  FileAttachment[]
  recipients                   ScheduleEventRecipient[]
  scheduleEventAcknowledgments DigitalSignatureAcknowledgement[]

  @@index([businessId, storeId, start, end, isArchived])
}

model ScheduleEventRecipient {
  id String @id

  scheduleEventId String
  scheduleEvent   ScheduleEvent @relation(fields: [scheduleEventId], references: [id], onDelete: Cascade)

  personId String
  person   Person @relation(fields: [personId], references: [id])

  createdAt DateTime @default(now())

  @@index([scheduleEventId])
  @@index([personId])
}

model DigitalSignatureAcknowledgement {
  scheduleEventId String
  scheduleEvent   ScheduleEvent @relation(fields: [scheduleEventId], references: [id], onDelete: Cascade)

  personId String
  person   Person @relation(fields: [personId], references: [id])

  acknowledgementTimestamp DateTime
  acknowledgementStatement String
  digitalSignature         String
  ipAddress                String
  deviceInfo               String
  clientId                 String

  createdAt DateTime @default(now())

  @@id([scheduleEventId, personId])
  @@index([scheduleEventId])
  @@index([personId])
}

model ShiftOfferAcceptance {
  shiftOfferId String
  shiftOffer   ShiftOffer @relation(fields: [shiftOfferId], references: [id])

  acceptorPersonId String
  acceptorPerson   Person @relation(fields: [acceptorPersonId], references: [id])

  acceptedAt DateTime @default(now())
  comments   String?

  @@id([shiftOfferId, acceptorPersonId])
  @@index([acceptorPersonId])
}

model ShiftOffer {
  id      String @id
  humanId Int

  scheduleId String
  schedule   Schedule @relation(fields: [scheduleId], references: [id], onDelete: Cascade)

  // if this is a house shift offer, then this is the scheduler who created the offer
  createdByPersonId String?
  createdByPerson   Person? @relation("ShiftOfferCreatedByPerson", fields: [createdByPersonId], references: [id])

  // Populated if offering a split shift
  start DateTime? @db.Time(0) // 24hr time
  end   DateTime? @db.Time(0) // 24hr time

  // The schedule version that this shift offer was created on.
  publishedScheduleVersion Int

  // The shift that is being offered. If the shift is changed or deleted, this will be nulled out.
  shiftId String?
  shift   Shift?  @relation(fields: [shiftId], references: [id], onDelete: SetNull)

  // The person who offered this shift. They may not necessarily be the person assigned to the shift after the offer
  // got created, because the scheduler or others may change the shift assignment.
  offerorPersonId String?
  offerorPerson   Person? @relation("ShiftOfferSubmittedBy", fields: [offerorPersonId], references: [id])

  // The persons the shift was offered to. This can be multiple people.
  offereePersons Person[] @relation("ShiftOfferredToPerson")

  storeId String
  store   Store  @relation(fields: [storeId], references: [id])

  isSubmitted     Boolean
  submittedAt     DateTime?
  submittedReason String?

  isAccepted Boolean

  // People who accepted the shift offer. If there are multiple people, the scheduler may choose one while
  // reviewing and approving the shift offer.
  acceptances ShiftOfferAcceptance[]

  isApproved               Boolean
  approvedAt               DateTime?
  approvedByPersonId       String?
  approvedByPerson         Person?   @relation("ShiftOfferApprovedByPerson", fields: [approvedByPersonId], references: [id])
  approvedReason           String?
  approvedAcceptorPersonId String?
  approvedAcceptorPerson   Person?   @relation("ShiftOfferApprovedAcceptorPerson", fields: [approvedAcceptorPersonId], references: [id])

  isDeclined         Boolean
  declinedAt         DateTime?
  declinedReason     String?
  declinedByPersonId String?
  declinedByPerson   Person?   @relation("ShiftOfferDeclinedByPerson", fields: [declinedByPersonId], references: [id])

  isCancelled                         Boolean
  cancelledAt                         DateTime?
  cancelledReason                     String?
  cancelledByPersonId                 String?
  cancelledByPerson                   Person?   @relation("ShiftOfferCancelledByPerson", fields: [cancelledByPersonId], references: [id])
  cancelledByPublishedScheduleVersion Int?

  createdAt         DateTime            @default(now())
  updatedAt         DateTime?           @updatedAt
  EntityNote        EntityNote[]
  EntityNoteHistory EntityNoteHistory[]

  notificationSend NotificationSend[]

  @@unique([storeId, humanId])
  @@index([scheduleId])
  @@index([shiftId])
  @@index([offerorPersonId])
  @@index([storeId])
}

model ShiftSwap {
  id String @id

  storeId String
  store   Store  @relation(fields: [storeId], references: [id])

  scheduleId String
  schedule   Schedule @relation(fields: [scheduleId], references: [id], onDelete: Cascade)

  // The schedule version that this shift offer was created on.
  publishedScheduleVersion Int

  // The person who offered this shift. They may not necessarily be the person assigned to the shift after the offer
  // got created, because the scheduler or others may change the shift assignment.
  offerorPersonId String
  offerorPerson   Person @relation("ShiftSwapOfferSubmittedBy", fields: [offerorPersonId], references: [id])

  // The shift that is being offered. If the shift is changed or deleted, this will be nulled out.
  offerorShiftId String?
  offerorShift   Shift?  @relation("ShiftSwapOfferedByShift", fields: [offerorShiftId], references: [id], onDelete: SetNull)

  // The person the swap was offered to.
  offereePersonId String
  offereePerson   Person @relation("ShiftSwapOfferedToPerson", fields: [offereePersonId], references: [id])

  // The shift that is being offered. If the shift is changed or deleted, this will be nulled out.
  offereeShiftId String?
  offereeShift   Shift?  @relation("ShiftSwapOfferedToShift", fields: [offereeShiftId], references: [id], onDelete: SetNull)

  // was it accepted by the offeree, and when?
  isAccepted Boolean
  acceptedAt DateTime?
  // was it rejected by the offeree, and when?
  isRejected Boolean
  rejectedAt DateTime?

  submittedAt     DateTime
  submittedReason String?

  isApproved         Boolean
  approvedAt         DateTime?
  approvedByPersonId String?
  approvedByPerson   Person?   @relation("ShiftSwapApprovedByPerson", fields: [approvedByPersonId], references: [id])
  approvedReason     String?

  isDeclined         Boolean
  declinedAt         DateTime?
  declinedReason     String?
  declinedByPersonId String?
  declinedByPerson   Person?   @relation("ShiftSwapDeclinedByPerson", fields: [declinedByPersonId], references: [id])

  isCancelled                         Boolean
  cancelledAt                         DateTime?
  cancelledReason                     String?
  cancelledByPersonId                 String?
  cancelledByPerson                   Person?   @relation("ShiftSwapCancelledByPerson", fields: [cancelledByPersonId], references: [id])
  cancelledByPublishedScheduleVersion Int?

  createdAt         DateTime            @default(now())
  updatedAt         DateTime?           @updatedAt
  EntityNote        EntityNote[]
  EntityNoteHistory EntityNoteHistory[]
  notificationSend  NotificationSend[]

  @@index([storeId])
  @@index([scheduleId])
  @@index([offerorPersonId])
  @@index([offerorShiftId])
  @@index([offereePersonId])
  @@index([offereeShiftId])
}

model WeatherForecast {
  id           String               @id
  zipCode      String               @unique
  modifiedAt   DateTime             @default(now())
  days         WeatherForecastDay[]
  cityName     String?
  cityCoordLat Float?
  cityCoordLon Float?

  createdAt DateTime  @default(now())
  updatedAt DateTime? @updatedAt

  @@index([zipCode])
}

model WeatherForecastDay {
  id                String          @id
  weatherForecastId String
  weatherForecast   WeatherForecast @relation(fields: [weatherForecastId], references: [id], onDelete: Cascade)
  date              String // "yyyy-MM-dd"
  highTempF         Float
  lowTempF          Float
  weatherIcon       String
  rawWeatherIcon    String?
  conditionText     String          @default("Sunny")

  tempDay            Float?
  tempMin            Float?
  tempMax            Float?
  tempNight          Float?
  tempEvening        Float?
  tempMorning        Float?
  feelsLikeDay       Float?
  feelsLikeNight     Float?
  feelsLikeEvening   Float?
  feelsLikeMorning   Float?
  pressure           Int?
  humidity           Int?
  weatherMain        String?
  weatherDescription String?
  windSpeed          Float?
  windDeg            Int?
  windGust           Float?
  clouds             Int?
  pop                Float?
  rain               Float?

  createdAt DateTime  @default(now())
  updatedAt DateTime? @updatedAt

  @@index([weatherForecastId])
}

model TimeOffType {
  storeId    String
  store      Store   @relation(fields: [storeId], references: [id], onDelete: Cascade)
  title      String
  isPaid     Boolean
  isArchived Boolean @default(false)

  personTimeOffs PersonTimeOff[]

  @@unique([storeId, title])
}

model DataFile {
  id         String   @id
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt
  uploadedAt DateTime

  uploadedByPersonId String
  uploadedByPerson   Person @relation(fields: [uploadedByPersonId], references: [id])

  s3ObjectKey String?
  fileName    String?
  mimeType    String?
  sizeInBytes Int?

  storeId String
  store   Store  @relation(fields: [storeId], references: [id])

  businessId String
  business   Business @relation(fields: [businessId], references: [id])

  timezone       String
  dataType       String // "15-min sales"
  dataRangeStart DateTime?
  dataRangeEnd   DateTime?
  numRows        Int?

  dataFrequency String?

  isArchived         Boolean   @default(false)
  archivedAt         DateTime?
  archivedByPersonId String?
  archivedByPerson   Person?   @relation("DataFileArchivedByPerson", fields: [archivedByPersonId], references: [id])

  dataFileJobs DataFileJob[]

  @@index([businessId, storeId])
}

// Tracks the progress of processing a DataFile in the background
model DataFileJob {
  id        String   @id
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  storeId String
  store   Store  @relation(fields: [storeId], references: [id])

  createdByPersonId String
  createdByPerson   Person @relation(fields: [createdByPersonId], references: [id])

  dataFileId String
  dataFile   DataFile @relation(fields: [dataFileId], references: [id])

  jobType String // "ingest"

  // The range of data that was actually processed from the DataFile. We can use this to
  // track what DataFileJob is responsible for what SalesHourly rows (the latest successful job
  // will overwrite the previous successful job's data if there are any overlaps in the sales data)
  dataRangeStart DateTime?
  dataRangeEnd   DateTime?
  rowsInserted   Int? // the number of rows inserted into the database, e.g. the hourly sales rows

  status String // "pending" | "started" | "success" | "error"

  startedAt DateTime?

  successAt      DateTime?
  successMessage String?

  errorAt      DateTime?
  errorCode    String?
  errorMessage String?

  // the data points that were ingested
  dataPoints SalesHourlyDataPoint[]

  @@index([storeId])
  @@index([dataFileId])
}

model SalesForecastJob {
  id        String   @id
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  storeId String
  store   Store  @relation(fields: [storeId], references: [id])

  // data about the algorithm that should be used to generate the forecast
  algorithmData Json?

  createdByPersonId String
  createdByPerson   Person @relation(fields: [createdByPersonId], references: [id])

  jobType String // "forecastWeek"

  // The range of data that was used for the forecast (e.g. past 8 weeks)
  dataRangeStart DateTime?
  dataRangeEnd   DateTime?

  status String // "pending" | "started" | "success" | "error"

  startedAt DateTime?

  successAt      DateTime?
  successMessage String?

  // If successful, the forecast data
  forecast Json? // type RawSalesWeekForecast

  errorAt      DateTime?
  errorCode    String?
  errorMessage String?

  // The forecasts that have been created from this job
  scheduleHourlySalesForecasts ScheduleHourlySalesForecast[]

  archivedAt         DateTime?
  archivedByPersonId String?
  archivedByPerson   Person?   @relation("SalesForecastJobArchivedByPerson", fields: [archivedByPersonId], references: [id])

  @@index([storeId])
}

model ScheduleHourlySalesForecast {
  storeId String
  store   Store  @relation(fields: [storeId], references: [id])

  scheduleId String   @id
  schedule   Schedule @relation(fields: [scheduleId], references: [id])

  createdAt        DateTime @default(now())
  productivityGoal Int? // cents / hour
  averagePayRate   Int // cents / hour

  // Version for event sourcing and concurrency control
  version BigInt

  // The user may initially create this forecast from a SalesForecastJob
  createdFromSalesForecastJobId String?
  createdFromSalesForecastJob   SalesForecastJob? @relation(fields: [createdFromSalesForecastJobId], references: [id])

  dataPoints Json // type ScheduleHourlySalesForecastDataPoint[]

  @@index([scheduleId])
  @@index([storeId, scheduleId])
}

model SalesHourlyDataPoint {
  storeId String
  store   Store  @relation(fields: [storeId], references: [id])

  // The data file job that created this data point
  // Is null if the data point was not created by a data file job. E.g. if we had some other source of sales data plugged into the system.
  dataFileJobId String?
  dataFileJob   DataFileJob? @relation(fields: [dataFileJobId], references: [id])

  // the hour. Implicitly assumes that these are the sales for the interval of time to time + 1hour
  time        DateTime @db.Timestamptz(0)
  amountCents Int

  // see https://aws.amazon.com/blogs/database/designing-high-performance-time-series-data-tables-on-amazon-rds-for-postgresql/
  // However this doesn't work for queries for a time range in a specific storeId. It would be good only for queries across all stores.
  // @@index([time(ops: Int4MinMaxOps)], type: Brin)

  // There should only be one data point per hour per store
  @@id([storeId, time])
}

model UserDevicePushToken {
  id          String   @id @default(cuid())
  userId      String
  user        User     @relation(fields: [userId], references: [id])
  deviceToken String   @unique
  deviceType  String // "ios" | "android"
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@index([userId])
  @@index([deviceToken])
}

model PersonNotificationSettings {
  id String @id @default(cuid())

  personId String
  person   Person @relation(fields: [personId], references: [id])
  storeId  String
  store    Store  @relation(fields: [storeId], references: [id])

  smsEnabled  Boolean @default(true)
  pushEnabled Boolean @default(true)

  receiveScheduleUpdates                 Boolean @default(true)
  receiveShiftOfferUpdates               Boolean @default(true)
  receiveActionableItemUpdates           Boolean @default(true)
  receiveChecklistUpdates                Boolean @default(true)
  receiveFeedbackUpdates                 Boolean @default(true)
  receiveAdminShiftAndScheduleUpdates    Boolean @default(true)
  receiveScheduleUpdatesSMS              Boolean @default(false)
  receiveShiftOfferUpdatesSMS            Boolean @default(false)
  receiveActionableItemUpdatesSMS        Boolean @default(false)
  receiveChecklistUpdatesSMS             Boolean @default(false)
  receiveFeedbackUpdatesSMS              Boolean @default(false)
  receiveAdminShiftAndScheduleUpdatesSMS Boolean @default(false)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@unique([personId, storeId])
  @@index([personId, storeId])
}

model ChecklistTags {
  storeId String
  store   Store  @relation(fields: [storeId], references: [id], onDelete: Cascade)

  title String

  @@id([storeId, title])
}

model ChecklistTemplate {
  id                           String  @id
  createdFromBackingTemplateId String?

  storeId String
  store   Store  @relation(fields: [storeId], references: [id])

  title              String
  description        String?
  notifyOnIncomplete Boolean   @default(false)
  start              DateTime?
  end                DateTime?

  // duration and end are redundant. We have them both here for querying purposes.
  duration         Int? // Stored in minutes
  isExplicitTiming Boolean

  isAllDay Boolean @default(false)

  isArchived         Boolean   @default(false)
  archivedReason     String?
  archivedAt         DateTime?
  archivedByPersonId String?
  archivedByPerson   Person?   @relation(fields: [archivedByPersonId], references: [id], name: "ChecklistTemplateArchivedByPerson")

  rrule String? // Stored as string representation of RRule

  assignPeople            Person[]       @relation("AssignedChecklistTemplate")
  assignShiftLead         Boolean?
  assignStoreAreaId       String?
  assignStoreArea         StoreArea?     @relation(fields: [assignStoreAreaId], references: [id])
  assignScheduleAreaTitle String?
  assignStorePositionId   String?
  assignStorePosition     StorePosition? @relation(fields: [assignStorePositionId], references: [id])

  items      ChecklistItem[]
  generators ChecklistGenerator[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([createdFromBackingTemplateId])
}

model ChecklistGenerator {
  id      String @id
  storeId String
  store   Store  @relation(fields: [storeId], references: [id])

  createdFromTemplate   ChecklistTemplate @relation(fields: [createdFromTemplateId], references: [id])
  createdFromTemplateId String
  isActive              Boolean           @default(true)

  title              String
  description        String?
  notifyOnIncomplete Boolean           @default(false)
  start              DateTime
  end                DateTime
  duration           Int // Stored in minutes
  isExplicitTiming   Boolean
  isAllDay           Boolean           @default(false)
  rrule              String? // Stored as string representation of RRule
  generatedUntil     DateTime // how far out has the system created ScheduledActions for this generator
  scheduledActions   ScheduledAction[]

  assignPeople            Person[]       @relation("AssignedChecklistGenerator")
  assignShiftLead         Boolean?
  assignStoreAreaId       String?
  assignStoreArea         StoreArea?     @relation(fields: [assignStoreAreaId], references: [id])
  assignScheduleAreaTitle String?
  assignStorePositionId   String?
  assignStorePosition     StorePosition? @relation(fields: [assignStorePositionId], references: [id])

  items           ChecklistItem[]
  generatedEvents ChecklistEvent[]

  tags String[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([storeId])
}

model ChecklistEventPersonAssignment {
  checklistEventId String
  checklistEvent   ChecklistEvent @relation(fields: [checklistEventId], references: [id], onDelete: Cascade)

  personId String
  person   Person @relation(fields: [personId], references: [id])

  createdAt DateTime @default(now())

  @@id([checklistEventId, personId])
  @@index([personId])
}

model ChecklistEvent {
  id      String @id
  storeId String
  store   Store  @relation(fields: [storeId], references: [id])

  createdByGenerator   ChecklistGenerator @relation(fields: [createdByGeneratorId], references: [id])
  createdByGeneratorId String

  // if the user has deleted this event but it has item progress, then we soft delete with isArchived
  isArchived         Boolean   @default(false)
  archivedReason     String?
  archivedAt         DateTime?
  archivedByPersonId String?
  archivedByPerson   Person?   @relation(fields: [archivedByPersonId], references: [id], name: "ChecklistArchivedByPerson")

  title              String
  description        String?
  notifyOnIncomplete Boolean           @default(false)
  start              DateTime
  end                DateTime
  duration           Int // Stored in minutes
  isExplicitTiming   Boolean
  isAllDay           Boolean           @default(false)
  scheduledActions   ScheduledAction[]

  assignPeople            ChecklistEventPersonAssignment[]
  assignShiftLead         Boolean?
  assignStoreAreaId       String?
  assignStoreArea         StoreArea?                       @relation(fields: [assignStoreAreaId], references: [id])
  assignScheduleAreaTitle String?
  assignStorePositionId   String?
  assignStorePosition     StorePosition?                   @relation(fields: [assignStorePositionId], references: [id])

  items        ChecklistItem[]
  itemProgress TeamMemberChecklistItemProgress[]

  // these columns are for querying purposes. The source of truth is the TeamMemberCHecklistItemProgress
  isComplete         Boolean   @default(false)
  completedAt        DateTime?
  itemCompletedCount Int       @default(0)
  itemCount          Int       @default(0)

  notificationSend NotificationSend[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([storeId, createdByGeneratorId, itemCompletedCount, start])
  @@index([storeId, start, end, isComplete])
  @@index([storeId, end, isComplete, notifyOnIncomplete])
  @@index([storeId, isComplete, completedAt])
}

model FileAttachment {
  id           String    @id
  title        String?
  description  String?
  documentType String? // Certification, Tax, etc
  expiresAt    DateTime?

  fileId String
  file   File   @relation(fields: [fileId], references: [id])

  checklistItems         ChecklistItem[]
  itemInstructions       ChecklistItemInstruction[]
  requirementCompletions ChecklistRequirementCompletion[]

  storeResources StoreResource[]

  personId String?
  person   Person? @relation(fields: [personId], references: [id])

  scheduleEvent   ScheduleEvent? @relation(fields: [scheduleEventId], references: [id])
  scheduleEventId String?

  vChecklistTemplateId String?
  vChecklistTemplate   VChecklistTemplate? @relation(fields: [vChecklistTemplateId], references: [id])

  vEventId String?
  vEvent   VEvent? @relation(fields: [vEventId], references: [id])

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([fileId])
  @@index([personId])
  @@index([scheduleEventId])
  @@index([vChecklistTemplateId])
  @@index([vEventId])
}

model ChecklistItemPersonAssignment {
  checklistItemId String
  checklistItem   ChecklistItem @relation(fields: [checklistItemId], references: [id])

  personId String
  person   Person @relation(fields: [personId], references: [id])

  createdAt DateTime @default(now())

  @@id([checklistItemId, personId])
  @@index([personId])
}

model ChecklistItem {
  id String @id

  title       String
  description String?
  order       Int

  attachments  FileAttachment[]
  requirements ChecklistItemRequirement[]
  instructions ChecklistItemInstruction[]
  assignments  ChecklistItemPersonAssignment[]

  template    ChecklistTemplate?  @relation(fields: [templateId], references: [id], onDelete: Cascade)
  templateId  String?
  generator   ChecklistGenerator? @relation(fields: [generatorId], references: [id], onDelete: Cascade)
  generatorId String?
  event       ChecklistEvent?     @relation(fields: [eventId], references: [id], onDelete: Cascade)
  eventId     String?

  // Progress tracking
  progress TeamMemberChecklistItemProgress[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([templateId])
  @@index([generatorId])
  @@index([eventId])
}

model ChecklistItemRequirement {
  id   String @id
  type String // enum: "addImage" | "addVideo" | "writeComment" | "inputNumber" | "addAcknowledgement" -- see ChecklistRequirementType

  checklistItemId String
  checklistItem   ChecklistItem @relation(fields: [checklistItemId], references: [id], onDelete: Cascade)

  createdAt   DateTime                         @default(now())
  updatedAt   DateTime                         @updatedAt
  completions ChecklistRequirementCompletion[]

  @@unique([checklistItemId, type])
  @@index([type])
}

model ChecklistItemInstruction {
  id           String          @id
  text         String?
  attachment   FileAttachment? @relation(fields: [attachmentId], references: [id])
  attachmentId String?

  checklistItemId String
  checklistItem   ChecklistItem @relation(fields: [checklistItemId], references: [id], onDelete: Cascade)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([checklistItemId])
}

model TeamMemberChecklistItemProgress {
  id               String         @id
  checklistEvent   ChecklistEvent @relation(fields: [checklistEventId], references: [id], onDelete: Restrict)
  checklistEventId String

  checklistItem   ChecklistItem @relation(fields: [checklistItemId], references: [id], onDelete: Restrict)
  checklistItemId String

  isComplete          Boolean   @default(false)
  completedAt         DateTime?
  completedByPersonId String?
  completedByPerson   Person?   @relation(fields: [completedByPersonId], references: [id])

  requirements ChecklistRequirementCompletion[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@unique([checklistEventId, checklistItemId])
  @@index([completedByPersonId])
  @@index([checklistEventId])
  @@index([checklistItemId])
}

model ChecklistRequirementCompletion {
  id            String                   @id
  requirementId String
  requirement   ChecklistItemRequirement @relation(fields: [requirementId], references: [id])

  completedAt         DateTime
  completedByPersonId String
  completedByPerson   Person   @relation(fields: [completedByPersonId], references: [id])

  // Optional fields for different requirement types
  attachment   FileAttachment? @relation(fields: [attachmentId], references: [id])
  attachmentId String?
  text         String?
  number       Float?
  booleanInput Boolean?

  progress   TeamMemberChecklistItemProgress @relation(fields: [progressId], references: [id])
  progressId String

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([completedByPersonId])
  @@index([requirementId])
  @@index([progressId])
}

model ScheduledAction {
  id String @id

  businessId String?
  business   Business? @relation(fields: [businessId], references: [id])

  checklistGeneratorId String?
  checklistGenerator   ChecklistGenerator? @relation(fields: [checklistGeneratorId], references: [id], onDelete: SetNull)

  checklistEventId String?
  checklistEvent   ChecklistEvent? @relation(fields: [checklistEventId], references: [id], onDelete: SetNull)

  vEventId           String?
  vEvent             VEvent?   @relation(fields: [vEventId], references: [id], onDelete: SetNull)
  vEventRecurrenceId DateTime?

  setupActivityId String?
  setupActivity   SetupActivity? @relation(fields: [setupActivityId], references: [id], onDelete: Cascade)

  // do not perform this action before this timestamp
  notBefore DateTime

  // Processors should not process this action after this date. notBefore and notAfter define a time window at which proessing is acceptable
  notAfter DateTime?

  // how many times a processor can retry if it fails
  numRetries  Int @default(0)
  numAttempts Int @default(0)

  // the name of the “function” to dispatch to
  actionType String

  // the args to the “function”
  actionPayload Json?

  isClaimed Boolean @default(false)

  // when the processor started working on this (to signal to other processors to not start it)
  claimedAt DateTime?

  // the processor that claimed this
  claimedBy String?

  completedAt  DateTime?
  erroredAt    DateTime?
  successAt    DateTime?
  errorMessage String?

  // If the action logic changes, there may still be old ScheduledActions that expect old processing to run on them. The dispatcher can use actionVersion to send those old actions to the old code.
  actionVersion String

  // high-priority (indicated by lower numbers with highest priority being zero) actions are processed before low-priority ones
  priority Int

  // Buffers up any other scheduled actions with this same group ID within the time window between notBefore and notAfter of the first occurrence. The system will call the action function with an array of all the actions buffered up. In other words, when the system encounters a ScheduledAction that has both a notAfter and a groupId, then it will not run that action function until the notAfter time, buffering up all the other ScheduledActions within that time frame.
  groupId String?

  @@index([isClaimed, vEventId, actionType, vEventRecurrenceId, notBefore])
  @@index([checklistGeneratorId])
  @@index([checklistEventId])
  @@index([checklistGeneratorId, isClaimed])
  @@index([checklistEventId, isClaimed])
  @@index([businessId, setupActivityId, isClaimed])
  @@index([notBefore, notAfter, isClaimed, groupId])
}

model SetupSheetTemplate {
  id          String @id
  storeId     String
  store       Store  @relation(fields: [storeId], references: [id], onDelete: Cascade)
  createdById String
  createdBy   Person @relation(fields: [createdById], references: [id])
  title       String

  areas             SetupSheetTemplateArea[]
  activeSetupSheets SetupSheet[]

  createdAt DateTime  @default(now())
  updatedAt DateTime? @updatedAt

  @@index([createdById])
}

model SetupSheetTemplateArea {
  id                   String                       @id
  setupSheetTemplateId String
  setupSheetTemplate   SetupSheetTemplate           @relation(fields: [setupSheetTemplateId], references: [id], onDelete: Cascade)
  storeAreaId          String
  storeArea            StoreArea                    @relation(fields: [storeAreaId], references: [id])
  templatePositions    SetupSheetTemplatePosition[]

  createdAt DateTime  @default(now())
  updatedAt DateTime? @updatedAt

  @@index([setupSheetTemplateId])
  @@index([storeAreaId])
}

model SetupSheetTemplatePosition {
  id              String                 @id
  // The title is automatically assigned to the setup sheet position based on the store position and given an
  // icrementing number within the app.  The customTitle allows the user to override the default title.
  // The customTitle should always be preferred over the title when supplied.
  title           String
  customTitle     String?
  templateAreaId  String
  templateArea    SetupSheetTemplateArea @relation(fields: [templateAreaId], references: [id], onDelete: Cascade)
  storePositionId String
  storePosition   StorePosition          @relation(fields: [storePositionId], references: [id])
  order           Int                    @default(0)

  // constraints for the start and end times of the setup sheet position. Shifts cannot be scheduled outside of these.
  start DateTime? @db.Time(0)
  end   DateTime? @db.Time(0)

  createdAt DateTime  @default(now())
  updatedAt DateTime? @updatedAt

  @@index([templateAreaId])
  @@index([storePositionId])
}

model SetupSheet {
  id          String           @id
  storeId     String
  store       Store            @relation(fields: [storeId], references: [id], onDelete: Cascade)
  createdById String
  createdBy   Person           @relation(fields: [createdById], references: [id])
  title       String
  areas       SetupSheetArea[]

  fromTemplateId String?
  fromTemplate   SetupSheetTemplate? @relation(fields: [fromTemplateId], references: [id])

  scheduleId String
  schedule   Schedule @relation(fields: [scheduleId], references: [id])

  // ISO 8601 week date, for easier querying only
  year Int
  week Int
  day  Int

  createdAt DateTime  @default(now())
  updatedAt DateTime? @updatedAt

  @@index([storeId])
  @@index([scheduleId])
  @@index([createdById])
}

model SetupSheetArea {
  id           String               @id
  setupSheetId String
  setupSheet   SetupSheet           @relation(fields: [setupSheetId], references: [id], onDelete: Cascade)
  storeAreaId  String
  storeArea    StoreArea            @relation(fields: [storeAreaId], references: [id])
  positions    SetupSheetPosition[]

  createdAt DateTime  @default(now())
  updatedAt DateTime? @updatedAt

  @@index([setupSheetId])
  @@index([storeAreaId])
}

model SetupSheetPosition {
  id              String                    @id
  // The title is automatically assigned to the setup sheet position based on the store position and given an
  // icrementing number within the app.  The customTitle allows the user to override the default title.
  // The customTitle should always be preferred over the title when supplied.
  title           String
  customTitle     String?
  areaId          String
  area            SetupSheetArea            @relation(fields: [areaId], references: [id], onDelete: Cascade)
  storeAreaId     String
  storeArea       StoreArea                 @relation(fields: [storeAreaId], references: [id])
  storePositionId String
  storePosition   StorePosition             @relation(fields: [storePositionId], references: [id])
  shifts          SetupSheetPositionShift[]
  order           Int                       @default(0)

  // constraints for the start and end times of the setup sheet position. Shifts cannot be scheduled outside of these.
  start DateTime? @db.Time(0)
  end   DateTime? @db.Time(0)

  createdAt DateTime  @default(now())
  updatedAt DateTime? @updatedAt

  @@index([areaId])
  @@index([storePositionId])
}

model SetupSheetPositionShift {
  id                   String             @id
  setupSheetPositionId String
  setupSheetPosition   SetupSheetPosition @relation(fields: [setupSheetPositionId], references: [id], onDelete: Cascade)
  personId             String
  person               Person             @relation(fields: [personId], references: [id])
  start                DateTime           @db.Time(0)
  end                  DateTime           @db.Time(0)
  // Absolute datetime of the start and end of the shift.
  // This is for easier querying only.
  startAbs             DateTime
  endAbs               DateTime

  shiftId String
  shift   Shift  @relation(fields: [shiftId], references: [id], onDelete: Cascade)

  createdAt DateTime  @default(now())
  updatedAt DateTime? @updatedAt

  @@index([setupSheetPositionId])
  @@index([personId])
}

model ShiftBreak {
  id String @id

  shiftId  String
  shift    Shift    @relation(fields: [shiftId], references: [id], onDelete: Cascade)
  personId String
  person   Person   @relation(fields: [personId], references: [id])
  start    DateTime @db.Time(0)
  end      DateTime @db.Time(0)
  // Absolute datetime of the start and end of the shift.
  // This is for easier querying only.
  startAbs DateTime
  endAbs   DateTime

  createdAt DateTime  @default(now())
  updatedAt DateTime? @updatedAt

  @@index([shiftId])
  @@index([shiftId, personId])
}

model SystemAlert {
  id String @id

  // if we want to show this alert to only a specific business or store, we can do that here
  businessId String?
  business   Business? @relation(fields: [businessId], references: [id])
  storeId    String?
  store      Store?    @relation(fields: [storeId], references: [id])

  // the path segments in the app the announcement pops up for. E.g. ["signedin", "drawer", "tabs", "[storeId]"]
  segments    String[]
  title       String
  description String

  // The version of the app that the announcement applies to. It will not display in other versions. Is a regex.
  appVersion String?

  // Whether the user can dismiss this alert to not see it again
  dismissible Boolean

  // whether to show the announcement only one time. If false, it will show up every time the user visits the pathname
  once Boolean @default(false)

  interactions SystemAlertInteraction[]

  @@index([businessId, storeId])
}

// Tracks who has interacted with an alert and how -- whether they dismissed it, etc
model SystemAlertInteraction {
  id      String      @id
  alertId String
  alert   SystemAlert @relation(fields: [alertId], references: [id])

  personId String
  person   Person @relation(fields: [personId], references: [id])

  isDismissed Boolean   @default(false)
  dismissedAt DateTime?

  isSeen Boolean   @default(false)
  seenAt DateTime?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([alertId])
  @@index([personId, alertId])
  @@index([isSeen, seenAt])
}

model StoreResource {
  id String @id

  store   Store  @relation(fields: [storeId], references: [id])
  storeId String

  resourceType  String
  resourceTitle String?

  linkTitle String?
  linkUrl   String?

  attachment   FileAttachment? @relation(fields: [attachmentId], references: [id])
  attachmentId String?

  vendor   Vendor? @relation(fields: [vendorId], references: [id])
  vendorId String?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([storeId])
  @@index([storeId, resourceType])
  @@index([vendorId])
}

model Vendor {
  id String @id

  store   Store  @relation(fields: [storeId], references: [id])
  storeId String

  companyName    String
  pointOfContact String?
  website        String?
  phoneNumber    String?
  email          String?
  notes          String?

  addressId String?
  address   StreetAddress? @relation(fields: [addressId], references: [id])

  storeResources StoreResource[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([storeId])
}

// An abstract "calendar event". This can be used for anything that has a start and end time and optionally a recurrence rule.
// For example, meetings, announcements, schedule events, checklists, tasks, etc.
model VEvent {
  id String @id

  storeId String
  store   Store  @relation(fields: [storeId], references: [id])

  description String?
  summary     String
  alarms      Json? // type VAlarm[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Version for event sourcing and concurrency control
  version BigInt

  // timing settings
  start    DateTime
  end      DateTime?
  timezone String

  // RRuleSet properties
  rrule      Json? // type RecurrenceRule
  rruleUntil DateTime? // if the rrule has an until date, this is the absolute date. Useful for querying
  rdates     Json? // type RDate[]
  exdates    Json? // type ExDate[]

  overrides            Json? // type VEventInstance[]
  latestOverrideDate   DateTime?
  earliestOverrideDate DateTime?

  customType String?
  customData Json?

  isDeleted Boolean   @default(false)
  deletedAt DateTime?

  isArchived         Boolean   @default(false)
  archivedAt         DateTime?
  archivedByPersonId String?
  archivedByPerson   Person?   @relation("VEventArchivedByPerson", fields: [archivedByPersonId], references: [id])
  archivedReason     String?

  checklistInteractionSummaries VChecklistInteractionSummary[]
  attachments                   FileAttachment[]
  scheduledActions              ScheduledAction[]
  notificationSends             NotificationSend[]

  @@index([storeId, customType, start, end, isDeleted, isArchived])
}

model VChecklistTemplate {
  id String @id

  storeId String
  store   Store  @relation(fields: [storeId], references: [id])

  version BigInt

  title       String
  description String?
  tags        String[]

  items Json // type ChecklistItem[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  isArchived      Boolean          @default(false)
  fileAttachments FileAttachment[]

  @@index([storeId, isArchived])
}

// An aggregation of all the interactions for a checklist instance. For querying purposes.
model VChecklistInteractionSummary {
  id String @id

  storeId String
  store   Store  @relation(fields: [storeId], references: [id])

  veventId String
  vevent   VEvent @relation(fields: [veventId], references: [id])

  // the version of the checklist VEvent that the latest interaction happened on
  veventVersion BigInt

  // recurring event instances will have a recurrenceId. Single events will not.
  recurrenceId DateTime?

  isComplete           Boolean   @default(false)
  completedAt          DateTime?
  itemCompletedCount   Int       @default(0)
  itemCount            Int       @default(0)
  completedByPersonIds String[]  @default([])

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([storeId, veventId, recurrenceId])
  @@index([veventId, recurrenceId])
}

// ----------- STORE LAYOUTS / TEMPLATES -----------

model StoreLayout {
  id String @id

  businessId String
  business   Business @relation(fields: [businessId], references: [id])

  store   Store  @relation(fields: [storeId], references: [id])
  storeId String

  isMasterLayout Boolean @default(false)

  title  String
  layout Json // type ValidStoreLayout

  createdById String
  createdBy   Person @relation(fields: [createdById], references: [id])

  isArchived Boolean   @default(false)
  archivedAt DateTime?

  createdAt      DateTime         @default(now())
  updatedAt      DateTime         @updatedAt
  StoreTimeFrame StoreTimeFrame[]

  @@index([storeId, isMasterLayout])
  @@index([businessId])
  @@index([createdById])
}

model StoreTimeFrame {
  businessId String
  business   Business @relation(fields: [businessId], references: [id])

  storeId String
  store   Store  @relation(fields: [storeId], references: [id])

  title String
  start DateTime @db.Time(0)
  end   DateTime @db.Time(0)

  storeLayoutId String
  storeLayout   StoreLayout @relation(fields: [storeLayoutId], references: [id])

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@id([storeId, start, end])
  @@index([storeId])
}

// ----------- LIVE, SETUP DAYS -----------

model SetupDay {
  id String @id

  businessId String
  business   Business @relation(fields: [businessId], references: [id])

  storeId String
  store   Store  @relation(fields: [storeId], references: [id])

  scheduleId String
  schedule   Schedule @relation(fields: [scheduleId], references: [id])

  year Int
  week Int
  day  Int

  data              Json // type ValidSetupDay
  dataSchemaVersion Int  @default(1)

  version Int

  createdAt            DateTime               @default(now())
  updatedAt            DateTime               @updatedAt
  SetupActivity        SetupActivity[]
  SetupHistoryActivity SetupHistoryActivity[]

  @@index([storeId])
  @@index([scheduleId])
  @@index([year, week, day])
}

// This is like a ShiftActivity. But it doesn't require publishing the schedule to manipulate it. It represents a planned activity for a setup day position.
model SetupActivity {
  id String @id

  setupDayId String
  setupDay   SetupDay @relation(fields: [setupDayId], references: [id], onDelete: Cascade)

  activityType       String // "setupPosition" | "break"
  setupPositionTitle String?

  // An optional title and description that can be displayed in the UI for Team Members. Copied from shift activities during a schedule publish
  title       String?
  description String?

  // Copied from shift activities during a schedule publish. No current use for it, but may be useful in future for analytics.
  countsTowardsLabor Boolean @default(true)

  // pay status for activityType = "break". Copied from shift activities during a schedule publish. No current use for it, but may be useful in future for analytics.
  payStatus String? // "paid" | "unpaid"

  storeId String
  store   Store  @relation(fields: [storeId], references: [id])

  personId String
  person   Person @relation("setupActivityPerson", fields: [personId], references: [id])

  shiftId String
  shift   Shift  @relation(fields: [shiftId], references: [id], onDelete: Cascade)

  start DateTime @db.Time(0)
  end   DateTime @db.Time(0)

  startAbs DateTime
  endAbs   DateTime

  createdAt       DateTime          @default(now())
  updatedAt       DateTime          @updatedAt
  ScheduledAction ScheduledAction[]

  @@index([storeId, setupDayId, activityType, startAbs, endAbs])
  @@index([storeId, startAbs, endAbs])
  @@index([storeId, shiftId])
}

model SetupHistoryActivity {
  id String @id

  setupDayId String
  setupDay   SetupDay @relation(fields: [setupDayId], references: [id], onDelete: Cascade)

  activityType       String // "setupPosition" | "break"
  setupPositionTitle String?

  storeId String
  store   Store  @relation(fields: [storeId], references: [id])

  personId String
  person   Person @relation("setupHistoryActivityPerson", fields: [personId], references: [id])

  shiftId String?
  shift   Shift?  @relation(fields: [shiftId], references: [id], onDelete: Cascade)

  start DateTime  @db.Time(0)
  end   DateTime? @db.Time(0)

  startAbs DateTime
  endAbs   DateTime?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([storeId, setupDayId, activityType, startAbs, endAbs])
  @@index([storeId, startAbs, endAbs])
}

model Plan {
  id              String  @id
  name            String
  description     String?
  stripeProductId String
  stripePriceId   String?

  isArchived   Boolean @default(false)
  hasAppAccess Boolean

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  storeOverrides Store[]

  @@index([stripeProductId, stripePriceId])
}

model Subscription {
  id String @id

  storeId String
  store   Store  @relation(fields: [storeId], references: [id])

  stripeCustomerId String
  // TODO after backfill: store Store @relation(fields: [stripeCustomerId], references: [stripeCustomerId])

  defaultPaymentMethodId String?
  description            String?
  latestInvoice          String?

  // A date in the future at which the subscription will automatically get canceled
  cancelAt          DateTime?
  // Whether this subscription will (if status=active) or did (if status=canceled) cancel at the end of the current billing period.
  cancelAtPeriodEnd Boolean
  // If the subscription has been canceled, the date of that cancellation. If the subscription was canceled with cancel_at_period_end, canceled_at will reflect the time of the most recent update request, not the end of the subscription period when the subscription is automatically moved to a canceled state.
  canceledAt        DateTime?

  // Additional comments about why the user canceled the subscription, if the subscription was canceled explicitly by the user.
  cancellationComment String?

  // The customer submitted reason for why they canceled, if the subscription was canceled explicitly by the user.
  cancellationFeedback String?

  // Why this subscription was canceled (cancellation_requested, payment_disputed, payment_failed)
  cancellationReason String?

  // Either charge_automatically, or send_invoice. When charging automatically, Stripe will attempt to pay this subscription at the end of the cycle using the default source attached to the customer. When sending an invoice, Stripe will email your customer an invoice with payment instructions and mark the subscription as active.
  collectionMethod String // charge_automatically | send_invoice

  // Number of days a customer has to pay invoices generated by this subscription. This value will be null for subscriptions where collection_method=charge_automatically.
  daysUntilDue Int?

  // If the subscription has ended, the date the subscription ended.
  endedAt DateTime?

  // Date when the subscription was first created. The date might differ from the created date due to backdating.
  startDate DateTime?

  // Has the value true if the object exists in live mode or the value false if the object exists in test mode.
  livemode Boolean

  stripeSubscriptionId String @unique
  stripeProductId      String
  stripePriceId        String
  stripePriceLookupKey String

  automaticTaxEnabled        Boolean
  automaticTaxDisabledReason String?

  // status = active, past_due, canceled, etc. For the full list see https://docs.stripe.com/billing/subscriptions/webhooks#state-changes
  status String

  currentPeriodStart DateTime
  currentPeriodEnd   DateTime

  // If the subscription has a trial, the beginning of that trial. For subsequent trials, this date remains as the start of the first ever trial on the subscription.
  trialStart       DateTime?
  // If the subscription has a trial, the end of that trial.
  trialEnd         DateTime?
  // Indicates how the subscription should change when the trial ends if the user did not provide a payment method.
  // Possible enum values
  // cancel - Cancel the subscription if a payment method is not attached when the trial ends.
  // create_invoice - Create an invoice when the trial ends, even if the user did not set up a payment method.
  // pause - Pause the subscription if a payment method is not attached when the trial ends.
  trialEndBehavior String?

  // Our app derives this from currentPeriodEnd whenever webhooks come through for the store.
  gracePeriodEnd DateTime?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([stripeCustomerId])
  @@index([stripeSubscriptionId])
  @@index([stripeProductId, stripePriceId])
}

model EducationSection {
  id String @id

  order     Int     @default(0)
  title     String
  icon      String
  isVisible Boolean @default(true)

  resources EducationResource[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model EducationResource {
  id String @id

  sectionId String
  section   EducationSection @relation(fields: [sectionId], references: [id])

  order       Int     @default(0)
  title       String
  description String?
  playbackId  String?
  videoUrl    String?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([sectionId])
}

model TutorialSteps {
  id String @id

  key             String
  title           String
  body            String
  linkToUrl       String?
  videoPlaybackId String?

  order     Int     @default(0)
  isVisible Boolean @default(true)
}

model TutorialStepsCompletion {
  storeId             String
  store               Store    @relation(fields: [storeId], references: [id])
  completedByPersonId String
  completedByPerson   Person   @relation(fields: [completedByPersonId], references: [id])
  step                String
  completedAt         DateTime @default(now())

  @@unique([storeId, completedByPersonId, step])
  @@index([storeId])
  @@index([completedByPersonId])
  @@index([step])
}

model Newspaper {
  id String @id

  title String
  body  String
  videoPlaybackId String?

  visibilityGroupId String
  visibilityGroup   NewspaperVisibilityGroup? @relation(fields: [visibilityGroupId], references: [id])
  readReceipts      NewspaperReadReceipt[]

  publishedAt DateTime?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model NewspaperReadReceipt {
  newspaperId String
  newspaper   Newspaper @relation(fields: [newspaperId], references: [id])

  personId String
  person   Person @relation(fields: [personId], references: [id])

  readAt DateTime @default(now())

  @@id([newspaperId, personId])
  @@index([personId])
  @@index([newspaperId])
}

model NewspaperVisibilityGroup {
  id String @id

  title String
  jobTitles String[]
  newspapers Newspaper[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model SetupSheetTag {
  storeId String
  store   Store  @relation(fields: [storeId], references: [id], onDelete: Cascade)

  title String

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@unique([storeId, title])
  @@index([storeId])
}

model PinnedChannel {
  id        String   @id @default(cuid())
  personId  String
  person    Person   @relation(fields: [personId], references: [id], onDelete: Cascade)
  channelId String
  pinnedAt  DateTime @default(now())

  @@unique([personId, channelId])
  @@index([personId])
  @@index([channelId])
}
