version: '3.8'
services:
  api:
    build:
      context: .
      dockerfile: ./packages/api/Dockerfile
    image: gioa-api:latest
    container_name: gioa-api
    restart: always
    volumes:
      # mount the local directory to the container for fast reload in development
      - ./packages/api/src:/app/packages/api/src
      - ./packages/api/.env:/app/packages/api/.env
      - ./packages/api/prisma:/app/packages/api/prisma
    ports:
      - "3000:3000"
    env_file:
      - packages/api/.env
    environment:
      - NODE_ENV=development
      - DATABASE_URL=**********************************************/gioa
  db:
    image: postgres:16
    container_name: gioa-db
    restart: always
    ports:
      - "5432:5432"
    environment:
      POSTGRES_USER: user-name
      POSTGRES_PASSWORD: strong-password
    volumes:
      - local_pgdata:/var/lib/postgresql/data
volumes:
  local_pgdata:
