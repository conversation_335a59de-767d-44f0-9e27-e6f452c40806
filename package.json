{"name": "gioa", "version": "1.0.0", "private": true, "scripts": {"dev": "pnpm --filter \"./packages/*\" run dev", "typecheck": "pnpm --filter \"./packages/*\" run typecheck", "docker:build": "docker build -t gioa-api:latest -f packages/api/Dockerfile .", "docker:run:prod": "docker-compose -f docker-compose.yaml up", "test": "pnpm --filter \"./packages/*\" run test"}, "dependencies": {"@lingui/cli": "4.10.1", "@lingui/macro": "4.10.1", "@lingui/react": "4.10.1", "@lingui/vite-plugin": "4.10.1", "@types/luxon": "^3.4.2", "luxon": "^3.5.0"}, "devDependencies": {"@types/pdf-parse": "^1.1.5", "@typescript/native-preview": "7.0.0-dev.20250828.1", "husky": "^9.0.11", "typescript": "5.8.3"}, "packageManager": "pnpm@8.3.1"}