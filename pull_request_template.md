## Testing Plan
_List out a checklist of all the cases to be tested. Be exhaustive._

- [ ]

## Security Checklist

### Where is the data stored?
_List all data items created or modified by this PR._

For example:
- [ ] Tables/rows in Postgres
- [ ] S3 buckets/paths
- [ ] Redis keys
- [ ] Other (external APIs, logs, etc)

### Where is the data accessed?
_For each item above, list access points: tRPC procedures, background jobs, 3rd party services, etc._


### Is every access point secured?
_For each access point above, confirm the following:_

- [ ] Is auth required?
- [ ] User is part of the correct business and store?
- [ ] Permission package permissions enforced?
- [ ] All database queries are scoped to secure IDs?

| Access Point | Auth? | Scoped to Business/Store? | Permission Package? | Notes                                |
|--------------|-------|---------------------------|---------------------|--------------------------------------|
| `/api/employees/:id/notes` | ✅ | ✅ | ✅ (`manager`+) | Uses `requireManager()` check        |
| `/team/:id` (headshot) | ✅ | ✅ | ✅ (`any user`) | Yes | File URL is signed, expires in 5 min |

---

### How could this be abused?
_Think adversarially. What could go wrong?_

For example:
- [ ] Could permissions be bypassed?
- [ ] Is there any internal trust not verified?
- [ ] Any stale access tokens or leaked secrets still valid?
