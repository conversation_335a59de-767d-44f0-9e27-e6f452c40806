version: '3.8'
services:
  api:
    build:
      context: .
      dockerfile: ./packages/api/Dockerfile
    image: gioa-api:latest
    container_name: gioa-api
    restart: always
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - DB_USERNAME=user-name
      - DB_PASSWORD=strong-password
      - DB_HOST=db
      - DB_PORT=5432
      - DB_DATABASE=gioa
      - MEDIA_BUCKET=betastage-mediastack-s3bucket07682993-9tyadphyma3k
      - MEDIA_BASE_URL=https://media.beta.gioanation.com
      - GIOA_EMAIL_DOMAIN=beta.gioanation.com
      - GIOA_SUBDOMAIN=beta
      - AWS_EMF_ENVIRONMENT=Local
    depends_on:
      - db
  db:
    image: postgres:16
    container_name: gioa-db
    restart: always
    ports:
      - "5432:5432"
    environment:
      POSTGRES_USER: user-name
      POSTGRES_PASSWORD: strong-password
    volumes:
      - local_pgdata:/var/lib/postgresql/data
  redis:
    image: redis/redis-stack:7.2.0-v16
    container_name: redis-server
    restart: always
    ports:
      - "6379:6379"
      - "8001:8001"
    volumes:
      - redis-data:/data
    command: redis-stack-server --appendonly yes
volumes:
  local_pgdata:
  redis-data:
